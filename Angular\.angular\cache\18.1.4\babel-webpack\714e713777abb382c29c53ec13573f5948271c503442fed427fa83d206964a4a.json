{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { NavigationStart } from '@angular/router';\nimport { AddUserComponent } from '../add-user/user-add.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"src/app/modules/services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/kendo-column.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@progress/kendo-angular-grid\";\nimport * as i11 from \"@progress/kendo-angular-inputs\";\nimport * as i12 from \"@progress/kendo-angular-buttons\";\nimport * as i13 from \"ng-inline-svg-2\";\nimport * as i14 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c7 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction UserListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 11);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"kendo-textbox\", 13);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function UserListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function UserListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    })(\"clear\", function UserListComponent_ng_template_4_Template_kendo_textbox_clear_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"span\", 15);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 18);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(14, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(16, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n  }\n}\nfunction UserListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 28)(7, \"label\", 29);\n    i0.ɵɵtext(8, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.role, $event) || (ctx_r2.appliedFilters.role = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_5_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(12, \"i\", 34);\n    i0.ɵɵtext(13, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_5_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(15, \"i\", 36);\n    i0.ɵɵtext(16, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.roles);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.role);\n  }\n}\nfunction UserListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_template_5_div_0_Template, 17, 4, \"div\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 50);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const dataItem_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.unlockUser(dataItem_r6));\n    });\n    i0.ɵɵelement(1, \"span\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 47);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.userId));\n    });\n    i0.ɵɵelement(1, \"span\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template, 2, 1, \"a\", 49);\n  }\n  if (rf & 2) {\n    const dataItem_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen055.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r6.IsLocked);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 45);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template, 3, 2, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c6));\n    i0.ɵɵproperty(\"width\", 125)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(10, _c7))(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r8.userFullName, \" \");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r9 = ctx.$implicit;\n    const column_r10 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r10)(\"filter\", filter_r9)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 52);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template, 3, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userFullName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"userFullName\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r11.email || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r12 = ctx.$implicit;\n    const column_r13 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r13)(\"filter\", filter_r12)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 56);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 250)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"email\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"email\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r14.title || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 59);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r15 = ctx.$implicit;\n    const column_r16 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r16)(\"filter\", filter_r15)(\"extra\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 58);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template, 6, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"title\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"title\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r17.phoneNo || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r18 = ctx.$implicit;\n    const column_r19 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r19)(\"filter\", filter_r18)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 60);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"phoneNo\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"phoneNo\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r20.roleName || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r21 = ctx.$implicit;\n    const column_r22 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r22)(\"filter\", filter_r21)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 61);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"roleName\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"roleName\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 65);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 66);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen040.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_0_Template, 1, 1, \"span\", 63)(1, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_1_Template, 1, 1, \"span\", 64);\n  }\n  if (rf & 2) {\n    const dataItem_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", dataItem_r23.userStatus === \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r23.userStatus === \"Inactive\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 67);\n    i0.ɵɵlistener(\"valueChange\", function UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r24 = i0.ɵɵrestoreView(_r24);\n      const filter_r26 = ctx_r24.$implicit;\n      const column_r27 = ctx_r24.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onStatusFilterChange($event, filter_r26, column_r27));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r27 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"data\", ctx_r2.filterOptions)(\"value\", ctx_r2.getFilterValue(filter_r26, column_r27));\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 62);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template, 2, 2, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template, 1, 2, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 100)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userStatus\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"userStatus\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"span\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r28 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, dataItem_r28.lastUpdatedDate, \"MM/dd/yyyy hh:mm a\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(dataItem_r28.lastUpdatedByFullName);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r29 = ctx.$implicit;\n    const column_r30 = ctx.column;\n    const filterService_r31 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r30)(\"filter\", filter_r29)(\"filterService\", filterService_r31);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 68);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template, 6, 5, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template, 7, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 160)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"maxResizableWidth\", 240)(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_1_Template, 2, 11, \"kendo-grid-column\", 37)(2, UserListComponent_ng_container_6_kendo_grid_column_2_Template, 3, 8, \"kendo-grid-column\", 38)(3, UserListComponent_ng_container_6_kendo_grid_column_3_Template, 3, 7, \"kendo-grid-column\", 39)(4, UserListComponent_ng_container_6_kendo_grid_column_4_Template, 3, 7, \"kendo-grid-column\", 40)(5, UserListComponent_ng_container_6_kendo_grid_column_5_Template, 3, 7, \"kendo-grid-column\", 41)(6, UserListComponent_ng_container_6_kendo_grid_column_6_Template, 3, 7, \"kendo-grid-column\", 42)(7, UserListComponent_ng_container_6_kendo_grid_column_7_Template, 3, 7, \"kendo-grid-column\", 43)(8, UserListComponent_ng_container_6_kendo_grid_column_8_Template, 3, 8, \"kendo-grid-column\", 44);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r32 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"userFullName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"email\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"title\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"phoneNo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"roleName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"userStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"lastUpdatedDate\");\n  }\n}\nfunction UserListComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"div\", 74)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 15);\n    i0.ɵɵtext(6, \"Loading users... Please wait...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserListComponent_ng_template_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"i\", 76);\n    i0.ɵɵelementStart(3, \"p\", 15);\n    i0.ɵɵtext(4, \"No users found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_7_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 78);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserListComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_template_7_div_0_Template, 7, 0, \"div\", 71)(1, UserListComponent_ng_template_7_div_1_Template, 8, 0, \"div\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading || ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && !ctx_r2.isLoading && ctx_r2.serverSideRowData.length === 0);\n  }\n}\nexport class UserListComponent {\n  usersService;\n  cdr;\n  router;\n  route;\n  modalService;\n  AppService;\n  customLayoutUtilsService;\n  httpUtilService;\n  kendoColumnService;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Active',\n    value: 'Active'\n  }, {\n    text: 'Inactive',\n    value: 'Inactive'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }],\n    roles: [] // Will be populated from backend\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration for the new system\n  gridColumns = [];\n  defaultColumns = [];\n  fixedColumns = [];\n  draggableColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Enhanced Columns with Kendo UI features\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Action',\n    width: 80,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'userFullName',\n    title: 'Name',\n    width: 150,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  },\n  // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\n  {\n    field: 'email',\n    title: 'Email',\n    width: 250,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 4\n  }, {\n    field: 'title',\n    title: 'Title',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 5\n  }, {\n    field: 'phoneNo',\n    title: 'Phone',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 6\n  }, {\n    field: 'roleName',\n    title: 'Role',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 7\n  }, {\n    field: 'Status',\n    title: 'Status',\n    width: 100,\n    type: 'status',\n    isFixed: false,\n    filterable: true,\n    order: 8\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated Date',\n    width: 160,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 9\n  }];\n  // OLD SYSTEM - to be removed\n  columnsVisibility = {};\n  // Old column configuration management removed - replaced with new system\n  // State\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  // Router subscription for saving state on navigation\n  routerSubscription;\n  // Storage key for state persistence\n  GRID_STATE_KEY = 'form-templates-grid-state';\n  // Pagination\n  page = {\n    size: 10,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  // Export options\n  exportOptions = [{\n    text: 'Export All',\n    value: 'all'\n  }, {\n    text: 'Export Selected',\n    value: 'selected'\n  }, {\n    text: 'Export Filtered',\n    value: 'filtered'\n  }];\n  // Selection state\n  selectedUsers = [];\n  isAllSelected = false;\n  // Statistics\n  userStatistics = {\n    activeUsers: 0,\n    inactiveUsers: 0,\n    suspendedUsers: 0,\n    lockedUsers: 0,\n    totalUsers: 0\n  };\n  // Bulk operations\n  showBulkActions = false;\n  bulkActionStatus = 'Active';\n  //add or edit default paramters\n  permissionArray = [];\n  constructor(usersService, cdr, router, route, modalService,\n  // Provides modal functionality to display modals\n  AppService, customLayoutUtilsService, httpUtilService, kendoColumnService) {\n    this.usersService = usersService;\n    this.cdr = cdr;\n    this.router = router;\n    this.route = route;\n    this.modalService = modalService;\n    this.AppService = AppService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilService = httpUtilService;\n    this.kendoColumnService = kendoColumnService;\n  }\n  ngOnInit() {\n    this.loginUser = this.AppService.getLoggedInUser();\n    console.log('Login user loaded:', this.loginUser);\n    // Setup search with debounce\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(searchTerm => {\n      console.log('Search triggered with term:', searchTerm);\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Set loading state for search\n      this.loading = true;\n      this.isLoading = true;\n      // Force change detection to show loader\n      this.cdr.detectChanges();\n      this.loadTable();\n    });\n    // Subscribe to router events to save state before navigation\n    this.routerSubscription = this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.saveGridState();\n      }\n    });\n    // Load saved state if available\n    this.loadGridState();\n    // Load roles for advanced filters\n    this.loadRoles();\n    // Load user statistics\n    this.loadUserStatistics();\n    // Initialize with default page load\n    this.onPageLoad();\n    // Initialize new column visibility system\n    this.initializeColumnVisibilitySystem();\n    // Load column configuration after a short delay to ensure loginUser is available\n    setTimeout(() => {\n      this.loadColumnConfigFromDatabase();\n    }, 100);\n  }\n  /**\n   * Initialize the new column visibility system\n   */\n  initializeColumnVisibilitySystem() {\n    // Initialize default columns\n    this.defaultColumns = this.gridColumnConfig.map(col => col.field);\n    this.gridColumns = [...this.defaultColumns];\n    // Set fixed columns (first 3 columns)\n    this.fixedColumns = ['action', 'FirstName', 'LastName'];\n    // Set draggable columns (all except fixed)\n    this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\n    // Initialize normal and expanded grid references\n    this.normalGrid = this.grid;\n    this.expandedGrid = this.grid;\n  }\n  ngAfterViewInit() {\n    // Load the table after the view is initialized\n    // Small delay to ensure the grid is properly rendered\n    setTimeout(() => {\n      this.loadTable();\n    }, 200);\n  }\n  // Method to handle when the component becomes visible\n  onTabActivated() {\n    // Set loading state for tab activation\n    this.loading = true;\n    this.isLoading = true;\n    // Refresh the data when the tab is activated\n    this.loadTable();\n    this.loadUserStatistics();\n  }\n  // Method to handle initial page load\n  onPageLoad() {\n    // Initialize the component with default data\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{\n      field: 'LastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.searchData = '';\n    // Set loading state for initial page load\n    this.loading = true;\n    this.isLoading = true;\n    // Load the data\n    this.loadTable();\n    this.loadUserStatistics();\n  }\n  // Refresh grid data - only refresh the grid with latest API call\n  refreshGrid() {\n    // Set loading state to show full-screen loader\n    this.loading = true;\n    this.isLoading = true;\n    // Reset to first page and clear any applied filters\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    // Clear search data\n    this.searchData = '';\n    // Load fresh data from API\n    this.loadTable();\n  }\n  // Old column configuration methods removed - replaced with new system\n  // Old column selector methods removed - replaced with new system\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.routerSubscription) {\n      this.routerSubscription.unsubscribe();\n    }\n    if (this.searchSubscription) {\n      this.searchSubscription.unsubscribe();\n    }\n    this.searchTerms.complete();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Force change detection to show loader\n    this.cdr.detectChanges();\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId\n    };\n    console.log('Loading table with search term:', this.searchData);\n    console.log('Full state object:', state);\n    console.log('Loading states - loading:', this.loading, 'isLoading:', this.isLoading);\n    this.usersService.getUsersForKendoGrid(state).subscribe({\n      next: data => {\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          this.handleEmptyResponse();\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const userData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = userData.length !== 0;\n          this.serverSideRowData = userData;\n          this.gridData = this.serverSideRowData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n        }\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      error: error => {\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Enhanced loadTable method that can use either endpoint\n  loadTable() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Use the new Kendo UI specific endpoint for better performance\n      _this.loadTableWithKendoEndpoint();\n    })();\n  }\n  handleEmptyResponse() {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n  }\n  // Enhanced search handling\n  clearSearch() {\n    // Clear search data and trigger search\n    this.searchData = '';\n    // Set loading state for clear search\n    this.loading = true;\n    this.isLoading = true;\n    this.searchTerms.next('');\n  }\n  // Clear all filters and search\n  clearAllFilters() {\n    this.searchData = '';\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for clear all filters\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  // Apply advanced filters\n  applyAdvancedFilters() {\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for advanced filters\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  // Toggle advanced filters panel\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  // Load roles for advanced filters\n  loadRoles() {\n    const queryParams = {\n      pageSize: 1000,\n      sortOrder: 'ASC',\n      sortField: 'roleName',\n      pageNumber: 0\n    };\n    this.usersService.getAllRoles(queryParams).subscribe({\n      next: data => {\n        if (data && data.responseData && data.responseData.content) {\n          this.advancedFilterOptions.roles = [{\n            text: 'All Roles',\n            value: null\n          }, ...data.responseData.content.map(role => ({\n            text: role.roleName,\n            value: role.roleName\n          }))];\n        }\n      },\n      error: error => {\n        console.error('Error loading roles:', error);\n        // Set default roles if loading fails\n        this.advancedFilterOptions.roles = [{\n          text: 'All Roles',\n          value: null\n        }];\n      }\n    });\n    this.usersService.getDefaultPermissions({}).subscribe(permissions => {\n      this.permissionArray = permissions.responseData;\n    });\n  }\n  // Load user statistics\n  loadUserStatistics() {\n    this.usersService.getUserStatistics().subscribe({\n      next: data => {\n        if (data && data.statistics) {\n          this.userStatistics = data.statistics;\n        }\n      },\n      error: error => {\n        console.error('Error loading user statistics:', error);\n      }\n    });\n  }\n  // Selection handling\n  onSelectionChange(selection) {\n    this.selectedUsers = selection.selectedRows || [];\n    this.isAllSelected = this.selectedUsers.length === this.serverSideRowData.length;\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n  // Select all users\n  selectAllUsers() {\n    if (this.isAllSelected) {\n      this.selectedUsers = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedUsers = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n  // Delete user\n  deleteUser(user) {\n    if (confirm(`Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const deleteData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.deleteUser(deleteData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: error => {\n          console.error('Error deleting user:', error);\n          this.customLayoutUtilsService.showError('Error deleting user', '');\n          //alert('Error deleting user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  // Bulk update user status\n  bulkUpdateUserStatus() {\n    if (this.selectedUsers.length === 0) {\n      //alert('Please select users to update.');\n      this.customLayoutUtilsService.showSuccess('Please select users to complete', '');\n      return;\n    }\n    if (confirm(`Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const bulkUpdateData = {\n        userIds: this.selectedUsers.map(user => user.userId),\n        status: this.bulkActionStatus,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n            this.selectedUsers = []; // Clear selection\n            this.showBulkActions = false;\n          }\n        },\n        error: error => {\n          console.error('Error updating users:', error);\n          //alert('Error updating users. Please try again.');\n          this.customLayoutUtilsService.showError('Error updating users. Please try again', '');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  // Unlock user\n  unlockUser(user) {\n    if (confirm(`Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const unlockData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.unlockUser(unlockData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            //alert(response.message);\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: error => {\n          console.error('Error unlocking user:', error);\n          this.customLayoutUtilsService.showError('Error unlocking user', '');\n          //alert('Error unlocking user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  onSearchKeyDown(event) {\n    console.log('Search keydown event:', event.key, 'Search data:', this.searchData);\n    if (event.key === 'Enter') {\n      // Trigger search immediately on Enter key\n      console.log('Triggering search on Enter key');\n      this.searchTerms.next(this.searchData || '');\n    }\n  }\n  // Handle search model changes\n  onSearchChange() {\n    // Trigger search when model changes with debouncing\n    console.log('Search model changed:', this.searchData);\n    console.log('Triggering search with debounce');\n    // Ensure search is triggered even for empty strings\n    this.searchTerms.next(this.searchData || '');\n  }\n  // Enhanced function to filter data from search and advanced filters\n  filterConfiguration() {\n    let filter = {\n      paginate: true,\n      search: '',\n      columnFilter: []\n    };\n    // Handle search text\n    let searchText;\n    if (this.searchData === null || this.searchData === undefined) {\n      searchText = '';\n    } else {\n      searchText = this.searchData;\n    }\n    filter.search = searchText.trim();\n    // Handle Kendo UI grid filters\n    if (this.activeFilters && this.activeFilters.length > 0) {\n      filter.columnFilter = [...this.activeFilters];\n    }\n    // Add advanced filters\n    if (this.appliedFilters.status && this.appliedFilters.status !== null) {\n      filter.columnFilter.push({\n        field: 'userStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    if (this.appliedFilters.role && this.appliedFilters.role !== null) {\n      filter.columnFilter.push({\n        field: 'roleName',\n        operator: 'eq',\n        value: this.appliedFilters.role\n      });\n    }\n    return filter;\n  }\n  // Grid event handlers\n  pageChange(event) {\n    this.skip = event.skip;\n    this.page.pageNumber = event.skip / event.take;\n    this.page.size = event.take;\n    // Set loading state for pagination\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  onSortChange(sort) {\n    // Handle empty sort array (normalize/unsort case)\n    const incomingSort = Array.isArray(sort) ? sort : [];\n    this.sort = incomingSort.length > 0 ? incomingSort : [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    // Update page order fields for consistency\n    if (this.sort.length > 0) {\n      this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = this.sort[0].dir || 'desc';\n    } else {\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n    // Set loading state for sorting\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  filterChange(filter) {\n    this.filter = filter;\n    this.gridFilter = filter;\n    this.activeFilters = this.flattenFilters(filter);\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for filtering\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  // Old column visibility methods removed - replaced with new system\n  // Fix 2: More robust getFilterValue method\n  getFilterValue(filter, column) {\n    if (!filter || !filter.filters || !column) {\n      return null;\n    }\n    const predicate = filter.filters.find(f => f && 'field' in f && f.field === column.field);\n    return predicate && 'value' in predicate ? predicate.value : null;\n  }\n  // Fix 3: More robust onStatusFilterChange method\n  onStatusFilterChange(value, filter, column) {\n    if (!filter || !filter.filters || !column) {\n      console.error('Invalid filter or column:', {\n        filter,\n        column\n      });\n      return;\n    }\n    const exists = filter.filters.findIndex(f => f && 'field' in f && f.field === column.field);\n    if (exists > -1) {\n      filter.filters.splice(exists, 1);\n    }\n    if (value !== null) {\n      filter.filters.push({\n        field: column.field,\n        operator: 'eq',\n        value: value\n      });\n    }\n    this.filterChange(filter);\n  }\n  // Fix 4: More robust flattenFilters method\n  flattenFilters(filter) {\n    const filters = [];\n    if (!filter || !filter.filters) {\n      return filters;\n    }\n    filter.filters.forEach(f => {\n      if (f && 'field' in f) {\n        // It's a FilterDescriptor\n        filters.push({\n          field: f.field,\n          operator: f.operator,\n          value: f.value\n        });\n      } else if (f && 'filters' in f) {\n        // It's a CompositeFilterDescriptor\n        filters.push(...this.flattenFilters(f));\n      }\n    });\n    return filters;\n  }\n  // Fix 5: More robust loadGridState method\n  loadGridState() {\n    try {\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n      if (!savedState) {\n        return;\n      }\n      const state = JSON.parse(savedState);\n      // Restore sort state\n      if (state && state.sort) {\n        this.sort = state.sort;\n        if (this.sort && this.sort.length > 0 && this.sort[0]) {\n          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n          this.page.orderDir = this.sort[0].dir || 'desc';\n        }\n      }\n      // Restore filter state\n      if (state && state.filter) {\n        this.filter = state.filter;\n        this.gridFilter = state.filter;\n        this.activeFilters = state.activeFilters || [];\n      }\n      // Restore pagination state\n      if (state && state.page) {\n        this.page = state.page;\n      }\n      if (state && state.skip !== undefined) {\n        this.skip = state.skip;\n      }\n      // Restore column visibility\n      if (state && state.columnsVisibility) {\n        this.columnsVisibility = state.columnsVisibility;\n      }\n      // Restore search state\n      if (state && state.searchData) {\n        this.searchData = state.searchData;\n      }\n      // Restore advanced filter states\n      if (state && state.appliedFilters) {\n        this.appliedFilters = state.appliedFilters;\n      }\n      if (state && state.showAdvancedFilters !== undefined) {\n        this.showAdvancedFilters = state.showAdvancedFilters;\n      }\n    } catch (error) {\n      console.error('Error loading grid state:', error);\n      // If there's an error, use default state\n    }\n  }\n  // Old getHiddenField method removed - replaced with new system\n  // Grid state persistence methods\n  saveGridState() {\n    const state = {\n      sort: this.sort,\n      filter: this.filter,\n      page: this.page,\n      skip: this.skip,\n      columnsVisibility: this.columnsVisibility,\n      searchData: this.searchData,\n      activeFilters: this.activeFilters,\n      appliedFilters: this.appliedFilters,\n      showAdvancedFilters: this.showAdvancedFilters\n    };\n    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n  }\n  // Function to add a new company (calls edit function with ID 0)\n  add() {\n    this.edit(0);\n  }\n  // Function to open the edit modal for adding/editing a company\n  edit(id) {\n    console.log('Line: 413', 'call edit function: ', id);\n    // Configuration options for the modal dialog\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddCompaniesComponent\n    const modalRef = this.modalService.open(AddUserComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = id;\n    modalRef.componentInstance.defaultPermissions = this.permissionArray;\n    // Subscribe to the modal event when data is updated\n    modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n      if (receivedEntry === true) {\n        // Reload the table data after a successful update\n        this.loadTable();\n      }\n    });\n  }\n  deleteTemplate(item) {\n    console.log('Delete template:', item);\n    // Implement delete functionality\n  }\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Enhanced export functionality\n  onExportClick(event) {\n    switch (event.item.value) {\n      case 'all':\n        this.exportAllUsers();\n        break;\n      case 'selected':\n        this.exportSelectedUsers();\n        break;\n      case 'filtered':\n        this.exportFilteredUsers();\n        break;\n      default:\n        console.warn('Unknown export option:', event.item.value);\n    }\n  }\n  exportAllUsers() {\n    const exportParams = {\n      filters: {},\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'All_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting users:', error);\n        this.customLayoutUtilsService.showError('Error exporting users', '');\n        //alert('Error exporting users. Please try again.');\n      }\n    });\n  }\n  exportSelectedUsers() {\n    if (this.selectedUsers.length === 0) {\n      this.customLayoutUtilsService.showError('Please select users to export', '');\n      //alert('Please select users to export.');\n      return;\n    }\n    const exportParams = {\n      filters: {\n        userIds: this.selectedUsers.map(user => user.UserId)\n      },\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Selected_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting selected users:', error);\n        this.customLayoutUtilsService.showError('Error exporting selected users', '');\n        //alert('Error exporting selected users. Please try again.');\n      }\n    });\n  }\n  exportFilteredUsers() {\n    const exportParams = {\n      filters: {\n        status: this.appliedFilters.status,\n        role: this.appliedFilters.role,\n        searchTerm: this.searchData\n      },\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Filtered_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting filtered users:', error);\n        this.customLayoutUtilsService.showError('Error exporting filtered users', '');\n        //alert('Error exporting filtered users. Please try again.');\n      }\n    });\n  }\n  downloadExcel(data, filename) {\n    // This would typically use a library like xlsx or similar\n    // For now, we'll create a simple CSV download\n    const csvContent = this.convertToCSV(data);\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  convertToCSV(data) {\n    if (data.length === 0) return '';\n    const headers = Object.keys(data[0]);\n    const csvRows = [headers.join(',')];\n    for (const row of data) {\n      const values = headers.map(header => {\n        const value = row[header];\n        return typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\n      });\n      csvRows.push(values.join(','));\n    }\n    return csvRows.join('\\n');\n  }\n  // NEW COLUMN VISIBILITY SYSTEM METHODS\n  /**\n   * Saves the current state of column visibility and order in the grid.\n   * This function categorizes columns into visible and hidden columns, records their titles,\n   * fields, and visibility status, and also captures the order of draggable columns.\n   * After gathering the necessary data, it sends this information to the backend for saving.\n   */\n  saveHead() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page.', '');\n      return;\n    }\n    const nonHiddenColumns = [];\n    const hiddenColumns = [];\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        if (!column.hidden) {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          nonHiddenColumns.push(columnData);\n        } else {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          hiddenColumns.push(columnData);\n        }\n      });\n    }\n    const draggableColumnsOrder = this.gridColumns.filter(col => !this.fixedColumns.includes(col)).map((field, index) => ({\n      field,\n      orderIndex: index\n    }));\n    // Prepare data for backend\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: hiddenColumns,\n      kendoColOrder: draggableColumnsOrder,\n      LoggedId: this.loginUser.userId\n    };\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n    // Save to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          // Also save to localStorage as backup\n          this.kendoColumnService.saveToLocalStorage(userData);\n          this.customLayoutUtilsService.showSuccess(res.message || 'Column settings saved successfully.', '');\n        } else {\n          this.customLayoutUtilsService.showError(res.message || 'Failed to save column settings.', '');\n        }\n        this.cdr.markForCheck();\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error saving column settings:', error);\n        // Fallback to localStorage on error\n        this.kendoColumnService.saveToLocalStorage(userData);\n        // Update local state\n        this.hiddenData = hiddenColumns;\n        this.kendoColOrder = draggableColumnsOrder;\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.customLayoutUtilsService.showError('Failed to save to server. Settings saved locally.', '');\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Reset the current state of column visibility and order in the grid to its original state.\n   * This function resets columns to default visibility and order, and saves the reset state.\n   */\n  resetTable() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page and try again.', '');\n      return;\n    }\n    // Double-check authentication token\n    const token = this.AppService.getLocalStorageItem('permitToken', true);\n    if (!token) {\n      console.error('Authentication token not found');\n      this.customLayoutUtilsService.showError('Authentication token not found. Please login again.', '');\n      return;\n    }\n    // Reset all state variables\n    this.searchData = '';\n    this.activeFilters = [];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.gridColumns = [...this.defaultColumns];\n    // Reset sort state to default\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    // Reset advanced filters\n    this.appliedFilters = {};\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n    // Reset column order index\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        const index = this.gridColumns.indexOf(column.field);\n        if (index !== -1) {\n          column.orderIndex = index;\n        }\n        // Reset column visibility - show all columns\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n    }\n    // Clear hidden columns\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.hiddenFields = [];\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Prepare reset data\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: [],\n      kendoColOrder: [],\n      LoggedId: this.loginUser.userId\n    };\n    // Only clear local settings; do not call server\n    this.kendoColumnService.clearFromLocalStorage('Users');\n    // Show loader and refresh grid\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.cdr.detectChanges();\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        this.grid.reset();\n      }, 100);\n    }\n    this.loadTable();\n  }\n  /**\n   * Loads and applies the saved column order from the user preferences or configuration.\n   * This function updates the grid column order, ensuring the fixed columns remain in place\n   * and the draggable columns are ordered according to the saved preferences.\n   */\n  loadSavedColumnOrder(kendoColOrder) {\n    try {\n      const savedOrder = kendoColOrder;\n      if (savedOrder) {\n        const parsedOrder = savedOrder;\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n          // Get only the draggable columns from saved order\n          const savedDraggableColumns = parsedOrder.sort((a, b) => a.orderIndex - b.orderIndex).map(col => col.field).filter(field => !this.fixedColumns.includes(field));\n          // Add any missing draggable columns at the end\n          const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\n          // Combine fixed columns with saved draggable columns\n          this.gridColumns = [...this.fixedColumns, ...savedDraggableColumns, ...missingColumns];\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } else {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    } catch (error) {\n      this.gridColumns = [...this.defaultColumns];\n    }\n  }\n  /**\n   * Checks if a given column is marked as hidden.\n   * This function searches the `hiddenFields` array to determine if the column should be hidden.\n   */\n  getHiddenField(columnName) {\n    return this.hiddenFields.indexOf(columnName) > -1;\n  }\n  /**\n   * Handles the column reordering event triggered when a column is moved by the user.\n   * The function checks if the column being moved is in the fixed columns and prevents reordering\n   * of fixed columns.\n   */\n  onColumnReorder(event) {\n    const {\n      columns,\n      newIndex,\n      oldIndex\n    } = event;\n    // Prevent reordering of fixed columns\n    if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\n      return;\n    }\n    // Update the gridColumns array\n    const reorderedColumns = [...this.gridColumns];\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n    reorderedColumns.splice(newIndex, 0, movedColumn);\n    this.gridColumns = reorderedColumns;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Handles column visibility changes from the Kendo Grid.\n   * Updates the local state when columns are shown or hidden.\n   */\n  updateColumnVisibility(event) {\n    if (this.isExpanded === false) {\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          if (column.hidden) {\n            const exists = this.hiddenData.some(item => item.field === columnData.field && item.hidden === true);\n            if (!exists) {\n              this.hiddenData.push(columnData);\n            }\n          } else {\n            let indexExists = this.hiddenData.findIndex(item => item.field === columnData.field && item.hidden === true);\n            if (indexExists !== -1) {\n              this.hiddenData.splice(indexExists, 1);\n            }\n          }\n        });\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.cdr.markForCheck();\n      }\n    }\n  }\n  /**\n   * Loads the saved column configuration from the backend or localStorage as fallback.\n   * This method is called during component initialization to restore user preferences.\n   */\n  loadColumnConfigFromDatabase() {\n    try {\n      // First try to load from backend\n      if (this.loginUser && this.loginUser.userId) {\n        this.kendoColumnService.getHideFields({\n          pageName: 'Users',\n          userID: this.loginUser.userId\n        }).subscribe({\n          next: res => {\n            if (!res.isFault && res.Data) {\n              this.kendoHide = res.Data;\n              this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\n              this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\n              this.hiddenFields = this.hiddenData.map(col => col.field);\n              // Update grid columns based on the hidden fields\n              if (this.grid && this.grid.columns) {\n                this.grid.columns.forEach(column => {\n                  if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                    column.includeInChooser = true;\n                    column.hidden = true;\n                  } else {\n                    column.hidden = false;\n                  }\n                });\n              }\n              // Load saved column order and update grid\n              this.loadSavedColumnOrder(this.kendoInitColOrder);\n              // Also save to localStorage as backup\n              this.kendoColumnService.saveToLocalStorage({\n                pageName: 'Users',\n                userID: this.loginUser.userId,\n                hiddenData: this.hiddenData,\n                kendoColOrder: this.kendoInitColOrder\n              });\n            }\n          },\n          error: error => {\n            console.error('Error loading from backend, falling back to localStorage:', error);\n            this.loadFromLocalStorageFallback();\n          }\n        });\n      } else {\n        // Fallback to localStorage if no user ID\n        this.loadFromLocalStorageFallback();\n      }\n    } catch (error) {\n      console.error('Error loading column configuration:', error);\n      this.loadFromLocalStorageFallback();\n    }\n  }\n  /**\n   * Fallback method to load column configuration from localStorage\n   */\n  loadFromLocalStorageFallback() {\n    try {\n      const savedConfig = this.kendoColumnService.getFromLocalStorage('Users', this.loginUser?.UserId || 0);\n      if (savedConfig) {\n        this.kendoHide = savedConfig;\n        this.hiddenData = savedConfig.hiddenData || [];\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        // Update grid columns based on the hidden fields\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach(column => {\n            if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n              column.includeInChooser = true;\n              column.hidden = true;\n            } else {\n              column.hidden = false;\n            }\n          });\n        }\n        // Load saved column order and update grid\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\n      }\n    } catch (error) {\n      console.error('Error loading from localStorage fallback:', error);\n    }\n  }\n  static ɵfac = function UserListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UserListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.KendoColumnService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserListComponent,\n    selectors: [[\"app-user-list\"]],\n    viewQuery: function UserListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 21,\n    consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"clear\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Role\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"userFullName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"email\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"title\", \"title\", \"Title\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"phoneNo\", \"title\", \"Phone\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"roleName\", \"title\", \"Role\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [\"title\", \"Edit\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-primary\", 3, \"inlineSVG\"], [\"title\", \"Unlock\", \"class\", \"btn btn-icon btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Unlock\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-warning\", 3, \"inlineSVG\"], [\"field\", \"userFullName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bolder\", \"cursor-pointer\"], [\"operator\", \"contains\", 3, \"column\", \"filter\", \"extra\"], [\"field\", \"email\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [3, \"innerHTML\"], [\"field\", \"title\", \"title\", \"Title\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"phoneNo\", \"title\", \"Phone\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"roleName\", \"title\", \"Role\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"ngbTooltip\", \"Active\", \"class\", \"svg-icon svg-icon-3 svg-icon-success\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Inactive\", \"class\", \"svg-icon svg-icon-3 svg-icon-danger text-danger\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Active\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-success\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"ngbTooltip\", \"Inactive\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", \"text-danger\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"valueChange\", \"data\", \"value\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\"], [1, \"text-gray-600\", \"fs-1r\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [\"role\", \"status\", 1, \"custom-colored-spinner-sm\", \"me-3\"], [1, \"text-center\"], [1, \"fas\", \"fa-users\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n    template: function UserListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, UserListComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"kendo-grid\", 3, 0);\n        i0.ɵɵlistener(\"columnReorder\", function UserListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onColumnReorder($event));\n        })(\"selectionChange\", function UserListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function UserListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function UserListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"sortChange\", function UserListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function UserListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(4, UserListComponent_ng_template_4_Template, 17, 10, \"ng-template\", 4)(5, UserListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 4)(6, UserListComponent_ng_container_6_Template, 9, 8, \"ng-container\", 5)(7, UserListComponent_ng_template_7_Template, 2, 2, \"ng-template\", 6);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.serverSideRowData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(16, _c2, i0.ɵɵpureFunction0(15, _c1)))(\"sortable\", i0.ɵɵpureFunction0(18, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(19, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.page.pageNumber * ctx.page.size)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(20, _c5));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n      }\n    },\n    dependencies: [i8.NgForOf, i8.NgIf, i9.NgControlStatus, i9.NgModel, i3.NgbTooltip, i10.GridComponent, i10.ToolbarTemplateDirective, i10.GridSpacerComponent, i10.ColumnComponent, i10.CellTemplateDirective, i10.NoRecordsTemplateDirective, i10.ContainsFilterOperatorComponent, i10.EndsWithFilterOperatorComponent, i10.EqualFilterOperatorComponent, i10.NotEqualFilterOperatorComponent, i10.StartsWithFilterOperatorComponent, i10.AfterFilterOperatorComponent, i10.AfterEqFilterOperatorComponent, i10.BeforeEqFilterOperatorComponent, i10.BeforeFilterOperatorComponent, i10.StringFilterMenuComponent, i10.FilterMenuTemplateDirective, i10.DateFilterMenuComponent, i11.TextBoxComponent, i12.ButtonComponent, i13.InlineSVGDirective, i14.DropDownListComponent, i8.DatePipe],\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #646367;\\n  box-shadow: 0 0 6px rgba(57, 58, 58, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.grid-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 500;\\n}\\n.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  width: 300px;\\n}\\n\\n.grid-toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n\\n\\n.k-grid-toolbar[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 5px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n  \\n\\n  \\n\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:not(.btn-primary) {\\n  padding: 0.375rem;\\n  min-width: 40px;\\n  width: 40px;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  gap: 0.5rem;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   kendo-dropdownbutton[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   kendo-dropdownbutton[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  background-color: #6f42c1;\\n  border-color: #6f42c1;\\n  color: white;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #5a32a3;\\n  border-color: #5a32a3;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background-color: #198754;\\n  border-color: #198754;\\n  color: white;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  background-color: #157347;\\n  border-color: #146c43;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  border-color: #ffc107;\\n  color: #000;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%]:hover {\\n  background-color: #ffca2c;\\n  border-color: #ffc720;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%] {\\n  background-color: #0dcaf0;\\n  border-color: #0dcaf0;\\n  color: #000;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%]:hover {\\n  background-color: #31d2f2;\\n  border-color: #25cff2;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5c636a;\\n  border-color: #565e64;\\n}\\n\\n\\n\\n.search-section[_ngcontent-%COMP%] {\\n  \\n\\n}\\n.search-section[_ngcontent-%COMP%]   .kendo-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-section[_ngcontent-%COMP%]   .kendo-textbox[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n  transition: all 0.15s ease-in-out;\\n}\\n.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]:hover {\\n  border-color: #adb5bd;\\n}\\n.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]:focus, .search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox.k-state-focused[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.total-count[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.875rem;\\n}\\n.total-count[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d !important;\\n}\\n.total-count[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%] {\\n  font-weight: 600 !important;\\n  color: #495057;\\n}\\n\\n.k-grid[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 40px 0;\\n  font-size: 16px;\\n  color: #888;\\n  background-color: #f9f9f9;\\n  border-radius: 6px;\\n  margin-top: 20px;\\n}\\n\\n.detail-container[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n\\n.detail-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 8px;\\n}\\n.detail-row[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  width: 120px;\\n  font-weight: 500;\\n  color: #666;\\n}\\n\\n\\n\\n.status-active[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: #e8f5e9;\\n  color: #2e7d32;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: #fff8e1;\\n  color: #ff8f00;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n\\n\\n[_nghost-%COMP%]     {\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n[_nghost-%COMP%]     .k-grid-header {\\n  background-color: #f5f5f5;\\n}\\n[_nghost-%COMP%]     .k-grid td .btn-icon {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  padding: 0.25rem;\\n  border-radius: 0.25rem;\\n  transition: all 0.15s ease-in-out;\\n}\\n[_nghost-%COMP%]     .k-grid td .btn-icon:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n[_nghost-%COMP%]     .k-button[title=Refresh] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 6px;\\n  padding: 8px 12px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n}\\n[_nghost-%COMP%]     .k-button[title=Refresh]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  transform: scale(1.05);\\n}\\n[_nghost-%COMP%]     .k-button[title=Refresh] .fas.fa-sync-alt {\\n  color: #6c757d;\\n  font-size: 14px;\\n}\\n[_nghost-%COMP%]     .column-config-panel {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .column-config-panel .form-check {\\n  margin-bottom: 10px;\\n}\\n[_nghost-%COMP%]     .column-config-panel .form-check .form-check-input {\\n  margin-right: 8px;\\n}\\n[_nghost-%COMP%]     .column-config-panel .form-check .form-check-input:checked {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n}\\n[_nghost-%COMP%]     .column-config-panel .form-check .form-check-label {\\n  color: white;\\n  font-weight: 500;\\n  cursor: pointer;\\n}\\n[_nghost-%COMP%]     .column-config-panel .btn-primary {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n}\\n[_nghost-%COMP%]     .column-config-panel .btn-primary:hover {\\n  background-color: #218838;\\n  border-color: #1e7e34;\\n}\\n[_nghost-%COMP%]     .column-config-panel .btn-secondary {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n}\\n[_nghost-%COMP%]     .column-config-panel .btn-secondary:hover {\\n  background-color: #5a6268;\\n  border-color: #545b62;\\n}\\n[_nghost-%COMP%]     .k-grid-column-sticky {\\n  background-color: #f8f9fa !important;\\n  border-right: 2px solid #dee2e6 !important;\\n}\\n[_nghost-%COMP%]     .k-grid-column-sticky .k-grid-header {\\n  background-color: #e9ecef !important;\\n}\\n[_nghost-%COMP%]     .k-grid-header th {\\n  font-weight: 600;\\n}\\n[_nghost-%COMP%]     .k-pager-numbers .k-link.k-state-selected {\\n  background-color: #007bff;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .k-button {\\n  margin-right: 5px;\\n}\\n[_nghost-%COMP%]     .k-icon {\\n  font-size: 16px;\\n}\\n[_nghost-%COMP%]     .k-grid-content {\\n  overflow-y: auto;\\n}\\n[_nghost-%COMP%]     .k-grid tr:hover {\\n  background-color: #f0f7ff;\\n}\\n[_nghost-%COMP%]     .k-loading-mask {\\n  background-color: rgba(255, 255, 255, 0.7);\\n}\\n[_nghost-%COMP%]     .k-loading-image::before, \\n[_nghost-%COMP%]     .k-loading-image::after {\\n  border-color: #007bff transparent;\\n}\\n\\n  .k-clear-value {\\n  color: red !important;\\n}\\n\\n  .k-grid td, \\n  .k-grid th {\\n  border: none !important; \\n\\n}\\n\\n  kendo-grid.k-grid .k-table-alt-row .k-grid-content-sticky {\\n  background-color: #fafafa !important;\\n}\\n\\n  kendo-grid.k-grid .k-grid-content-sticky {\\n  border-top-color: rgba(0, 0, 0, 0.08);\\n  border-left-color: rgba(0, 0, 0, 0.3);\\n  border-right-color: rgba(0, 0, 0, 0.3);\\n  background-color: #fafafa !important;\\n}\\n\\n  .k-grid .k-table-row.k-selected > td, \\n.k-grid[_ngcontent-%COMP%]   td.k-selected[_ngcontent-%COMP%], \\n.k-grid[_ngcontent-%COMP%]   .k-table-row.k-selected[_ngcontent-%COMP%]    > td[_ngcontent-%COMP%], \\n.k-grid[_ngcontent-%COMP%]   .k-table-td.k-selected[_ngcontent-%COMP%], \\n.k-grid[_ngcontent-%COMP%]   .k-table-row.k-selected[_ngcontent-%COMP%]    > .k-table-td[_ngcontent-%COMP%] {\\n  background-color: transparent !important;\\n}\\n\\n  .k-grid .k-table-row.k-selected:hover .k-grid-content-sticky {\\n  background-color: #fafafa !important;\\n}\\n\\n  .k-clear-value {\\n  color: red !important;\\n}\\n\\n.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 9999;\\n  background: white;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  \\n\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%] {\\n  width: 100%;\\n  \\n\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n  transition: all 0.15s ease-in-out;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%]:hover {\\n  border-color: #adb5bd;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%]:focus, .advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap.k-state-focused[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-select[_ngcontent-%COMP%] {\\n  border-radius: 0 0.375rem 0.375rem 0;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5c636a;\\n  border-color: #565e64;\\n}\\n\\n[_nghost-%COMP%]     .k-grid .k-grid-header {\\n  background-color: #edf0f3;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header {\\n  font-weight: 600;\\n  color: #495057;\\n  border-color: #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover {\\n  background-color: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover {\\n  background-color: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt {\\n  background-color: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager {\\n  background-color: #f8f9fa;\\n  border-top: 1px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content {\\n  padding: 1rem;\\n}\\n[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content .k-filter-menu-item {\\n  margin-bottom: 0.5rem;\\n}\\n[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content .k-filter-menu-item .k-textbox {\\n  width: 100%;\\n}\\n[_nghost-%COMP%]     .k-button {\\n  border-radius: 0.375rem;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-button.k-primary {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n[_nghost-%COMP%]     .k-button.k-primary:hover {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n\\n.custom-no-records[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n  font-style: italic;\\n}\\n\\n@media (max-width: 768px) {\\n  .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .k-grid-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .k-grid-toolbar[_ngcontent-%COMP%]   .kendo-grid-spacer[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  \\n\\n  .column-list[_ngcontent-%COMP%] {\\n    max-height: 400px;\\n    overflow-y: auto;\\n  }\\n  .column-item[_ngcontent-%COMP%] {\\n    border: 1px solid #dee2e6;\\n    border-radius: 6px;\\n    margin-bottom: 8px;\\n    background-color: #fff;\\n    transition: all 0.2s ease;\\n  }\\n  .column-item[_ngcontent-%COMP%]:hover {\\n    border-color: #adb5bd;\\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  }\\n  .column-item.dragging[_ngcontent-%COMP%] {\\n    opacity: 0.5;\\n    transform: rotate(5deg);\\n  }\\n  .column-controls[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .drag-handle[_ngcontent-%COMP%] {\\n    cursor: grab;\\n    color: #6c757d;\\n  }\\n  .drag-handle[_ngcontent-%COMP%]:active {\\n    cursor: grabbing;\\n  }\\n  .column-checkbox[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n  }\\n  .column-info[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n  .column-info[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    margin-bottom: 0;\\n    cursor: pointer;\\n  }\\n  .column-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .column-actions[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n  }\\n}\\n\\n\\n  .k-grid-toolbar {\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n  .k-grid-toolbar .k-button {\\n  display: inline-flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  gap: 0.5rem !important;\\n  font-size: 0.875rem !important;\\n  font-weight: 500 !important;\\n  padding: 0.375rem 0.75rem !important;\\n  border-radius: 0.375rem !important;\\n  transition: all 0.15s ease-in-out !important;\\n  min-width: 40px !important;\\n  height: 40px !important;\\n}\\n  .k-grid-toolbar .k-button:hover {\\n  transform: translateY(-1px) !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;\\n}\\n  .k-grid-toolbar kendo-dropdownbutton .k-button {\\n  padding: 0.375rem 0.75rem !important;\\n  gap: 0.5rem !important;\\n}\\n  .k-grid-toolbar > * {\\n  margin-right: 0.5rem !important;\\n}\\n  .k-grid-toolbar > *:last-child {\\n  margin-right: 0 !important;\\n}\\n  .k-grid-toolbar .btn, \\n  .k-grid-toolbar .k-button, \\n  .k-grid-toolbar kendo-dropdownbutton {\\n  margin-right: 0.5rem !important;\\n}\\n  .k-grid-toolbar .btn:last-child, \\n  .k-grid-toolbar .k-button:last-child, \\n  .k-grid-toolbar kendo-dropdownbutton:last-child {\\n  margin-right: 0 !important;\\n}\\n\\n\\n\\n  .k-grid .k-grid-header .k-header {\\n  background-color: #edf0f3 !important;\\n  font-weight: 600 !important;\\n  border-color: #dee2e6 !important;\\n}\\n  .k-grid .k-grid-content .k-table-row:hover {\\n  background-color: #f0f7ff !important;\\n}\\n  .k-grid .k-pager .k-pager-numbers .k-link {\\n  border-radius: 0.25rem !important;\\n  transition: all 0.15s ease-in-out !important;\\n}\\n  .k-grid .k-pager .k-pager-numbers .k-link:hover {\\n  background-color: #e9ecef !important;\\n}\\n  .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected {\\n  background-color: #007bff !important;\\n  color: white !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subject", "debounceTime", "distinctUntilChanged", "NavigationStart", "AddUserComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "UserListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchData", "ɵɵresetView", "ɵɵlistener", "UserListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "onSearchChange", "UserListComponent_ng_template_4_Template_kendo_textbox_clear_1_listener", "clearSearch", "ɵɵelement", "UserListComponent_ng_template_4_Template_button_click_8_listener", "add", "UserListComponent_ng_template_4_Template_button_click_11_listener", "toggleExpand", "UserListComponent_ng_template_4_Template_button_click_13_listener", "resetTable", "UserListComponent_ng_template_4_Template_button_click_15_listener", "refreshGrid", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r4", "appliedFilters", "status", "UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener", "role", "UserListComponent_ng_template_5_div_0_Template_button_click_11_listener", "applyAdvancedFilters", "UserListComponent_ng_template_5_div_0_Template_button_click_14_listener", "clearAllFilters", "advancedFilterOptions", "roles", "ɵɵtemplate", "UserListComponent_ng_template_5_div_0_Template", "showAdvancedFilters", "UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener", "_r7", "dataItem_r6", "$implicit", "unlockUser", "UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener", "_r5", "edit", "userId", "UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template", "IsLocked", "UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c6", "fixedColumns", "includes", "_c7", "getHiddenField", "ɵɵtextInterpolate1", "dataItem_r8", "userFullName", "column_r10", "filter_r9", "UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template", "dataItem_r11", "email", "ɵɵsanitizeHtml", "column_r13", "filter_r12", "UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template", "dataItem_r14", "title", "column_r16", "filter_r15", "UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template", "dataItem_r17", "phoneNo", "column_r19", "filter_r18", "UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template", "dataItem_r20", "<PERSON><PERSON><PERSON>", "column_r22", "filter_r21", "UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_0_Template", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_1_Template", "dataItem_r23", "userStatus", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r24", "_r24", "filter_r26", "column_r27", "column", "onStatusFilterChange", "filterOptions", "getFilterValue", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template", "ɵɵpipeBind2", "dataItem_r28", "lastUpdatedDate", "lastUpdatedByFullName", "column_r30", "filter_r29", "filterService_r31", "UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template", "ɵɵelementContainerStart", "UserListComponent_ng_container_6_kendo_grid_column_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_2_Template", "UserListComponent_ng_container_6_kendo_grid_column_3_Template", "UserListComponent_ng_container_6_kendo_grid_column_4_Template", "UserListComponent_ng_container_6_kendo_grid_column_5_Template", "UserListComponent_ng_container_6_kendo_grid_column_6_Template", "UserListComponent_ng_container_6_kendo_grid_column_7_Template", "UserListComponent_ng_container_6_kendo_grid_column_8_Template", "column_r32", "UserListComponent_ng_template_7_div_1_Template_button_click_5_listener", "_r33", "loadTable", "UserListComponent_ng_template_7_div_0_Template", "UserListComponent_ng_template_7_div_1_Template", "loading", "isLoading", "serverSideRowData", "length", "UserListComponent", "usersService", "cdr", "router", "route", "modalService", "AppService", "customLayoutUtilsService", "httpUtilService", "kendoColumnService", "grid", "gridData", "IsListHasValue", "loginUser", "searchTerms", "searchSubscription", "filter", "logic", "filters", "gridFilter", "activeFilters", "text", "value", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "draggableColumns", "normalGrid", "expandedGrid", "gridColumnConfig", "field", "width", "isFixed", "type", "order", "filterable", "columnsVisibility", "sort", "dir", "routerSubscription", "GRID_STATE_KEY", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "exportOptions", "selectedUsers", "isAllSelected", "userStatistics", "activeUsers", "inactiveUsers", "suspendedUsers", "lockedUsers", "totalUsers", "showBulkActions", "bulkActionStatus", "permissionArray", "constructor", "ngOnInit", "getLoggedInUser", "console", "log", "pipe", "subscribe", "searchTerm", "detectChanges", "events", "event", "saveGridState", "loadGridState", "loadRoles", "loadUserStatistics", "onPageLoad", "initializeColumnVisibilitySystem", "setTimeout", "loadColumnConfigFromDatabase", "map", "col", "ngAfterViewInit", "onTabActivated", "ngOnDestroy", "unsubscribe", "complete", "loadTableWithKendoEndpoint", "loadingSubject", "next", "state", "take", "search", "loggedInUserId", "getUsersForKendoGrid", "data", "<PERSON><PERSON><PERSON>", "responseData", "errors", "error", "handleEmptyResponse", "userData", "total", "Math", "ceil", "_this", "_asyncToGenerator", "toggleAdvancedFilters", "queryParams", "pageSize", "sortOrder", "sortField", "getAllRoles", "content", "getDefaultPermissions", "permissions", "getUserStatistics", "statistics", "onSelectionChange", "selection", "selectedRows", "selectAllUsers", "deleteUser", "user", "confirm", "FirstName", "LastName", "deleteData", "response", "message", "showSuccess", "showError", "bulkUpdateUserStatus", "bulkUpdateData", "userIds", "firstName", "lastName", "unlockData", "key", "filterConfiguration", "paginate", "columnFilter", "searchText", "undefined", "trim", "push", "operator", "pageChange", "onSortChange", "incomingSort", "Array", "isArray", "filterChange", "flattenFilters", "predicate", "find", "f", "exists", "findIndex", "splice", "for<PERSON>ach", "savedState", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "id", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "defaultPermissions", "passEntry", "receivedEntry", "deleteTemplate", "item", "gridContainer", "document", "querySelector", "classList", "toggle", "refresh", "onExportClick", "exportAllUsers", "exportSelectedUsers", "exportFilteredUsers", "warn", "exportParams", "format", "exportUsers", "exportData", "downloadExcel", "UserId", "filename", "csv<PERSON><PERSON>nt", "convertToCSV", "blob", "Blob", "link", "createElement", "url", "URL", "createObjectURL", "setAttribute", "Date", "toISOString", "split", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "Object", "keys", "csvRows", "join", "row", "values", "header", "saveHead", "nonHiddenColumns", "hiddenColumns", "columns", "hidden", "columnData", "draggableColumnsOrder", "index", "orderIndex", "pageName", "userID", "LoggedId", "createHideFields", "res", "saveToLocalStorage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "token", "getLocalStorageItem", "indexOf", "clearFromLocalStorage", "reset", "loadSavedColumnOrder", "savedOrder", "parsedOrder", "savedDraggableColumns", "a", "b", "missingColumns", "columnName", "onColumnReorder", "newIndex", "oldIndex", "reorderedColumns", "movedColumn", "updateColumnVisibility", "some", "indexExists", "getHideFields", "Data", "hideData", "includeInChooser", "loadFromLocalStorageFallback", "savedConfig", "getFromLocalStorage", "ɵɵdirectiveInject", "i1", "UserService", "ChangeDetectorRef", "i2", "Router", "ActivatedRoute", "i3", "NgbModal", "i4", "i5", "CustomLayoutUtilsService", "i6", "HttpUtilsService", "i7", "KendoColumnService", "selectors", "viewQuery", "UserListComponent_Query", "rf", "ctx", "UserListComponent_div_0_Template", "UserListComponent_Template_kendo_grid_columnReorder_2_listener", "_r1", "UserListComponent_Template_kendo_grid_selectionChange_2_listener", "UserListComponent_Template_kendo_grid_filterChange_2_listener", "UserListComponent_Template_kendo_grid_pageChange_2_listener", "UserListComponent_Template_kendo_grid_sortChange_2_listener", "UserListComponent_Template_kendo_grid_columnVisibilityChange_2_listener", "UserListComponent_ng_template_4_Template", "UserListComponent_ng_template_5_Template", "UserListComponent_ng_container_6_Template", "UserListComponent_ng_template_7_Template", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\user_list\\user-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\user_list\\user-list.component.html"], "sourcesContent": ["import {\n  Component,\n  On<PERSON>ni<PERSON>,\n  <PERSON><PERSON><PERSON>roy,\n  ChangeDetector<PERSON>ef,\n  ViewChild,\n  AfterViewInit,\n} from '@angular/core';\nimport { SortDescriptor } from '@progress/kendo-data-query';\nimport {\n  FilterDescriptor,\n  CompositeFilterDescriptor,\n  process,\n} from '@progress/kendo-data-query';\nimport { State } from '@progress/kendo-data-query';\nimport {\n  Subject,\n  debounceTime,\n  distinctUntilChanged,\n  Subscription,\n} from 'rxjs';\nimport { Router, NavigationStart, ActivatedRoute } from '@angular/router';\nimport { saveAs } from '@progress/kendo-file-saver';\nimport { AppService } from 'src/app/modules/services/app.service';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\n\nimport { AddUserComponent } from '../add-user/user-add.component';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { UserService } from '../../services/user.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { KendoColumnService } from '../../services/kendo-column.service';\n\n// Type definitions\ninterface UserData {\n  UserId: number;\n  FirstName: string;\n  LastName: string;\n  Email: string;\n  Status: string;\n  Title: string;\n  PhoneNo: string;\n  RoleName: string;\n  LastUpdatedDate: string;\n  CreatedDate: string;\n  IsEmailNotificationEnabled: boolean;\n  IsPasswordChanged: boolean;\n  IsLocked: boolean;\n  PharmacyId?: number;\n  MedicalCenterId?: number;\n  CreatedBy?: string;\n  LastUpdatedBy?: string;\n}\n\n// Type for page configuration\ninterface PageConfig {\n  size: number;\n  pageNumber: number;\n  totalElements: number;\n  totalPages: number;\n  orderBy: string;\n  orderDir: string;\n}\n\n@Component({\n  selector: 'app-user-list',\n  templateUrl: './user-list.component.html',\n  styleUrls: ['./user-list.component.scss'],\n})\nexport class UserListComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('normalGrid') grid: any;\n\n  // Data\n  public serverSideRowData: any[] = [];\n  public gridData: any[] = [];\n  public IsListHasValue: boolean = false;\n\n  public loading: boolean = false;\n  public isLoading: boolean = false;\n\n  loginUser: any = {};\n\n  // Search\n  public searchData: string = '';\n  private searchTerms = new Subject<string>();\n  private searchSubscription: Subscription;\n\n  // Enhanced Filters for Kendo UI\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public activeFilters: Array<{\n    field: string;\n    operator: string;\n    value: any;\n  }> = [];\n\n  public filterOptions: Array<{ text: string; value: string | null }> = [\n    { text: 'All', value: null },\n    { text: 'Active', value: 'Active' },\n    { text: 'Inactive', value: 'Inactive' },\n  ];\n\n  // Advanced filter options\n  public advancedFilterOptions = {\n    status: [\n      { text: 'All', value: null },\n      { text: 'Active', value: 'Active' },\n      { text: 'Inactive', value: 'Inactive' },\n    ] as Array<{ text: string; value: string | null }>,\n    roles: [] as Array<{ text: string; value: string | null }>, // Will be populated from backend\n  };\n\n  // Filter state\n  public showAdvancedFilters = false;\n  public appliedFilters: {\n    status?: string | null;\n    role?: string | null;\n  } = {};\n\n  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\n  public kendoHide: any;\n  public hiddenData: any = [];\n  public kendoColOrder: any = [];\n  public kendoInitColOrder: any = [];\n  public hiddenFields: any = [];\n\n  // Column configuration for the new system\n  public gridColumns: string[] = [];\n  public defaultColumns: string[] = [];\n  public fixedColumns: string[] = [];\n  public draggableColumns: string[] = [];\n  public normalGrid: any;\n  public expandedGrid: any;\n  public isExpanded = false;\n\n  // Enhanced Columns with Kendo UI features\n  public gridColumnConfig: Array<{\n    field: string;\n    title: string;\n    width: number;\n    isFixed: boolean;\n    type: string;\n    filterable?: boolean;\n    order: number;\n  }> = [\n    {\n      field: 'action',\n      title: 'Action',\n      width: 80,\n      isFixed: true,\n      type: 'action',\n      order: 1,\n    },\n    {\n      field: 'userFullName',\n      title: 'Name',\n      width: 150,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2,\n    },\n    // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\n    {\n      field: 'email',\n      title: 'Email',\n      width: 250,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 4,\n    },\n    {\n      field: 'title',\n      title: 'Title',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5,\n    },\n    {\n      field: 'phoneNo',\n      title: 'Phone',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6,\n    },\n    {\n      field: 'roleName',\n      title: 'Role',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 7,\n    },\n    {\n      field: 'Status',\n      title: 'Status',\n      width: 100,\n      type: 'status',\n      isFixed: false,\n      filterable: true,\n      order: 8,\n    },\n    {\n      field: 'lastUpdatedDate',\n      title: 'Updated Date',\n      width: 160,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 9,\n    },\n  ];\n\n  // OLD SYSTEM - to be removed\n  public columnsVisibility: Record<string, boolean> = {};\n\n  // Old column configuration management removed - replaced with new system\n\n  // State\n  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n\n  // Router subscription for saving state on navigation\n  private routerSubscription: Subscription;\n\n  // Storage key for state persistence\n  private readonly GRID_STATE_KEY = 'form-templates-grid-state';\n\n  // Pagination\n  public page: PageConfig = {\n    size: 10,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc',\n  };\n  public skip: number = 0;\n\n  // Export options\n  public exportOptions: Array<{ text: string; value: string }> = [\n    { text: 'Export All', value: 'all' },\n    { text: 'Export Selected', value: 'selected' },\n    { text: 'Export Filtered', value: 'filtered' },\n  ];\n\n  // Selection state\n  public selectedUsers: any[] = [];\n  public isAllSelected: boolean = false;\n\n  // Statistics\n  public userStatistics: {\n    activeUsers: number;\n    inactiveUsers: number;\n    suspendedUsers: number;\n    lockedUsers: number;\n    totalUsers: number;\n  } = {\n    activeUsers: 0,\n    inactiveUsers: 0,\n    suspendedUsers: 0,\n    lockedUsers: 0,\n    totalUsers: 0,\n  };\n\n  // Bulk operations\n  public showBulkActions = false;\n  public bulkActionStatus: string = 'Active';\n\n  //add or edit default paramters\n  public permissionArray: any = [];\n\n  constructor(\n    private usersService: UserService,\n    private cdr: ChangeDetectorRef,\n    private router: Router,\n    private route: ActivatedRoute,\n    private modalService: NgbModal, // Provides modal functionality to display modals\n    public AppService: AppService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private httpUtilService: HttpUtilsService,\n    private kendoColumnService: KendoColumnService\n  ) {}\n\n  ngOnInit(): void {\n    this.loginUser = this.AppService.getLoggedInUser();\n    console.log('Login user loaded:', this.loginUser);\n\n    // Setup search with debounce\n    this.searchSubscription = this.searchTerms\n      .pipe(debounceTime(500), distinctUntilChanged())\n      .subscribe((searchTerm) => {\n        console.log('Search triggered with term:', searchTerm);\n        this.page.pageNumber = 0;\n        this.skip = 0;\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        // Force change detection to show loader\n        this.cdr.detectChanges();\n        this.loadTable();\n      });\n\n    // Subscribe to router events to save state before navigation\n    this.routerSubscription = this.router.events.subscribe((event) => {\n      if (event instanceof NavigationStart) {\n        this.saveGridState();\n      }\n    });\n\n    // Load saved state if available\n    this.loadGridState();\n\n    // Load roles for advanced filters\n    this.loadRoles();\n\n    // Load user statistics\n    this.loadUserStatistics();\n\n    // Initialize with default page load\n    this.onPageLoad();\n\n    // Initialize new column visibility system\n    this.initializeColumnVisibilitySystem();\n\n    // Load column configuration after a short delay to ensure loginUser is available\n    setTimeout(() => {\n      this.loadColumnConfigFromDatabase();\n    }, 100);\n  }\n\n  /**\n   * Initialize the new column visibility system\n   */\n  private initializeColumnVisibilitySystem(): void {\n    // Initialize default columns\n    this.defaultColumns = this.gridColumnConfig.map((col) => col.field);\n    this.gridColumns = [...this.defaultColumns];\n\n    // Set fixed columns (first 3 columns)\n    this.fixedColumns = ['action', 'FirstName', 'LastName'];\n\n    // Set draggable columns (all except fixed)\n    this.draggableColumns = this.defaultColumns.filter(\n      (col) => !this.fixedColumns.includes(col)\n    );\n\n    // Initialize normal and expanded grid references\n    this.normalGrid = this.grid;\n    this.expandedGrid = this.grid;\n  }\n\n  ngAfterViewInit(): void {\n    // Load the table after the view is initialized\n    // Small delay to ensure the grid is properly rendered\n    setTimeout(() => {\n      this.loadTable();\n    }, 200);\n  }\n\n  // Method to handle when the component becomes visible\n  onTabActivated(): void {\n    // Set loading state for tab activation\n    this.loading = true;\n    this.isLoading = true;\n    // Refresh the data when the tab is activated\n    this.loadTable();\n    this.loadUserStatistics();\n  }\n\n  // Method to handle initial page load\n  onPageLoad(): void {\n    // Initialize the component with default data\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{ field: 'LastUpdatedDate', dir: 'desc' }];\n    this.filter = { logic: 'and', filters: [] };\n    this.searchData = '';\n\n    // Set loading state for initial page load\n    this.loading = true;\n    this.isLoading = true;\n    // Load the data\n    this.loadTable();\n    this.loadUserStatistics();\n  }\n\n  // Refresh grid data - only refresh the grid with latest API call\n  refreshGrid(): void {\n    // Set loading state to show full-screen loader\n    this.loading = true;\n    this.isLoading = true;\n\n    // Reset to first page and clear any applied filters\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.filter = { logic: 'and', filters: [] };\n    this.gridFilter = { logic: 'and', filters: [] };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n\n    // Clear search data\n    this.searchData = '';\n\n    // Load fresh data from API\n    this.loadTable();\n  }\n\n  // Old column configuration methods removed - replaced with new system\n\n  // Old column selector methods removed - replaced with new system\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.routerSubscription) {\n      this.routerSubscription.unsubscribe();\n    }\n    if (this.searchSubscription) {\n      this.searchSubscription.unsubscribe();\n    }\n    this.searchTerms.complete();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    \n    // Force change detection to show loader\n    this.cdr.detectChanges();\n\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId,\n    };\n\n    console.log('Loading table with search term:', this.searchData);\n    console.log('Full state object:', state);\n    console.log('Loading states - loading:', this.loading, 'isLoading:', this.isLoading);\n\n    this.usersService.getUsersForKendoGrid(state).subscribe({\n      next: (data: {\n        isFault?: boolean;\n        responseData?: {\n          data: any[];\n          total: number;\n          errors?: string[];\n          status?: number;\n        };\n        data?: any[];\n        total?: number;\n        errors?: string[];\n        status?: number;\n      }) => {\n        // Handle the new API response structure\n        if (\n          data.isFault ||\n          (data.responseData &&\n            data.responseData.errors &&\n            data.responseData.errors.length > 0)\n        ) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          this.handleEmptyResponse();\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const userData = responseData.data || [];\n          const total = responseData.total || 0;\n\n          this.IsListHasValue = userData.length !== 0;\n          this.serverSideRowData = userData;\n          this.gridData = this.serverSideRowData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n        }\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      error: (error: unknown) => {\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.detectChanges();\n      },\n    });\n  }\n\n  // Enhanced loadTable method that can use either endpoint\n  async loadTable() {\n    // Use the new Kendo UI specific endpoint for better performance\n    this.loadTableWithKendoEndpoint();\n  }\n\n  private handleEmptyResponse(): void {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n  }\n\n  // Enhanced search handling\n  clearSearch(): void {\n    // Clear search data and trigger search\n    this.searchData = '';\n    // Set loading state for clear search\n    this.loading = true;\n    this.isLoading = true;\n    this.searchTerms.next('');\n  }\n\n  // Clear all filters and search\n  clearAllFilters(): void {\n    this.searchData = '';\n    this.filter = { logic: 'and', filters: [] };\n    this.gridFilter = { logic: 'and', filters: [] };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for clear all filters\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n\n  // Apply advanced filters\n  applyAdvancedFilters(): void {\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for advanced filters\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n\n  // Toggle advanced filters panel\n  toggleAdvancedFilters(): void {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n\n  // Load roles for advanced filters\n  loadRoles(): void {\n    const queryParams: {\n      pageSize: number;\n      sortOrder: string;\n      sortField: string;\n      pageNumber: number;\n    } = {\n      pageSize: 1000,\n      sortOrder: 'ASC',\n      sortField: 'roleName',\n      pageNumber: 0,\n    };\n\n    this.usersService.getAllRoles(queryParams).subscribe({\n      next: (data: {\n        responseData?: {\n          content: Array<{ roleName: string }>;\n        };\n      }) => {\n        if (data && data.responseData && data.responseData.content) {\n          this.advancedFilterOptions.roles = [\n            { text: 'All Roles', value: null },\n            ...data.responseData.content.map((role: { roleName: string }) => ({\n              text: role.roleName,\n              value: role.roleName,\n            })),\n          ];\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error loading roles:', error);\n        // Set default roles if loading fails\n        this.advancedFilterOptions.roles = [{ text: 'All Roles', value: null }];\n      },\n    });\n    this.usersService\n      .getDefaultPermissions({})\n      .subscribe((permissions: any) => {\n        this.permissionArray = permissions.responseData;\n      });\n  }\n\n  // Load user statistics\n  loadUserStatistics(): void {\n    this.usersService.getUserStatistics().subscribe({\n      next: (data: any) => {\n        if (data && data.statistics) {\n          this.userStatistics = data.statistics;\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error loading user statistics:', error);\n      },\n    });\n  }\n\n  // Selection handling\n  onSelectionChange(selection: any): void {\n    this.selectedUsers = selection.selectedRows || [];\n    this.isAllSelected =\n      this.selectedUsers.length === this.serverSideRowData.length;\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n\n  // Select all users\n  selectAllUsers(): void {\n    if (this.isAllSelected) {\n      this.selectedUsers = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedUsers = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n\n  // Delete user\n  deleteUser(user: any): void {\n    if (\n      confirm(\n        `Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`\n      )\n    ) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n\n      const deleteData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0,\n      };\n\n      this.usersService.deleteUser(deleteData).subscribe({\n        next: (response: any) => {\n          if (response && response.message) {\n            //alert(response.message);\n                                        this.customLayoutUtilsService.showSuccess(response.message, '');\n\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: (error: unknown) => {\n          console.error('Error deleting user:', error);\n                            this.customLayoutUtilsService.showError('Error deleting user', '');\n\n          //alert('Error deleting user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  // Bulk update user status\n  bulkUpdateUserStatus(): void {\n    if (this.selectedUsers.length === 0) {\n      //alert('Please select users to update.');\n                                  this.customLayoutUtilsService.showSuccess('Please select users to complete', '');\n\n      return;\n    }\n\n    if (\n      confirm(\n        `Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`\n      )\n    ) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n\n      const bulkUpdateData = {\n        userIds: this.selectedUsers.map((user) => user.userId),\n        status: this.bulkActionStatus,\n        loggedInUserId: this.loginUser.userId || 0,\n      };\n\n      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\n        next: (response: any) => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n            this.selectedUsers = []; // Clear selection\n            this.showBulkActions = false;\n          }\n        },\n        error: (error: unknown) => {\n          console.error('Error updating users:', error);\n          //alert('Error updating users. Please try again.');\n                                      this.customLayoutUtilsService.showError('Error updating users. Please try again', '');\n\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  // Unlock user\n  unlockUser(user: any): void {\n    if (\n      confirm(\n        `Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`\n      )\n    ) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n\n      const unlockData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0,\n      };\n\n      this.usersService.unlockUser(unlockData).subscribe({\n        next: (response: any) => {\n          if (response && response.message) {\n                                        this.customLayoutUtilsService.showSuccess(response.message, '');\n\n            //alert(response.message);\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: (error: unknown) => {\n          console.error('Error unlocking user:', error);\n                                      this.customLayoutUtilsService.showError('Error unlocking user', '');\n\n          //alert('Error unlocking user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  onSearchKeyDown(event: KeyboardEvent): void {\n    console.log('Search keydown event:', event.key, 'Search data:', this.searchData);\n    if (event.key === 'Enter') {\n      // Trigger search immediately on Enter key\n      console.log('Triggering search on Enter key');\n      this.searchTerms.next(this.searchData || '');\n    }\n  }\n\n  // Handle search model changes\n  onSearchChange(): void {\n    // Trigger search when model changes with debouncing\n    console.log('Search model changed:', this.searchData);\n    console.log('Triggering search with debounce');\n    // Ensure search is triggered even for empty strings\n    this.searchTerms.next(this.searchData || '');\n  }\n\n  // Enhanced function to filter data from search and advanced filters\n  filterConfiguration(): {\n    paginate: boolean;\n    search: string;\n    columnFilter: Array<{\n      field: string;\n      operator: string;\n      value: any;\n    }>;\n  } {\n    let filter: {\n      paginate: boolean;\n      search: string;\n      columnFilter: Array<{\n        field: string;\n        operator: string;\n        value: any;\n      }>;\n    } = {\n      paginate: true,\n      search: '',\n      columnFilter: [],\n    };\n\n    // Handle search text\n    let searchText: string;\n    if (this.searchData === null || this.searchData === undefined) {\n      searchText = '';\n    } else {\n      searchText = this.searchData;\n    }\n    filter.search = searchText.trim();\n\n    // Handle Kendo UI grid filters\n    if (this.activeFilters && this.activeFilters.length > 0) {\n      filter.columnFilter = [...this.activeFilters];\n    }\n\n    // Add advanced filters\n    if (this.appliedFilters.status && this.appliedFilters.status !== null) {\n      filter.columnFilter.push({\n        field: 'userStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status,\n      });\n    }\n\n    if (this.appliedFilters.role && this.appliedFilters.role !== null) {\n      filter.columnFilter.push({\n        field: 'roleName',\n        operator: 'eq',\n        value: this.appliedFilters.role,\n      });\n    }\n\n    return filter;\n  }\n\n  // Grid event handlers\n  public pageChange(event: { skip: number; take: number }): void {\n    this.skip = event.skip;\n    this.page.pageNumber = event.skip / event.take;\n    this.page.size = event.take;\n    // Set loading state for pagination\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n\n  public onSortChange(sort: SortDescriptor[]): void {\n    // Handle empty sort array (normalize/unsort case)\n    const incomingSort = Array.isArray(sort) ? sort : [];\n    this.sort = incomingSort.length > 0\n      ? incomingSort\n      : [{ field: 'lastUpdatedDate', dir: 'desc' }];\n\n    // Update page order fields for consistency\n    if (this.sort.length > 0) {\n      this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = this.sort[0].dir || 'desc';\n    } else {\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n    \n    // Set loading state for sorting\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n\n  public filterChange(filter: CompositeFilterDescriptor): void {\n    this.filter = filter;\n    this.gridFilter = filter;\n    this.activeFilters = this.flattenFilters(filter);\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for filtering\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n\n  // Old column visibility methods removed - replaced with new system\n\n  // Fix 2: More robust getFilterValue method\n  public getFilterValue(\n    filter: CompositeFilterDescriptor,\n    column: { field: string }\n  ): any {\n    if (!filter || !filter.filters || !column) {\n      return null;\n    }\n    const predicate = filter.filters.find(\n      (f: any) => f && 'field' in f && f.field === column.field\n    );\n    return predicate && 'value' in predicate ? predicate.value : null;\n  }\n\n  // Fix 3: More robust onStatusFilterChange method\n  public onStatusFilterChange(\n    value: string | null,\n    filter: CompositeFilterDescriptor,\n    column: { field: string }\n  ): void {\n    if (!filter || !filter.filters || !column) {\n      console.error('Invalid filter or column:', { filter, column });\n      return;\n    }\n\n    const exists = filter.filters.findIndex(\n      (f: any) => f && 'field' in f && f.field === column.field\n    );\n    if (exists > -1) {\n      filter.filters.splice(exists, 1);\n    }\n\n    if (value !== null) {\n      filter.filters.push({\n        field: column.field,\n        operator: 'eq',\n        value: value,\n      });\n    }\n\n    this.filterChange(filter);\n  }\n\n  // Fix 4: More robust flattenFilters method\n  private flattenFilters(filter: CompositeFilterDescriptor): Array<{\n    field: string;\n    operator: string;\n    value: any;\n  }> {\n    const filters: Array<{\n      field: string;\n      operator: string;\n      value: any;\n    }> = [];\n\n    if (!filter || !filter.filters) {\n      return filters;\n    }\n\n    filter.filters.forEach((f: any) => {\n      if (f && 'field' in f) {\n        // It's a FilterDescriptor\n        filters.push({\n          field: f.field,\n          operator: f.operator,\n          value: f.value,\n        });\n      } else if (f && 'filters' in f) {\n        // It's a CompositeFilterDescriptor\n        filters.push(...this.flattenFilters(f));\n      }\n    });\n\n    return filters;\n  }\n\n  // Fix 5: More robust loadGridState method\n  private loadGridState(): void {\n    try {\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n\n      if (!savedState) {\n        return;\n      }\n\n      const state: {\n        sort?: SortDescriptor[];\n        filter?: CompositeFilterDescriptor;\n        page?: {\n          size: number;\n          pageNumber: number;\n          totalElements: number;\n          totalPages: number;\n          orderBy: string;\n          orderDir: string;\n        };\n        skip?: number;\n        columnsVisibility?: Record<string, boolean>;\n        searchData?: string;\n        activeFilters?: Array<{\n          field: string;\n          operator: string;\n          value: any;\n        }>;\n        appliedFilters?: {\n          status?: string | null;\n          role?: string | null;\n        };\n        showAdvancedFilters?: boolean;\n      } = JSON.parse(savedState);\n\n      // Restore sort state\n      if (state && state.sort) {\n        this.sort = state.sort;\n        if (this.sort && this.sort.length > 0 && this.sort[0]) {\n          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n          this.page.orderDir = this.sort[0].dir || 'desc';\n        }\n      }\n\n      // Restore filter state\n      if (state && state.filter) {\n        this.filter = state.filter;\n        this.gridFilter = state.filter;\n        this.activeFilters = state.activeFilters || [];\n      }\n\n      // Restore pagination state\n      if (state && state.page) {\n        this.page = state.page;\n      }\n\n      if (state && state.skip !== undefined) {\n        this.skip = state.skip;\n      }\n\n      // Restore column visibility\n      if (state && state.columnsVisibility) {\n        this.columnsVisibility = state.columnsVisibility;\n      }\n\n      // Restore search state\n      if (state && state.searchData) {\n        this.searchData = state.searchData;\n      }\n\n      // Restore advanced filter states\n      if (state && state.appliedFilters) {\n        this.appliedFilters = state.appliedFilters;\n      }\n\n      if (state && state.showAdvancedFilters !== undefined) {\n        this.showAdvancedFilters = state.showAdvancedFilters;\n      }\n    } catch (error) {\n      console.error('Error loading grid state:', error);\n      // If there's an error, use default state\n    }\n  }\n\n  // Old getHiddenField method removed - replaced with new system\n\n  // Grid state persistence methods\n  private saveGridState(): void {\n    const state: {\n      sort: SortDescriptor[];\n      filter: CompositeFilterDescriptor;\n      page: {\n        size: number;\n        pageNumber: number;\n        totalElements: number;\n        totalPages: number;\n        orderBy: string;\n        orderDir: string;\n      };\n      skip: number;\n      columnsVisibility: Record<string, boolean>;\n      searchData: string;\n      activeFilters: Array<{\n        field: string;\n        operator: string;\n        value: any;\n      }>;\n      appliedFilters: {\n        status?: string | null;\n        role?: string | null;\n      };\n      showAdvancedFilters: boolean;\n    } = {\n      sort: this.sort,\n      filter: this.filter,\n      page: this.page,\n      skip: this.skip,\n      columnsVisibility: this.columnsVisibility,\n      searchData: this.searchData,\n      activeFilters: this.activeFilters,\n      appliedFilters: this.appliedFilters,\n      showAdvancedFilters: this.showAdvancedFilters,\n    };\n\n    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n  }\n\n  // Function to add a new company (calls edit function with ID 0)\n  add() {\n    this.edit(0);\n  }\n\n  // Function to open the edit modal for adding/editing a company\n  edit(id: number) {\n    console.log('Line: 413', 'call edit function: ', id);\n    // Configuration options for the modal dialog\n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the AddCompaniesComponent\n    const modalRef = this.modalService.open(AddUserComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = id;\n    modalRef.componentInstance.defaultPermissions = this.permissionArray;\n    // Subscribe to the modal event when data is updated\n    modalRef.componentInstance.passEntry.subscribe((receivedEntry: boolean) => {\n      if (receivedEntry === true) {\n        // Reload the table data after a successful update\n        this.loadTable();\n      }\n    });\n  }\n\n  public deleteTemplate(item: UserData): void {\n    console.log('Delete template:', item);\n    // Implement delete functionality\n  }\n  public toggleExpand(): void {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector(\n      '.grid-container'\n    ) as HTMLElement;\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n\n  // Enhanced export functionality\n  public onExportClick(event: { item: { value: string } }): void {\n    switch (event.item.value) {\n      case 'all':\n        this.exportAllUsers();\n        break;\n      case 'selected':\n        this.exportSelectedUsers();\n        break;\n      case 'filtered':\n        this.exportFilteredUsers();\n        break;\n      default:\n        console.warn('Unknown export option:', event.item.value);\n    }\n  }\n\n  private exportAllUsers(): void {\n    const exportParams = {\n      filters: {},\n      format: 'excel',\n    };\n\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: (response: any) => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'All_Users');\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error exporting users:', error);\n                                    this.customLayoutUtilsService.showError('Error exporting users', '');\n\n        //alert('Error exporting users. Please try again.');\n      },\n    });\n  }\n\n  private exportSelectedUsers(): void {\n    if (this.selectedUsers.length === 0) {\n                                  this.customLayoutUtilsService.showError('Please select users to export', '');\n\n      //alert('Please select users to export.');\n      return;\n    }\n\n    const exportParams = {\n      filters: {\n        userIds: this.selectedUsers.map((user) => user.UserId),\n      },\n      format: 'excel',\n    };\n\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: (response: any) => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Selected_Users');\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error exporting selected users:', error);\n                                    this.customLayoutUtilsService.showError('Error exporting selected users', '');\n\n        //alert('Error exporting selected users. Please try again.');\n      },\n    });\n  }\n\n  private exportFilteredUsers(): void {\n    const exportParams = {\n      filters: {\n        status: this.appliedFilters.status,\n        role: this.appliedFilters.role,\n        searchTerm: this.searchData,\n      },\n      format: 'excel',\n    };\n\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: (response: any) => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Filtered_Users');\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error exporting filtered users:', error);\n                                    this.customLayoutUtilsService.showError('Error exporting filtered users', '');\n\n        //alert('Error exporting filtered users. Please try again.');\n      },\n    });\n  }\n\n  private downloadExcel(data: any[], filename: string): void {\n    // This would typically use a library like xlsx or similar\n    // For now, we'll create a simple CSV download\n    const csvContent = this.convertToCSV(data);\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute(\n      'download',\n      `${filename}_${new Date().toISOString().split('T')[0]}.csv`\n    );\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n\n  private convertToCSV(data: any[]): string {\n    if (data.length === 0) return '';\n\n    const headers = Object.keys(data[0]);\n    const csvRows = [headers.join(',')];\n\n    for (const row of data) {\n      const values = headers.map((header) => {\n        const value = row[header];\n        return typeof value === 'string' && value.includes(',')\n          ? `\"${value}\"`\n          : value;\n      });\n      csvRows.push(values.join(','));\n    }\n\n    return csvRows.join('\\n');\n  }\n\n  // NEW COLUMN VISIBILITY SYSTEM METHODS\n\n  /**\n   * Saves the current state of column visibility and order in the grid.\n   * This function categorizes columns into visible and hidden columns, records their titles,\n   * fields, and visibility status, and also captures the order of draggable columns.\n   * After gathering the necessary data, it sends this information to the backend for saving.\n   */\n  saveHead(): void {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError(\n        'User not logged in. Please refresh the page.',\n        ''\n      );\n      return;\n    }\n\n    const nonHiddenColumns: any[] = [];\n    const hiddenColumns: any[] = [];\n\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach((column: any) => {\n        if (!column.hidden) {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden,\n          };\n          nonHiddenColumns.push(columnData);\n        } else {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden,\n          };\n          hiddenColumns.push(columnData);\n        }\n      });\n    }\n\n    const draggableColumnsOrder = this.gridColumns\n      .filter((col) => !this.fixedColumns.includes(col))\n      .map((field, index) => ({\n        field,\n        orderIndex: index,\n      }));\n\n    // Prepare data for backend\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: hiddenColumns,\n      kendoColOrder: draggableColumnsOrder,\n      LoggedId: this.loginUser.userId,\n    };\n\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n\n    // Save to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: (res) => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map((col: any) => col.field);\n\n          // Also save to localStorage as backup\n          this.kendoColumnService.saveToLocalStorage(userData);\n\n          this.customLayoutUtilsService.showSuccess(\n            res.message || 'Column settings saved successfully.',\n            ''\n          );\n        } else {\n          this.customLayoutUtilsService.showError(\n            res.message || 'Failed to save column settings.',\n            ''\n          );\n        }\n        this.cdr.markForCheck();\n      },\n      error: (error) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error saving column settings:', error);\n\n        // Fallback to localStorage on error\n        this.kendoColumnService.saveToLocalStorage(userData);\n\n        // Update local state\n        this.hiddenData = hiddenColumns;\n        this.kendoColOrder = draggableColumnsOrder;\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\n\n        this.customLayoutUtilsService.showError(\n          'Failed to save to server. Settings saved locally.',\n          ''\n        );\n        this.cdr.markForCheck();\n      },\n    });\n  }\n\n  /**\n   * Reset the current state of column visibility and order in the grid to its original state.\n   * This function resets columns to default visibility and order, and saves the reset state.\n   */\n  resetTable(): void {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError(\n        'User not logged in. Please refresh the page and try again.',\n        ''\n      );\n      return;\n    }\n\n    // Double-check authentication token\n    const token = this.AppService.getLocalStorageItem('permitToken', true);\n    if (!token) {\n      console.error('Authentication token not found');\n      this.customLayoutUtilsService.showError(\n        'Authentication token not found. Please login again.',\n        ''\n      );\n      return;\n    }\n\n    // Reset all state variables\n    this.searchData = '';\n    this.activeFilters = [];\n    this.filter = { logic: 'and', filters: [] };\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.gridColumns = [...this.defaultColumns];\n\n    // Reset sort state to default\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n\n    // Reset advanced filters\n    this.appliedFilters = {};\n\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n\n    // Reset column order index\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach((column: any) => {\n        const index = this.gridColumns.indexOf(column.field);\n        if (index !== -1) {\n          column.orderIndex = index;\n        }\n        // Reset column visibility - show all columns\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n    }\n\n    // Clear hidden columns\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.hiddenFields = [];\n\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = { logic: 'and', filters: [] };\n      \n      // Reset sorting\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n      \n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n\n    // Prepare reset data\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: [],\n      kendoColOrder: [],\n      LoggedId: this.loginUser.userId,\n    };\n\n    // Only clear local settings; do not call server\n    this.kendoColumnService.clearFromLocalStorage('Users');\n\n    // Show loader and refresh grid\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.cdr.detectChanges();\n\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        this.grid.reset();\n      }, 100);\n    }\n\n    this.loadTable();\n  }\n\n  /**\n   * Loads and applies the saved column order from the user preferences or configuration.\n   * This function updates the grid column order, ensuring the fixed columns remain in place\n   * and the draggable columns are ordered according to the saved preferences.\n   */\n  loadSavedColumnOrder(kendoColOrder: any): void {\n    try {\n      const savedOrder = kendoColOrder;\n      if (savedOrder) {\n        const parsedOrder = savedOrder;\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n          // Get only the draggable columns from saved order\n          const savedDraggableColumns = parsedOrder\n            .sort((a, b) => a.orderIndex - b.orderIndex)\n            .map((col) => col.field)\n            .filter((field) => !this.fixedColumns.includes(field));\n\n          // Add any missing draggable columns at the end\n          const missingColumns = this.draggableColumns.filter(\n            (col) => !savedDraggableColumns.includes(col)\n          );\n\n          // Combine fixed columns with saved draggable columns\n          this.gridColumns = [\n            ...this.fixedColumns,\n            ...savedDraggableColumns,\n            ...missingColumns,\n          ];\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } else {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    } catch (error) {\n      this.gridColumns = [...this.defaultColumns];\n    }\n  }\n\n  /**\n   * Checks if a given column is marked as hidden.\n   * This function searches the `hiddenFields` array to determine if the column should be hidden.\n   */\n  getHiddenField(columnName: any): boolean {\n    return this.hiddenFields.indexOf(columnName) > -1;\n  }\n\n  /**\n   * Handles the column reordering event triggered when a column is moved by the user.\n   * The function checks if the column being moved is in the fixed columns and prevents reordering\n   * of fixed columns.\n   */\n  onColumnReorder(event: any): void {\n    const { columns, newIndex, oldIndex } = event;\n\n    // Prevent reordering of fixed columns\n    if (\n      this.fixedColumns.includes(columns[oldIndex].field) ||\n      this.fixedColumns.includes(columns[newIndex].field)\n    ) {\n      return;\n    }\n\n    // Update the gridColumns array\n    const reorderedColumns = [...this.gridColumns];\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n    reorderedColumns.splice(newIndex, 0, movedColumn);\n\n    this.gridColumns = reorderedColumns;\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Handles column visibility changes from the Kendo Grid.\n   * Updates the local state when columns are shown or hidden.\n   */\n  updateColumnVisibility(event: any): void {\n    if (this.isExpanded === false) {\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach((column: any) => {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden,\n          };\n          if (column.hidden) {\n            const exists = this.hiddenData.some(\n              (item: any) =>\n                item.field === columnData.field && item.hidden === true\n            );\n            if (!exists) {\n              this.hiddenData.push(columnData);\n            }\n          } else {\n            let indexExists = this.hiddenData.findIndex(\n              (item: any) =>\n                item.field === columnData.field && item.hidden === true\n            );\n            if (indexExists !== -1) {\n              this.hiddenData.splice(indexExists, 1);\n            }\n          }\n        });\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\n        this.cdr.markForCheck();\n      }\n    }\n  }\n\n  /**\n   * Loads the saved column configuration from the backend or localStorage as fallback.\n   * This method is called during component initialization to restore user preferences.\n   */\n  private loadColumnConfigFromDatabase(): void {\n    try {\n      // First try to load from backend\n      if (this.loginUser && this.loginUser.userId) {\n        this.kendoColumnService\n          .getHideFields({\n            pageName: 'Users',\n            userID: this.loginUser.userId,\n          })\n          .subscribe({\n            next: (res) => {\n              if (!res.isFault && res.Data) {\n                this.kendoHide = res.Data;\n                this.hiddenData = res.Data.hideData\n                  ? JSON.parse(res.Data.hideData)\n                  : [];\n                this.kendoInitColOrder = res.Data.kendoColOrder\n                  ? JSON.parse(res.Data.kendoColOrder)\n                  : [];\n                this.hiddenFields = this.hiddenData.map(\n                  (col: any) => col.field\n                );\n\n                // Update grid columns based on the hidden fields\n                if (this.grid && this.grid.columns) {\n                  this.grid.columns.forEach((column: any) => {\n                    if (\n                      this.hiddenData.some(\n                        (item: any) =>\n                          item.title === column.title && item.hidden\n                      )\n                    ) {\n                      column.includeInChooser = true;\n                      column.hidden = true;\n                    } else {\n                      column.hidden = false;\n                    }\n                  });\n                }\n\n                // Load saved column order and update grid\n                this.loadSavedColumnOrder(this.kendoInitColOrder);\n\n                // Also save to localStorage as backup\n                this.kendoColumnService.saveToLocalStorage({\n                  pageName: 'Users',\n                  userID: this.loginUser.userId,\n                  hiddenData: this.hiddenData,\n                  kendoColOrder: this.kendoInitColOrder,\n                });\n              }\n            },\n            error: (error) => {\n              console.error(\n                'Error loading from backend, falling back to localStorage:',\n                error\n              );\n              this.loadFromLocalStorageFallback();\n            },\n          });\n      } else {\n        // Fallback to localStorage if no user ID\n        this.loadFromLocalStorageFallback();\n      }\n    } catch (error) {\n      console.error('Error loading column configuration:', error);\n      this.loadFromLocalStorageFallback();\n    }\n  }\n\n  /**\n   * Fallback method to load column configuration from localStorage\n   */\n  private loadFromLocalStorageFallback(): void {\n    try {\n      const savedConfig = this.kendoColumnService.getFromLocalStorage(\n        'Users',\n        this.loginUser?.UserId || 0\n      );\n      if (savedConfig) {\n        this.kendoHide = savedConfig;\n        this.hiddenData = savedConfig.hiddenData || [];\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\n\n        // Update grid columns based on the hidden fields\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach((column: any) => {\n            if (\n              this.hiddenData.some(\n                (item: any) => item.title === column.title && item.hidden\n              )\n            ) {\n              column.includeInChooser = true;\n              column.hidden = true;\n            } else {\n              column.hidden = false;\n            }\n          });\n        }\n\n        // Load saved column order and update grid\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\n      }\n    } catch (error) {\n      console.error('Error loading from localStorage fallback:', error);\n    }\n  }\n}\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"grid-container\">\r\n  <kendo-grid\r\n    #normalGrid\r\n    [data]=\"serverSideRowData\"\r\n    [pageSize]=\"page.size\"\r\n    [sort]=\"sort\"\r\n    [pageable]=\"{\r\n      pageSizes: [10, 15, 20, 50, 100],\r\n      previousNext: true,\r\n      info: true,\r\n      type: 'numeric',\r\n      buttonCount: 5\r\n    }\"\r\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\r\n    [groupable]=\"false\"\r\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\r\n    (columnReorder)=\"onColumnReorder($event)\"\r\n    (selectionChange)=\"onSelectionChange($event)\"\r\n    [reorderable]=\"true\"\r\n    style=\"width: auto; overflow-x: auto\"\r\n    [resizable]=\"false\"\r\n    [height]=\"720\"\r\n    [skip]=\"page.pageNumber * page.size\"\r\n    [filter]=\"filter\"\r\n    [columnMenu]=\"{ filter: true }\"\r\n    (filterChange)=\"filterChange($event)\"\r\n    (pageChange)=\"pageChange($event)\"\r\n    (sortChange)=\"onSortChange($event)\"\r\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\r\n  >\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <!-- Search Section -->\r\n      <div class=\"d-flex align-items-center me-3 search-section\">\r\n        <kendo-textbox\r\n          [style.width.px]=\"500\"\r\n          placeholder=\"Search...\"\r\n          [(ngModel)]=\"searchData\"\r\n          [clearButton]=\"true\"\r\n          (keydown)=\"onSearchKeyDown($event)\"\r\n          (ngModelChange)=\"onSearchChange()\"\r\n          (clear)=\"clearSearch()\"\r\n        ></kendo-textbox>\r\n      </div>\r\n\r\n      <kendo-grid-spacer></kendo-grid-spacer>\r\n\r\n      <!-- Total Count - Repositioned to the right -->\r\n      <div class=\"d-flex align-items-center me-3\">\r\n        <span class=\"text-muted\">Total: </span>\r\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\r\n      </div>\r\n\r\n      <!-- Action Buttons -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-sm me-2\" (click)=\"add()\">\r\n        <span\r\n          [inlineSVG]=\"'./assets/media/icons/duotune/arrows/arr075.svg'\"\r\n          class=\"svg-icon svg-icon-3\"\r\n        ></span>\r\n        Add\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-secondary btn-sm me-2\"\r\n        (click)=\"toggleExpand()\"\r\n        title=\"Toggle Grid Expansion\"\r\n      >\r\n        <i\r\n          class=\"fas\"\r\n          [class.fa-expand]=\"!isExpanded\"\r\n          [class.fa-compress]=\"isExpanded\"\r\n        ></i>\r\n      </button>\r\n\r\n      <!-- <kendo-dropdownbutton\r\n        text=\"Export Excel\"\r\n        iconClass=\"fas fa-file-excel\"\r\n        [data]=\"exportOptions\"\r\n        class=\"custom-dropdown\"\r\n        (itemClick)=\"onExportClick($event)\"\r\n        title=\"Export\"\r\n      >\r\n      </kendo-dropdownbutton> -->\r\n\r\n      <!-- Save Column Settings Button -->\r\n      <!-- <button\r\n        type=\"button\"\r\n        class=\"btn btn-success btn-sm me-2\"\r\n        (click)=\"saveHead()\"\r\n        title=\"Save Column Settings\"\r\n      >\r\n        <i class=\"fas fa-save\"></i>\r\n      </button> -->\r\n\r\n      <!-- Reset Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-warning btn-sm me-2\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset to Default\"\r\n      >\r\n        <i class=\"fas fa-undo\"></i>\r\n      </button>\r\n\r\n      <!-- Refresh Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-info btn-sm me-2\"\r\n        (click)=\"refreshGrid()\"\r\n        title=\"Refresh Grid Data\"\r\n      >\r\n        <i class=\"fas fa-sync-alt\"></i>\r\n      </button>\r\n    </ng-template>\r\n\r\n    <!-- Advanced Filters Panel -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <div\r\n        *ngIf=\"showAdvancedFilters\"\r\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\r\n      >\r\n        <div class=\"row\">\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Status</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.status\"\r\n              [(ngModel)]=\"appliedFilters.status\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              placeholder=\"Select Status\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Role</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.roles\"\r\n              [(ngModel)]=\"appliedFilters.role\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              placeholder=\"Select Role\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3 d-flex align-items-end\">\r\n            <button\r\n              kendoButton\r\n              (click)=\"applyAdvancedFilters()\"\r\n              class=\"btn-primary me-2\"\r\n            >\r\n              <i class=\"fas fa-check\"></i> Apply Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              (click)=\"clearAllFilters()\"\r\n              class=\"btn-secondary\"\r\n            >\r\n              <i class=\"fas fa-times\"></i> Clear\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n\r\n    <ng-container *ngFor=\"let column of gridColumns\">\r\n      <!-- Action Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'action'\"\r\n        title=\"Actions\"\r\n        [width]=\"125\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('action')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [columnMenu]=\"false\"\r\n        [style]=\"{ 'background-color': '#efefef !important' }\"\r\n        [hidden]=\"getHiddenField('action')\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <a\r\n            title=\"Edit\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"edit(dataItem.userId)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen055.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-primary\"\r\n            >\r\n            </span>\r\n          </a>\r\n          <!-- Delete button hidden -->\r\n          <!-- <a\r\n            title=\"Delete\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"deleteUser(dataItem)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen027.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-danger\"\r\n            >\r\n            </span>\r\n          </a> -->\r\n          <a\r\n            *ngIf=\"dataItem.IsLocked\"\r\n            title=\"Unlock\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"unlockUser(dataItem)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-warning\"\r\n            >\r\n            </span>\r\n          </a>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- First Name Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'userFullName'\"\r\n        field=\"userFullName\"\r\n        title=\"Name\"\r\n        [width]=\"150\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('userFullName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('userFullName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.userFullName }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Last Name Column -->\r\n      <!-- <kendo-grid-column *ngIf=\"column === 'lastName'\"\r\n        field=\"LastName\"\r\n        title=\"Last Name\"\r\n        [width]=\"150\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('LastName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3','font-weight':'600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('LastName')\"\r\n        [filterable]=\"true\">\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.LastName }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu [column]=\"column\" [filter]=\"filter\" [extra]=\"true\">\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column> -->\r\n\r\n      <!-- Email Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'email'\"\r\n        field=\"email\"\r\n        title=\"Email\"\r\n        [width]=\"250\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('email')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('email')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span [innerHTML]=\"dataItem.email || '-'\"></span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Title Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'title'\"\r\n        field=\"title\"\r\n        title=\"Title\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('title')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('title')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span [innerHTML]=\"dataItem.title || '-'\"></span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Phone Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'phoneNo'\"\r\n        field=\"phoneNo\"\r\n        title=\"Phone\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('phoneNo')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('phoneNo')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span [innerHTML]=\"dataItem.phoneNo || '-'\"></span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Role Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'roleName'\"\r\n        field=\"roleName\"\r\n        title=\"Role\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('roleName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('roleName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span [innerHTML]=\"dataItem.roleName || '-'\"></span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Status Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'userStatus'\"\r\n        field=\"userStatus\"\r\n        title=\"Status\"\r\n        [width]=\"100\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('userStatus')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('userStatus')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <span\r\n            *ngIf=\"dataItem.userStatus === 'Active'\"\r\n            ngbTooltip=\"Active\"\r\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\r\n            class=\"svg-icon svg-icon-3 svg-icon-success\"\r\n            style=\"margin-left: 1.5rem\"\r\n          >\r\n          </span>\r\n          <span\r\n            *ngIf=\"dataItem.userStatus === 'Inactive'\"\r\n            ngbTooltip=\"Inactive\"\r\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen040.svg'\"\r\n            class=\"svg-icon svg-icon-3 svg-icon-danger text-danger\"\r\n            style=\"margin-left: 1.5rem\"\r\n          >\r\n          </span>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-dropdownlist\r\n            [data]=\"filterOptions\"\r\n            [value]=\"getFilterValue(filter, column)\"\r\n            (valueChange)=\"onStatusFilterChange($event, filter, column)\"\r\n            textField=\"text\"\r\n            valueField=\"value\"\r\n          >\r\n          </kendo-dropdownlist>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Updated Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'lastUpdatedDate'\"\r\n        field=\"lastUpdatedDate\"\r\n        title=\"Updated Date\"\r\n        [width]=\"160\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('lastUpdatedDate')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        filter=\"date\"\r\n        format=\"MM/dd/yyyy\"\r\n        [maxResizableWidth]=\"240\"\r\n        [hidden]=\"getHiddenField('lastUpdatedDate')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <span class=\"text-gray-600 fs-1r\">{{\r\n            dataItem.lastUpdatedDate | date : \"MM/dd/yyyy hh:mm a\"\r\n          }}</span>\r\n          <br /><span class=\"text-gray-600 fs-1r\">{{\r\n            dataItem.lastUpdatedByFullName\r\n          }}</span>\r\n        </ng-template>\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n    </ng-container>\r\n\r\n    <ng-template kendoGridNoRecordsTemplate>\r\n      <div class=\"custom-no-records\" *ngIf=\"loading || isLoading\">\r\n        <div class=\"d-flex align-items-center justify-content-center\">\r\n          <div class=\"custom-colored-spinner-sm me-3\" role=\"status\">\r\n            <span class=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n          <span class=\"text-muted\">Loading users... Please wait...</span>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"custom-no-records\"\r\n        *ngIf=\"!loading && !isLoading && serverSideRowData.length === 0\"\r\n      >\r\n        <div class=\"text-center\">\r\n          <i class=\"fas fa-users text-muted mb-2\" style=\"font-size: 2rem\"></i>\r\n          <p class=\"text-muted\">No users found</p>\r\n          <button kendoButton (click)=\"loadTable()\" class=\"btn-primary\">\r\n            <i class=\"fas fa-refresh me-2\"></i>Refresh\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n  </kendo-grid>\r\n</div>\r\n"], "mappings": ";AAeA,SACEA,OAAO,EACPC,YAAY,EACZC,oBAAoB,QAEf,MAAM;AACb,SAAiBC,eAAe,QAAwB,iBAAiB;AAKzE,SAASC,gBAAgB,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtB3DC,EAHN,CAAAC,cAAA,aAAqE,aACtC,aACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IAmCEH,EADF,CAAAC,cAAA,cAA2D,wBASxD;IALCD,EAAA,CAAAI,gBAAA,2BAAAC,gFAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,UAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,UAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAIxBN,EAFA,CAAAc,UAAA,qBAAAC,0EAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWJ,MAAA,CAAAO,eAAA,CAAAV,MAAA,CAAuB;IAAA,EAAC,2BAAAD,gFAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAClBJ,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC,mBAAAC,wEAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CACzBJ,MAAA,CAAAU,WAAA,EAAa;IAAA,EAAC;IAE3BnB,EADG,CAAAG,YAAA,EAAgB,EACb;IAENH,EAAA,CAAAoB,SAAA,wBAAuC;IAIrCpB,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAA0E;IAAhBD,EAAA,CAAAc,UAAA,mBAAAO,iEAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAa,GAAA,EAAK;IAAA,EAAC;IACvEtB,EAAA,CAAAoB,SAAA,eAGQ;IACRpB,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAS,kEAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IAGxBxB,EAAA,CAAAoB,SAAA,aAIK;IACPpB,EAAA,CAAAG,YAAA,EAAS;IAuBTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAW,kEAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAiB,UAAA,EAAY;IAAA,EAAC;IAGtB1B,EAAA,CAAAoB,SAAA,aAA2B;IAC7BpB,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAa,kEAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmB,WAAA,EAAa;IAAA,EAAC;IAGvB5B,EAAA,CAAAoB,SAAA,aAA+B;IACjCpB,EAAA,CAAAG,YAAA,EAAS;;;;IA9ELH,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAA8B,WAAA,oBAAsB;IAEtB9B,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAG,UAAA,CAAwB;IACxBZ,EAAA,CAAAgC,UAAA,qBAAoB;IAYKhC,EAAA,CAAA6B,SAAA,GAA6B;IAA7B7B,EAAA,CAAAiC,iBAAA,CAAAxB,MAAA,CAAAyB,IAAA,CAAAC,aAAA,MAA6B;IAMtDnC,EAAA,CAAA6B,SAAA,GAA8D;IAA9D7B,EAAA,CAAAgC,UAAA,+DAA8D;IAc9DhC,EAAA,CAAA6B,SAAA,GAA+B;IAC/B7B,EADA,CAAAoC,WAAA,eAAA3B,MAAA,CAAA4B,UAAA,CAA+B,gBAAA5B,MAAA,CAAA4B,UAAA,CACC;;;;;;IAqD9BrC,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAkC,2FAAAhC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA+B,cAAA,CAAAC,MAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAA+B,cAAA,CAAAC,MAAA,GAAAnC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAMvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAAsB,gBACM;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAsC,2FAAApC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA+B,cAAA,CAAAG,IAAA,EAAArC,MAAA,MAAAG,MAAA,CAAA+B,cAAA,CAAAG,IAAA,GAAArC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAiC;IAMrCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,eAA6C,kBAK1C;IAFCD,EAAA,CAAAc,UAAA,mBAAA8B,wEAAA;MAAA5C,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAoC,oBAAA,EAAsB;IAAA,EAAC;IAGhC7C,EAAA,CAAAoB,SAAA,aAA4B;IAACpB,EAAA,CAAAE,MAAA,uBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAc,UAAA,mBAAAgC,wEAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAsC,eAAA,EAAiB;IAAA,EAAC;IAG3B/C,EAAA,CAAAoB,SAAA,aAA4B;IAACpB,EAAA,CAAAE,MAAA,eAC/B;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IApCEH,EAAA,CAAA6B,SAAA,GAAqC;IAArC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAuC,qBAAA,CAAAP,MAAA,CAAqC;IACrCzC,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAA+B,cAAA,CAAAC,MAAA,CAAmC;IAUnCzC,EAAA,CAAA6B,SAAA,GAAoC;IAApC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAuC,qBAAA,CAAAC,KAAA,CAAoC;IACpCjD,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAA+B,cAAA,CAAAG,IAAA,CAAiC;;;;;IApBzC3C,EAAA,CAAAkD,UAAA,IAAAC,8CAAA,mBAGC;;;;IAFEnD,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA2C,mBAAA,CAAyB;;;;;;IAoFxBpD,EAAA,CAAAC,cAAA,YAKC;IADCD,EAAA,CAAAc,UAAA,mBAAAuC,mGAAA;MAAArD,EAAA,CAAAO,aAAA,CAAA+C,GAAA;MAAA,MAAAC,WAAA,GAAAvD,EAAA,CAAAU,aAAA,GAAA8C,SAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgD,UAAA,CAAAF,WAAA,CAAoB;IAAA,EAAC;IAE9BvD,EAAA,CAAAoB,SAAA,eAIO;IACTpB,EAAA,CAAAG,YAAA,EAAI;;;IAJAH,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;;;;;;IA9BnEhC,EAAA,CAAAC,cAAA,YAIC;IADCD,EAAA,CAAAc,UAAA,mBAAA4C,+FAAA;MAAA,MAAAH,WAAA,GAAAvD,EAAA,CAAAO,aAAA,CAAAoD,GAAA,EAAAH,SAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmD,IAAA,CAAAL,WAAA,CAAAM,MAAA,CAAqB;IAAA,EAAC;IAE/B7D,EAAA,CAAAoB,SAAA,eAIO;IACTpB,EAAA,CAAAG,YAAA,EAAI;IAaJH,EAAA,CAAAkD,UAAA,IAAAY,+EAAA,gBAKC;;;;IAtBG9D,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;IAkBhEhC,EAAA,CAAA6B,SAAA,EAAuB;IAAvB7B,EAAA,CAAAgC,UAAA,SAAAuB,WAAA,CAAAQ,QAAA,CAAuB;;;;;IArC9B/D,EAAA,CAAAC,cAAA,4BAWC;IACCD,EAAA,CAAAkD,UAAA,IAAAc,2EAAA,0BAAgD;IAqClDhE,EAAA,CAAAG,YAAA,EAAoB;;;;IAxClBH,EAAA,CAAAiE,UAAA,CAAAjE,EAAA,CAAAkE,eAAA,IAAAC,GAAA,EAAsD;IACtDnE,EAPA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,WACiC,gBAAArE,EAAA,CAAAkE,eAAA,KAAAI,GAAA,EACuB,2BAC7C,qBACN,WAAA7D,MAAA,CAAA8D,cAAA,WAEe;;;;;IAwD/BvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAC,WAAA,CAAAC,YAAA,MACF;;;;;IAIF1E,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAoB,SAAA,qCAAiE;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAA2C,UAAA,CAAiB,WAAAC,SAAA,CACA,gBACF;;;;;IAvBrB5E,EAAA,CAAAC,cAAA,4BAWC;IAQCD,EAPA,CAAAkD,UAAA,IAAA2B,2EAAA,0BAAgD,IAAAC,2EAAA,0BAOwB;IAU1E9E,EAAA,CAAAG,YAAA,EAAoB;;;;IAnBlBH,EANA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,iBACuC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACiB,2BAC7C,WAAA7D,MAAA,CAAA8D,cAAA,iBACe,oBACtB;;;;;IA+DjBvE,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAoB,SAAA,eAAiD;IACnDpB,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAA6B,SAAA,EAAmC;IAAnC7B,EAAA,CAAAgC,UAAA,cAAA+C,YAAA,CAAAC,KAAA,SAAAhF,EAAA,CAAAiF,cAAA,CAAmC;;;;;IAI3CjF,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAoB,SAAA,qCAAiE;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAkD,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IApBrBnF,EAAA,CAAAC,cAAA,4BAUC;IAMCD,EALA,CAAAkD,UAAA,IAAAkC,2EAAA,0BAAgD,IAAAC,2EAAA,0BAKwB;IAU1ErF,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,UAC+B,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACwB,WAAA7D,MAAA,CAAA8D,cAAA,UACrC,oBACf;;;;;IAgCjBvE,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAoB,SAAA,eAAiD;IACnDpB,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAA6B,SAAA,EAAmC;IAAnC7B,EAAA,CAAAgC,UAAA,cAAAsD,YAAA,CAAAC,KAAA,SAAAvF,EAAA,CAAAiF,cAAA,CAAmC;;;;;IAI3CjF,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAAwD,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IApBpBzF,EAAA,CAAAC,cAAA,4BAUC;IAMCD,EALA,CAAAkD,UAAA,IAAAwC,2EAAA,0BAAgD,IAAAC,2EAAA,0BAKwB;IAa1E3F,EAAA,CAAAG,YAAA,EAAoB;;;;IApBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,UAC+B,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACwB,WAAA7D,MAAA,CAAA8D,cAAA,UACrC,oBACf;;;;;IAmCjBvE,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAoB,SAAA,eAAmD;IACrDpB,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAA6B,SAAA,EAAqC;IAArC7B,EAAA,CAAAgC,UAAA,cAAA4D,YAAA,CAAAC,OAAA,SAAA7F,EAAA,CAAAiF,cAAA,CAAqC;;;;;IAI7CjF,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAoB,SAAA,qCAAiE;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAA8D,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IApBrB/F,EAAA,CAAAC,cAAA,4BAUC;IAMCD,EALA,CAAAkD,UAAA,IAAA8C,2EAAA,0BAAgD,IAAAC,2EAAA,0BAKwB;IAU1EjG,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,YACiC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACsB,WAAA7D,MAAA,CAAA8D,cAAA,YACnC,oBACjB;;;;;IAgCjBvE,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAoB,SAAA,eAAoD;IACtDpB,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAA6B,SAAA,EAAsC;IAAtC7B,EAAA,CAAAgC,UAAA,cAAAkE,YAAA,CAAAC,QAAA,SAAAnG,EAAA,CAAAiF,cAAA,CAAsC;;;;;IAI9CjF,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAoB,SAAA,qCAAiE;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAoE,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IApBrBrG,EAAA,CAAAC,cAAA,4BAUC;IAMCD,EALA,CAAAkD,UAAA,IAAAoD,2EAAA,0BAAgD,IAAAC,2EAAA,0BAKwB;IAU1EvG,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,aACkC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACqB,WAAA7D,MAAA,CAAA8D,cAAA,aAClC,oBAClB;;;;;IAgCjBvE,EAAA,CAAAoB,SAAA,eAOO;;;IAJLpB,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IAKjEhC,EAAA,CAAAoB,SAAA,eAOO;;;IAJLpB,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IAHjEhC,EARA,CAAAkD,UAAA,IAAAsD,kFAAA,mBAMC,IAAAC,kFAAA,mBAQA;;;;IAbEzG,EAAA,CAAAgC,UAAA,SAAA0E,YAAA,CAAAC,UAAA,cAAsC;IAQtC3G,EAAA,CAAA6B,SAAA,EAAwC;IAAxC7B,EAAA,CAAAgC,UAAA,SAAA0E,YAAA,CAAAC,UAAA,gBAAwC;;;;;;IAS3C3G,EAAA,CAAAC,cAAA,6BAMC;IAHCD,EAAA,CAAAc,UAAA,yBAAA8F,sHAAAtG,MAAA;MAAA,MAAAuG,OAAA,GAAA7G,EAAA,CAAAO,aAAA,CAAAuG,IAAA;MAAA,MAAAC,UAAA,GAAAF,OAAA,CAAArD,SAAA;MAAA,MAAAwD,UAAA,GAAAH,OAAA,CAAAI,MAAA;MAAA,MAAAxG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAeJ,MAAA,CAAAyG,oBAAA,CAAA5G,MAAA,EAAAyG,UAAA,EAAAC,UAAA,CAA4C;IAAA,EAAC;IAI9DhH,EAAA,CAAAG,YAAA,EAAqB;;;;;;IALnBH,EADA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA0G,aAAA,CAAsB,UAAA1G,MAAA,CAAA2G,cAAA,CAAAL,UAAA,EAAAC,UAAA,EACkB;;;;;IAhC9ChH,EAAA,CAAAC,cAAA,4BAUC;IAmBCD,EAlBA,CAAAkD,UAAA,IAAAmE,2EAAA,0BAAgD,IAAAC,2EAAA,0BAkBwB;IAU1EtH,EAAA,CAAAG,YAAA,EAAoB;;;;IA9BlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,eACoC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACmB,WAAA7D,MAAA,CAAA8D,cAAA,eAChC,oBACpB;;;;;IAgDjBvE,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAEhC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAoB,SAAA,SAAM;IAAApB,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAEtC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IALyBH,EAAA,CAAA6B,SAAA,EAEhC;IAFgC7B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAuH,WAAA,OAAAC,YAAA,CAAAC,eAAA,wBAEhC;IACsCzH,EAAA,CAAA6B,SAAA,GAEtC;IAFsC7B,EAAA,CAAAiC,iBAAA,CAAAuF,YAAA,CAAAE,qBAAA,CAEtC;;;;;IAQF1H,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAAoB,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnEpB,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAAgC,UAAA,WAAA2F,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IAhCrC7H,EAAA,CAAAC,cAAA,4BAaC;IASCD,EARA,CAAAkD,UAAA,IAAA4E,2EAAA,0BAAgD,IAAAC,2EAAA,0BAa/C;IAeH/H,EAAA,CAAAG,YAAA,EAAoB;;;;IA9BlBH,EARA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,oBACyC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACc,0BAG9C,WAAA7D,MAAA,CAAA8D,cAAA,oBACmB,oBACzB;;;;;IAhSvBvE,EAAA,CAAAgI,uBAAA,GAAiD;IAoR/ChI,EAlRA,CAAAkD,UAAA,IAAA+E,6DAAA,iCAWC,IAAAC,6DAAA,gCAoDA,IAAAC,6DAAA,gCA4DA,IAAAC,6DAAA,gCA6BA,IAAAC,6DAAA,gCAgCA,IAAAC,6DAAA,gCA6BA,IAAAC,6DAAA,gCA6BA,IAAAC,6DAAA,gCA6CA;;;;;IA9RExI,EAAA,CAAA6B,SAAA,EAAyB;IAAzB7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,cAAyB;IAoDzBzI,EAAA,CAAA6B,SAAA,EAA+B;IAA/B7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,oBAA+B;IA6D/BzI,EAAA,CAAA6B,SAAA,EAAwB;IAAxB7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,aAAwB;IA6BxBzI,EAAA,CAAA6B,SAAA,EAAwB;IAAxB7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,aAAwB;IAgCxBzI,EAAA,CAAA6B,SAAA,EAA0B;IAA1B7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,eAA0B;IA6B1BzI,EAAA,CAAA6B,SAAA,EAA2B;IAA3B7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,gBAA2B;IA6B3BzI,EAAA,CAAA6B,SAAA,EAA6B;IAA7B7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,kBAA6B;IA0C7BzI,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,uBAAkC;;;;;IAgD/BzI,EAHN,CAAAC,cAAA,cAA4D,cACI,cACF,eAC1B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAE5DF,EAF4D,CAAAG,YAAA,EAAO,EAC3D,EACF;;;;;;IAKJH,EAJF,CAAAC,cAAA,cAGC,cAC0B;IACvBD,EAAA,CAAAoB,SAAA,YAAoE;IACpEpB,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8D;IAA1CD,EAAA,CAAAc,UAAA,mBAAA4H,uEAAA;MAAA1I,EAAA,CAAAO,aAAA,CAAAoI,IAAA;MAAA,MAAAlI,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmI,SAAA,EAAW;IAAA,EAAC;IACvC5I,EAAA,CAAAoB,SAAA,YAAmC;IAAApB,EAAA,CAAAE,MAAA,eACrC;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAXNH,EARA,CAAAkD,UAAA,IAAA2F,8CAAA,kBAA4D,IAAAC,8CAAA,kBAW3D;;;;IAX+B9I,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAsI,OAAA,IAAAtI,MAAA,CAAAuI,SAAA,CAA0B;IAUvDhJ,EAAA,CAAA6B,SAAA,EAA8D;IAA9D7B,EAAA,CAAAgC,UAAA,UAAAvB,MAAA,CAAAsI,OAAA,KAAAtI,MAAA,CAAAuI,SAAA,IAAAvI,MAAA,CAAAwI,iBAAA,CAAAC,MAAA,OAA8D;;;ADtbvE,OAAM,MAAOC,iBAAiB;EAiNlBC,YAAA;EACAC,GAAA;EACAC,MAAA;EACAC,KAAA;EACAC,YAAA;EACDC,UAAA;EACCC,wBAAA;EACAC,eAAA;EACAC,kBAAA;EAxNeC,IAAI;EAE7B;EACOZ,iBAAiB,GAAU,EAAE;EAC7Ba,QAAQ,GAAU,EAAE;EACpBC,cAAc,GAAY,KAAK;EAE/BhB,OAAO,GAAY,KAAK;EACxBC,SAAS,GAAY,KAAK;EAEjCgB,SAAS,GAAQ,EAAE;EAEnB;EACOpJ,UAAU,GAAW,EAAE;EACtBqJ,WAAW,GAAG,IAAItK,OAAO,EAAU;EACnCuK,kBAAkB;EAE1B;EACOC,MAAM,GAA8B;IAAEC,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACjEC,UAAU,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACrEE,aAAa,GAIf,EAAE;EAEApD,aAAa,GAAkD,CACpE;IAAEqD,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,CACxC;EAED;EACOzH,qBAAqB,GAAG;IAC7BP,MAAM,EAAE,CACN;MAAE+H,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,CACS;IAClDxH,KAAK,EAAE,EAAmD,CAAE;GAC7D;EAED;EACOG,mBAAmB,GAAG,KAAK;EAC3BZ,cAAc,GAGjB,EAAE;EAEN;EACOkI,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7B5G,YAAY,GAAa,EAAE;EAC3B6G,gBAAgB,GAAa,EAAE;EAC/BC,UAAU;EACVC,YAAY;EACZ9I,UAAU,GAAG,KAAK;EAEzB;EACO+I,gBAAgB,GAQlB,CACH;IACEC,KAAK,EAAE,QAAQ;IACf9F,KAAK,EAAE,QAAQ;IACf+F,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,cAAc;IACrB9F,KAAK,EAAE,MAAM;IACb+F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR;EACD;EACA;IACEJ,KAAK,EAAE,OAAO;IACd9F,KAAK,EAAE,OAAO;IACd+F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,OAAO;IACd9F,KAAK,EAAE,OAAO;IACd+F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,SAAS;IAChB9F,KAAK,EAAE,OAAO;IACd+F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,UAAU;IACjB9F,KAAK,EAAE,MAAM;IACb+F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,QAAQ;IACf9F,KAAK,EAAE,QAAQ;IACf+F,KAAK,EAAE,GAAG;IACVE,IAAI,EAAE,QAAQ;IACdD,OAAO,EAAE,KAAK;IACdG,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,iBAAiB;IACxB9F,KAAK,EAAE,cAAc;IACrB+F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,CACF;EAED;EACOE,iBAAiB,GAA4B,EAAE;EAEtD;EAEA;EACOC,IAAI,GAAqB,CAAC;IAAEP,KAAK,EAAE,iBAAiB;IAAEQ,GAAG,EAAE;EAAM,CAAE,CAAC;EAE3E;EACQC,kBAAkB;EAE1B;EACiBC,cAAc,GAAG,2BAA2B;EAE7D;EACO7J,IAAI,GAAe;IACxB8J,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACb9J,aAAa,EAAE,CAAC;IAChB+J,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EACMC,IAAI,GAAW,CAAC;EAEvB;EACOC,aAAa,GAA2C,CAC7D;IAAE9B,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAK,CAAE,EACpC;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,EAC9C;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,CAC/C;EAED;EACO8B,aAAa,GAAU,EAAE;EACzBC,aAAa,GAAY,KAAK;EAErC;EACOC,cAAc,GAMjB;IACFC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE;GACb;EAED;EACOC,eAAe,GAAG,KAAK;EACvBC,gBAAgB,GAAW,QAAQ;EAE1C;EACOC,eAAe,GAAQ,EAAE;EAEhCC,YACU9D,YAAyB,EACzBC,GAAsB,EACtBC,MAAc,EACdC,KAAqB,EACrBC,YAAsB;EAAE;EACzBC,UAAsB,EACrBC,wBAAkD,EAClDC,eAAiC,EACjCC,kBAAsC;IARtC,KAAAR,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,UAAU,GAAVA,UAAU;IACT,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;EACzB;EAEHuD,QAAQA,CAAA;IACN,IAAI,CAACnD,SAAS,GAAG,IAAI,CAACP,UAAU,CAAC2D,eAAe,EAAE;IAClDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACtD,SAAS,CAAC;IAEjD;IACA,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvCsD,IAAI,CAAC3N,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/C2N,SAAS,CAAEC,UAAU,IAAI;MACxBJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEG,UAAU,CAAC;MACtD,IAAI,CAACvL,IAAI,CAAC+J,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MACb;MACA,IAAI,CAACtD,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB;MACA,IAAI,CAACK,GAAG,CAACqE,aAAa,EAAE;MACxB,IAAI,CAAC9E,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACkD,kBAAkB,GAAG,IAAI,CAACxC,MAAM,CAACqE,MAAM,CAACH,SAAS,CAAEI,KAAK,IAAI;MAC/D,IAAIA,KAAK,YAAY9N,eAAe,EAAE;QACpC,IAAI,CAAC+N,aAAa,EAAE;MACtB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,aAAa,EAAE;IAEpB;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB;IACA,IAAI,CAACC,kBAAkB,EAAE;IAEzB;IACA,IAAI,CAACC,UAAU,EAAE;IAEjB;IACA,IAAI,CAACC,gCAAgC,EAAE;IAEvC;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQF,gCAAgCA,CAAA;IACtC;IACA,IAAI,CAAClD,cAAc,GAAG,IAAI,CAACI,gBAAgB,CAACiD,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACjD,KAAK,CAAC;IACnE,IAAI,CAACN,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAE3C;IACA,IAAI,CAAC5G,YAAY,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;IAEvD;IACA,IAAI,CAAC6G,gBAAgB,GAAG,IAAI,CAACD,cAAc,CAACb,MAAM,CAC/CmE,GAAG,IAAK,CAAC,IAAI,CAAClK,YAAY,CAACC,QAAQ,CAACiK,GAAG,CAAC,CAC1C;IAED;IACA,IAAI,CAACpD,UAAU,GAAG,IAAI,CAACrB,IAAI;IAC3B,IAAI,CAACsB,YAAY,GAAG,IAAI,CAACtB,IAAI;EAC/B;EAEA0E,eAAeA,CAAA;IACb;IACA;IACAJ,UAAU,CAAC,MAAK;MACd,IAAI,CAACvF,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA4F,cAAcA,CAAA;IACZ;IACA,IAAI,CAACzF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACJ,SAAS,EAAE;IAChB,IAAI,CAACoF,kBAAkB,EAAE;EAC3B;EAEA;EACAC,UAAUA,CAAA;IACR;IACA,IAAI,CAAC/L,IAAI,CAAC+J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACT,IAAI,GAAG,CAAC;MAAEP,KAAK,EAAE,iBAAiB;MAAEQ,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAC1B,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACzJ,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACmI,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACJ,SAAS,EAAE;IAChB,IAAI,CAACoF,kBAAkB,EAAE;EAC3B;EAEA;EACApM,WAAWA,CAAA;IACT;IACA,IAAI,CAACmH,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAAC9G,IAAI,CAAC+J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAAClC,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC/H,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAC5B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACgI,SAAS,EAAE;EAClB;EAEA;EAEA;EAEA6F,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC3C,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC4C,WAAW,EAAE;IACvC;IACA,IAAI,IAAI,CAACxE,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACwE,WAAW,EAAE;IACvC;IACA,IAAI,CAACzE,WAAW,CAAC0E,QAAQ,EAAE;EAC7B;EACA;EACAC,0BAA0BA,CAAA;IACxB,IAAI,CAAC7F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACW,eAAe,CAACkF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACzF,GAAG,CAACqE,aAAa,EAAE;IAExB;IACA,MAAMqB,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAAC9M,IAAI,CAAC8J,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfT,IAAI,EAAE,IAAI,CAACA,IAAI;MACfzB,MAAM,EAAE,IAAI,CAACA,MAAM,CAACE,OAAO;MAC3B4E,MAAM,EAAE,IAAI,CAACrO,UAAU;MACvBsO,cAAc,EAAE,IAAI,CAAClF,SAAS,CAACnG;KAChC;IAEDwJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAC1M,UAAU,CAAC;IAC/DyM,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyB,KAAK,CAAC;IACxC1B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACvE,OAAO,EAAE,YAAY,EAAE,IAAI,CAACC,SAAS,CAAC;IAEpF,IAAI,CAACI,YAAY,CAAC+F,oBAAoB,CAACJ,KAAK,CAAC,CAACvB,SAAS,CAAC;MACtDsB,IAAI,EAAGM,IAYN,IAAI;QACH;QACA,IACEA,IAAI,CAACC,OAAO,IACXD,IAAI,CAACE,YAAY,IAChBF,IAAI,CAACE,YAAY,CAACC,MAAM,IACxBH,IAAI,CAACE,YAAY,CAACC,MAAM,CAACrG,MAAM,GAAG,CAAE,EACtC;UACA,MAAMqG,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7DlC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAED,MAAM,CAAC;UAC9C,IAAI,CAACE,mBAAmB,EAAE;QAC5B,CAAC,MAAM;UACL;UACA,MAAMH,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMM,QAAQ,GAAGJ,YAAY,CAACF,IAAI,IAAI,EAAE;UACxC,MAAMO,KAAK,GAAGL,YAAY,CAACK,KAAK,IAAI,CAAC;UAErC,IAAI,CAAC5F,cAAc,GAAG2F,QAAQ,CAACxG,MAAM,KAAK,CAAC;UAC3C,IAAI,CAACD,iBAAiB,GAAGyG,QAAQ;UACjC,IAAI,CAAC5F,QAAQ,GAAG,IAAI,CAACb,iBAAiB;UACtC,IAAI,CAAC/G,IAAI,CAACC,aAAa,GAAGwN,KAAK;UAC/B,IAAI,CAACzN,IAAI,CAACgK,UAAU,GAAG0D,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACzN,IAAI,CAAC8J,IAAI,CAAC;QAC1D;QACA,IAAI,CAACrC,eAAe,CAACkF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDU,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAAC1G,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACW,eAAe,CAACkF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDH,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC5F,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACW,eAAe,CAACkF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACzF,GAAG,CAACqE,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA;EACM9E,SAASA,CAAA;IAAA,IAAAkH,KAAA;IAAA,OAAAC,iBAAA;MACb;MACAD,KAAI,CAAClB,0BAA0B,EAAE;IAAC;EACpC;EAEQa,mBAAmBA,CAAA;IACzB,IAAI,CAAC1F,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACd,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACa,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC5H,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAACgK,UAAU,GAAG,CAAC;EAC1B;EAEA;EACA/K,WAAWA,CAAA;IACT;IACA,IAAI,CAACP,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACmI,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACiB,WAAW,CAAC6E,IAAI,CAAC,EAAE,CAAC;EAC3B;EAEA;EACA/L,eAAeA,CAAA;IACb,IAAI,CAACnC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACuJ,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC/H,cAAc,GAAG,EAAE;IACxB,IAAI,CAACN,IAAI,CAAC+J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAACtD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEA;EACA/F,oBAAoBA,CAAA;IAClB,IAAI,CAACX,IAAI,CAAC+J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAACtD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEA;EACAoH,qBAAqBA,CAAA;IACnB,IAAI,CAAC5M,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;EACA2K,SAASA,CAAA;IACP,MAAMkC,WAAW,GAKb;MACFC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,UAAU;MACrBnE,UAAU,EAAE;KACb;IAED,IAAI,CAAC7C,YAAY,CAACiH,WAAW,CAACJ,WAAW,CAAC,CAACzC,SAAS,CAAC;MACnDsB,IAAI,EAAGM,IAIN,IAAI;QACH,IAAIA,IAAI,IAAIA,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,CAACgB,OAAO,EAAE;UAC1D,IAAI,CAACtN,qBAAqB,CAACC,KAAK,GAAG,CACjC;YAAEuH,IAAI,EAAE,WAAW;YAAEC,KAAK,EAAE;UAAI,CAAE,EAClC,GAAG2E,IAAI,CAACE,YAAY,CAACgB,OAAO,CAACjC,GAAG,CAAE1L,IAA0B,KAAM;YAChE6H,IAAI,EAAE7H,IAAI,CAACwD,QAAQ;YACnBsE,KAAK,EAAE9H,IAAI,CAACwD;WACb,CAAC,CAAC,CACJ;QACH;MACF,CAAC;MACDqJ,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACA,IAAI,CAACxM,qBAAqB,CAACC,KAAK,GAAG,CAAC;UAAEuH,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC;MACzE;KACD,CAAC;IACF,IAAI,CAACrB,YAAY,CACdmH,qBAAqB,CAAC,EAAE,CAAC,CACzB/C,SAAS,CAAEgD,WAAgB,IAAI;MAC9B,IAAI,CAACvD,eAAe,GAAGuD,WAAW,CAAClB,YAAY;IACjD,CAAC,CAAC;EACN;EAEA;EACAtB,kBAAkBA,CAAA;IAChB,IAAI,CAAC5E,YAAY,CAACqH,iBAAiB,EAAE,CAACjD,SAAS,CAAC;MAC9CsB,IAAI,EAAGM,IAAS,IAAI;QAClB,IAAIA,IAAI,IAAIA,IAAI,CAACsB,UAAU,EAAE;UAC3B,IAAI,CAACjE,cAAc,GAAG2C,IAAI,CAACsB,UAAU;QACvC;MACF,CAAC;MACDlB,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACJ;EAEA;EACAmB,iBAAiBA,CAACC,SAAc;IAC9B,IAAI,CAACrE,aAAa,GAAGqE,SAAS,CAACC,YAAY,IAAI,EAAE;IACjD,IAAI,CAACrE,aAAa,GAChB,IAAI,CAACD,aAAa,CAACrD,MAAM,KAAK,IAAI,CAACD,iBAAiB,CAACC,MAAM;IAC7D,IAAI,CAAC6D,eAAe,GAAG,IAAI,CAACR,aAAa,CAACrD,MAAM,GAAG,CAAC;EACtD;EAEA;EACA4H,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACtE,aAAa,EAAE;MACtB,IAAI,CAACD,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACD,aAAa,GAAG,CAAC,GAAG,IAAI,CAACtD,iBAAiB,CAAC;MAChD,IAAI,CAACuD,aAAa,GAAG,IAAI;IAC3B;IACA,IAAI,CAACO,eAAe,GAAG,IAAI,CAACR,aAAa,CAACrD,MAAM,GAAG,CAAC;EACtD;EAEA;EACA6H,UAAUA,CAACC,IAAS;IAClB,IACEC,OAAO,CACL,wCAAwCD,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACG,QAAQ,GAAG,CAC3E,EACD;MACA;MACA,IAAI,CAACpI,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAMoI,UAAU,GAAG;QACjBvN,MAAM,EAAEmN,IAAI,CAACnN,MAAM;QACnBqL,cAAc,EAAE,IAAI,CAAClF,SAAS,CAACnG,MAAM,IAAI;OAC1C;MAED,IAAI,CAACuF,YAAY,CAAC2H,UAAU,CAACK,UAAU,CAAC,CAAC5D,SAAS,CAAC;QACjDsB,IAAI,EAAGuC,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YAC4B,IAAI,CAAC5H,wBAAwB,CAAC6H,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE3F,IAAI,CAAC1I,SAAS,EAAE,CAAC,CAAC;YAClB,IAAI,CAACoF,kBAAkB,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC;QACDwB,KAAK,EAAGA,KAAc,IAAI;UACxBnC,OAAO,CAACmC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC1B,IAAI,CAAC9F,wBAAwB,CAAC8H,SAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC;UAEpF;UACA;UACA,IAAI,CAACzI,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACAyI,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAClF,aAAa,CAACrD,MAAM,KAAK,CAAC,EAAE;MACnC;MAC4B,IAAI,CAACQ,wBAAwB,CAAC6H,WAAW,CAAC,iCAAiC,EAAE,EAAE,CAAC;MAE5G;IACF;IAEA,IACEN,OAAO,CACL,mCAAmC,IAAI,CAAC1E,aAAa,CAACrD,MAAM,qBAAqB,IAAI,CAAC8D,gBAAgB,GAAG,CAC1G,EACD;MACA;MACA,IAAI,CAACjE,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAM0I,cAAc,GAAG;QACrBC,OAAO,EAAE,IAAI,CAACpF,aAAa,CAAC8B,GAAG,CAAE2C,IAAI,IAAKA,IAAI,CAACnN,MAAM,CAAC;QACtDpB,MAAM,EAAE,IAAI,CAACuK,gBAAgB;QAC7BkC,cAAc,EAAE,IAAI,CAAClF,SAAS,CAACnG,MAAM,IAAI;OAC1C;MAED,IAAI,CAACuF,YAAY,CAACqI,oBAAoB,CAACC,cAAc,CAAC,CAAClE,SAAS,CAAC;QAC/DsB,IAAI,EAAGuC,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YACA,IAAI,CAAC1I,SAAS,EAAE,CAAC,CAAC;YAClB,IAAI,CAACoF,kBAAkB,EAAE,CAAC,CAAC;YAC3B,IAAI,CAACzB,aAAa,GAAG,EAAE,CAAC,CAAC;YACzB,IAAI,CAACQ,eAAe,GAAG,KAAK;UAC9B;QACF,CAAC;QACDyC,KAAK,EAAGA,KAAc,IAAI;UACxBnC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C;UAC4B,IAAI,CAAC9F,wBAAwB,CAAC8H,SAAS,CAAC,wCAAwC,EAAE,EAAE,CAAC;UAEjH;UACA,IAAI,CAACzI,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACAvF,UAAUA,CAACuN,IAAS;IAClB,IACEC,OAAO,CACL,wCAAwCD,IAAI,CAACY,SAAS,IAAIZ,IAAI,CAACa,QAAQ,GAAG,CAC3E,EACD;MACA;MACA,IAAI,CAAC9I,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAM8I,UAAU,GAAG;QACjBjO,MAAM,EAAEmN,IAAI,CAACnN,MAAM;QACnBqL,cAAc,EAAE,IAAI,CAAClF,SAAS,CAACnG,MAAM,IAAI;OAC1C;MAED,IAAI,CAACuF,YAAY,CAAC3F,UAAU,CAACqO,UAAU,CAAC,CAACtE,SAAS,CAAC;QACjDsB,IAAI,EAAGuC,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACJ,IAAI,CAAC5H,wBAAwB,CAAC6H,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE3F;YACA,IAAI,CAAC1I,SAAS,EAAE,CAAC,CAAC;YAClB,IAAI,CAACoF,kBAAkB,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC;QACDwB,KAAK,EAAGA,KAAc,IAAI;UACxBnC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UACjB,IAAI,CAAC9F,wBAAwB,CAAC8H,SAAS,CAAC,sBAAsB,EAAE,EAAE,CAAC;UAE/F;UACA;UACA,IAAI,CAACzI,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEAhI,eAAeA,CAAC4M,KAAoB;IAClCP,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEM,KAAK,CAACmE,GAAG,EAAE,cAAc,EAAE,IAAI,CAACnR,UAAU,CAAC;IAChF,IAAIgN,KAAK,CAACmE,GAAG,KAAK,OAAO,EAAE;MACzB;MACA1E,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,IAAI,CAACrD,WAAW,CAAC6E,IAAI,CAAC,IAAI,CAAClO,UAAU,IAAI,EAAE,CAAC;IAC9C;EACF;EAEA;EACAK,cAAcA,CAAA;IACZ;IACAoM,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC1M,UAAU,CAAC;IACrDyM,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C;IACA,IAAI,CAACrD,WAAW,CAAC6E,IAAI,CAAC,IAAI,CAAClO,UAAU,IAAI,EAAE,CAAC;EAC9C;EAEA;EACAoR,mBAAmBA,CAAA;IASjB,IAAI7H,MAAM,GAQN;MACF8H,QAAQ,EAAE,IAAI;MACdhD,MAAM,EAAE,EAAE;MACViD,YAAY,EAAE;KACf;IAED;IACA,IAAIC,UAAkB;IACtB,IAAI,IAAI,CAACvR,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,KAAKwR,SAAS,EAAE;MAC7DD,UAAU,GAAG,EAAE;IACjB,CAAC,MAAM;MACLA,UAAU,GAAG,IAAI,CAACvR,UAAU;IAC9B;IACAuJ,MAAM,CAAC8E,MAAM,GAAGkD,UAAU,CAACE,IAAI,EAAE;IAEjC;IACA,IAAI,IAAI,CAAC9H,aAAa,IAAI,IAAI,CAACA,aAAa,CAACrB,MAAM,GAAG,CAAC,EAAE;MACvDiB,MAAM,CAAC+H,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC3H,aAAa,CAAC;IAC/C;IAEA;IACA,IAAI,IAAI,CAAC/H,cAAc,CAACC,MAAM,IAAI,IAAI,CAACD,cAAc,CAACC,MAAM,KAAK,IAAI,EAAE;MACrE0H,MAAM,CAAC+H,YAAY,CAACI,IAAI,CAAC;QACvBjH,KAAK,EAAE,YAAY;QACnBkH,QAAQ,EAAE,IAAI;QACd9H,KAAK,EAAE,IAAI,CAACjI,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA,IAAI,IAAI,CAACD,cAAc,CAACG,IAAI,IAAI,IAAI,CAACH,cAAc,CAACG,IAAI,KAAK,IAAI,EAAE;MACjEwH,MAAM,CAAC+H,YAAY,CAACI,IAAI,CAAC;QACvBjH,KAAK,EAAE,UAAU;QACjBkH,QAAQ,EAAE,IAAI;QACd9H,KAAK,EAAE,IAAI,CAACjI,cAAc,CAACG;OAC5B,CAAC;IACJ;IAEA,OAAOwH,MAAM;EACf;EAEA;EACOqI,UAAUA,CAAC5E,KAAqC;IACrD,IAAI,CAACvB,IAAI,GAAGuB,KAAK,CAACvB,IAAI;IACtB,IAAI,CAACnK,IAAI,CAAC+J,UAAU,GAAG2B,KAAK,CAACvB,IAAI,GAAGuB,KAAK,CAACoB,IAAI;IAC9C,IAAI,CAAC9M,IAAI,CAAC8J,IAAI,GAAG4B,KAAK,CAACoB,IAAI;IAC3B;IACA,IAAI,CAACjG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEO6J,YAAYA,CAAC7G,IAAsB;IACxC;IACA,MAAM8G,YAAY,GAAGC,KAAK,CAACC,OAAO,CAAChH,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;IACpD,IAAI,CAACA,IAAI,GAAG8G,YAAY,CAACxJ,MAAM,GAAG,CAAC,GAC/BwJ,YAAY,GACZ,CAAC;MAAErH,KAAK,EAAE,iBAAiB;MAAEQ,GAAG,EAAE;IAAM,CAAE,CAAC;IAE/C;IACA,IAAI,IAAI,CAACD,IAAI,CAAC1C,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAAChH,IAAI,CAACiK,OAAO,GAAG,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,CAACP,KAAK,IAAI,iBAAiB;MAC3D,IAAI,CAACnJ,IAAI,CAACkK,QAAQ,GAAG,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,IAAI,MAAM;IACjD,CAAC,MAAM;MACL,IAAI,CAAC3J,IAAI,CAACiK,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAACjK,IAAI,CAACkK,QAAQ,GAAG,MAAM;IAC7B;IAEA;IACA,IAAI,CAACrD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEOiK,YAAYA,CAAC1I,MAAiC;IACnD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,UAAU,GAAGH,MAAM;IACxB,IAAI,CAACI,aAAa,GAAG,IAAI,CAACuI,cAAc,CAAC3I,MAAM,CAAC;IAChD,IAAI,CAACjI,IAAI,CAAC+J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAACtD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEA;EAEA;EACOxB,cAAcA,CACnB+C,MAAiC,EACjClD,MAAyB;IAEzB,IAAI,CAACkD,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,IAAI,CAACpD,MAAM,EAAE;MACzC,OAAO,IAAI;IACb;IACA,MAAM8L,SAAS,GAAG5I,MAAM,CAACE,OAAO,CAAC2I,IAAI,CAClCC,CAAM,IAAKA,CAAC,IAAI,OAAO,IAAIA,CAAC,IAAIA,CAAC,CAAC5H,KAAK,KAAKpE,MAAM,CAACoE,KAAK,CAC1D;IACD,OAAO0H,SAAS,IAAI,OAAO,IAAIA,SAAS,GAAGA,SAAS,CAACtI,KAAK,GAAG,IAAI;EACnE;EAEA;EACOvD,oBAAoBA,CACzBuD,KAAoB,EACpBN,MAAiC,EACjClD,MAAyB;IAEzB,IAAI,CAACkD,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,IAAI,CAACpD,MAAM,EAAE;MACzCoG,OAAO,CAACmC,KAAK,CAAC,2BAA2B,EAAE;QAAErF,MAAM;QAAElD;MAAM,CAAE,CAAC;MAC9D;IACF;IAEA,MAAMiM,MAAM,GAAG/I,MAAM,CAACE,OAAO,CAAC8I,SAAS,CACpCF,CAAM,IAAKA,CAAC,IAAI,OAAO,IAAIA,CAAC,IAAIA,CAAC,CAAC5H,KAAK,KAAKpE,MAAM,CAACoE,KAAK,CAC1D;IACD,IAAI6H,MAAM,GAAG,CAAC,CAAC,EAAE;MACf/I,MAAM,CAACE,OAAO,CAAC+I,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;IAClC;IAEA,IAAIzI,KAAK,KAAK,IAAI,EAAE;MAClBN,MAAM,CAACE,OAAO,CAACiI,IAAI,CAAC;QAClBjH,KAAK,EAAEpE,MAAM,CAACoE,KAAK;QACnBkH,QAAQ,EAAE,IAAI;QACd9H,KAAK,EAAEA;OACR,CAAC;IACJ;IAEA,IAAI,CAACoI,YAAY,CAAC1I,MAAM,CAAC;EAC3B;EAEA;EACQ2I,cAAcA,CAAC3I,MAAiC;IAKtD,MAAME,OAAO,GAIR,EAAE;IAEP,IAAI,CAACF,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;MAC9B,OAAOA,OAAO;IAChB;IAEAF,MAAM,CAACE,OAAO,CAACgJ,OAAO,CAAEJ,CAAM,IAAI;MAChC,IAAIA,CAAC,IAAI,OAAO,IAAIA,CAAC,EAAE;QACrB;QACA5I,OAAO,CAACiI,IAAI,CAAC;UACXjH,KAAK,EAAE4H,CAAC,CAAC5H,KAAK;UACdkH,QAAQ,EAAEU,CAAC,CAACV,QAAQ;UACpB9H,KAAK,EAAEwI,CAAC,CAACxI;SACV,CAAC;MACJ,CAAC,MAAM,IAAIwI,CAAC,IAAI,SAAS,IAAIA,CAAC,EAAE;QAC9B;QACA5I,OAAO,CAACiI,IAAI,CAAC,GAAG,IAAI,CAACQ,cAAc,CAACG,CAAC,CAAC,CAAC;MACzC;IACF,CAAC,CAAC;IAEF,OAAO5I,OAAO;EAChB;EAEA;EACQyD,aAAaA,CAAA;IACnB,IAAI;MACF,MAAMwF,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACzH,cAAc,CAAC;MAE5D,IAAI,CAACuH,UAAU,EAAE;QACf;MACF;MAEA,MAAMvE,KAAK,GAwBP0E,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;MAE1B;MACA,IAAIvE,KAAK,IAAIA,KAAK,CAACnD,IAAI,EAAE;QACvB,IAAI,CAACA,IAAI,GAAGmD,KAAK,CAACnD,IAAI;QACtB,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE;UACrD,IAAI,CAAC1J,IAAI,CAACiK,OAAO,GAAG,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,CAACP,KAAK,IAAI,iBAAiB;UAC3D,IAAI,CAACnJ,IAAI,CAACkK,QAAQ,GAAG,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,IAAI,MAAM;QACjD;MACF;MAEA;MACA,IAAIkD,KAAK,IAAIA,KAAK,CAAC5E,MAAM,EAAE;QACzB,IAAI,CAACA,MAAM,GAAG4E,KAAK,CAAC5E,MAAM;QAC1B,IAAI,CAACG,UAAU,GAAGyE,KAAK,CAAC5E,MAAM;QAC9B,IAAI,CAACI,aAAa,GAAGwE,KAAK,CAACxE,aAAa,IAAI,EAAE;MAChD;MAEA;MACA,IAAIwE,KAAK,IAAIA,KAAK,CAAC7M,IAAI,EAAE;QACvB,IAAI,CAACA,IAAI,GAAG6M,KAAK,CAAC7M,IAAI;MACxB;MAEA,IAAI6M,KAAK,IAAIA,KAAK,CAAC1C,IAAI,KAAK+F,SAAS,EAAE;QACrC,IAAI,CAAC/F,IAAI,GAAG0C,KAAK,CAAC1C,IAAI;MACxB;MAEA;MACA,IAAI0C,KAAK,IAAIA,KAAK,CAACpD,iBAAiB,EAAE;QACpC,IAAI,CAACA,iBAAiB,GAAGoD,KAAK,CAACpD,iBAAiB;MAClD;MAEA;MACA,IAAIoD,KAAK,IAAIA,KAAK,CAACnO,UAAU,EAAE;QAC7B,IAAI,CAACA,UAAU,GAAGmO,KAAK,CAACnO,UAAU;MACpC;MAEA;MACA,IAAImO,KAAK,IAAIA,KAAK,CAACvM,cAAc,EAAE;QACjC,IAAI,CAACA,cAAc,GAAGuM,KAAK,CAACvM,cAAc;MAC5C;MAEA,IAAIuM,KAAK,IAAIA,KAAK,CAAC3L,mBAAmB,KAAKgP,SAAS,EAAE;QACpD,IAAI,CAAChP,mBAAmB,GAAG2L,KAAK,CAAC3L,mBAAmB;MACtD;IACF,CAAC,CAAC,OAAOoM,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;IACF;EACF;EAEA;EAEA;EACQ3B,aAAaA,CAAA;IACnB,MAAMkB,KAAK,GAwBP;MACFnD,IAAI,EAAE,IAAI,CAACA,IAAI;MACfzB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBjI,IAAI,EAAE,IAAI,CAACA,IAAI;MACfmK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfV,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzC/K,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B2J,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC/H,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCY,mBAAmB,EAAE,IAAI,CAACA;KAC3B;IAEDmQ,YAAY,CAACI,OAAO,CAAC,IAAI,CAAC5H,cAAc,EAAE0H,IAAI,CAACG,SAAS,CAAC7E,KAAK,CAAC,CAAC;EAClE;EAEA;EACAzN,GAAGA,CAAA;IACD,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC;EACd;EAEA;EACAA,IAAIA,CAACiQ,EAAU;IACbxG,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,sBAAsB,EAAEuG,EAAE,CAAC;IACpD;IACA,MAAMC,eAAe,GAKjB;MACF9H,IAAI,EAAE,IAAI;MAAE;MACZ+H,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC1K,YAAY,CAAC2K,IAAI,CAACpU,gBAAgB,EAAE+T,eAAe,CAAC;IAC1E;IACAI,QAAQ,CAACE,iBAAiB,CAACP,EAAE,GAAGA,EAAE;IAClCK,QAAQ,CAACE,iBAAiB,CAACC,kBAAkB,GAAG,IAAI,CAACpH,eAAe;IACpE;IACAiH,QAAQ,CAACE,iBAAiB,CAACE,SAAS,CAAC9G,SAAS,CAAE+G,aAAsB,IAAI;MACxE,IAAIA,aAAa,KAAK,IAAI,EAAE;QAC1B;QACA,IAAI,CAAC3L,SAAS,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;EAEO4L,cAAcA,CAACC,IAAc;IAClCpH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEmH,IAAI,CAAC;IACrC;EACF;EACOjT,YAAYA,CAAA;IACjB;IACA,MAAMkT,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiB,CACH;IAChB,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAACzS,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAACwH,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACkL,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACOC,aAAaA,CAACpH,KAAkC;IACrD,QAAQA,KAAK,CAAC6G,IAAI,CAAChK,KAAK;MACtB,KAAK,KAAK;QACR,IAAI,CAACwK,cAAc,EAAE;QACrB;MACF,KAAK,UAAU;QACb,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF,KAAK,UAAU;QACb,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF;QACE9H,OAAO,CAAC+H,IAAI,CAAC,wBAAwB,EAAExH,KAAK,CAAC6G,IAAI,CAAChK,KAAK,CAAC;IAC5D;EACF;EAEQwK,cAAcA,CAAA;IACpB,MAAMI,YAAY,GAAG;MACnBhL,OAAO,EAAE,EAAE;MACXiL,MAAM,EAAE;KACT;IAED,IAAI,CAAClM,YAAY,CAACmM,WAAW,CAACF,YAAY,CAAC,CAAC7H,SAAS,CAAC;MACpDsB,IAAI,EAAGuC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACmE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAACpE,QAAQ,CAACmE,UAAU,EAAE,WAAW,CAAC;QACtD;MACF,CAAC;MACDhG,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAClB,IAAI,CAAC9F,wBAAwB,CAAC8H,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;QAEhG;MACF;KACD,CAAC;EACJ;EAEQ0D,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAAC3I,aAAa,CAACrD,MAAM,KAAK,CAAC,EAAE;MACP,IAAI,CAACQ,wBAAwB,CAAC8H,SAAS,CAAC,+BAA+B,EAAE,EAAE,CAAC;MAExG;MACA;IACF;IAEA,MAAM6D,YAAY,GAAG;MACnBhL,OAAO,EAAE;QACPsH,OAAO,EAAE,IAAI,CAACpF,aAAa,CAAC8B,GAAG,CAAE2C,IAAI,IAAKA,IAAI,CAAC0E,MAAM;OACtD;MACDJ,MAAM,EAAE;KACT;IAED,IAAI,CAAClM,YAAY,CAACmM,WAAW,CAACF,YAAY,CAAC,CAAC7H,SAAS,CAAC;MACpDsB,IAAI,EAAGuC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACmE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAACpE,QAAQ,CAACmE,UAAU,EAAE,gBAAgB,CAAC;QAC3D;MACF,CAAC;MACDhG,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QAC3B,IAAI,CAAC9F,wBAAwB,CAAC8H,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;QAEzG;MACF;KACD,CAAC;EACJ;EAEQ2D,mBAAmBA,CAAA;IACzB,MAAME,YAAY,GAAG;MACnBhL,OAAO,EAAE;QACP5H,MAAM,EAAE,IAAI,CAACD,cAAc,CAACC,MAAM;QAClCE,IAAI,EAAE,IAAI,CAACH,cAAc,CAACG,IAAI;QAC9B8K,UAAU,EAAE,IAAI,CAAC7M;OAClB;MACD0U,MAAM,EAAE;KACT;IAED,IAAI,CAAClM,YAAY,CAACmM,WAAW,CAACF,YAAY,CAAC,CAAC7H,SAAS,CAAC;MACpDsB,IAAI,EAAGuC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACmE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAACpE,QAAQ,CAACmE,UAAU,EAAE,gBAAgB,CAAC;QAC3D;MACF,CAAC;MACDhG,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QAC3B,IAAI,CAAC9F,wBAAwB,CAAC8H,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;QAEzG;MACF;KACD,CAAC;EACJ;EAEQiE,aAAaA,CAACrG,IAAW,EAAEuG,QAAgB;IACjD;IACA;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,YAAY,CAACzG,IAAI,CAAC;IAC1C,MAAM0G,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEpK,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMwK,IAAI,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IACrCE,IAAI,CAACK,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BF,IAAI,CAACK,YAAY,CACf,UAAU,EACV,GAAGV,QAAQ,IAAI,IAAIW,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAC5D;IACDR,IAAI,CAACS,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChC/B,QAAQ,CAACgC,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC;IAC/BA,IAAI,CAACa,KAAK,EAAE;IACZlC,QAAQ,CAACgC,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;EACjC;EAEQH,YAAYA,CAACzG,IAAW;IAC9B,IAAIA,IAAI,CAAClG,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEhC,MAAM6N,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC7H,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM8H,OAAO,GAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC;IAEnC,KAAK,MAAMC,GAAG,IAAIhI,IAAI,EAAE;MACtB,MAAMiI,MAAM,GAAGN,OAAO,CAAC1I,GAAG,CAAEiJ,MAAM,IAAI;QACpC,MAAM7M,KAAK,GAAG2M,GAAG,CAACE,MAAM,CAAC;QACzB,OAAO,OAAO7M,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACpG,QAAQ,CAAC,GAAG,CAAC,GACnD,IAAIoG,KAAK,GAAG,GACZA,KAAK;MACX,CAAC,CAAC;MACFyM,OAAO,CAAC5E,IAAI,CAAC+E,MAAM,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC;IAEA,OAAOD,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;EAC3B;EAEA;EAEA;;;;;;EAMAI,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACvN,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACnG,MAAM,EAAE;MAC7CwJ,OAAO,CAACmC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAACxF,SAAS,CAAC;MACzD,IAAI,CAACN,wBAAwB,CAAC8H,SAAS,CACrC,8CAA8C,EAC9C,EAAE,CACH;MACD;IACF;IAEA,MAAMgG,gBAAgB,GAAU,EAAE;IAClC,MAAMC,aAAa,GAAU,EAAE;IAE/B,IAAI,IAAI,CAAC5N,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC6N,OAAO,EAAE;MAClC,IAAI,CAAC7N,IAAI,CAAC6N,OAAO,CAACrE,OAAO,CAAEpM,MAAW,IAAI;QACxC,IAAI,CAACA,MAAM,CAAC0Q,MAAM,EAAE;UAClB,MAAMC,UAAU,GAAG;YACjBrS,KAAK,EAAE0B,MAAM,CAAC1B,KAAK;YACnB8F,KAAK,EAAEpE,MAAM,CAACoE,KAAK;YACnBsM,MAAM,EAAE1Q,MAAM,CAAC0Q;WAChB;UACDH,gBAAgB,CAAClF,IAAI,CAACsF,UAAU,CAAC;QACnC,CAAC,MAAM;UACL,MAAMA,UAAU,GAAG;YACjBrS,KAAK,EAAE0B,MAAM,CAAC1B,KAAK;YACnB8F,KAAK,EAAEpE,MAAM,CAACoE,KAAK;YACnBsM,MAAM,EAAE1Q,MAAM,CAAC0Q;WAChB;UACDF,aAAa,CAACnF,IAAI,CAACsF,UAAU,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,MAAMC,qBAAqB,GAAG,IAAI,CAAC9M,WAAW,CAC3CZ,MAAM,CAAEmE,GAAG,IAAK,CAAC,IAAI,CAAClK,YAAY,CAACC,QAAQ,CAACiK,GAAG,CAAC,CAAC,CACjDD,GAAG,CAAC,CAAChD,KAAK,EAAEyM,KAAK,MAAM;MACtBzM,KAAK;MACL0M,UAAU,EAAED;KACb,CAAC,CAAC;IAEL;IACA,MAAMpI,QAAQ,GAAG;MACfsI,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,IAAI,CAACjO,SAAS,CAACnG,MAAM;MAC7B8G,UAAU,EAAE8M,aAAa;MACzB7M,aAAa,EAAEiN,qBAAqB;MACpCK,QAAQ,EAAE,IAAI,CAAClO,SAAS,CAACnG;KAC1B;IAED;IACA,IAAI,CAAC8F,eAAe,CAACkF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAAClF,kBAAkB,CAACuO,gBAAgB,CAACzI,QAAQ,CAAC,CAAClC,SAAS,CAAC;MAC3DsB,IAAI,EAAGsJ,GAAG,IAAI;QACZ,IAAI,CAACzO,eAAe,CAACkF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACsJ,GAAG,CAAC/I,OAAO,EAAE;UAChB;UACA,IAAI,CAAC1E,UAAU,GAAG8M,aAAa;UAC/B,IAAI,CAAC7M,aAAa,GAAGiN,qBAAqB;UAC1C,IAAI,CAAC/M,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;UAEhE;UACA,IAAI,CAACzB,kBAAkB,CAACyO,kBAAkB,CAAC3I,QAAQ,CAAC;UAEpD,IAAI,CAAChG,wBAAwB,CAAC6H,WAAW,CACvC6G,GAAG,CAAC9G,OAAO,IAAI,qCAAqC,EACpD,EAAE,CACH;QACH,CAAC,MAAM;UACL,IAAI,CAAC5H,wBAAwB,CAAC8H,SAAS,CACrC4G,GAAG,CAAC9G,OAAO,IAAI,iCAAiC,EAChD,EAAE,CACH;QACH;QACA,IAAI,CAACjI,GAAG,CAACiP,YAAY,EAAE;MACzB,CAAC;MACD9I,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7F,eAAe,CAACkF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CzB,OAAO,CAACmC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QAErD;QACA,IAAI,CAAC5F,kBAAkB,CAACyO,kBAAkB,CAAC3I,QAAQ,CAAC;QAEpD;QACA,IAAI,CAAC/E,UAAU,GAAG8M,aAAa;QAC/B,IAAI,CAAC7M,aAAa,GAAGiN,qBAAqB;QAC1C,IAAI,CAAC/M,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAEhE,IAAI,CAAC3B,wBAAwB,CAAC8H,SAAS,CACrC,mDAAmD,EACnD,EAAE,CACH;QACD,IAAI,CAACnI,GAAG,CAACiP,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;EAIA5W,UAAUA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAACsI,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACnG,MAAM,EAAE;MAC7CwJ,OAAO,CAACmC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAACxF,SAAS,CAAC;MACzD,IAAI,CAACN,wBAAwB,CAAC8H,SAAS,CACrC,4DAA4D,EAC5D,EAAE,CACH;MACD;IACF;IAEA;IACA,MAAM+G,KAAK,GAAG,IAAI,CAAC9O,UAAU,CAAC+O,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC;IACtE,IAAI,CAACD,KAAK,EAAE;MACVlL,OAAO,CAACmC,KAAK,CAAC,gCAAgC,CAAC;MAC/C,IAAI,CAAC9F,wBAAwB,CAAC8H,SAAS,CACrC,qDAAqD,EACrD,EAAE,CACH;MACD;IACF;IAEA;IACA,IAAI,CAAC5Q,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC2J,aAAa,GAAG,EAAE;IACvB,IAAI,CAACJ,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACgC,IAAI,GAAG,CAAC;IACb,IAAI,CAACnK,IAAI,CAAC+J,UAAU,GAAG,CAAC;IACxB,IAAI,CAAClB,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAE3C;IACA,IAAI,CAACY,IAAI,GAAG,CAAC;MAAEP,KAAK,EAAE,iBAAiB;MAAEQ,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAC3J,IAAI,CAACiK,OAAO,GAAG,iBAAiB;IACrC,IAAI,CAACjK,IAAI,CAACkK,QAAQ,GAAG,MAAM;IAE3B;IACA,IAAI,CAAC5J,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACY,mBAAmB,GAAG,KAAK;IAEhC;IACA,IAAI,IAAI,CAACyG,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC6N,OAAO,EAAE;MAClC,IAAI,CAAC7N,IAAI,CAAC6N,OAAO,CAACrE,OAAO,CAAEpM,MAAW,IAAI;QACxC,MAAM6Q,KAAK,GAAG,IAAI,CAAC/M,WAAW,CAAC0N,OAAO,CAACxR,MAAM,CAACoE,KAAK,CAAC;QACpD,IAAIyM,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB7Q,MAAM,CAAC8Q,UAAU,GAAGD,KAAK;QAC3B;QACA;QACA,IAAI7Q,MAAM,CAACoE,KAAK,IAAIpE,MAAM,CAACoE,KAAK,KAAK,QAAQ,EAAE;UAC7CpE,MAAM,CAAC0Q,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAChN,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACE,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,IAAI,CAACjB,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACM,MAAM,GAAG;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACR,IAAI,CAAC+B,IAAI,GAAG,CAAC;QAAEP,KAAK,EAAE,iBAAiB;QAAEQ,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAAChC,IAAI,CAACwC,IAAI,GAAG,CAAC;MAClB,IAAI,CAACxC,IAAI,CAACqG,QAAQ,GAAG,IAAI,CAAChO,IAAI,CAAC8J,IAAI;IACrC;IAEA;IACA,MAAM0D,QAAQ,GAAG;MACfsI,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,IAAI,CAACjO,SAAS,CAACnG,MAAM;MAC7B8G,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjBsN,QAAQ,EAAE,IAAI,CAAClO,SAAS,CAACnG;KAC1B;IAED;IACA,IAAI,CAAC+F,kBAAkB,CAAC8O,qBAAqB,CAAC,OAAO,CAAC;IAEtD;IACA,IAAI,CAAC3P,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACW,eAAe,CAACkF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzF,GAAG,CAACqE,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAAC7D,IAAI,EAAE;MACbsE,UAAU,CAAC,MAAK;QACd,IAAI,CAACtE,IAAI,CAACkL,OAAO,EAAE;QACnB,IAAI,CAAClL,IAAI,CAAC8O,KAAK,EAAE;MACnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,IAAI,CAAC/P,SAAS,EAAE;EAClB;EAEA;;;;;EAKAgQ,oBAAoBA,CAAChO,aAAkB;IACrC,IAAI;MACF,MAAMiO,UAAU,GAAGjO,aAAa;MAChC,IAAIiO,UAAU,EAAE;QACd,MAAMC,WAAW,GAAGD,UAAU;QAC9B,IAAIlG,KAAK,CAACC,OAAO,CAACkG,WAAW,CAAC,IAAIA,WAAW,CAAC5P,MAAM,GAAG,CAAC,EAAE;UACxD;UACA,MAAM6P,qBAAqB,GAAGD,WAAW,CACtClN,IAAI,CAAC,CAACoN,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjB,UAAU,GAAGkB,CAAC,CAAClB,UAAU,CAAC,CAC3C1J,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACjD,KAAK,CAAC,CACvBlB,MAAM,CAAEkB,KAAK,IAAK,CAAC,IAAI,CAACjH,YAAY,CAACC,QAAQ,CAACgH,KAAK,CAAC,CAAC;UAExD;UACA,MAAM6N,cAAc,GAAG,IAAI,CAACjO,gBAAgB,CAACd,MAAM,CAChDmE,GAAG,IAAK,CAACyK,qBAAqB,CAAC1U,QAAQ,CAACiK,GAAG,CAAC,CAC9C;UAED;UACA,IAAI,CAACvD,WAAW,GAAG,CACjB,GAAG,IAAI,CAAC3G,YAAY,EACpB,GAAG2U,qBAAqB,EACxB,GAAGG,cAAc,CAClB;QACH,CAAC,MAAM;UACL,IAAI,CAACnO,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACd,IAAI,CAACzE,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC7C;EACF;EAEA;;;;EAIAzG,cAAcA,CAAC4U,UAAe;IAC5B,OAAO,IAAI,CAACrO,YAAY,CAAC2N,OAAO,CAACU,UAAU,CAAC,GAAG,CAAC,CAAC;EACnD;EAEA;;;;;EAKAC,eAAeA,CAACxL,KAAU;IACxB,MAAM;MAAE8J,OAAO;MAAE2B,QAAQ;MAAEC;IAAQ,CAAE,GAAG1L,KAAK;IAE7C;IACA,IACE,IAAI,CAACxJ,YAAY,CAACC,QAAQ,CAACqT,OAAO,CAAC4B,QAAQ,CAAC,CAACjO,KAAK,CAAC,IACnD,IAAI,CAACjH,YAAY,CAACC,QAAQ,CAACqT,OAAO,CAAC2B,QAAQ,CAAC,CAAChO,KAAK,CAAC,EACnD;MACA;IACF;IAEA;IACA,MAAMkO,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACxO,WAAW,CAAC;IAC9C,MAAM,CAACyO,WAAW,CAAC,GAAGD,gBAAgB,CAACnG,MAAM,CAACkG,QAAQ,EAAE,CAAC,CAAC;IAC1DC,gBAAgB,CAACnG,MAAM,CAACiG,QAAQ,EAAE,CAAC,EAAEG,WAAW,CAAC;IAEjD,IAAI,CAACzO,WAAW,GAAGwO,gBAAgB;IACnC,IAAI,CAAClQ,GAAG,CAACiP,YAAY,EAAE;EACzB;EAEA;;;;EAIAmB,sBAAsBA,CAAC7L,KAAU;IAC/B,IAAI,IAAI,CAACvL,UAAU,KAAK,KAAK,EAAE;MAC7B,IAAI,IAAI,CAACwH,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC6N,OAAO,EAAE;QAClC,IAAI,CAAC7N,IAAI,CAAC6N,OAAO,CAACrE,OAAO,CAAEpM,MAAW,IAAI;UACxC,MAAM2Q,UAAU,GAAG;YACjBrS,KAAK,EAAE0B,MAAM,CAAC1B,KAAK;YACnB8F,KAAK,EAAEpE,MAAM,CAACoE,KAAK;YACnBsM,MAAM,EAAE1Q,MAAM,CAAC0Q;WAChB;UACD,IAAI1Q,MAAM,CAAC0Q,MAAM,EAAE;YACjB,MAAMzE,MAAM,GAAG,IAAI,CAACvI,UAAU,CAAC+O,IAAI,CAChCjF,IAAS,IACRA,IAAI,CAACpJ,KAAK,KAAKuM,UAAU,CAACvM,KAAK,IAAIoJ,IAAI,CAACkD,MAAM,KAAK,IAAI,CAC1D;YACD,IAAI,CAACzE,MAAM,EAAE;cACX,IAAI,CAACvI,UAAU,CAAC2H,IAAI,CAACsF,UAAU,CAAC;YAClC;UACF,CAAC,MAAM;YACL,IAAI+B,WAAW,GAAG,IAAI,CAAChP,UAAU,CAACwI,SAAS,CACxCsB,IAAS,IACRA,IAAI,CAACpJ,KAAK,KAAKuM,UAAU,CAACvM,KAAK,IAAIoJ,IAAI,CAACkD,MAAM,KAAK,IAAI,CAC1D;YACD,IAAIgC,WAAW,KAAK,CAAC,CAAC,EAAE;cACtB,IAAI,CAAChP,UAAU,CAACyI,MAAM,CAACuG,WAAW,EAAE,CAAC,CAAC;YACxC;UACF;QACF,CAAC,CAAC;QACF,IAAI,CAAC7O,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAChE,IAAI,CAAChC,GAAG,CAACiP,YAAY,EAAE;MACzB;IACF;EACF;EAEA;;;;EAIQlK,4BAA4BA,CAAA;IAClC,IAAI;MACF;MACA,IAAI,IAAI,CAACpE,SAAS,IAAI,IAAI,CAACA,SAAS,CAACnG,MAAM,EAAE;QAC3C,IAAI,CAAC+F,kBAAkB,CACpBgQ,aAAa,CAAC;UACb5B,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,IAAI,CAACjO,SAAS,CAACnG;SACxB,CAAC,CACD2J,SAAS,CAAC;UACTsB,IAAI,EAAGsJ,GAAG,IAAI;YACZ,IAAI,CAACA,GAAG,CAAC/I,OAAO,IAAI+I,GAAG,CAACyB,IAAI,EAAE;cAC5B,IAAI,CAACnP,SAAS,GAAG0N,GAAG,CAACyB,IAAI;cACzB,IAAI,CAAClP,UAAU,GAAGyN,GAAG,CAACyB,IAAI,CAACC,QAAQ,GAC/BrG,IAAI,CAACC,KAAK,CAAC0E,GAAG,CAACyB,IAAI,CAACC,QAAQ,CAAC,GAC7B,EAAE;cACN,IAAI,CAACjP,iBAAiB,GAAGuN,GAAG,CAACyB,IAAI,CAACjP,aAAa,GAC3C6I,IAAI,CAACC,KAAK,CAAC0E,GAAG,CAACyB,IAAI,CAACjP,aAAa,CAAC,GAClC,EAAE;cACN,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CACpCC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CACxB;cAED;cACA,IAAI,IAAI,CAACxB,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC6N,OAAO,EAAE;gBAClC,IAAI,CAAC7N,IAAI,CAAC6N,OAAO,CAACrE,OAAO,CAAEpM,MAAW,IAAI;kBACxC,IACE,IAAI,CAAC0D,UAAU,CAAC+O,IAAI,CACjBjF,IAAS,IACRA,IAAI,CAAClP,KAAK,KAAK0B,MAAM,CAAC1B,KAAK,IAAIkP,IAAI,CAACkD,MAAM,CAC7C,EACD;oBACA1Q,MAAM,CAAC8S,gBAAgB,GAAG,IAAI;oBAC9B9S,MAAM,CAAC0Q,MAAM,GAAG,IAAI;kBACtB,CAAC,MAAM;oBACL1Q,MAAM,CAAC0Q,MAAM,GAAG,KAAK;kBACvB;gBACF,CAAC,CAAC;cACJ;cAEA;cACA,IAAI,CAACiB,oBAAoB,CAAC,IAAI,CAAC/N,iBAAiB,CAAC;cAEjD;cACA,IAAI,CAACjB,kBAAkB,CAACyO,kBAAkB,CAAC;gBACzCL,QAAQ,EAAE,OAAO;gBACjBC,MAAM,EAAE,IAAI,CAACjO,SAAS,CAACnG,MAAM;gBAC7B8G,UAAU,EAAE,IAAI,CAACA,UAAU;gBAC3BC,aAAa,EAAE,IAAI,CAACC;eACrB,CAAC;YACJ;UACF,CAAC;UACD2E,KAAK,EAAGA,KAAK,IAAI;YACfnC,OAAO,CAACmC,KAAK,CACX,2DAA2D,EAC3DA,KAAK,CACN;YACD,IAAI,CAACwK,4BAA4B,EAAE;UACrC;SACD,CAAC;MACN,CAAC,MAAM;QACL;QACA,IAAI,CAACA,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,OAAOxK,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACwK,4BAA4B,EAAE;IACrC;EACF;EAEA;;;EAGQA,4BAA4BA,CAAA;IAClC,IAAI;MACF,MAAMC,WAAW,GAAG,IAAI,CAACrQ,kBAAkB,CAACsQ,mBAAmB,CAC7D,OAAO,EACP,IAAI,CAAClQ,SAAS,EAAE0L,MAAM,IAAI,CAAC,CAC5B;MACD,IAAIuE,WAAW,EAAE;QACf,IAAI,CAACvP,SAAS,GAAGuP,WAAW;QAC5B,IAAI,CAACtP,UAAU,GAAGsP,WAAW,CAACtP,UAAU,IAAI,EAAE;QAC9C,IAAI,CAACE,iBAAiB,GAAGoP,WAAW,CAACrP,aAAa,IAAI,EAAE;QACxD,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAEhE;QACA,IAAI,IAAI,CAACxB,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC6N,OAAO,EAAE;UAClC,IAAI,CAAC7N,IAAI,CAAC6N,OAAO,CAACrE,OAAO,CAAEpM,MAAW,IAAI;YACxC,IACE,IAAI,CAAC0D,UAAU,CAAC+O,IAAI,CACjBjF,IAAS,IAAKA,IAAI,CAAClP,KAAK,KAAK0B,MAAM,CAAC1B,KAAK,IAAIkP,IAAI,CAACkD,MAAM,CAC1D,EACD;cACA1Q,MAAM,CAAC8S,gBAAgB,GAAG,IAAI;cAC9B9S,MAAM,CAAC0Q,MAAM,GAAG,IAAI;YACtB,CAAC,MAAM;cACL1Q,MAAM,CAAC0Q,MAAM,GAAG,KAAK;YACvB;UACF,CAAC,CAAC;QACJ;QAEA;QACA,IAAI,CAACiB,oBAAoB,CAAC,IAAI,CAAC/N,iBAAiB,CAAC;MACnD;IACF,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACnE;EACF;;qCApmDWrG,iBAAiB,EAAAnJ,EAAA,CAAAma,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAra,EAAA,CAAAma,iBAAA,CAAAna,EAAA,CAAAsa,iBAAA,GAAAta,EAAA,CAAAma,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAxa,EAAA,CAAAma,iBAAA,CAAAI,EAAA,CAAAE,cAAA,GAAAza,EAAA,CAAAma,iBAAA,CAAAO,EAAA,CAAAC,QAAA,GAAA3a,EAAA,CAAAma,iBAAA,CAAAS,EAAA,CAAAnR,UAAA,GAAAzJ,EAAA,CAAAma,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAA9a,EAAA,CAAAma,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAhb,EAAA,CAAAma,iBAAA,CAAAc,EAAA,CAAAC,kBAAA;EAAA;;UAAjB/R,iBAAiB;IAAAgS,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QCnE9Btb,EAAA,CAAAkD,UAAA,IAAAsY,gCAAA,iBAAqE;QAUnExb,EADF,CAAAC,cAAA,aAA4B,uBA6BzB;QADCD,EAZA,CAAAc,UAAA,2BAAA2a,+DAAAnb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CAAiB0a,GAAA,CAAAnC,eAAA,CAAA9Y,MAAA,CAAuB;QAAA,EAAC,6BAAAqb,iEAAArb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CACtB0a,GAAA,CAAA5K,iBAAA,CAAArQ,MAAA,CAAyB;QAAA,EAAC,0BAAAsb,8DAAAtb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CAQ7B0a,GAAA,CAAA1I,YAAA,CAAAvS,MAAA,CAAoB;QAAA,EAAC,wBAAAub,4DAAAvb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CACvB0a,GAAA,CAAA/I,UAAA,CAAAlS,MAAA,CAAkB;QAAA,EAAC,wBAAAwb,4DAAAxb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CACnB0a,GAAA,CAAA9I,YAAA,CAAAnS,MAAA,CAAoB;QAAA,EAAC,oCAAAyb,wEAAAzb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CACT0a,GAAA,CAAA9B,sBAAA,CAAAnZ,MAAA,CAA8B;QAAA,EAAC;QAyczDN,EAvcA,CAAAkD,UAAA,IAAA8Y,wCAAA,2BAAsC,IAAAC,wCAAA,yBAsFA,IAAAC,yCAAA,0BAgDW,IAAAC,wCAAA,yBAiUT;QAuB5Cnc,EADE,CAAAG,YAAA,EAAa,EACT;;;QArgBAH,EAAA,CAAAgC,UAAA,SAAAuZ,GAAA,CAAAxS,OAAA,IAAAwS,GAAA,CAAAvS,SAAA,CAA0B;QAY5BhJ,EAAA,CAAA6B,SAAA,GAA0B;QAqB1B7B,EArBA,CAAAgC,UAAA,SAAAuZ,GAAA,CAAAtS,iBAAA,CAA0B,aAAAsS,GAAA,CAAArZ,IAAA,CAAA8J,IAAA,CACJ,SAAAuP,GAAA,CAAA3P,IAAA,CACT,aAAA5L,EAAA,CAAAoc,eAAA,KAAAC,GAAA,EAAArc,EAAA,CAAAkE,eAAA,KAAAoY,GAAA,GAOX,aAAAtc,EAAA,CAAAkE,eAAA,KAAAqY,GAAA,EACgD,oBAC/B,eAAAvc,EAAA,CAAAkE,eAAA,KAAAsY,GAAA,EACoC,qBAGnC,oBAED,eACL,SAAAjB,GAAA,CAAArZ,IAAA,CAAA+J,UAAA,GAAAsP,GAAA,CAAArZ,IAAA,CAAA8J,IAAA,CACsB,WAAAuP,GAAA,CAAApR,MAAA,CACnB,eAAAnK,EAAA,CAAAkE,eAAA,KAAAuY,GAAA,EACc;QA4IEzc,EAAA,CAAA6B,SAAA,GAAc;QAAd7B,EAAA,CAAAgC,UAAA,YAAAuZ,GAAA,CAAAxQ,WAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}