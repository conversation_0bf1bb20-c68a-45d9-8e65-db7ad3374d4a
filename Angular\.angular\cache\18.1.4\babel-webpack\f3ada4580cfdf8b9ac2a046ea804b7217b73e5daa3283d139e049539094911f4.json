{"ast": null, "code": "import { AppService } from '../../services/app.service';\nimport jsPDF from 'jspdf';\nimport autoTable from 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/custom-layout.utils.service\";\nimport * as i3 from \"../../services/app.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../services/permits.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(correction_r1.Response);\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Resolved Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, correction_r1.ResolvedDate, \"MM/dd/yyyy\"));\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 29);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 30)(9, \"div\", 31)(10, \"label\");\n    i0.ɵɵtext(11, \"Corrective Action\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 32);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 31)(15, \"label\");\n    i0.ɵɵtext(16, \"Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 32);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_19_Template, 5, 1, \"div\", 33)(20, ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_20_Template, 6, 4, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r2 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Correction Type: \", correction_r1.CorrectionTypeName || \"General\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Category: \", correction_r1.CorrectionCategoryName || \"General Correction\", \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(correction_r1.CorrectiveAction || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r1.Comments || \"No comment provided\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r1.Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r1.ResolvedDate);\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h6\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ReviewDetailsModalComponent_ng_container_39_div_1_div_3_Template, 21, 7, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Corrections (\", ctx_r2.review.corrections.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.review.corrections);\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"h6\", 24);\n    i0.ɵɵtext(2, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.review.comments);\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReviewDetailsModalComponent_ng_container_39_div_1_Template, 4, 2, \"div\", 21)(2, ReviewDetailsModalComponent_ng_container_39_div_2_Template, 6, 1, \"div\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.review == null ? null : ctx_r2.review.corrections) && ctx_r2.review.corrections.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(ctx_r2.review == null ? null : ctx_r2.review.corrections) || ctx_r2.review.corrections.length === 0) && (ctx_r2.review == null ? null : ctx_r2.review.comments));\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 39);\n    i0.ɵɵlistener(\"ngSubmit\", function ReviewDetailsModalComponent_ng_container_40_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit());\n    });\n    i0.ɵɵelementStart(2, \"div\", 40)(3, \"div\", 41)(4, \"label\", 42);\n    i0.ɵɵtext(5, \"EOR / AOR / Owner Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"textarea\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\", 41)(9, \"label\", 42);\n    i0.ɵɵtext(10, \"Comment Responded By\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.reviewForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading);\n  }\n}\nfunction ReviewDetailsModalComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \" No corrections or comments available for this review.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ReviewDetailsModalComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 48);\n  }\n}\nexport let ReviewDetailsModalComponent = /*#__PURE__*/(() => {\n  class ReviewDetailsModalComponent {\n    modal;\n    customLayoutUtilsService;\n    appService;\n    fb;\n    cdr;\n    permitsService;\n    review;\n    permitId = null;\n    permitDetails = {};\n    selectedTab = 'corrections';\n    reviewForm;\n    isLoading = false;\n    loginUser;\n    constructor(modal, customLayoutUtilsService, appService, fb, cdr, permitsService) {\n      this.modal = modal;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.appService = appService;\n      this.fb = fb;\n      this.cdr = cdr;\n      this.permitsService = permitsService;\n    }\n    ngOnInit() {\n      this.loginUser = this.appService.getLoggedInUser();\n      this.reviewForm = this.fb.group({\n        // cityComments: [this.reviewData?.comments || ''],\n        EORAOROwner_Response: [this.review?.EORAOROwner_Response || ''],\n        commentResponsedBy: [this.review?.commentResponsedBy || '']\n      });\n    }\n    showTab(tab, $event) {\n      this.selectedTab = tab;\n      this.cdr.markForCheck();\n    }\n    onSubmit() {\n      this.isLoading = true;\n      const formData = {\n        // cityComments:this.reviewForm.controls.cityComments.value,\n        EORAOROwner_Response: this.reviewForm.controls.EORAOROwner_Response.value,\n        commentResponsedBy: this.reviewForm.controls.commentResponsedBy.value,\n        permitId: this.permitId,\n        commentsId: this.review.commentsId,\n        loggedInUserId: this.loginUser.userId\n      };\n      this.permitsService.updateExternalReview(formData).subscribe({\n        next: res => {\n          this.isLoading = false;\n          if (res?.isFault === false) {\n            //alert(res.responseData.message);\n            this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n            this.modal.close('updated');\n          } else {\n            this.customLayoutUtilsService.showError(res.responseData.message || 'Failed to update external review', '');\n            //alert(res.responseData.message || 'Failed to update external review');\n          }\n        },\n        error: err => {\n          this.isLoading = false;\n          this.customLayoutUtilsService.showError('Error updating external review', '');\n          //alert('Error updating external review');\n          console.error(err);\n        }\n      });\n    }\n    getPdf() {\n      const permitNumber = this.permitDetails?.permitNumber || '';\n      const projectName = this.permitDetails?.projectName || '';\n      const applicantName = this.permitDetails?.applicantName || '';\n      const reviewer = (this.review?.AssignedTo || this.review?.municipalityReviewer || '').toString();\n      const cityComments = (this.review?.Comments || (this.review?.cityComments ?? '')).toString();\n      const ownerResponse = (this.reviewForm?.value?.EORAOROwner_Response ?? this.review?.EORAOROwner_Response ?? '').toString();\n      const respondedBy = (this.reviewForm?.value?.commentResponsedBy ?? this.review?.commentResponsedBy ?? '').toString();\n      const cycle = (this.review?.cycle || this.review?.Cycle || '').toString();\n      const status = (this.review?.StatusName || this.review?.commentstatus || this.review?.status || '').toString();\n      const reviewCategory = (this.review?.TypeName || this.review?.reviewCategory || '').toString();\n      const dueDate = this.review?.DueDate ? AppService.formatDate(this.review.DueDate) : '';\n      const completedDate = this.review?.CompletedDate ? AppService.formatDate(this.review.CompletedDate) : '';\n      const createdDate = this.review?.createdDate ? AppService.formatDate(this.review.createdDate) : '';\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'pt',\n        format: 'a4'\n      });\n      // Page metrics and margins\n      const pageWidth = doc.internal.pageSize.getWidth();\n      const pageHeight = doc.internal.pageSize.getHeight();\n      const margin = {\n        left: 50,\n        right: 50,\n        top: 60,\n        bottom: 60\n      };\n      let currentY = margin.top;\n      // Professional Header with Company Branding\n      const drawHeader = () => {\n        // Company Logo/Title Area\n        doc.setFillColor(41, 128, 185); // Professional blue\n        doc.rect(0, 0, pageWidth, 50, 'F');\n        doc.setTextColor(255, 255, 255);\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(18);\n        doc.text('PERMIT TRACKER SYSTEM', margin.left, 25);\n        doc.setFontSize(12);\n        doc.text('Review Report', pageWidth - margin.right - 80, 25);\n        // Reset text color\n        doc.setTextColor(0, 0, 0);\n        // Permit Information Header\n        currentY = 70;\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(14);\n        doc.text('REVIEW DETAILS', margin.left, currentY);\n        currentY += 20;\n      };\n      drawHeader();\n      // Permit Information Section\n      const drawPermitInfo = () => {\n        const infoData = [['Permit #:', permitNumber || ''], ['Project Name:', projectName || ''], ['Applicant Name:', applicantName || ''], ['Review Category:', reviewCategory || ''], ['Reviewer:', reviewer || ''], ['Status:', status || ''], ['Due Date:', dueDate || ''], ['Completed Date:', completedDate || ''], ['Created Date:', createdDate || '']];\n        autoTable(doc, {\n          startY: currentY,\n          body: infoData,\n          margin: {\n            left: margin.left,\n            right: margin.right\n          },\n          styles: {\n            font: 'helvetica',\n            fontSize: 10,\n            cellPadding: 8,\n            overflow: 'linebreak'\n          },\n          columnStyles: {\n            0: {\n              cellWidth: 120,\n              fontStyle: 'bold',\n              fillColor: [240, 240, 240]\n            },\n            1: {\n              cellWidth: 200\n            }\n          },\n          theme: 'grid'\n        });\n        currentY = doc.lastAutoTable.finalY + 20;\n      };\n      drawPermitInfo();\n      // City Comments Section\n      if (cityComments) {\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(12);\n        doc.setTextColor(192, 0, 0); // Red color for city comments\n        doc.text('CITY COMMENTS', margin.left, currentY);\n        currentY += 15;\n        doc.setTextColor(0, 0, 0);\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(10);\n        // Split long text into multiple lines\n        const splitText = doc.splitTextToSize(cityComments, pageWidth - margin.left - margin.right);\n        doc.text(splitText, margin.left, currentY);\n        currentY += splitText.length * 12 + 20;\n      }\n      // Owner Response Section\n      if (ownerResponse) {\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(12);\n        doc.text('EOR/AOR/OWNER RESPONSE', margin.left, currentY);\n        currentY += 15;\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(10);\n        const splitText = doc.splitTextToSize(ownerResponse, pageWidth - margin.left - margin.right);\n        doc.text(splitText, margin.left, currentY);\n        currentY += splitText.length * 12 + 20;\n        // Response details\n        if (respondedBy) {\n          doc.setFont('helvetica', 'bold');\n          doc.setFontSize(10);\n          doc.text(`Responded By: ${respondedBy}`, margin.left, currentY);\n          currentY += 15;\n        }\n      }\n      // Corrections Section\n      if (this.review?.Corrections && this.review.Corrections.length > 0) {\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(12);\n        doc.text('CORRECTIONS & DETAILS', margin.left, currentY);\n        currentY += 15;\n        this.review.Corrections.forEach((correction, index) => {\n          // Check if we need a new page\n          if (currentY > pageHeight - 200) {\n            doc.addPage();\n            drawHeader();\n            currentY = margin.top + 20;\n          }\n          // Correction header\n          doc.setFont('helvetica', 'bold');\n          doc.setFontSize(10);\n          doc.text(`Correction ${index + 1}`, margin.left, currentY);\n          currentY += 15;\n          // Correction details table\n          const correctionData = [['Correction ID:', correction.CorrectionID || ''], ['Type:', correction.CorrectionTypeName || ''], ['Category:', correction.CorrectionCategoryName || ''], ['Comments:', correction.Comments || ''], ['Corrective Action:', correction.CorrectiveAction || ''], ['Response:', correction.Response || ''], ['Owner Response:', correction.EORAOROwner_Response || ''], ['Responded By:', correction.commentResponsedBy || ''], ['Resolved Date:', correction.ResolvedDate ? AppService.formatDate(correction.ResolvedDate) : ''], ['Is Resolved:', correction.IsResolvedText || '']];\n          autoTable(doc, {\n            startY: currentY,\n            body: correctionData,\n            margin: {\n              left: margin.left,\n              right: margin.right\n            },\n            styles: {\n              font: 'helvetica',\n              fontSize: 9,\n              cellPadding: 6,\n              overflow: 'linebreak'\n            },\n            columnStyles: {\n              0: {\n                cellWidth: 100,\n                fontStyle: 'bold',\n                fillColor: [245, 245, 245]\n              },\n              1: {\n                cellWidth: 200\n              }\n            },\n            theme: 'grid'\n          });\n          currentY = doc.lastAutoTable.finalY + 15;\n        });\n      }\n      // Footer\n      const drawFooter = () => {\n        const pageCount = doc.getNumberOfPages();\n        for (let i = 1; i <= pageCount; i++) {\n          doc.setPage(i);\n          // Footer line\n          doc.setDrawColor(200, 200, 200);\n          doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n          // Footer text\n          doc.setFont('helvetica', 'normal');\n          doc.setFontSize(8);\n          doc.setTextColor(100, 100, 100);\n          doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n          doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n        }\n      };\n      drawFooter();\n      // Save the PDF\n      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${reviewCategory ? reviewCategory.replace(/[^a-zA-Z0-9]/g, '_') + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n    }\n    static ɵfac = function ReviewDetailsModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReviewDetailsModalComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i3.AppService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PermitsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReviewDetailsModalComponent,\n      selectors: [[\"app-review-details-modal\"]],\n      inputs: {\n        review: \"review\",\n        permitId: \"permitId\",\n        permitDetails: \"permitDetails\"\n      },\n      decls: 50,\n      vars: 23,\n      consts: [[1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [1, \"dates-section\"], [1, \"date-row\"], [1, \"date-item\"], [1, \"date-value\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"min-height\", \"30px !important\", \"padding\", \"0 1.25rem !important\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-6\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", \"fs-6\", 3, \"click\", \"ngClass\"], [1, \"card-body\", \"p-0\"], [4, \"ngIf\"], [\"class\", \"no-data-section\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"btn-elevate\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [\"class\", \"corrections-section\", 4, \"ngIf\"], [\"class\", \"comments-section\", 4, \"ngIf\"], [1, \"corrections-section\"], [1, \"section-title\"], [\"class\", \"correction-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"correction-item\"], [1, \"correction-header\"], [1, \"correction-number\"], [1, \"correction-action\"], [1, \"correction-content\"], [1, \"correction-field\"], [1, \"correction-comment\"], [\"class\", \"correction-field\", 4, \"ngIf\"], [1, \"correction-response\"], [1, \"correction-resolved\"], [1, \"comments-section\"], [1, \"comment-content\"], [1, \"comment-text\"], [\"novalidate\", \"\", 1, \"px-6\", \"mb-10\", 3, \"ngSubmit\", \"formGroup\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"formControlName\", \"EORAOROwner_Response\", \"rows\", \"3\", \"placeholder\", \"Enter response\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"text\", \"formControlName\", \"commentResponsedBy\", \"placeholder\", \"Who responded to comments\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"no-data-section\"], [1, \"no-data-message\"], [1, \"fas\", \"fa-info-circle\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function ReviewDetailsModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_button_click_3_listener() {\n            return ctx.modal.dismiss();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"label\");\n          i0.ɵɵtext(9, \"Reviwer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 7);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 6)(13, \"label\");\n          i0.ɵɵtext(14, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 7);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\");\n          i0.ɵɵtext(19, \"Due Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\", 7);\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 6)(24, \"label\");\n          i0.ɵɵtext(25, \"Completed Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"span\", 7);\n          i0.ɵɵtext(27);\n          i0.ɵɵpipe(28, \"date\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(29, \"div\", 8)(30, \"div\", 9)(31, \"ul\", 10)(32, \"li\", 11)(33, \"a\", 12);\n          i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_a_click_33_listener($event) {\n            return ctx.showTab(\"corrections\", $event);\n          });\n          i0.ɵɵtext(34, \" Corrections / Comments \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"li\", 11)(36, \"a\", 12);\n          i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_a_click_36_listener($event) {\n            return ctx.showTab(\"review\", $event);\n          });\n          i0.ɵɵtext(37, \" Reviews \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"div\", 13);\n          i0.ɵɵtemplate(39, ReviewDetailsModalComponent_ng_container_39_Template, 3, 2, \"ng-container\", 14)(40, ReviewDetailsModalComponent_ng_container_40_Template, 12, 3, \"ng-container\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(41, ReviewDetailsModalComponent_div_41_Template, 5, 0, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 16)(43, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_button_click_43_listener() {\n            return ctx.modal.close();\n          });\n          i0.ɵɵtext(44, \"Close\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_button_click_45_listener() {\n            return ctx.getPdf();\n          });\n          i0.ɵɵtext(46, \" Download PDF \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_button_click_47_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(48, ReviewDetailsModalComponent_span_48_Template, 1, 0, \"span\", 20);\n          i0.ɵɵtext(49, \" Update \");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.review == null ? null : ctx.review.name);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.review == null ? null : ctx.review.reviewer);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.review == null ? null : ctx.review.status);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 13, ctx.review == null ? null : ctx.review.dueDate, \"MM/dd/yyyy\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(28, 16, ctx.review == null ? null : ctx.review.completedDate, \"MM/dd/yyyy\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx.selectedTab === \"corrections\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx.selectedTab === \"review\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"corrections\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"review\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (!(ctx.review == null ? null : ctx.review.corrections) || ctx.review.corrections.length === 0) && !(ctx.review == null ? null : ctx.review.comments));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i6.DatePipe],\n      styles: [\"[_nghost-%COMP%]     .modal-dialog{max-width:800px;width:90%}[_nghost-%COMP%]     .modal-body{max-height:70vh;overflow-y:auto;padding:0}.review-header-banner[_ngcontent-%COMP%]{padding:1rem 1.5rem;display:flex;justify-content:space-between;align-items:center;border-radius:.375rem .375rem 0 0;margin-top:2px;margin-left:4px}.banner-content[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:1rem;flex:1}.banner-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;font-weight:500}.status-icon[_ngcontent-%COMP%]{font-size:.75rem}.status-icon.status-requires-resubmit[_ngcontent-%COMP%]{color:#ffc107}.status-icon.status-approved[_ngcontent-%COMP%]{color:#28a745}.status-icon.status-under-review[_ngcontent-%COMP%]{color:#17a2b8}.status-icon.status-pending[_ngcontent-%COMP%]{color:#ffc107}.banner-text[_ngcontent-%COMP%]{white-space:nowrap}.envelope-icon[_ngcontent-%COMP%]{color:#007bff;margin-left:.5rem}.square-icon[_ngcontent-%COMP%]{color:#007bff;margin-left:.25rem}.dates-section[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid #e5eaee;background-color:#f8f9fa}.date-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr 1fr 1fr;gap:2rem}.date-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.date-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:.75rem;font-weight:600;color:#6c7293;text-transform:uppercase;letter-spacing:.05rem}.date-item[_ngcontent-%COMP%]   .date-value[_ngcontent-%COMP%]{font-size:.875rem;color:#3f4254;font-weight:500}.comment-section[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid #e5eaee}.section-title[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;color:#3f4254;margin-bottom:.75rem;padding-bottom:.25rem;border-bottom:1px solid #e5eaee}.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:.875rem;color:#3f4254;line-height:1.4}.contact-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50}.contact-info[_ngcontent-%COMP%]   .contact-title[_ngcontent-%COMP%]{color:#6c7293}.contact-info[_ngcontent-%COMP%]   .contact-id[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .contact-license[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .contact-certification[_ngcontent-%COMP%]{color:#6c7293;font-family:Courier New,monospace}.contact-info[_ngcontent-%COMP%]   .contact-organization[_ngcontent-%COMP%]{font-weight:500;color:#495057}.contact-info[_ngcontent-%COMP%]   .contact-phone[_ngcontent-%COMP%]{color:#6c7293}.contact-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%]{color:#007bff;font-weight:500}.corrections-section[_ngcontent-%COMP%]{padding:1rem 1.5rem}.correction-item[_ngcontent-%COMP%]{border:1px solid #e5eaee;border-radius:.375rem;margin-bottom:1rem;background:#fff}.correction-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.correction-header[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:.5rem 1rem;border-bottom:1px solid #e5eaee;border-radius:.375rem .375rem 0 0;display:flex;align-items:center;gap:1rem}.correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;border-radius:50%;width:24px;height:24px;display:inline-flex;align-items:center;justify-content:center;font-size:.75rem;font-weight:600;flex-shrink:0}.correction-header[_ngcontent-%COMP%]   .correction-type[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;color:#3f4254}.correction-header[_ngcontent-%COMP%]   .correction-action[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;color:#3f4254}.correction-content[_ngcontent-%COMP%]{padding:1rem}.correction-field[_ngcontent-%COMP%]{margin-bottom:.75rem}.correction-field[_ngcontent-%COMP%]:last-child{margin-bottom:0}.correction-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;font-size:.75rem;font-weight:600;color:#6c7293;text-transform:uppercase;letter-spacing:.05rem;margin-bottom:.25rem}.correction-field[_ngcontent-%COMP%]   .correction-value[_ngcontent-%COMP%]{font-size:.875rem;color:#3f4254;font-weight:500}.correction-field[_ngcontent-%COMP%]   .correction-comment[_ngcontent-%COMP%]{font-size:.875rem;color:#3f4254;line-height:1.5;background-color:#f8f9fa;padding:.75rem;border-radius:.25rem;border-left:3px solid #007bff}.correction-field[_ngcontent-%COMP%]   .correction-response[_ngcontent-%COMP%]{font-size:.875rem;color:#28a745;line-height:1.5;background-color:#f8fff9;padding:.75rem;border-radius:.25rem;border-left:3px solid #28a745}.correction-field[_ngcontent-%COMP%]   .correction-resolved[_ngcontent-%COMP%]{font-size:.875rem;color:#6c7293;font-weight:500}.comments-section[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid #e5eaee}.comment-content[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%]{font-size:.875rem;color:#3f4254;line-height:1.6;background-color:#f8f9fa;padding:1rem;border-radius:.375rem;border-left:4px solid #17a2b8;white-space:pre-wrap;word-wrap:break-word}.no-data-section[_ngcontent-%COMP%]{padding:2rem 1.5rem;text-align:center}.no-data-message[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.75rem;color:#6c7293}.no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;color:#dee2e6}.no-data-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500}.correction-contact-info[_ngcontent-%COMP%]{margin-top:1rem;padding-top:1rem;border-top:1px solid #e5eaee}.correction-contact-info[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%]{font-size:.75rem;font-weight:600;color:#6c7293;text-transform:uppercase;letter-spacing:.05rem;margin-bottom:.5rem}.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:.75rem;border-radius:.25rem}.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:.875rem;color:#3f4254;line-height:1.4}.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50}.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-title[_ngcontent-%COMP%]{color:#6c7293}.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-id[_ngcontent-%COMP%], .correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-license[_ngcontent-%COMP%], .correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-certification[_ngcontent-%COMP%]{color:#6c7293;font-family:Courier New,monospace}.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-organization[_ngcontent-%COMP%]{font-weight:500;color:#495057}.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-phone[_ngcontent-%COMP%]{color:#6c7293}.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%]{color:#007bff;font-weight:500}@media (max-width: 768px){.date-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}.banner-content[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.banner-item[_ngcontent-%COMP%]{font-size:.8rem}}\"]\n    });\n  }\n  return ReviewDetailsModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}