{"ast": null, "code": "import jsPDF from 'jspdf';\nimport autoTable from 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/custom-layout.utils.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"@angular/common\";\nfunction EditExternalReviewComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"span\", 24);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EditExternalReviewComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 25);\n  }\n}\nexport class EditExternalReviewComponent {\n  fb;\n  modal;\n  modalService;\n  customLayoutUtilsService;\n  permitsService;\n  appService;\n  permitId = null;\n  reviewData = {}; // For edit mode\n  permitDetails = {};\n  loggedInUserId = 'user'; // Should be passed from parent\n  reviewForm;\n  isLoading = false;\n  constructor(fb, modal, modalService, customLayoutUtilsService, permitsService, appService) {\n    this.fb = fb;\n    this.modal = modal;\n    this.modalService = modalService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.permitsService = permitsService;\n    this.appService = appService;\n  }\n  ngOnInit() {\n    this.reviewForm = this.fb.group({\n      // cityComments: [this.reviewData?.comments || ''],\n      EORAOROwner_Response: [this.reviewData?.EORAOROwner_Response || ''],\n      commentResponsedBy: [this.reviewData?.commentResponsedBy || '']\n    });\n  }\n  formatDateForInput(date) {\n    if (!date) return '';\n    const d = new Date(date);\n    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type=\"date\"]\n  }\n  onSubmit() {\n    this.isLoading = true;\n    const formData = {\n      // cityComments:this.reviewForm.controls.cityComments.value,\n      EORAOROwner_Response: this.reviewForm.controls.EORAOROwner_Response.value,\n      commentResponsedBy: this.reviewForm.controls.commentResponsedBy.value,\n      permitId: this.permitId,\n      commentsId: this.reviewData.commentsId,\n      loggedInUserId: this.loggedInUserId\n    };\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          this.modal.close('updated');\n        } else {\n          this.customLayoutUtilsService.showError(res.responseData.message || 'Failed to update external review', '');\n          //alert(res.responseData.message || 'Failed to update external review');\n        }\n      },\n      error: err => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('error updating external review', '');\n        //alert('Error updating external review');\n        console.error(err);\n      }\n    });\n  }\n  getPdf() {\n    const permitNumber = this.permitDetails?.permitNumber || '';\n    const projectName = this.permitDetails?.projectName || '';\n    const applicantName = this.permitDetails?.applicantName || '';\n    const reviewer = (this.reviewData?.reviewer || this.reviewData?.municipalityReviewer || '').toString();\n    const cityComments = (this.reviewData?.comments || (this.reviewData?.cityComments ?? '')).toString();\n    const ownerResponse = (this.reviewForm?.value?.EORAOROwner_Response ?? this.reviewData?.EORAOROwner_Response ?? '').toString();\n    const respondedBy = (this.reviewForm?.value?.commentResponsedBy ?? this.reviewData?.commentResponsedBy ?? '').toString();\n    const cycle = (this.reviewData?.cycle || this.reviewData?.Cycle || '').toString();\n    const status = (this.reviewData?.status || this.reviewData?.commentstatus || '').toString();\n    const reviewCategory = (this.reviewData?.reviewCategory || '').toString();\n    const dueDate = this.reviewData?.dueDate ? this.appService.formatDate(this.reviewData.dueDate) : '';\n    const completedDate = this.reviewData?.completedDate ? this.appService.formatDate(this.reviewData.completedDate) : '';\n    const createdDate = this.reviewData?.createdDate ? this.appService.formatDate(this.reviewData.createdDate) : '';\n    const doc = new jsPDF({\n      orientation: 'portrait',\n      unit: 'pt',\n      format: 'a4'\n    });\n    // Page metrics and margins\n    const pageWidth = doc.internal.pageSize.getWidth();\n    const pageHeight = doc.internal.pageSize.getHeight();\n    const margin = {\n      left: 50,\n      right: 50,\n      top: 60,\n      bottom: 60\n    };\n    let currentY = margin.top;\n    // Professional Header with Company Branding\n    const drawHeader = () => {\n      // Company Logo/Title Area\n      doc.setFillColor(41, 128, 185); // Professional blue\n      doc.rect(0, 0, pageWidth, 50, 'F');\n      doc.setTextColor(255, 255, 255);\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(18);\n      doc.text('PERMIT TRACKER SYSTEM', margin.left, 25);\n      doc.setFontSize(12);\n      doc.text('Review Report', pageWidth - margin.right - 80, 25);\n      // Reset text color\n      doc.setTextColor(0, 0, 0);\n      // Permit Information Header\n      currentY = 70;\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(14);\n      doc.text('REVIEW DETAILS', margin.left, currentY);\n      currentY += 20;\n    };\n    drawHeader();\n    // Permit Information Section\n    const drawPermitInfo = () => {\n      const infoData = [['Permit #:', permitNumber || 'N/A'], ['Project Name:', projectName || 'N/A'], ['Applicant Name:', applicantName || 'N/A'], ['Review Category:', reviewCategory || 'N/A'], ['Reviewer:', reviewer || 'N/A'], ['Status:', status || 'N/A'], ['Due Date:', dueDate || 'N/A'], ['Completed Date:', completedDate || 'N/A'], ['Created Date:', createdDate || 'N/A']];\n      autoTable(doc, {\n        startY: currentY,\n        body: infoData,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 10,\n          cellPadding: 8,\n          overflow: 'linebreak'\n        },\n        columnStyles: {\n          0: {\n            cellWidth: 120,\n            fontStyle: 'bold',\n            fillColor: [240, 240, 240]\n          },\n          1: {\n            cellWidth: 200\n          }\n        },\n        theme: 'grid'\n      });\n      currentY = doc.lastAutoTable.finalY + 20;\n    };\n    drawPermitInfo();\n    // City Comments Section\n    if (cityComments) {\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(12);\n      doc.setTextColor(192, 0, 0); // Red color for city comments\n      doc.text('CITY COMMENTS', margin.left, currentY);\n      currentY += 15;\n      doc.setTextColor(0, 0, 0);\n      doc.setFont('helvetica', 'normal');\n      doc.setFontSize(10);\n      // Split long text into multiple lines\n      const splitText = doc.splitTextToSize(cityComments, pageWidth - margin.left - margin.right);\n      doc.text(splitText, margin.left, currentY);\n      currentY += splitText.length * 12 + 20;\n    }\n    // Owner Response Section\n    if (ownerResponse) {\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(12);\n      doc.text('EOR/AOR/OWNER RESPONSE', margin.left, currentY);\n      currentY += 15;\n      doc.setFont('helvetica', 'normal');\n      doc.setFontSize(10);\n      const splitText = doc.splitTextToSize(ownerResponse, pageWidth - margin.left - margin.right);\n      doc.text(splitText, margin.left, currentY);\n      currentY += splitText.length * 12 + 20;\n      // Response details\n      if (respondedBy) {\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(10);\n        doc.text(`Responded By: ${respondedBy}`, margin.left, currentY);\n        currentY += 15;\n      }\n    }\n    // Footer\n    const drawFooter = () => {\n      const pageCount = doc.getNumberOfPages();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        // Footer line\n        doc.setDrawColor(200, 200, 200);\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n        // Footer text\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(8);\n        doc.setTextColor(100, 100, 100);\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n      }\n    };\n    drawFooter();\n    // Save the PDF\n    const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${reviewCategory ? reviewCategory.replace(/[^a-zA-Z0-9]/g, '_') + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  }\n  onCancel() {\n    this.modal.dismiss('cancelled');\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n  }\n  ngOnDestroy() {\n    // Reset loading state when modal is dismissed/cancelled\n    this.isLoading = false;\n  }\n  static ɵfac = function EditExternalReviewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EditExternalReviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.AppService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EditExternalReviewComponent,\n    selectors: [[\"app-edit-external-review\"]],\n    inputs: {\n      permitId: \"permitId\",\n      reviewData: \"reviewData\",\n      permitDetails: \"permitDetails\",\n      loggedInUserId: \"loggedInUserId\"\n    },\n    decls: 58,\n    vars: 20,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [\"class\", \"loading-overlay-inside\", 4, \"ngIf\"], [1, \"dates-section\"], [1, \"date-row\"], [1, \"date-item\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"date-value\"], [1, \"review-status\", 3, \"ngClass\"], [\"novalidate\", \"\", 3, \"ngSubmit\", \"formGroup\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [\"formControlName\", \"EORAOROwner_Response\", \"rows\", \"3\", \"placeholder\", \"Enter response\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"text\", \"formControlName\", \"commentResponsedBy\", \"placeholder\", \"Who responded to comments\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"modal-footer\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [1, \"loading-overlay-inside\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function EditExternalReviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵelementStart(4, \"div\");\n        i0.ɵɵtext(5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 3)(7, \"i\", 4);\n        i0.ɵɵlistener(\"click\", function EditExternalReviewComponent_Template_i_click_7_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 5);\n        i0.ɵɵtemplate(9, EditExternalReviewComponent_div_9_Template, 4, 0, \"div\", 6);\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"label\", 10);\n        i0.ɵɵtext(14, \"Reviwer\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"span\", 11);\n        i0.ɵɵtext(16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 9)(18, \"label\", 10);\n        i0.ɵɵtext(19, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"span\", 12);\n        i0.ɵɵtext(21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 9)(23, \"label\", 10);\n        i0.ɵɵtext(24, \"Due Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"span\", 11);\n        i0.ɵɵtext(26);\n        i0.ɵɵpipe(27, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"div\", 9)(29, \"label\", 10);\n        i0.ɵɵtext(30, \"Completed Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"span\", 11);\n        i0.ɵɵtext(32);\n        i0.ɵɵpipe(33, \"date\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(34, \"div\", 8)(35, \"div\", 9)(36, \"label\", 10);\n        i0.ɵɵtext(37, \"Comments\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"span\", 11);\n        i0.ɵɵtext(39);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(40, \"form\", 13);\n        i0.ɵɵlistener(\"ngSubmit\", function EditExternalReviewComponent_Template_form_ngSubmit_40_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(41, \"div\", 14)(42, \"div\", 15)(43, \"label\", 10);\n        i0.ɵɵtext(44, \"EOR / AOR / Owner Response\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(45, \"textarea\", 16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(46, \"div\", 14)(47, \"div\", 15)(48, \"label\", 10);\n        i0.ɵɵtext(49, \"Comment Responded By\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(50, \"input\", 17);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(51, \"div\", 18)(52, \"div\")(53, \"button\", 19);\n        i0.ɵɵlistener(\"click\", function EditExternalReviewComponent_Template_button_click_53_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵtext(54, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function EditExternalReviewComponent_Template_button_click_55_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtemplate(56, EditExternalReviewComponent_span_56_Template, 1, 0, \"span\", 21);\n        i0.ɵɵtext(57, \" Update \");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\"Edit External Review - \", ctx.reviewData == null ? null : ctx.reviewData.name, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(ctx.reviewData == null ? null : ctx.reviewData.reviewer);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass(ctx.reviewData.status));\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.reviewData == null ? null : ctx.reviewData.status, \" \");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(27, 14, ctx.reviewData == null ? null : ctx.reviewData.dueDate, \"MM/dd/yyyy\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 17, ctx.reviewData == null ? null : ctx.reviewData.completedDate, \"MM/dd/yyyy\"));\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(ctx.reviewData == null ? null : ctx.reviewData.comments);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.reviewForm);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.DatePipe],\n    styles: [\".dates-section[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background-color: #f8f9fa;\\n}\\n\\n.date-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n  gap: 2rem;\\n}\\n\\n.date-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.date-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  color: #6c7293;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n}\\n.date-item[_ngcontent-%COMP%]   .date-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n}\\n\\n.review-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n}\\n.review-status.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.review-status.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.review-status.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.review-status.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.review-status.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.review-status.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.review-status.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.review-status.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["jsPDF", "autoTable", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "EditExternalReviewComponent", "fb", "modal", "modalService", "customLayoutUtilsService", "permitsService", "appService", "permitId", "reviewData", "permitDetails", "loggedInUserId", "reviewForm", "isLoading", "constructor", "ngOnInit", "group", "EORAOROwner_Response", "commentResponsedBy", "formatDateForInput", "date", "d", "Date", "toISOString", "split", "onSubmit", "formData", "controls", "value", "commentsId", "updateExternalReview", "subscribe", "next", "res", "<PERSON><PERSON><PERSON>", "showSuccess", "responseData", "message", "close", "showError", "error", "err", "console", "getPdf", "permitNumber", "projectName", "applicantName", "reviewer", "municipalityReviewer", "toString", "cityComments", "comments", "ownerResponse", "respondedBy", "cycle", "Cycle", "status", "commentstatus", "reviewCategory", "dueDate", "formatDate", "completedDate", "createdDate", "doc", "orientation", "unit", "format", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "margin", "left", "right", "top", "bottom", "currentY", "<PERSON><PERSON><PERSON><PERSON>", "setFillColor", "rect", "setTextColor", "setFont", "setFontSize", "text", "drawPermitInfo", "infoData", "startY", "body", "styles", "font", "fontSize", "cellPadding", "overflow", "columnStyles", "cellWidth", "fontStyle", "fillColor", "theme", "lastAutoTable", "finalY", "splitText", "splitTextToSize", "length", "drawFooter", "pageCount", "getNumberOfPages", "i", "setPage", "setDrawColor", "line", "toLocaleString", "fileName", "replace", "save", "onCancel", "dismiss", "getStatusClass", "toLowerCase", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "NgbActiveModal", "NgbModal", "i3", "CustomLayoutUtilsService", "i4", "PermitsService", "i5", "AppService", "selectors", "inputs", "decls", "vars", "consts", "template", "EditExternalReviewComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵlistener", "EditExternalReviewComponent_Template_i_click_7_listener", "ɵɵtemplate", "EditExternalReviewComponent_div_9_Template", "EditExternalReviewComponent_Template_form_ngSubmit_40_listener", "EditExternalReviewComponent_Template_button_click_53_listener", "EditExternalReviewComponent_Template_button_click_55_listener", "EditExternalReviewComponent_span_56_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "ɵɵproperty", "ɵɵtextInterpolate", "ɵɵpipeBind2"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\edit-external-review\\edit-external-review.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\edit-external-review\\edit-external-review.component.html"], "sourcesContent": ["import { Component, Input, OnDestroy } from '@angular/core';\r\nimport { FormGroup, FormBuilder } from '@angular/forms';\r\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport jsPDF from 'jspdf';\r\nimport autoTable from 'jspdf-autotable';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\n\r\n@Component({\r\n  selector: 'app-edit-external-review',\r\n  templateUrl: './edit-external-review.component.html',\r\n  styleUrl: './edit-external-review.component.scss'\r\n})\r\nexport class EditExternalReviewComponent implements OnDestroy {\r\n  @Input() permitId: number | null = null;\r\n  @Input() reviewData: any = {}; // For edit mode\r\n  @Input() permitDetails: any = {};\r\n  @Input() loggedInUserId: string = 'user'; // Should be passed from parent\r\n\r\n  reviewForm!: FormGroup;\r\n  isLoading: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    public modal: NgbActiveModal,\r\n    private modalService: NgbModal,\r\n    private customLayoutUtilsService: CustomLayoutUtilsService,\r\n    private permitsService: PermitsService,\r\n    private appService: AppService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n\r\n    this.reviewForm = this.fb.group({\r\n      // cityComments: [this.reviewData?.comments || ''],\r\n      EORAOROwner_Response: [this.reviewData?.EORAOROwner_Response || ''],\r\n      commentResponsedBy: [this.reviewData?.commentResponsedBy || '']\r\n    });\r\n  }\r\n\r\n  private formatDateForInput(date: string | Date): string {\r\n    if (!date) return '';\r\n    const d = new Date(date);\r\n    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type=\"date\"]\r\n  }\r\n\r\n  onSubmit(): void {\r\n      this.isLoading = true;\r\n      const formData = {\r\n        // cityComments:this.reviewForm.controls.cityComments.value,\r\n        EORAOROwner_Response:this.reviewForm.controls.EORAOROwner_Response.value,\r\n        commentResponsedBy:this.reviewForm.controls.commentResponsedBy.value,\r\n        permitId: this.permitId,\r\n        commentsId:this.reviewData.commentsId,\r\n        loggedInUserId: this.loggedInUserId\r\n      };\r\n        this.permitsService.updateExternalReview(formData).subscribe({\r\n          next: (res: any) => {\r\n            this.isLoading = false;\r\n            if (res?.isFault === false) {\r\n              //alert(res.responseData.message);\r\n                                  this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n\r\n              this.modal.close('updated');\r\n\r\n            } else {\r\n                                  this.customLayoutUtilsService.showError(res.responseData.message || 'Failed to update external review', '');\r\n\r\n              //alert(res.responseData.message || 'Failed to update external review');\r\n            }\r\n          },\r\n          error: (err: any) => {\r\n            this.isLoading = false;\r\n                                this.customLayoutUtilsService.showError('error updating external review', '');\r\n\r\n            //alert('Error updating external review');\r\n            console.error(err);\r\n          }\r\n        });\r\n\r\n\r\n  }\r\n  getPdf(){\r\n    const permitNumber = this.permitDetails?.permitNumber || '';\r\n    const projectName = this.permitDetails?.projectName || '';\r\n    const applicantName = this.permitDetails?.applicantName || '';\r\n    const reviewer = (this.reviewData?.reviewer || this.reviewData?.municipalityReviewer || '').toString();\r\n    const cityComments = (this.reviewData?.comments || (this.reviewData?.cityComments ?? '')).toString();\r\n    const ownerResponse = (this.reviewForm?.value?.EORAOROwner_Response ?? this.reviewData?.EORAOROwner_Response ?? '').toString();\r\n    const respondedBy = (this.reviewForm?.value?.commentResponsedBy ?? this.reviewData?.commentResponsedBy ?? '').toString();\r\n    const cycle = (this.reviewData?.cycle || this.reviewData?.Cycle || '').toString();\r\n    const status = (this.reviewData?.status || this.reviewData?.commentstatus || '').toString();\r\n    const reviewCategory = (this.reviewData?.reviewCategory || '').toString();\r\n    const dueDate = this.reviewData?.dueDate ? this.appService.formatDate(this.reviewData.dueDate) : '';\r\n    const completedDate = this.reviewData?.completedDate ? this.appService.formatDate(this.reviewData.completedDate) : '';\r\n    const createdDate = this.reviewData?.createdDate ? this.appService.formatDate(this.reviewData.createdDate) : '';\r\n\r\n    const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\r\n\r\n    // Page metrics and margins\r\n    const pageWidth = doc.internal.pageSize.getWidth();\r\n    const pageHeight = doc.internal.pageSize.getHeight();\r\n    const margin = { left: 50, right: 50, top: 60, bottom: 60 };\r\n\r\n    let currentY = margin.top;\r\n\r\n    // Professional Header with Company Branding\r\n    const drawHeader = () => {\r\n      // Company Logo/Title Area\r\n      doc.setFillColor(41, 128, 185); // Professional blue\r\n      doc.rect(0, 0, pageWidth, 50, 'F');\r\n      \r\n      doc.setTextColor(255, 255, 255);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(18);\r\n      doc.text('PERMIT TRACKER SYSTEM', margin.left, 25);\r\n      \r\n      doc.setFontSize(12);\r\n      doc.text('Review Report', pageWidth - margin.right - 80, 25);\r\n      \r\n      // Reset text color\r\n      doc.setTextColor(0, 0, 0);\r\n      \r\n      // Permit Information Header\r\n      currentY = 70;\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(14);\r\n      doc.text('REVIEW DETAILS', margin.left, currentY);\r\n      \r\n      currentY += 20;\r\n    };\r\n\r\n    drawHeader();\r\n\r\n    // Permit Information Section\r\n    const drawPermitInfo = () => {\r\n      const infoData = [\r\n        ['Permit #:', permitNumber || 'N/A'],\r\n        ['Project Name:', projectName || 'N/A'],\r\n        ['Applicant Name:', applicantName || 'N/A'],\r\n        ['Review Category:', reviewCategory || 'N/A'],\r\n        ['Reviewer:', reviewer || 'N/A'],\r\n        ['Status:', status || 'N/A'],\r\n        ['Due Date:', dueDate || 'N/A'],\r\n        ['Completed Date:', completedDate || 'N/A'],\r\n        ['Created Date:', createdDate || 'N/A']\r\n      ];\r\n\r\n      autoTable(doc, {\r\n        startY: currentY,\r\n        body: infoData,\r\n        margin: { left: margin.left, right: margin.right },\r\n        styles: {\r\n          font: 'helvetica',\r\n          fontSize: 10,\r\n          cellPadding: 8,\r\n          overflow: 'linebreak'\r\n        },\r\n        columnStyles: {\r\n          0: { cellWidth: 120, fontStyle: 'bold', fillColor: [240, 240, 240] },\r\n          1: { cellWidth: 200 }\r\n        },\r\n        theme: 'grid'\r\n      });\r\n\r\n      currentY = (doc as any).lastAutoTable.finalY + 20;\r\n    };\r\n\r\n    drawPermitInfo();\r\n\r\n    // City Comments Section\r\n    if (cityComments) {\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(12);\r\n      doc.setTextColor(192, 0, 0); // Red color for city comments\r\n      doc.text('CITY COMMENTS', margin.left, currentY);\r\n      currentY += 15;\r\n\r\n      doc.setTextColor(0, 0, 0);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.setFontSize(10);\r\n      \r\n      // Split long text into multiple lines\r\n      const splitText = doc.splitTextToSize(cityComments, pageWidth - margin.left - margin.right);\r\n      doc.text(splitText, margin.left, currentY);\r\n      currentY += splitText.length * 12 + 20;\r\n    }\r\n\r\n    // Owner Response Section\r\n    if (ownerResponse) {\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(12);\r\n      doc.text('EOR/AOR/OWNER RESPONSE', margin.left, currentY);\r\n      currentY += 15;\r\n\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.setFontSize(10);\r\n      \r\n      const splitText = doc.splitTextToSize(ownerResponse, pageWidth - margin.left - margin.right);\r\n      doc.text(splitText, margin.left, currentY);\r\n      currentY += splitText.length * 12 + 20;\r\n\r\n      // Response details\r\n      if (respondedBy) {\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.setFontSize(10);\r\n        doc.text(`Responded By: ${respondedBy}`, margin.left, currentY);\r\n        currentY += 15;\r\n      }\r\n    }\r\n\r\n    // Footer\r\n    const drawFooter = () => {\r\n      const pageCount = doc.getNumberOfPages();\r\n      for (let i = 1; i <= pageCount; i++) {\r\n        doc.setPage(i);\r\n        \r\n        // Footer line\r\n        doc.setDrawColor(200, 200, 200);\r\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\r\n        \r\n        // Footer text\r\n        doc.setFont('helvetica', 'normal');\r\n        doc.setFontSize(8);\r\n        doc.setTextColor(100, 100, 100);\r\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\r\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\r\n      }\r\n    };\r\n\r\n    drawFooter();\r\n\r\n    // Save the PDF\r\n    const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${reviewCategory ? reviewCategory.replace(/[^a-zA-Z0-9]/g, '_') + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\r\n    doc.save(fileName);\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.modal.dismiss('cancelled');\r\n  }\r\n\r\n  public getStatusClass(status: string): string {\r\n    if (!status) return 'status-n-a';\r\n    return (\r\n      'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-')\r\n    );\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // Reset loading state when modal is dismissed/cancelled\r\n    this.isLoading = false;\r\n  }\r\n}\r\n", "<div class=\"modal-content h-auto\">\r\n  <!-- Header -->\r\n  <div class=\"modal-header bg-light-primary\">\r\n    <div class=\"modal-title h5 fs-3\">\r\n      <ng-container>\r\n        <div>Edit External Review - {{ reviewData?.name }}</div>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n      <i\r\n        class=\"fa-solid fs-2 fa-xmark text-white\"\r\n        (click)=\"onCancel()\"\r\n      ></i>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Body -->\r\n  <div\r\n    class=\"modal-body large-modal-body\"\r\n   \r\n  >\r\n   <!-- style=\"max-height: calc(100vh - 250px); overflow-y: auto; position: relative;\" -->\r\n    <!-- Loading Overlay -->\r\n    <div *ngIf=\"isLoading\" class=\"loading-overlay-inside\">\r\n      <div class=\"spinner-border text-primary spinner-md\" role=\"status\">\r\n        <span class=\"visually-hidden\">Loading...</span>\r\n      </div>\r\n    </div>\r\n    <div class=\"dates-section\">\r\n      <div class=\"date-row\">\r\n        <div class=\"date-item\">\r\n          <label class=\"fw-bold form-label mb-2\">Reviwer</label>\r\n          <span class=\"date-value\">{{ reviewData?.reviewer }}</span>\r\n        </div>\r\n        <div class=\"date-item\">\r\n          <label class=\"fw-bold form-label mb-2\">Status</label>\r\n          <span class=\"review-status\" [ngClass]=\"getStatusClass(reviewData.status)\">\r\n            {{ reviewData?.status }}\r\n          </span>\r\n          <!-- <span class=\"date-value\" [ngClass]=\"getStatusClass(reviewData.status)\">{{ reviewData?.status }}</span> -->\r\n        </div>\r\n        <div class=\"date-item\">\r\n          <label class=\"fw-bold form-label mb-2\">Due Date</label>\r\n          <span class=\"date-value\">{{ reviewData?.dueDate | date:'MM/dd/yyyy' }}</span>\r\n        </div>\r\n        <div class=\"date-item\">\r\n          <label class=\"fw-bold form-label mb-2\">Completed Date</label>\r\n          <span class=\"date-value\">{{ reviewData?.completedDate | date:'MM/dd/yyyy' }}</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"date-row\">\r\n        <div class=\"date-item\">\r\n          <label class=\"fw-bold form-label mb-2\">Comments</label>\r\n          <span class=\"date-value\">{{ reviewData?.comments }}</span>\r\n        </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Form -->\r\n    <form [formGroup]=\"reviewForm\" (ngSubmit)=\"onSubmit()\" novalidate>\r\n\r\n\r\n\r\n      <!-- City Comments -->\r\n      <!-- <div class=\"row mt-4\">\r\n        <div class=\"col-xl-12\">\r\n          <label class=\"fw-bold form-label mb-2\">City Comments</label>\r\n          <textarea\r\n            formControlName=\"cityComments\"\r\n            rows=\"3\"\r\n            class=\"form-control form-control-sm\"\r\n            placeholder=\"Enter comments\"\r\n            readonly\r\n            [disabled] =true\r\n          ></textarea>\r\n        </div>\r\n      </div> -->\r\n\r\n      <!-- EOR / AOR / Owner Response -->\r\n      <div class=\"row mt-4\">\r\n        <div class=\"col-xl-12\">\r\n          <label class=\"fw-bold form-label mb-2\">EOR / AOR / Owner Response</label>\r\n          <textarea\r\n            formControlName=\"EORAOROwner_Response\"\r\n            rows=\"3\"\r\n            class=\"form-control form-control-sm\"\r\n            placeholder=\"Enter response\"\r\n            [disabled]=\"isLoading\"\r\n          ></textarea>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Comment Responded By -->\r\n      <div class=\"row mt-4\">\r\n        <div class=\"col-xl-12\">\r\n          <label class=\"fw-bold form-label mb-2\">Comment Responded By</label>\r\n          <input\r\n            type=\"text\"\r\n            formControlName=\"commentResponsedBy\"\r\n            class=\"form-control form-control-sm\"\r\n            placeholder=\"Who responded to comments\"\r\n            [disabled]=\"isLoading\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </form>\r\n  </div>\r\n\r\n  <!-- Footer -->\r\n  <div class=\"modal-footer justify-content-end\">\r\n    <div>\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-danger btn-sm btn-elevate me-2\"\r\n        (click)=\"onCancel()\"\r\n        [disabled]=\"isLoading\"\r\n      >\r\n        Cancel\r\n      </button>\r\n      <button\r\n        type=\"submit\"\r\n        class=\"btn btn-success btn-sm\"\r\n        [disabled]=\"isLoading\"\r\n        (click)=\"onSubmit()\"\r\n      >\r\n        <span *ngIf=\"isLoading\" class=\"spinner-border spinner-border-sm me-2\"></span>\r\n        Update\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n"], "mappings": "AAKA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,iBAAiB;;;;;;;;;;ICmB/BC,EAFJ,CAAAC,cAAA,cAAsD,cACc,eAClC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAE5CF,EAF4C,CAAAG,YAAA,EAAO,EAC3C,EACF;;;;;IAkGFH,EAAA,CAAAI,SAAA,eAA6E;;;AD/GrF,OAAM,MAAOC,2BAA2B;EAU5BC,EAAA;EACDC,KAAA;EACCC,YAAA;EACAC,wBAAA;EACAC,cAAA;EACAC,UAAA;EAdDC,QAAQ,GAAkB,IAAI;EAC9BC,UAAU,GAAQ,EAAE,CAAC,CAAC;EACtBC,aAAa,GAAQ,EAAE;EACvBC,cAAc,GAAW,MAAM,CAAC,CAAC;EAE1CC,UAAU;EACVC,SAAS,GAAY,KAAK;EAE1BC,YACUZ,EAAe,EAChBC,KAAqB,EACpBC,YAAsB,EACtBC,wBAAkD,EAClDC,cAA8B,EAC9BC,UAAsB;IALtB,KAAAL,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,UAAU,GAAVA,UAAU;EACjB;EAEHQ,QAAQA,CAAA;IAEN,IAAI,CAACH,UAAU,GAAG,IAAI,CAACV,EAAE,CAACc,KAAK,CAAC;MAC9B;MACAC,oBAAoB,EAAE,CAAC,IAAI,CAACR,UAAU,EAAEQ,oBAAoB,IAAI,EAAE,CAAC;MACnEC,kBAAkB,EAAE,CAAC,IAAI,CAACT,UAAU,EAAES,kBAAkB,IAAI,EAAE;KAC/D,CAAC;EACJ;EAEQC,kBAAkBA,CAACC,IAAmB;IAC5C,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAOC,CAAC,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxC;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACZ,SAAS,GAAG,IAAI;IACrB,MAAMa,QAAQ,GAAG;MACf;MACAT,oBAAoB,EAAC,IAAI,CAACL,UAAU,CAACe,QAAQ,CAACV,oBAAoB,CAACW,KAAK;MACxEV,kBAAkB,EAAC,IAAI,CAACN,UAAU,CAACe,QAAQ,CAACT,kBAAkB,CAACU,KAAK;MACpEpB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBqB,UAAU,EAAC,IAAI,CAACpB,UAAU,CAACoB,UAAU;MACrClB,cAAc,EAAE,IAAI,CAACA;KACtB;IACC,IAAI,CAACL,cAAc,CAACwB,oBAAoB,CAACJ,QAAQ,CAAC,CAACK,SAAS,CAAC;MAC3DC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACpB,SAAS,GAAG,KAAK;QACtB,IAAIoB,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UAC1B;UACoB,IAAI,CAAC7B,wBAAwB,CAAC8B,WAAW,CAACF,GAAG,CAACG,YAAY,CAACC,OAAO,EAAE,EAAE,CAAC;UAE3F,IAAI,CAAClC,KAAK,CAACmC,KAAK,CAAC,SAAS,CAAC;QAE7B,CAAC,MAAM;UACe,IAAI,CAACjC,wBAAwB,CAACkC,SAAS,CAACN,GAAG,CAACG,YAAY,CAACC,OAAO,IAAI,kCAAkC,EAAE,EAAE,CAAC;UAE/H;QACF;MACF,CAAC;MACDG,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAAC5B,SAAS,GAAG,KAAK;QACF,IAAI,CAACR,wBAAwB,CAACkC,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;QAEjG;QACAG,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EAGR;EACAE,MAAMA,CAAA;IACJ,MAAMC,YAAY,GAAG,IAAI,CAAClC,aAAa,EAAEkC,YAAY,IAAI,EAAE;IAC3D,MAAMC,WAAW,GAAG,IAAI,CAACnC,aAAa,EAAEmC,WAAW,IAAI,EAAE;IACzD,MAAMC,aAAa,GAAG,IAAI,CAACpC,aAAa,EAAEoC,aAAa,IAAI,EAAE;IAC7D,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACtC,UAAU,EAAEsC,QAAQ,IAAI,IAAI,CAACtC,UAAU,EAAEuC,oBAAoB,IAAI,EAAE,EAAEC,QAAQ,EAAE;IACtG,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACzC,UAAU,EAAE0C,QAAQ,KAAK,IAAI,CAAC1C,UAAU,EAAEyC,YAAY,IAAI,EAAE,CAAC,EAAED,QAAQ,EAAE;IACpG,MAAMG,aAAa,GAAG,CAAC,IAAI,CAACxC,UAAU,EAAEgB,KAAK,EAAEX,oBAAoB,IAAI,IAAI,CAACR,UAAU,EAAEQ,oBAAoB,IAAI,EAAE,EAAEgC,QAAQ,EAAE;IAC9H,MAAMI,WAAW,GAAG,CAAC,IAAI,CAACzC,UAAU,EAAEgB,KAAK,EAAEV,kBAAkB,IAAI,IAAI,CAACT,UAAU,EAAES,kBAAkB,IAAI,EAAE,EAAE+B,QAAQ,EAAE;IACxH,MAAMK,KAAK,GAAG,CAAC,IAAI,CAAC7C,UAAU,EAAE6C,KAAK,IAAI,IAAI,CAAC7C,UAAU,EAAE8C,KAAK,IAAI,EAAE,EAAEN,QAAQ,EAAE;IACjF,MAAMO,MAAM,GAAG,CAAC,IAAI,CAAC/C,UAAU,EAAE+C,MAAM,IAAI,IAAI,CAAC/C,UAAU,EAAEgD,aAAa,IAAI,EAAE,EAAER,QAAQ,EAAE;IAC3F,MAAMS,cAAc,GAAG,CAAC,IAAI,CAACjD,UAAU,EAAEiD,cAAc,IAAI,EAAE,EAAET,QAAQ,EAAE;IACzE,MAAMU,OAAO,GAAG,IAAI,CAAClD,UAAU,EAAEkD,OAAO,GAAG,IAAI,CAACpD,UAAU,CAACqD,UAAU,CAAC,IAAI,CAACnD,UAAU,CAACkD,OAAO,CAAC,GAAG,EAAE;IACnG,MAAME,aAAa,GAAG,IAAI,CAACpD,UAAU,EAAEoD,aAAa,GAAG,IAAI,CAACtD,UAAU,CAACqD,UAAU,CAAC,IAAI,CAACnD,UAAU,CAACoD,aAAa,CAAC,GAAG,EAAE;IACrH,MAAMC,WAAW,GAAG,IAAI,CAACrD,UAAU,EAAEqD,WAAW,GAAG,IAAI,CAACvD,UAAU,CAACqD,UAAU,CAAC,IAAI,CAACnD,UAAU,CAACqD,WAAW,CAAC,GAAG,EAAE;IAE/G,MAAMC,GAAG,GAAG,IAAIrE,KAAK,CAAC;MAAEsE,WAAW,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAE,CAAC;IAE5E;IACA,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;IAClD,MAAMC,UAAU,GAAGR,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACG,SAAS,EAAE;IACpD,MAAMC,MAAM,GAAG;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,GAAG,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAE,CAAE;IAE3D,IAAIC,QAAQ,GAAGL,MAAM,CAACG,GAAG;IAEzB;IACA,MAAMG,UAAU,GAAGA,CAAA,KAAK;MACtB;MACAhB,GAAG,CAACiB,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;MAChCjB,GAAG,CAACkB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAEd,SAAS,EAAE,EAAE,EAAE,GAAG,CAAC;MAElCJ,GAAG,CAACmB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BnB,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,uBAAuB,EAAEZ,MAAM,CAACC,IAAI,EAAE,EAAE,CAAC;MAElDX,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,eAAe,EAAElB,SAAS,GAAGM,MAAM,CAACE,KAAK,GAAG,EAAE,EAAE,EAAE,CAAC;MAE5D;MACAZ,GAAG,CAACmB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAEzB;MACAJ,QAAQ,GAAG,EAAE;MACbf,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,gBAAgB,EAAEZ,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MAEjDA,QAAQ,IAAI,EAAE;IAChB,CAAC;IAEDC,UAAU,EAAE;IAEZ;IACA,MAAMO,cAAc,GAAGA,CAAA,KAAK;MAC1B,MAAMC,QAAQ,GAAG,CACf,CAAC,WAAW,EAAE3C,YAAY,IAAI,KAAK,CAAC,EACpC,CAAC,eAAe,EAAEC,WAAW,IAAI,KAAK,CAAC,EACvC,CAAC,iBAAiB,EAAEC,aAAa,IAAI,KAAK,CAAC,EAC3C,CAAC,kBAAkB,EAAEY,cAAc,IAAI,KAAK,CAAC,EAC7C,CAAC,WAAW,EAAEX,QAAQ,IAAI,KAAK,CAAC,EAChC,CAAC,SAAS,EAAES,MAAM,IAAI,KAAK,CAAC,EAC5B,CAAC,WAAW,EAAEG,OAAO,IAAI,KAAK,CAAC,EAC/B,CAAC,iBAAiB,EAAEE,aAAa,IAAI,KAAK,CAAC,EAC3C,CAAC,eAAe,EAAEC,WAAW,IAAI,KAAK,CAAC,CACxC;MAEDnE,SAAS,CAACoE,GAAG,EAAE;QACbyB,MAAM,EAAEV,QAAQ;QAChBW,IAAI,EAAEF,QAAQ;QACdd,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDe,MAAM,EAAE;UACNC,IAAI,EAAE,WAAW;UACjBC,QAAQ,EAAE,EAAE;UACZC,WAAW,EAAE,CAAC;UACdC,QAAQ,EAAE;SACX;QACDC,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEC,SAAS,EAAE,GAAG;YAAEC,SAAS,EAAE,MAAM;YAAEC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UAAC,CAAE;UACpE,CAAC,EAAE;YAAEF,SAAS,EAAE;UAAG;SACpB;QACDG,KAAK,EAAE;OACR,CAAC;MAEFrB,QAAQ,GAAIf,GAAW,CAACqC,aAAa,CAACC,MAAM,GAAG,EAAE;IACnD,CAAC;IAEDf,cAAc,EAAE;IAEhB;IACA,IAAIpC,YAAY,EAAE;MAChBa,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACmB,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7BnB,GAAG,CAACsB,IAAI,CAAC,eAAe,EAAEZ,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MAChDA,QAAQ,IAAI,EAAE;MAEdf,GAAG,CAACmB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzBnB,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MAEnB;MACA,MAAMkB,SAAS,GAAGvC,GAAG,CAACwC,eAAe,CAACrD,YAAY,EAAEiB,SAAS,GAAGM,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,CAAC;MAC3FZ,GAAG,CAACsB,IAAI,CAACiB,SAAS,EAAE7B,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MAC1CA,QAAQ,IAAIwB,SAAS,CAACE,MAAM,GAAG,EAAE,GAAG,EAAE;IACxC;IAEA;IACA,IAAIpD,aAAa,EAAE;MACjBW,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,wBAAwB,EAAEZ,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MACzDA,QAAQ,IAAI,EAAE;MAEdf,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MAEnB,MAAMkB,SAAS,GAAGvC,GAAG,CAACwC,eAAe,CAACnD,aAAa,EAAEe,SAAS,GAAGM,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,CAAC;MAC5FZ,GAAG,CAACsB,IAAI,CAACiB,SAAS,EAAE7B,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MAC1CA,QAAQ,IAAIwB,SAAS,CAACE,MAAM,GAAG,EAAE,GAAG,EAAE;MAEtC;MACA,IAAInD,WAAW,EAAE;QACfU,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;QACnBrB,GAAG,CAACsB,IAAI,CAAC,iBAAiBhC,WAAW,EAAE,EAAEoB,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;QAC/DA,QAAQ,IAAI,EAAE;MAChB;IACF;IAEA;IACA,MAAM2B,UAAU,GAAGA,CAAA,KAAK;MACtB,MAAMC,SAAS,GAAG3C,GAAG,CAAC4C,gBAAgB,EAAE;MACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,SAAS,EAAEE,CAAC,EAAE,EAAE;QACnC7C,GAAG,CAAC8C,OAAO,CAACD,CAAC,CAAC;QAEd;QACA7C,GAAG,CAAC+C,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/B/C,GAAG,CAACgD,IAAI,CAACtC,MAAM,CAACC,IAAI,EAAEH,UAAU,GAAG,EAAE,EAAEJ,SAAS,GAAGM,MAAM,CAACE,KAAK,EAAEJ,UAAU,GAAG,EAAE,CAAC;QAEjF;QACAR,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCpB,GAAG,CAACqB,WAAW,CAAC,CAAC,CAAC;QAClBrB,GAAG,CAACmB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/BnB,GAAG,CAACsB,IAAI,CAAC,iBAAiB,IAAI/D,IAAI,EAAE,CAAC0F,cAAc,EAAE,EAAE,EAAEvC,MAAM,CAACC,IAAI,EAAEH,UAAU,GAAG,EAAE,CAAC;QACtFR,GAAG,CAACsB,IAAI,CAAC,QAAQuB,CAAC,OAAOF,SAAS,EAAE,EAAEvC,SAAS,GAAGM,MAAM,CAACE,KAAK,GAAG,EAAE,EAAEJ,UAAU,GAAG,EAAE,CAAC;MACvF;IACF,CAAC;IAEDkC,UAAU,EAAE;IAEZ;IACA,MAAMQ,QAAQ,GAAG,UAAUrE,YAAY,GAAGA,YAAY,GAAG,GAAG,GAAG,EAAE,GAAGc,cAAc,GAAGA,cAAc,CAACwD,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI5F,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC3LuC,GAAG,CAACoD,IAAI,CAACF,QAAQ,CAAC;EACpB;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACjH,KAAK,CAACkH,OAAO,CAAC,WAAW,CAAC;EACjC;EAEOC,cAAcA,CAAC9D,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,OACE,SAAS,GAAGA,MAAM,CAAC+D,WAAW,EAAE,CAACL,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE7E;EAEAM,WAAWA,CAAA;IACT;IACA,IAAI,CAAC3G,SAAS,GAAG,KAAK;EACxB;;qCA9OWZ,2BAA2B,EAAAL,EAAA,CAAA6H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/H,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAjI,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAE,QAAA,GAAAlI,EAAA,CAAA6H,iBAAA,CAAAM,EAAA,CAAAC,wBAAA,GAAApI,EAAA,CAAA6H,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAtI,EAAA,CAAA6H,iBAAA,CAAAU,EAAA,CAAAC,UAAA;EAAA;;UAA3BnI,2BAA2B;IAAAoI,SAAA;IAAAC,MAAA;MAAA9H,QAAA;MAAAC,UAAA;MAAAC,aAAA;MAAAC,cAAA;IAAA;IAAA4H,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXpChJ,EAHJ,CAAAC,cAAA,aAAkC,aAEW,aACR;QAC/BD,EAAA,CAAAkJ,uBAAA,GAAc;QACZlJ,EAAA,CAAAC,cAAA,UAAK;QAAAD,EAAA,CAAAE,MAAA,GAA6C;QAAAF,EAAA,CAAAG,YAAA,EAAM;;QAE5DH,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAItB;QADCD,EAAA,CAAAmJ,UAAA,mBAAAC,wDAAA;UAAA,OAASH,GAAA,CAAAzB,QAAA,EAAU;QAAA,EAAC;QAG1BxH,EAFK,CAAAG,YAAA,EAAI,EACD,EACF;QAGNH,EAAA,CAAAC,cAAA,aAGC;QAGCD,EAAA,CAAAqJ,UAAA,IAAAC,0CAAA,iBAAsD;QAQhDtJ,EAHN,CAAAC,cAAA,cAA2B,cACH,cACG,iBACkB;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtDH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,IAA0B;QACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;QAEJH,EADF,CAAAC,cAAA,cAAuB,iBACkB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrDH,EAAA,CAAAC,cAAA,gBAA0E;QACxED,EAAA,CAAAE,MAAA,IACF;QAEFF,EAFE,CAAAG,YAAA,EAAO,EAEH;QAEJH,EADF,CAAAC,cAAA,cAAuB,iBACkB;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACvDH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,IAA6C;;QACxEF,EADwE,CAAAG,YAAA,EAAO,EACzE;QAEJH,EADF,CAAAC,cAAA,cAAuB,iBACkB;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7DH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAmD;;QAEhFF,EAFgF,CAAAG,YAAA,EAAO,EAC/E,EACF;QAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,iBACkB;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACvDH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,IAA0B;QAGzDF,EAHyD,CAAAG,YAAA,EAAO,EACtD,EACA,EACJ;QAGNH,EAAA,CAAAC,cAAA,gBAAkE;QAAnCD,EAAA,CAAAmJ,UAAA,sBAAAI,+DAAA;UAAA,OAAYN,GAAA,CAAApH,QAAA,EAAU;QAAA,EAAC;QAsBhD7B,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;QAAAD,EAAA,CAAAE,MAAA,kCAA0B;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACzEH,EAAA,CAAAI,SAAA,oBAMY;QAEhBJ,EADE,CAAAG,YAAA,EAAM,EACF;QAKFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;QAAAD,EAAA,CAAAE,MAAA,4BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACnEH,EAAA,CAAAI,SAAA,iBAME;QAIVJ,EAHM,CAAAG,YAAA,EAAM,EACF,EACD,EACH;QAKFH,EAFJ,CAAAC,cAAA,eAA8C,WACvC,kBAMF;QAFCD,EAAA,CAAAmJ,UAAA,mBAAAK,8DAAA;UAAA,OAASP,GAAA,CAAAzB,QAAA,EAAU;QAAA,EAAC;QAGpBxH,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAKC;QADCD,EAAA,CAAAmJ,UAAA,mBAAAM,8DAAA;UAAA,OAASR,GAAA,CAAApH,QAAA,EAAU;QAAA,EAAC;QAEpB7B,EAAA,CAAAqJ,UAAA,KAAAK,4CAAA,mBAAsE;QACtE1J,EAAA,CAAAE,MAAA,gBACF;QAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;QA7HOH,EAAA,CAAA2J,SAAA,GAA6C;QAA7C3J,EAAA,CAAA4J,kBAAA,4BAAAX,GAAA,CAAApI,UAAA,kBAAAoI,GAAA,CAAApI,UAAA,CAAAgJ,IAAA,KAA6C;QAkBhD7J,EAAA,CAAA2J,SAAA,GAAe;QAAf3J,EAAA,CAAA8J,UAAA,SAAAb,GAAA,CAAAhI,SAAA,CAAe;QASUjB,EAAA,CAAA2J,SAAA,GAA0B;QAA1B3J,EAAA,CAAA+J,iBAAA,CAAAd,GAAA,CAAApI,UAAA,kBAAAoI,GAAA,CAAApI,UAAA,CAAAsC,QAAA,CAA0B;QAIvBnD,EAAA,CAAA2J,SAAA,GAA6C;QAA7C3J,EAAA,CAAA8J,UAAA,YAAAb,GAAA,CAAAvB,cAAA,CAAAuB,GAAA,CAAApI,UAAA,CAAA+C,MAAA,EAA6C;QACvE5D,EAAA,CAAA2J,SAAA,EACF;QADE3J,EAAA,CAAA4J,kBAAA,MAAAX,GAAA,CAAApI,UAAA,kBAAAoI,GAAA,CAAApI,UAAA,CAAA+C,MAAA,MACF;QAKyB5D,EAAA,CAAA2J,SAAA,GAA6C;QAA7C3J,EAAA,CAAA+J,iBAAA,CAAA/J,EAAA,CAAAgK,WAAA,SAAAf,GAAA,CAAApI,UAAA,kBAAAoI,GAAA,CAAApI,UAAA,CAAAkD,OAAA,gBAA6C;QAI7C/D,EAAA,CAAA2J,SAAA,GAAmD;QAAnD3J,EAAA,CAAA+J,iBAAA,CAAA/J,EAAA,CAAAgK,WAAA,SAAAf,GAAA,CAAApI,UAAA,kBAAAoI,GAAA,CAAApI,UAAA,CAAAoD,aAAA,gBAAmD;QAMnDjE,EAAA,CAAA2J,SAAA,GAA0B;QAA1B3J,EAAA,CAAA+J,iBAAA,CAAAd,GAAA,CAAApI,UAAA,kBAAAoI,GAAA,CAAApI,UAAA,CAAA0C,QAAA,CAA0B;QAMnDvD,EAAA,CAAA2J,SAAA,EAAwB;QAAxB3J,EAAA,CAAA8J,UAAA,cAAAb,GAAA,CAAAjI,UAAA,CAAwB;QA4BtBhB,EAAA,CAAA2J,SAAA,GAAsB;QAAtB3J,EAAA,CAAA8J,UAAA,aAAAb,GAAA,CAAAhI,SAAA,CAAsB;QActBjB,EAAA,CAAA2J,SAAA,GAAsB;QAAtB3J,EAAA,CAAA8J,UAAA,aAAAb,GAAA,CAAAhI,SAAA,CAAsB;QAc1BjB,EAAA,CAAA2J,SAAA,GAAsB;QAAtB3J,EAAA,CAAA8J,UAAA,aAAAb,GAAA,CAAAhI,SAAA,CAAsB;QAOtBjB,EAAA,CAAA2J,SAAA,GAAsB;QAAtB3J,EAAA,CAAA8J,UAAA,aAAAb,GAAA,CAAAhI,SAAA,CAAsB;QAGfjB,EAAA,CAAA2J,SAAA,EAAe;QAAf3J,EAAA,CAAA8J,UAAA,SAAAb,GAAA,CAAAhI,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}