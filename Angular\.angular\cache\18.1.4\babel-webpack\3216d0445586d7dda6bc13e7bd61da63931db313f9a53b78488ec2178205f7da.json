{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { NavigationStart } from '@angular/router';\nimport { EmailTemplatesEditComponent } from '../email-templates-edit/email-templates-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"src/app/modules/services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/email-template.service\";\nimport * as i8 from \"../../services/kendo-column.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@progress/kendo-angular-grid\";\nimport * as i12 from \"@progress/kendo-angular-inputs\";\nimport * as i13 from \"@progress/kendo-angular-buttons\";\nimport * as i14 from \"ng-inline-svg-2\";\nimport * as i15 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c7 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction EmailTemplatesListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"span\", 19);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"kendo-textbox\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    })(\"clear\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_clear_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"span\", 24);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 27);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(12, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(14, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(16, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_16_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37)(3, \"label\", 38);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"label\", 38);\n    i0.ɵɵtext(8, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.role, $event) || (ctx_r2.appliedFilters.role = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 41)(11, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(12, \"i\", 43);\n    i0.ɵɵtext(13, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(15, \"i\", 45);\n    i0.ɵɵtext(16, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.roles);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.role);\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_template_16_div_0_Template, 17, 4, \"div\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const dataItem_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.unlockUser(dataItem_r6));\n    });\n    i0.ɵɵelement(1, \"span\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 55);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.templatePID));\n    });\n    i0.ɵɵelement(1, \"span\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template, 2, 1, \"a\", 57);\n  }\n  if (rf & 2) {\n    const dataItem_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen055.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r6.IsLocked);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 53);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template, 3, 2, \"ng-template\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c6));\n    i0.ɵɵproperty(\"width\", 125)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(10, _c7))(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r8.templateName, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r9 = ctx.$implicit;\n    const column_r10 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r10)(\"filter\", filter_r9)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 60);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"templateName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"templateName\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r11.emailTo, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r12 = ctx.$implicit;\n    const column_r13 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r13)(\"filter\", filter_r12)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 64);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 250)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"email\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"emailTo\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r14.emailSubject, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r15 = ctx.$implicit;\n    const column_r16 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r16)(\"filter\", filter_r15)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 65);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"title\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"title\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r17.category, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r18 = ctx.$implicit;\n    const column_r19 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r19)(\"filter\", filter_r18)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 66);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"roleName\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"roleName\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 70);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 71);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen040.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_0_Template, 1, 1, \"span\", 68)(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_1_Template, 1, 1, \"span\", 69);\n  }\n  if (rf & 2) {\n    const dataItem_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", dataItem_r20.userStatus === \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r20.userStatus === \"Inactive\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 72);\n    i0.ɵɵlistener(\"valueChange\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r21 = i0.ɵɵrestoreView(_r21);\n      const filter_r23 = ctx_r21.$implicit;\n      const column_r24 = ctx_r21.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onStatusFilterChange($event, filter_r23, column_r24));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r23 = ctx.$implicit;\n    const column_r24 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"data\", ctx_r2.filterOptions)(\"value\", ctx_r2.getFilterValue(filter_r23, column_r24));\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 67);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_Template, 2, 2, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template, 1, 2, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 100)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userStatus\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"userStatus\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"span\", 74);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r25 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, dataItem_r25.lastUpdatedDate, \"MM/dd/yyyy hh:mm a\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(dataItem_r25.lastUpdatedByFullName);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 75);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r27 = ctx.column;\n    const filterService_r28 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r27)(\"filter\", filter_r26)(\"filterService\", filterService_r28);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 73);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_1_Template, 6, 5, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_2_Template, 7, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 160)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"maxResizableWidth\", 240)(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_Template, 2, 11, \"kendo-grid-column\", 46)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_Template, 3, 8, \"kendo-grid-column\", 47)(3, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_Template, 3, 7, \"kendo-grid-column\", 48)(4, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_Template, 3, 7, \"kendo-grid-column\", 49)(5, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_Template, 3, 7, \"kendo-grid-column\", 50)(6, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_Template, 3, 7, \"kendo-grid-column\", 51)(7, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_Template, 3, 8, \"kendo-grid-column\", 52);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r29 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"templateName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"emailTo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"emailSubject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"category\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"userStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"lastUpdatedDate\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_18_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵelement(2, \"i\", 79);\n    i0.ɵɵelementStart(3, \"p\", 24);\n    i0.ɵɵtext(4, \"No users found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_18_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 81);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_template_18_div_0_Template, 8, 0, \"div\", 76);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading === false && ctx_r2.serverSideRowData.length === 0);\n  }\n}\nexport let EmailTemplatesListComponent = /*#__PURE__*/(() => {\n  class EmailTemplatesListComponent {\n    usersService;\n    cdr;\n    router;\n    route;\n    modalService;\n    AppService;\n    customLayoutUtilsService;\n    httpUtilService;\n    emailTemplateService;\n    kendoColumnService;\n    grid;\n    // Data\n    serverSideRowData = [];\n    gridData = [];\n    IsListHasValue = false;\n    loading = false;\n    isLoading = false;\n    loginUser = {};\n    // Search\n    searchData = '';\n    searchTerms = new Subject();\n    searchSubscription;\n    // Enhanced Filters for Kendo UI\n    filter = {\n      logic: 'and',\n      filters: []\n    };\n    gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    activeFilters = [];\n    filterOptions = [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }];\n    // Advanced filter options\n    advancedFilterOptions = {\n      status: [{\n        text: 'All',\n        value: null\n      }, {\n        text: 'Active',\n        value: 'Active'\n      }, {\n        text: 'Inactive',\n        value: 'Inactive'\n      }],\n      roles: [],\n      // Will be populated from backend\n      categories: [] // Will be populated from backend\n    };\n    // Filter state\n    showAdvancedFilters = false;\n    appliedFilters = {};\n    // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\n    kendoHide;\n    hiddenData = [];\n    kendoColOrder = [];\n    kendoInitColOrder = [];\n    hiddenFields = [];\n    // Column configuration for the new system\n    gridColumns = [];\n    defaultColumns = [];\n    fixedColumns = [];\n    draggableColumns = [];\n    normalGrid;\n    expandedGrid;\n    isExpanded = false;\n    // Enhanced Columns with Kendo UI features\n    gridColumnConfig = [{\n      field: 'action',\n      title: 'Actions',\n      width: 80,\n      isFixed: true,\n      type: 'action',\n      order: 1\n    }, {\n      field: 'templateName',\n      title: 'Template Name',\n      width: 150,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2\n    },\n    // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\n    {\n      field: 'emailTo',\n      title: 'Email',\n      width: 250,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 4\n    }, {\n      field: 'emailSubject',\n      title: 'Email Subject',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5\n    }, {\n      field: 'emailBody',\n      title: 'Email Body',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6\n    }, {\n      field: 'category',\n      title: 'Category',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 7\n    }, {\n      field: 'templateStatus',\n      title: 'Template Status',\n      width: 100,\n      type: 'status',\n      isFixed: false,\n      filterable: true,\n      order: 8\n    }, {\n      field: 'lastUpdatedDate',\n      title: 'Updated Date',\n      width: 160,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 9\n    }];\n    // OLD SYSTEM - to be removed\n    columnsVisibility = {};\n    // Old column configuration management removed - replaced with new system\n    // State\n    sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    // Router subscription for saving state on navigation\n    routerSubscription;\n    // Storage key for state persistence\n    GRID_STATE_KEY = 'form-templates-grid-state';\n    // Pagination\n    page = {\n      size: 10,\n      pageNumber: 0,\n      totalElements: 0,\n      totalPages: 0,\n      orderBy: 'lastUpdatedDate',\n      orderDir: 'desc'\n    };\n    skip = 0;\n    // Export options\n    exportOptions = [{\n      text: 'Export All',\n      value: 'all'\n    }, {\n      text: 'Export Selected',\n      value: 'selected'\n    }, {\n      text: 'Export Filtered',\n      value: 'filtered'\n    }];\n    // Selection state\n    selectedUsers = [];\n    isAllSelected = false;\n    // Statistics\n    userStatistics = {\n      activeUsers: 0,\n      inactiveUsers: 0,\n      suspendedUsers: 0,\n      lockedUsers: 0,\n      totalUsers: 0\n    };\n    // Bulk operations\n    showBulkActions = false;\n    bulkActionStatus = 'Active';\n    //add or edit default paramters\n    permissionArray = [];\n    constructor(usersService, cdr, router, route, modalService,\n    // Provides modal functionality to display modals\n    AppService, customLayoutUtilsService, httpUtilService, emailTemplateService, kendoColumnService) {\n      this.usersService = usersService;\n      this.cdr = cdr;\n      this.router = router;\n      this.route = route;\n      this.modalService = modalService;\n      this.AppService = AppService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.httpUtilService = httpUtilService;\n      this.emailTemplateService = emailTemplateService;\n      this.kendoColumnService = kendoColumnService;\n    }\n    goBack() {\n      this.router.navigate(['/setting/view']);\n    }\n    ngOnInit() {\n      this.loginUser = this.AppService.getLoggedInUser();\n      console.log('Login user loaded:', this.loginUser);\n      // Setup search with debounce\n      this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        this.page.pageNumber = 0;\n        this.skip = 0;\n        this.loadTable();\n      });\n      // Subscribe to router events to save state before navigation\n      this.routerSubscription = this.router.events.subscribe(event => {\n        if (event instanceof NavigationStart) {\n          this.saveGridState();\n        }\n      });\n      // Load saved state if available\n      this.loadGridState();\n      // Load roles for advanced filters\n      this.loadRoles();\n      // Load user statistics\n      // this.loadUserStatistics();\n      // Initialize with default page load\n      this.onPageLoad();\n      // Initialize new column visibility system\n      this.initializeColumnVisibilitySystem();\n      // Load column configuration after a short delay to ensure loginUser is available\n      setTimeout(() => {\n        this.loadColumnConfigFromDatabase();\n      }, 100);\n    }\n    /**\n     * Initialize the new column visibility system\n     */\n    initializeColumnVisibilitySystem() {\n      // Initialize default columns\n      this.defaultColumns = this.gridColumnConfig.map(col => col.field);\n      this.gridColumns = [...this.defaultColumns];\n      // Set fixed columns (first 3 columns)\n      this.fixedColumns = ['action', 'FirstName', 'LastName'];\n      // Set draggable columns (all except fixed)\n      this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\n      // Initialize normal and expanded grid references\n      this.normalGrid = this.grid;\n      this.expandedGrid = this.grid;\n      // Load additional data after main data is loaded\n      setTimeout(() => {\n        this.loadCategories();\n        this.loadTemplateStatistics();\n      }, 100);\n    }\n    ngAfterViewInit() {\n      // Load the table after the view is initialized\n      // Small delay to ensure the grid is properly rendered\n      setTimeout(() => {\n        this.loadTable();\n      }, 200);\n    }\n    // Method to handle when the component becomes visible\n    onTabActivated() {\n      // Set loading state for tab activation\n      this.loading = true;\n      this.isLoading = true;\n      // Refresh the data when the tab is activated\n      this.loadTable();\n      this.loadTemplateStatistics();\n    }\n    // Method to handle initial page load\n    onPageLoad() {\n      // Initialize the component with default data\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.searchData = '';\n      // Load the data\n      this.loadTable();\n    }\n    // Refresh grid data - only refresh the grid with latest API call\n    refreshGrid() {\n      // Set loading state to show full-screen loader\n      this.loading = true;\n      this.isLoading = true;\n      // Reset to first page and clear any applied filters\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.gridFilter = {\n        logic: 'and',\n        filters: []\n      };\n      this.activeFilters = [];\n      this.appliedFilters = {};\n      // Clear search data\n      this.searchData = '';\n      // Load fresh data from API\n      this.loadTable();\n    }\n    ngOnDestroy() {\n      // Clean up subscriptions\n      if (this.routerSubscription) {\n        this.routerSubscription.unsubscribe();\n      }\n      if (this.searchSubscription) {\n        this.searchSubscription.unsubscribe();\n      }\n      this.searchTerms.complete();\n    }\n    // New method to load data using Kendo UI specific endpoint\n    loadTableWithKendoEndpoint() {\n      this.loading = true;\n      this.isLoading = true;\n      // Enable loader\n      this.httpUtilService.loadingSubject.next(true);\n      // Prepare state object for Kendo UI endpoint\n      const state = {\n        take: this.page.size,\n        skip: this.skip,\n        sort: this.sort,\n        filter: this.filter,\n        search: this.searchData,\n        loggedInUserId: this.loginUser.userId\n      };\n      this.emailTemplateService.getEmailTemplatesForKendoGrid(state).subscribe({\n        next: data => {\n          // Handle the new API response structure\n          if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n            const errors = data.responseData?.errors || data.errors || [];\n            console.error('Kendo UI Grid errors:', errors);\n            this.handleEmptyResponse();\n          } else {\n            // Handle both old and new response structures\n            const responseData = data.responseData || data;\n            const userData = responseData.data || [];\n            const total = responseData.total || 0;\n            this.IsListHasValue = userData.length !== 0;\n            this.serverSideRowData = userData;\n            this.gridData = this.serverSideRowData;\n            console.log('this.gridData', this.gridData);\n            this.page.totalElements = total;\n            this.page.totalPages = Math.ceil(total / this.page.size);\n          }\n          this.httpUtilService.loadingSubject.next(false);\n        },\n        error: error => {\n          console.error('Error loading data with Kendo UI endpoint:', error);\n          this.handleEmptyResponse();\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        },\n        complete: () => {\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.detectChanges();\n        }\n      });\n    }\n    // Enhanced loadTable method that can use either endpoint\n    loadTable() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        // Use the new Kendo UI specific endpoint for better performance\n        _this.loadTableWithKendoEndpoint();\n      })();\n    }\n    handleEmptyResponse() {\n      this.loading = false;\n      this.isLoading = false;\n      this.httpUtilService.loadingSubject.next(false);\n      this.IsListHasValue = false;\n      this.serverSideRowData = [];\n      this.gridData = [];\n      this.page.totalElements = 0;\n      this.page.totalPages = 0;\n      this.cdr.detectChanges();\n    }\n    // Enhanced search handling\n    clearSearch() {\n      if (this.searchData === '') {\n        this.searchTerms.next('');\n      }\n    }\n    onSearchChange() {\n      this.searchTerms.next(this.searchData || '');\n    }\n    // Clear all filters and search\n    clearAllFilters() {\n      this.searchData = '';\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.gridFilter = {\n        logic: 'and',\n        filters: []\n      };\n      this.activeFilters = [];\n      this.appliedFilters = {};\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.loadTable();\n    }\n    // Apply advanced filters\n    applyAdvancedFilters() {\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.loadTable();\n    }\n    // Toggle advanced filters panel\n    toggleAdvancedFilters() {\n      this.showAdvancedFilters = !this.showAdvancedFilters;\n    }\n    // Load roles for advanced filters\n    loadRoles() {\n      const queryParams = {\n        pageSize: 1000,\n        sortOrder: 'ASC',\n        sortField: 'roleName',\n        pageNumber: 0\n      };\n      this.usersService.getAllRoles(queryParams).subscribe({\n        next: data => {\n          if (data && data.responseData && data.responseData.content) {\n            this.advancedFilterOptions.roles = [{\n              text: 'All Roles',\n              value: null\n            }, ...data.responseData.content.map(role => ({\n              text: role.roleName,\n              value: role.roleName\n            }))];\n          } else {\n            // Set default if no data\n            this.advancedFilterOptions.categories = [{\n              text: 'All Categories',\n              value: null\n            }];\n          }\n        },\n        error: error => {\n          console.error('Error loading roles:', error);\n          // Set default roles if loading fails\n          this.advancedFilterOptions.roles = [{\n            text: 'All Roles',\n            value: null\n          }];\n        }\n      });\n      this.usersService.getDefaultPermissions({}).subscribe(permissions => {\n        this.permissionArray = permissions.responseData;\n      });\n    }\n    // Load user statistics\n    loadUserStatistics() {\n      this.usersService.getUserStatistics().subscribe({\n        next: data => {\n          if (data && data.statistics) {\n            this.userStatistics = data.statistics;\n          }\n        },\n        error: error => {\n          console.error('Error loading user statistics:', error);\n        }\n      });\n    }\n    // Selection handling\n    onSelectionChange(selection) {\n      this.selectedUsers = selection.selectedRows || [];\n      this.isAllSelected = this.selectedUsers.length === this.serverSideRowData.length;\n      this.showBulkActions = this.selectedUsers.length > 0;\n    }\n    // Select all users\n    selectAllUsers() {\n      if (this.isAllSelected) {\n        this.selectedUsers = [];\n        this.isAllSelected = false;\n      } else {\n        this.selectedUsers = [...this.serverSideRowData];\n        this.isAllSelected = true;\n      }\n      this.showBulkActions = this.selectedUsers.length > 0;\n    }\n    // Delete user\n    deleteUser(user) {\n      if (confirm(`Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`)) {\n        // Show loading state\n        this.loading = true;\n        this.isLoading = true;\n        const deleteData = {\n          userId: user.userId,\n          loggedInUserId: this.loginUser.userId || 0\n        };\n        this.usersService.deleteUser(deleteData).subscribe({\n          next: response => {\n            if (response && response.message) {\n              //alert(response.message);\n              this.customLayoutUtilsService.showSuccess(response.message, '');\n              this.loadTable(); // Reload the table\n              // this.loadUserStatistics(); // Reload statistics\n            }\n          },\n          error: error => {\n            console.error('Error deleting user:', error);\n            this.customLayoutUtilsService.showError('Error deleting user', '');\n            //alert('Error deleting user. Please try again.');\n            // Reset loading state on error\n            this.loading = false;\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    // Bulk update user status\n    bulkUpdateUserStatus() {\n      if (this.selectedUsers.length === 0) {\n        this.customLayoutUtilsService.showError('Please select users to update', '');\n        //alert('Please select users to update.');\n        return;\n      }\n      if (confirm(`Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`)) {\n        // Show loading state\n        this.loading = true;\n        this.isLoading = true;\n        const bulkUpdateData = {\n          userIds: this.selectedUsers.map(user => user.userId),\n          status: this.bulkActionStatus,\n          loggedInUserId: this.loginUser.userId || 0\n        };\n        this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\n          next: response => {\n            if (response && response.message) {\n              //alert(response.message);\n              this.customLayoutUtilsService.showSuccess(response.message, '');\n              this.loadTable(); // Reload the table\n              // this.loadUserStatistics(); // Reload statistics\n              this.selectedUsers = []; // Clear selection\n              this.showBulkActions = false;\n            }\n          },\n          error: error => {\n            console.error('Error updating users:', error);\n            //alert('Error updating users. Please try again.');\n            this.customLayoutUtilsService.showError('Error updating users', '');\n            // Reset loading state on error\n            this.loading = false;\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    // Unlock user\n    unlockUser(user) {\n      if (confirm(`Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`)) {\n        // Show loading state\n        this.loading = true;\n        this.isLoading = true;\n        const unlockData = {\n          userId: user.userId,\n          loggedInUserId: this.loginUser.userId || 0\n        };\n        this.usersService.unlockUser(unlockData).subscribe({\n          next: response => {\n            if (response && response.message) {\n              //alert(response.message);\n              this.customLayoutUtilsService.showSuccess(response.message, '');\n              this.loadTable(); // Reload the table\n              // this.loadUserStatistics(); // Reload statistics\n            }\n          },\n          error: error => {\n            console.error('Error unlocking user:', error);\n            this.customLayoutUtilsService.showSuccess('Error unlocking user. Please try again', '');\n            //alert('Error unlocking user. Please try again.');\n            // Reset loading state on error\n            this.loading = false;\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    onSearchKeyDown(event) {\n      if (event.key === 'Enter') {\n        this.searchTerms.next(this.searchData);\n      }\n    }\n    // Enhanced function to filter data from search and advanced filters\n    filterConfiguration() {\n      let filter = {\n        paginate: true,\n        search: '',\n        columnFilter: []\n      };\n      // Handle search text\n      let searchText;\n      if (this.searchData === null || this.searchData === undefined) {\n        searchText = '';\n      } else {\n        searchText = this.searchData;\n      }\n      filter.search = searchText.trim();\n      // Handle Kendo UI grid filters\n      if (this.activeFilters && this.activeFilters.length > 0) {\n        filter.columnFilter = [...this.activeFilters];\n      }\n      // Add advanced filters\n      if (this.appliedFilters.status && this.appliedFilters.status !== null) {\n        filter.columnFilter.push({\n          field: 'userStatus',\n          operator: 'eq',\n          value: this.appliedFilters.status\n        });\n      }\n      if (this.appliedFilters.role && this.appliedFilters.role !== null) {\n        filter.columnFilter.push({\n          field: 'roleName',\n          operator: 'eq',\n          value: this.appliedFilters.role\n        });\n      }\n      return filter;\n    }\n    // Grid event handlers\n    pageChange(event) {\n      this.skip = event.skip;\n      this.page.pageNumber = event.skip / event.take;\n      this.page.size = event.take;\n      this.loadTable();\n    }\n    onSortChange(sort) {\n      console.log('Sort change triggered:', sort);\n      // Handle empty sort array (normalize/unsort case)\n      const incomingSort = Array.isArray(sort) ? sort : [];\n      if (incomingSort.length === 0) {\n        // Normalize/unsort case - return to default sorting\n        console.log('Normalize triggered - returning to default sort');\n        this.sort = [{\n          field: 'lastUpdatedDate',\n          dir: 'desc'\n        }];\n        this.page.orderBy = 'lastUpdatedDate';\n        this.page.orderDir = 'desc';\n        // Reset the grid's sort state to default\n        if (this.grid) {\n          this.grid.sort = [{\n            field: 'lastUpdatedDate',\n            dir: 'desc'\n          }];\n        }\n      } else {\n        // Normal sorting case\n        this.sort = incomingSort;\n        this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n        this.page.orderDir = this.sort[0].dir || 'desc';\n      }\n      console.log('Final sort state:', this.sort);\n      console.log('Page order:', {\n        orderBy: this.page.orderBy,\n        orderDir: this.page.orderDir\n      });\n      this.loadTable();\n    }\n    filterChange(filter) {\n      this.filter = filter;\n      this.gridFilter = filter;\n      this.activeFilters = this.flattenFilters(filter);\n      this.page.pageNumber = 0;\n      this.saveGridState();\n      // Set loading state for sorting\n      this.loading = true;\n      this.isLoading = true;\n      this.skip = 0;\n      this.loadTable();\n    }\n    // Old column visibility methods removed - replaced with new system\n    // Fix 2: More robust getFilterValue method\n    getFilterValue(filter, column) {\n      if (!filter || !filter.filters || !column) {\n        return null;\n      }\n      const predicate = filter.filters.find(f => f && 'field' in f && f.field === column.field);\n      return predicate && 'value' in predicate ? predicate.value : null;\n    }\n    // Fix 3: More robust onStatusFilterChange method\n    onStatusFilterChange(value, filter, column) {\n      if (!filter || !filter.filters || !column) {\n        console.error('Invalid filter or column:', {\n          filter,\n          column\n        });\n        return;\n      }\n      const exists = filter.filters.findIndex(f => f && 'field' in f && f.field === column.field);\n      if (exists > -1) {\n        filter.filters.splice(exists, 1);\n      }\n      if (value !== null) {\n        filter.filters.push({\n          field: column.field,\n          operator: 'eq',\n          value: value\n        });\n      }\n      this.filterChange(filter);\n    }\n    // Fix 4: More robust flattenFilters method\n    flattenFilters(filter) {\n      const filters = [];\n      if (!filter || !filter.filters) {\n        return filters;\n      }\n      filter.filters.forEach(f => {\n        if (f && 'field' in f) {\n          // It's a FilterDescriptor\n          filters.push({\n            field: f.field,\n            operator: f.operator,\n            value: f.value\n          });\n        } else if (f && 'filters' in f) {\n          // It's a CompositeFilterDescriptor\n          filters.push(...this.flattenFilters(f));\n        }\n      });\n      return filters;\n    }\n    // Fix 5: More robust loadGridState method\n    loadGridState() {\n      try {\n        const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n        if (!savedState) {\n          return;\n        }\n        const state = JSON.parse(savedState);\n        // Restore sort state\n        if (state && state.sort) {\n          this.sort = state.sort;\n          if (this.sort && this.sort.length > 0 && this.sort[0]) {\n            this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n            this.page.orderDir = this.sort[0].dir || 'desc';\n          }\n        }\n        // Restore filter state\n        if (state && state.filter) {\n          this.filter = state.filter;\n          this.gridFilter = state.filter;\n          this.activeFilters = state.activeFilters || [];\n        }\n        // Restore pagination state\n        if (state && state.page) {\n          this.page = state.page;\n        }\n        if (state && state.skip !== undefined) {\n          this.skip = state.skip;\n        }\n        // Restore column visibility\n        if (state && state.columnsVisibility) {\n          this.columnsVisibility = state.columnsVisibility;\n        }\n        // Restore search state\n        if (state && state.searchData) {\n          this.searchData = state.searchData;\n        }\n        // Restore advanced filter states\n        if (state && state.appliedFilters) {\n          this.appliedFilters = state.appliedFilters;\n        }\n        if (state && state.showAdvancedFilters !== undefined) {\n          this.showAdvancedFilters = state.showAdvancedFilters;\n        }\n      } catch (error) {\n        console.error('Error loading grid state:', error);\n        // If there's an error, use default state\n      }\n    }\n    /**\n     * Reset the current state of column visibility and order in the grid to its original state.\n     */\n    resetTable() {\n      // Check if loginUser is available\n      if (!this.loginUser || !this.loginUser.userId) {\n        console.error('loginUser not available:', this.loginUser);\n        this.customLayoutUtilsService.showError('User not logged in. Please refresh the page and try again.', '');\n        return;\n      }\n      // Reset all grid state to default\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.searchData = '';\n      this.appliedFilters = {};\n      this.showAdvancedFilters = false;\n      // Reset column visibility and order\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          const index = this.gridColumns.indexOf(column.field);\n          if (index !== -1) {\n            column.orderIndex = index;\n          }\n          // Reset column visibility - show all columns\n          if (column.field && column.field !== 'action') {\n            column.hidden = false;\n          }\n        });\n      }\n      // Clear hidden columns\n      this.hiddenData = [];\n      this.kendoColOrder = [];\n      this.hiddenFields = [];\n      // Reset the Kendo Grid's internal state\n      if (this.grid) {\n        // Clear all filters\n        this.grid.filter = {\n          logic: 'and',\n          filters: []\n        };\n        // Reset sorting\n        this.grid.sort = [{\n          field: 'lastUpdatedDate',\n          dir: 'desc'\n        }];\n        // Reset to first page\n        this.grid.skip = 0;\n        this.grid.pageSize = this.page.size;\n      }\n      // Prepare reset data\n      const userData = {\n        pageName: 'EmailTemplates',\n        userID: this.loginUser.userId,\n        hiddenData: [],\n        kendoColOrder: [],\n        LoggedId: this.loginUser.userId\n      };\n      // Show loading state\n      this.httpUtilService.loadingSubject.next(true);\n      // Save reset state to backend\n      this.kendoColumnService.createHideFields(userData).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!res.isFault) {\n            // Also clear from localStorage\n            this.kendoColumnService.clearFromLocalStorage('EmailTemplates');\n            this.customLayoutUtilsService.showSuccess(res.message || 'Column settings reset successfully.', '');\n          } else {\n            this.customLayoutUtilsService.showError(res.message || 'Failed to reset column settings.', '');\n          }\n          // Trigger change detection and refresh grid\n          this.cdr.detectChanges();\n          // Small delay to ensure the grid is updated\n          setTimeout(() => {\n            if (this.grid) {\n              this.grid.refresh();\n            }\n          }, 100);\n          this.loadTable();\n        },\n        error: error => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('Error resetting column settings:', error);\n          // Check if it's an authentication error\n          if (error.status === 401 || error.error && error.error.status === 401) {\n            this.customLayoutUtilsService.showError('Authentication failed. Please login again.', '');\n            // Optionally redirect to login page\n          } else {\n            this.customLayoutUtilsService.showError('Error resetting column settings. Please try again.', '');\n          }\n        }\n      });\n    }\n    // Grid state persistence methods\n    saveGridState() {\n      const state = {\n        sort: this.sort,\n        filter: this.filter,\n        page: this.page,\n        skip: this.skip,\n        columnsVisibility: this.columnsVisibility,\n        searchData: this.searchData,\n        activeFilters: this.activeFilters,\n        appliedFilters: this.appliedFilters,\n        showAdvancedFilters: this.showAdvancedFilters\n      };\n      localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n    }\n    // Function to add a new company (calls edit function with ID 0)\n    add() {\n      this.edit(0);\n    }\n    // Function to open the edit modal for adding/editing a company\n    edit(id) {\n      console.log('Line: 413', 'call edit function: ', id);\n      // Configuration options for the modal dialog\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Open the modal and load the AddCompaniesComponent\n      const modalRef = this.modalService.open(EmailTemplatesEditComponent, NgbModalOptions);\n      // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n      modalRef.componentInstance.id = id;\n      // modalRef.componentInstance.defaultPermissions = this.permissionArray;\n      // // Subscribe to the modal event when data is updated\n      // modalRef.componentInstance.passEntry.subscribe((receivedEntry: boolean) => {\n      //   if (receivedEntry === true) {\n      //     // Reload the table data after a successful update\n      //     this.loadTable();\n      //   }\n      // });\n    }\n    // Delete functionality removed\n    // Load categories for advanced filters\n    loadCategories() {\n      // Implementation for loading categories\n      console.log('Loading categories...');\n    }\n    // Load template statistics\n    loadTemplateStatistics() {\n      // Implementation for loading template statistics\n      console.log('Loading template statistics...');\n    }\n    // Setup search subscription\n    setupSearchSubscription() {\n      this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n        this.page.pageNumber = 0;\n        this.skip = 0;\n        this.loadTable();\n      });\n    }\n    toggleExpand() {\n      // Find grid container element and toggle fullscreen class\n      const gridContainer = document.querySelector('.grid-container');\n      if (gridContainer) {\n        gridContainer.classList.toggle('fullscreen-grid');\n        this.isExpanded = !this.isExpanded;\n        // Refresh grid after resize to ensure proper rendering\n        if (this.grid) {\n          this.grid.refresh();\n        }\n      }\n    }\n    // Enhanced export functionality\n    onExportClick(event) {\n      switch (event.item.value) {\n        case 'all':\n          this.exportAllUsers();\n          break;\n        case 'selected':\n          this.exportSelectedUsers();\n          break;\n        case 'filtered':\n          this.exportFilteredUsers();\n          break;\n        default:\n          console.warn('Unknown export option:', event.item.value);\n      }\n    }\n    exportAllUsers() {\n      const exportParams = {\n        filters: {},\n        format: 'excel'\n      };\n      this.usersService.exportUsers(exportParams).subscribe({\n        next: response => {\n          if (response && response.exportData) {\n            this.downloadExcel(response.exportData, 'All_Users');\n          }\n        },\n        error: error => {\n          console.error('Error exporting users:', error);\n          //alert('Error exporting users. Please try again.');\n        }\n      });\n    }\n    exportSelectedUsers() {\n      if (this.selectedUsers.length === 0) {\n        //alert('Please select users to export.');\n        return;\n      }\n      const exportParams = {\n        filters: {\n          userIds: this.selectedUsers.map(user => user.UserId)\n        },\n        format: 'excel'\n      };\n      this.usersService.exportUsers(exportParams).subscribe({\n        next: response => {\n          if (response && response.exportData) {\n            this.downloadExcel(response.exportData, 'Selected_Users');\n          }\n        },\n        error: error => {\n          console.error('Error exporting selected users:', error);\n          //alert('Error exporting selected users. Please try again.');\n        }\n      });\n    }\n    exportFilteredUsers() {\n      const exportParams = {\n        filters: {\n          status: this.appliedFilters.status,\n          role: this.appliedFilters.role,\n          searchTerm: this.searchData\n        },\n        format: 'excel'\n      };\n      this.usersService.exportUsers(exportParams).subscribe({\n        next: response => {\n          if (response && response.exportData) {\n            this.downloadExcel(response.exportData, 'Filtered_Users');\n          }\n        },\n        error: error => {\n          console.error('Error exporting filtered users:', error);\n          //alert('Error exporting filtered users. Please try again.');\n        }\n      });\n    }\n    downloadExcel(data, filename) {\n      // This would typically use a library like xlsx or similar\n      // For now, we'll create a simple CSV download\n      const csvContent = this.convertToCSV(data);\n      const blob = new Blob([csvContent], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    convertToCSV(data) {\n      if (data.length === 0) return '';\n      const headers = Object.keys(data[0]);\n      const csvRows = [headers.join(',')];\n      for (const row of data) {\n        const values = headers.map(header => {\n          const value = row[header];\n          return typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\n        });\n        csvRows.push(values.join(','));\n      }\n      return csvRows.join('\\n');\n    }\n    // NEW COLUMN VISIBILITY SYSTEM METHODS\n    /**\n     * Saves the current state of column visibility and order in the grid.\n     * This function categorizes columns into visible and hidden columns, records their titles,\n     * fields, and visibility status, and also captures the order of draggable columns.\n     * After gathering the necessary data, it sends this information to the backend for saving.\n     */\n    saveHead() {\n      // Check if loginUser is available\n      if (!this.loginUser || !this.loginUser.userId) {\n        console.error('loginUser not available:', this.loginUser);\n        this.customLayoutUtilsService.showError('User not logged in. Please refresh the page.', '');\n        return;\n      }\n      const nonHiddenColumns = [];\n      const hiddenColumns = [];\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          if (!column.hidden) {\n            const columnData = {\n              title: column.title,\n              field: column.field,\n              hidden: column.hidden\n            };\n            nonHiddenColumns.push(columnData);\n          } else {\n            const columnData = {\n              title: column.title,\n              field: column.field,\n              hidden: column.hidden\n            };\n            hiddenColumns.push(columnData);\n          }\n        });\n      }\n      const draggableColumnsOrder = this.gridColumns.filter(col => !this.fixedColumns.includes(col)).map((field, index) => ({\n        field,\n        orderIndex: index\n      }));\n      // Prepare data for backend\n      const userData = {\n        pageName: 'Users',\n        userID: this.loginUser.userId,\n        hiddenData: hiddenColumns,\n        kendoColOrder: draggableColumnsOrder,\n        LoggedId: this.loginUser.userId\n      };\n      // Show loading state\n      this.httpUtilService.loadingSubject.next(true);\n      // Save to backend\n      this.kendoColumnService.createHideFields(userData).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!res.isFault) {\n            // Update local state\n            this.hiddenData = hiddenColumns;\n            this.kendoColOrder = draggableColumnsOrder;\n            this.hiddenFields = this.hiddenData.map(col => col.field);\n            // Also save to localStorage as backup\n            this.kendoColumnService.saveToLocalStorage(userData);\n            this.customLayoutUtilsService.showSuccess(res.message || 'Column settings saved successfully.', '');\n          } else {\n            this.customLayoutUtilsService.showError(res.message || 'Failed to save column settings.', '');\n          }\n          this.cdr.markForCheck();\n        },\n        error: error => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('Error saving column settings:', error);\n          // Fallback to localStorage on error\n          this.kendoColumnService.saveToLocalStorage(userData);\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          this.customLayoutUtilsService.showError('Failed to save to server. Settings saved locally.', '');\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    /**\n     * Loads and applies the saved column order from the user preferences or configuration.\n     * This function updates the grid column order, ensuring the fixed columns remain in place\n     * and the draggable columns are ordered according to the saved preferences.\n     */\n    loadSavedColumnOrder(kendoColOrder) {\n      try {\n        const savedOrder = kendoColOrder;\n        if (savedOrder) {\n          const parsedOrder = savedOrder;\n          if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n            // Get only the draggable columns from saved order\n            const savedDraggableColumns = parsedOrder.sort((a, b) => a.orderIndex - b.orderIndex).map(col => col.field).filter(field => !this.fixedColumns.includes(field));\n            // Add any missing draggable columns at the end\n            const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\n            // Combine fixed columns with saved draggable columns\n            this.gridColumns = [...this.fixedColumns, ...savedDraggableColumns, ...missingColumns];\n          } else {\n            this.gridColumns = [...this.defaultColumns];\n          }\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } catch (error) {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    }\n    /**\n     * Checks if a given column is marked as hidden.\n     * This function searches the `hiddenFields` array to determine if the column should be hidden.\n     */\n    getHiddenField(columnName) {\n      return this.hiddenFields.indexOf(columnName) > -1;\n    }\n    /**\n     * Handles the column reordering event triggered when a column is moved by the user.\n     * The function checks if the column being moved is in the fixed columns and prevents reordering\n     * of fixed columns.\n     */\n    onColumnReorder(event) {\n      const {\n        columns,\n        newIndex,\n        oldIndex\n      } = event;\n      // Prevent reordering of fixed columns\n      if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\n        return;\n      }\n      // Update the gridColumns array\n      const reorderedColumns = [...this.gridColumns];\n      const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n      reorderedColumns.splice(newIndex, 0, movedColumn);\n      this.gridColumns = reorderedColumns;\n      this.cdr.markForCheck();\n    }\n    /**\n     * Handles column visibility changes from the Kendo Grid.\n     * Updates the local state when columns are shown or hidden.\n     */\n    updateColumnVisibility(event) {\n      if (this.isExpanded === false) {\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach(column => {\n            const columnData = {\n              title: column.title,\n              field: column.field,\n              hidden: column.hidden\n            };\n            if (column.hidden) {\n              const exists = this.hiddenData.some(item => item.field === columnData.field && item.hidden === true);\n              if (!exists) {\n                this.hiddenData.push(columnData);\n              }\n            } else {\n              let indexExists = this.hiddenData.findIndex(item => item.field === columnData.field && item.hidden === true);\n              if (indexExists !== -1) {\n                this.hiddenData.splice(indexExists, 1);\n              }\n            }\n          });\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          this.cdr.markForCheck();\n        }\n      }\n    }\n    /**\n     * Loads the saved column configuration from the backend or localStorage as fallback.\n     * This method is called during component initialization to restore user preferences.\n     */\n    loadColumnConfigFromDatabase() {\n      try {\n        // First try to load from backend\n        if (this.loginUser && this.loginUser.userId) {\n          this.kendoColumnService.getHideFields({\n            pageName: 'Users',\n            userID: this.loginUser.userId\n          }).subscribe({\n            next: res => {\n              if (!res.isFault && res.Data) {\n                this.kendoHide = res.Data;\n                this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\n                this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\n                this.hiddenFields = this.hiddenData.map(col => col.field);\n                // Update grid columns based on the hidden fields\n                if (this.grid && this.grid.columns) {\n                  this.grid.columns.forEach(column => {\n                    if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                      column.includeInChooser = true;\n                      column.hidden = true;\n                    } else {\n                      column.hidden = false;\n                    }\n                  });\n                }\n                // Load saved column order and update grid\n                this.loadSavedColumnOrder(this.kendoInitColOrder);\n                // Also save to localStorage as backup\n                this.kendoColumnService.saveToLocalStorage({\n                  pageName: 'Users',\n                  userID: this.loginUser.userId,\n                  hiddenData: this.hiddenData,\n                  kendoColOrder: this.kendoInitColOrder\n                });\n              }\n            },\n            error: error => {\n              console.error('Error loading from backend, falling back to localStorage:', error);\n              this.loadFromLocalStorageFallback();\n            }\n          });\n        } else {\n          // Fallback to localStorage if no user ID\n          this.loadFromLocalStorageFallback();\n        }\n      } catch (error) {\n        console.error('Error loading column configuration:', error);\n        this.loadFromLocalStorageFallback();\n      }\n    }\n    /**\n     * Fallback method to load column configuration from localStorage\n     */\n    loadFromLocalStorageFallback() {\n      try {\n        const savedConfig = this.kendoColumnService.getFromLocalStorage('Users', this.loginUser?.UserId || 0);\n        if (savedConfig) {\n          this.kendoHide = savedConfig;\n          this.hiddenData = savedConfig.hiddenData || [];\n          this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          // Update grid columns based on the hidden fields\n          if (this.grid && this.grid.columns) {\n            this.grid.columns.forEach(column => {\n              if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                column.includeInChooser = true;\n                column.hidden = true;\n              } else {\n                column.hidden = false;\n              }\n            });\n          }\n          // Load saved column order and update grid\n          this.loadSavedColumnOrder(this.kendoInitColOrder);\n        }\n      } catch (error) {\n        console.error('Error loading from localStorage fallback:', error);\n      }\n    }\n    static ɵfac = function EmailTemplatesListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EmailTemplatesListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.EmailTemplateService), i0.ɵɵdirectiveInject(i8.KendoColumnService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EmailTemplatesListComponent,\n      selectors: [[\"app-email-templates-list\"]],\n      viewQuery: function EmailTemplatesListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      decls: 19,\n      vars: 22,\n      consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"card\", \"mb-5\", \"mb-xl-5\"], [1, \"card-body\", \"pb-0\", \"pt-0\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-3x\", \"border-transparent\", \"fs-5\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", \"active\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"button\", \"title\", \"Go back to Settings Dashboard\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", \"back-button\", 3, \"click\"], [1, \"fa\", \"fa-arrow-left\", \"me-2\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\", \"loading\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"clear\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"type\", \"button\", \"title\", \"Refresh\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Role\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"templateName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"emailTo\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"emailSubject\", \"title\", \"Email Subject\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"category\", \"title\", \"category\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [\"title\", \"Edit\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-primary\", 3, \"inlineSVG\"], [\"title\", \"Unlock\", \"class\", \"btn btn-icon btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Unlock\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-warning\", 3, \"inlineSVG\"], [\"field\", \"templateName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bolder\", \"cursor-pointer\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"emailTo\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"emailSubject\", \"title\", \"Email Subject\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"category\", \"title\", \"category\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"ngbTooltip\", \"Active\", \"class\", \"svg-icon svg-icon-3 svg-icon-success\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Inactive\", \"class\", \"svg-icon svg-icon-3 svg-icon-danger text-danger\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Active\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-success\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"ngbTooltip\", \"Inactive\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", \"text-danger\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"valueChange\", \"data\", \"value\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\"], [1, \"text-gray-600\", \"fs-1r\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"fas\", \"fa-users\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n      template: function EmailTemplatesListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, EmailTemplatesListComponent_div_0_Template, 7, 0, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"ul\", 5)(5, \"li\", 6)(6, \"a\", 7);\n          i0.ɵɵtext(7, \" Email Templates \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_Template_button_click_9_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵelement(10, \"i\", 10);\n          i0.ɵɵtext(11, \" Back \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"kendo-grid\", 12, 0);\n          i0.ɵɵlistener(\"columnReorder\", function EmailTemplatesListComponent_Template_kendo_grid_columnReorder_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          })(\"selectionChange\", function EmailTemplatesListComponent_Template_kendo_grid_selectionChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChange($event));\n          })(\"filterChange\", function EmailTemplatesListComponent_Template_kendo_grid_filterChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterChange($event));\n          })(\"pageChange\", function EmailTemplatesListComponent_Template_kendo_grid_pageChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChange($event));\n          })(\"sortChange\", function EmailTemplatesListComponent_Template_kendo_grid_sortChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSortChange($event));\n          })(\"columnVisibilityChange\", function EmailTemplatesListComponent_Template_kendo_grid_columnVisibilityChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n          });\n          i0.ɵɵtemplate(15, EmailTemplatesListComponent_ng_template_15_Template, 17, 10, \"ng-template\", 13)(16, EmailTemplatesListComponent_ng_template_16_Template, 1, 1, \"ng-template\", 13)(17, EmailTemplatesListComponent_ng_container_17_Template, 8, 7, \"ng-container\", 14)(18, EmailTemplatesListComponent_ng_template_18_Template, 1, 1, \"ng-template\", 15);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"data\", ctx.serverSideRowData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(17, _c2, i0.ɵɵpureFunction0(16, _c1)))(\"sortable\", i0.ɵɵpureFunction0(19, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(20, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.page.pageNumber * ctx.page.size)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(21, _c5))(\"loading\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n        }\n      },\n      dependencies: [i9.NgForOf, i9.NgIf, i10.NgControlStatus, i10.NgModel, i3.NgbTooltip, i11.GridComponent, i11.ToolbarTemplateDirective, i11.GridSpacerComponent, i11.ColumnComponent, i11.CellTemplateDirective, i11.NoRecordsTemplateDirective, i11.ContainsFilterOperatorComponent, i11.EndsWithFilterOperatorComponent, i11.EqualFilterOperatorComponent, i11.NotEqualFilterOperatorComponent, i11.StartsWithFilterOperatorComponent, i11.AfterFilterOperatorComponent, i11.AfterEqFilterOperatorComponent, i11.BeforeEqFilterOperatorComponent, i11.BeforeFilterOperatorComponent, i11.StringFilterMenuComponent, i11.FilterMenuTemplateDirective, i11.DateFilterMenuComponent, i12.TextBoxComponent, i13.ButtonComponent, i14.InlineSVGDirective, i15.DropDownListComponent, i9.DatePipe],\n      styles: [\".nav-line-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{cursor:pointer;transition:all .3s ease}.nav-line-tabs[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]{color:#009ef7!important;border-bottom:2px solid #009ef7;font-weight:600}.nav-line-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover{color:#009ef7!important}.tab-content[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.grid-container[_ngcontent-%COMP%]{padding:20px;display:flex;flex-direction:column;height:100%;position:relative}.fullscreen-loading-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#000000b3;display:flex;align-items:center;justify-content:center;z-index:9999}.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{border-radius:.375rem;padding:.5rem .75rem;width:80%;border:2px solid #646367;box-shadow:0 0 6px #393a3a80}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{border-radius:.375rem;padding:.75rem 1.25rem;min-width:120px;background-color:#4c4e4f;color:#fff;font-weight:500;transition:background .3s,transform .2s}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{background-color:#4c4e4f;transform:scale(1.05)}.loading-content[_ngcontent-%COMP%]{text-align:center;color:#3699ff;padding:40px;min-width:200px}.grid-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:500}.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{width:300px}.grid-toolbar[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:center;margin-bottom:10px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:8px 16px;border-radius:6px;font-weight:500;transition:all .2s ease}.advanced-filters-panel[_ngcontent-%COMP%], .template-statistics[_ngcontent-%COMP%]{background-color:#f8f9fa;border:1px solid #dee2e6;border-radius:8px;margin-bottom:20px}.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{padding:15px}.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;margin-bottom:5px}.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{border-radius:6px}.card-body[_ngcontent-%COMP%] > .d-flex.justify-content-between.align-items-center[_ngcontent-%COMP%]{align-items:center}.back-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;align-self:center;padding:.15rem .5rem;border-radius:.55rem;font-weight:600;font-size:.8rem;line-height:1;margin-top:.1rem}.back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.badge[_ngcontent-%COMP%]{padding:6px 12px;border-radius:20px;font-size:.75rem;font-weight:500}.badge-success[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724}.badge-danger[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24}.badge-info[_ngcontent-%COMP%]{background-color:#d1ecf1;color:#0c5460}.badge-secondary[_ngcontent-%COMP%]{background-color:#e2e3e5;color:#383d41}.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:4px 8px;font-size:.875rem;border-radius:4px}.custom-no-records[_ngcontent-%COMP%]{padding:40px;text-align:center}.custom-no-records[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{width:2rem;height:2rem}.bulk-actions[_ngcontent-%COMP%]{background-color:#fff3cd;border:1px solid #ffeaa7;border-radius:6px;padding:10px;margin-bottom:15px}.fullscreen-grid[_ngcontent-%COMP%]{position:fixed;inset:0;z-index:9999;background:#fff;padding:20px;overflow:auto}\"]\n    });\n  }\n  return EmailTemplatesListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}