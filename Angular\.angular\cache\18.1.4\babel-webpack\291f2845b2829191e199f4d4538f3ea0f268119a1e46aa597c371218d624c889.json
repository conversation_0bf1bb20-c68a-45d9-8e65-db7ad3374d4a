{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./core/layout-init.service\";\nimport * as i2 from \"./core/layout.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../modules/services/http-utils.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../partials/layout/extras/scroll-top/scroll-top.component\";\nimport * as i7 from \"../partials/layout/modals/invite-users-modal/invite-users-modal.component\";\nimport * as i8 from \"../partials/layout/modals/main-modal/main-modal.component\";\nimport * as i9 from \"../partials/layout/modals/upgrade-plan-modal/upgrade-plan-modal.component\";\nimport * as i10 from \"../partials/layout/drawers/activity-drawer/activity-drawer.component\";\nimport * as i11 from \"../partials/layout/drawers/messenger-drawer/messenger-drawer.component\";\nimport * as i12 from \"./components/header/header.component\";\nimport * as i13 from \"./components/content/content.component\";\nimport * as i14 from \"./components/footer/footer.component\";\nimport * as i15 from \"./components/scripts-init/scripts-init.component\";\nimport * as i16 from \"./components/toolbar/toolbar.component\";\nimport * as i17 from \"./components/sidebar/sidebar.component\";\nconst _c0 = [\"ktSidebar\"];\nconst _c1 = [\"ktAside\"];\nconst _c2 = [\"ktHeaderMobile\"];\nconst _c3 = [\"ktHeader\"];\nfunction LayoutComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\", 12)(3, \"span\", 13);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 14);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LayoutComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-header\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appHeaderDefaultClass);\n  }\n}\nfunction LayoutComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-sidebar\", 16, 0);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appSidebarDefaultClass);\n  }\n}\nfunction LayoutComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction LayoutComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-toolbar\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appToolbarCSSClass)(\"appToolbarLayout\", ctx_r0.appToolbarLayout);\n  }\n}\nfunction LayoutComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-footer\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appFooterCSSClass)(\"appFooterContainerCSSClass\", ctx_r0.appFooterContainerCSSClass);\n  }\n}\nexport class LayoutComponent {\n  initService;\n  layout;\n  router;\n  activatedRoute;\n  httpUtilService;\n  unsubscribe = [];\n  // Loading state\n  isLoading = false;\n  // Public variables\n  // page\n  pageContainerCSSClasses;\n  // header\n  appHeaderDefaultClass = '';\n  appHeaderDisplay;\n  appHeaderDefaultStickyEnabled;\n  appHeaderDefaultStickyAttributes = {};\n  appHeaderDefaultMinimizeEnabled;\n  appHeaderDefaultMinimizeAttributes = {};\n  // toolbar\n  appToolbarDisplay;\n  appToolbarLayout;\n  appToolbarCSSClass = '';\n  appToolbarSwapEnabled;\n  appToolbarSwapAttributes = {};\n  appToolbarStickyEnabled;\n  appToolbarStickyAttributes = {};\n  appToolbarMinimizeEnabled;\n  appToolbarMinimizeAttributes = {};\n  // content\n  appContentContiner;\n  appContentContainerClass;\n  contentCSSClasses;\n  contentContainerCSSClass;\n  // sidebar\n  appSidebarDefaultClass;\n  appSidebarDefaultDrawerEnabled;\n  appSidebarDefaultDrawerAttributes = {};\n  appSidebarDisplay;\n  appSidebarDefaultStickyEnabled;\n  appSidebarDefaultStickyAttributes = {};\n  ktSidebar;\n  /// sidebar panel\n  appSidebarPanelDisplay;\n  // footer\n  appFooterDisplay;\n  appFooterCSSClass = '';\n  appFooterContainer = '';\n  appFooterContainerCSSClass = '';\n  appFooterFixedDesktop;\n  appFooterFixedMobile;\n  // scrolltop\n  scrolltopDisplay;\n  ktAside;\n  ktHeaderMobile;\n  ktHeader;\n  constructor(initService, layout, router, activatedRoute, httpUtilService) {\n    this.initService = initService;\n    this.layout = layout;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.httpUtilService = httpUtilService;\n    // define layout type and load layout\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        const currentLayoutType = this.layout.currentLayoutTypeSubject.value;\n        const nextLayoutType = this.activatedRoute?.firstChild?.snapshot.data.layout || this.layout.getBaseLayoutTypeFromLocalStorage();\n        if (currentLayoutType !== nextLayoutType || !currentLayoutType) {\n          this.layout.currentLayoutTypeSubject.next(nextLayoutType);\n          this.initService.reInitProps(nextLayoutType);\n        }\n      }\n    });\n  }\n  ngOnInit() {\n    const subscr = this.layout.layoutConfigSubject.asObservable().subscribe(config => {\n      this.updateProps(config);\n    });\n    this.unsubscribe.push(subscr);\n    // Subscribe to loading state changes\n    const loadingSubscr = this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading === true;\n    });\n    this.unsubscribe.push(loadingSubscr);\n  }\n  updateProps(config) {\n    this.scrolltopDisplay = this.layout.getProp('scrolltop.display', config);\n    this.pageContainerCSSClasses = this.layout.getStringCSSClasses('pageContainer');\n    this.appHeaderDefaultClass = this.layout.getProp('app.header.default.class', config);\n    this.appHeaderDisplay = this.layout.getProp('app.header.display', config);\n    this.appFooterDisplay = this.layout.getProp('app.footer.display', config);\n    this.appSidebarDisplay = this.layout.getProp('app.sidebar.display', config);\n    this.appSidebarPanelDisplay = this.layout.getProp('app.sidebar-panel.display', config);\n    this.appToolbarDisplay = this.layout.getProp('app.toolbar.display', config);\n    this.contentCSSClasses = this.layout.getStringCSSClasses('content');\n    this.contentContainerCSSClass = this.layout.getStringCSSClasses('contentContainer');\n    this.appContentContiner = this.layout.getProp('app.content.container', config);\n    this.appContentContainerClass = this.layout.getProp('app.content.containerClass', config);\n    // footer\n    if (this.appFooterDisplay) {\n      this.updateFooter(config);\n    }\n    // sidebar\n    if (this.appSidebarDisplay) {\n      this.updateSidebar(config);\n    }\n    // header\n    if (this.appHeaderDisplay) {\n      this.updateHeader(config);\n    }\n    // toolbar\n    if (this.appToolbarDisplay) {\n      this.updateToolbar(config);\n    }\n  }\n  updateSidebar(config) {\n    this.appSidebarDefaultClass = this.layout.getProp('app.sidebar.default.class', config);\n    this.appSidebarDefaultDrawerEnabled = this.layout.getProp('app.sidebar.default.drawer.enabled', config);\n    if (this.appSidebarDefaultDrawerEnabled) {\n      this.appSidebarDefaultDrawerAttributes = this.layout.getProp('app.sidebar.default.drawer.attributes', config);\n    }\n    this.appSidebarDefaultStickyEnabled = this.layout.getProp('app.sidebar.default.sticky.enabled', config);\n    if (this.appSidebarDefaultStickyEnabled) {\n      this.appSidebarDefaultStickyAttributes = this.layout.getProp('app.sidebar.default.sticky.attributes', config);\n    }\n    setTimeout(() => {\n      const sidebarElement = document.getElementById('kt_app_sidebar');\n      // sidebar\n      if (this.appSidebarDisplay && sidebarElement) {\n        const sidebarAttributes = sidebarElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        sidebarAttributes.forEach(attr => sidebarElement.removeAttribute(attr));\n        if (this.appSidebarDefaultDrawerEnabled) {\n          for (const key in this.appSidebarDefaultDrawerAttributes) {\n            if (this.appSidebarDefaultDrawerAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(key, this.appSidebarDefaultDrawerAttributes[key]);\n            }\n          }\n        }\n        if (this.appSidebarDefaultStickyEnabled) {\n          for (const key in this.appSidebarDefaultStickyAttributes) {\n            if (this.appSidebarDefaultStickyAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(key, this.appSidebarDefaultStickyAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  updateHeader(config) {\n    this.appHeaderDefaultStickyEnabled = this.layout.getProp('app.header.default.sticky.enabled', config);\n    if (this.appHeaderDefaultStickyEnabled) {\n      this.appHeaderDefaultStickyAttributes = this.layout.getProp('app.header.default.sticky.attributes', config);\n    }\n    this.appHeaderDefaultMinimizeEnabled = this.layout.getProp('app.header.default.minimize.enabled', config);\n    if (this.appHeaderDefaultMinimizeEnabled) {\n      this.appHeaderDefaultMinimizeAttributes = this.layout.getProp('app.header.default.minimize.attributes', config);\n    }\n    setTimeout(() => {\n      const headerElement = document.getElementById('kt_app_header');\n      // header\n      if (this.appHeaderDisplay && headerElement) {\n        const headerAttributes = headerElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        headerAttributes.forEach(attr => headerElement.removeAttribute(attr));\n        if (this.appHeaderDefaultStickyEnabled) {\n          for (const key in this.appHeaderDefaultStickyAttributes) {\n            if (this.appHeaderDefaultStickyAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(key, this.appHeaderDefaultStickyAttributes[key]);\n            }\n          }\n        }\n        if (this.appHeaderDefaultMinimizeEnabled) {\n          for (const key in this.appHeaderDefaultMinimizeAttributes) {\n            if (this.appHeaderDefaultMinimizeAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(key, this.appHeaderDefaultMinimizeAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  updateFooter(config) {\n    this.appFooterCSSClass = this.layout.getProp('app.footer.class', config);\n    this.appFooterContainer = this.layout.getProp('app.footer.container', config);\n    this.appFooterContainerCSSClass = this.layout.getProp('app.footer.containerClass', config);\n    if (this.appFooterContainer === 'fixed') {\n      this.appFooterContainerCSSClass += ' container-xxl';\n    } else {\n      if (this.appFooterContainer === 'fluid') {\n        this.appFooterContainerCSSClass += ' container-fluid';\n      }\n    }\n    this.appFooterFixedDesktop = this.layout.getProp('app.footer.fixed.desktop', config);\n    if (this.appFooterFixedDesktop) {\n      document.body.setAttribute('data-kt-app-footer-fixed', 'true');\n    }\n    this.appFooterFixedMobile = this.layout.getProp('app.footer.fixed.mobile');\n    if (this.appFooterFixedMobile) {\n      document.body.setAttribute('data-kt-app-footer-fixed-mobile', 'true');\n    }\n  }\n  updateToolbar(config) {\n    this.appToolbarLayout = this.layout.getProp('app.toolbar.layout', config);\n    this.appToolbarSwapEnabled = this.layout.getProp('app.toolbar.swap.enabled', config);\n    if (this.appToolbarSwapEnabled) {\n      this.appToolbarSwapAttributes = this.layout.getProp('app.toolbar.swap.attributes', config);\n    }\n    this.appToolbarStickyEnabled = this.layout.getProp('app.toolbar.sticky.enabled', config);\n    if (this.appToolbarStickyEnabled) {\n      this.appToolbarStickyAttributes = this.layout.getProp('app.toolbar.sticky.attributes', config);\n    }\n    this.appToolbarCSSClass = this.layout.getProp('app.toolbar.class', config) || '';\n    this.appToolbarMinimizeEnabled = this.layout.getProp('app.toolbar.minimize.enabled', config);\n    if (this.appToolbarMinimizeEnabled) {\n      this.appToolbarMinimizeAttributes = this.layout.getProp('app.toolbar.minimize.attributes', config);\n      this.appToolbarCSSClass += ' app-toolbar-minimize';\n    }\n    setTimeout(() => {\n      const toolbarElement = document.getElementById('kt_app_toolbar');\n      // toolbar\n      if (this.appToolbarDisplay && toolbarElement) {\n        const toolbarAttributes = toolbarElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        toolbarAttributes.forEach(attr => toolbarElement.removeAttribute(attr));\n        if (this.appToolbarSwapEnabled) {\n          for (const key in this.appToolbarSwapAttributes) {\n            if (this.appToolbarSwapAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarSwapAttributes[key]);\n            }\n          }\n        }\n        if (this.appToolbarStickyEnabled) {\n          for (const key in this.appToolbarStickyAttributes) {\n            if (this.appToolbarStickyAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarStickyAttributes[key]);\n            }\n          }\n        }\n        if (this.appToolbarMinimizeEnabled) {\n          for (const key in this.appToolbarMinimizeAttributes) {\n            if (this.appToolbarMinimizeAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarMinimizeAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n  static ɵfac = function LayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LayoutComponent)(i0.ɵɵdirectiveInject(i1.LayoutInitService), i0.ɵɵdirectiveInject(i2.LayoutService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LayoutComponent,\n    selectors: [[\"app-layout\"]],\n    viewQuery: function LayoutComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n        i0.ɵɵviewQuery(_c3, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktSidebar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktAside = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktHeaderMobile = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktHeader = _t.first);\n      }\n    },\n    decls: 20,\n    vars: 10,\n    consts: [[\"ktSidebar\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [\"id\", \"kt_app_root\", 1, \"d-flex\", \"flex-column\", \"flex-root\", \"app-root\"], [\"id\", \"kt_app_page\", 1, \"app-page\", \"flex-column\", \"flex-column-fluid\"], [4, \"ngIf\"], [\"id\", \"kt_app_wrapper\", 1, \"app-wrapper\", \"flex-column\", \"flex-row-fluid\"], [\"id\", \"kt_app_main\", 1, \"app-main\", \"flex-column\", \"flex-row-fluid\"], [1, \"d-flex\", \"flex-column\", \"flex-column-fluid\"], [\"id\", \" kt_app_content\", 1, \"app-content\", 3, \"ngClass\", \"contentContainerCSSClass\", \"appContentContiner\", \"appContentContainerClass\"], [\"id\", \"kt_scrolltop\", \"data-kt-scrolltop\", \"true\", 1, \"scrolltop\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [\"id\", \"kt_app_header\", \"data-kt-sticky\", \"true\", \"data-kt-sticky-activate\", \"{default: true, lg: true}\", \"data-kt-sticky-name\", \"app-header-minimize\", \"data-kt-sticky-offset\", \"{default: '200px', lg: '0'}\", \"data-kt-sticky-animation\", \"false\", 1, \"app-header\", 3, \"ngClass\"], [\"id\", \"kt_app_sidebar\", 1, \"app-sidebar\", \"flex-column\", 3, \"ngClass\"], [\"id\", \"kt_app_toolbar\", 1, \"app-toolbar\", 3, \"ngClass\", \"appToolbarLayout\"], [\"id\", \"kt_app_footer\", 1, \"app-footer\", 3, \"ngClass\", \"appFooterContainerCSSClass\"]],\n    template: function LayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, LayoutComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3);\n        i0.ɵɵtemplate(3, LayoutComponent_ng_container_3_Template, 2, 1, \"ng-container\", 4);\n        i0.ɵɵelementStart(4, \"div\", 5);\n        i0.ɵɵtemplate(5, LayoutComponent_ng_container_5_Template, 3, 1, \"ng-container\", 4)(6, LayoutComponent_ng_container_6_Template, 1, 0, \"ng-container\", 4);\n        i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n        i0.ɵɵtemplate(9, LayoutComponent_ng_container_9_Template, 2, 2, \"ng-container\", 4);\n        i0.ɵɵelement(10, \"app-content\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(11, LayoutComponent_ng_container_11_Template, 2, 2, \"ng-container\", 4);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(12, \"app-scripts-init\");\n        i0.ɵɵelementContainerStart(13);\n        i0.ɵɵelement(14, \"app-scroll-top\", 9);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelement(15, \"app-activity-drawer\")(16, \"app-messenger-drawer\")(17, \"app-main-modal\")(18, \"app-invite-users-modal\")(19, \"app-upgrade-plan-modal\");\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.appHeaderDisplay);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.appSidebarDisplay);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.appSidebarPanelDisplay);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.appToolbarDisplay);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.contentCSSClasses)(\"contentContainerCSSClass\", ctx.contentContainerCSSClass)(\"appContentContiner\", ctx.appContentContiner)(\"appContentContainerClass\", ctx.appContentContainerClass);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.appFooterDisplay);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgIf, i6.LayoutScrollTopComponent, i7.InviteUsersModalComponent, i8.MainModalComponent, i9.UpgradePlanModalComponent, i10.ActivityDrawerComponent, i11.MessengerDrawerComponent, i12.HeaderComponent, i13.ContentComponent, i14.FooterComponent, i15.ScriptsInitComponent, i16.ToolbarComponent, i17.SidebarComponent],\n    styles: [\"[_nghost-%COMP%] {\\n  height: 100%;\\n  margin: 0;\\n}\\n[_nghost-%COMP%]   .flex-root[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n.page-loaded[_ngcontent-%COMP%]   app-layout[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transition: opacity 1s ease-in-out;\\n}\\n\\n.app-sidebar[_ngcontent-%COMP%] {\\n  border-right: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvX21ldHJvbmljL2xheW91dC9sYXlvdXQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0VBQ0EsU0FBQTtBQUNGO0FBQ0U7RUFDRSxZQUFBO0FBQ0o7O0FBSUU7RUFDRSxVQUFBO0VBQ0Esa0NBQUE7QUFESjs7QUFLQTtFQUVJLGVBQUE7QUFISiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgbWFyZ2luOiAwO1xyXG5cclxuICAuZmxleC1yb290IHtcclxuICAgIGhlaWdodDogMTAwJTtcclxuICB9XHJcbn1cclxuXHJcbi5wYWdlLWxvYWRlZCB7XHJcbiAgYXBwLWxheW91dCB7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gICAgdHJhbnNpdGlvbjogb3BhY2l0eSAxcyBlYXNlLWluLW91dDtcclxuICB9XHJcbn1cclxuXHJcbi5hcHAtc2lkZWJhciB7XHJcbiAgICAvLyBiYWNrZ3JvdW5kLWNvbG9yOiAjMWI3ZTZjICFpbXBvcnRhbnQ7XHJcbiAgICBib3JkZXItcmlnaHQ6IDA7XHJcbn1cclxuXHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "appHeaderDefaultClass", "appSidebarDefaultClass", "ɵɵelementContainer", "appToolbarCSSClass", "appToolbarLayout", "appFooterCSSClass", "appFooterContainerCSSClass", "LayoutComponent", "initService", "layout", "router", "activatedRoute", "httpUtilService", "unsubscribe", "isLoading", "pageContainerCSSClasses", "appHeaderDisplay", "appHeaderDefaultStickyEnabled", "appHeaderDefaultStickyAttributes", "appHeaderDefaultMinimizeEnabled", "appHeaderDefaultMinimizeAttributes", "appToolbarDisplay", "appToolbarSwapEnabled", "appToolbarSwapAttributes", "appToolbarStickyEnabled", "appToolbarStickyAttributes", "appToolbarMinimizeEnabled", "appToolbarMinimizeAttributes", "appContentContiner", "appContentContainerClass", "contentCSSClasses", "contentContainerCSSClass", "appSidebarDefaultDrawerEnabled", "appSidebarDefaultDrawerAttributes", "appSidebarDisplay", "appSidebarDefaultStickyEnabled", "appSidebarDefaultStickyAttributes", "ktSidebar", "appSidebarPanelDisplay", "appFooterDisplay", "appFooter<PERSON><PERSON><PERSON>", "appFooterFixedDesktop", "appFooterFixedMobile", "scrolltopDisplay", "ktAside", "ktHeaderMobile", "ktHeader", "constructor", "events", "subscribe", "event", "currentLayoutType", "currentLayoutTypeSubject", "value", "nextLayoutType", "<PERSON><PERSON><PERSON><PERSON>", "snapshot", "data", "getBaseLayoutTypeFromLocalStorage", "next", "reInitProps", "ngOnInit", "subscr", "layoutConfigSubject", "asObservable", "config", "updateProps", "push", "loadingSubscr", "loadingSubject", "loading", "getProp", "getStringCSSClasses", "updateFooter", "updateSidebar", "updateHeader", "updateToolbar", "setTimeout", "sidebarElement", "document", "getElementById", "sidebarAttributes", "getAttributeNames", "filter", "t", "indexOf", "for<PERSON>ach", "attr", "removeAttribute", "key", "hasOwnProperty", "setAttribute", "headerElement", "headerAttributes", "body", "toolbarElement", "toolbarAttributes", "ngOnDestroy", "sb", "ɵɵdirectiveInject", "i1", "LayoutInitService", "i2", "LayoutService", "i3", "Router", "ActivatedRoute", "i4", "HttpUtilsService", "selectors", "viewQuery", "LayoutComponent_Query", "rf", "ctx", "ɵɵtemplate", "LayoutComponent_div_0_Template", "LayoutComponent_ng_container_3_Template", "LayoutComponent_ng_container_5_Template", "LayoutComponent_ng_container_6_Template", "LayoutComponent_ng_container_9_Template", "LayoutComponent_ng_container_11_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\_metronic\\layout\\layout.component.ts", "D:\\permittracker\\Angular\\src\\app\\_metronic\\layout\\layout.component.html"], "sourcesContent": ["import {\n  Component,\n  OnInit,\n  ViewChild,\n  ElementRef,\n  On<PERSON><PERSON>roy,\n} from '@angular/core';\nimport { ActivatedRoute, NavigationEnd, Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { LayoutService } from './core/layout.service';\nimport { LayoutInitService } from './core/layout-init.service';\nimport { ILayout, LayoutType } from './core/configs/config';\nimport { HttpUtilsService } from '../../modules/services/http-utils.service';\n\n@Component({\n  selector: 'app-layout',\n  templateUrl: './layout.component.html',\n  styleUrls: ['./layout.component.scss'],\n})\nexport class LayoutComponent implements OnInit, OnDestroy {\n  private unsubscribe: Subscription[] = [];\n\n  // Loading state\n  isLoading: boolean = false;\n\n  // Public variables\n  // page\n  pageContainerCSSClasses: string;\n  // header\n  appHeaderDefaultClass: string = '';\n  appHeaderDisplay: boolean;\n  appHeaderDefaultStickyEnabled: boolean;\n  appHeaderDefaultStickyAttributes: { [attrName: string]: string } = {};\n  appHeaderDefaultMinimizeEnabled: boolean;\n  appHeaderDefaultMinimizeAttributes: { [attrName: string]: string } = {};\n  // toolbar\n  appToolbarDisplay: boolean;\n  appToolbarLayout: 'classic' | 'accounting' | 'extended' | 'reports' | 'saas';\n  appToolbarCSSClass: string = '';\n  appToolbarSwapEnabled: boolean;\n  appToolbarSwapAttributes: { [attrName: string]: string } = {};\n  appToolbarStickyEnabled: boolean;\n  appToolbarStickyAttributes: { [attrName: string]: string } = {};\n  appToolbarMinimizeEnabled: boolean;\n  appToolbarMinimizeAttributes: { [attrName: string]: string } = {};\n\n  // content\n  appContentContiner?: 'fixed' | 'fluid';\n  appContentContainerClass: string;\n  contentCSSClasses: string;\n  contentContainerCSSClass: string;\n  // sidebar\n  appSidebarDefaultClass: string;\n  appSidebarDefaultDrawerEnabled: boolean;\n  appSidebarDefaultDrawerAttributes: { [attrName: string]: string } = {};\n  appSidebarDisplay: boolean;\n  appSidebarDefaultStickyEnabled: boolean;\n  appSidebarDefaultStickyAttributes: { [attrName: string]: string } = {};\n  @ViewChild('ktSidebar', { static: true }) ktSidebar: ElementRef;\n  /// sidebar panel\n  appSidebarPanelDisplay: boolean;\n  // footer\n  appFooterDisplay: boolean;\n  appFooterCSSClass: string = '';\n  appFooterContainer: string = '';\n  appFooterContainerCSSClass: string = '';\n  appFooterFixedDesktop: boolean;\n  appFooterFixedMobile: boolean;\n\n  // scrolltop\n  scrolltopDisplay: boolean;\n\n  @ViewChild('ktAside', { static: true }) ktAside: ElementRef;\n  @ViewChild('ktHeaderMobile', { static: true }) ktHeaderMobile: ElementRef;\n  @ViewChild('ktHeader', { static: true }) ktHeader: ElementRef;\n\n  constructor(\n    private initService: LayoutInitService,\n    private layout: LayoutService,\n    private router: Router,\n    private activatedRoute: ActivatedRoute,\n    private httpUtilService: HttpUtilsService\n  ) {\n    // define layout type and load layout\n    this.router.events.subscribe((event) => {\n      if (event instanceof NavigationEnd) {\n        const currentLayoutType = this.layout.currentLayoutTypeSubject.value;\n\n        const nextLayoutType: LayoutType =\n          this.activatedRoute?.firstChild?.snapshot.data.layout ||\n          this.layout.getBaseLayoutTypeFromLocalStorage();\n\n        if (currentLayoutType !== nextLayoutType || !currentLayoutType) {\n          this.layout.currentLayoutTypeSubject.next(nextLayoutType);\n          this.initService.reInitProps(nextLayoutType);\n        }\n      }\n    });\n  }\n\n  ngOnInit() {\n    const subscr = this.layout.layoutConfigSubject\n      .asObservable()\n      .subscribe((config) => {\n        this.updateProps(config);\n      });\n    this.unsubscribe.push(subscr);\n\n    // Subscribe to loading state changes\n    const loadingSubscr = this.httpUtilService.loadingSubject.subscribe(\n      (loading) => {\n        this.isLoading = loading === true;\n      }\n    );\n    this.unsubscribe.push(loadingSubscr);\n  }\n\n  updateProps(config: ILayout) {\n    this.scrolltopDisplay = this.layout.getProp(\n      'scrolltop.display',\n      config\n    ) as boolean;\n    this.pageContainerCSSClasses =\n      this.layout.getStringCSSClasses('pageContainer');\n    this.appHeaderDefaultClass = this.layout.getProp(\n      'app.header.default.class',\n      config\n    ) as string;\n    this.appHeaderDisplay = this.layout.getProp(\n      'app.header.display',\n      config\n    ) as boolean;\n    this.appFooterDisplay = this.layout.getProp(\n      'app.footer.display',\n      config\n    ) as boolean;\n    this.appSidebarDisplay = this.layout.getProp(\n      'app.sidebar.display',\n      config\n    ) as boolean;\n    this.appSidebarPanelDisplay = this.layout.getProp(\n      'app.sidebar-panel.display',\n      config\n    ) as boolean;\n    this.appToolbarDisplay = this.layout.getProp(\n      'app.toolbar.display',\n      config\n    ) as boolean;\n    this.contentCSSClasses = this.layout.getStringCSSClasses('content');\n    this.contentContainerCSSClass =\n      this.layout.getStringCSSClasses('contentContainer');\n    this.appContentContiner = this.layout.getProp(\n      'app.content.container',\n      config\n    ) as 'fixed' | 'fluid';\n    this.appContentContainerClass = this.layout.getProp(\n      'app.content.containerClass',\n      config\n    ) as string;\n    // footer\n    if (this.appFooterDisplay) {\n      this.updateFooter(config);\n    }\n    // sidebar\n    if (this.appSidebarDisplay) {\n      this.updateSidebar(config);\n    }\n    // header\n    if (this.appHeaderDisplay) {\n      this.updateHeader(config);\n    }\n    // toolbar\n    if (this.appToolbarDisplay) {\n      this.updateToolbar(config);\n    }\n  }\n\n  updateSidebar(config: ILayout) {\n    this.appSidebarDefaultClass = this.layout.getProp(\n      'app.sidebar.default.class',\n      config\n    ) as string;\n\n    this.appSidebarDefaultDrawerEnabled = this.layout.getProp(\n      'app.sidebar.default.drawer.enabled',\n      config\n    ) as boolean;\n    if (this.appSidebarDefaultDrawerEnabled) {\n      this.appSidebarDefaultDrawerAttributes = this.layout.getProp(\n        'app.sidebar.default.drawer.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    this.appSidebarDefaultStickyEnabled = this.layout.getProp(\n      'app.sidebar.default.sticky.enabled',\n      config\n    ) as boolean;\n    if (this.appSidebarDefaultStickyEnabled) {\n      this.appSidebarDefaultStickyAttributes = this.layout.getProp(\n        'app.sidebar.default.sticky.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    setTimeout(() => {\n      const sidebarElement = document.getElementById('kt_app_sidebar');\n      // sidebar\n      if (this.appSidebarDisplay && sidebarElement) {\n        const sidebarAttributes = sidebarElement\n          .getAttributeNames()\n          .filter((t) => t.indexOf('data-') > -1);\n        sidebarAttributes.forEach((attr) =>\n          sidebarElement.removeAttribute(attr)\n        );\n\n        if (this.appSidebarDefaultDrawerEnabled) {\n          for (const key in this.appSidebarDefaultDrawerAttributes) {\n            if (this.appSidebarDefaultDrawerAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(\n                key,\n                this.appSidebarDefaultDrawerAttributes[key]\n              );\n            }\n          }\n        }\n\n        if (this.appSidebarDefaultStickyEnabled) {\n          for (const key in this.appSidebarDefaultStickyAttributes) {\n            if (this.appSidebarDefaultStickyAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(\n                key,\n                this.appSidebarDefaultStickyAttributes[key]\n              );\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n\n  updateHeader(config: ILayout) {\n    this.appHeaderDefaultStickyEnabled = this.layout.getProp(\n      'app.header.default.sticky.enabled',\n      config\n    ) as boolean;\n    if (this.appHeaderDefaultStickyEnabled) {\n      this.appHeaderDefaultStickyAttributes = this.layout.getProp(\n        'app.header.default.sticky.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    this.appHeaderDefaultMinimizeEnabled = this.layout.getProp(\n      'app.header.default.minimize.enabled',\n      config\n    ) as boolean;\n    if (this.appHeaderDefaultMinimizeEnabled) {\n      this.appHeaderDefaultMinimizeAttributes = this.layout.getProp(\n        'app.header.default.minimize.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    setTimeout(() => {\n      const headerElement = document.getElementById('kt_app_header');\n      // header\n      if (this.appHeaderDisplay && headerElement) {\n        const headerAttributes = headerElement\n          .getAttributeNames()\n          .filter((t) => t.indexOf('data-') > -1);\n        headerAttributes.forEach((attr) => headerElement.removeAttribute(attr));\n\n        if (this.appHeaderDefaultStickyEnabled) {\n          for (const key in this.appHeaderDefaultStickyAttributes) {\n            if (this.appHeaderDefaultStickyAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(\n                key,\n                this.appHeaderDefaultStickyAttributes[key]\n              );\n            }\n          }\n        }\n\n        if (this.appHeaderDefaultMinimizeEnabled) {\n          for (const key in this.appHeaderDefaultMinimizeAttributes) {\n            if (this.appHeaderDefaultMinimizeAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(\n                key,\n                this.appHeaderDefaultMinimizeAttributes[key]\n              );\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n\n  updateFooter(config: ILayout) {\n    this.appFooterCSSClass = this.layout.getProp('app.footer.class', config) as string;\n    this.appFooterContainer = this.layout.getProp('app.footer.container', config) as string;\n    this.appFooterContainerCSSClass = this.layout.getProp('app.footer.containerClass', config) as string;\n    if (this.appFooterContainer === 'fixed') {\n      this.appFooterContainerCSSClass += ' container-xxl';\n    } else {\n      if (this.appFooterContainer === 'fluid') {\n        this.appFooterContainerCSSClass += ' container-fluid';\n      }\n    }\n\n    this.appFooterFixedDesktop = this.layout.getProp('app.footer.fixed.desktop', config) as boolean;\n    if (this.appFooterFixedDesktop) {\n      document.body.setAttribute('data-kt-app-footer-fixed', 'true')\n    }\n\n    this.appFooterFixedMobile = this.layout.getProp('app.footer.fixed.mobile') as boolean;\n    if (this.appFooterFixedMobile) {\n      document.body.setAttribute('data-kt-app-footer-fixed-mobile', 'true')\n    }\n  }\n\n  updateToolbar(config: ILayout) {\n    this.appToolbarLayout = this.layout.getProp(\n      'app.toolbar.layout',\n      config\n    ) as 'classic' | 'accounting' | 'extended' | 'reports' | 'saas';\n    this.appToolbarSwapEnabled = this.layout.getProp(\n      'app.toolbar.swap.enabled',\n      config\n    ) as boolean;\n    if (this.appToolbarSwapEnabled) {\n      this.appToolbarSwapAttributes = this.layout.getProp(\n        'app.toolbar.swap.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    this.appToolbarStickyEnabled = this.layout.getProp(\n      'app.toolbar.sticky.enabled',\n      config\n    ) as boolean;\n    if (this.appToolbarStickyEnabled) {\n      this.appToolbarStickyAttributes = this.layout.getProp(\n        'app.toolbar.sticky.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    this.appToolbarCSSClass =\n      (this.layout.getProp('app.toolbar.class', config) as string) || '';\n    this.appToolbarMinimizeEnabled = this.layout.getProp(\n      'app.toolbar.minimize.enabled',\n      config\n    ) as boolean;\n    if (this.appToolbarMinimizeEnabled) {\n      this.appToolbarMinimizeAttributes = this.layout.getProp(\n        'app.toolbar.minimize.attributes',\n        config\n      ) as { [attrName: string]: string };\n      this.appToolbarCSSClass += ' app-toolbar-minimize';\n    }\n\n    setTimeout(() => {\n      const toolbarElement = document.getElementById('kt_app_toolbar');\n      // toolbar\n      if (this.appToolbarDisplay && toolbarElement) {\n        const toolbarAttributes = toolbarElement\n          .getAttributeNames()\n          .filter((t) => t.indexOf('data-') > -1);\n        toolbarAttributes.forEach((attr) =>\n          toolbarElement.removeAttribute(attr)\n        );\n\n        if (this.appToolbarSwapEnabled) {\n          for (const key in this.appToolbarSwapAttributes) {\n            if (this.appToolbarSwapAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(\n                key,\n                this.appToolbarSwapAttributes[key]\n              );\n            }\n          }\n        }\n\n        if (this.appToolbarStickyEnabled) {\n          for (const key in this.appToolbarStickyAttributes) {\n            if (this.appToolbarStickyAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(\n                key,\n                this.appToolbarStickyAttributes[key]\n              );\n            }\n          }\n        }\n\n        if (this.appToolbarMinimizeEnabled) {\n          for (const key in this.appToolbarMinimizeAttributes) {\n            if (this.appToolbarMinimizeAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(\n                key,\n                this.appToolbarMinimizeAttributes[key]\n              );\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe());\n  }\n}\n", "<!-- Global Loading Overlay for Main App -->\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"custom-colored-spinner\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<!--begin::App-->\n<div class=\"d-flex flex-column flex-root app-root\" id=\"kt_app_root\">\n  <!--begin::Page-->\n  <div class=\"app-page flex-column flex-column-fluid\" id=\"kt_app_page\">\n    <ng-container *ngIf=\"appHeaderDisplay\">\n      <!--begin::Header-->\n      <app-header [ngClass]=\"appHeaderDefaultClass\" id=\"kt_app_header\" class=\"app-header\" data-kt-sticky=\"true\" data-kt-sticky-activate=\"{default: true, lg: true}\" data-kt-sticky-name=\"app-header-minimize\" data-kt-sticky-offset=\"{default: '200px', lg: '0'}\" data-kt-sticky-animation=\"false\">\n      </app-header>\n      <!--end::Header-->\n    </ng-container>\n\n    <!--begin::Wrapper-->\n    <div class=\"app-wrapper flex-column flex-row-fluid\" id=\"kt_app_wrapper\">\n\n      <ng-container *ngIf=\"appSidebarDisplay\">\n        <!--begin::sidebar-->\n        <app-sidebar #ktSidebar id=\"kt_app_sidebar\" class=\"app-sidebar flex-column\" [ngClass]=\"appSidebarDefaultClass\">\n        </app-sidebar>\n        <!--end::sidebar-->\n      </ng-container>\n\n      <ng-container *ngIf=\"appSidebarPanelDisplay\">\n        <!-- TODO: app sidebar panel -->\n      </ng-container>\n      <!--begin::Main-->\n      <div class=\"app-main flex-column flex-row-fluid\" id=\"kt_app_main\">\n        <!--begin::Content wrapper-->\n        <div class=\"d-flex flex-column flex-column-fluid\">\n          <ng-container *ngIf=\"appToolbarDisplay\">\n            <app-toolbar class=\"app-toolbar\" [ngClass]=\"appToolbarCSSClass\" id=\"kt_app_toolbar\"\n              [appToolbarLayout]=\"appToolbarLayout\"></app-toolbar>\n          </ng-container>\n          <app-content id=\" kt_app_content\" class=\"app-content\" [ngClass]=\"contentCSSClasses\"\n            [contentContainerCSSClass]=\"contentContainerCSSClass\" [appContentContiner]=\"appContentContiner\"\n            [appContentContainerClass]=\"appContentContainerClass\">\n          </app-content>\n        </div>\n        <!--end::Content wrapper-->\n        <ng-container *ngIf=\"appFooterDisplay\">\n          <app-footer class=\"app-footer\" [ngClass]=\"appFooterCSSClass\" id=\"kt_app_footer\"\n            [appFooterContainerCSSClass]=\"appFooterContainerCSSClass\"></app-footer>\n        </ng-container>\n      </div>\n      <!--end:::Main-->\n    </div>\n    <!--end::Wrapper-->\n\n  </div>\n  <!--end::Page-->\n</div>\n<!--end::App-->\n\n<app-scripts-init></app-scripts-init>\n<ng-container>\n  <app-scroll-top id=\"kt_scrolltop\" class=\"scrolltop\" data-kt-scrolltop=\"true\"></app-scroll-top>\n</ng-container>\n<!-- begin:: Drawers -->\n<app-activity-drawer></app-activity-drawer>\n<app-messenger-drawer></app-messenger-drawer>\n<!-- end:: Drawers -->\n\n<!-- end:: Engage -->\n\n<!-- end:: Engage -->\n\n<!-- begin:: Modals -->\n<app-main-modal></app-main-modal>\n<app-invite-users-modal></app-invite-users-modal>\n<app-upgrade-plan-modal></app-upgrade-plan-modal>\n<!-- end:: Modals -->\n"], "mappings": "AAOA,SAAyBA,aAAa,QAAgB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;ICHjEC,EAHN,CAAAC,cAAA,cAA0D,cAC3B,cACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;IAMFH,EAAA,CAAAI,uBAAA,GAAuC;IAErCJ,EAAA,CAAAK,SAAA,qBACa;;;;;IADDL,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,UAAA,YAAAC,MAAA,CAAAC,qBAAA,CAAiC;;;;;IAQ7CT,EAAA,CAAAI,uBAAA,GAAwC;IAEtCJ,EAAA,CAAAK,SAAA,yBACc;;;;;IAD8DL,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAO,UAAA,YAAAC,MAAA,CAAAE,sBAAA,CAAkC;;;;;IAKhHV,EAAA,CAAAW,kBAAA,GAEe;;;;;IAKXX,EAAA,CAAAI,uBAAA,GAAwC;IACtCJ,EAAA,CAAAK,SAAA,sBACsD;;;;;IADrBL,EAAA,CAAAM,SAAA,EAA8B;IAC7DN,EAD+B,CAAAO,UAAA,YAAAC,MAAA,CAAAI,kBAAA,CAA8B,qBAAAJ,MAAA,CAAAK,gBAAA,CACxB;;;;;IAQ3Cb,EAAA,CAAAI,uBAAA,GAAuC;IACrCJ,EAAA,CAAAK,SAAA,qBACyE;;;;;IAD1CL,EAAA,CAAAM,SAAA,EAA6B;IAC1DN,EAD6B,CAAAO,UAAA,YAAAC,MAAA,CAAAM,iBAAA,CAA6B,+BAAAN,MAAA,CAAAO,0BAAA,CACD;;;AD/BrE,OAAM,MAAOC,eAAe;EA0DhBC,WAAA;EACAC,MAAA;EACAC,MAAA;EACAC,cAAA;EACAC,eAAA;EA7DFC,WAAW,GAAmB,EAAE;EAExC;EACAC,SAAS,GAAY,KAAK;EAE1B;EACA;EACAC,uBAAuB;EACvB;EACAf,qBAAqB,GAAW,EAAE;EAClCgB,gBAAgB;EAChBC,6BAA6B;EAC7BC,gCAAgC,GAAmC,EAAE;EACrEC,+BAA+B;EAC/BC,kCAAkC,GAAmC,EAAE;EACvE;EACAC,iBAAiB;EACjBjB,gBAAgB;EAChBD,kBAAkB,GAAW,EAAE;EAC/BmB,qBAAqB;EACrBC,wBAAwB,GAAmC,EAAE;EAC7DC,uBAAuB;EACvBC,0BAA0B,GAAmC,EAAE;EAC/DC,yBAAyB;EACzBC,4BAA4B,GAAmC,EAAE;EAEjE;EACAC,kBAAkB;EAClBC,wBAAwB;EACxBC,iBAAiB;EACjBC,wBAAwB;EACxB;EACA9B,sBAAsB;EACtB+B,8BAA8B;EAC9BC,iCAAiC,GAAmC,EAAE;EACtEC,iBAAiB;EACjBC,8BAA8B;EAC9BC,iCAAiC,GAAmC,EAAE;EAC5BC,SAAS;EACnD;EACAC,sBAAsB;EACtB;EACAC,gBAAgB;EAChBlC,iBAAiB,GAAW,EAAE;EAC9BmC,kBAAkB,GAAW,EAAE;EAC/BlC,0BAA0B,GAAW,EAAE;EACvCmC,qBAAqB;EACrBC,oBAAoB;EAEpB;EACAC,gBAAgB;EAEwBC,OAAO;EACAC,cAAc;EACpBC,QAAQ;EAEjDC,YACUvC,WAA8B,EAC9BC,MAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,eAAiC;IAJjC,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAEvB;IACA,IAAI,CAACF,MAAM,CAACsC,MAAM,CAACC,SAAS,CAAEC,KAAK,IAAI;MACrC,IAAIA,KAAK,YAAY5D,aAAa,EAAE;QAClC,MAAM6D,iBAAiB,GAAG,IAAI,CAAC1C,MAAM,CAAC2C,wBAAwB,CAACC,KAAK;QAEpE,MAAMC,cAAc,GAClB,IAAI,CAAC3C,cAAc,EAAE4C,UAAU,EAAEC,QAAQ,CAACC,IAAI,CAAChD,MAAM,IACrD,IAAI,CAACA,MAAM,CAACiD,iCAAiC,EAAE;QAEjD,IAAIP,iBAAiB,KAAKG,cAAc,IAAI,CAACH,iBAAiB,EAAE;UAC9D,IAAI,CAAC1C,MAAM,CAAC2C,wBAAwB,CAACO,IAAI,CAACL,cAAc,CAAC;UACzD,IAAI,CAAC9C,WAAW,CAACoD,WAAW,CAACN,cAAc,CAAC;QAC9C;MACF;IACF,CAAC,CAAC;EACJ;EAEAO,QAAQA,CAAA;IACN,MAAMC,MAAM,GAAG,IAAI,CAACrD,MAAM,CAACsD,mBAAmB,CAC3CC,YAAY,EAAE,CACdf,SAAS,CAAEgB,MAAM,IAAI;MACpB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IAC1B,CAAC,CAAC;IACJ,IAAI,CAACpD,WAAW,CAACsD,IAAI,CAACL,MAAM,CAAC;IAE7B;IACA,MAAMM,aAAa,GAAG,IAAI,CAACxD,eAAe,CAACyD,cAAc,CAACpB,SAAS,CAChEqB,OAAO,IAAI;MACV,IAAI,CAACxD,SAAS,GAAGwD,OAAO,KAAK,IAAI;IACnC,CAAC,CACF;IACD,IAAI,CAACzD,WAAW,CAACsD,IAAI,CAACC,aAAa,CAAC;EACtC;EAEAF,WAAWA,CAACD,MAAe;IACzB,IAAI,CAACtB,gBAAgB,GAAG,IAAI,CAAClC,MAAM,CAAC8D,OAAO,CACzC,mBAAmB,EACnBN,MAAM,CACI;IACZ,IAAI,CAAClD,uBAAuB,GAC1B,IAAI,CAACN,MAAM,CAAC+D,mBAAmB,CAAC,eAAe,CAAC;IAClD,IAAI,CAACxE,qBAAqB,GAAG,IAAI,CAACS,MAAM,CAAC8D,OAAO,CAC9C,0BAA0B,EAC1BN,MAAM,CACG;IACX,IAAI,CAACjD,gBAAgB,GAAG,IAAI,CAACP,MAAM,CAAC8D,OAAO,CACzC,oBAAoB,EACpBN,MAAM,CACI;IACZ,IAAI,CAAC1B,gBAAgB,GAAG,IAAI,CAAC9B,MAAM,CAAC8D,OAAO,CACzC,oBAAoB,EACpBN,MAAM,CACI;IACZ,IAAI,CAAC/B,iBAAiB,GAAG,IAAI,CAACzB,MAAM,CAAC8D,OAAO,CAC1C,qBAAqB,EACrBN,MAAM,CACI;IACZ,IAAI,CAAC3B,sBAAsB,GAAG,IAAI,CAAC7B,MAAM,CAAC8D,OAAO,CAC/C,2BAA2B,EAC3BN,MAAM,CACI;IACZ,IAAI,CAAC5C,iBAAiB,GAAG,IAAI,CAACZ,MAAM,CAAC8D,OAAO,CAC1C,qBAAqB,EACrBN,MAAM,CACI;IACZ,IAAI,CAACnC,iBAAiB,GAAG,IAAI,CAACrB,MAAM,CAAC+D,mBAAmB,CAAC,SAAS,CAAC;IACnE,IAAI,CAACzC,wBAAwB,GAC3B,IAAI,CAACtB,MAAM,CAAC+D,mBAAmB,CAAC,kBAAkB,CAAC;IACrD,IAAI,CAAC5C,kBAAkB,GAAG,IAAI,CAACnB,MAAM,CAAC8D,OAAO,CAC3C,uBAAuB,EACvBN,MAAM,CACc;IACtB,IAAI,CAACpC,wBAAwB,GAAG,IAAI,CAACpB,MAAM,CAAC8D,OAAO,CACjD,4BAA4B,EAC5BN,MAAM,CACG;IACX;IACA,IAAI,IAAI,CAAC1B,gBAAgB,EAAE;MACzB,IAAI,CAACkC,YAAY,CAACR,MAAM,CAAC;IAC3B;IACA;IACA,IAAI,IAAI,CAAC/B,iBAAiB,EAAE;MAC1B,IAAI,CAACwC,aAAa,CAACT,MAAM,CAAC;IAC5B;IACA;IACA,IAAI,IAAI,CAACjD,gBAAgB,EAAE;MACzB,IAAI,CAAC2D,YAAY,CAACV,MAAM,CAAC;IAC3B;IACA;IACA,IAAI,IAAI,CAAC5C,iBAAiB,EAAE;MAC1B,IAAI,CAACuD,aAAa,CAACX,MAAM,CAAC;IAC5B;EACF;EAEAS,aAAaA,CAACT,MAAe;IAC3B,IAAI,CAAChE,sBAAsB,GAAG,IAAI,CAACQ,MAAM,CAAC8D,OAAO,CAC/C,2BAA2B,EAC3BN,MAAM,CACG;IAEX,IAAI,CAACjC,8BAA8B,GAAG,IAAI,CAACvB,MAAM,CAAC8D,OAAO,CACvD,oCAAoC,EACpCN,MAAM,CACI;IACZ,IAAI,IAAI,CAACjC,8BAA8B,EAAE;MACvC,IAAI,CAACC,iCAAiC,GAAG,IAAI,CAACxB,MAAM,CAAC8D,OAAO,CAC1D,uCAAuC,EACvCN,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAC9B,8BAA8B,GAAG,IAAI,CAAC1B,MAAM,CAAC8D,OAAO,CACvD,oCAAoC,EACpCN,MAAM,CACI;IACZ,IAAI,IAAI,CAAC9B,8BAA8B,EAAE;MACvC,IAAI,CAACC,iCAAiC,GAAG,IAAI,CAAC3B,MAAM,CAAC8D,OAAO,CAC1D,uCAAuC,EACvCN,MAAM,CAC2B;IACrC;IAEAY,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;MAChE;MACA,IAAI,IAAI,CAAC9C,iBAAiB,IAAI4C,cAAc,EAAE;QAC5C,MAAMG,iBAAiB,GAAGH,cAAc,CACrCI,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCJ,iBAAiB,CAACK,OAAO,CAAEC,IAAI,IAC7BT,cAAc,CAACU,eAAe,CAACD,IAAI,CAAC,CACrC;QAED,IAAI,IAAI,CAACvD,8BAA8B,EAAE;UACvC,KAAK,MAAMyD,GAAG,IAAI,IAAI,CAACxD,iCAAiC,EAAE;YACxD,IAAI,IAAI,CAACA,iCAAiC,CAACyD,cAAc,CAACD,GAAG,CAAC,EAAE;cAC9DX,cAAc,CAACa,YAAY,CACzBF,GAAG,EACH,IAAI,CAACxD,iCAAiC,CAACwD,GAAG,CAAC,CAC5C;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAACtD,8BAA8B,EAAE;UACvC,KAAK,MAAMsD,GAAG,IAAI,IAAI,CAACrD,iCAAiC,EAAE;YACxD,IAAI,IAAI,CAACA,iCAAiC,CAACsD,cAAc,CAACD,GAAG,CAAC,EAAE;cAC9DX,cAAc,CAACa,YAAY,CACzBF,GAAG,EACH,IAAI,CAACrD,iCAAiC,CAACqD,GAAG,CAAC,CAC5C;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAd,YAAYA,CAACV,MAAe;IAC1B,IAAI,CAAChD,6BAA6B,GAAG,IAAI,CAACR,MAAM,CAAC8D,OAAO,CACtD,mCAAmC,EACnCN,MAAM,CACI;IACZ,IAAI,IAAI,CAAChD,6BAA6B,EAAE;MACtC,IAAI,CAACC,gCAAgC,GAAG,IAAI,CAACT,MAAM,CAAC8D,OAAO,CACzD,sCAAsC,EACtCN,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAC9C,+BAA+B,GAAG,IAAI,CAACV,MAAM,CAAC8D,OAAO,CACxD,qCAAqC,EACrCN,MAAM,CACI;IACZ,IAAI,IAAI,CAAC9C,+BAA+B,EAAE;MACxC,IAAI,CAACC,kCAAkC,GAAG,IAAI,CAACX,MAAM,CAAC8D,OAAO,CAC3D,wCAAwC,EACxCN,MAAM,CAC2B;IACrC;IAEAY,UAAU,CAAC,MAAK;MACd,MAAMe,aAAa,GAAGb,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;MAC9D;MACA,IAAI,IAAI,CAAChE,gBAAgB,IAAI4E,aAAa,EAAE;QAC1C,MAAMC,gBAAgB,GAAGD,aAAa,CACnCV,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCQ,gBAAgB,CAACP,OAAO,CAAEC,IAAI,IAAKK,aAAa,CAACJ,eAAe,CAACD,IAAI,CAAC,CAAC;QAEvE,IAAI,IAAI,CAACtE,6BAA6B,EAAE;UACtC,KAAK,MAAMwE,GAAG,IAAI,IAAI,CAACvE,gCAAgC,EAAE;YACvD,IAAI,IAAI,CAACA,gCAAgC,CAACwE,cAAc,CAACD,GAAG,CAAC,EAAE;cAC7DG,aAAa,CAACD,YAAY,CACxBF,GAAG,EACH,IAAI,CAACvE,gCAAgC,CAACuE,GAAG,CAAC,CAC3C;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAACtE,+BAA+B,EAAE;UACxC,KAAK,MAAMsE,GAAG,IAAI,IAAI,CAACrE,kCAAkC,EAAE;YACzD,IAAI,IAAI,CAACA,kCAAkC,CAACsE,cAAc,CAACD,GAAG,CAAC,EAAE;cAC/DG,aAAa,CAACD,YAAY,CACxBF,GAAG,EACH,IAAI,CAACrE,kCAAkC,CAACqE,GAAG,CAAC,CAC7C;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAhB,YAAYA,CAACR,MAAe;IAC1B,IAAI,CAAC5D,iBAAiB,GAAG,IAAI,CAACI,MAAM,CAAC8D,OAAO,CAAC,kBAAkB,EAAEN,MAAM,CAAW;IAClF,IAAI,CAACzB,kBAAkB,GAAG,IAAI,CAAC/B,MAAM,CAAC8D,OAAO,CAAC,sBAAsB,EAAEN,MAAM,CAAW;IACvF,IAAI,CAAC3D,0BAA0B,GAAG,IAAI,CAACG,MAAM,CAAC8D,OAAO,CAAC,2BAA2B,EAAEN,MAAM,CAAW;IACpG,IAAI,IAAI,CAACzB,kBAAkB,KAAK,OAAO,EAAE;MACvC,IAAI,CAAClC,0BAA0B,IAAI,gBAAgB;IACrD,CAAC,MAAM;MACL,IAAI,IAAI,CAACkC,kBAAkB,KAAK,OAAO,EAAE;QACvC,IAAI,CAAClC,0BAA0B,IAAI,kBAAkB;MACvD;IACF;IAEA,IAAI,CAACmC,qBAAqB,GAAG,IAAI,CAAChC,MAAM,CAAC8D,OAAO,CAAC,0BAA0B,EAAEN,MAAM,CAAY;IAC/F,IAAI,IAAI,CAACxB,qBAAqB,EAAE;MAC9BsC,QAAQ,CAACe,IAAI,CAACH,YAAY,CAAC,0BAA0B,EAAE,MAAM,CAAC;IAChE;IAEA,IAAI,CAACjD,oBAAoB,GAAG,IAAI,CAACjC,MAAM,CAAC8D,OAAO,CAAC,yBAAyB,CAAY;IACrF,IAAI,IAAI,CAAC7B,oBAAoB,EAAE;MAC7BqC,QAAQ,CAACe,IAAI,CAACH,YAAY,CAAC,iCAAiC,EAAE,MAAM,CAAC;IACvE;EACF;EAEAf,aAAaA,CAACX,MAAe;IAC3B,IAAI,CAAC7D,gBAAgB,GAAG,IAAI,CAACK,MAAM,CAAC8D,OAAO,CACzC,oBAAoB,EACpBN,MAAM,CACuD;IAC/D,IAAI,CAAC3C,qBAAqB,GAAG,IAAI,CAACb,MAAM,CAAC8D,OAAO,CAC9C,0BAA0B,EAC1BN,MAAM,CACI;IACZ,IAAI,IAAI,CAAC3C,qBAAqB,EAAE;MAC9B,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACd,MAAM,CAAC8D,OAAO,CACjD,6BAA6B,EAC7BN,MAAM,CAC2B;IACrC;IAEA,IAAI,CAACzC,uBAAuB,GAAG,IAAI,CAACf,MAAM,CAAC8D,OAAO,CAChD,4BAA4B,EAC5BN,MAAM,CACI;IACZ,IAAI,IAAI,CAACzC,uBAAuB,EAAE;MAChC,IAAI,CAACC,0BAA0B,GAAG,IAAI,CAAChB,MAAM,CAAC8D,OAAO,CACnD,+BAA+B,EAC/BN,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAC9D,kBAAkB,GACpB,IAAI,CAACM,MAAM,CAAC8D,OAAO,CAAC,mBAAmB,EAAEN,MAAM,CAAY,IAAI,EAAE;IACpE,IAAI,CAACvC,yBAAyB,GAAG,IAAI,CAACjB,MAAM,CAAC8D,OAAO,CAClD,8BAA8B,EAC9BN,MAAM,CACI;IACZ,IAAI,IAAI,CAACvC,yBAAyB,EAAE;MAClC,IAAI,CAACC,4BAA4B,GAAG,IAAI,CAAClB,MAAM,CAAC8D,OAAO,CACrD,iCAAiC,EACjCN,MAAM,CAC2B;MACnC,IAAI,CAAC9D,kBAAkB,IAAI,uBAAuB;IACpD;IAEA0E,UAAU,CAAC,MAAK;MACd,MAAMkB,cAAc,GAAGhB,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;MAChE;MACA,IAAI,IAAI,CAAC3D,iBAAiB,IAAI0E,cAAc,EAAE;QAC5C,MAAMC,iBAAiB,GAAGD,cAAc,CACrCb,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCW,iBAAiB,CAACV,OAAO,CAAEC,IAAI,IAC7BQ,cAAc,CAACP,eAAe,CAACD,IAAI,CAAC,CACrC;QAED,IAAI,IAAI,CAACjE,qBAAqB,EAAE;UAC9B,KAAK,MAAMmE,GAAG,IAAI,IAAI,CAAClE,wBAAwB,EAAE;YAC/C,IAAI,IAAI,CAACA,wBAAwB,CAACmE,cAAc,CAACD,GAAG,CAAC,EAAE;cACrDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAAClE,wBAAwB,CAACkE,GAAG,CAAC,CACnC;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAACjE,uBAAuB,EAAE;UAChC,KAAK,MAAMiE,GAAG,IAAI,IAAI,CAAChE,0BAA0B,EAAE;YACjD,IAAI,IAAI,CAACA,0BAA0B,CAACiE,cAAc,CAACD,GAAG,CAAC,EAAE;cACvDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAAChE,0BAA0B,CAACgE,GAAG,CAAC,CACrC;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAAC/D,yBAAyB,EAAE;UAClC,KAAK,MAAM+D,GAAG,IAAI,IAAI,CAAC9D,4BAA4B,EAAE;YACnD,IAAI,IAAI,CAACA,4BAA4B,CAAC+D,cAAc,CAACD,GAAG,CAAC,EAAE;cACzDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAAC9D,4BAA4B,CAAC8D,GAAG,CAAC,CACvC;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAACpF,WAAW,CAACyE,OAAO,CAAEY,EAAE,IAAKA,EAAE,CAACrF,WAAW,EAAE,CAAC;EACpD;;qCAxYWN,eAAe,EAAAhB,EAAA,CAAA4G,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA9G,EAAA,CAAA4G,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAhH,EAAA,CAAA4G,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAlH,EAAA,CAAA4G,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAnH,EAAA,CAAA4G,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA;EAAA;;UAAfrG,eAAe;IAAAsG,SAAA;IAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;QClB5BzH,EAAA,CAAA2H,UAAA,IAAAC,8BAAA,iBAA0D;QAYxD5H,EAFF,CAAAC,cAAA,aAAoE,aAEG;QACnED,EAAA,CAAA2H,UAAA,IAAAE,uCAAA,0BAAuC;QAQvC7H,EAAA,CAAAC,cAAA,aAAwE;QAStED,EAPA,CAAA2H,UAAA,IAAAG,uCAAA,0BAAwC,IAAAC,uCAAA,0BAOK;QAM3C/H,EAFF,CAAAC,cAAA,aAAkE,aAEd;QAChDD,EAAA,CAAA2H,UAAA,IAAAK,uCAAA,0BAAwC;QAIxChI,EAAA,CAAAK,SAAA,sBAGc;QAChBL,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAA2H,UAAA,KAAAM,wCAAA,0BAAuC;QAW/CjI,EAPM,CAAAG,YAAA,EAAM,EAEF,EAGF,EAEF;QAGNH,EAAA,CAAAK,SAAA,wBAAqC;QACrCL,EAAA,CAAAI,uBAAA,IAAc;QACZJ,EAAA,CAAAK,SAAA,yBAA8F;;QAchGL,EAXA,CAAAK,SAAA,2BAA2C,4BACE,sBAQZ,8BACgB,8BACA;;;QA7E3CL,EAAA,CAAAO,UAAA,SAAAmH,GAAA,CAAAnG,SAAA,CAAe;QAaFvB,EAAA,CAAAM,SAAA,GAAsB;QAAtBN,EAAA,CAAAO,UAAA,SAAAmH,GAAA,CAAAjG,gBAAA,CAAsB;QAUpBzB,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAO,UAAA,SAAAmH,GAAA,CAAA/E,iBAAA,CAAuB;QAOvB3C,EAAA,CAAAM,SAAA,EAA4B;QAA5BN,EAAA,CAAAO,UAAA,SAAAmH,GAAA,CAAA3E,sBAAA,CAA4B;QAOxB/C,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAO,UAAA,SAAAmH,GAAA,CAAA5F,iBAAA,CAAuB;QAIgB9B,EAAA,CAAAM,SAAA,EAA6B;QAEjFN,EAFoD,CAAAO,UAAA,YAAAmH,GAAA,CAAAnF,iBAAA,CAA6B,6BAAAmF,GAAA,CAAAlF,wBAAA,CAC5B,uBAAAkF,GAAA,CAAArF,kBAAA,CAA0C,6BAAAqF,GAAA,CAAApF,wBAAA,CAC1C;QAI1CtC,EAAA,CAAAM,SAAA,EAAsB;QAAtBN,EAAA,CAAAO,UAAA,SAAAmH,GAAA,CAAA1E,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}