{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"../services/http-utils.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = [\"root\", \"\"];\nfunction AuthComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 8);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n// const BODY_CLASSES = ['bgi-size-cover', 'bgi-position-center', 'bgi-no-repeat'];\nexport let AuthComponent = /*#__PURE__*/(() => {\n  class AuthComponent {\n    titleService;\n    httpUtilService;\n    today = new Date();\n    isLoading = false;\n    loadingSubscription = new Subscription();\n    constructor(titleService, httpUtilService) {\n      this.titleService = titleService;\n      this.httpUtilService = httpUtilService;\n    }\n    ngOnInit() {\n      // BODY_CLASSES.forEach((c) => document.body.classList.add(c));\n      // Default title for auth wrapper; individual pages can override\n      this.titleService.setTitle('Permit Tracker');\n      // Subscribe to loading state changes\n      this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n        this.isLoading = loading === true;\n      });\n    }\n    ngOnDestroy() {\n      // BODY_CLASSES.forEach((c) => document.body.classList.remove(c));\n      this.loadingSubscription.unsubscribe();\n    }\n    static ɵfac = function AuthComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthComponent)(i0.ɵɵdirectiveInject(i1.Title), i0.ɵɵdirectiveInject(i2.HttpUtilsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AuthComponent,\n      selectors: [[\"body\", \"root\", \"\"]],\n      attrs: _c0,\n      decls: 5,\n      vars: 1,\n      consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"flex-column-fluid\", \"bgi-position-y-bottom\", \"position-x-center\", \"bgi-no-repeat\", \"bgi-size-contain\", \"bgi-attachment-fixed\", 2, \"background-color\", \"#d4d5d5\"], [1, \"d-flex\", \"flex-center\", \"flex-column\", \"flex-column-fluid\", \"p-10\", \"pb-lg-20\"], [1, \"w-lg-450px\", \"bg-body\", \"rounded\", \"shadow-sm\", \"p-10\", \"p-lg-15\", \"mx-auto\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"]],\n      template: function AuthComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AuthComponent_div_0_Template, 7, 0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgIf, i4.RouterOutlet],\n      styles: [\"[_nghost-%COMP%]{height:100%}\"]\n    });\n  }\n  return AuthComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}