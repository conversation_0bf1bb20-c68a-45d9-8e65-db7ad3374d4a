{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AuthRoutingModule } from './auth-routing.module';\nimport { LoginComponent } from './components/login/login.component';\nimport { RegistrationComponent } from './components/registration/registration.component';\nimport { ForgotPasswordComponent } from './components/forgot-password/forgot-password.component';\nimport { LogoutComponent } from './components/logout/logout.component';\nimport { AuthComponent } from './auth.component';\nimport { TranslationModule } from '../i18n/translation.module';\nimport { ChangePasswordComponent } from './components/change-password/change-password.component';\nimport { PasswordStrengthComponent } from './components/password-strength/password-strength.component';\nimport { HttpUtilsService } from '../services/http-utils.service';\nimport * as i0 from \"@angular/core\";\nexport class AuthModule {\n  static ɵfac = function AuthModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [HttpUtilsService],\n    imports: [CommonModule, TranslationModule, AuthRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [LoginComponent, RegistrationComponent, ForgotPasswordComponent, LogoutComponent, ChangePasswordComponent, AuthComponent, PasswordStrengthComponent],\n    imports: [CommonModule, TranslationModule, AuthRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "HttpClientModule", "AuthRoutingModule", "LoginComponent", "RegistrationComponent", "ForgotPasswordComponent", "LogoutComponent", "AuthComponent", "TranslationModule", "ChangePasswordComponent", "PasswordStrengthComponent", "HttpUtilsService", "AuthModule", "imports", "declarations"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\auth\\auth.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { AuthRoutingModule } from './auth-routing.module';\r\nimport { LoginComponent } from './components/login/login.component';\r\nimport { RegistrationComponent } from './components/registration/registration.component';\r\nimport { ForgotPasswordComponent } from './components/forgot-password/forgot-password.component';\r\nimport { LogoutComponent } from './components/logout/logout.component';\r\nimport { AuthComponent } from './auth.component';\r\nimport { TranslationModule } from '../i18n/translation.module';\r\nimport { ChangePasswordComponent } from './components/change-password/change-password.component';\r\nimport { PasswordStrengthComponent } from './components/password-strength/password-strength.component';\r\nimport { HttpUtilsService } from '../services/http-utils.service';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    LoginComponent,\r\n    RegistrationComponent,\r\n    ForgotPasswordComponent,\r\n    LogoutComponent,\r\n    ChangePasswordComponent,\r\n    AuthComponent,\r\n    PasswordStrengthComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    TranslationModule,\r\n    AuthRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    HttpClientModule,\r\n  ],\r\n  providers: [HttpUtilsService],\r\n})\r\nexport class AuthModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,qBAAqB,QAAQ,kDAAkD;AACxF,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,yBAAyB,QAAQ,4DAA4D;AACtG,SAASC,gBAAgB,QAAQ,gCAAgC;;AAsBjE,OAAM,MAAOC,UAAU;;qCAAVA,UAAU;EAAA;;UAAVA;EAAU;;eAFV,CAACD,gBAAgB,CAAC;IAAAE,OAAA,GAP3Bf,YAAY,EACZU,iBAAiB,EACjBN,iBAAiB,EACjBF,WAAW,EACXD,mBAAmB,EACnBE,gBAAgB;EAAA;;;2EAIPW,UAAU;IAAAE,YAAA,GAlBnBX,cAAc,EACdC,qBAAqB,EACrBC,uBAAuB,EACvBC,eAAe,EACfG,uBAAuB,EACvBF,aAAa,EACbG,yBAAyB;IAAAG,OAAA,GAGzBf,YAAY,EACZU,iBAAiB,EACjBN,iBAAiB,EACjBF,WAAW,EACXD,mBAAmB,EACnBE,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}