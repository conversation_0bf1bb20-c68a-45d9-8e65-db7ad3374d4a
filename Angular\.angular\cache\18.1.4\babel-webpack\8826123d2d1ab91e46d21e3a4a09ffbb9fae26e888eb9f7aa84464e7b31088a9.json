{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"../services/http-utils.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = [\"root\", \"\"];\nfunction AuthComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 8);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n// const BODY_CLASSES = ['bgi-size-cover', 'bgi-position-center', 'bgi-no-repeat'];\nexport class AuthComponent {\n  titleService;\n  httpUtilService;\n  today = new Date();\n  isLoading = false;\n  loadingSubscription = new Subscription();\n  constructor(titleService, httpUtilService) {\n    this.titleService = titleService;\n    this.httpUtilService = httpUtilService;\n  }\n  ngOnInit() {\n    // BODY_CLASSES.forEach((c) => document.body.classList.add(c));\n    // Default title for auth wrapper; individual pages can override\n    this.titleService.setTitle('Permit Tracker');\n    // Subscribe to loading state changes\n    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading === true;\n    });\n  }\n  ngOnDestroy() {\n    // BODY_CLASSES.forEach((c) => document.body.classList.remove(c));\n    this.loadingSubscription.unsubscribe();\n  }\n  static ɵfac = function AuthComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthComponent)(i0.ɵɵdirectiveInject(i1.Title), i0.ɵɵdirectiveInject(i2.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AuthComponent,\n    selectors: [[\"body\", \"root\", \"\"]],\n    attrs: _c0,\n    decls: 5,\n    vars: 1,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"flex-column-fluid\", \"bgi-position-y-bottom\", \"position-x-center\", \"bgi-no-repeat\", \"bgi-size-contain\", \"bgi-attachment-fixed\", 2, \"background-color\", \"#d4d5d5\"], [1, \"d-flex\", \"flex-center\", \"flex-column\", \"flex-column-fluid\", \"p-10\", \"pb-lg-20\"], [1, \"w-lg-450px\", \"bg-body\", \"rounded\", \"shadow-sm\", \"p-10\", \"p-lg-15\", \"mx-auto\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"]],\n    template: function AuthComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AuthComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"router-outlet\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      }\n    },\n    dependencies: [i3.NgIf, i4.RouterOutlet],\n    styles: [\"[_nghost-%COMP%] {\\n  height: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9hdXRoL2F1dGguY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XG4gIGhlaWdodDogMTAwJTtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AuthComponent", "titleService", "httpUtilService", "today", "Date", "isLoading", "loadingSubscription", "constructor", "ngOnInit", "setTitle", "loadingSubject", "subscribe", "loading", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "Title", "i2", "HttpUtilsService", "selectors", "attrs", "_c0", "decls", "vars", "consts", "template", "AuthComponent_Template", "rf", "ctx", "ɵɵtemplate", "AuthComponent_div_0_Template", "ɵɵelement", "ɵɵproperty"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\auth\\auth.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\auth\\auth.component.html"], "sourcesContent": ["import { Component, OnDestroy, OnInit } from '@angular/core';\nimport { Title } from '@angular/platform-browser';\nimport { HttpUtilsService } from '../services/http-utils.service';\nimport { Subscription } from 'rxjs';\n\n// const BODY_CLASSES = ['bgi-size-cover', 'bgi-position-center', 'bgi-no-repeat'];\n\n@Component({\n  // eslint-disable-next-line @angular-eslint/component-selector\n  selector: '<body[root]>',\n  templateUrl: './auth.component.html',\n  styleUrls: ['./auth.component.scss'],\n})\nexport class AuthComponent implements OnInit, OnDestroy {\n  today: Date = new Date();\n  isLoading: boolean = false;\n  private loadingSubscription: Subscription = new Subscription();\n\n  constructor(\n    private titleService: Title,\n    private httpUtilService: HttpUtilsService\n  ) {}\n\n  ngOnInit(): void {\n    // BODY_CLASSES.forEach((c) => document.body.classList.add(c));\n    // Default title for auth wrapper; individual pages can override\n    this.titleService.setTitle('Permit Tracker');\n    \n    // Subscribe to loading state changes\n    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(\n      (loading) => {\n        this.isLoading = loading === true;\n      }\n    );\n  }\n\n  ngOnDestroy() {\n    // BODY_CLASSES.forEach((c) => document.body.classList.remove(c));\n    this.loadingSubscription.unsubscribe();\n  }\n}\n", "<!-- Global Loading Overlay for Auth Pages -->\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"custom-colored-spinner\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<div\n  class=\"\n    d-flex\n    flex-column flex-column-fluid\n    bgi-position-y-bottom\n    position-x-center\n    bgi-no-repeat bgi-size-contain bgi-attachment-fixed\n  \"\n  style=\"background-color: #d4d5d5;\"\n>\n  <!--begin::Content-->\n  <div class=\"d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20\">\n\n    <!--begin::Content body-->\n    <div class=\"w-lg-450px bg-body rounded shadow-sm p-10 p-lg-15 mx-auto\">\n      <router-outlet></router-outlet>\n    </div>\n    <!--end::Content body-->\n  </div>\n  <!--end::Content-->\n\n</div>\n"], "mappings": "AAGA,SAASA,YAAY,QAAQ,MAAM;;;;;;;;;ICC7BC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;ADHN;AAQA,OAAM,MAAOC,aAAa;EAMdC,YAAA;EACAC,eAAA;EANVC,KAAK,GAAS,IAAIC,IAAI,EAAE;EACxBC,SAAS,GAAY,KAAK;EAClBC,mBAAmB,GAAiB,IAAIX,YAAY,EAAE;EAE9DY,YACUN,YAAmB,EACnBC,eAAiC;IADjC,KAAAD,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;EACtB;EAEHM,QAAQA,CAAA;IACN;IACA;IACA,IAAI,CAACP,YAAY,CAACQ,QAAQ,CAAC,gBAAgB,CAAC;IAE5C;IACA,IAAI,CAACH,mBAAmB,GAAG,IAAI,CAACJ,eAAe,CAACQ,cAAc,CAACC,SAAS,CACrEC,OAAO,IAAI;MACV,IAAI,CAACP,SAAS,GAAGO,OAAO,KAAK,IAAI;IACnC,CAAC,CACF;EACH;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACP,mBAAmB,CAACQ,WAAW,EAAE;EACxC;;qCA1BWd,aAAa,EAAAJ,EAAA,CAAAmB,iBAAA,CAAAC,EAAA,CAAAC,KAAA,GAAArB,EAAA,CAAAmB,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;;UAAbnB,aAAa;IAAAoB,SAAA;IAAAC,KAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ1BhC,EAAA,CAAAkC,UAAA,IAAAC,4BAAA,iBAA0D;QAuBtDnC,EAdJ,CAAAC,cAAA,aASC,aAE6E,aAGH;QACrED,EAAA,CAAAoC,SAAA,oBAA+B;QAMrCpC,EALI,CAAAG,YAAA,EAAM,EAEF,EAGF;;;QA9BAH,EAAA,CAAAqC,UAAA,SAAAJ,GAAA,CAAAxB,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}