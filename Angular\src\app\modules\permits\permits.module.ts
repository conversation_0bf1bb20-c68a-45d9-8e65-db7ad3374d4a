import {
  NgModule,
  NO_ERRORS_SCHEMA,
  CUSTOM_ELEMENTS_SCHEMA,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { PermitsComponent } from './permits.component';
import { PermitEditComponent } from './permit-edit/permit-edit.component';
import { PermitListComponent } from './permit-list/permit-list.component';
import { PermitViewComponent } from './permit-view/permit-view.component';
import { PermitsRoutingModule } from './permits-routing.module';
import { SharedModule } from '../shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { GridModule } from '@progress/kendo-angular-grid';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { PermitPopupComponent } from './permit-popup/permit-popup.component';
import { NgbModal, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';

import { ReviewDetailsModalComponent } from './review-details-modal/review-details-modal.component';
import { AddEditInternalReviewComponent } from './add-edit-internal-review/add-edit-internal-review.component';
import { EditExternalReviewComponent } from './edit-external-review/edit-external-review.component';
import { ResponseModalComponent } from './response-modal/response-modal.component';

@NgModule({
  declarations: [
    PermitsComponent,
    PermitListComponent,
    PermitEditComponent,
    PermitPopupComponent,
    PermitViewComponent,
    ReviewDetailsModalComponent,
    AddEditInternalReviewComponent,
    EditExternalReviewComponent,
    ResponseModalComponent
  ],
  imports: [
    CommonModule,
    PermitsRoutingModule,
    SharedModule,
    FormsModule,
    GridModule,
    InputsModule,
    DropDownsModule,
    ButtonsModule,
    DateInputsModule,
    InlineSVGModule,
    ReactiveFormsModule, // ✅ Needed for [formGroup]
    NgbModule,

    NgSelectModule,
  ],
  schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
})
export class PermitsModule {}
