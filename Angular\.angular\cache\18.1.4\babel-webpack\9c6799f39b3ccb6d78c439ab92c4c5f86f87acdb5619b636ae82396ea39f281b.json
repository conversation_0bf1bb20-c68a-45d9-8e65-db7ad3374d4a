{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport { ReviewDetailsModalComponent } from '../review-details-modal/review-details-modal.component';\nimport { ResponseModalComponent } from '../response-modal/response-modal.component';\nimport { AddEditInternalReviewComponent } from '../add-edit-internal-review/add-edit-internal-review.component';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport { EditExternalReviewComponent } from '../edit-external-review/edit-external-review.component';\nimport { AppService } from '../../services/app.service';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport { autoTable } from 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../../services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/permits.service\";\nimport * as i7 from \"../../services/http-utils.service\";\nimport * as i8 from \"@angular/common\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 8);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_li_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_li_20_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showTab(\"internal\", $event));\n    });\n    i0.ɵɵtext(2, \" Internal Reviews \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r1.selectedTab === \"internal\"));\n  }\n}\nfunction PermitViewComponent_div_2_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_li_21_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showTab(\"external\", $event));\n    });\n    i0.ɵɵtext(2, \" External Reviews \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r1.selectedTab === \"external\"));\n  }\n}\nfunction PermitViewComponent_div_2_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editPermit());\n    });\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitViewComponent_div_2_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadInternalReviewsPdf());\n    });\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_24_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addPopUp());\n    });\n    i0.ɵɵelement(4, \"i\", 35);\n    i0.ɵɵtext(5, \"Add Review \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.auditEntries.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_25_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleAllSubmittals());\n    });\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"span\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_25_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.syncPermits(true));\n    });\n    i0.ɵɵelement(6, \"i\", 40);\n    i0.ɵɵtext(7, \" Sync \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_25_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPortal());\n    });\n    i0.ɵɵelement(9, \"i\", 42);\n    i0.ɵɵtext(10, \" Portal \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.externalSubmittals.length === 0)(\"title\", ctx_r1.areAllSubmittalsExpanded() ? \"Collapse all submittals\" : \"Expand all submittals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.areAllSubmittalsExpanded() ? \"fa-compress-arrows-alt\" : \"fa-expand-arrows-alt\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.areAllSubmittalsExpanded() ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r1.permit == null ? null : ctx_r1.permit.permitEntityID))(\"title\", (ctx_r1.permit == null ? null : ctx_r1.permit.permitEntityID) ? \"Open Portal\" : \"Portal not available - Permit Entity ID required\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 43)(2, \"div\", 44)(3, \"div\", 45)(4, \"label\");\n    i0.ɵɵtext(5, \"Project Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 45)(9, \"label\");\n    i0.ɵɵtext(10, \"Permit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 46);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 45)(14, \"label\");\n    i0.ɵɵtext(15, \"Primary Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 46);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 45)(19, \"label\");\n    i0.ɵɵtext(20, \"Permit Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 47);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 45)(24, \"label\");\n    i0.ɵɵtext(25, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 46);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 45)(29, \"label\");\n    i0.ɵɵtext(30, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 46);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 45)(34, \"label\");\n    i0.ɵɵtext(35, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 46);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\", 48);\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 45)(43, \"label\");\n    i0.ɵɵtext(44, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"span\", 46);\n    i0.ɵɵtext(46);\n    i0.ɵɵpipe(47, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 45)(49, \"label\");\n    i0.ɵɵtext(50, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 46);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 45)(55, \"label\");\n    i0.ɵɵtext(56, \"Complete Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 46);\n    i0.ɵɵtext(58);\n    i0.ɵɵpipe(59, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 45)(61, \"label\");\n    i0.ɵɵtext(62, \"internal Review Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"span\", 47);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(65, \"div\", 49)(66, \"div\", 10)(67, \"h4\");\n    i0.ɵɵtext(68, \"Notes / Action\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_27_Template_button_click_69_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      const notesActionsTemplate_r9 = i0.ɵɵreference(4);\n      return i0.ɵɵresetView(ctx_r1.onEdit(notesActionsTemplate_r9));\n    });\n    i0.ɵɵelement(70, \"i\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 43)(72, \"div\", 51)(73, \"div\", 52)(74, \"label\");\n    i0.ɵɵtext(75, \"Attention Reason\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"span\", 46);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 52)(79, \"label\");\n    i0.ɵɵtext(80, \"Internal Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"span\", 46);\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 52)(84, \"label\");\n    i0.ɵɵtext(85, \"Action Taken\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"span\", 46);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.projectName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitType || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.primaryContact || \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.permit.permitStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitStatus || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.location || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitCategory || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitIssueDate ? i0.ɵɵpipeBind2(38, 17, ctx_r1.permit.permitIssueDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Applied on \", ctx_r1.permit.permitAppliedDate ? i0.ɵɵpipeBind2(41, 20, ctx_r1.permit.permitAppliedDate, \"MM/dd/yyyy\") : \"\", \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitExpirationDate ? i0.ɵɵpipeBind2(47, 23, ctx_r1.permit.permitExpirationDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitFinalDate ? i0.ɵɵpipeBind2(53, 26, ctx_r1.permit.permitFinalDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitCompleteDate ? i0.ɵɵpipeBind2(59, 29, ctx_r1.permit.permitCompleteDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.permit.internalReviewStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.permit.internalReviewStatus || \"\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.attentionReason || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.internalNotes || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.actionTaken || \"\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56);\n    i0.ɵɵelement(2, \"i\", 57);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No internal reviews found for this permit.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 61);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template_tr_click_0_listener() {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectAudit(i_r11));\n    });\n    i0.ɵɵelementStart(1, \"td\", 62)(2, \"div\", 63)(3, \"h6\", 64);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 65);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 66)(8, \"small\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"td\", 68)(11, \"div\", 69)(12, \"div\", 70)(13, \"small\", 71);\n    i0.ɵɵtext(14, \"Reviewed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 72);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 70)(19, \"small\", 71);\n    i0.ɵɵtext(20, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 72);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 70)(25, \"small\", 71);\n    i0.ɵɵtext(26, \"Reviewer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 72);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"td\", 73)(30, \"i\", 74);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template_i_click_30_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      ctx_r1.editInternalReview(i_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const audit_r12 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"table-active\", ctx_r1.selectedAuditIndex === i_r11);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(audit_r12.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(audit_r12.internalVerificationStatus))(\"ngStyle\", ctx_r1.getStatusStyle(audit_r12.internalVerificationStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", audit_r12.internalVerificationStatus || \"Pending\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(audit_r12.typeCodeDrawing || \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(audit_r12.reviewedDate ? i0.ɵɵpipeBind2(17, 10, audit_r12.reviewedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(audit_r12.completedDate ? i0.ɵɵpipeBind2(23, 13, audit_r12.completedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(audit_r12.internalReviewer || \"\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_28_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"table\", 59)(2, \"tbody\");\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template, 31, 16, \"tr\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.auditEntries);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitViewComponent_div_2_ng_container_28_div_1_Template, 5, 0, \"div\", 53)(2, PermitViewComponent_div_2_ng_container_28_div_2_Template, 4, 1, \"div\", 54);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.auditEntries.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.auditEntries.length > 0);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 78)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\", 79);\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.fetchExternalReviews());\n    });\n    i0.ɵɵelement(7, \"i\", 81);\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.reviewsError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 55)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\", 82);\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"No external reviews found for this permit.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const correction_r19 = i0.ɵɵnextContext().$implicit;\n      const review_r17 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.openResponseModal(correction_r19, review_r17));\n    });\n    i0.ɵɵtext(1, \" Respond \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const correction_r19 = i0.ɵɵnextContext().$implicit;\n      const review_r17 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.openResponseModal(correction_r19, review_r17));\n    });\n    i0.ɵɵtext(1, \" Edit Response \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 140);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r19.Response, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" EOR / AOR / Owner Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 141);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r19.EORAOROwner_Response, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" Comment Responded By \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r19.commentResponsedBy, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 143);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 120)(2, \"div\", 121);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 122)(5, \"div\", 123)(6, \"div\", 124)(7, \"span\", 125);\n    i0.ɵɵtext(8, \"Correction Type: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 126);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 124)(12, \"span\", 125);\n    i0.ɵɵtext(13, \"Category: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 126);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 124)(17, \"span\", 125);\n    i0.ɵɵtext(18, \"Resolved: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 127);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 128);\n    i0.ɵɵtemplate(23, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template, 2, 1, \"button\", 129)(24, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template, 2, 1, \"button\", 130);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 131)(26, \"div\", 132)(27, \"label\", 133);\n    i0.ɵɵtext(28, \" Corrective Action \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 134);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 132)(32, \"label\", 133);\n    i0.ɵɵtext(33, \" Comment \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 135);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_div_36_Template, 5, 1, \"div\", 136)(37, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_div_37_Template, 5, 1, \"div\", 136)(38, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_div_38_Template, 5, 1, \"div\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_div_39_Template, 1, 0, \"div\", 137);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const correction_r19 = ctx.$implicit;\n    const i_r21 = ctx.index;\n    const review_r17 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r21 + 1);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(correction_r19.CorrectionTypeName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r19.CorrectionCategoryName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r19.ResolvedDate ? i0.ɵɵpipeBind2(21, 12, correction_r19.ResolvedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !correction_r19.EORAOROwner_Response && !correction_r19.commentResponsedBy && ctx_r1.shouldShowEditResponseButton(correction_r19));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (correction_r19.EORAOROwner_Response || correction_r19.commentResponsedBy) && ctx_r1.shouldShowEditResponseButton(correction_r19));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", correction_r19.CorrectiveAction || \"\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", correction_r19.Comments || \"No comment provided\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r19.Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r19.EORAOROwner_Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r19.commentResponsedBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r21 < review_r17.corrections.length - 1);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"h6\", 117);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_div_3_Template, 40, 15, \"div\", 118);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const review_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Corrections (\", review_r17.corrections.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", review_r17.corrections);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"h6\", 117);\n    i0.ɵɵtext(2, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 145)(4, \"div\", 146);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const review_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(review_r17.comments);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"div\", 148);\n    i0.ɵɵelement(2, \"i\", 149);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \" No corrections or comments available for this review.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template_div_click_1_listener() {\n      const review_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.toggleReviewAccordion(review_r17.commentsId));\n    });\n    i0.ɵɵelementStart(2, \"div\", 99);\n    i0.ɵɵelement(3, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h6\", 100);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 101)(7, \"span\", 102);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 103)(10, \"span\", 104);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 105)(13, \"span\", 106);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 107)(17, \"span\", 108);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 109)(21, \"i\", 110);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template_i_click_21_listener($event) {\n      const review_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      ctx_r1.downloadReviewPDF(review_r17);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 111)(23, \"div\", 112);\n    i0.ɵɵtemplate(24, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_Template, 4, 2, \"div\", 113)(25, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_25_Template, 6, 1, \"div\", 114)(26, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_26_Template, 5, 0, \"div\", 115);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const review_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isReviewExpanded(review_r17.commentsId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isReviewExpanded(review_r17.commentsId) ? \"fa-chevron-down\" : \"fa-chevron-right\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", review_r17.FailureFlag ? \"red\" : \"green\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", review_r17.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(review_r17.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", review_r17.status || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(review_r17.reviewer || \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", review_r17.dueDate ? \"Due: \" + i0.ɵɵpipeBind2(15, 16, review_r17.dueDate, \"MM/dd/yyyy\") : \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", review_r17.completedDate ? \"Completed: \" + i0.ɵɵpipeBind2(19, 19, review_r17.completedDate, \"MM/dd/yyyy\") : \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"d-none\", !ctx_r1.isReviewExpanded(review_r17.commentsId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (review_r17 == null ? null : review_r17.corrections) && review_r17.corrections.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(review_r17 == null ? null : review_r17.corrections) || review_r17.corrections.length === 0) && (review_r17 == null ? null : review_r17.comments));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(review_r17 == null ? null : review_r17.corrections) || review_r17.corrections.length === 0) && !(review_r17 == null ? null : review_r17.comments));\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\", 87);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_Template_tr_click_1_listener() {\n      const i_r15 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.toggleSubmittalAccordion(i_r15));\n    });\n    i0.ɵɵelementStart(2, \"td\", 88)(3, \"div\", 63)(4, \"div\", 89);\n    i0.ɵɵelement(5, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h6\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 90);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"td\", 91)(11, \"div\", 69)(12, \"div\", 70)(13, \"small\", 71);\n    i0.ɵɵtext(14, \"Due\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 72);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 70)(19, \"small\", 71);\n    i0.ɵɵtext(20, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 72);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"tr\", 92)(25, \"td\", 93)(26, \"div\", 94)(27, \"div\", 95);\n    i0.ɵɵtemplate(28, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template, 27, 22, \"div\", 96);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const sub_r22 = ctx.$implicit;\n    const i_r15 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isSubmittalExpanded(i_r15));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isSubmittalExpanded(i_r15) ? \"fa-chevron-down\" : \"fa-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sub_r22.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(sub_r22.submittalStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", sub_r22.submittalStatus, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 11, sub_r22.dueDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 14, sub_r22.receivedDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"d-none\", !ctx_r1.isSubmittalExpanded(i_r15));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getExternalReviewsForSubmittal(sub_r22.id));\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84)(2, \"table\", 85)(3, \"tbody\");\n    i0.ɵɵtemplate(4, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_Template, 29, 17, \"ng-container\", 86);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.externalSubmittals);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 75)(2, \"div\", 9);\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_29_div_3_Template, 9, 2, \"div\", 76)(4, PermitViewComponent_div_2_ng_container_29_div_4_Template, 6, 0, \"div\", 76)(5, PermitViewComponent_div_2_ng_container_29_div_5_Template, 5, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.reviewsError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reviewsError && ctx_r1.externalSubmittals.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reviewsError && ctx_r1.externalSubmittals.length > 0);\n  }\n}\nfunction PermitViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"div\", 12)(4, \"div\", 13)(5, \"span\", 14);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 17)(12, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(13, \"i\", 19);\n    i0.ɵɵtext(14, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"ul\", 21)(17, \"li\", 22)(18, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_a_click_18_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(19, \" Permit Details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, PermitViewComponent_div_2_li_20_Template, 3, 3, \"li\", 24)(21, PermitViewComponent_div_2_li_21_Template, 3, 3, \"li\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 25);\n    i0.ɵɵtemplate(23, PermitViewComponent_div_2_button_23_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, PermitViewComponent_div_2_div_24_Template, 6, 2, \"div\", 27)(25, PermitViewComponent_div_2_div_25_Template, 11, 7, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 28);\n    i0.ɵɵtemplate(27, PermitViewComponent_div_2_ng_container_27_Template, 88, 32, \"ng-container\", 29)(28, PermitViewComponent_div_2_ng_container_28_Template, 3, 2, \"ng-container\", 29)(29, PermitViewComponent_div_2_ng_container_29_Template, 6, 3, \"ng-container\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Permit # \", ctx_r1.permit.permitNumber || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitReviewType || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.permit.permitName || \"\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isExternalReviewEnabled());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"details\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"internal\" && ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"external\" && ctx_r1.isExternalReviewEnabled());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.permit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"internal\" && ctx_r1.permit && ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"external\" && ctx_r1.permit && ctx_r1.isExternalReviewEnabled());\n  }\n}\nfunction PermitViewComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 150)(1, \"div\", 151)(2, \"div\", 152);\n    i0.ɵɵelementContainerStart(3);\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5, \"Edit Notes/Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 153)(7, \"i\", 154);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_i_click_7_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closModal());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 155)(9, \"form\", 156)(10, \"div\", 157)(11, \"div\", 158)(12, \"div\", 159)(13, \"label\", 160);\n    i0.ɵɵtext(14, \" Attention Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"textarea\", 161);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 157)(17, \"div\", 158)(18, \"div\", 159)(19, \"label\", 162);\n    i0.ɵɵtext(20, \" Internal Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 163);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 157)(23, \"div\", 158)(24, \"div\", 159)(25, \"label\", 164);\n    i0.ɵɵtext(26, \" Action Taken \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"textarea\", 165);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(28, \"div\", 166)(29, \"div\")(30, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closModal());\n    });\n    i0.ɵɵtext(31, \" Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \"\\u00A0 \");\n    i0.ɵɵelementStart(33, \"button\", 168);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editNotesandactions());\n    });\n    i0.ɵɵtext(34, \"Update \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.notesForm);\n  }\n}\nexport let PermitViewComponent = /*#__PURE__*/(() => {\n  class PermitViewComponent {\n    route;\n    router;\n    modalService;\n    cdr;\n    fb;\n    appService;\n    customLayoutUtilsService;\n    permitsService;\n    httpUtilService;\n    notesForm;\n    permitId = null;\n    permit = null;\n    isLoading = false; // Main page loader\n    auditEntries = [];\n    selectedAuditIndex = 0; // Set to 0 to make first item initially active\n    selectedAuditName = '';\n    selectedAuditStatus = '';\n    selectedTab = 'details';\n    permitReviewType = '';\n    externalReviews = [];\n    reviewsError = '';\n    externalSubmittals = [];\n    selectedExternalSubmittalId = null;\n    internalReviews = [];\n    loginUser = {};\n    isAdmin = false;\n    singlePermit;\n    expandedSubmittals = new Set();\n    expandedReviews = new Set();\n    reviewSelectedTabs = {};\n    routeSubscription = new Subscription();\n    queryParamsSubscription = new Subscription();\n    loadingSubscription = new Subscription();\n    statusList = [{\n      text: 'Pending',\n      value: 'Pending'\n    }, {\n      text: 'In Progress',\n      value: 'In Progress'\n    }, {\n      text: 'Completed',\n      value: 'Completed'\n    }, {\n      text: 'On Hold',\n      value: 'On Hold'\n    }];\n    // Navigation tracking\n    previousPage = 'permit-list'; // Default fallback\n    projectId = null;\n    constructor(route, router, modalService, cdr, fb,\n    // private modal: NgbActiveModal,\n    appService, customLayoutUtilsService, permitsService, httpUtilService) {\n      this.route = route;\n      this.router = router;\n      this.modalService = modalService;\n      this.cdr = cdr;\n      this.fb = fb;\n      this.appService = appService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.permitsService = permitsService;\n      this.httpUtilService = httpUtilService;\n    }\n    ngOnInit() {\n      this.loginUser = this.appService.getLoggedInUser();\n      this.isAdmin = this.checkIfAdmin();\n      // Subscribe to global loading state\n      this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n        this.isLoading = loading === true;\n      });\n      // Read query parameters for navigation tracking\n      this.queryParamsSubscription = this.route.queryParams.subscribe(params => {\n        this.previousPage = params['from'] || 'permit-list';\n        this.projectId = params['projectId'] ? Number(params['projectId']) : null;\n        console.log('Permit view - query params:', {\n          previousPage: this.previousPage,\n          projectId: this.projectId\n        });\n      });\n      // Listen for route parameter changes\n      this.routeSubscription = this.route.paramMap.subscribe(params => {\n        const idParam = params.get('id');\n        this.permitId = idParam ? Number(idParam) : null;\n        if (this.permitId) {\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n          this.fetchInternalReviews();\n        }\n      });\n      this.loadForm();\n    }\n    loadForm() {\n      this.notesForm = this.fb.group({\n        attentionReason: [''],\n        internalNotes: [''],\n        actionTaken: ['']\n      });\n      // Trigger change detection to update the view\n      this.cdr.detectChanges();\n    }\n    ngOnDestroy() {\n      if (this.routeSubscription) {\n        this.routeSubscription.unsubscribe();\n      }\n      if (this.queryParamsSubscription) {\n        this.queryParamsSubscription.unsubscribe();\n      }\n      if (this.loadingSubscription) {\n        this.loadingSubscription.unsubscribe();\n      }\n    }\n    fetchPermitDetails() {\n      if (!this.permitId) {\n        return;\n      }\n      this.httpUtilService.loadingSubject.next(true);\n      this.permitsService.getPermit({\n        permitId: this.permitId\n      }).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.log('Permit API Response:', res);\n          if (!res?.isFault) {\n            this.permit = res.responseData?.data || res.responseData || null;\n            console.log('Permit data assigned:', this.permit);\n            console.log('Permit permitName field:', this.permit?.permitName);\n            console.log('All permit fields:', Object.keys(this.permit || {}));\n            this.permitReviewType = this.permit?.permitReviewType || '';\n            // Default to details tab, user can navigate to reviews as needed\n            this.selectedTab = 'details';\n          } else {\n            console.error('API returned fault:', res.faultMessage);\n            this.permit = null;\n          }\n          this.cdr.markForCheck();\n        },\n        error: () => {\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    fetchExternalReviews() {\n      if (!this.permitId) {\n        return;\n      }\n      this.httpUtilService.loadingSubject.next(true);\n      this.reviewsError = '';\n      this.permitsService.getAllReviews({\n        permitId: this.permitId\n      }).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (res?.isFault) {\n            this.reviewsError = res.faultMessage || 'Failed to load reviews';\n          } else {\n            const reviews = res.responseData?.reviews || [];\n            this.externalReviews = reviews.map(r => ({\n              commentsId: r.commentsId,\n              name: r.TypeName,\n              reviewer: r.AssignedTo,\n              status: r.StatusText,\n              completedDate: r.CompletedDate ? new Date(r.CompletedDate) : null,\n              dueDate: r.DueDate ? new Date(r.DueDate) : null,\n              receivedDate: r.receivedDate ? new Date(r.receivedDate) : null,\n              comments: r.Comments,\n              corrections: r.Corrections || [],\n              submittalId: r.SubmittalId,\n              FailureFlag: r.FailureFlag,\n              reviewCategory: r.reviewCategory,\n              EORAOROwner_Response: r.EORAOROwner_Response,\n              commentResponsedBy: r.commentResponsedBy\n            }));\n            // Build submittal list grouped from reviews\n            const idToReviews = {};\n            this.externalReviews.forEach(rv => {\n              const key = String(rv.submittalId ?? 'unknown');\n              if (!idToReviews[key]) {\n                idToReviews[key] = [];\n              }\n              idToReviews[key].push(rv);\n            });\n            this.externalSubmittals = Object.keys(idToReviews).map(key => {\n              const items = idToReviews[key];\n              // Determine status priority: Requires Re-submit > Under Review > Approved w/ Conditions > Approved\n              const statusOrder = {\n                'Requires Re-submit': 4,\n                'Under Review': 3,\n                'Approved w/ Conditions': 2,\n                'Approved': 1\n              };\n              const submittalStatus = items.reduce((acc, it) => {\n                const a = statusOrder[acc] || 0;\n                const b = statusOrder[it.status] || 0;\n                return b > a ? it.status : acc;\n              }, '');\n              // Aggregate dates\n              const dueDate = items.reduce((acc, it) => {\n                if (!it.dueDate) {\n                  return acc;\n                }\n                if (!acc) {\n                  return it.dueDate;\n                }\n                return acc > it.dueDate ? it.dueDate : acc; // earliest due date\n              }, null);\n              const completedDate = items.reduce((acc, it) => {\n                if (!it.completedDate) {\n                  return acc;\n                }\n                if (!acc) {\n                  return it.completedDate;\n                }\n                return acc < it.completedDate ? it.completedDate : acc; // latest completed date\n              }, null);\n              // Get received date from the first item that has it\n              const receivedDate = items.find(it => it.receivedDate)?.receivedDate || items.find(it => it.createdDate)?.createdDate || null;\n              // Get submittal name from the first item (all items in this group have same submittalId)\n              const reviewCategory = items[0]?.reviewCategory || `Submittal ${key}`;\n              return {\n                id: key,\n                title: reviewCategory,\n                submittalStatus: submittalStatus || items[0]?.status || '',\n                receivedDate: receivedDate ? new Date(receivedDate) : null,\n                dueDate: dueDate,\n                completedDate: completedDate\n              };\n            }).sort((a, b) => {\n              // Sort by received date in descending order (latest first)\n              if (!a.receivedDate && !b.receivedDate) return 0;\n              if (!a.receivedDate) return 1;\n              if (!b.receivedDate) return -1;\n              return b.receivedDate.getTime() - a.receivedDate.getTime();\n            });\n            // Select first submittal by default\n            if (this.externalSubmittals.length > 0) {\n              this.selectedExternalSubmittalId = this.externalSubmittals[0].id;\n              this.selectedAuditName = this.externalSubmittals[0].title;\n              this.selectedAuditStatus = this.externalSubmittals[0].submittalStatus;\n            }\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          this.reviewsError = 'Failed to load reviews';\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    fetchInternalReviews() {\n      if (!this.permitId) {\n        return;\n      }\n      this.httpUtilService.loadingSubject.next(true);\n      this.permitsService.getInternalReviews({\n        permitId: this.permitId,\n        take: 50,\n        skip: 0\n      }).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (res?.isFault) {\n            console.error('Failed to load internal reviews:', res.faultMessage);\n          } else {\n            this.internalReviews = res.responseData?.data || res.data || [];\n            // Transform internal reviews to match the auditEntries format\n            this.auditEntries = this.internalReviews.map(review => ({\n              commentsId: review.commentsId,\n              title: review.reviewCategory,\n              reviewCategory: review.reviewCategory,\n              // Preserve reviewCategory for edit modal\n              typeCodeDrawing: review.typeCodeDrawing,\n              reviewComments: review.reviewComments,\n              nonComplianceItems: review.nonComplianceItems,\n              aeResponse: review.aeResponse,\n              internalReviewer: review.internalReviewer,\n              internalVerificationStatus: review.internalVerificationStatus,\n              reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n              completedDate: review.completedDate ? new Date(review.completedDate) : null,\n              reviews: [{\n                name: review.reviewCategory,\n                typeCodeDrawing: review.typeCodeDrawing,\n                reviewComments: review.reviewComments,\n                nonComplianceItems: review.nonComplianceItems,\n                aeResponse: review.aeResponse,\n                internalReviewer: review.internalReviewer,\n                internalVerificationStatus: review.internalVerificationStatus,\n                reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n                completedDate: review.completedDate ? new Date(review.completedDate) : null\n              }]\n            }));\n            // Select first internal review by default\n            if (this.auditEntries.length > 0) {\n              this.selectedAuditIndex = 0;\n              this.selectedAuditName = this.auditEntries[0].title;\n              this.selectedAuditStatus = this.auditEntries[0].internalVerificationStatus || 'Pending';\n            }\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('Error loading internal reviews:', err);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    downloadInternalReviewsPdf() {\n      if (!this.internalReviews || this.internalReviews.length === 0) {\n        return;\n      }\n      const grouped = {};\n      this.internalReviews.forEach(r => {\n        const key = r.reviewCategory || 'Uncategorized';\n        if (!grouped[key]) {\n          grouped[key] = [];\n        }\n        grouped[key].push(r);\n      });\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'pt',\n        format: 'a4'\n      });\n      const pageWidth = doc.internal.pageSize.getWidth();\n      // Use more horizontal space: reduce side margins to ~0.5 inch (36pt)\n      const margin = {\n        left: 36,\n        right: 36,\n        top: 40\n      };\n      let y = margin.top;\n      const addCategory = (category, items) => {\n        // Category block title (centered, uppercase)\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(10);\n        doc.text(`${category.toUpperCase()} REVIEW COMMENTS`, pageWidth / 2, y, {\n          align: 'center'\n        });\n        y += 12;\n        // Reviewer line (take distinct non-empty names, join with comma)\n        const reviewers = Array.from(new Set(items.map(it => (it.internalReviewer || '').toString().trim()).filter(v => v)));\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(10);\n        doc.text(`Reviewer: ${reviewers.join(', ') || ''}`, pageWidth / 2, y, {\n          align: 'center'\n        });\n        y += 12;\n        // Dates line (Reviewed Date / Responses Date)\n        const reviewedDate = items.find(it => it.reviewedDate)?.reviewedDate;\n        const responsesDate = items.find(it => it.completedDate)?.completedDate;\n        doc.setFont('helvetica', 'italic');\n        doc.setFontSize(9);\n        doc.text(`Reviewed Date: ${reviewedDate ? AppService.formatDate(reviewedDate) : ''}`, margin.left, y);\n        doc.text(`Response Date: ${responsesDate ? AppService.formatDate(responsesDate) : ''}`, pageWidth - margin.right, y, {\n          align: 'right'\n        });\n        y += 6;\n        const rows = items.map((it, idx) => [(idx + 1).toString(), it.typeCodeDrawing || '', (it.reviewComments || '').toString(), (it.aeResponse || '').toString(), it.internalVerificationStatus || '']);\n        autoTable(doc, {\n          startY: y + 5,\n          head: [['#', 'Drawing #', 'Review Comments', 'A/E Response', 'Status']],\n          body: rows,\n          margin: {\n            left: margin.left,\n            right: margin.right\n          },\n          styles: {\n            font: 'helvetica',\n            fontSize: 8,\n            cellPadding: 5,\n            valign: 'top'\n          },\n          headStyles: {\n            fillColor: [33, 150, 243],\n            textColor: 255,\n            halign: 'center',\n            fontSize: 9\n          },\n          // Fit exactly into available width (pageWidth - margins)\n          // A4 width ~595pt; with 36pt margins each side → 523pt content width\n          columnStyles: {\n            0: {\n              cellWidth: 24,\n              halign: 'center'\n            },\n            // #\n            1: {\n              cellWidth: 55\n            },\n            // Drawing # (even narrower)\n            2: {\n              cellWidth: 198\n            },\n            // Review Comments (half of remaining)\n            3: {\n              cellWidth: 197\n            },\n            // A/E Response (other half)\n            4: {\n              cellWidth: 49\n            } // Verification Status (fits)\n          },\n          theme: 'grid'\n        });\n        // update y for next section\n        // @ts-ignore\n        y = doc.lastAutoTable.finalY + 20;\n        // add page if needed\n        if (y > doc.internal.pageSize.getHeight() - 100) {\n          doc.addPage();\n          y = margin.top;\n        }\n      };\n      Object.keys(grouped).forEach((category, idx) => {\n        // Removed divider line between categories\n        addCategory(category, grouped[category]);\n      });\n      const fileName = `Internal_Reviews_${this.permit?.permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n    }\n    selectExternalSubmittal(id) {\n      this.selectedExternalSubmittalId = id;\n      const sel = this.externalSubmittals.find(s => String(s.id) === String(id));\n      if (sel) {\n        this.selectedAuditName = sel.title;\n        this.selectedAuditStatus = sel.submittalStatus;\n      }\n    }\n    getExternalReviewsForSelectedSubmittal() {\n      if (!this.selectedExternalSubmittalId) {\n        return [];\n      }\n      return this.getExternalReviewsForSubmittal(this.selectedExternalSubmittalId);\n    }\n    getExternalReviewsForSubmittal(submittalId) {\n      const reviews = this.externalReviews.filter(r => String(r.submittalId) === String(submittalId));\n      // Sort by FailureFlag: false reviews first (desc), then true reviews (desc)\n      return reviews.sort((a, b) => {\n        // If both have the same FailureFlag value, maintain original order (reverse for desc)\n        if (a.FailureFlag === b.FailureFlag) {\n          return 0;\n        }\n        // False reviews (FailureFlag = false) come first\n        if (!a.FailureFlag && b.FailureFlag) {\n          return -1;\n        }\n        // True reviews (FailureFlag = true) come after false reviews\n        if (a.FailureFlag && !b.FailureFlag) {\n          return 1;\n        }\n        return 0;\n      }).reverse(); // Reverse to get descending order within each group\n    }\n    toggleSubmittalAccordion(index) {\n      if (this.expandedSubmittals.has(index)) {\n        this.expandedSubmittals.delete(index);\n      } else {\n        this.expandedSubmittals.add(index);\n      }\n    }\n    isSubmittalExpanded(index) {\n      return this.expandedSubmittals.has(index);\n    }\n    areAllSubmittalsExpanded() {\n      return this.externalSubmittals && this.externalSubmittals.length > 0 && this.expandedSubmittals.size === this.externalSubmittals.length;\n    }\n    toggleAllSubmittals() {\n      if (!this.externalSubmittals || this.externalSubmittals.length === 0) {\n        return;\n      }\n      if (this.areAllSubmittalsExpanded()) {\n        this.expandedSubmittals.clear();\n      } else {\n        this.expandedSubmittals = new Set(this.externalSubmittals.map((_, idx) => idx));\n      }\n      this.cdr.markForCheck();\n    }\n    toggleReviewAccordion(reviewId) {\n      if (this.expandedReviews.has(reviewId)) {\n        this.expandedReviews.delete(reviewId);\n      } else {\n        this.expandedReviews.add(reviewId);\n        // Set default tab for this review if not already set\n        if (!this.reviewSelectedTabs[reviewId]) {\n          this.reviewSelectedTabs[reviewId] = 'corrections';\n        }\n      }\n    }\n    isReviewExpanded(reviewId) {\n      return this.expandedReviews.has(reviewId);\n    }\n    showReviewTab(reviewId, tab, $event) {\n      $event.stopPropagation();\n      this.reviewSelectedTabs[reviewId] = tab;\n      this.cdr.markForCheck();\n    }\n    updateReviewResponse(review) {\n      this.httpUtilService.loadingSubject.next(true);\n      const formData = {\n        EORAOROwner_Response: review.EORAOROwner_Response,\n        commentResponsedBy: review.commentResponsedBy,\n        permitId: this.permitId,\n        commentsId: review.commentsId,\n        loggedInUserId: this.loginUser.userId\n      };\n      this.permitsService.updateExternalReview(formData).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (res?.isFault === false) {\n            //alert(res.responseData.message);\n            this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n            // Refresh external reviews to get updated data\n            this.fetchExternalReviews();\n          } else {\n            this.customLayoutUtilsService.showError(res.faultMessage || '❌ Error syncing permit', '');\n            //alert(res.faultMessage || 'Failed to update review response');\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          this.customLayoutUtilsService.showError('❌ Error updating review response', '');\n          //alert('Error updating review response');\n          console.error(err);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    isSelectedSubmittal(submittalId) {\n      return String(this.selectedExternalSubmittalId) === String(submittalId);\n    }\n    selectAudit(index) {\n      this.selectedAuditIndex = index;\n      this.selectedAuditName = this.auditEntries[this.selectedAuditIndex].title;\n      this.selectedAuditStatus = this.auditEntries[this.selectedAuditIndex].submittalStatus;\n    }\n    getReviewsForSelectedAudit() {\n      if (this.selectedAuditIndex === null || this.selectedAuditIndex >= this.auditEntries.length) {\n        return [];\n      }\n      return this.auditEntries[this.selectedAuditIndex].reviews || [];\n    }\n    goBack() {\n      console.log('goBack called - previousPage:', this.previousPage, 'projectId:', this.projectId);\n      if (this.previousPage === 'project' && this.projectId) {\n        // Navigate back to the specific project view with permits tab active\n        console.log('Navigating to project view with permits tab active');\n        this.router.navigate(['/projects/view', this.projectId], {\n          queryParams: {\n            activeTab: 'permits'\n          }\n        });\n      } else {\n        // Default to permit list\n        console.log('Navigating to permit list');\n        this.router.navigate(['/permits/list']);\n      }\n    }\n    goToPortal() {\n      window.open(`${this.permit.cityReviewLink + this.permit.permitEntityID}`, '_blank');\n    }\n    openReviewDetails(review) {\n      const modalRef = this.modalService.open(ReviewDetailsModalComponent, {\n        size: 'lg'\n      });\n      modalRef.componentInstance.review = review;\n      modalRef.componentInstance.permitId = this.permitId;\n      modalRef.componentInstance.permitDetails = this.permit;\n      // Handle modal result\n      modalRef.result.then(result => {\n        if (result === 'created' || result === 'updated') {\n          // Refresh internal reviews\n          this.fetchExternalReviews();\n        }\n      }).catch(error => {\n        // Modal was dismissed\n        console.log('Modal dismissed');\n      });\n    }\n    getStatusClass(status) {\n      if (!status) return 'status-n-a';\n      const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n      console.log('Status mapping - Original:', status, 'Normalized:', normalized);\n      // Map common synonyms from API to a single class we style\n      const aliasMap = {\n        'in-review': 'in-review',\n        'requires-re-submit': 'requires-re-submit',\n        'approved-w-conditions': 'approved-w-conditions',\n        'in-progress': 'in-progress',\n        'completed': 'completed',\n        'verified': 'verified',\n        'pending': 'pending',\n        'rejected': 'rejected',\n        'approved': 'approved',\n        'under-review': 'under-review',\n        'requires-resubmit': 'requires-resubmit',\n        'pacifica-verification': 'pacifica-verification',\n        'dis-approved': 'dis-approved',\n        'not-required': 'not-required',\n        '1-cycle-completed': '1-cycle-completed',\n        '1 cycle completed': '1-cycle-completed',\n        'cycle completed': '1-cycle-completed'\n      };\n      const resolved = aliasMap[normalized] || normalized;\n      const finalClass = 'status-' + resolved;\n      console.log('Final status class:', finalClass);\n      console.log('Available CSS classes for debugging:', ['status-pending', 'status-in-progress', 'status-completed', 'status-verified', 'status-rejected', 'status-approved', 'status-under-review', 'status-requires-resubmit', 'status-pacifica-verification', 'status-dis-approved', 'status-not-required', 'status-in-review', 'status-1-cycle-completed']);\n      return finalClass;\n    }\n    getStatusStyle(status) {\n      if (!status) return {};\n      const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n      const styleMap = {\n        '1-cycle-completed': {\n          backgroundColor: '#e8f5e8',\n          color: '#2e7d32',\n          border: '1px solid #a5d6a7'\n        },\n        '1 cycle completed': {\n          backgroundColor: '#e8f5e8',\n          color: '#2e7d32',\n          border: '1px solid #a5d6a7'\n        },\n        'cycle completed': {\n          backgroundColor: '#e8f5e8',\n          color: '#2e7d32',\n          border: '1px solid #a5d6a7'\n        },\n        'pacifica-verification': {\n          backgroundColor: '#e1f5fe',\n          color: '#0277bd',\n          border: '1px solid #81d4fa'\n        },\n        'dis-approved': {\n          backgroundColor: '#ffebee',\n          color: '#c62828',\n          border: '1px solid #ffcdd2'\n        },\n        'not-required': {\n          backgroundColor: '#f5f5f5',\n          color: '#757575',\n          border: '1px solid #e0e0e0'\n        },\n        'in-review': {\n          backgroundColor: '#e8eaf6',\n          color: '#3949ab',\n          border: '1px solid #c5cae9'\n        },\n        'pending': {\n          backgroundColor: '#fff3e0',\n          color: '#e65100',\n          border: '1px solid #ffcc02'\n        },\n        'approved': {\n          backgroundColor: '#e8f5e8',\n          color: '#1b5e20',\n          border: '1px solid #c8e6c9'\n        },\n        'completed': {\n          backgroundColor: '#e8f5e8',\n          color: '#1b5e20',\n          border: '1px solid #c8e6c9'\n        },\n        'rejected': {\n          backgroundColor: '#ffebee',\n          color: '#c62828',\n          border: '1px solid #ffcdd2'\n        }\n      };\n      return styleMap[normalized] || {};\n    }\n    showTab(tab, $event) {\n      if (tab === 'internal' && !this.isInternalReviewEnabled()) {\n        return;\n      }\n      this.selectedTab = tab;\n      this.cdr.markForCheck();\n    }\n    isInternalReviewEnabled() {\n      const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n      // show only when type is 'internal' or 'both'\n      return type === 'internal' || type === 'both';\n    }\n    isExternalReviewEnabled() {\n      const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n      // show only when type is 'external' or 'both'\n      return type === 'external' || type === 'both';\n    }\n    addPopUp() {\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Open the modal and load the AddEditInternalReviewComponent\n      const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n      // Pass data to the modal\n      modalRef.componentInstance.permitId = this.permitId;\n      modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n      modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n      // Handle modal result\n      modalRef.result.then(result => {\n        if (result === 'created' || result === 'updated') {\n          // Refresh internal reviews\n          this.fetchInternalReviews();\n        }\n      }).catch(error => {\n        // Modal was dismissed\n        console.log('Modal dismissed');\n      });\n    }\n    editInternalReview(reviewIndex) {\n      if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {\n        return;\n      }\n      const NgbModalOptions = {\n        size: 'lg',\n        backdrop: 'static',\n        keyboard: false,\n        scrollable: true\n      };\n      const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n      // Pass data to the modal for editing\n      modalRef.componentInstance.permitId = this.permitId;\n      modalRef.componentInstance.reviewData = this.auditEntries[reviewIndex];\n      modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n      modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n      // Handle modal result\n      modalRef.result.then(result => {\n        if (result === 'updated') {\n          // Refresh internal reviews\n          this.fetchInternalReviews();\n        }\n      }).catch(error => {\n        console.log('Modal dismissed');\n      });\n    }\n    editPopUp() {\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Open the modal and load the ProjectPopup\n      const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n    }\n    editPermit() {\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Open the modal and load the ProjectPopup\n      const modalRef = this.modalService.open(PermitPopupComponent, NgbModalOptions);\n      // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n      modalRef.componentInstance.id = this.permitId;\n      // Listen for passEntry event to refresh permit details when permit is saved\n      modalRef.componentInstance.passEntry.subscribe(saved => {\n        if (saved) {\n          this.fetchPermitDetails();\n        }\n      });\n    }\n    syncPermits(i) {\n      this.httpUtilService.loadingSubject.next(true);\n      this.singlePermit = i || false;\n      this.permitsService.syncPermits({\n        permitId: this.permitId,\n        singlePermit: this.singlePermit,\n        autoLogin: true\n      }).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.log('Sync response:', res);\n          console.log('Response type:', typeof res);\n          console.log('Response keys:', Object.keys(res || {}));\n          console.log('Response success:', res?.success);\n          console.log('Response message:', res?.message);\n          console.log('Response responseData:', res?.responseData);\n          // Handle various response structures\n          let responseData = res;\n          // Check different possible response structures\n          if (res?.responseData) {\n            responseData = res.responseData;\n          } else if (res?.body?.responseData) {\n            responseData = res.body.responseData;\n          } else if (res?.body) {\n            responseData = res.body;\n          }\n          console.log('Final responseData:', responseData);\n          console.log('Final success:', responseData?.success);\n          console.log('Final message:', responseData?.message);\n          if (responseData?.isFault) {\n            //alert(responseData.faultMessage || 'Failed to sync permit');\n            this.customLayoutUtilsService.showError(responseData.faultMessage, '');\n          } else if (responseData?.success === false) {\n            // Handle specific error messages from the API\n            if (responseData.message === 'Permit not found in Energov system') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n            } else if (responseData.message === 'No permits found for any keywords') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n            } else {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n            }\n          } else if (responseData?.success === true || responseData?.data) {\n            this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n            //alert('✅ Permit synced successfully');\n            this.fetchPermitDetails();\n            this.fetchExternalReviews();\n          } else {\n            // Fallback for unknown response structure\n            console.log('Unknown response structure, showing generic success');\n            //alert('✅ Permit synced successfully');\n            this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n            this.fetchPermitDetails();\n            this.fetchExternalReviews();\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          // Handle HTTP error responses\n          console.log('Error response:', err);\n          console.log('Error type:', typeof err);\n          console.log('Error keys:', Object.keys(err || {}));\n          console.log('Error status:', err?.status);\n          console.log('Error message:', err?.message);\n          console.log('Error error:', err?.error);\n          // The interceptor passes err.error to the error handler\n          // So err might actually be the response data\n          if (err?.success === false) {\n            // Handle specific error messages from the API\n            if (err.message === 'Permit not found in Energov system') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n            } else if (err.message === 'No permits found for any keywords') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n            } else {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n            }\n          } else if (err?.error?.message) {\n            if (err.error.message === 'Permit not found in Energov system') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n            } else if (err.error.message === 'No permits found for any keywords') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n            } else {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert(`❌ ${err.error.message}`);\n            }\n          } else if (err?.status === 404) {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            // Handle 404 specifically for permit not found\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else {\n            //alert('❌ Error syncing permit');\n          }\n          console.error(err);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    editExternalReview(review) {\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Open the modal and load the AddEditInternalReviewComponent\n      const modalRef = this.modalService.open(EditExternalReviewComponent, NgbModalOptions);\n      console.log('reviewData ', review);\n      // Pass data to the modal\n      modalRef.componentInstance.permitId = this.permitId;\n      modalRef.componentInstance.reviewData = review;\n      modalRef.componentInstance.permitDetails = this.permit;\n      modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n      // Handle modal result\n      modalRef.result.then(result => {\n        if (result === 'created' || result === 'updated') {\n          // Refresh internal reviews\n          this.fetchExternalReviews();\n        }\n      }).catch(error => {\n        // Modal was dismissed\n        console.log('Modal dismissed');\n      });\n    }\n    openResponseModal(correction, review) {\n      // Open the modal using NgbModal\n      const modalRef = this.modalService.open(ResponseModalComponent, {\n        size: 'lg',\n        backdrop: 'static',\n        keyboard: false\n      });\n      // Pass data to the modal\n      modalRef.componentInstance.correction = correction;\n      modalRef.componentInstance.review = review;\n      modalRef.componentInstance.permitId = this.permitId;\n      modalRef.componentInstance.loggedInUserId = this.loginUser.userId;\n      modalRef.componentInstance.isAdmin = this.isAdmin;\n      // Handle modal result\n      modalRef.componentInstance.responseSubmitted.subscribe(formData => {\n        this.submitResponse(formData, modalRef);\n      });\n      // Handle response completion to reset loading state\n      modalRef.componentInstance.responseCompleted.subscribe(success => {\n        if (!success) {\n          // Reset loading state if submission failed\n          modalRef.componentInstance.isLoading = false;\n        }\n      });\n      modalRef.result.then(() => {\n        // Modal was closed\n      }).catch(() => {\n        // Modal was dismissed\n      });\n    }\n    submitResponse(formData, modalRef) {\n      if (!formData) {\n        return;\n      }\n      this.httpUtilService.loadingSubject.next(true);\n      this.permitsService.updateExternalReview(formData).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (res?.isFault === false) {\n            this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n            //alert(res.responseData.message || 'Response submitted successfully');\n            this.fetchExternalReviews(); // Refresh external reviews to get updated data\n            // Emit success completion event\n            if (modalRef && modalRef.componentInstance) {\n              modalRef.componentInstance.responseCompleted.emit(true);\n            }\n            // Close the modal if it was passed\n            if (modalRef) {\n              modalRef.close();\n            }\n          } else {\n            //alert(res.faultMessage || 'Failed to submit response');\n            this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to submit response', '');\n            // Emit failure completion event\n            if (modalRef && modalRef.componentInstance) {\n              modalRef.componentInstance.responseCompleted.emit(false);\n            }\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          //alert('Error submitting response');\n          console.error(err);\n          this.customLayoutUtilsService.showSuccess('Error submitting response', '');\n          // Emit failure completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(false);\n          }\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    downloadReviewPDF(review) {\n      if (!review) {\n        return;\n      }\n      try {\n        const permitNumber = this.permit?.permitNumber || '';\n        const reviewer = (review?.AssignedTo || review?.municipalityReviewer || review?.reviewer || '').toString();\n        const cityComments = (review?.Comments || (review?.cityComments ?? '')).toString();\n        const ownerResponse = (review?.EORAOROwner_Response ?? '').toString();\n        // Calculate submittal count for this review\n        const submittalCount = this.externalSubmittals ? this.externalSubmittals.length : 0;\n        const cycle = submittalCount > 0 ? submittalCount.toString() : (review?.cycle || review?.Cycle || '').toString();\n        const status = (review?.StatusName || review?.commentstatus || review?.status || '').toString();\n        const displayDate = review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate ? AppService.formatDate(review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate) : AppService.formatDate(new Date());\n        const doc = new jsPDF({\n          orientation: 'portrait',\n          unit: 'pt',\n          format: 'a4'\n        });\n        const pageWidth = doc.internal.pageSize.getWidth();\n        const pageHeight = doc.internal.pageSize.getHeight();\n        const margin = {\n          left: 40,\n          right: 40,\n          top: 40,\n          bottom: 40\n        };\n        // Header: Permit Number\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(10);\n        doc.text(`Permit #: ${permitNumber || ''}`, margin.left, margin.top);\n        const startY = margin.top + 20;\n        // Table with one row matching the screenshot\n        const headers = ['REVIEWED BY', 'CITY COMMENTS', 'EOR/AOR/OWNER COMMENT RESPONSE', 'CYCLE', 'STATUS', 'DATE'];\n        // Build body rows. If there are corrections, show one row per correction; otherwise single row\n        const rawCorrections = (review && review.Corrections) ?? review.corrections ?? [];\n        const correctionsArray = Array.isArray(rawCorrections) ? rawCorrections : rawCorrections ? [rawCorrections] : [];\n        const bodyRows = correctionsArray.length > 0 ? correctionsArray.map(c => [reviewer || '', c?.Comments || cityComments || '', c?.Response || c?.EORAOROwner_Response || ownerResponse || '', cycle || '', status || '', (c?.ResolvedDate ? AppService.formatDate(c.ResolvedDate) : displayDate) || '']) : [[reviewer || '', cityComments || '', ownerResponse || '', cycle || '', status || '', displayDate || '']];\n        autoTable(doc, {\n          startY,\n          head: [headers],\n          body: bodyRows,\n          margin: {\n            left: margin.left,\n            right: margin.right\n          },\n          styles: {\n            font: 'helvetica',\n            fontSize: 8,\n            cellPadding: 5,\n            overflow: 'linebreak'\n          },\n          headStyles: {\n            fillColor: [240, 240, 240],\n            textColor: [0, 0, 0],\n            fontStyle: 'bold',\n            fontSize: 8\n          },\n          columnStyles: {\n            0: {\n              cellWidth: 90\n            },\n            1: {\n              cellWidth: (pageWidth - margin.left - margin.right) * 0.26\n            },\n            2: {\n              cellWidth: (pageWidth - margin.left - margin.right) * 0.24\n            },\n            3: {\n              cellWidth: 45,\n              halign: 'center'\n            },\n            4: {\n              cellWidth: 60,\n              halign: 'center'\n            },\n            5: {\n              cellWidth: 60,\n              halign: 'center'\n            }\n          },\n          didParseCell: data => {\n            // City comments text in red\n            if (data.section === 'body' && data.column.index === 1) {\n              data.cell.styles.textColor = [192, 0, 0];\n            }\n          },\n          didDrawCell: data => {\n            // Fully colored background for Status cell\n            if (data.section === 'body' && data.column.index === 4) {\n              const value = String(data.cell.raw || '');\n              const isApproved = value.toLowerCase() === 'approved';\n              const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\n              const textColor = [255, 255, 255];\n              // fill whole cell\n              doc.setFillColor(bg[0], bg[1], bg[2]);\n              doc.rect(data.cell.x + 0.5, data.cell.y + 0.5, data.cell.width - 1, data.cell.height - 1, 'F');\n              // write centered white text\n              doc.setTextColor(textColor[0], textColor[1], textColor[2]);\n              doc.setFont('helvetica', 'bold');\n              doc.setFontSize(8);\n              const textWidth = doc.getTextWidth(value);\n              const textX = data.cell.x + data.cell.width / 2 - textWidth / 2;\n              const textY = data.cell.y + data.cell.height / 2 + 3;\n              doc.text(value, textX, textY);\n              data.cell.text = [];\n            }\n          },\n          theme: 'grid'\n        });\n        // Footer\n        const pageCount = doc.getNumberOfPages();\n        for (let i = 1; i <= pageCount; i++) {\n          doc.setPage(i);\n          doc.setDrawColor(200, 200, 200);\n          doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n          doc.setFont('helvetica', 'normal');\n          doc.setFontSize(8);\n          doc.setTextColor(100, 100, 100);\n          doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n          doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n        }\n        const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n        doc.save(fileName);\n      } catch (error) {\n        console.error('Error generating PDF:', error);\n        this.customLayoutUtilsService.showSuccess('Error generation PDF. Please try agaiin', '');\n        //alert('Error generating PDF. Please try again.');\n      }\n    }\n    checkIfAdmin() {\n      // Check if the user is an admin based on roleId\n      // Assuming roleId 1 is admin - adjust this based on your role system\n      return this.loginUser && this.loginUser.roleId === 1;\n    }\n    shouldShowEditResponseButton(correction) {\n      // Show edit response button if:\n      // 1. User is admin (can always edit)\n      // 2. User is not admin but lockResponse is false (unlocked by admin)\n      if (this.isAdmin) {\n        return true;\n      }\n      // For non-admin users, only show if lockResponse is explicitly false\n      return correction.lockResponse === false;\n    }\n    onEdit(template) {\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      this.modalService.open(template, NgbModalOptions);\n      // console.log(\"this.permit\", this.permit)\n      this.notesForm.patchValue({\n        actionTaken: this.permit.actionTaken,\n        attentionReason: this.permit.attentionReason,\n        internalNotes: this.permit.internalNotes\n        // actionTaken:'helo',\n      });\n    }\n    closModal() {\n      this.modalService.dismissAll();\n    }\n    editNotesandactions() {\n      this.httpUtilService.loadingSubject.next(true);\n      const formData = {\n        permitId: this.permitId,\n        actionTaken: this.notesForm.value.actionTaken,\n        internalNotes: this.notesForm.value.internalNotes,\n        attentionReason: this.notesForm.value.attentionReason\n      };\n      this.permitsService.editNotesAndActions(formData).subscribe({\n        next: res => {\n          this.closModal();\n          this.httpUtilService.loadingSubject.next(false);\n          if (res?.isFault === false) {\n            //alert(res.responseData.message);\n            this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n            // Refresh permit details to get updated data from server\n            this.fetchPermitDetails();\n          } else {\n            this.customLayoutUtilsService.showError(res.faultMessage || '❌ Error in update notes and actions', '');\n            //alert(res.faultMessage || 'Failed to update review response');\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          this.customLayoutUtilsService.showError('❌ Error in update notes and actions', '');\n          //alert('Error updating review response');\n          console.error(err);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    static ɵfac = function PermitViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PermitViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.PermitsService), i0.ɵɵdirectiveInject(i7.HttpUtilsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PermitViewComponent,\n      selectors: [[\"app-permit-view\"]],\n      decls: 5,\n      vars: 2,\n      consts: [[\"notesActionsTemplate\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"permit-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"permit-details-header\"], [1, \"header-content\", \"w-100\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"permit-title\"], [1, \"status-text\", \"status-under-review\"], [1, \"permit-number-line\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\", 2, \"margin-right\", \"16px\"], [\"type\", \"button\", \"class\", \"btn btn-link p-0\", \"title\", \"Edit Permit\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"button-group\", 4, \"ngIf\"], [1, \"card-body\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Edit Permit\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\", 2, \"font-size\", \"1.1rem\"], [\"type\", \"button\", \"title\", \"Download Internal Reviews PDF\", 1, \"btn\", \"btn-link\", \"p-0\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"download-pdf-icon\", 2, \"color\", \"#3699ff\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"bi\", \"bi-plus-lg\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", 3, \"ngClass\"], [1, \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-sync-alt\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"permit-details-content\"], [1, \"permit-details-grid\"], [1, \"permit-detail-item\"], [1, \"permit-value\"], [1, \"status-text\", 3, \"ngClass\"], [1, \"text-gray-500\", \"fs-7\", \"p-0\"], [1, \"permit-details-card\"], [\"type\", \"button\", \"title\", \"Edit Notes/Actions\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"notes-actions-container\"], [1, \"permit-detail-item-full\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-clipboard-list\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [\"class\", \"audit-table-row\", 3, \"table-active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"audit-table-row\", 3, \"click\"], [1, \"audit-title-cell\", \"px-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"mb-0\", \"me-3\"], [1, \"audit-status-badge\", 3, \"ngClass\", \"ngStyle\"], [1, \"mt-1\"], [1, \"text-muted\"], [1, \"audit-dates-cell\", \"px-3\"], [1, \"d-flex\", \"gap-4\"], [1, \"date-item\"], [1, \"text-muted\", \"d-block\"], [1, \"fw-medium\"], [1, \"audit-actions-cell\", \"px-4\"], [\"title\", \"Edit Review\", 1, \"fas\", \"fa-edit\", \"action-icon\", \"edit-icon\", 3, \"click\"], [1, \"external-reviews-card\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"class\", \"card-body p-0\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"fa-3x\", \"mb-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-redo\"], [1, \"fas\", \"fa-external-link-alt\", \"fa-3x\", \"mb-3\"], [1, \"card-body\", \"p-0\"], [1, \"table-responsive\", \"external-reviews-table\"], [1, \"table\", \"table-hover\", \"mb-0\"], [4, \"ngFor\", \"ngForOf\"], [1, \"external-submittal-row\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"submittal-title-cell\", \"px-3\"], [1, \"accordion-toggle\", \"me-2\"], [1, \"submittal-status-badge\", 3, \"ngClass\"], [1, \"submittal-dates-cell\", \"px-3\"], [1, \"accordion-content-row\"], [\"colspan\", \"2\", 1, \"p-0\"], [1, \"accordion-content\"], [1, \"reviews-container\", \"p-3\"], [\"class\", \"review-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"review-item\"], [1, \"review-single-line\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"review-accordion-toggle\", \"me-2\"], [1, \"review-title\"], [1, \"review-status-container\"], [1, \"review-status\", 3, \"ngClass\"], [1, \"reviewer-container\"], [1, \"reviewer\"], [1, \"due-date-container\"], [1, \"due-date\"], [1, \"completed-date-container\"], [1, \"completed-date\"], [1, \"review-actions-container\"], [\"title\", \"Download Review PDF\", 1, \"fas\", \"fa-download\", \"download-pdf-icon\", 3, \"click\"], [1, \"review-details-accordion\"], [1, \"review-details-content\"], [\"class\", \"corrections-section p-3\", \"style\", \"padding-bottom: 0px !important;\", 4, \"ngIf\"], [\"class\", \"comments-section p-3\", 4, \"ngIf\"], [\"class\", \"no-data-section p-3\", 4, \"ngIf\"], [1, \"corrections-section\", \"p-3\", 2, \"padding-bottom\", \"0px !important\"], [1, \"section-title\"], [\"class\", \"correction-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"correction-item\"], [1, \"correction-header\", \"d-flex\", \"align-items-center\"], [1, \"correction-number\"], [1, \"correction-meta\", \"flex-grow-1\", \"ms-3\", \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"meta-fields\", \"d-flex\", \"align-items-center\", \"w-100\"], [1, \"meta-field\", \"flex-fill\"], [1, \"meta-label\", \"fw-bold\"], [1, \"meta-value\"], [1, \"meta-value\", \"resolved-date\"], [1, \"respond-buttons\"], [\"class\", \"btn btn-primary btn-sm me-3\", \"title\", \"Respond to this correction\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary btn-sm me-3\", \"title\", \"Edit response to this correction\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"correction-content\"], [1, \"correction-field\"], [1, \"field-label\"], [1, \"field-content\", \"corrective-action\"], [1, \"field-content\", \"comment\"], [\"class\", \"correction-field\", 4, \"ngIf\"], [\"class\", \"correction-separator\", 4, \"ngIf\"], [\"title\", \"Respond to this correction\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [\"title\", \"Edit response to this correction\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"field-content\", \"response\"], [1, \"field-content\", \"eor-response\"], [1, \"field-content\", \"responded-by\"], [1, \"correction-separator\"], [1, \"comments-section\", \"p-3\"], [1, \"comment-content\"], [1, \"comment-text\"], [1, \"no-data-section\", \"p-3\"], [1, \"no-data-message\", \"text-center\", \"text-muted\"], [1, \"fas\", \"fa-info-circle\"], [1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"medium-modal-body\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [1, \"form-group\"], [\"for\", \"projectName\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"attentionReason\", \"rows\", \"2\", \"formControlName\", \"attentionReason\", \"placeholder\", \"Type here...\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"projectDescription\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"internalNotes\", \"rows\", \"3\", \"formControlName\", \"internalNotes\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"internalProjectNo\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"internalNotes\", \"rows\", \"3\", \"formControlName\", \"actionTaken\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [1, \"modal-footer\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n      template: function PermitViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PermitViewComponent_div_0_Template, 7, 0, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2);\n          i0.ɵɵtemplate(2, PermitViewComponent_div_2_Template, 30, 14, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, PermitViewComponent_ng_template_3_Template, 35, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.permit);\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgStyle, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i8.DatePipe],\n      styles: [\"@charset \\\"UTF-8\\\";.permit-view-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:0}.permit-details-header[_ngcontent-%COMP%]{padding:0 1.5rem;border-bottom:1px solid #e5eaee;background:transparent}.permit-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding-top:.5rem}.permit-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#3f4254;font-weight:600;font-size:1.1rem}.permit-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]{display:flex;gap:.5rem;align-items:center;margin-left:auto}.permit-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%]{font-size:.875rem!important;padding:.375rem .75rem!important;line-height:1.5!important}.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], .permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.3rem;padding:.15rem .5rem;border-radius:.55rem;background-color:#f3f6f9;color:#3f4254;border:1px solid #e5eaee;box-shadow:0 2px 6px #0000000d;font-weight:600;font-size:.8rem;line-height:1;transition:background-color .2s ease,box-shadow .2s ease,transform .02s ease}.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#5e6e82;font-size:.75rem}.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, .permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover{background-color:#eef2f7;box-shadow:0 3px 10px #00000012;text-decoration:none}.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, .permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active{transform:translateY(1px);box-shadow:0 1px 4px #0000000f}.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:2rem}.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover{background-color:transparent;color:#3699ff!important;text-decoration:none}.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus{box-shadow:none;outline:none}.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:color .2s ease;line-height:1}.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{color:#3699ff!important}.btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;background-color:#f5f5f5!important;color:#999!important;border-color:#ddd!important}.btn[_ngcontent-%COMP%]:disabled:hover{background-color:#f5f5f5!important;color:#999!important;border-color:#ddd!important;box-shadow:none!important}.permit-details-card[_ngcontent-%COMP%]{background:#fff;margin:1rem 0;overflow:hidden}.permit-details-header[_ngcontent-%COMP%]{background:#f5f6f8;padding:.75rem 1.25rem;border-bottom:1px solid #e3e6ea;display:flex;justify-content:space-between;align-items:center}.permit-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;font-size:1rem;font-weight:600;color:#333}.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:2rem}.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover{background-color:transparent;color:#3699ff!important;text-decoration:none}.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus{box-shadow:none;outline:none}.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:color .2s ease;line-height:1}.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{color:#3699ff!important}.edit-btn[_ngcontent-%COMP%]{background:#1b7e6c;color:#fff;border:none;border-radius:6px;padding:.35rem .75rem;font-size:.85rem;cursor:pointer;display:flex;align-items:center;gap:4px;transition:background .2s ease}.edit-btn[_ngcontent-%COMP%]:hover{background:#166354}.permit-details-content[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-radius:.75rem;box-shadow:0 4px 10px #00000014;margin:1rem 0;overflow:hidden}.permit-details-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1fr 1fr;gap:1.5rem}.notes-actions-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.permit-detail-item-full[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.4rem;width:100%}.permit-detail-item-full[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;line-height:1.2;font-size:.875rem;font-weight:600;color:#6c7293;text-transform:capitalize;letter-spacing:.1rem;margin:0}.permit-detail-item-full[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%]{font-size:1rem;color:#3f4254;font-weight:500;padding:.5rem 0;border-bottom:none;word-wrap:break-word;white-space:pre-wrap}.permit-detail-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.4rem}.permit-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;line-height:1.2;font-size:.875rem;font-weight:600;color:#111;text-transform:capitalize;letter-spacing:.1rem;margin:0}.permit-detail-item[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%], .permit-detail-item[_ngcontent-%COMP%]   .permit-status[_ngcontent-%COMP%]{margin-top:.1rem}.permit-detail-item[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%]{font-size:1rem;color:#3f4254;font-weight:500;padding:.5rem 0;border-bottom:none}.permit-detail-item[_ngcontent-%COMP%]   .permit-status[_ngcontent-%COMP%]{display:block;vertical-align:top;padding:.5rem 0;font-size:1rem;font-weight:600;text-align:left;background:transparent;border:none;min-width:0;border-radius:0}.permit-detail-item[_ngcontent-%COMP%]   .status-approved[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.permit-detail-item[_ngcontent-%COMP%]   .status-pending[_ngcontent-%COMP%]{background-color:#fff3e0;color:#e65100;border:1px solid #ffcc02}.permit-detail-item[_ngcontent-%COMP%]   .status-rejected[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828;border:1px solid #ffcdd2}.permit-detail-item[_ngcontent-%COMP%]   .status-submitted[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1565c0;border:1px solid #bbdefb}.permit-detail-item[_ngcontent-%COMP%]   .status-void[_ngcontent-%COMP%], .permit-detail-item[_ngcontent-%COMP%]   .status-unknown[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.permit-detail-item[_ngcontent-%COMP%]   .status-requires-resubmit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.permit-detail-item[_ngcontent-%COMP%]   .status-conditional-approval[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.permit-detail-item[_ngcontent-%COMP%]   .status-under-review[_ngcontent-%COMP%]{background-color:#e8eaf6;color:#3949ab;border:1px solid #c5cae9}.loading-section[_ngcontent-%COMP%]{background:#fff;border-radius:.475rem;padding:2rem;text-align:center;border:1px solid #e5eaee}.loading-section[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}.loading-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c7293;margin:0}.loading-section[_ngcontent-%COMP%]   .loading-tips[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{color:#a1a5b7!important;font-size:.875rem}.loading-section[_ngcontent-%COMP%]   .loading-tips[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem;color:#3699ff}.no-permit-data[_ngcontent-%COMP%]{margin-bottom:1rem}.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{border-radius:.475rem;border:1px solid #e5eaee}.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#3f4254;font-weight:600}.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#6c7293}.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0 0 1rem;padding-left:1.5rem;color:#6c7293}.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:.25rem}.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem;padding:.5rem 1rem}.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem}.table-responsive[_ngcontent-%COMP%]{overflow-x:auto;padding:0 .5rem;margin-top:0}.external-reviews-table[_ngcontent-%COMP%]{padding-top:0!important;margin-top:0!important}.card-body[_ngcontent-%COMP%]{padding-top:0!important}.table[_ngcontent-%COMP%]{margin-bottom:0;overflow:hidden}.table[_ngcontent-%COMP%]   .audit-table-row[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]{cursor:pointer;transition:all .2s ease;border-bottom:1px solid #e5eaee;background:#fff;border-radius:.5rem;margin-bottom:.5rem;box-shadow:0 1px 3px #0000001a}.table[_ngcontent-%COMP%]   .audit-table-row[_ngcontent-%COMP%]:hover, .table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;transform:translateY(-1px);box-shadow:0 2px 8px #00000026}.table[_ngcontent-%COMP%]   .audit-table-row.expanded[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .external-submittal-row.expanded[_ngcontent-%COMP%]{background:#e3f2fd;border-color:#2196f3;box-shadow:0 2px 8px #2196f333}.table[_ngcontent-%COMP%]   .audit-table-row.table-active[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .external-submittal-row.table-active[_ngcontent-%COMP%]{background-color:#e3f2fd}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]{width:75%;vertical-align:middle}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge[_ngcontent-%COMP%]{font-size:.7rem;padding:.2rem .4rem;border-radius:.25rem;font-weight:600;border:1px solid transparent;text-transform:uppercase;letter-spacing:.05rem;display:inline-block;min-width:60px;text-align:center}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-re-submit[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-re-submit[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-re-submit[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-re-submit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved-w-conditions[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-complete[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-complete[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-complete[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-complete[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-n-a[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-n-a[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-n-a[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-n-a[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0;color:#e65100;border:1px solid #ffcc02}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-progress[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-progress[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-progress[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-progress[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1565c0;border:1px solid #90caf9}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-completed[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-completed[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-completed[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-completed[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-verified[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-verified[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-verified[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-verified[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#2e7d32;border:1px solid #a5d6a7}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828;border:1px solid #ffcdd2}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%]{background-color:#e8eaf6;color:#3949ab;border:1px solid #c5cae9}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-resubmit[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-resubmit[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-resubmit[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-resubmit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-on-hold[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-on-hold[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-on-hold[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-on-hold[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%]{background-color:#e1f5fe!important;color:#0277bd!important;border:1px solid #81d4fa!important}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%]{background-color:#ffebee!important;color:#c62828!important;border:1px solid #ffcdd2!important}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%]{background-color:#f5f5f5!important;color:#757575!important;border:1px solid #e0e0e0!important}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%]{background-color:#e8eaf6!important;color:#3949ab!important;border:1px solid #c5cae9!important}.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%]{background-color:#e8f5e8!important;color:#2e7d32!important;border:1px solid #a5d6a7!important}.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]{width:25%;vertical-align:middle;text-align:right;padding-right:0}.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{gap:1rem;justify-content:flex-end}.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]{text-align:center;width:120px;min-width:120px;max-width:120px}.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.7rem;font-weight:500;display:block}.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   .fw-medium[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   .fw-medium[_ngcontent-%COMP%]{font-size:.85rem;display:block;min-height:1.25rem}.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]{width:15%;vertical-align:middle;text-align:center}.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]{font-size:1rem;color:#3699ff;cursor:pointer;transition:color .2s ease,transform .2s ease}.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]:hover{color:#2b7ce0;transform:scale(1.1)}.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]{color:#3699ff}.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]:hover{color:#2b7ce0}.table[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]{color:#3699ff;font-size:.8rem;transition:transform .2s ease;display:flex;align-items:center;justify-content:center;width:20px;height:20px}.table[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:transform .2s ease}.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9ff,#e8f2ff);border-left:4px solid #3699ff;box-shadow:inset 0 2px 4px #3699ff1a;transition:all .3s ease}.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]   .accordion-content[_ngcontent-%COMP%]{border-top:1px solid #d1e7ff;background:transparent}.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]   .accordion-content[_ngcontent-%COMP%]   .reviews-container[_ngcontent-%COMP%]{max-height:400px;overflow-y:auto;background:#ffffffb3;border-radius:.375rem;margin:.5rem;padding:1rem;box-shadow:0 2px 8px #3699ff26}.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:has( + .accordion-content-row[_ngcontent-%COMP%]:not(.d-none)){background:linear-gradient(135deg,#f0f8ff,#e8f2ff);box-shadow:0 2px 8px #3699ff33;transition:all .3s ease;margin-right:0}.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:has( + .accordion-content-row[_ngcontent-%COMP%]:not(.d-none))   .submittal-title-cell[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]{color:#2b7ce0;transform:scale(1.1)}.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]{transition:all .3s ease}.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.section-header[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid #e5eaee;background:#f8f9fa;border-radius:.475rem .475rem 0 0;display:flex;justify-content:space-between;align-items:center}.section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0;color:#3f4254;font-weight:600;font-size:1rem}.section-header[_ngcontent-%COMP%]   .section-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.75rem;padding:.375rem .75rem}.section-header[_ngcontent-%COMP%]   .submittal-status[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem;border-radius:.25rem;font-weight:500;border:1px solid transparent}.section-header[_ngcontent-%COMP%]   .submittal-status.status-approved[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.section-header[_ngcontent-%COMP%]   .submittal-status.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0;color:#e65100;border:1px solid #ffcc02}.section-header[_ngcontent-%COMP%]   .submittal-status.status-under-review[_ngcontent-%COMP%], .section-header[_ngcontent-%COMP%]   .submittal-status.status-in-review[_ngcontent-%COMP%]{background-color:#e8eaf6;color:#3949ab;border:1px solid #c5cae9}.section-header[_ngcontent-%COMP%]   .submittal-status.status-rejected[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828;border:1px solid #ffcdd2}.section-header[_ngcontent-%COMP%]   .submittal-status.status-requires-re-submit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.section-header[_ngcontent-%COMP%]   .submittal-status.status-approved-w-conditions[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.section-header[_ngcontent-%COMP%]   .submittal-status.status-complete[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.section-header[_ngcontent-%COMP%]   .submittal-status.status-n-a[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.reviews-container[_ngcontent-%COMP%]{max-height:400px;overflow-y:auto}.status-approved[_ngcontent-%COMP%]{background-color:#e8f5e8!important;color:#1b5e20!important;border:1px solid #c8e6c9!important}.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0!important;color:#e65100!important;border:1px solid #ffcc02!important}.status-requires-resubmit[_ngcontent-%COMP%]{background-color:#ffebee!important;color:#c62828!important;border:1px solid #ffcdd2!important}.status-under-review[_ngcontent-%COMP%]{background-color:#e3f2fd!important;color:#1565c0!important;border:1px solid #bbdefb!important}.status-complete[_ngcontent-%COMP%]{background-color:#e8f5e8!important;color:#1b5e20!important;border:1px solid #c8e6c9!important}.status-1-cycle-completed[_ngcontent-%COMP%]{background-color:#e8f5e8!important;color:#2e7d32!important;border:1px solid #a5d6a7!important}.status-pacifica-verification[_ngcontent-%COMP%]{background-color:#e1f5fe!important;color:#0277bd!important;border:1px solid #81d4fa!important}.status-dis-approved[_ngcontent-%COMP%]{background-color:#ffebee!important;color:#c62828!important;border:1px solid #ffcdd2!important}.status-not-required[_ngcontent-%COMP%]{background-color:#f5f5f5!important;color:#757575!important;border:1px solid #e0e0e0!important}.status-in-review[_ngcontent-%COMP%]{background-color:#e8eaf6!important;color:#3949ab!important;border:1px solid #c5cae9!important}.reviews-container[_ngcontent-%COMP%]{max-height:500px;overflow-y:auto;padding:.75rem}.review-item[_ngcontent-%COMP%]{border-bottom:1px solid #e5eaee;padding:.5rem 0;background:transparent;transition:background-color .2s ease}.review-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.review-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.review-single-line[_ngcontent-%COMP%]{display:grid;grid-template-columns:20px 1fr 120px 120px 140px 140px 30px;align-items:center;gap:.75rem;cursor:pointer;padding:.5rem;background:transparent;border-radius:.375rem;transition:all .2s ease}.review-single-line[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;transform:translateY(-1px);box-shadow:0 2px 8px #0000001a}.review-single-line.expanded[_ngcontent-%COMP%]{background:#e3f2fd;border:1px solid #2196f3}.review-single-line[_ngcontent-%COMP%]   .review-title[_ngcontent-%COMP%]{margin:0;font-size:.9rem;font-weight:600;color:#3f4254;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.review-single-line[_ngcontent-%COMP%]   .review-status-container[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .reviewer-container[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .due-date-container[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .completed-date-container[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .review-actions-container[_ngcontent-%COMP%]{display:flex;align-items:center;min-height:20px}.review-single-line[_ngcontent-%COMP%]   .review-actions-container[_ngcontent-%COMP%]{gap:.5rem;justify-content:flex-end}.review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem;border-radius:.25rem;font-weight:500;border:1px solid transparent;white-space:nowrap;display:inline-block}.review-single-line[_ngcontent-%COMP%]   .review-status.status-approved[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.review-single-line[_ngcontent-%COMP%]   .review-status.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0;color:#e65100;border:1px solid #ffcc02}.review-single-line[_ngcontent-%COMP%]   .review-status.status-under-review[_ngcontent-%COMP%]{background-color:#e8eaf6;color:#3949ab;border:1px solid #c5cae9}.review-single-line[_ngcontent-%COMP%]   .review-status.status-rejected[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828;border:1px solid #ffcdd2}.review-single-line[_ngcontent-%COMP%]   .review-status.status-requires-re-submit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.review-single-line[_ngcontent-%COMP%]   .review-status.status-approved-w-conditions[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.review-single-line[_ngcontent-%COMP%]   .review-status.status-complete[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.review-single-line[_ngcontent-%COMP%]   .review-status.status-n-a[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.review-single-line[_ngcontent-%COMP%]   .review-status.status-submitted[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1565c0;border:1px solid #bbdefb}.review-single-line[_ngcontent-%COMP%]   .review-status.status-void[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.review-single-line[_ngcontent-%COMP%]   .review-status.status-conditional-approval[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.review-single-line[_ngcontent-%COMP%]   .review-status.status-requires-resubmit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.review-single-line[_ngcontent-%COMP%]   .review-status.status-unknown[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%]{font-size:.8rem;color:#6c7293;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%]{font-size:.7rem;color:#6c7293;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.review-single-line[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]{font-size:1rem;color:#3699ff;cursor:pointer;transition:color .2s ease,transform .2s ease;margin-left:.5rem}.review-single-line[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]:hover{color:#2b7ce0;transform:scale(1.1)}.review-single-line[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]{color:#3699ff}.review-single-line[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]:hover{color:#2b7ce0}.review-single-line[_ngcontent-%COMP%]   .action-icon.view-icon[_ngcontent-%COMP%]{color:#6c7293}.review-single-line[_ngcontent-%COMP%]   .action-icon.view-icon[_ngcontent-%COMP%]:hover{color:#3699ff}.review-single-line[_ngcontent-%COMP%]   .review-accordion-toggle[_ngcontent-%COMP%]{color:#3699ff;font-size:.8rem;transition:transform .2s ease;display:flex;align-items:center;justify-content:center;width:20px;height:20px}.review-single-line[_ngcontent-%COMP%]   .review-accordion-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:transform .2s ease}.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]{color:#28a745;font-size:1rem;cursor:pointer;transition:all .2s ease;padding:.25rem}.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]:hover{color:#1e7e34;transform:scale(1.1)}.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]:active{transform:scale(.95)}.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]{color:#3699ff;font-size:1rem;cursor:pointer;transition:all .2s ease;padding:.25rem}.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]:hover{color:#2b7ce0;transform:scale(1.1)}.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]:active{transform:scale(.95)}.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]{border:none;background:none;box-shadow:none;text-decoration:none}.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover, .review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus, .review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:active{background:none;box-shadow:none;text-decoration:none}.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled   .download-pdf-icon[_ngcontent-%COMP%]{cursor:not-allowed;color:#6c757d}.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled   .download-pdf-icon[_ngcontent-%COMP%]:hover{transform:none;color:#6c757d}.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border:1px solid #e5eaee;box-shadow:0 2px 4px #0000001a}.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1.5rem}.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]{margin-bottom:0}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]{margin-top:1rem;border-top:1px solid #e5eaee;background:linear-gradient(135deg,#f8f9ff,#e8f2ff);border-radius:.375rem;box-shadow:inset 0 2px 4px #3699ff1a;transition:all .3s ease}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]{padding:1rem;background:#ffffffb3;border-radius:.375rem;margin:.5rem;box-shadow:0 2px 8px #3699ff26}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]{background:#fff!important;border-radius:.75rem!important;padding:2rem!important;border:1px solid #e5eaee!important;box-shadow:0 4px 20px #00000014!important;margin:0!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.4rem!important;font-weight:800!important;color:#1a202c!important;margin-bottom:2rem!important;padding-bottom:1rem!important;border-bottom:3px solid #3699ff!important;position:relative!important;display:flex!important;align-items:center!important;gap:1rem!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]:before{content:\\\"\\\\1f4cb\\\";font-size:1.8rem;filter:drop-shadow(0 2px 4px rgba(54,153,255,.3))}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title:after, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-3px;left:0;width:60px;height:3px;background:linear-gradient(90deg,#3699ff,#1bc5bd);border-radius:2px;box-shadow:0 2px 8px #3699ff66}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]{background:#fff!important;border:1px solid #e5eaee!important;border-radius:.75rem!important;padding:1.5rem!important;box-shadow:0 4px 12px #00000014!important;position:relative!important;transition:all .3s cubic-bezier(.4,0,.2,1)!important;overflow:hidden!important;margin-bottom:2rem!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:4px;height:100%;background:linear-gradient(180deg,#3699ff,#1bc5bd);border-radius:.75rem 0 0 .75rem}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:hover, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 25px #3699ff26}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:hover:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover:before{background:linear-gradient(180deg,#1bc5bd,#3699ff)}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1.25rem;margin-bottom:1.5rem;padding-bottom:1rem;border-bottom:2px solid #f1f3f4;position:relative}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header:after, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-2px;left:0;width:50px;height:2px;background:linear-gradient(90deg,#3699ff,#1bc5bd);border-radius:1px}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-number, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3699ff,#1bc5bd)!important;color:#fff!important;width:32px!important;height:32px!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;font-size:.9rem!important;font-weight:800!important;box-shadow:0 4px 12px #3699ff66!important;flex-shrink:0!important;position:relative!important;transition:all .3s ease!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-number:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:-2px;background:linear-gradient(135deg,#3699ff,#1bc5bd);border-radius:50%;z-index:-1;opacity:.3;animation:_ngcontent-%COMP%_pulse 2s ease-in-out infinite}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem;flex:1}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]{display:flex!important;align-items:center!important;gap:.75rem!important;padding:.5rem 0!important;background:transparent!important;border-radius:0!important;border:none!important;transition:all .2s ease!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row:hover, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover{background:transparent;transform:none}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-label, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%]{font-size:.85rem;font-weight:700;color:#4a5568;text-transform:capitalize;letter-spacing:.05rem;min-width:80px}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-value, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;color:#2d3748;flex:1}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .resolved-date, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%]{color:inherit;background:transparent;padding:.25rem 0;border-radius:0;font-weight:700}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]{margin-bottom:1.5rem;position:relative}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field:last-child, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child{margin-bottom:0}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]{font-size:.85rem;font-weight:800;color:#2d3748;text-transform:capitalize;letter-spacing:.15rem;margin-bottom:.75rem;display:flex;align-items:center;gap:.75rem;position:relative}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label i, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3699ff;font-size:.9rem;width:16px;text-align:center}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]:before{content:\\\"\\\\25b6\\\";color:#3699ff;font-size:.8rem;animation:_ngcontent-%COMP%_slideRight 1.5s ease-in-out infinite}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]{font-size:1rem!important;color:#2d3748!important;line-height:1.7!important;padding:1.25rem!important;background:linear-gradient(135deg,#f8f9fa,#fff)!important;border-radius:.75rem!important;border:1px solid #e2e8f0!important;box-shadow:inset 0 2px 8px #0000000a!important;position:relative!important;transition:all .3s ease!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:4px;height:100%;background:linear-gradient(180deg,#e2e8f0,#cbd5e0);border-radius:.75rem 0 0 .75rem}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content:hover, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover{box-shadow:inset 0 2px 12px #00000014;transform:translateY(-1px)}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff5f5,#fff);border-color:#fed7d7}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#feb2b2,#fc8181)}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.comment, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f0f9ff,#fff);border-color:#bee3f8}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.comment:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#90cdf4,#63b3ed)}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e8f5e8,#f0fff4);border-color:#c6f6d5}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#38a169,#2f855a)}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response:after, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]:after{content:\\\"\\\\2713\\\";position:absolute;top:1rem;right:1rem;color:#38a169;font-weight:700;font-size:1.2rem;animation:_ngcontent-%COMP%_checkmark .6s ease-in-out}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.eor-response, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e8f5e8,#f0fff4)!important;border-color:#c6f6d5!important;box-shadow:inset 0 2px 8px #38a1691a!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.eor-response:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#38a169,#2f855a)!important;width:4px!important;height:100%!important;border-radius:.75rem 0 0 .75rem!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.responded-by, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e8f5e8,#f0fff4)!important;border-color:#c6f6d5!important;box-shadow:inset 0 2px 8px #38a1691a!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.responded-by:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#38a169,#2f855a)!important;width:4px!important;height:100%!important;border-radius:.75rem 0 0 .75rem!important}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-separator, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]{height:2px;background:linear-gradient(90deg,transparent,#e5eaee,transparent);margin:2rem 0;position:relative}.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-separator:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:-2px;left:50%;transform:translate(-50%);width:60px;height:6px;background:linear-gradient(90deg,#3699ff,#1bc5bd);border-radius:3px;box-shadow:0 2px 8px #3699ff4d}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]{background:#fff!important;border-radius:.75rem!important;padding:2rem!important;border:1px solid #e5eaee!important;box-shadow:0 4px 20px #00000014!important}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.4rem!important;font-weight:800!important;color:#1a202c!important;margin-bottom:2rem!important;padding-bottom:1rem!important;border-bottom:3px solid #3699ff!important;position:relative!important;display:flex!important;align-items:center!important;gap:1rem!important}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]:before{content:\\\"\\\\1f4cb\\\";font-size:1.8rem;filter:drop-shadow(0 2px 4px rgba(54,153,255,.3))}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title:after, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]:after, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-3px;left:0;width:60px;height:3px;background:linear-gradient(90deg,#3699ff,#1bc5bd);border-radius:2px;box-shadow:0 2px 8px #3699ff66}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]{background:#fff!important;border:1px solid #e5eaee!important;border-radius:.75rem!important;padding:1.5rem!important;box-shadow:0 4px 12px #00000014!important;position:relative!important;transition:all .3s cubic-bezier(.4,0,.2,1)!important;overflow:hidden!important;margin-bottom:2rem!important}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:4px;height:100%;background:linear-gradient(180deg,#3699ff,#1bc5bd);border-radius:.75rem 0 0 .75rem}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:hover, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 25px #3699ff26}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:hover:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover:before{background:linear-gradient(180deg,#1bc5bd,#3699ff)}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1.25rem;margin-bottom:1.5rem;padding-bottom:1rem;border-bottom:2px solid #f1f3f4;position:relative}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header:after, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]:after, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-2px;left:0;width:50px;height:2px;background:linear-gradient(90deg,#3699ff,#1bc5bd);border-radius:1px}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-number, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3699ff,#1bc5bd)!important;color:#fff!important;width:32px!important;height:32px!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;font-size:.9rem!important;font-weight:800!important;box-shadow:0 4px 12px #3699ff66!important;flex-shrink:0!important;position:relative!important;transition:all .3s ease!important}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-number:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:-2px;background:linear-gradient(135deg,#3699ff,#1bc5bd);border-radius:50%;z-index:-1;opacity:.3;animation:_ngcontent-%COMP%_pulse 2s ease-in-out infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:.3}50%{transform:scale(1.1);opacity:.6}}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem;flex:1}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;padding:.5rem 0;background:transparent;border-radius:0;border:none;transition:all .2s ease}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row:hover, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover{background:transparent;transform:none}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-label, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%]{font-size:.85rem;font-weight:700;color:#4a5568;text-transform:capitalize;letter-spacing:.05rem;min-width:80px}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-value, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;color:#2d3748;flex:1}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .resolved-date, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%]{color:inherit;background:transparent;padding:.25rem 0;border-radius:0;font-weight:700}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .respond-btn, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .respond-btn[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .respond-btn[_ngcontent-%COMP%]{margin-left:.5rem!important}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]{margin-bottom:1.5rem;position:relative}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field:last-child, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child{margin-bottom:0}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]{font-size:.85rem;font-weight:800;color:#2d3748;text-transform:capitalize;letter-spacing:.15rem;margin-bottom:.75rem;display:flex;align-items:center;gap:.75rem;position:relative}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label i, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3699ff;font-size:.9rem;width:16px;text-align:center}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]:before{content:\\\"\\\\25b6\\\";color:#3699ff;font-size:.8rem;animation:_ngcontent-%COMP%_slideRight 1.5s ease-in-out infinite}@keyframes _ngcontent-%COMP%_slideRight{0%,to{transform:translate(0)}50%{transform:translate(3px)}}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]{font-size:1rem;color:#2d3748;line-height:1.7;padding:1.25rem;background:linear-gradient(135deg,#f8f9fa,#fff);border-radius:.75rem;border:1px solid #e2e8f0;box-shadow:inset 0 2px 8px #0000000a;position:relative;transition:all .3s ease}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:4px;height:100%;background:linear-gradient(180deg,#e2e8f0,#cbd5e0);border-radius:.75rem 0 0 .75rem}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content:hover, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover{box-shadow:inset 0 2px 12px #00000014;transform:translateY(-1px)}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff5f5,#fff);border-color:#fed7d7}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#feb2b2,#fc8181)}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.comment, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f0f9ff,#fff);border-color:#bee3f8}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.comment:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#90cdf4,#63b3ed)}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e8f5e8,#f0fff4);border-color:#c6f6d5}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#38a169,#2f855a)}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response:after, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]:after, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]:after{content:\\\"\\\\2713\\\";position:absolute;top:1rem;right:1rem;color:#38a169;font-weight:700;font-size:1.2rem;animation:_ngcontent-%COMP%_checkmark .6s ease-in-out}@keyframes _ngcontent-%COMP%_checkmark{0%{transform:scale(0) rotate(0)}50%{transform:scale(1.2) rotate(180deg)}to{transform:scale(1) rotate(360deg)}}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-separator, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]{height:2px;background:linear-gradient(90deg,transparent,#e5eaee,transparent);margin:2rem 0;position:relative}.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-separator:before, .review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]:before, .review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:-2px;left:50%;transform:translate(-50%);width:60px;height:6px;background:linear-gradient(90deg,#3699ff,#1bc5bd);border-radius:3px;box-shadow:0 2px 8px #3699ff4d}.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;color:#3f4254;margin-bottom:1rem;padding-bottom:.5rem;border-bottom:2px solid #3699ff}.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%]{background:#fff;border:1px solid #e5eaee;border-radius:.375rem;padding:1rem}.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%]{font-size:.9rem;color:#3f4254;line-height:1.5}.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]{padding:2rem;text-align:center;color:#6c7293}.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:.5rem;display:block;color:#3699ff}.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.9rem}.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]{background:#fff;border:1px solid #e5eaee;border-radius:.375rem}.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;color:#3f4254;margin-bottom:.5rem}.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border:1px solid #e5eaee;border-radius:.375rem;padding:.5rem .75rem;font-size:.875rem}.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#3699ff;box-shadow:0 0 0 .2rem #3699ff40}.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]{background-color:#1bc5bd;border-color:#1bc5bd;font-size:.875rem;padding:.5rem 1rem}.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover{background-color:#17a2b8;border-color:#17a2b8}.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;margin-bottom:.75rem;font-size:.75rem;color:#6c7293}.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], .review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem}.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%]:before, .review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%]:before{content:\\\"\\\";width:4px;height:4px;background-color:#6c7293;border-radius:50%}.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .view-btn[_ngcontent-%COMP%]{margin-left:auto;font-size:.65rem;padding:6px 10px;line-height:1;border-radius:.25rem;font-weight:500}.no-selection[_ngcontent-%COMP%]{padding:2rem;text-align:center;color:#6c7293}@media (max-width: 768px){.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.review-single-line[_ngcontent-%COMP%]{grid-template-columns:20px 1fr 100px 100px 120px 120px 30px;gap:.5rem;font-size:.8rem}.review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%]{font-size:.7rem;padding:.2rem .4rem}.review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%]{font-size:.65rem}.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]{font-size:.9rem}}@media (max-width: 480px){.review-single-line[_ngcontent-%COMP%]{grid-template-columns:20px 1fr 80px 80px 100px 100px 25px;gap:.25rem;padding:.25rem}.review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%]{font-size:.65rem;padding:.15rem .3rem}.review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%]{font-size:.6rem}.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]{font-size:.8rem}}.title-wrap[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.title-line[_ngcontent-%COMP%]{display:flex;align-items:baseline;gap:.75rem}.permit-title[_ngcontent-%COMP%]{font-size:1.05rem;font-weight:700;color:#181c32}.status-text[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;color:#3f4254;padding:.25rem .5rem;border-radius:.25rem;border:1px solid transparent;width:-moz-fit-content;width:fit-content}.status-text.status-approved[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.status-text.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0;color:#e65100;border:1px solid #ffcc02}.status-text.status-under-review[_ngcontent-%COMP%]{background-color:#e8eaf6;color:#3949ab;border:1px solid #c5cae9}.status-text.status-rejected[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828;border:1px solid #ffcdd2}.status-text.status-submitted[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1565c0;border:1px solid #bbdefb}.status-text.status-requires-resubmit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.status-text.status-conditional-approval[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.status-text.status-void[_ngcontent-%COMP%], .status-text.status-n-a[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.permit-number-line[_ngcontent-%COMP%]{font-size:.85rem;color:#6c7293;padding-bottom:.25rem}.fullscreen-loading-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#0006;display:flex;justify-content:center;align-items:center;z-index:9999}.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]{text-align:center;color:#3699ff;padding:2rem}.correction-content[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%], .correction-content[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#fff)!important;border-color:#e2e8f0!important;box-shadow:inset 0 2px 8px #0000000a!important}.correction-content[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]:before, .correction-content[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]:before{background:linear-gradient(180deg,#38a169,#2f855a)!important;width:4px!important;height:100%!important;border-radius:.75rem 0 0 .75rem!important}.notes-actions-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 12px #00000014;padding:1.5rem;max-width:700px;margin:auto}.notes-actions-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1.2rem;border-bottom:1px solid #e5e5e5;padding-bottom:.8rem}.notes-actions-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;margin:0}.edit-btn[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:6px 14px;border-radius:6px;font-size:.9rem;display:flex;align-items:center;gap:6px;cursor:pointer;transition:background .3s ease}.edit-btn[_ngcontent-%COMP%]:hover{background:#0056b3}.notes-actions-body[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem}.permit-detail-item-full[_ngcontent-%COMP%]{display:flex;flex-direction:column}.permit-detail-item-full[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;margin-bottom:.4rem;color:#333}.permit-detail-item-full[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;min-height:60px;padding:.6rem;border:1px solid #d1d5db;border-radius:8px;resize:vertical;font-size:.95rem}.kendo-dropdown[_ngcontent-%COMP%]{width:100%;border-radius:8px}\"]\n    });\n  }\n  return PermitViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}