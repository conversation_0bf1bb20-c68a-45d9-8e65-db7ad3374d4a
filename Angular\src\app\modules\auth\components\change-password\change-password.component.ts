import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from 'src/app/modules/services/user.service';
import { AppService } from 'src/app/modules/services/app.service';
import { HttpUtilsService } from '../../../services/http-utils.service';
import { CustomLayoutUtilsService } from 'src/app/modules/services/custom-layout.utils.service';

// import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
// import { HttpUtilsService } from '../../services/http-utils.service';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {
  isLoading$: any; //loader for form fields
  hasFormErrors: boolean = false; //boolean for checking error forms
  viewLoading: boolean = false; //boolean for loading fields
  changePassword: FormGroup;
  confirmPasswordError: boolean = false; //boolean for checking password error
  password: string = ''; //to save password
  passwordChange: boolean = true; //boolean to change password
  samePassword: boolean = false; //boolean to check passwords are same
  passwordIsValid = false; //boolean to check password strength
  passwordMinLength: number = 7; //set minimum password length
  loginUser: any = {};  //store localstorage json value
  cpasswordShown = true; //boolean for existing password shown
  newPasswordShown = true; //boolean for new password shown
  newConpasswordShown = true;  //boolean for new confirm password shown
  @Input() showClose: boolean=false;
  @Output() passEntry: EventEmitter<any> = new EventEmitter();
  companyPolicy:any;

  constructor(public modal: NgbActiveModal,
    private userService:UserService,
    private formBuilders: FormBuilder, private cdr: ChangeDetectorRef,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    public appService:AppService,
    private httpUtilService: HttpUtilsService

  ) { }

  // Method to handle cancel button click
  onCancelClick(): void {
    // Reset loading state when cancel is clicked
    this.httpUtilService.loadingSubject.next(false);
    this.modal.dismiss();
  }

  ngOnInit(): void {
    this.loginUser = this.appService.getLoggedInUser();
    //form field validation
    this.changePassword = this.formBuilders.group({
      existingPassword: new FormControl('', [Validators.required, Validators.maxLength(20)]),
      password: new FormControl('', [Validators.required, Validators.minLength(this.passwordMinLength), Validators.maxLength(20)]),
      confirmPassword: new FormControl('', [Validators.required, Validators.minLength(this.passwordMinLength), Validators.maxLength(20)])
    }, {
      validator: this.ConfirmedValidator('password', 'confirmPassword')
    });
    // this.getPolicy()
  }



 // Form validation based on the User's Security Policy
 buildForm() {
  this.changePassword.get('password')?.setValidators([Validators.required, Validators.maxLength(20), Validators.minLength(this.passwordMinLength)]);
  this.changePassword.get('confirmPassword')?.setValidators([Validators.required, Validators.maxLength(20), Validators.minLength(this.passwordMinLength)]);
}

  // function to check whether the form has any error
  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.changePassword.controls[controlName];
    if (!control) {
      return false;
    }
    const result =
      this.changePassword.controls[controlName].hasError(validationType) &&
      (control.dirty || control.touched);
    return result;
  }
  // Form validation for new password and confirm password
  //param:controlName - new password field value, matchingControlName - confirm password field value
  ConfirmedValidator(controlName: string, matchingControlName: string) {
    return (formGroup: FormGroup) => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];
      if (matchingControl.errors && !matchingControl.errors.confirmedValidator) {
        return;
      }
      if (control.value !== matchingControl.value) {
        matchingControl.setErrors({ confirmedValidator: true });
        this.confirmPasswordError = true;
      } else {
        this.confirmPasswordError = false;
        matchingControl.setErrors(null);
      }
    }
  }

  //function to modify boolean depending on whether the  existing password eye symbol is on or off
  cshowpassword(event: any) {
    this.cpasswordShown = event;
  }
  //function to modify boolean depending on whether the  new password eye symbol is on or off
  newshowpassword(event: any) {
    this.newPasswordShown = event;
  }
  //function to modify boolean depending on whether the new confirm password eye symbol is on or off
  newconshowpassword(event: any) {
    this.newConpasswordShown = event;
  }


  // function to check whether current password and new password are same
  checkSamePassword() {
    const controls = this.changePassword.controls;
    if (controls.existingPassword.value === controls.password.value) {
      this.samePassword = true;
      this.cdr.markForCheck();
    } else {
      this.samePassword = false;
      this.cdr.markForCheck();
    }
    console.log(this.samePassword , this.samePassword)
  }
  // function to check the password strength
  // Param : event: event fires when password strength is changed.
  onPasswordChange(event: any) {
    this.password = this.changePassword.controls['password'].value;
    this.passwordIsValid = event;
    console.log('this.passwordIsValid ', this.passwordIsValid)
    this.cdr.markForCheck();
  }
  //function to save a form fields to API
  save() {
    this.hasFormErrors = false;
    this.viewLoading = true;
    const controls = this.changePassword.controls;
    //function to check whether validated fields are filled
    if (this.changePassword.invalid) {
      Object.keys(controls).forEach(controlName =>
        controls[controlName].markAsTouched()
      );
      this.viewLoading = false;
      this.cdr.markForCheck();
      return;
    }
    const queryparam = {
      userId: this.loginUser.userId,
      oldPassword: controls.existingPassword.value,
      newPassword: controls.password.value
    };
    // show loader
    this.httpUtilService.loadingSubject.next(true);
    //api call to update password to API
    this.userService.changePassword(queryparam).subscribe({
      next: (res: { isFault: any; responseData: { message: any; }; }) => {
        this.httpUtilService.loadingSubject.next(false);
        if (!res.isFault) {
          this.router.navigate(['/auth/login'], { relativeTo: this.activatedRoute });
          this.modal.close();
          if(this.showClose===false){
                    this.customLayoutUtilsService.showSuccess(res.responseData.message, '');
            // //alert(res.responseData.message);
          }
        } else {
                    this.customLayoutUtilsService.showSuccess(res.responseData.message, '');

          // //alert(res.responseData.message);
        }
      },
      error: () => {
        this.httpUtilService.loadingSubject.next(false);
      }
    });

  }

  onValidityCheck(){
     console.log('this.viewLoading ', this.viewLoading)
      console.log('this.passwordIsValid ', this.passwordIsValid)
       console.log('this.changePassword ', this.changePassword.valid)
        console.log('this.samePassword ', this.samePassword)
                console.log('this.changePassword ', this.changePassword)
    if(this.viewLoading || this.passwordIsValid === false || this.changePassword.valid === false || this.samePassword === true){
      return true;
    }else{
      return false
    }
  }

}
