{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport { AppSettings } from 'src/app/app.settings';\nimport * as _ from 'lodash';\nimport { map } from 'rxjs';\nimport { each } from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/http-utils.service\";\nimport * as i3 from \"../../services/custom-layout.utils.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"../../services/user.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@ng-select/ng-select\";\nimport * as i9 from \"igniteui-angular\";\nimport * as i10 from \"../../shared/checkbox-group/checkbox.component\";\nimport * as i11 from \"../../shared/checkbox-group/checkbox-group.component\";\nconst _c0 = a0 => ({\n  \"active\": a0\n});\nfunction AddUserComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add User\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Edit User\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_div_21_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_div_21_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_div_21_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1, \"Enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_div_21_div_28_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_div_21_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 52)(2, \"div\", 53)(3, \"div\", 25)(4, \"label\", 26);\n    i0.ɵɵtext(5, \"Auto Password?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\")(7, \"label\", 54)(8, \"input\", 55);\n    i0.ɵɵlistener(\"change\", function AddUserComponent_div_21_div_28_Template_input_change_8_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onAutoPasswordChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \"\\u00A0 \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"div\", 56)(11, \"div\", 25)(12, \"label\", 26);\n    i0.ɵɵtext(13, \"Password\");\n    i0.ɵɵelementStart(14, \"sup\", 27);\n    i0.ɵɵtext(15, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 57);\n    i0.ɵɵelement(17, \"input\", 58);\n    i0.ɵɵelementStart(18, \"div\", 59)(19, \"span\", 60);\n    i0.ɵɵlistener(\"click\", function AddUserComponent_div_21_div_28_Template_span_click_19_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.newPasswordShown === true ? ctx_r2.newshowpassword(false) : ctx_r2.newshowpassword(true));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(20, AddUserComponent_div_21_div_28_span_20_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"type\", ctx_r2.newPasswordShown === true ? \"password\" : \"text\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.newPasswordShown === true ? \"bi bi-eye-slash-fill\" : \"bi bi-eye-fill\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.controlHasError(\"required\", \"password\"));\n  }\n}\nfunction AddUserComponent_div_21_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMapInterpolate1(\"background-image: url(\", ctx_r2.imageUrl, \"); border-radius: 50%;\");\n  }\n}\nfunction AddUserComponent_div_21_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_div_21_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25)(3, \"label\", 26);\n    i0.ɵɵtext(4, \"Status \");\n    i0.ɵɵelementStart(5, \"sup\", 27);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 64)(8, \"div\", 65);\n    i0.ɵɵelement(9, \"input\", 66);\n    i0.ɵɵelementStart(10, \"label\", 67);\n    i0.ɵɵtext(11, \" Active \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 65);\n    i0.ɵɵelement(13, \"input\", 68);\n    i0.ɵɵelementStart(14, \"label\", 69);\n    i0.ɵɵtext(15, \" Inactive \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction AddUserComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"div\", 25)(4, \"label\", 26);\n    i0.ɵɵtext(5, \"First Name\");\n    i0.ɵɵelementStart(6, \"sup\", 27);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"input\", 28);\n    i0.ɵɵtemplate(9, AddUserComponent_div_21_span_9_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 24)(11, \"div\", 25)(12, \"label\", 26);\n    i0.ɵɵtext(13, \"Last Name\");\n    i0.ɵɵelementStart(14, \"sup\", 27);\n    i0.ɵɵtext(15, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"input\", 30);\n    i0.ɵɵtemplate(17, AddUserComponent_div_21_span_17_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 23)(19, \"div\", 24)(20, \"div\", 31)(21, \"div\", 25)(22, \"label\", 26);\n    i0.ɵɵtext(23, \"Email\");\n    i0.ɵɵelementStart(24, \"sup\", 27);\n    i0.ɵɵtext(25, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(26, \"input\", 32);\n    i0.ɵɵtemplate(27, AddUserComponent_div_21_span_27_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(28, AddUserComponent_div_21_div_28_Template, 21, 3, \"div\", 33);\n    i0.ɵɵelementStart(29, \"div\", 31)(30, \"div\", 25)(31, \"label\", 26);\n    i0.ɵɵtext(32, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"igx-input-group\");\n    i0.ɵɵelement(34, \"input\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 31)(36, \"div\", 25)(37, \"label\", 26);\n    i0.ɵɵtext(38, \"Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(39, \"input\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 24)(41, \"div\", 36)(42, \"div\", 37)(43, \"div\", 38)(44, \"label\", 39);\n    i0.ɵɵtext(45, \"Profile Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(46, \"br\");\n    i0.ɵɵelementStart(47, \"div\", 40)(48, \"div\", 41);\n    i0.ɵɵtemplate(49, AddUserComponent_div_21_div_49_Template, 1, 3, \"div\", 42)(50, AddUserComponent_div_21_div_50_Template, 2, 0, \"div\", 43);\n    i0.ɵɵelementStart(51, \"label\", 44)(52, \"i\", 45);\n    i0.ɵɵlistener(\"click\", function AddUserComponent_div_21_Template_i_click_52_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const imguploader_r4 = i0.ɵɵreference(54);\n      return i0.ɵɵresetView(imguploader_r4.click());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(53, \"input\", 46, 0)(55, \"input\", 47);\n    i0.ɵɵelementStart(56, \"span\", 48);\n    i0.ɵɵelement(57, \"i\", 49);\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵtemplate(58, AddUserComponent_div_21_div_58_Template, 16, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.controlHasError(\"required\", \"firstname\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.controlHasError(\"pattern\", \"lastname\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.controlHasError(\"email\", \"email\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"igxMask\", \"(************* Ext. 9999\");\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.image !== \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.image === \"\" || ctx_r2.image === null);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id !== 0);\n  }\n}\nfunction AddUserComponent_div_22_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_div_22_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 79)(2, \"label\", 80);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 79)(5, \"checkbox-group\", 81)(6, \"checkbox\", 82);\n    i0.ɵɵtext(7, \"Read\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"checkbox\", 83);\n    i0.ɵɵtext(9, \"Write\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"checkbox\", 84);\n    i0.ɵɵtext(11, \"Delete \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const p_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(p_r6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"formControlName\", p_r6);\n  }\n}\nfunction AddUserComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 70)(2, \"label\", 71);\n    i0.ɵɵtext(3, \"Role \");\n    i0.ɵɵelementStart(4, \"sup\", 27);\n    i0.ɵɵtext(5, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"ng-select\", 72);\n    i0.ɵɵlistener(\"change\", function AddUserComponent_div_22_Template_ng_select_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeRoleAccess($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AddUserComponent_div_22_span_7_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 73)(9, \"div\", 74)(10, \"table\", 75)(11, \"th\", 76);\n    i0.ɵɵtext(12, \"Permissions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 76)(14, \"div\", 77)(15, \"span\");\n    i0.ɵɵtext(16, \"Read\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Write\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Delete\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, AddUserComponent_div_22_tr_21_Template, 12, 2, \"tr\", 78);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r2.roleArray)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.appService.controlHasError(\"required\", \"role\", ctx_r2.userForm));\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.perNameArray);\n  }\n}\nfunction AddUserComponent_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function AddUserComponent_button_25_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showTab(\"basic\", $event));\n    });\n    i0.ɵɵtext(1, \"Previous\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function AddUserComponent_button_30_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showTab(\"role\", $event));\n    });\n    i0.ɵɵtext(1, \"Next\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function AddUserComponent_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save());\n    });\n    i0.ɵɵtext(1, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.userForm.invalid);\n  }\n}\n// import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nexport class AddUserComponent {\n  modal;\n  cdr;\n  httpUtilService;\n  layoutUtilService;\n  fb;\n  appService;\n  userService;\n  id;\n  defaultPermissions;\n  passEntry = new EventEmitter();\n  userForm;\n  users = {};\n  loginUser = {};\n  roleArray = [];\n  medicalCentersArray = [];\n  pharmacyArray = [];\n  image = '';\n  imageUrl = '';\n  perNameArray = []; //store permission in UI\n  permissionArray = []; // field to save the initial values of default permissions\n  selectedTab = 'basic'; //store navigation tab\n  IsAutoPassword = true;\n  newPasswordShown = true; //boolean for password shown\n  showExternalPassword = false;\n  constructor(modal, cdr, httpUtilService, layoutUtilService, fb, appService, userService) {\n    this.modal = modal;\n    this.cdr = cdr;\n    this.httpUtilService = httpUtilService;\n    this.layoutUtilService = layoutUtilService;\n    this.fb = fb;\n    this.appService = appService;\n    this.userService = userService;\n    // No global loading subscription - use local loading state only\n  }\n  //policyForm: FormGroup;\n  ngOnInit() {\n    console.log(\"Call add client component\", this.id);\n    this.loginUser = this.appService.getLoggedInUser();\n    this.permissionArray = this.defaultPermissions;\n    this.loadForm();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n    this.userService.getAllRoles({\n      paginate: false\n    }).pipe(map(data => data)).subscribe(data => {\n      if (!data.isFault) {\n        console.log(\"Line: 78\", data);\n        //disable the loading\n        this.roleArray = data.responseData.roles;\n        console.log('this.roleArray', this.roleArray);\n        this.cdr.markForCheck();\n      } else {\n        this.roleArray = [];\n        this.cdr.markForCheck();\n      }\n    });\n    this.cdr.markForCheck();\n  }\n  loadForm() {\n    const formGroup = {};\n    formGroup['firstname'] = new FormControl('', Validators.compose([Validators.required]));\n    formGroup['lastname'] = new FormControl('', Validators.compose([Validators.required]));\n    formGroup['email'] = new FormControl('', Validators.compose([Validators.email]));\n    formGroup['phone'] = new FormControl('');\n    formGroup['password'] = new FormControl('');\n    formGroup['IsAuto'] = new FormControl('');\n    formGroup['role'] = new FormControl('');\n    formGroup['title'] = new FormControl('');\n    formGroup['role'] = new FormControl('');\n    formGroup['status'] = new FormControl('');\n    let pArray = [];\n    // assign the form fields for Permissions\n    this.permissionArray.forEach(perm => {\n      pArray.push(perm.Name);\n      formGroup[perm.Name] = new FormControl('');\n    });\n    this.perNameArray = pArray;\n    this.userForm = this.fb.group(formGroup);\n    // field to display the Permission in UI\n    this.perNameArray = pArray;\n  }\n  //function to modify boolean depending on whether the  password eye symbol is on or off\n  newshowpassword(event) {\n    this.newPasswordShown = event;\n  }\n  togglePasswordVisibility() {\n    this.showExternalPassword = !this.showExternalPassword;\n  }\n  onAutoPasswordChange(event) {\n    const controls = this.userForm.controls;\n    this.IsAutoPassword = controls.IsAuto.value;\n    if (this.IsAutoPassword == false) {\n      this.userForm.get('password')?.setValidators(Validators.compose([Validators.required]));\n      this.userForm.get('password')?.updateValueAndValidity();\n      this.userForm.controls['password'].enable();\n    } else {\n      this.userForm.controls['password'].disable();\n    }\n  }\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    console.log(\"Call patch form component line: 151\", this.id);\n    this.userService.getUser({\n      userId: this.id,\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: user => {\n        console.log(\"Line: 153\", user.responseData.data); // <-- This should now print\n        this.httpUtilService.loadingSubject.next(false);\n        if (!user.isFault) {\n          const userData = user.responseData;\n          let rPerms = JSON.parse(user.responseData.rolePermissions);\n          this.image = user.responseData.imageName;\n          this.imageUrl = this.appService.ImageUrl(this.image);\n          this.userForm.patchValue({\n            firstname: userData.firstName,\n            lastname: userData.lastName,\n            phone: userData.phoneNo?.replace(/[()\\-\\sExt.]/g, '') || '',\n            status: userData.userStatus === 'Active' ? 'true' : 'false',\n            email: userData.email,\n            title: userData.title,\n            role: userData.roleId,\n            pharmacyId: userData.pharmacyId,\n            medicalCenterId: userData.medicalCenterId\n          });\n          // patch the permission values\n          let self = this;\n          each(rPerms, r => {\n            _.forEach(r, function (value, key) {\n              self.userForm.patchValue({\n                [key]: value\n              });\n            });\n          });\n        } else {\n          console.warn(\"User response has isFault = true\", user.responseData);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error(\"API call failed\", err);\n      }\n    });\n  }\n  //function to browse imgae file\n  browseFile(event) {\n    const fSize = Math.round(event.target.files[0].size / 1024);\n    const file = event.target.files[0];\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    let selectedImage = file.name;\n    const fileData = new FormData();\n    fileData.append('files', file);\n    if (fSize > 500) {\n      this.layoutUtilService.showError(selectedImage + ' - File size upto 500KB.Couldnt upload the files', '');\n    } else {\n      this.userService.uploadImage(fileData).subscribe(res => {\n        if (!res.isFault) {\n          this.image = res.responseData.fileName;\n          this.imageUrl = AppSettings.IMAGEPATH + this.image;\n          this.cdr.markForCheck();\n        }\n      });\n    }\n  }\n  controlHasError(validation, controlName) {\n    const control = this.userForm.controls[controlName];\n    if (!control) {\n      return false;\n    }\n    let result = control.hasError(validation) && (control.dirty || control.touched);\n    return result;\n  }\n  save() {\n    let controls = this.userForm.controls;\n    if (this.userForm.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      //this.layoutUtilService.showError('Please fill all required fields', '')\n      return;\n    }\n    let userData = this.prepareuser();\n    console.log(\"Line: 203\", userData);\n    if (this.id === 0) {\n      this.create(userData);\n    } else {\n      this.edit(userData);\n    }\n  }\n  // API to update the user details based on the userid\n  edit(userData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.userService.updateUser(userData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  // API to save new user details\n  create(userData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.userService.createUser(userData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  //function to map input form fields to API fields\n  prepareuser() {\n    const formData = this.userForm.value;\n    this.users.firstName = formData.firstname;\n    this.users.lastName = formData.lastname;\n    this.users.phoneNo = formData.phone !== '' ? this.appService.getPhoneFormat(formData.phone) : '';\n    this.users.userStatus = formData.status === 'true' ? 'Active' : 'Inactive';\n    this.users.roleId = formData.role;\n    this.users.title = formData.title;\n    this.users.password = formData.password;\n    this.users.isAuto = formData.IsAuto;\n    this.users.imageName = this.image;\n    this.users.loggedInUserId = this.loginUser.userId;\n    this.users.userId = this.id;\n    this.users.email = formData.email;\n    this.users.isEmailNotificationEnabled = true;\n    this.users.pharmacyId = formData.pharmacyId;\n    this.users.medicalCenterId = formData.medicalCenterId;\n    // let controls = this.userForm.controls;\n    // let perArray: any = [];\n    // let self: any = this;\n    // _.forEach(controls, function (value, key) {\n    //   let rjson = _.find(self.permissionArray, function (o) {\n    //     return o.Name === key;\n    //   });\n    //   if (rjson !== undefined) {\n    //     let permissionJson = self.getPermissionJson(rjson, value);\n    //     perArray.push(perm+issionJson);\n    //   }\n    // });\n    // this.users.permissions = perArray;\n    return this.users;\n  }\n  //function to navigate tab\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  //function to change permission based on role name\n  changeRoleAccess(event) {\n    console.log('event ', event);\n    this.httpUtilService.loadingSubject.next(true);\n    this.userService.getRole({\n      roleId: event.roleId\n    }).subscribe(role => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!role.isFault) {\n        let rPerms = JSON.parse(role.responseData.rolePermissions);\n        // patch the permission values\n        let self = this;\n        each(rPerms, r => {\n          _.forEach(r, function (value, key) {\n            console.log('value ', value);\n            console.log('key ', key);\n            self.userForm.patchValue({\n              [key]: value\n            });\n          });\n        });\n        console.log('form data ', self.userForm);\n        this.cdr.markForCheck();\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  changePharmacy(event) {}\n  changeMedicalCenter(event) {}\n  static ɵfac = function AddUserComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AddUserComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.HttpUtilsService), i0.ɵɵdirectiveInject(i3.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.AppService), i0.ɵɵdirectiveInject(i6.UserService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddUserComponent,\n    selectors: [[\"app-user-add\"]],\n    inputs: {\n      id: \"id\",\n      defaultPermissions: \"defaultPermissions\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 32,\n    vars: 14,\n    consts: [[\"imguploader\", \"\"], [1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [\"id\", \"example-modal-sizes-title-lg\", 1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [\"class\", \"card-body response-list\", 4, \"ngIf\"], [1, \"modal-footer\", \"justify-content-between\"], [1, \"float-left\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-elevate btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-elevate btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-elevate btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"card-body\", \"response-list\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-6\"], [1, \"form-group\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"name\", \"firstname\", \"formControlName\", \"firstname\", \"placeholder\", \"Type Here\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"custom-error-css\", 4, \"ngIf\"], [\"type\", \"text\", \"name\", \"lastname\", \"formControlName\", \"lastname\", \"placeholder\", \"Type Here\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-12\", \"mt-4\"], [\"type\", \"text\", \"name\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Type Here\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"col-xl-12 mt-4\", 4, \"ngIf\"], [\"name\", \"phone\", \"id\", \"tel\", \"igxInput\", \"\", \"type\", \"text\", \"autocomplete\", \"off\", \"formControlName\", \"phone\", 1, \"form-control\", \"form-control-sm\", 3, \"igxMask\"], [\"type\", \"text\", \"name\", \"title\", \"formControlName\", \"title\", \"placeholder\", \"Type Here\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-12\", \"flex-center\"], [1, \"form-group\", \"row\"], [1, \"col-lg-12\"], [1, \"fs-5\", \"fw-bold\", \"form-label\"], [1, \"d-flex\", \"justify-content-center\", \"w-100\", \"mb-2\"], [\"data-kt-image-input\", \"true\", 1, \"image-input\", \"image-input-outline\"], [\"class\", \"image-input-wrapper w-150px h-150px\", 3, \"style\", 4, \"ngIf\"], [\"class\", \"image-input-wrapper bg-light-primary w-150px h-150px text-center\", \"style\", \"border-radius: 50%;\", 4, \"ngIf\"], [\"data-kt-image-input-action\", \"change\", \"data-bs-toggle\", \"tooltip\", \"title\", \"\", \"data-bs-original-title\", \"Change avatar\", 1, \"btn\", \"btn-icon\", \"btn-circle\", \"btn-active-color-primary\", \"w-25px\", \"h-25px\", \"bg-body\", \"shadow\"], [1, \"bi\", \"bi-pencil-fill\", \"fs-7\", 3, \"click\"], [\"type\", \"file\", \"name\", \"avatar\", \"accept\", \".png, .jpg, .jpeg\", 1, \"visually-hidden\"], [\"type\", \"hidden\", \"name\", \"avatar_remove\"], [\"data-kt-image-input-action\", \"cancel\", \"data-bs-toggle\", \"tooltip\", \"title\", \"\", \"data-bs-original-title\", \"Cancel avatar\", 1, \"btn\", \"btn-icon\", \"btn-circle\", \"btn-active-color-primary\", \"w-25px\", \"h-25px\", \"bg-body\", \"shadow\"], [1, \"bi\", \"bi-x\", \"fs-2\"], [\"class\", \"row mt-4\", 4, \"ngIf\"], [1, \"custom-error-css\"], [1, \"row\", \"d-flex\"], [1, \"col-xl-4\"], [1, \"form-check\", \"form-check-custom\", \"form-switch\", \"form-switch-sm\", \"mb-3\", \"wd-fix\"], [\"type\", \"checkbox\", \"name\", \"IsAuto\", \"formControlName\", \"IsAuto\", \"value\", \"true\", 1, \"form-check-input\", 3, \"change\"], [1, \"col-xl-8\"], [1, \"input-group\", \"mb-0\"], [\"name\", \"password\", \"placeholder\", \"Type Here\", \"autocomplete\", \"off\", \"formControlName\", \"password\", 1, \"form-control\", \"form-control-sm\", 3, \"type\"], [1, \"input-group-text\"], [3, \"click\", \"ngClass\"], [1, \"image-input-wrapper\", \"w-150px\", \"h-150px\"], [1, \"image-input-wrapper\", \"bg-light-primary\", \"w-150px\", \"h-150px\", \"text-center\", 2, \"border-radius\", \"50%\"], [1, \"image-input-wrapper\", \"w-150px\", \"h-150px\", 2, \"background-image\", \"url('./assets/media/svg/avatars/blank.svg')\", \"border-radius\", \"50%\"], [1, \"d-flex\", \"mt-2\"], [1, \"form-check\", \"form-check-inline\", \"form-check-sm\"], [\"type\", \"radio\", \"value\", \"true\", \"name\", \"status\", \"id\", \"Active\", \"formControlName\", \"status\", 1, \"form-check-input\"], [\"for\", \"Active\", 1, \"form-check-label\"], [\"type\", \"radio\", \"value\", \"false\", \"name\", \"status\", \"id\", \"Inactive\", \"formControlName\", \"status\", 1, \"form-check-input\"], [\"for\", \"Inactive\", 1, \"form-check-label\"], [1, \"col-lg-6\"], [1, \"fw-semibold\", \"fs-6\", \"mb-2\"], [\"bindLabel\", \"roleName\", \"name\", \"role\", \"formControlName\", \"role\", \"bindValue\", \"roleId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [1, \"form-group\", \"row\", \"mb-4\", \"px-10\", \"pt-2\"], [1, \"col-12\", \"d-flex\", \"justify-content-start\"], [1, \"w-100\"], [1, \"p-1\", \"fw-bold\", \"fs-6\", 2, \"width\", \"390px !important\"], [1, \"d-flex\", \"justify-content-between\"], [4, \"ngFor\", \"ngForOf\"], [1, \"p-1\"], [1, \"fw-semibold\", \"fs-6\"], [\"ngDefaultControl\", \"\", 3, \"formControlName\"], [\"value\", \"Read\"], [\"value\", \"Write\", 2, \"padding-left\", \"145px\"], [\"value\", \"Delete\", 2, \"padding-left\", \"145px\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-elevate\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-elevate\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-elevate\", \"btn-sm\", 3, \"click\", \"disabled\"]],\n    template: function AddUserComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, AddUserComponent_div_4_Template, 2, 0, \"div\", 4)(5, AddUserComponent_div_5_Template, 2, 0, \"div\", 4);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"i\", 6);\n        i0.ɵɵlistener(\"click\", function AddUserComponent_Template_i_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelementContainerStart(9);\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"ul\", 11)(14, \"li\", 12)(15, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function AddUserComponent_Template_a_click_15_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(16, \" Profile \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"li\", 12)(18, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function AddUserComponent_Template_a_click_18_listener($event) {\n          return ctx.showTab(\"role\", $event);\n        });\n        i0.ɵɵtext(19, \" Role & Access \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(20, \"form\", 14);\n        i0.ɵɵtemplate(21, AddUserComponent_div_21_Template, 59, 8, \"div\", 15)(22, AddUserComponent_div_22_Template, 22, 5, \"div\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 16)(24, \"div\", 17);\n        i0.ɵɵtemplate(25, AddUserComponent_button_25_Template, 2, 0, \"button\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\")(27, \"button\", 19);\n        i0.ɵɵlistener(\"click\", function AddUserComponent_Template_button_click_27_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵtext(28, \"Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(29, \" \\u00A0 \");\n        i0.ɵɵtemplate(30, AddUserComponent_button_30_Template, 2, 0, \"button\", 20)(31, AddUserComponent_button_31_Template, 2, 1, \"button\", 21);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.selectedTab === \"role\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.userForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"role\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"role\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"role\");\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.CheckboxControlValueAccessor, i4.RadioControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i8.NgSelectComponent, i9.IgxInputGroupComponent, i9.IgxInputDirective, i9.IgxMaskDirective, i10.CheckboxComponent, i11.CheckboxGroupComponent],\n    styles: [\".input-group-text[_ngcontent-%COMP%] {\\n  padding-top: 5px;\\n  padding-bottom: 5px;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n}\\n\\n.mx-10px[_ngcontent-%COMP%] {\\n  margin-right: 6rem !important;\\n  margin-left: 8rem !important;\\n}\\n\\nbody[_ngcontent-%COMP%]:not(:-moz-handler-blocked)   fieldset[_ngcontent-%COMP%] {\\n  display: table-cell;\\n}\\n\\n.toggle[_ngcontent-%COMP%] {\\n  font-size: 0;\\n  display: flex;\\n  flex-flow: row nowrap;\\n  justify-content: flex-start;\\n  align-items: stretch;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 0;\\n  height: 0;\\n  position: absolute;\\n  left: -9999px;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]    + label[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0.6rem 1.5rem;\\n  box-sizing: border-box;\\n  position: relative;\\n  display: inline-block;\\n  border: solid 1px #DDD;\\n  background-color: #FFF;\\n  font-size: 1.1rem;\\n  line-height: 160%;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]    + label[_ngcontent-%COMP%]:first-of-type {\\n  border-radius: 6px 0 0 6px;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]    + label[_ngcontent-%COMP%]:last-of-type {\\n  border-radius: 0 6px 6px 0;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover    + label[_ngcontent-%COMP%] {\\n  border-color: #213140;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + label[_ngcontent-%COMP%] {\\n  background-color: #11A7DB;\\n  color: #FFF;\\n  box-shadow: 0 0 10px rgba(17, 167, 219, 0.5);\\n  border-color: #11A7DB;\\n  z-index: 1;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked:disabled    + label[_ngcontent-%COMP%] {\\n  background-color: #E1E3EA;\\n  color: white;\\n  border-color: #E1E3EA;\\n  box-shadow: none;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked:disabled    + label[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover    + label[_ngcontent-%COMP%] {\\n  border-color: #DDD;\\n}\\n\\n.color-picker[_ngcontent-%COMP%] {\\n  opacity: 1 !important;\\n  visibility: visible !important;\\n}\\n\\n.k-picker-md[_ngcontent-%COMP%] {\\n  font-size: 12px !important;\\n  line-height: 1.4285714286;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "Validators", "AppSettings", "_", "map", "each", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AddUserComponent_div_21_div_28_Template_input_change_8_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onAutoPasswordChange", "ɵɵelement", "AddUserComponent_div_21_div_28_Template_span_click_19_listener", "newPasswordShown", "newshowpassword", "ɵɵtemplate", "AddUserComponent_div_21_div_28_span_20_Template", "ɵɵadvance", "ɵɵproperty", "controlHasError", "ɵɵstyleMapInterpolate1", "imageUrl", "AddUserComponent_div_21_span_9_Template", "AddUserComponent_div_21_span_17_Template", "AddUserComponent_div_21_span_27_Template", "AddUserComponent_div_21_div_28_Template", "AddUserComponent_div_21_div_49_Template", "AddUserComponent_div_21_div_50_Template", "AddUserComponent_div_21_Template_i_click_52_listener", "_r1", "imguploader_r4", "ɵɵreference", "click", "AddUserComponent_div_21_div_58_Template", "id", "image", "ɵɵtextInterpolate", "p_r6", "ɵɵpropertyInterpolate", "AddUserComponent_div_22_Template_ng_select_change_6_listener", "_r5", "changeRoleAccess", "AddUserComponent_div_22_span_7_Template", "AddUserComponent_div_22_tr_21_Template", "<PERSON><PERSON><PERSON><PERSON>", "appService", "userForm", "perNameArray", "AddUserComponent_button_25_Template_button_click_0_listener", "_r7", "showTab", "AddUserComponent_button_30_Template_button_click_0_listener", "_r8", "AddUserComponent_button_31_Template_button_click_0_listener", "_r9", "save", "invalid", "AddUserComponent", "modal", "cdr", "httpUtilService", "layoutUtilService", "fb", "userService", "defaultPermissions", "passEntry", "users", "loginUser", "medicalCentersArray", "pharmacyArray", "permissionArray", "selectedTab", "IsAutoPassword", "showExternalPassword", "constructor", "ngOnInit", "console", "log", "getLoggedInUser", "loadForm", "patchForm", "getAllRoles", "paginate", "pipe", "data", "subscribe", "<PERSON><PERSON><PERSON>", "responseData", "roles", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formGroup", "compose", "required", "email", "p<PERSON><PERSON>y", "for<PERSON>ach", "perm", "push", "Name", "group", "event", "togglePasswordVisibility", "controls", "IsAuto", "value", "get", "setValidators", "updateValueAndValidity", "enable", "disable", "loadingSubject", "next", "getUser", "userId", "loggedInUserId", "user", "userData", "rPerms", "JSON", "parse", "rolePermissions", "imageName", "ImageUrl", "patchValue", "firstname", "firstName", "lastname", "lastName", "phone", "phoneNo", "replace", "status", "userStatus", "title", "role", "roleId", "pharmacyId", "medicalCenterId", "self", "r", "key", "warn", "error", "err", "browseFile", "fSize", "Math", "round", "target", "files", "size", "file", "reader", "FileReader", "readAsDataURL", "selectedImage", "name", "fileData", "FormData", "append", "showError", "uploadImage", "res", "fileName", "IMAGEPATH", "validation", "controlName", "control", "result", "<PERSON><PERSON><PERSON><PERSON>", "dirty", "touched", "Object", "keys", "<PERSON><PERSON><PERSON><PERSON>ched", "prepareuser", "create", "edit", "updateUser", "showSuccess", "message", "emit", "close", "createUser", "formData", "getPhoneFormat", "password", "isAuto", "isEmailNotificationEnabled", "tab", "getRole", "detectChanges", "changePharmacy", "changeMedicalCenter", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "ChangeDetectorRef", "i2", "HttpUtilsService", "i3", "CustomLayoutUtilsService", "i4", "FormBuilder", "i5", "AppService", "i6", "UserService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "AddUserComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "AddUserComponent_div_4_Template", "AddUserComponent_div_5_Template", "AddUserComponent_Template_i_click_7_listener", "dismiss", "AddUserComponent_Template_a_click_15_listener", "AddUserComponent_Template_a_click_18_listener", "AddUserComponent_div_21_Template", "AddUserComponent_div_22_Template", "AddUserComponent_button_25_Template", "AddUserComponent_Template_button_click_27_listener", "AddUserComponent_button_30_Template", "AddUserComponent_button_31_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\add-user\\user-add.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\add-user\\user-add.component.html"], "sourcesContent": ["import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\n\nimport { AppService } from '../../services/app.service';\nimport { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';\n\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { AppSettings } from 'src/app/app.settings';\n\n\nimport * as _ from 'lodash';\nimport { map } from 'rxjs';\nimport { UserService } from '../../services/user.service';\nimport { each } from 'lodash';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\n\n// import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\n\n\n\n@Component({\n  selector: 'app-user-add',\n  templateUrl: './user-add.component.html',\n  styleUrls: ['./user-add.component.scss']\n})\nexport class AddUserComponent implements OnInit {\n\n    @Input() id: number;\n    @Input() defaultPermissions:any;\n    @Output() passEntry: EventEmitter<any> = new EventEmitter();\n    userForm: FormGroup;\n    users:any={};\n    loginUser:any={};\n    roleArray: any = []\n    medicalCentersArray:any=[];\n    pharmacyArray:any =[]\n    image = '';\n    imageUrl = '';\n    perNameArray: any = [];//store permission in UI\n    permissionArray: any = [];// field to save the initial values of default permissions\n    selectedTab: string = 'basic'; //store navigation tab\n    IsAutoPassword: boolean = true;\n    newPasswordShown = true; //boolean for password shown\n    showExternalPassword = false;\n\n  constructor(public modal: NgbActiveModal,\n    private cdr: ChangeDetectorRef,\n    private httpUtilService: HttpUtilsService,\n    private layoutUtilService: CustomLayoutUtilsService,\n    private fb: FormBuilder,\n    public appService: AppService,\n    private userService: UserService,\n    ) {\n    // No global loading subscription - use local loading state only\n     }\n    //policyForm: FormGroup;\n\n  ngOnInit(): void {\n    console.log(\"Call add client component\", this.id)\n    this.loginUser = this.appService.getLoggedInUser();\n    this.permissionArray = this.defaultPermissions;\n    this.loadForm();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n    this.userService.getAllRoles({ paginate: false }).pipe(\n      map((data: any) => data as any)).subscribe((data:any) => {\n        if (!data.isFault) {\n          console.log(\"Line: 78\", data)\n          //disable the loading\n          this.roleArray = data.responseData.roles;\n          console.log('this.roleArray', this.roleArray)\n          this.cdr.markForCheck();\n        } else {\n          this.roleArray = [];\n          this.cdr.markForCheck();\n        }\n      });\n\n    this.cdr.markForCheck();\n  }\n\n\n  loadForm() {\n    const formGroup: any = {};\n    formGroup['firstname'] = new FormControl('', Validators.compose([Validators.required]));\n    formGroup['lastname'] = new FormControl('', Validators.compose([Validators.required]));\n    formGroup['email'] = new FormControl('',Validators.compose([Validators.email]));\n    formGroup['phone'] = new FormControl('');\n    formGroup['password'] = new FormControl('');\n    formGroup['IsAuto'] = new FormControl('');\n    formGroup['role'] = new FormControl('');\n    formGroup['title'] = new FormControl('');\n     formGroup['role'] = new FormControl('');\n    formGroup['status'] = new FormControl('');\n    let pArray: any = [];\n    // assign the form fields for Permissions\n    this.permissionArray.forEach((perm: any) => {\n      pArray.push(perm.Name);\n      formGroup[perm.Name] = new FormControl('');\n    });\n    this.perNameArray = pArray;\n    this.userForm = this.fb.group(formGroup);\n    // field to display the Permission in UI\n    this.perNameArray = pArray;\n  }\n\n\n  //function to modify boolean depending on whether the  password eye symbol is on or off\n  newshowpassword(event: any) {\n    this.newPasswordShown = event;\n  }\n  togglePasswordVisibility(): void {\n    this.showExternalPassword = !this.showExternalPassword;\n  }\n\n  onAutoPasswordChange(event: any) {\n    const controls = this.userForm.controls\n    this.IsAutoPassword = controls.IsAuto.value;\n    if (this.IsAutoPassword == false) {\n      this.userForm.get('password')?.setValidators(Validators.compose([Validators.required]));\n      this.userForm.get('password')?.updateValueAndValidity();\n      this.userForm.controls['password'].enable()\n    }\n    else {\n      this.userForm.controls['password'].disable()\n    }\n\n  }\n\n  patchForm(){\n    this.httpUtilService.loadingSubject.next(true);\n    console.log(\"Call patch form component line: 151\" , this.id)\n    this.userService.getUser({ userId: this.id,loggedInUserId :this.loginUser.userId  }).subscribe({\n      next: (user: any) => {\n        console.log(\"Line: 153\", user.responseData.data); // <-- This should now print\n\n        this.httpUtilService.loadingSubject.next(false);\n\n        if (!user.isFault) {\n          const userData = user.responseData;\n          let rPerms = JSON.parse(user.responseData.rolePermissions );\n          this.image = user.responseData.imageName\n          this.imageUrl = this.appService.ImageUrl(this.image)\n          this.userForm.patchValue({\n            firstname: userData.firstName,\n            lastname: userData.lastName,\n            phone: userData.phoneNo?.replace(/[()\\-\\sExt.]/g, '') || '',\n            status: userData.userStatus === 'Active' ? 'true' : 'false',\n            email: userData.email,\n            title: userData.title,\n            role: userData.roleId,\n            pharmacyId:userData.pharmacyId,\n            medicalCenterId:userData.medicalCenterId\n          });\n\n              // patch the permission values\n          let self: any = this;\n          each(rPerms, (r:any) => {\n            _.forEach(r, function (value, key) {\n              self.userForm.patchValue({\n                [key]: value,\n              });\n\n            });\n          });\n        } else {\n          console.warn(\"User response has isFault = true\", user.responseData);\n        }\n      },\n      error: (err) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error(\"API call failed\", err);\n      }\n    });\n\n  }\n\n\n  //function to browse imgae file\n  browseFile(event: any) {\n    const fSize = Math.round(event.target.files[0].size / 1024);\n    const file = event.target.files[0];\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    let selectedImage = file.name;\n    const fileData = new FormData();\n    fileData.append('files', file)\n    if (fSize > 500) {\n      this.layoutUtilService.showError(selectedImage + ' - File size upto 500KB.Couldnt upload the files', '');\n    } else {\n      this.userService.uploadImage(fileData).subscribe((res: any) => {\n        if (!res.isFault) {\n          this.image = res.responseData.fileName;\n          this.imageUrl = AppSettings.IMAGEPATH + this.image;\n          this.cdr.markForCheck();\n        }\n      });\n    }\n  }\n\n  controlHasError(validation: any, controlName: string | number): boolean {\n    const control = this.userForm.controls[controlName];\n    if (!control) {\n      return false;\n    }\n    let result = control.hasError(validation) && (control.dirty || control.touched);\n    return result;\n  }\n\n  save(){\n    let controls = this.userForm.controls\n    if (this.userForm.invalid) {\n      Object.keys(controls).forEach(controlName =>\n        controls[controlName].markAsTouched()\n      );\n      //this.layoutUtilService.showError('Please fill all required fields', '')\n      return;\n    }\n    let userData: any = this.prepareuser();\n\n\n    console.log(\"Line: 203\", userData)\n\n    if (this.id === 0) {\n      this.create(userData);\n\n    } else {\n      this.edit(userData);\n\n    }\n\n  }\n\n\n\n   // API to update the user details based on the userid\n   edit(userData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.userService.updateUser(userData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true)\n        this.modal.close()\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n        this.passEntry.emit(false)\n      }\n    });\n  }\n  // API to save new user details\n  create(userData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.userService.createUser(userData).subscribe((res: any) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true)\n        this.modal.close()\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n        this.passEntry.emit(false)\n      }\n    });\n  }\n\n\n\n  //function to map input form fields to API fields\n  prepareuser() {\n    const formData = this.userForm.value;\n    this.users.firstName = formData.firstname;\n    this.users.lastName = formData.lastname;\n    this.users.phoneNo = formData.phone !== '' ? this.appService.getPhoneFormat(formData.phone) : ''\n    this.users.userStatus = formData.status === 'true' ? 'Active' : 'Inactive';\n    this.users.roleId = formData.role;\n    this.users.title = formData.title;\n    this.users.password = formData.password;\n    this.users.isAuto = formData.IsAuto;\n    this.users.imageName = this.image;\n    this.users.loggedInUserId = this.loginUser.userId;\n    this.users.userId = this.id\n    this.users.email = formData.email;\n    this.users.isEmailNotificationEnabled = true;\n    this.users.pharmacyId = formData.pharmacyId;\n    this.users.medicalCenterId = formData.medicalCenterId;\n    // let controls = this.userForm.controls;\n    // let perArray: any = [];\n    // let self: any = this;\n    // _.forEach(controls, function (value, key) {\n    //   let rjson = _.find(self.permissionArray, function (o) {\n    //     return o.Name === key;\n    //   });\n    //   if (rjson !== undefined) {\n    //     let permissionJson = self.getPermissionJson(rjson, value);\n    //     perArray.push(perm+issionJson);\n    //   }\n    // });\n    // this.users.permissions = perArray;\n    return this.users;\n  }\n\n\n  //function to navigate tab\n  showTab(tab: any, $event: any) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n\n  //function to change permission based on role name\n  changeRoleAccess(event: any) {\n    console.log('event ', event)\n\n    this.httpUtilService.loadingSubject.next(true);\n    this.userService.getRole({ roleId: event.roleId }).subscribe((role: any) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!role.isFault) {\n        let rPerms = JSON.parse(role.responseData.rolePermissions);\n        // patch the permission values\n        let self: any = this;\n        each(rPerms, (r) => {\n          _.forEach(r, function (value, key) {\n            console.log('value ', value )\n                   console.log('key ', key )\n            self.userForm.patchValue({\n              [key]: value,\n            });\n          });\n        })\n        console.log('form data ', self.userForm)\n        this.cdr.markForCheck();\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  changePharmacy(event:any){\n\n  }\n  changeMedicalCenter(event:any){\n\n  }\n}\n", "<div class=\"modal-content h-auto\">\n    <div class=\"modal-header bg-light-primary\">\n        <div class=\"modal-title h5 fs-3\" id=\"example-modal-sizes-title-lg\">\n            <ng-container>\n                <div *ngIf=\"id ===0\">Add User</div>\n                <div *ngIf=\"id !==0\">Edit User</div>\n            </ng-container>\n        </div>\n        <div class=\"float-right\">\n            <!-- <a class=\"btn btn-icon  btn-sm pl-08 border-gray bg-danger\" > -->\n                <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"modal.dismiss()\"></i>\n            <!-- </a> -->\n        </div>\n    </div>\n    <div class=\"modal-body large-modal-body\" >\n        <!-- style=\"max-height: calc(100vh - 250px); overflow-y: auto;\" -->\n        <ng-container>\n\n             <div class=\"row\">\n                <div class=\"col-xl-12\">\n                    <div class=\"d-flex\">\n                        <ul\n                            class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\">\n                            <li class=\"nav-item\">\n                                <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                                    [ngClass]=\"{ 'active':selectedTab==='basic'}\" (click)=\"showTab('basic', $event)\">\n                                    Profile\n                                </a>\n                            </li>\n                            <li class=\"nav-item\">\n                                <a class=\"nav-link text-active-primary me-6  cursor-pointer \" data-toggle=\"tab\"\n                                    [ngClass]=\"{ 'active':selectedTab==='role'}\" (click)=\"showTab('role', $event)\">\n                                    Role & Access\n                                </a>\n                            </li>\n\n                        </ul>\n                    </div>\n                </div>\n\n            </div>\n\n            <form class=\"form form-label-right\" [formGroup]=\"userForm\">\n                <div class=\"card-body response-list\" *ngIf=\"selectedTab==='basic'\">\n\n                    <div class=\"row mt-4\">\n                        <div class=\"col-xl-6\">\n                            <div class=\"form-group\">\n                                <label class=\"fw-bold form-label mb-2\">First Name<sup\n                                        class=\"text-danger\">*</sup></label>\n                                <input type=\"text\" class=\"form-control form-control-sm\" name=\"firstname\"\n                                    formControlName=\"firstname\" placeholder=\"Type Here\">\n                                <span class=\"custom-error-css\"\n                                    *ngIf=\"controlHasError('required', 'firstname')\">Required Field</span>\n                            </div>\n                        </div>\n                        <div class=\"col-xl-6\">\n                            <div class=\"form-group\">\n                                <label class=\"fw-bold form-label mb-2\">Last Name<sup class=\"text-danger\">*</sup></label>\n                                <input type=\"text\" class=\"form-control form-control-sm\" name=\"lastname\"\n                                    formControlName=\"lastname\" placeholder=\"Type Here\">\n                                <span class=\"custom-error-css\" *ngIf=\"controlHasError('pattern', 'lastname')\">Required Field</span>\n                            </div>\n                        </div>\n                    </div>\n                    <div class=\"row mt-4\">\n                        <div class=\"col-xl-6\">\n                            <!-- <div class=\"col-xl-12\">\n                                <div class=\"form-group\">\n                                    <label class=\"fw-bold form-label mb-2\">Company Name<sup\n                                            class=\"text-danger\">*</sup></label>\n                                    <input type=\"text\" class=\"form-control form-control-sm\" name=\"companyname\"\n                                        formControlName=\"companyname\" placeholder=\"Type Here\">\n                                    <span class=\"custom-error-css\"\n                                        *ngIf=\"controlHasError('required', 'companyname')\">Required Field</span>\n                                </div>\n                            </div> -->\n\n                            <div class=\"col-xl-12 mt-4\">\n                                <div class=\"form-group\">\n                                    <label class=\"fw-bold form-label mb-2\">Email<sup class=\"text-danger\">*</sup></label>\n                                    <input type=\"text\" class=\"form-control form-control-sm\" name=\"email\"\n                                        formControlName=\"email\" placeholder=\"Type Here\">\n                                        <span class=\"custom-error-css\"\n                                        *ngIf=\"controlHasError('email', 'email')\">Enter a valid email</span>\n                                </div>\n                            </div>\n\n                            <div class=\"col-xl-12 mt-4\" *ngIf=\"id === 0\">\n                                <div class=\"row d-flex\">\n                                    <div class=\"col-xl-4\">\n                                    <div class=\"form-group\">\n                                        <label class=\"fw-bold form-label mb-2\">Auto Password?</label>\n                                        <div>\n                                        <label class=\"form-check form-check-custom form-switch form-switch-sm mb-3 wd-fix\">\n                                            <input class=\"form-check-input\" type=\"checkbox\" name=\"IsAuto\"\n                                            (change)=\"onAutoPasswordChange($event)\" formControlName=\"IsAuto\" value=true />&nbsp;\n                                        </label>\n                                        </div>\n                                    </div>\n                                    </div>\n                                    <div class=\"col-xl-8\">\n                                    <div class=\"form-group\">\n                                        <label class=\"fw-bold form-label mb-2\">Password<sup class=\"text-danger\">*</sup></label>\n                                        <div class=\"input-group mb-0\">\n                                        <input [type]=\"newPasswordShown === true ?'password':'text'\" class=\"form-control form-control-sm\"\n                                            name=\"password\" placeholder=\"Type Here\" autocomplete=\"off\" formControlName=\"password\" />\n                                        <div class=\"input-group-text\">\n                                            <span [ngClass]=\"newPasswordShown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'\"\n                                            (click)=\"newPasswordShown===true? newshowpassword(false):newshowpassword(true)\"></span>\n                                        </div>\n                                        </div>\n\n\n                                    </div>\n                                    <span class=\"custom-error-css\" *ngIf=\"controlHasError('required', 'password')\">Required\n                                        Field</span>\n                                    </div>\n                                </div>\n                            </div>\n\n\n                            <div class=\"col-xl-12 mt-4\">\n                                <div class=\"form-group\">\n                                    <label class=\"fw-bold form-label mb-2\">Phone</label>\n\n                                    <igx-input-group>\n                                        <input class=\"form-control form-control-sm\" name=\"phone\" id=\"tel\" igxInput\n                                            type=\"text\" autocomplete=\"off\" formControlName=\"phone\"\n                                            [igxMask]=\"'(************* Ext. 9999'\" />\n                                    </igx-input-group>\n                                </div>\n                            </div>\n\n                            <div class=\"col-xl-12 mt-4\">\n                                <div class=\"form-group\">\n                                    <label class=\"fw-bold form-label mb-2\">Title</label>\n\n                                    <input type=\"text\" class=\"form-control form-control-sm\" name=\"title\"\n                                    formControlName=\"title\" placeholder=\"Type Here\">\n                                </div>\n                            </div>\n\n                        </div>\n\n                        <div class=\"col-xl-6\">\n                            <div class=\"col-xl-12 flex-center\">\n                                <div class=\"form-group row\">\n                                    <div class=\"col-lg-12\">\n\n                                        <label class=\"fs-5 fw-bold form-label\">Profile Image</label>\n                                        <br>\n                                        <div class=\"d-flex justify-content-center w-100 mb-2\">\n                                            <div class=\"image-input image-input-outline\" data-kt-image-input=\"true\">\n                                                <!--begin::Preview existing avatar-->\n                                                <div class=\"image-input-wrapper w-150px h-150px\"\n                                                    style=\"background-image: url({{imageUrl}}); border-radius: 50%;\"\n                                                    *ngIf=\"image !==''\">\n                                                </div>\n                                                <div class=\"image-input-wrapper bg-light-primary w-150px h-150px text-center\"\n                                                    *ngIf=\"image ==='' || image ===null\" style=\"border-radius: 50%;\">\n\n                                                    <div class=\"image-input-wrapper w-150px h-150px\"\n                                                        style=\"background-image: url('./assets/media/svg/avatars/blank.svg');border-radius: 50%;\">\n                                                    </div>\n                                                </div>\n                                                <!--end::Preview existing avatar-->\n\n\n                                                <!--begin::Label-->\n                                                <label\n                                                    class=\"btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow\"\n                                                    data-kt-image-input-action=\"change\" data-bs-toggle=\"tooltip\" title=\"\"\n                                                    data-bs-original-title=\"Change avatar\">\n                                                    <i class=\"bi bi-pencil-fill fs-7\" (click)=\"imguploader.click();\"></i>\n                                                </label>\n                                                <!--begin::Inputs-->\n                                                <input class=\"visually-hidden\" type=\"file\" name=\"avatar\" #imguploader\n                                                    accept=\".png, .jpg, .jpeg\" >\n                                                <!-- <input class=\"visually-hidden\" type=\"file\" name=\"avatar\" #imguploader\n                                                    accept=\".png, .jpg, .jpeg\" (change)=\"browseFile($event);\"> -->\n                                                <input type=\"hidden\" name=\"avatar_remove\">\n                                                <!--end::Inputs-->\n                                                <!--end::Label-->\n                                                <!--begin::Cancel-->\n                                                <span\n                                                    class=\"btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow\"\n                                                    data-kt-image-input-action=\"cancel\" data-bs-toggle=\"tooltip\" title=\"\"\n                                                    data-bs-original-title=\"Cancel avatar\">\n                                                    <i class=\"bi bi-x fs-2\"></i>\n                                                </span>\n                                                <!--end::Cancel-->\n                                            </div>\n                                        </div>\n\n                                    </div>\n\n\n                                </div>\n\n                            </div>\n                        </div>\n                    </div>\n\n\n                    <div class=\"row mt-4\" *ngIf=\"id!==0\">\n                        <div class=\"col-xl-6\">\n                            <div class=\"form-group\">\n                                <label class=\"fw-bold form-label mb-2\">Status <sup class=\"text-danger\">*</sup></label>\n                                <div class=\"d-flex mt-2\">\n                                    <div class=\"form-check form-check-inline form-check-sm\">\n                                        <input class=\"form-check-input\" type=\"radio\" value='true'\n                                            name=\"status\" id=\"Active\" formControlName=\"status\">\n                                        <label class=\"form-check-label\" for=\"Active\">\n                                            Active\n                                        </label>\n                                    </div>\n                                    <div class=\"form-check form-check-inline form-check-sm \">\n                                        <input class=\"form-check-input\" type=\"radio\" value='false'\n                                            name=\"status\" id=\"Inactive\" formControlName=\"status\">\n                                        <label class=\"form-check-label\" for=\"Inactive\">\n                                            Inactive\n                                        </label>\n                                    </div>\n\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                </div>\n                <div class=\"card-body response-list\" *ngIf=\"selectedTab==='role'\">\n                    <div class=\"col-lg-6\">\n                                <label class=\"fw-semibold fs-6 mb-2\">Role <sup class=\"text-danger\">*</sup></label>\n                                <ng-select [items]=\"roleArray\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"roleName\"\n                                    name=\"role\" formControlName=\"role\" bindValue=\"roleId\"\n                                    (change)=\"changeRoleAccess($event)\" placeholder=\"Select an option\">\n                                </ng-select>\n                                <span class=\"custom-error-css\" *ngIf=\"appService.controlHasError('required', 'role',userForm)\">Required\n                                    Field</span>\n                            </div>\n                            <div class=\"form-group row mb-4 px-10 pt-2\">\n                                <div class=\"col-12 d-flex justify-content-start\">\n                                    <table class=\"w-100\">\n                                        <th class=\"p-1 fw-bold fs-6\" style=\"width:390px !important;\">Permissions</th>\n                                        <th class=\"p-1 fw-bold fs-6\" style=\"width:390px !important;\">\n                                            <div class=\"d-flex justify-content-between\">\n                                                <span>Read</span>\n                                                <span>Write</span>\n                                                <span>Delete</span>\n                                            </div>\n                                        </th>\n                                        <tr *ngFor=\"let p of perNameArray\">\n                                            <td class=\"p-1\">\n                                                <label class=\"fw-semibold fs-6\">{{p}}</label>\n                                            </td>\n                                            <td class=\"p-1\">\n                                                <checkbox-group ngDefaultControl formControlName=\"{{p}}\" >\n                                                    <checkbox value=\"Read\">Read</checkbox>\n                                                    <checkbox style=\"padding-left:145px\" value=\"Write\">Write</checkbox>\n                                                    <checkbox style=\"padding-left:145px\" value=\"Delete\">Delete\n                                                    </checkbox>\n                                                </checkbox-group>\n\n                                            </td>\n                                        </tr>\n                                    </table>\n                                </div>\n                            </div>\n                </div>\n\n\n            </form>\n        </ng-container>\n    </div>\n      <div class=\"modal-footer justify-content-between\">\n        <div class=\"float-left\">\n            <button type=\"button\" class=\"btn btn-secondary btn-elevate btn-sm\" *ngIf=\"selectedTab==='role'\"\n                (click)=\"showTab('basic',$event)\">Previous</button>\n        </div>\n        <div>\n            <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate mr-2\"\n                (click)=\"modal.dismiss()\">Cancel</button>\n            &nbsp;\n            <button type=\"button\" class=\"btn btn-primary btn-elevate btn-sm\" *ngIf=\"selectedTab==='basic'\"\n                (click)=\"showTab('role',$event)\">Next</button>\n            <button type=\"button\" class=\"btn btn-primary btn-elevate btn-sm\" *ngIf=\"selectedTab==='role'\" [disabled]=\"userForm.invalid\"\n                (click)=\"save()\">Save</button>\n        </div>\n    </div>\n</div>\n"], "mappings": "AACA,SAAuCA,YAAY,QAA+B,eAAe;AAGjG,SAAiCC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAGhF,SAASC,WAAW,QAAQ,sBAAsB;AAGlD,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAC3B,SAASC,GAAG,QAAQ,MAAM;AAE1B,SAASC,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;ICTbC,EAAA,CAAAC,cAAA,UAAqB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACnCH,EAAA,CAAAC,cAAA,UAAqB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA+CpBH,EAAA,CAAAC,cAAA,eACqD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAQ1EH,EAAA,CAAAC,cAAA,eAA8E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAsB3FH,EAAA,CAAAC,cAAA,eAC0C;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA+BxEH,EAAA,CAAAC,cAAA,eAA+E;IAAAD,EAAA,CAAAE,MAAA,qBACtE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAxBZH,EAJZ,CAAAC,cAAA,cAA6C,cACjB,cACE,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGzDH,EAFJ,CAAAC,cAAA,UAAK,gBAC8E,gBAED;IAA9ED,EAAA,CAAAI,UAAA,oBAAAC,gEAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAG,oBAAA,CAAAN,MAAA,CAA4B;IAAA,EAAC;IADvCN,EAAA,CAAAG,YAAA,EAC8E;IAAAH,EAAA,CAAAE,MAAA,cAClF;IAGJF,EAHI,CAAAG,YAAA,EAAQ,EACF,EACJ,EACA;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IACvFH,EAAA,CAAAC,cAAA,eAA8B;IAC9BD,EAAA,CAAAa,SAAA,iBAC4F;IAExFb,EADJ,CAAAC,cAAA,eAA8B,gBAEsD;IAAhFD,EAAA,CAAAI,UAAA,mBAAAU,+DAAA;MAAAd,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAM,gBAAA,KAA4B,IAAI,GAAEN,MAAA,CAAAO,eAAA,CAAgB,KAAK,CAAC,GAACP,MAAA,CAAAO,eAAA,CAAgB,IAAI,CAAC;IAAA,EAAC;IAKvFhB,EALwF,CAAAG,YAAA,EAAO,EACrF,EACA,EAGJ;IACNH,EAAA,CAAAiB,UAAA,KAAAC,+CAAA,mBAA+E;IAIvFlB,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;;IAdaH,EAAA,CAAAmB,SAAA,IAAqD;IAArDnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAM,gBAAA,gCAAqD;IAGlDf,EAAA,CAAAmB,SAAA,GAA4E;IAA5EnB,EAAA,CAAAoB,UAAA,YAAAX,MAAA,CAAAM,gBAAA,sDAA4E;IAO1Df,EAAA,CAAAmB,SAAA,EAA6C;IAA7CnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAY,eAAA,yBAA6C;;;;;IAwCjErB,EAAA,CAAAa,SAAA,cAGM;;;;IAFFb,EAAA,CAAAsB,sBAAA,2BAAAb,MAAA,CAAAc,QAAA,2BAAgE;;;;;IAGpEvB,EAAA,CAAAC,cAAA,cACqE;IAEjED,EAAA,CAAAa,SAAA,cAEM;IACVb,EAAA,CAAAG,YAAA,EAAM;;;;;IA2CtBH,EAHZ,CAAAC,cAAA,cAAqC,cACX,cACM,gBACmB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IAElFH,EADJ,CAAAC,cAAA,cAAyB,cACmC;IACpDD,EAAA,CAAAa,SAAA,gBACuD;IACvDb,EAAA,CAAAC,cAAA,iBAA6C;IACzCD,EAAA,CAAAE,MAAA,gBACJ;IACJF,EADI,CAAAG,YAAA,EAAQ,EACN;IACNH,EAAA,CAAAC,cAAA,eAAyD;IACrDD,EAAA,CAAAa,SAAA,iBACyD;IACzDb,EAAA,CAAAC,cAAA,iBAA+C;IAC3CD,EAAA,CAAAE,MAAA,kBACJ;IAMpBF,EANoB,CAAAG,YAAA,EAAQ,EACN,EAEJ,EACJ,EACJ,EACJ;;;;;;IApLMH,EALhB,CAAAC,cAAA,cAAmE,cAEzC,cACI,cACM,gBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAC,cAAA,cACrB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IAC3CH,EAAA,CAAAa,SAAA,gBACwD;IACxDb,EAAA,CAAAiB,UAAA,IAAAO,uCAAA,mBACqD;IAE7DxB,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAsB,eACM,iBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IACxFH,EAAA,CAAAa,SAAA,iBACuD;IACvDb,EAAA,CAAAiB,UAAA,KAAAQ,wCAAA,mBAA8E;IAG1FzB,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAgBUH,EAfhB,CAAAC,cAAA,eAAsB,eACI,eAYU,eACA,iBACmB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IACpFH,EAAA,CAAAa,SAAA,iBACoD;IAChDb,EAAA,CAAAiB,UAAA,KAAAS,wCAAA,mBAC0C;IAEtD1B,EADI,CAAAG,YAAA,EAAM,EACJ;IAENH,EAAA,CAAAiB,UAAA,KAAAU,uCAAA,mBAA6C;IAoCrC3B,EAFR,CAAAC,cAAA,eAA4B,eACA,iBACmB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEpDH,EAAA,CAAAC,cAAA,uBAAiB;IACbD,EAAA,CAAAa,SAAA,iBAE6C;IAGzDb,EAFQ,CAAAG,YAAA,EAAkB,EAChB,EACJ;IAIEH,EAFR,CAAAC,cAAA,eAA4B,eACA,iBACmB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEpDH,EAAA,CAAAa,SAAA,iBACgD;IAI5Db,EAHQ,CAAAG,YAAA,EAAM,EACJ,EAEJ;IAOUH,EALhB,CAAAC,cAAA,eAAsB,eACiB,eACH,eACD,iBAEoB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAa,SAAA,UAAI;IAEAb,EADJ,CAAAC,cAAA,eAAsD,eACsB;IAMpED,EAJA,CAAAiB,UAAA,KAAAW,uCAAA,kBAEwB,KAAAC,uCAAA,kBAG6C;IAcjE7B,EAJJ,CAAAC,cAAA,iBAG2C,aAC0B;IAA/BD,EAAA,CAAAI,UAAA,mBAAA0B,qDAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAC,cAAA,GAAAhC,EAAA,CAAAiC,WAAA;MAAA,OAAAjC,EAAA,CAAAW,WAAA,CAASqB,cAAA,CAAAE,KAAA,EAAmB;IAAA,EAAE;IACpElC,EADqE,CAAAG,YAAA,EAAI,EACjE;IAMRH,EAJA,CAAAa,SAAA,oBACgC,iBAGU;IAI1Cb,EAAA,CAAAC,cAAA,gBAG2C;IACvCD,EAAA,CAAAa,SAAA,aAA4B;IAa5Db,EAZ4B,CAAAG,YAAA,EAAO,EAEL,EACJ,EAEJ,EAGJ,EAEJ,EACJ,EACJ;IAGNH,EAAA,CAAAiB,UAAA,KAAAkB,uCAAA,mBAAqC;IAyBzCnC,EAAA,CAAAG,YAAA,EAAM;;;;IAjLeH,EAAA,CAAAmB,SAAA,GAA8C;IAA9CnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAY,eAAA,0BAA8C;IAQnBrB,EAAA,CAAAmB,SAAA,GAA4C;IAA5CnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAY,eAAA,wBAA4C;IAuBnErB,EAAA,CAAAmB,SAAA,IAAuC;IAAvCnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAY,eAAA,mBAAuC;IAIvBrB,EAAA,CAAAmB,SAAA,EAAc;IAAdnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAA2B,EAAA,OAAc;IAyC3BpC,EAAA,CAAAmB,SAAA,GAAsC;IAAtCnB,EAAA,CAAAoB,UAAA,uCAAsC;IA4B7BpB,EAAA,CAAAmB,SAAA,IAAiB;IAAjBnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAA4B,KAAA,QAAiB;IAGjBrC,EAAA,CAAAmB,SAAA,EAAkC;IAAlCnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAA4B,KAAA,WAAA5B,MAAA,CAAA4B,KAAA,UAAkC;IA6C5CrC,EAAA,CAAAmB,SAAA,GAAY;IAAZnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAA2B,EAAA,OAAY;;;;;IAiCvBpC,EAAA,CAAAC,cAAA,eAA+F;IAAAD,EAAA,CAAAE,MAAA,qBACtF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAeAH,EAFR,CAAAC,cAAA,SAAmC,aACf,gBACoB;IAAAD,EAAA,CAAAE,MAAA,GAAK;IACzCF,EADyC,CAAAG,YAAA,EAAQ,EAC5C;IAGGH,EAFR,CAAAC,cAAA,aAAgB,yBAC8C,mBAC/B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,mBAAmD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnEH,EAAA,CAAAC,cAAA,oBAAoD;IAAAD,EAAA,CAAAE,MAAA,eACpD;IAIZF,EAJY,CAAAG,YAAA,EAAW,EACE,EAEhB,EACJ;;;;IAXmCH,EAAA,CAAAmB,SAAA,GAAK;IAALnB,EAAA,CAAAsC,iBAAA,CAAAC,IAAA,CAAK;IAGJvC,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAwC,qBAAA,oBAAAD,IAAA,CAAuB;;;;;;IAxBxEvC,EAFhB,CAAAC,cAAA,cAAkE,cACxC,gBAC2B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IAClFH,EAAA,CAAAC,cAAA,oBAEuE;IAAnED,EAAA,CAAAI,UAAA,oBAAAqC,6DAAAnC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAmC,GAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAkC,gBAAA,CAAArC,MAAA,CAAwB;IAAA,EAAC;IACvCN,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAiB,UAAA,IAAA2B,uCAAA,mBAA+F;IAEnG5C,EAAA,CAAAG,YAAA,EAAM;IAIMH,EAHZ,CAAAC,cAAA,cAA4C,cACS,iBACxB,cAC4C;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGrEH,EAFR,CAAAC,cAAA,cAA6D,eACb,YAClC;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAEpBF,EAFoB,CAAAG,YAAA,EAAO,EACjB,EACL;IACLH,EAAA,CAAAiB,UAAA,KAAA4B,sCAAA,kBAAmC;IAiB3D7C,EAHoB,CAAAG,YAAA,EAAQ,EACN,EACJ,EACZ;;;;IAnCqBH,EAAA,CAAAmB,SAAA,GAAmB;IAAqBnB,EAAxC,CAAAoB,UAAA,UAAAX,MAAA,CAAAqC,SAAA,CAAmB,oBAAoB,mBAAmB;IAIrC9C,EAAA,CAAAmB,SAAA,EAA6D;IAA7DnB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAsC,UAAA,CAAA1B,eAAA,qBAAAZ,MAAA,CAAAuC,QAAA,EAA6D;IAcnEhD,EAAA,CAAAmB,SAAA,IAAe;IAAfnB,EAAA,CAAAoB,UAAA,YAAAX,MAAA,CAAAwC,YAAA,CAAe;;;;;;IAyB7DjD,EAAA,CAAAC,cAAA,iBACsC;IAAlCD,EAAA,CAAAI,UAAA,mBAAA8C,4DAAA5C,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,OAAA,CAAQ,OAAO,EAAA9C,MAAA,CAAQ;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMvDH,EAAA,CAAAC,cAAA,iBACqC;IAAjCD,EAAA,CAAAI,UAAA,mBAAAiD,4DAAA/C,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,OAAA,CAAQ,MAAM,EAAA9C,MAAA,CAAQ;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAClDH,EAAA,CAAAC,cAAA,iBACqB;IAAjBD,EAAA,CAAAI,UAAA,mBAAAmD,4DAAA;MAAAvD,EAAA,CAAAO,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgD,IAAA,EAAM;IAAA,EAAC;IAACzD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAD4DH,EAAA,CAAAoB,UAAA,aAAAX,MAAA,CAAAuC,QAAA,CAAAU,OAAA,CAA6B;;;AD9QvI;AASA,OAAM,MAAOC,gBAAgB;EAoBRC,KAAA;EACTC,GAAA;EACAC,eAAA;EACAC,iBAAA;EACAC,EAAA;EACDjB,UAAA;EACCkB,WAAA;EAxBC7B,EAAE;EACF8B,kBAAkB;EACjBC,SAAS,GAAsB,IAAI1E,YAAY,EAAE;EAC3DuD,QAAQ;EACRoB,KAAK,GAAK,EAAE;EACZC,SAAS,GAAK,EAAE;EAChBvB,SAAS,GAAQ,EAAE;EACnBwB,mBAAmB,GAAK,EAAE;EAC1BC,aAAa,GAAM,EAAE;EACrBlC,KAAK,GAAG,EAAE;EACVd,QAAQ,GAAG,EAAE;EACb0B,YAAY,GAAQ,EAAE,CAAC;EACvBuB,eAAe,GAAQ,EAAE,CAAC;EAC1BC,WAAW,GAAW,OAAO,CAAC,CAAC;EAC/BC,cAAc,GAAY,IAAI;EAC9B3D,gBAAgB,GAAG,IAAI,CAAC,CAAC;EACzB4D,oBAAoB,GAAG,KAAK;EAE9BC,YAAmBhB,KAAqB,EAC9BC,GAAsB,EACtBC,eAAiC,EACjCC,iBAA2C,EAC3CC,EAAe,EAChBjB,UAAsB,EACrBkB,WAAwB;IANf,KAAAL,KAAK,GAALA,KAAK;IACd,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAjB,UAAU,GAAVA,UAAU;IACT,KAAAkB,WAAW,GAAXA,WAAW;IAEnB;EACC;EACD;EAEFY,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC3C,EAAE,CAAC;IACjD,IAAI,CAACiC,SAAS,GAAG,IAAI,CAACtB,UAAU,CAACiC,eAAe,EAAE;IAClD,IAAI,CAACR,eAAe,GAAG,IAAI,CAACN,kBAAkB;IAC9C,IAAI,CAACe,QAAQ,EAAE;IACf,IAAI,IAAI,CAAC7C,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC8C,SAAS,EAAE;IAClB;IACA,IAAI,CAACjB,WAAW,CAACkB,WAAW,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAE,CAAC,CAACC,IAAI,CACpDvF,GAAG,CAAEwF,IAAS,IAAKA,IAAW,CAAC,CAAC,CAACC,SAAS,CAAED,IAAQ,IAAI;MACtD,IAAI,CAACA,IAAI,CAACE,OAAO,EAAE;QACjBV,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEO,IAAI,CAAC;QAC7B;QACA,IAAI,CAACxC,SAAS,GAAGwC,IAAI,CAACG,YAAY,CAACC,KAAK;QACxCZ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACjC,SAAS,CAAC;QAC7C,IAAI,CAACe,GAAG,CAAC8B,YAAY,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAAC7C,SAAS,GAAG,EAAE;QACnB,IAAI,CAACe,GAAG,CAAC8B,YAAY,EAAE;MACzB;IACF,CAAC,CAAC;IAEJ,IAAI,CAAC9B,GAAG,CAAC8B,YAAY,EAAE;EACzB;EAGAV,QAAQA,CAAA;IACN,MAAMW,SAAS,GAAQ,EAAE;IACzBA,SAAS,CAAC,WAAW,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACkG,OAAO,CAAC,CAAClG,UAAU,CAACmG,QAAQ,CAAC,CAAC,CAAC;IACvFF,SAAS,CAAC,UAAU,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACkG,OAAO,CAAC,CAAClG,UAAU,CAACmG,QAAQ,CAAC,CAAC,CAAC;IACtFF,SAAS,CAAC,OAAO,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,EAACC,UAAU,CAACkG,OAAO,CAAC,CAAClG,UAAU,CAACoG,KAAK,CAAC,CAAC,CAAC;IAC/EH,SAAS,CAAC,OAAO,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,CAAC;IACxCkG,SAAS,CAAC,UAAU,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,CAAC;IAC3CkG,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,CAAC;IACzCkG,SAAS,CAAC,MAAM,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,CAAC;IACvCkG,SAAS,CAAC,OAAO,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,CAAC;IACvCkG,SAAS,CAAC,MAAM,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,CAAC;IACxCkG,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAIlG,WAAW,CAAC,EAAE,CAAC;IACzC,IAAIsG,MAAM,GAAQ,EAAE;IACpB;IACA,IAAI,CAACxB,eAAe,CAACyB,OAAO,CAAEC,IAAS,IAAI;MACzCF,MAAM,CAACG,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC;MACtBR,SAAS,CAACM,IAAI,CAACE,IAAI,CAAC,GAAG,IAAI1G,WAAW,CAAC,EAAE,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACuD,YAAY,GAAG+C,MAAM;IAC1B,IAAI,CAAChD,QAAQ,GAAG,IAAI,CAACgB,EAAE,CAACqC,KAAK,CAACT,SAAS,CAAC;IACxC;IACA,IAAI,CAAC3C,YAAY,GAAG+C,MAAM;EAC5B;EAGA;EACAhF,eAAeA,CAACsF,KAAU;IACxB,IAAI,CAACvF,gBAAgB,GAAGuF,KAAK;EAC/B;EACAC,wBAAwBA,CAAA;IACtB,IAAI,CAAC5B,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEA/D,oBAAoBA,CAAC0F,KAAU;IAC7B,MAAME,QAAQ,GAAG,IAAI,CAACxD,QAAQ,CAACwD,QAAQ;IACvC,IAAI,CAAC9B,cAAc,GAAG8B,QAAQ,CAACC,MAAM,CAACC,KAAK;IAC3C,IAAI,IAAI,CAAChC,cAAc,IAAI,KAAK,EAAE;MAChC,IAAI,CAAC1B,QAAQ,CAAC2D,GAAG,CAAC,UAAU,CAAC,EAAEC,aAAa,CAACjH,UAAU,CAACkG,OAAO,CAAC,CAAClG,UAAU,CAACmG,QAAQ,CAAC,CAAC,CAAC;MACvF,IAAI,CAAC9C,QAAQ,CAAC2D,GAAG,CAAC,UAAU,CAAC,EAAEE,sBAAsB,EAAE;MACvD,IAAI,CAAC7D,QAAQ,CAACwD,QAAQ,CAAC,UAAU,CAAC,CAACM,MAAM,EAAE;IAC7C,CAAC,MACI;MACH,IAAI,CAAC9D,QAAQ,CAACwD,QAAQ,CAAC,UAAU,CAAC,CAACO,OAAO,EAAE;IAC9C;EAEF;EAEA7B,SAASA,CAAA;IACP,IAAI,CAACpB,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9CnC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAG,IAAI,CAAC3C,EAAE,CAAC;IAC5D,IAAI,CAAC6B,WAAW,CAACiD,OAAO,CAAC;MAAEC,MAAM,EAAE,IAAI,CAAC/E,EAAE;MAACgF,cAAc,EAAE,IAAI,CAAC/C,SAAS,CAAC8C;IAAM,CAAG,CAAC,CAAC5B,SAAS,CAAC;MAC7F0B,IAAI,EAAGI,IAAS,IAAI;QAClBvC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsC,IAAI,CAAC5B,YAAY,CAACH,IAAI,CAAC,CAAC,CAAC;QAElD,IAAI,CAACxB,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAE/C,IAAI,CAACI,IAAI,CAAC7B,OAAO,EAAE;UACjB,MAAM8B,QAAQ,GAAGD,IAAI,CAAC5B,YAAY;UAClC,IAAI8B,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC5B,YAAY,CAACiC,eAAe,CAAE;UAC3D,IAAI,CAACrF,KAAK,GAAGgF,IAAI,CAAC5B,YAAY,CAACkC,SAAS;UACxC,IAAI,CAACpG,QAAQ,GAAG,IAAI,CAACwB,UAAU,CAAC6E,QAAQ,CAAC,IAAI,CAACvF,KAAK,CAAC;UACpD,IAAI,CAACW,QAAQ,CAAC6E,UAAU,CAAC;YACvBC,SAAS,EAAER,QAAQ,CAACS,SAAS;YAC7BC,QAAQ,EAAEV,QAAQ,CAACW,QAAQ;YAC3BC,KAAK,EAAEZ,QAAQ,CAACa,OAAO,EAAEC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,IAAI,EAAE;YAC3DC,MAAM,EAAEf,QAAQ,CAACgB,UAAU,KAAK,QAAQ,GAAG,MAAM,GAAG,OAAO;YAC3DvC,KAAK,EAAEuB,QAAQ,CAACvB,KAAK;YACrBwC,KAAK,EAAEjB,QAAQ,CAACiB,KAAK;YACrBC,IAAI,EAAElB,QAAQ,CAACmB,MAAM;YACrBC,UAAU,EAACpB,QAAQ,CAACoB,UAAU;YAC9BC,eAAe,EAACrB,QAAQ,CAACqB;WAC1B,CAAC;UAEE;UACJ,IAAIC,IAAI,GAAQ,IAAI;UACpB7I,IAAI,CAACwH,MAAM,EAAGsB,CAAK,IAAI;YACrBhJ,CAAC,CAACoG,OAAO,CAAC4C,CAAC,EAAE,UAAUnC,KAAK,EAAEoC,GAAG;cAC/BF,IAAI,CAAC5F,QAAQ,CAAC6E,UAAU,CAAC;gBACvB,CAACiB,GAAG,GAAGpC;eACR,CAAC;YAEJ,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACL5B,OAAO,CAACiE,IAAI,CAAC,kCAAkC,EAAE1B,IAAI,CAAC5B,YAAY,CAAC;QACrE;MACF,CAAC;MACDuD,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACnF,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CnC,OAAO,CAACkE,KAAK,CAAC,iBAAiB,EAAEC,GAAG,CAAC;MACvC;KACD,CAAC;EAEJ;EAGA;EACAC,UAAUA,CAAC5C,KAAU;IACnB,MAAM6C,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC/C,KAAK,CAACgD,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,GAAG,IAAI,CAAC;IAC3D,MAAMC,IAAI,GAAGnD,KAAK,CAACgD,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,aAAa,CAACH,IAAI,CAAC;IAC1B,IAAII,aAAa,GAAGJ,IAAI,CAACK,IAAI;IAC7B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAER,IAAI,CAAC;IAC9B,IAAIN,KAAK,GAAG,GAAG,EAAE;MACf,IAAI,CAACpF,iBAAiB,CAACmG,SAAS,CAACL,aAAa,GAAG,kDAAkD,EAAE,EAAE,CAAC;IAC1G,CAAC,MAAM;MACL,IAAI,CAAC5F,WAAW,CAACkG,WAAW,CAACJ,QAAQ,CAAC,CAACxE,SAAS,CAAE6E,GAAQ,IAAI;QAC5D,IAAI,CAACA,GAAG,CAAC5E,OAAO,EAAE;UAChB,IAAI,CAACnD,KAAK,GAAG+H,GAAG,CAAC3E,YAAY,CAAC4E,QAAQ;UACtC,IAAI,CAAC9I,QAAQ,GAAG3B,WAAW,CAAC0K,SAAS,GAAG,IAAI,CAACjI,KAAK;UAClD,IAAI,CAACwB,GAAG,CAAC8B,YAAY,EAAE;QACzB;MACF,CAAC,CAAC;IACJ;EACF;EAEAtE,eAAeA,CAACkJ,UAAe,EAAEC,WAA4B;IAC3D,MAAMC,OAAO,GAAG,IAAI,CAACzH,QAAQ,CAACwD,QAAQ,CAACgE,WAAW,CAAC;IACnD,IAAI,CAACC,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;IACA,IAAIC,MAAM,GAAGD,OAAO,CAACE,QAAQ,CAACJ,UAAU,CAAC,KAAKE,OAAO,CAACG,KAAK,IAAIH,OAAO,CAACI,OAAO,CAAC;IAC/E,OAAOH,MAAM;EACf;EAEAjH,IAAIA,CAAA;IACF,IAAI+C,QAAQ,GAAG,IAAI,CAACxD,QAAQ,CAACwD,QAAQ;IACrC,IAAI,IAAI,CAACxD,QAAQ,CAACU,OAAO,EAAE;MACzBoH,MAAM,CAACC,IAAI,CAACvE,QAAQ,CAAC,CAACP,OAAO,CAACuE,WAAW,IACvChE,QAAQ,CAACgE,WAAW,CAAC,CAACQ,aAAa,EAAE,CACtC;MACD;MACA;IACF;IACA,IAAI1D,QAAQ,GAAQ,IAAI,CAAC2D,WAAW,EAAE;IAGtCnG,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuC,QAAQ,CAAC;IAElC,IAAI,IAAI,CAAClF,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC8I,MAAM,CAAC5D,QAAQ,CAAC;IAEvB,CAAC,MAAM;MACL,IAAI,CAAC6D,IAAI,CAAC7D,QAAQ,CAAC;IAErB;EAEF;EAIC;EACA6D,IAAIA,CAAC7D,QAAa;IACjB,IAAI,CAACxD,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAChD,WAAW,CAACmH,UAAU,CAAC9D,QAAQ,CAAC,CAAC/B,SAAS,CAAC6E,GAAG,IAAG;MACpD,IAAI,CAACtG,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACmD,GAAG,CAAC5E,OAAO,EAAE;QAChB,IAAI,CAACzB,iBAAiB,CAACsH,WAAW,CAACjB,GAAG,CAAC3E,YAAY,CAAC6F,OAAO,EAAE,EAAE,CAAC;QAChE,IAAI,CAACnH,SAAS,CAACoH,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC3H,KAAK,CAAC4H,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACzH,iBAAiB,CAACmG,SAAS,CAACE,GAAG,CAAC3E,YAAY,CAAC6F,OAAO,EAAE,EAAE,CAAC;QAC9D,IAAI,CAACnH,SAAS,CAACoH,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EACA;EACAL,MAAMA,CAAC5D,QAAa;IAClB,IAAI,CAACxD,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAChD,WAAW,CAACwH,UAAU,CAACnE,QAAQ,CAAC,CAAC/B,SAAS,CAAE6E,GAAQ,IAAI;MAC3D,IAAI,CAACtG,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACmD,GAAG,CAAC5E,OAAO,EAAE;QAChB,IAAI,CAACzB,iBAAiB,CAACsH,WAAW,CAACjB,GAAG,CAAC3E,YAAY,CAAC6F,OAAO,EAAE,EAAE,CAAC;QAChE,IAAI,CAACnH,SAAS,CAACoH,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC3H,KAAK,CAAC4H,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACzH,iBAAiB,CAACmG,SAAS,CAACE,GAAG,CAAC3E,YAAY,CAAC6F,OAAO,EAAE,EAAE,CAAC;QAC9D,IAAI,CAACnH,SAAS,CAACoH,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EAIA;EACAN,WAAWA,CAAA;IACT,MAAMS,QAAQ,GAAG,IAAI,CAAC1I,QAAQ,CAAC0D,KAAK;IACpC,IAAI,CAACtC,KAAK,CAAC2D,SAAS,GAAG2D,QAAQ,CAAC5D,SAAS;IACzC,IAAI,CAAC1D,KAAK,CAAC6D,QAAQ,GAAGyD,QAAQ,CAAC1D,QAAQ;IACvC,IAAI,CAAC5D,KAAK,CAAC+D,OAAO,GAAGuD,QAAQ,CAACxD,KAAK,KAAK,EAAE,GAAG,IAAI,CAACnF,UAAU,CAAC4I,cAAc,CAACD,QAAQ,CAACxD,KAAK,CAAC,GAAG,EAAE;IAChG,IAAI,CAAC9D,KAAK,CAACkE,UAAU,GAAGoD,QAAQ,CAACrD,MAAM,KAAK,MAAM,GAAG,QAAQ,GAAG,UAAU;IAC1E,IAAI,CAACjE,KAAK,CAACqE,MAAM,GAAGiD,QAAQ,CAAClD,IAAI;IACjC,IAAI,CAACpE,KAAK,CAACmE,KAAK,GAAGmD,QAAQ,CAACnD,KAAK;IACjC,IAAI,CAACnE,KAAK,CAACwH,QAAQ,GAAGF,QAAQ,CAACE,QAAQ;IACvC,IAAI,CAACxH,KAAK,CAACyH,MAAM,GAAGH,QAAQ,CAACjF,MAAM;IACnC,IAAI,CAACrC,KAAK,CAACuD,SAAS,GAAG,IAAI,CAACtF,KAAK;IACjC,IAAI,CAAC+B,KAAK,CAACgD,cAAc,GAAG,IAAI,CAAC/C,SAAS,CAAC8C,MAAM;IACjD,IAAI,CAAC/C,KAAK,CAAC+C,MAAM,GAAG,IAAI,CAAC/E,EAAE;IAC3B,IAAI,CAACgC,KAAK,CAAC2B,KAAK,GAAG2F,QAAQ,CAAC3F,KAAK;IACjC,IAAI,CAAC3B,KAAK,CAAC0H,0BAA0B,GAAG,IAAI;IAC5C,IAAI,CAAC1H,KAAK,CAACsE,UAAU,GAAGgD,QAAQ,CAAChD,UAAU;IAC3C,IAAI,CAACtE,KAAK,CAACuE,eAAe,GAAG+C,QAAQ,CAAC/C,eAAe;IACrD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAI,CAACvE,KAAK;EACnB;EAGA;EACAhB,OAAOA,CAAC2I,GAAQ,EAAEzL,MAAW;IAC3B,IAAI,CAACmE,WAAW,GAAGsH,GAAG;IACtB,IAAI,CAAClI,GAAG,CAAC8B,YAAY,EAAE;EACzB;EAEA;EACAhD,gBAAgBA,CAAC2D,KAAU;IACzBxB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuB,KAAK,CAAC;IAE5B,IAAI,CAACxC,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAChD,WAAW,CAAC+H,OAAO,CAAC;MAAEvD,MAAM,EAAEnC,KAAK,CAACmC;IAAM,CAAE,CAAC,CAAClD,SAAS,CAAEiD,IAAS,IAAI;MACzE,IAAI,CAAC1E,eAAe,CAACkD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACuB,IAAI,CAAChD,OAAO,EAAE;QACjB,IAAI+B,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACe,IAAI,CAAC/C,YAAY,CAACiC,eAAe,CAAC;QAC1D;QACA,IAAIkB,IAAI,GAAQ,IAAI;QACpB7I,IAAI,CAACwH,MAAM,EAAGsB,CAAC,IAAI;UACjBhJ,CAAC,CAACoG,OAAO,CAAC4C,CAAC,EAAE,UAAUnC,KAAK,EAAEoC,GAAG;YAC/BhE,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE2B,KAAK,CAAE;YACtB5B,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE+D,GAAG,CAAE;YAChCF,IAAI,CAAC5F,QAAQ,CAAC6E,UAAU,CAAC;cACvB,CAACiB,GAAG,GAAGpC;aACR,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;QACF5B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6D,IAAI,CAAC5F,QAAQ,CAAC;QACxC,IAAI,CAACa,GAAG,CAAC8B,YAAY,EAAE;QACvB,IAAI,CAAC9B,GAAG,CAACoI,aAAa,EAAE;MAC1B;IACF,CAAC,CAAC;EACJ;EACAC,cAAcA,CAAC5F,KAAS,GAExB;EACA6F,mBAAmBA,CAAC7F,KAAS,GAE7B;;qCA5TW3C,gBAAgB,EAAA3D,EAAA,CAAAoM,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtM,EAAA,CAAAoM,iBAAA,CAAApM,EAAA,CAAAuM,iBAAA,GAAAvM,EAAA,CAAAoM,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAAzM,EAAA,CAAAoM,iBAAA,CAAAM,EAAA,CAAAC,wBAAA,GAAA3M,EAAA,CAAAoM,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA7M,EAAA,CAAAoM,iBAAA,CAAAU,EAAA,CAAAC,UAAA,GAAA/M,EAAA,CAAAoM,iBAAA,CAAAY,EAAA,CAAAC,WAAA;EAAA;;UAAhBtJ,gBAAgB;IAAAuJ,SAAA;IAAAC,MAAA;MAAA/K,EAAA;MAAA8B,kBAAA;IAAA;IAAAkJ,OAAA;MAAAjJ,SAAA;IAAA;IAAAkJ,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvBrB1N,EAFR,CAAAC,cAAA,aAAkC,aACa,aAC4B;QAC/DD,EAAA,CAAA4N,uBAAA,GAAc;QAEV5N,EADA,CAAAiB,UAAA,IAAA4M,+BAAA,iBAAqB,IAAAC,+BAAA,iBACA;;QAE7B9N,EAAA,CAAAG,YAAA,EAAM;QAGEH,EAFR,CAAAC,cAAA,aAAyB,WAEsD;QAA1BD,EAAA,CAAAI,UAAA,mBAAA2N,6CAAA;UAAA,OAASJ,GAAA,CAAA/J,KAAA,CAAAoK,OAAA,EAAe;QAAA,EAAC;QAGlFhO,EAHmF,CAAAG,YAAA,EAAI,EAE7E,EACJ;QACNH,EAAA,CAAAC,cAAA,aAA0C;QAEtCD,EAAA,CAAA4N,uBAAA,GAAc;QAQU5N,EANnB,CAAAC,cAAA,cAAiB,cACS,eACC,cAEuF,cAC9E,aAEoE;QAAnCD,EAAA,CAAAI,UAAA,mBAAA6N,8CAAA3N,MAAA;UAAA,OAASqN,GAAA,CAAAvK,OAAA,CAAQ,OAAO,EAAA9C,MAAA,CAAS;QAAA,EAAC;QAChFN,EAAA,CAAAE,MAAA,iBACJ;QACJF,EADI,CAAAG,YAAA,EAAI,EACH;QAEDH,EADJ,CAAAC,cAAA,cAAqB,aAEkE;QAAlCD,EAAA,CAAAI,UAAA,mBAAA8N,8CAAA5N,MAAA;UAAA,OAASqN,GAAA,CAAAvK,OAAA,CAAQ,MAAM,EAAA9C,MAAA,CAAS;QAAA,EAAC;QAC9EN,EAAA,CAAAE,MAAA,uBACJ;QAOpBF,EAPoB,CAAAG,YAAA,EAAI,EACH,EAEJ,EACH,EACJ,EAEJ;QAENH,EAAA,CAAAC,cAAA,gBAA2D;QA6LvDD,EA5LA,CAAAiB,UAAA,KAAAkN,gCAAA,mBAAmE,KAAAC,gCAAA,mBA4LD;QAyCtEpO,EAAA,CAAAG,YAAA,EAAO;;QAEfH,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADF,CAAAC,cAAA,eAAkD,eACxB;QACpBD,EAAA,CAAAiB,UAAA,KAAAoN,mCAAA,qBACsC;QAC1CrO,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,WAAK,kBAE6B;QAA1BD,EAAA,CAAAI,UAAA,mBAAAkO,mDAAA;UAAA,OAASX,GAAA,CAAA/J,KAAA,CAAAoK,OAAA,EAAe;QAAA,EAAC;QAAChO,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC7CH,EAAA,CAAAE,MAAA,gBACA;QAEAF,EAFA,CAAAiB,UAAA,KAAAsN,mCAAA,qBACqC,KAAAC,mCAAA,qBAEhB;QAGjCxO,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;QA9RgBH,EAAA,CAAAmB,SAAA,GAAa;QAAbnB,EAAA,CAAAoB,UAAA,SAAAuM,GAAA,CAAAvL,EAAA,OAAa;QACbpC,EAAA,CAAAmB,SAAA,EAAa;QAAbnB,EAAA,CAAAoB,UAAA,SAAAuM,GAAA,CAAAvL,EAAA,OAAa;QAoBCpC,EAAA,CAAAmB,SAAA,IAA6C;QAA7CnB,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAAyO,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAlJ,WAAA,cAA6C;QAM7CzE,EAAA,CAAAmB,SAAA,GAA4C;QAA5CnB,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAAyO,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAlJ,WAAA,aAA4C;QAWhCzE,EAAA,CAAAmB,SAAA,GAAsB;QAAtBnB,EAAA,CAAAoB,UAAA,cAAAuM,GAAA,CAAA3K,QAAA,CAAsB;QAChBhD,EAAA,CAAAmB,SAAA,EAA2B;QAA3BnB,EAAA,CAAAoB,UAAA,SAAAuM,GAAA,CAAAlJ,WAAA,aAA2B;QA4L3BzE,EAAA,CAAAmB,SAAA,EAA0B;QAA1BnB,EAAA,CAAAoB,UAAA,SAAAuM,GAAA,CAAAlJ,WAAA,YAA0B;QA8CAzE,EAAA,CAAAmB,SAAA,GAA0B;QAA1BnB,EAAA,CAAAoB,UAAA,SAAAuM,GAAA,CAAAlJ,WAAA,YAA0B;QAO5BzE,EAAA,CAAAmB,SAAA,GAA2B;QAA3BnB,EAAA,CAAAoB,UAAA,SAAAuM,GAAA,CAAAlJ,WAAA,aAA2B;QAE3BzE,EAAA,CAAAmB,SAAA,EAA0B;QAA1BnB,EAAA,CAAAoB,UAAA,SAAAuM,GAAA,CAAAlJ,WAAA,YAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}