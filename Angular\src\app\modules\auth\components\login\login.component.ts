import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Subscription, Observable } from 'rxjs';
import { first } from 'rxjs/operators';
import { UserModel } from '../../models/user.model';
import { AuthService } from '../../services/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AppService } from 'src/app/modules/services/app.service';
import { jwtDecode } from 'jwt-decode';
import { HttpUtilsService } from 'src/app/modules/services/http-utils.service';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { PageInfoService } from 'src/app/_metronic/layout/core/page-info.service';
import { Title } from '@angular/platform-browser';
import { CustomLayoutUtilsService } from 'src/app/modules/services/custom-layout.utils.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit, OnDestroy {
  // KeenThemes mock, change it to:
  defaultAuth: any = {
    email: '<EMAIL>',
    password: 'SwiJZf',
  };
  loginForm: FormGroup;
  hasError: boolean;
  returnUrl: string;
  isLoading$: Observable<boolean>;
  passwordshown = true;// password show/hide
  userData:any ={};
  // private fields
  private unsubscribe: Subscription[] = []; // Read more: => https://brianflove.com/2016/12/11/anguar-2-unsubscribe-observables/

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private route: ActivatedRoute,
    private appService:AppService,
    private httpUtilService:HttpUtilsService,
        private customLayoutUtilsService: CustomLayoutUtilsService,

    private router: Router,
    private modalService: NgbModal,
    private pageInfo: PageInfoService,
    private titleService: Title,
  ) {
    this.isLoading$ = this.authService.isLoading$;
    // redirect to home if already logged in
    if (this.authService.currentUserValue) {
      this.router.navigate(['/']);
    }
  }

  ngOnInit(): void {
    // Ensure title is set in auth layout where global scripts may be inactive
    this.pageInfo.updateTitle('Login');
    this.titleService.setTitle('Login - Permit Tracker');
    this.initForm();
    // get return url from route parameters or default to '/'
    this.returnUrl =
      this.route.snapshot.queryParams['returnUrl'.toString()] || '/';
  }

  // convenience getter for easy access to form fields
  get f() {
    return this.loginForm.controls;
  }

  initForm() {
    this.loginForm = this.fb.group({
      email: [
        '',
        Validators.compose([
          Validators.required,
          Validators.email,
          Validators.minLength(3),
          Validators.maxLength(320), // https://stackoverflow.com/questions/386294/what-is-the-maximum-length-of-a-valid-email-address
        ]),
      ],
      password: [
        '',
        Validators.compose([
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(100),
        ]),
      ],
      remember: [false]
    });
    let rememberMeLocalStorage = this.appService.getLocalStorageItem('permitRemember', false);
    let userLocallyStoredData = this.appService.getLocalStorageItem('permitUserAuth', true);
    //condition for patching  remember me based on boolean
    if (rememberMeLocalStorage === 'true' || rememberMeLocalStorage === true) {
      this.loginForm.patchValue({
        email: userLocallyStoredData.userName,
        password: userLocallyStoredData.password,
        remember: true
      })
    }
  }

  submit() {
    this.hasError = false;
    const controlsOfForm = this.loginForm.controls;
    if (this.loginForm.invalid) {
      return;
    }
     const authData = {
      userName: controlsOfForm.email.value,
      password: controlsOfForm.password.value,
    };
    
    // Show global loader
    this.httpUtilService.loadingSubject.next(true);
    
    // //API call for login
    this.authService.login(authData)
      .subscribe((data:any) => {
        if (data.isFault === false) {
          this.userData = data.responseData;
          console.log("data.responseData",data.responseData)
          if (this.loginForm.value.remember === true) {
            this.appService.setLocalStorageItem('permitRemember', 'true', false);
          } else {
            this.appService.setLocalStorageItem('permitRemember', 'false', false);
          }
          this.appService.setLocalStorageItem('permitUserAuth', authData, true);
          // set the local storage for the user details
          this.appService.setLocalStorageItem("permitUser", data.responseData, true);
          this.appService.setLocalStorageItem("permitToken", data.responseData.token, true);
          // this.layoutUtilService.showSuccess(data.responseData.message, '');
          const token = data.responseData.token;
          const decodedToken:any = jwtDecode(token);
          const expirationTime = decodedToken.exp;
          console.log('decodedToken ',decodedToken)
              console.log('expirationTime ',expirationTime)
          this.authService.setToken(data.responseData.token,expirationTime);
          if (data.responseData.isPasswordChanged === false) {
            this.changePassword();
          }else{
            // Keep loader active during navigation and page reload
            this.router.navigate(['/dashboard'], { replaceUrl: true }).then(() => {
              location.reload();
            });
          }
        } else {
          // Hide loader on error
          this.httpUtilService.loadingSubject.next(false);
          this.customLayoutUtilsService.showSuccess(data.responseData.message, '');
          // //alert(data.responseData.message);
        }
      },
      (error) => {
        // Hide loader on error
        this.httpUtilService.loadingSubject.next(false);
        console.error('Login error:', error);
      });
  }

   //function for opening pop up for change password
  changePassword() {
    // define the NgbModels options.
    const modalOption: NgbModalOptions = {};
    modalOption.backdrop = 'static';
    modalOption.keyboard = false;
    modalOption.size = 'md';
    // open the NgbModel for Confirmation.
    const modalRef = this.modalService.open(ChangePasswordComponent, modalOption);
    modalRef.componentInstance.showClose = false;
    //get response from edit user modal
    modalRef.componentInstance.passEntry.subscribe((receivedEntry: any) => {
      if (receivedEntry == true) {
        // this.router.navigate(['/dashboard'], {relativeTo: this.route});

        this.router.navigate(['/dashboard'], { replaceUrl: true }).then(() => {
          location.reload();
        });
      }
    })

  }
  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  //function for show hide password
   showpassword(event: any) {
    this.passwordshown = event;
  }
}
