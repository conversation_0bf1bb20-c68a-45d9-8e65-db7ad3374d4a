{"ast": null, "code": "import { Subscription, combineLatest } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"label\");\n    i0.ɵɵtext(5, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 30)(9, \"label\");\n    i0.ɵɵtext(10, \"Project Manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 29);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 30)(14, \"label\");\n    i0.ɵɵtext(15, \"External Project Manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 29);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 28)(19, \"label\");\n    i0.ɵɵtext(20, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 29);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 30)(24, \"label\");\n    i0.ɵɵtext(25, \"Start Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 29);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 30)(30, \"label\");\n    i0.ɵɵtext(31, \"End Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 29);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectDescription || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.internalProjectManagerName || ctx_r1.project.internalProjectManager || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.externalPMNames || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectLocation || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStartDate ? i0.ɵɵpipeBind2(28, 6, ctx_r1.project.projectStartDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectEndDate ? i0.ɵɵpipeBind2(34, 9, ctx_r1.project.projectEndDate, \"MM/dd/yyyy\") : \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_26_div_2_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 39);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_ng_container_26_div_2_tr_15_Template_a_click_2_listener() {\n      const permit_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r4.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"select\", 40);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_2_ng_container_26_div_2_tr_15_Template_select_change_8_listener($event) {\n      const permit_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r4, $event.target.value));\n    });\n    i0.ɵɵelementStart(9, \"option\", 41);\n    i0.ɵɵtext(10, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"option\", 42);\n    i0.ɵɵtext(12, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 43);\n    i0.ɵɵtext(14, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 44);\n    i0.ɵɵtext(16, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 45);\n    i0.ɵɵtext(18, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 46);\n    i0.ɵɵtext(20, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 47);\n    i0.ɵɵtext(22, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 48);\n    i0.ɵɵtext(24, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"td\")(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"td\")(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const permit_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r4.permitName || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r4.permitNumber || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r4.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(permit_r4.permitAppliedDate ? i0.ɵɵpipeBind2(28, 7, permit_r4.permitAppliedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(permit_r4.attentionReason || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"table\", 37)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Permit #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Submitted Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Ball in Court\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_2_ng_container_26_div_2_tr_15_Template, 32, 10, \"tr\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectViewComponent_div_2_ng_container_26_div_1_Template, 5, 0, \"div\", 31)(2, ProjectViewComponent_div_2_ng_container_26_div_2_Template, 16, 1, \"div\", 32);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nfunction ProjectViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12)(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵtext(12, \"Edit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(14, \"i\", 19);\n    i0.ɵɵtext(15, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 20)(17, \"ul\", 21)(18, \"li\", 22)(19, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(20, \" Project Details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"li\", 22)(22, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_22_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"permits\", $event));\n    });\n    i0.ɵɵtext(23, \" Permits List \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 24);\n    i0.ɵɵtemplate(25, ProjectViewComponent_div_2_ng_container_25_Template, 35, 12, \"ng-container\", 25)(26, ProjectViewComponent_div_2_ng_container_26_Template, 3, 2, \"ng-container\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"Project # \", ctx_r1.project.internalProjectNumber || \"\", \" - \", ctx_r1.project.projectName || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"Active\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx_r1.selectedTab === \"permits\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.project);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"permits\");\n  }\n}\nexport class ProjectViewComponent {\n  route;\n  router;\n  cdr;\n  appService;\n  projectsService;\n  permitsService;\n  modalService;\n  httpUtilService;\n  projectId = null;\n  project = null;\n  isLoading = false;\n  selectedTab = 'details';\n  projectPermits = [];\n  loginUser = {};\n  routeSubscription = new Subscription();\n  loadingSubscription = new Subscription();\n  constructor(route, router, cdr, appService, projectsService, permitsService, modalService, httpUtilService) {\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.modalService = modalService;\n    this.httpUtilService = httpUtilService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    // Subscribe to global loading state\n    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading === true;\n    });\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([this.route.paramMap, this.route.queryParams]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n      console.log('Project view - received params:', {\n        projectId: this.projectId,\n        queryParams\n      });\n      // Handle active tab from query params\n      const activeTab = queryParams['activeTab'];\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n        this.selectedTab = activeTab;\n        console.log('Setting selectedTab from query params:', activeTab);\n      } else {\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\n      }\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n    if (this.loadingSubscription) {\n      this.loadingSubscription.unsubscribe();\n    }\n  }\n  fetchProjectDetails() {\n    if (!this.projectId) {\n      return;\n    }\n    this.httpUtilService.loadingSubject.next(true);\n    this.projectsService.getProject({\n      projectId: this.projectId\n    }).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchProjectPermits() {\n    if (!this.projectId) {\n      return;\n    }\n    this.httpUtilService.loadingSubject.next(true);\n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [{\n          field: 'projectId',\n          operator: 'eq',\n          value: this.projectId\n        }]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter(p => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/projects/list']);\n  }\n  editProject() {\n    if (!this.projectId) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(ProjectPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    // Subscribe to the modal event when it closes\n    modalRef.result.then(result => {\n      // Handle successful edit\n      if (result) {\n        console.log('Project edited successfully:', result);\n        // Refresh project details and permits\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n    }, reason => {\n      // Handle modal dismissal\n      console.log('Modal dismissed:', reason);\n    });\n  }\n  viewPermit(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'project',\n        projectId: this.projectId\n      }\n    });\n  }\n  onStatusChange(permit, newStatus) {\n    if (!permit?.permitId || !newStatus) {\n      return;\n    }\n    const allowed = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n    if (!allowed.includes(newStatus)) {\n      return;\n    }\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.httpUtilService.loadingSubject.next(true);\n    this.cdr.markForCheck();\n    this.permitsService.updatePermitInternalReviewStatus({\n      permitId: permit.permitId,\n      internalReviewStatus: newStatus\n    }).subscribe({\n      next: res => {\n        const isFault = res?.isFault || res?.responseData?.isFault;\n        if (isFault) {\n          permit.internalReviewStatus = previous;\n        }\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        permit.internalReviewStatus = previous;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectViewComponent,\n    selectors: [[\"app-project-view\"]],\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"project-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"project-details-header\"], [1, \"header-content\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"project-title\"], [1, \"status-text\", \"status-active\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"portal-button\", 3, \"click\"], [1, \"fa\", \"fa-pencil\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"card-body\"], [4, \"ngIf\"], [1, \"project-details-content\"], [1, \"project-details-grid\"], [1, \"project-detail-item\", \"span-2\"], [1, \"project-value\"], [1, \"project-detail-item\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"fw-bold\", 3, \"click\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"]],\n    template: function ProjectViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, ProjectViewComponent_div_2_Template, 27, 11, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.project);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i7.DatePipe],\n    styles: [\".project-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n\\n.project-view-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  padding-bottom: 0.25rem;\\n}\\n.project-view-container[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding-top: 0.5rem;\\n}\\n\\n.project-details-header[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: transparent;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  padding: 0.375rem 0.75rem !important;\\n  line-height: 1.5 !important;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  padding: 0.15rem 0.5rem;\\n  border-radius: 0.55rem;\\n  background-color: #f3f6f9;\\n  color: #3f4254;\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1;\\n  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.02s ease;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #5e6e82;\\n  font-size: 0.75rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover {\\n  background-color: #eef2f7;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);\\n  text-decoration: none;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(1px);\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n}\\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.project-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n}\\n\\n.project-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.project-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.4rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  line-height: 1.2;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #323337;\\n  text-transform: capitalize;\\n  letter-spacing: 0.1rem;\\n  margin: 0;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #3b3c3f;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%], \\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  margin-top: 0.1rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  padding: 0.5rem 0;\\n  border-bottom: none;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  display: block;\\n  vertical-align: top;\\n  padding: 0.5rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  text-align: left;\\n  background: transparent;\\n  border: none;\\n  min-width: 0;\\n  border-radius: 0;\\n}\\n\\n.project-detail-item.span-2[_ngcontent-%COMP%] {\\n  grid-column: span 2;\\n}\\n\\n.status-active[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n\\n.status-cancelled[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n\\n.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  padding: 0 0.5rem 0 0.5rem;\\n  margin-top: 0;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  overflow: hidden;\\n  table-layout: fixed;\\n  width: 100%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #3f4254;\\n  border-bottom: 2px solid #e5eaee;\\n  padding: 1rem 0.75rem;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5eaee;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-number-col[_ngcontent-%COMP%] {\\n  width: 25%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-description-col[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-type-col[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-status-col[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-number-cell[_ngcontent-%COMP%] {\\n  width: 25%;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-description-cell[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-type-cell[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-status-cell[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child, \\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%] {\\n  color: var(--bs-primary, #0d6efd);\\n  text-decoration: none;\\n  font-weight: 700;\\n  cursor: pointer;\\n  transition: color 0.15s ease, -webkit-text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease, -webkit-text-decoration 0.15s ease;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:hover {\\n  color: #0b5ed7;\\n  text-decoration: underline;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(24, 125, 228, 0.25);\\n  border-radius: 0.25rem;\\n}\\n\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-description-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-type-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-status-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n}\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.project-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.project-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.4);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #3699ff;\\n  padding: 2rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n    gap: 1rem;\\n  }\\n  .project-detail-item.span-2[_ngcontent-%COMP%] {\\n    grid-column: auto;\\n  }\\n  .table-responsive[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.25rem 0.5rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 0.75rem;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: flex-start;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: flex-end;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "combineLatest", "ProjectPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "project", "projectDescription", "internalProjectManagerName", "internalProjectManager", "externalPMNames", "projectLocation", "projectStartDate", "ɵɵpipeBind2", "projectEndDate", "ɵɵelement", "ɵɵlistener", "ProjectViewComponent_div_2_ng_container_26_div_2_tr_15_Template_a_click_2_listener", "permit_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewPermit", "permitId", "ProjectViewComponent_div_2_ng_container_26_div_2_tr_15_Template_select_change_8_listener", "$event", "onStatusChange", "target", "value", "ɵɵtextInterpolate1", "permitName", "permitNumber", "ɵɵproperty", "internalReviewStatus", "isLoading", "permitAppliedDate", "attentionReason", "ɵɵtemplate", "ProjectViewComponent_div_2_ng_container_26_div_2_tr_15_Template", "projectPermits", "ProjectViewComponent_div_2_ng_container_26_div_1_Template", "ProjectViewComponent_div_2_ng_container_26_div_2_Template", "length", "ProjectViewComponent_div_2_Template_button_click_10_listener", "_r1", "editProject", "ProjectViewComponent_div_2_Template_button_click_13_listener", "goBack", "ProjectViewComponent_div_2_Template_a_click_19_listener", "showTab", "ProjectViewComponent_div_2_Template_a_click_22_listener", "ProjectViewComponent_div_2_ng_container_25_Template", "ProjectViewComponent_div_2_ng_container_26_Template", "ɵɵtextInterpolate2", "internalProjectNumber", "projectName", "projectStatus", "ɵɵpureFunction1", "_c0", "selectedTab", "ProjectViewComponent", "route", "router", "cdr", "appService", "projectsService", "permitsService", "modalService", "httpUtilService", "projectId", "loginUser", "routeSubscription", "loadingSubscription", "constructor", "ngOnInit", "getLoggedInUser", "loadingSubject", "subscribe", "loading", "paramMap", "queryParams", "idParam", "get", "Number", "console", "log", "activeTab", "fetchProjectDetails", "fetchProjectPermits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "next", "getProject", "res", "<PERSON><PERSON><PERSON>", "responseData", "Project", "data", "Object", "keys", "error", "faultMessage", "err", "getPermitsForKendoGrid", "take", "skip", "sort", "filter", "logic", "filters", "field", "operator", "search", "loggedInUserId", "userId", "rawPermits", "p", "permitProjectId", "projectID", "project_id", "ProjectId", "ProjectID", "navigate", "NgbModalOptions", "size", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "id", "result", "then", "reason", "from", "permit", "newStatus", "allowed", "includes", "previous", "updatePermitInternalReviewStatus", "getStatusClass", "status", "toLowerCase", "replace", "tab", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "ChangeDetectorRef", "i2", "AppService", "i3", "ProjectsService", "i4", "PermitsService", "i5", "NgbModal", "i6", "HttpUtilsService", "selectors", "decls", "vars", "consts", "template", "ProjectViewComponent_Template", "rf", "ctx", "ProjectViewComponent_div_0_Template", "ProjectViewComponent_div_2_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription, combineLatest } from 'rxjs';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\n\r\n@Component({\r\n  selector: 'app-project-view',\r\n  templateUrl: './project-view.component.html',\r\n  styleUrls: ['./project-view.component.scss']\r\n})\r\nexport class ProjectViewComponent implements OnInit, OnDestroy {\r\n  public projectId: number | null = null;\r\n  public project: any = null;\r\n  public isLoading: boolean = false;\r\n  public selectedTab: string = 'details';\r\n  public projectPermits: any[] = [];\r\n  public loginUser: any = {};\r\n  private routeSubscription: Subscription = new Subscription();\r\n  private loadingSubscription: Subscription = new Subscription();\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private appService: AppService,\r\n    private projectsService: ProjectsService,\r\n    private permitsService: PermitsService,\r\n    private modalService: NgbModal,\r\n    private httpUtilService: HttpUtilsService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n\r\n    // Subscribe to global loading state\r\n    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(\r\n      (loading: any) => {\r\n        this.isLoading = loading === true;\r\n      }\r\n    );\r\n\r\n    // Combine route params and query params to handle both together\r\n    this.routeSubscription = combineLatest([\r\n      this.route.paramMap,\r\n      this.route.queryParams\r\n    ]).subscribe(([paramMap, queryParams]) => {\r\n      const idParam = paramMap.get('id');\r\n      this.projectId = idParam ? Number(idParam) : null;\r\n\r\n      console.log('Project view - received params:', { projectId: this.projectId, queryParams });\r\n      \r\n      // Handle active tab from query params\r\n      const activeTab = queryParams['activeTab'];\r\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\r\n        this.selectedTab = activeTab;\r\n        console.log('Setting selectedTab from query params:', activeTab);\r\n      } else {\r\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\r\n      }\r\n\r\n      if (this.projectId) {\r\n        this.fetchProjectDetails();\r\n        this.fetchProjectPermits();\r\n      }\r\n      \r\n      this.cdr.markForCheck();\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.routeSubscription) {\r\n      this.routeSubscription.unsubscribe();\r\n    }\r\n    if (this.loadingSubscription) {\r\n      this.loadingSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  public fetchProjectDetails(): void {\r\n    if (!this.projectId) { return; }\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.projectsService.getProject({ projectId: this.projectId }).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.log('Project API Response:', res);\r\n        if (!res?.isFault) {\r\n          // Try different response structures\r\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\r\n          console.log('Project data assigned:', this.project);\r\n          console.log('Project fields available:', Object.keys(this.project || {}));\r\n          // Don't override selectedTab here - let query params handle it\r\n        } else {\r\n          console.error('API returned fault:', res.faultMessage);\r\n          this.project = null;\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error fetching project details:', err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public fetchProjectPermits(): void {\r\n    if (!this.projectId) { return; }\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    \r\n    // Get permits for this specific project\r\n    this.permitsService.getPermitsForKendoGrid({\r\n      take: 100,\r\n      skip: 0,\r\n      sort: [],\r\n      filter: {\r\n        logic: 'and',\r\n        filters: [\r\n          {\r\n            field: 'projectId',\r\n            operator: 'eq',\r\n            value: this.projectId\r\n          }\r\n        ]\r\n      },\r\n      search: '',\r\n      loggedInUserId: this.loginUser.userId\r\n    }).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.log('Project permits API response:', res);\r\n        if (res?.isFault) {\r\n          console.error('Failed to load project permits:', res.faultMessage);\r\n          this.projectPermits = [];\r\n        } else {\r\n          const rawPermits = res.responseData?.data || res.data || [];\r\n          // Client-side guard: ensure only permits for this project are shown\r\n          this.projectPermits = (rawPermits || []).filter((p: any) => {\r\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\r\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\r\n          });\r\n          console.log('Project permits assigned (filtered):', this.projectPermits);\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error loading project permits:', err);\r\n        this.projectPermits = [];\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public goBack(): void {\r\n    this.router.navigate(['/projects/list']);\r\n  }\r\n\r\n  public editProject(): void {\r\n    if (!this.projectId) { return; }\r\n    \r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the ProjectPopup\r\n    const modalRef = this.modalService.open(\r\n      ProjectPopupComponent,\r\n      NgbModalOptions\r\n    );\r\n    \r\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\r\n    modalRef.componentInstance.id = this.projectId;\r\n    modalRef.componentInstance.project = this.project;\r\n    \r\n    // Subscribe to the modal event when it closes\r\n    modalRef.result.then(\r\n      (result) => {\r\n        // Handle successful edit\r\n        if (result) {\r\n          console.log('Project edited successfully:', result);\r\n          // Refresh project details and permits\r\n          this.fetchProjectDetails();\r\n          this.fetchProjectPermits();\r\n        }\r\n      },\r\n      (reason) => {\r\n        // Handle modal dismissal\r\n        console.log('Modal dismissed:', reason);\r\n      }\r\n    );\r\n  }\r\n\r\n  public viewPermit(permitId: number): void {\r\n    this.router.navigate(['/permits/view', permitId], { \r\n      queryParams: { from: 'project', projectId: this.projectId } \r\n    });\r\n  }\r\n\r\n  public onStatusChange(permit: any, newStatus: string): void {\r\n    if (!permit?.permitId || !newStatus) { return; }\r\n    const allowed = [\r\n      'Approved',\r\n      'Pacifica Verification',\r\n      'Dis-Approved',\r\n      'Pending',\r\n      'Not Required',\r\n      'In Review',\r\n      '1 Cycle Completed'\r\n    ];\r\n    if (!allowed.includes(newStatus)) { return; }\r\n\r\n    const previous = permit.internalReviewStatus;\r\n    permit.internalReviewStatus = newStatus;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.cdr.markForCheck();\r\n\r\n    this.permitsService\r\n      .updatePermitInternalReviewStatus({ permitId: permit.permitId, internalReviewStatus: newStatus })\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          const isFault = res?.isFault || res?.responseData?.isFault;\r\n          if (isFault) {\r\n            permit.internalReviewStatus = previous;\r\n          }\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          this.cdr.markForCheck();\r\n        },\r\n        error: () => {\r\n          permit.internalReviewStatus = previous;\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          this.cdr.markForCheck();\r\n        }\r\n      });\r\n  }\r\n\r\n  public getStatusClass(status: string): string {\r\n    if (!status) return 'status-n-a';\r\n    return (\r\n      'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-')\r\n    );\r\n  }\r\n\r\n  showTab(tab: string, $event: any) {\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"custom-colored-spinner\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<div class=\"project-view-container\">\n  <!-- Project Details Card -->\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"project\">\n    <!-- Project Details Header -->\n    <div class=\"project-details-header\">\n      <div class=\"header-content\">\n        <div class=\"title-wrap\">\n          <div class=\"title-line\">\n            <span class=\"project-title\">Project # {{ project.internalProjectNumber || \"\" }} - {{ project.projectName || \"\" }}</span>\n            <span class=\"status-text status-active\">{{ project.projectStatus || \"Active\" }}</span>\n          </div>\n        </div>\n        <div class=\"button-group\">\n          <button type=\"button\" class=\"btn portal-button\" (click)=\"editProject()\">\n            <i class=\"fa fa-pencil\"></i>Edit\n          </button>\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center\" (click)=\"goBack()\">\n            <i class=\"fas fa-arrow-left me-2\"></i>\n            Back\n          </button>\n        </div>\n      </div>\n    </div>\n    <!-- Card Header with Tabs -->\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\">\n      <!-- Tabs -->\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap\">\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'details' }\"\n            (click)=\"showTab('details', $event)\">\n            Project Details\n          </a>\n        </li>\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'permits' }\"\n            (click)=\"showTab('permits', $event)\">\n            Permits List\n          </a>\n        </li>\n      </ul>\n    </div>\n\n    <!-- Card Body with Tab Content -->\n    <div class=\"card-body\">\n      <!-- Project Details Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'details' && project\">\n        <div class=\"project-details-content\">\n          <div class=\"project-details-grid\">\n            <div class=\"project-detail-item span-2\">\n              <label>Description</label>\n              <span class=\"project-value\">{{ project.projectDescription || \"\" }}</span>\n            </div>\n             <div class=\"project-detail-item\">\n               <label>Project Manager</label>\n               <span class=\"project-value\">{{ project.internalProjectManagerName || project.internalProjectManager || \"\" }}</span>\n             </div>\n            <div class=\"project-detail-item\">\n              <label>External Project Manager</label>\n              <span class=\"project-value\">{{ project.externalPMNames || \"\" }}</span>\n            </div>\n            <div class=\"project-detail-item span-2\">\n              <label>Location</label>\n              <span class=\"project-value\">{{ project.projectLocation || \"\" }}</span>\n            </div>\n            <div class=\"project-detail-item\">\n              <label>Start Date</label>\n              <span class=\"project-value\">{{\n                project.projectStartDate\n                ? (project.projectStartDate | date : \"MM/dd/yyyy\")\n                : \"\"\n              }}</span>\n            </div>\n            <div class=\"project-detail-item\">\n              <label>End Date</label>\n              <span class=\"project-value\">{{\n                project.projectEndDate\n                ? (project.projectEndDate | date : \"MM/dd/yyyy\")\n                : \"\"\n              }}</span>\n            </div>\n           </div>\n         </div>\n       </ng-container>\n\n      <!-- Permits List Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'permits'\">\n        <!-- Empty State for Permits -->\n        <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"projectPermits.length === 0\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-file-alt fa-3x mb-3\"></i>\n            <p>No permits found for this project.</p>\n          </div>\n        </div>\n\n        <!-- Permits Table -->\n        <div class=\"table-responsive\" *ngIf=\"projectPermits.length > 0\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th>Permit Name</th>\n                <th>Permit #</th>\n                <th>Status</th>\n                <th>Submitted Date</th>\n                <th>Ball in Court</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let permit of projectPermits\">\n                <td>\n                  <a \n                    class=\"fw-bold\" \n                    (click)=\"viewPermit(permit.permitId)\" \n                    title=\"View Permit\"\n                    aria-label=\"View Permit\"\n                  >\n                    {{ permit.permitName || \"\" }}\n                  </a>\n                </td>\n                <td>\n                  <span>{{ permit.permitNumber || \"\" }}</span>\n                </td>\n                <td>\n                  <select class=\"form-select form-select-sm w-auto\"\n                          [value]=\"permit.internalReviewStatus || ''\"\n                          (change)=\"onStatusChange(permit, $any($event.target).value)\"\n                          [disabled]=\"isLoading\">\n                    <option [value]=\"''\" disabled>Select status</option>\n                    <option value=\"Approved\">Approved</option>\n                    <option value=\"Pacifica Verification\">Pacifica Verification</option>\n                    <option value=\"Dis-Approved\">Dis-Approved</option>\n                    <option value=\"Pending\">Pending</option>\n                    <option value=\"Not Required\">Not Required</option>\n                    <option value=\"In Review\">In Review</option>\n                    <option value=\"1 Cycle Completed\">1 Cycle Completed</option>\n                  </select>\n                </td>\n                <td>\n                  <span>{{ permit.permitAppliedDate ? (permit.permitAppliedDate | date : 'MM/dd/yyyy') : '' }}</span>\n                </td>\n                <td>\n                  <span>{{ permit.attentionReason || '' }}</span>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,aAAa,QAAQ,MAAM;AAKlD,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;;;;;;;;;;ICH1EC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;IA+CAH,EAAA,CAAAI,uBAAA,GAA0D;IAIlDJ,EAHN,CAAAC,cAAA,cAAqC,cACD,cACQ,YAC/B;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;IAEHH,EADF,CAAAC,cAAA,cAAiC,YACxB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgF;IAC9GF,EAD8G,CAAAG,YAAA,EAAO,EAC/G;IAELH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAwC,aAC/B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IAGPF,EAHO,CAAAG,YAAA,EAAO,EACL,EACD,EACF;;;;;IA/B2BH,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAC,kBAAA,OAAsC;IAIrCT,EAAA,CAAAK,SAAA,GAAgF;IAAhFL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAE,0BAAA,IAAAH,MAAA,CAAAC,OAAA,CAAAG,sBAAA,OAAgF;IAIjFX,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAI,eAAA,OAAmC;IAInCZ,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAK,eAAA,OAAmC;IAInCb,EAAA,CAAAK,SAAA,GAI1B;IAJ0BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAM,gBAAA,GAAAd,EAAA,CAAAe,WAAA,QAAAR,MAAA,CAAAC,OAAA,CAAAM,gBAAA,qBAI1B;IAI0Bd,EAAA,CAAAK,SAAA,GAI1B;IAJ0BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAQ,cAAA,GAAAhB,EAAA,CAAAe,WAAA,QAAAR,MAAA,CAAAC,OAAA,CAAAQ,cAAA,qBAI1B;;;;;IAUNhB,EADF,CAAAC,cAAA,cAAkH,cACvF;IACvBD,EAAA,CAAAiB,SAAA,YAA0C;IAC1CjB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAEzCF,EAFyC,CAAAG,YAAA,EAAI,EACrC,EACF;;;;;;IAiBIH,EAFJ,CAAAC,cAAA,SAA0C,SACpC,YAMD;IAHCD,EAAA,CAAAkB,UAAA,mBAAAC,mFAAA;MAAA,MAAAC,SAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASlB,MAAA,CAAAmB,UAAA,CAAAN,SAAA,CAAAO,QAAA,CAA2B;IAAA,EAAC;IAIrC3B,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAEHH,EADF,CAAAC,cAAA,SAAI,WACI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACzC;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAI6B;IADvBD,EAAA,CAAAkB,UAAA,oBAAAU,yFAAAC,MAAA;MAAA,MAAAT,SAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAUlB,MAAA,CAAAuB,cAAA,CAAAV,SAAA,EAAAS,MAAA,CAAAE,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAElEhC,EAAA,CAAAC,cAAA,iBAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAAsC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAAkC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACrD,EACN;IAEHH,EADF,CAAAC,cAAA,UAAI,YACI;IAAAD,EAAA,CAAAE,MAAA,IAAsF;;IAC9FF,EAD8F,CAAAG,YAAA,EAAO,EAChG;IAEHH,EADF,CAAAC,cAAA,UAAI,YACI;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAE5CF,EAF4C,CAAAG,YAAA,EAAO,EAC5C,EACF;;;;;IA3BCH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAiC,kBAAA,MAAAb,SAAA,CAAAc,UAAA,YACF;IAGMlC,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAc,SAAA,CAAAe,YAAA,OAA+B;IAI7BnC,EAAA,CAAAK,SAAA,GAA2C;IAE3CL,EAFA,CAAAoC,UAAA,UAAAhB,SAAA,CAAAiB,oBAAA,OAA2C,aAAA9B,MAAA,CAAA+B,SAAA,CAErB;IACpBtC,EAAA,CAAAK,SAAA,EAAY;IAAZL,EAAA,CAAAoC,UAAA,aAAY;IAWhBpC,EAAA,CAAAK,SAAA,IAAsF;IAAtFL,EAAA,CAAAM,iBAAA,CAAAc,SAAA,CAAAmB,iBAAA,GAAAvC,EAAA,CAAAe,WAAA,QAAAK,SAAA,CAAAmB,iBAAA,qBAAsF;IAGtFvC,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAc,SAAA,CAAAoB,eAAA,OAAkC;;;;;IAzC1CxC,EAJR,CAAAC,cAAA,cAAgE,gBAC7B,YACxB,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAErBF,EAFqB,CAAAG,YAAA,EAAK,EACnB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAyC,UAAA,KAAAC,+DAAA,mBAA0C;IAsChD1C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAtCuBH,EAAA,CAAAK,SAAA,IAAiB;IAAjBL,EAAA,CAAAoC,UAAA,YAAA7B,MAAA,CAAAoC,cAAA,CAAiB;;;;;IAtBhD3C,EAAA,CAAAI,uBAAA,GAA+C;IAU7CJ,EARA,CAAAyC,UAAA,IAAAG,yDAAA,kBAAkH,IAAAC,yDAAA,mBAQlD;;;;;IARe7C,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAoC,UAAA,SAAA7B,MAAA,CAAAoC,cAAA,CAAAG,MAAA,OAAiC;IAQjF9C,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAoC,UAAA,SAAA7B,MAAA,CAAAoC,cAAA,CAAAG,MAAA,KAA+B;;;;;;IAvF1D9C,EANV,CAAAC,cAAA,aAAsD,aAEhB,cACN,cACF,cACE,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxHH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAEnFF,EAFmF,CAAAG,YAAA,EAAO,EAClF,EACF;IAEJH,EADF,CAAAC,cAAA,cAA0B,kBACgD;IAAxBD,EAAA,CAAAkB,UAAA,mBAAA6B,6DAAA;MAAA/C,EAAA,CAAAqB,aAAA,CAAA2B,GAAA;MAAA,MAAAzC,MAAA,GAAAP,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASlB,MAAA,CAAA0C,WAAA,EAAa;IAAA,EAAC;IACrEjD,EAAA,CAAAiB,SAAA,aAA4B;IAAAjB,EAAA,CAAAE,MAAA,aAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwG;IAAnBD,EAAA,CAAAkB,UAAA,mBAAAgC,6DAAA;MAAAlD,EAAA,CAAAqB,aAAA,CAAA2B,GAAA;MAAA,MAAAzC,MAAA,GAAAP,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASlB,MAAA,CAAA4C,MAAA,EAAQ;IAAA,EAAC;IACrGnD,EAAA,CAAAiB,SAAA,aAAsC;IACtCjB,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAMAH,EAJN,CAAAC,cAAA,eAAyF,cAEgB,cAChF,aAEoB;IAArCD,EAAA,CAAAkB,UAAA,mBAAAkC,wDAAAvB,MAAA;MAAA7B,EAAA,CAAAqB,aAAA,CAAA2B,GAAA;MAAA,MAAAzC,MAAA,GAAAP,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASlB,MAAA,CAAA8C,OAAA,CAAQ,SAAS,EAAAxB,MAAA,CAAS;IAAA,EAAC;IACpC7B,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAEHH,EADF,CAAAC,cAAA,cAAqB,aAEoB;IAArCD,EAAA,CAAAkB,UAAA,mBAAAoC,wDAAAzB,MAAA;MAAA7B,EAAA,CAAAqB,aAAA,CAAA2B,GAAA;MAAA,MAAAzC,MAAA,GAAAP,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASlB,MAAA,CAAA8C,OAAA,CAAQ,SAAS,EAAAxB,MAAA,CAAS;IAAA,EAAC;IACpC7B,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAI,EACD,EACF,EACD;IAGNH,EAAA,CAAAC,cAAA,eAAuB;IA0CrBD,EAxCA,CAAAyC,UAAA,KAAAc,mDAAA,6BAA0D,KAAAC,mDAAA,2BAwCX;IA+DnDxD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA5IgCH,EAAA,CAAAK,SAAA,GAAqF;IAArFL,EAAA,CAAAyD,kBAAA,eAAAlD,MAAA,CAAAC,OAAA,CAAAkD,qBAAA,eAAAnD,MAAA,CAAAC,OAAA,CAAAmD,WAAA,WAAqF;IACzE3D,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAoD,aAAA,aAAuC;IAmBrB5D,EAAA,CAAAK,SAAA,IAAiD;IAAjDL,EAAA,CAAAoC,UAAA,YAAApC,EAAA,CAAA6D,eAAA,IAAAC,GAAA,EAAAvD,MAAA,CAAAwD,WAAA,gBAAiD;IAMjD/D,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAoC,UAAA,YAAApC,EAAA,CAAA6D,eAAA,IAAAC,GAAA,EAAAvD,MAAA,CAAAwD,WAAA,gBAAiD;IAWlG/D,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAoC,UAAA,SAAA7B,MAAA,CAAAwD,WAAA,iBAAAxD,MAAA,CAAAC,OAAA,CAAyC;IAwCzCR,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAoC,UAAA,SAAA7B,MAAA,CAAAwD,WAAA,cAA8B;;;ADhFnD,OAAM,MAAOC,oBAAoB;EAWrBC,KAAA;EACAC,MAAA;EACAC,GAAA;EACAC,UAAA;EACAC,eAAA;EACAC,cAAA;EACAC,YAAA;EACAC,eAAA;EAjBHC,SAAS,GAAkB,IAAI;EAC/BjE,OAAO,GAAQ,IAAI;EACnB8B,SAAS,GAAY,KAAK;EAC1ByB,WAAW,GAAW,SAAS;EAC/BpB,cAAc,GAAU,EAAE;EAC1B+B,SAAS,GAAQ,EAAE;EAClBC,iBAAiB,GAAiB,IAAI9E,YAAY,EAAE;EACpD+E,mBAAmB,GAAiB,IAAI/E,YAAY,EAAE;EAE9DgF,YACUZ,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,cAA8B,EAC9BC,YAAsB,EACtBC,eAAiC;IAPjC,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;EACtB;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACN,UAAU,CAACW,eAAe,EAAE;IAElD;IACA,IAAI,CAACH,mBAAmB,GAAG,IAAI,CAACJ,eAAe,CAACQ,cAAc,CAACC,SAAS,CACrEC,OAAY,IAAI;MACf,IAAI,CAAC5C,SAAS,GAAG4C,OAAO,KAAK,IAAI;IACnC,CAAC,CACF;IAED;IACA,IAAI,CAACP,iBAAiB,GAAG7E,aAAa,CAAC,CACrC,IAAI,CAACmE,KAAK,CAACkB,QAAQ,EACnB,IAAI,CAAClB,KAAK,CAACmB,WAAW,CACvB,CAAC,CAACH,SAAS,CAAC,CAAC,CAACE,QAAQ,EAAEC,WAAW,CAAC,KAAI;MACvC,MAAMC,OAAO,GAAGF,QAAQ,CAACG,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,CAACb,SAAS,GAAGY,OAAO,GAAGE,MAAM,CAACF,OAAO,CAAC,GAAG,IAAI;MAEjDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAAEhB,SAAS,EAAE,IAAI,CAACA,SAAS;QAAEW;MAAW,CAAE,CAAC;MAE1F;MACA,MAAMM,SAAS,GAAGN,WAAW,CAAC,WAAW,CAAC;MAC1C,IAAIM,SAAS,KAAKA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,SAAS,CAAC,EAAE;QACrE,IAAI,CAAC3B,WAAW,GAAG2B,SAAS;QAC5BF,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,SAAS,CAAC;MAClE,CAAC,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC1B,WAAW,CAAC;MAC7E;MAEA,IAAI,IAAI,CAACU,SAAS,EAAE;QAClB,IAAI,CAACkB,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;MAEA,IAAI,CAACzB,GAAG,CAAC0B,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACnB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACoB,WAAW,EAAE;IACtC;IACA,IAAI,IAAI,CAACnB,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACmB,WAAW,EAAE;IACxC;EACF;EAEOJ,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAClB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACD,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC3B,eAAe,CAAC4B,UAAU,CAAC;MAAExB,SAAS,EAAE,IAAI,CAACA;IAAS,CAAE,CAAC,CAACQ,SAAS,CAAC;MACvEe,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC1B,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/CR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAES,GAAG,CAAC;QACzC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB;UACA,IAAI,CAAC3F,OAAO,GAAG0F,GAAG,CAACE,YAAY,EAAEC,OAAO,IAAIH,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACE,YAAY,IAAI,IAAI;UAC9FZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACjF,OAAO,CAAC;UACnDgF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEc,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChG,OAAO,IAAI,EAAE,CAAC,CAAC;UACzE;QACF,CAAC,MAAM;UACLgF,OAAO,CAACiB,KAAK,CAAC,qBAAqB,EAAEP,GAAG,CAACQ,YAAY,CAAC;UACtD,IAAI,CAAClG,OAAO,GAAG,IAAI;QACrB;QACA,IAAI,CAAC2D,GAAG,CAAC0B,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAG,IAAI;QACb,IAAI,CAACnC,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/CR,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEE,GAAG,CAAC;QACrD,IAAI,CAACxC,GAAG,CAAC0B,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOD,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACnB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACD,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAAC1B,cAAc,CAACsC,sBAAsB,CAAC;MACzCC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;QACNC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,CACP;UACEC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,IAAI;UACdpF,KAAK,EAAE,IAAI,CAACyC;SACb;OAEJ;MACD4C,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,IAAI,CAAC5C,SAAS,CAAC6C;KAChC,CAAC,CAACtC,SAAS,CAAC;MACXe,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC1B,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/CR,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,GAAG,CAAC;QACjD,IAAIA,GAAG,EAAEC,OAAO,EAAE;UAChBX,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEP,GAAG,CAACQ,YAAY,CAAC;UAClE,IAAI,CAAC/D,cAAc,GAAG,EAAE;QAC1B,CAAC,MAAM;UACL,MAAM6E,UAAU,GAAGtB,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACI,IAAI,IAAI,EAAE;UAC3D;UACA,IAAI,CAAC3D,cAAc,GAAG,CAAC6E,UAAU,IAAI,EAAE,EAAER,MAAM,CAAES,CAAM,IAAI;YACzD,MAAMC,eAAe,GAAGD,CAAC,EAAEhD,SAAS,IAAIgD,CAAC,EAAEE,SAAS,IAAIF,CAAC,EAAEG,UAAU,IAAIH,CAAC,EAAEI,SAAS,IAAIJ,CAAC,EAAEK,SAAS;YACrG,OAAO,IAAI,CAACrD,SAAS,IAAI,IAAI,GAAGc,MAAM,CAACmC,eAAe,CAAC,KAAKnC,MAAM,CAAC,IAAI,CAACd,SAAS,CAAC,GAAG,IAAI;UAC3F,CAAC,CAAC;UACFe,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC9C,cAAc,CAAC;QAC1E;QACA,IAAI,CAACwB,GAAG,CAAC0B,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAQ,IAAI;QAClB,IAAI,CAACnC,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/CR,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEE,GAAG,CAAC;QACpD,IAAI,CAAChE,cAAc,GAAG,EAAE;QACxB,IAAI,CAACwB,GAAG,CAAC0B,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEO1C,MAAMA,CAAA;IACX,IAAI,CAACe,MAAM,CAAC6D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEO9E,WAAWA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACwB,SAAS,EAAE;MAAE;IAAQ;IAE/B,MAAMuD,eAAe,GAKjB;MACFC,IAAI,EAAE,IAAI;MAAE;MACZC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC9D,YAAY,CAAC+D,IAAI,CACrCvI,qBAAqB,EACrBiI,eAAe,CAChB;IAED;IACAK,QAAQ,CAACE,iBAAiB,CAACC,EAAE,GAAG,IAAI,CAAC/D,SAAS;IAC9C4D,QAAQ,CAACE,iBAAiB,CAAC/H,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACA6H,QAAQ,CAACI,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAIA,MAAM,EAAE;QACVjD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgD,MAAM,CAAC;QACnD;QACA,IAAI,CAAC9C,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;IACF,CAAC,EACA+C,MAAM,IAAI;MACT;MACAnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,MAAM,CAAC;IACzC,CAAC,CACF;EACH;EAEOjH,UAAUA,CAACC,QAAgB;IAChC,IAAI,CAACuC,MAAM,CAAC6D,QAAQ,CAAC,CAAC,eAAe,EAAEpG,QAAQ,CAAC,EAAE;MAChDyD,WAAW,EAAE;QAAEwD,IAAI,EAAE,SAAS;QAAEnE,SAAS,EAAE,IAAI,CAACA;MAAS;KAC1D,CAAC;EACJ;EAEO3C,cAAcA,CAAC+G,MAAW,EAAEC,SAAiB;IAClD,IAAI,CAACD,MAAM,EAAElH,QAAQ,IAAI,CAACmH,SAAS,EAAE;MAAE;IAAQ;IAC/C,MAAMC,OAAO,GAAG,CACd,UAAU,EACV,uBAAuB,EACvB,cAAc,EACd,SAAS,EACT,cAAc,EACd,WAAW,EACX,mBAAmB,CACpB;IACD,IAAI,CAACA,OAAO,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MAAE;IAAQ;IAE5C,MAAMG,QAAQ,GAAGJ,MAAM,CAACxG,oBAAoB;IAC5CwG,MAAM,CAACxG,oBAAoB,GAAGyG,SAAS;IACvC,IAAI,CAACtE,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC7B,GAAG,CAAC0B,YAAY,EAAE;IAEvB,IAAI,CAACvB,cAAc,CAChB4E,gCAAgC,CAAC;MAAEvH,QAAQ,EAAEkH,MAAM,CAAClH,QAAQ;MAAEU,oBAAoB,EAAEyG;IAAS,CAAE,CAAC,CAChG7D,SAAS,CAAC;MACTe,IAAI,EAAGE,GAAQ,IAAI;QACjB,MAAMC,OAAO,GAAGD,GAAG,EAAEC,OAAO,IAAID,GAAG,EAAEE,YAAY,EAAED,OAAO;QAC1D,IAAIA,OAAO,EAAE;UACX0C,MAAM,CAACxG,oBAAoB,GAAG4G,QAAQ;QACxC;QACA,IAAI,CAACzE,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC7B,GAAG,CAAC0B,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAEA,CAAA,KAAK;QACVoC,MAAM,CAACxG,oBAAoB,GAAG4G,QAAQ;QACtC,IAAI,CAACzE,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC7B,GAAG,CAAC0B,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEOsD,cAAcA,CAACC,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,OACE,SAAS,GAAGA,MAAM,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE7E;EAEAjG,OAAOA,CAACkG,GAAW,EAAE1H,MAAW;IAC9B,IAAI,CAACkC,WAAW,GAAGwF,GAAG;IACtB,IAAI,CAACpF,GAAG,CAAC0B,YAAY,EAAE;EACzB;;qCAnPW7B,oBAAoB,EAAAhE,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1J,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA3J,EAAA,CAAAwJ,iBAAA,CAAAxJ,EAAA,CAAA4J,iBAAA,GAAA5J,EAAA,CAAAwJ,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA9J,EAAA,CAAAwJ,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAhK,EAAA,CAAAwJ,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAlK,EAAA,CAAAwJ,iBAAA,CAAAW,EAAA,CAAAC,QAAA,GAAApK,EAAA,CAAAwJ,iBAAA,CAAAa,EAAA,CAAAC,gBAAA;EAAA;;UAApBtG,oBAAoB;IAAAuG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdjC7K,EAAA,CAAAyC,UAAA,IAAAsI,mCAAA,iBAA0D;QAS1D/K,EAAA,CAAAC,cAAA,aAAoC;QAElCD,EAAA,CAAAyC,UAAA,IAAAuI,mCAAA,mBAAsD;QAmJxDhL,EAAA,CAAAG,YAAA,EAAM;;;QA9JAH,EAAA,CAAAoC,UAAA,SAAA0I,GAAA,CAAAxI,SAAA,CAAe;QAWoBtC,EAAA,CAAAK,SAAA,GAAa;QAAbL,EAAA,CAAAoC,UAAA,SAAA0I,GAAA,CAAAtK,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}