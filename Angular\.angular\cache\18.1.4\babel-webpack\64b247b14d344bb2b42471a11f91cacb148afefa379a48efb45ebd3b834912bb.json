{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { RouterModule } from '@angular/router';\nimport { NgbDropdownModule, NgbProgressbarModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TranslationModule } from '../../modules/i18n';\nimport { LayoutComponent } from './layout.component';\nimport { ExtrasModule } from '../partials/layout/extras/extras.module';\nimport { Routing } from '../../pages/routing';\nimport { HeaderComponent } from './components/header/header.component';\nimport { ContentComponent } from './components/content/content.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport { ScriptsInitComponent } from './components/scripts-init/scripts-init.component';\nimport { ToolbarComponent } from './components/toolbar/toolbar.component';\nimport { TopbarComponent } from './components/topbar/topbar.component';\nimport { PageTitleComponent } from './components/header/page-title/page-title.component';\nimport { HeaderMenuComponent } from './components/header/header-menu/header-menu.component';\nimport { DrawersModule, DropdownMenusModule, ModalsModule, EngagesModule } from '../partials';\nimport { EngagesComponent } from '../partials/layout/engages/engages.component';\nimport { ThemeModeModule } from '../partials/layout/theme-mode-switcher/theme-mode.module';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport { SidebarLogoComponent } from './components/sidebar/sidebar-logo/sidebar-logo.component';\nimport { SidebarMenuComponent } from './components/sidebar/sidebar-menu/sidebar-menu.component';\nimport { SidebarFooterComponent } from './components/sidebar/sidebar-footer/sidebar-footer.component';\nimport { NavbarComponent } from './components/header/navbar/navbar.component';\nimport { AccountingComponent } from './components/toolbar/accounting/accounting.component';\nimport { ClassicComponent } from './components/toolbar/classic/classic.component';\nimport { ExtendedComponent } from './components/toolbar/extended/extended.component';\nimport { ReportsComponent } from './components/toolbar/reports/reports.component';\nimport { SaasComponent } from './components/toolbar/saas/saas.component';\nimport { SharedModule } from \"../shared/shared.module\";\nimport { HttpUtilsService } from '../../modules/services/http-utils.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../partials/layout/extras/dropdown-inner/notifications-inner/notifications-inner.component\";\nimport * as i4 from \"../partials/layout/extras/dropdown-inner/quick-links-inner/quick-links-inner.component\";\nimport * as i5 from \"../partials/layout/extras/dropdown-inner/search-result-inner/search-result-inner.component\";\nimport * as i6 from \"../partials/layout/extras/dropdown-inner/user-inner/user-inner.component\";\nimport * as i7 from \"../partials/layout/theme-mode-switcher/theme-mode-switcher.component\";\nimport * as i8 from \"../shared/keenicon/keenicon.component\";\nconst routes = [{\n  path: '',\n  component: LayoutComponent,\n  children: Routing\n}];\nexport class LayoutModule {\n  static ɵfac = function LayoutModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LayoutModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LayoutModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [HttpUtilsService],\n    imports: [CommonModule, RouterModule.forChild(routes), TranslationModule, InlineSVGModule, NgbDropdownModule, NgbProgressbarModule, ExtrasModule, ModalsModule, DrawersModule, EngagesModule, DropdownMenusModule, NgbTooltipModule, TranslateModule, ThemeModeModule, SharedModule, RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LayoutModule, {\n    declarations: [LayoutComponent, HeaderComponent, ContentComponent, FooterComponent, ScriptsInitComponent, ToolbarComponent, TopbarComponent, PageTitleComponent, HeaderMenuComponent, EngagesComponent, SidebarComponent, SidebarLogoComponent, SidebarMenuComponent, SidebarFooterComponent, NavbarComponent, AccountingComponent, ClassicComponent, ExtendedComponent, ReportsComponent, SaasComponent],\n    imports: [CommonModule, i1.RouterModule, TranslationModule, InlineSVGModule, NgbDropdownModule, NgbProgressbarModule, ExtrasModule, ModalsModule, DrawersModule, EngagesModule, DropdownMenusModule, NgbTooltipModule, TranslateModule, ThemeModeModule, SharedModule],\n    exports: [RouterModule]\n  });\n})();\ni0.ɵɵsetComponentScope(TopbarComponent, function () {\n  return [i2.NgClass, i2.NgIf, i3.NotificationsInnerComponent, i4.QuickLinksInnerComponent, i5.SearchResultInnerComponent, i6.UserInnerComponent, i7.ThemeModeSwitcherComponent, i8.KeeniconComponent];\n}, []);", "map": {"version": 3, "names": ["CommonModule", "InlineSVGModule", "RouterModule", "NgbDropdownModule", "NgbProgressbarModule", "NgbTooltipModule", "TranslateModule", "TranslationModule", "LayoutComponent", "ExtrasModule", "Routing", "HeaderComponent", "ContentComponent", "FooterComponent", "ScriptsInitComponent", "ToolbarComponent", "TopbarComponent", "PageTitleComponent", "HeaderMenuComponent", "DrawersModule", "DropdownMenusModule", "ModalsModule", "EngagesModule", "EngagesComponent", "ThemeModeModule", "SidebarComponent", "SidebarLogoComponent", "SidebarMenuComponent", "SidebarFooterComponent", "NavbarComponent", "AccountingComponent", "ClassicComponent", "ExtendedComponent", "ReportsComponent", "SaasComponent", "SharedModule", "HttpUtilsService", "routes", "path", "component", "children", "LayoutModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1", "exports", "i2", "Ng<PERSON><PERSON>", "NgIf", "i3", "NotificationsInnerComponent", "i4", "QuickLinksInnerComponent", "i5", "SearchResultInnerComponent", "i6", "UserInnerComponent", "i7", "ThemeModeSwitcherComponent", "i8", "KeeniconComponent"], "sources": ["D:\\permittracker\\Angular\\src\\app\\_metronic\\layout\\layout.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { RouterModule, Routes } from '@angular/router';\nimport {\n  NgbDropdownModule,\n  NgbProgressbarModule,\n  NgbTooltipModule,\n} from '@ng-bootstrap/ng-bootstrap';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TranslationModule } from '../../modules/i18n';\nimport { LayoutComponent } from './layout.component';\nimport { ExtrasModule } from '../partials/layout/extras/extras.module';\nimport { Routing } from '../../pages/routing';\nimport { HeaderComponent } from './components/header/header.component';\nimport { ContentComponent } from './components/content/content.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport { ScriptsInitComponent } from './components/scripts-init/scripts-init.component';\nimport { ToolbarComponent } from './components/toolbar/toolbar.component';\nimport { TopbarComponent } from './components/topbar/topbar.component';\nimport { PageTitleComponent } from './components/header/page-title/page-title.component';\nimport { HeaderMenuComponent } from './components/header/header-menu/header-menu.component';\nimport {\n  DrawersModule,\n  DropdownMenusModule,\n  ModalsModule,\n  EngagesModule,\n} from '../partials';\nimport { EngagesComponent } from '../partials/layout/engages/engages.component';\nimport { ThemeModeModule } from '../partials/layout/theme-mode-switcher/theme-mode.module';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport { SidebarLogoComponent } from './components/sidebar/sidebar-logo/sidebar-logo.component';\nimport { SidebarMenuComponent } from './components/sidebar/sidebar-menu/sidebar-menu.component';\nimport { SidebarFooterComponent } from './components/sidebar/sidebar-footer/sidebar-footer.component';\nimport { NavbarComponent } from './components/header/navbar/navbar.component';\nimport { AccountingComponent } from './components/toolbar/accounting/accounting.component';\nimport { ClassicComponent } from './components/toolbar/classic/classic.component';\nimport { ExtendedComponent } from './components/toolbar/extended/extended.component';\nimport { ReportsComponent } from './components/toolbar/reports/reports.component';\nimport { SaasComponent } from './components/toolbar/saas/saas.component';\nimport {SharedModule} from \"../shared/shared.module\";\nimport { HttpUtilsService } from '../../modules/services/http-utils.service';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: LayoutComponent,\n    children: Routing,\n  },\n];\n\n@NgModule({\n  declarations: [\n    LayoutComponent,\n    HeaderComponent,\n    ContentComponent,\n    FooterComponent,\n    ScriptsInitComponent,\n    ToolbarComponent,\n    TopbarComponent,\n    PageTitleComponent,\n    HeaderMenuComponent,\n    EngagesComponent,\n    SidebarComponent,\n    SidebarLogoComponent,\n    SidebarMenuComponent,\n    SidebarFooterComponent,\n    NavbarComponent,\n    AccountingComponent,\n    ClassicComponent,\n    ExtendedComponent,\n    ReportsComponent,\n    SaasComponent,\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    TranslationModule,\n    InlineSVGModule,\n    NgbDropdownModule,\n    NgbProgressbarModule,\n    ExtrasModule,\n    ModalsModule,\n    DrawersModule,\n    EngagesModule,\n    DropdownMenusModule,\n    NgbTooltipModule,\n    TranslateModule,\n    ThemeModeModule,\n    SharedModule\n  ],\n  providers: [HttpUtilsService],\n  exports: [RouterModule],\n})\nexport class LayoutModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SACEC,iBAAiB,EACjBC,oBAAoB,EACpBC,gBAAgB,QACX,4BAA4B;AACnC,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,YAAY,QAAQ,yCAAyC;AACtE,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SACEC,aAAa,EACbC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,QACR,aAAa;AACpB,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,eAAe,QAAQ,0DAA0D;AAC1F,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,oBAAoB,QAAQ,0DAA0D;AAC/F,SAASC,oBAAoB,QAAQ,0DAA0D;AAC/F,SAASC,sBAAsB,QAAQ,8DAA8D;AACrG,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,mBAAmB,QAAQ,sDAAsD;AAC1F,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,aAAa,QAAQ,0CAA0C;AACxE,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAASC,gBAAgB,QAAQ,2CAA2C;;;;;;;;;;AAE5E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAE/B,eAAe;EAC1BgC,QAAQ,EAAE9B;CACX,CACF;AA6CD,OAAM,MAAO+B,YAAY;;qCAAZA,YAAY;EAAA;;UAAZA;EAAY;;eAHZ,CAACL,gBAAgB,CAAC;IAAAM,OAAA,GAhB3B1C,YAAY,EACZE,YAAY,CAACyC,QAAQ,CAACN,MAAM,CAAC,EAC7B9B,iBAAiB,EACjBN,eAAe,EACfE,iBAAiB,EACjBC,oBAAoB,EACpBK,YAAY,EACZY,YAAY,EACZF,aAAa,EACbG,aAAa,EACbF,mBAAmB,EACnBf,gBAAgB,EAChBC,eAAe,EACfkB,eAAe,EACfW,YAAY,EAGJjC,YAAY;EAAA;;;2EAEXuC,YAAY;IAAAG,YAAA,GAzCrBpC,eAAe,EACfG,eAAe,EACfC,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,gBAAgB,EAChBC,eAAe,EACfC,kBAAkB,EAClBC,mBAAmB,EACnBK,gBAAgB,EAChBE,gBAAgB,EAChBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,eAAe,EACfC,mBAAmB,EACnBC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa;IAAAQ,OAAA,GAGb1C,YAAY,EAAA6C,EAAA,CAAA3C,YAAA,EAEZK,iBAAiB,EACjBN,eAAe,EACfE,iBAAiB,EACjBC,oBAAoB,EACpBK,YAAY,EACZY,YAAY,EACZF,aAAa,EACbG,aAAa,EACbF,mBAAmB,EACnBf,gBAAgB,EAChBC,eAAe,EACfkB,eAAe,EACfW,YAAY;IAAAW,OAAA,GAGJ5C,YAAY;EAAA;AAAA;uBAjCpBc,eAAe;EAAA,QAAA+B,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAC,EAAA,CAAAC,2BAAA,EAAAC,EAAA,CAAAC,wBAAA,EAAAC,EAAA,CAAAC,0BAAA,EAAAC,EAAA,CAAAC,kBAAA,EAAAC,EAAA,CAAAC,0BAAA,EAAAC,EAAA,CAAAC,iBAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}