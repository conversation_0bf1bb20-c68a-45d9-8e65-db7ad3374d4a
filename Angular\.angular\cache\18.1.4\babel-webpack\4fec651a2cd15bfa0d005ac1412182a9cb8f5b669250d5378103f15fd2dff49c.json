{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport * as _ from 'lodash';\nimport { each } from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/http-utils.service\";\nimport * as i3 from \"../../services/custom-layout.utils.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"../../services/user.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../shared/checkbox-group/checkbox.component\";\nimport * as i9 from \"../../shared/checkbox-group/checkbox-group.component\";\nfunction RoleEditComponent_ng_container_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Role - \", ctx_r0.roleName, \"\");\n  }\n}\nfunction RoleEditComponent_ng_container_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1, \"Add Role\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RoleEditComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, RoleEditComponent_ng_container_3_div_1_Template, 2, 1, \"div\", 27)(2, RoleEditComponent_ng_container_3_div_2_Template, 2, 0, \"div\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id !== 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id === 0);\n  }\n}\nfunction RoleEditComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RoleEditComponent_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30)(2, \"label\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 30)(5, \"checkbox-group\", 32);\n    i0.ɵɵelement(6, \"checkbox\", 33)(7, \"checkbox\", 34)(8, \"checkbox\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const p_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(p_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formControlName\", p_r2);\n  }\n}\nexport class RoleEditComponent {\n  modal;\n  cdr;\n  httpUtilService;\n  layoutUtilService;\n  fb;\n  appService;\n  UserService;\n  id; // RolePID -input from the role component\n  permissions; // Permissions - input from the role component\n  passEntry = new EventEmitter(); // output tp the role component\n  editroles; //Initialize the form\n  role = {}; // field to save the role details\n  permissionArray = []; // field to save the initial values of default permissions\n  perNameArray = []; // field to save the Name's  of the Permissions in a array\n  rolePermissions = []; // field to save the final values of the Permissions for the role\n  Permissions = []; //store permission array\n  loginUser = {}; //store local storage data of logged in user\n  selectedpermission = []; //store permission array based on role name changes\n  roleName = '';\n  constructor(modal, cdr, httpUtilService, layoutUtilService, fb, appService, UserService) {\n    this.modal = modal;\n    this.cdr = cdr;\n    this.httpUtilService = httpUtilService;\n    this.layoutUtilService = layoutUtilService;\n    this.fb = fb;\n    this.appService = appService;\n    this.UserService = UserService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.permissionArray = this.permissions; // get the initial values of the default permissions\n    this.loadForm(); // load the form variables\n    if (this.id !== 0) {\n      //get the Role details and patch the values to the form fields\n      this.patchForm();\n    }\n  }\n  /**\n  * Form initialization\n  * Default params, validators\n  */\n  loadForm() {\n    const formGroup = {};\n    //assign the form fields\n    formGroup['roleName'] = new FormControl('', Validators.compose([Validators.required]));\n    // formGroup['systemRole'] = new FormControl(null,Validators.compose([Validators.required]));\n    formGroup['description'] = new FormControl('');\n    // formGroup['status'] = new FormControl('');\n    let pArray = [];\n    // assign the form fields for Permissions\n    this.permissionArray.forEach(perm => {\n      pArray.push(perm.Name);\n      formGroup[perm.Name] = new FormControl('');\n    });\n    //Group the form field and assign it to the form group\n    this.editroles = this.fb.group(formGroup);\n    // field to display the Permission in UI\n    this.perNameArray = pArray;\n  }\n  //function to patch field from API\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.getRole({\n      roleId: this.id\n    }).subscribe(role => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!role.isFault) {\n        let rPerms = JSON.parse(role.responseData.rolePermissions);\n        this.roleName = role.responseData.roleName;\n        this.editroles.patchValue({\n          roleName: role.responseData.roleName,\n          description: role.responseData.description\n          // systemRole:role.responseData.DefaultRoleId,\n          // status:role.responseData.status\n        });\n        // patch the permission values\n        let self = this;\n        each(rPerms, r => {\n          _.forEach(r, function (value, key) {\n            self.editroles.patchValue({\n              [key]: value\n            });\n          });\n        });\n      }\n    });\n  }\n  //function to change permission array based on role name changes\n  changeSystemAccess(event) {\n    this.selectedpermission = JSON.parse(event.Permissions);\n    let perArray = [];\n    let self = this;\n    each(this.selectedpermission, r => {\n      _.forEach(r, function (value, key) {\n        self.editroles.patchValue({\n          [key]: value\n        });\n      });\n    });\n  }\n  //Form submit\n  save() {\n    let roleData = this.prepareRole();\n    console.log('roleData ', roleData);\n    const controls = this.editroles.controls;\n    if (this.editroles.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      this.layoutUtilService.showError('Please fill all required fields', '');\n      return;\n    }\n    if (this.id === 0) {\n      this.create(roleData);\n    } else {\n      this.edit(roleData);\n    }\n  }\n  // API to update the role details based on the RolePID\n  edit(roleData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.editRole(roleData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n      }\n    });\n  }\n  // API to save new role details\n  create(roleData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.addRole(roleData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n      }\n    });\n  }\n  //function to map input form fields to API fields\n  prepareRole() {\n    const formData = this.editroles.value;\n    this.role.roleId = this.id;\n    // this.role.DefaultRoleId = formData.systemRole;\n    // this.role.DefaultRoleId = '';\n    this.role.roleName = formData.roleName;\n    this.role.description = formData.description;\n    this.role.loggedInUserId = this.loginUser.userId;\n    this.role.status = 'Active';\n    let controls = this.editroles.controls;\n    let perArray = [];\n    let self = this;\n    console.log('permissionArray ', self.permissionArray);\n    _.forEach(controls, function (value, key) {\n      console.log('valeu ', value, 'Key ', key);\n      let rjson = _.find(self.permissionArray, function (o) {\n        return o.Name === key;\n      });\n      if (rjson !== undefined) {\n        let permissionJson = self.getPermissionJson(rjson, value);\n        perArray.push(permissionJson);\n      }\n    });\n    this.role.rolePermissions = perArray;\n    return this.role;\n  }\n  // format the permission data for each permission\n  getPermissionJson(permission, controls) {\n    let newPermission = {\n      [permission.Name]: controls.value\n    };\n    return newPermission;\n  }\n  // Method to handle modal dismiss and reset loading state\n  dismissModal() {\n    this.httpUtilService.loadingSubject.next(false);\n    this.modal.dismiss();\n  }\n  static ɵfac = function RoleEditComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RoleEditComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.HttpUtilsService), i0.ɵɵdirectiveInject(i3.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.AppService), i0.ɵɵdirectiveInject(i6.UserService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RoleEditComponent,\n    selectors: [[\"app-role-edit\"]],\n    inputs: {\n      id: \"id\",\n      permissions: \"permissions\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 47,\n    vars: 5,\n    consts: [[1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"example-modal-sizes-title-lg\", 1, \"modal-title\"], [4, \"ngIf\"], [1, \"float-right\", \"cursor-pointer\", \"ir-12\"], [1, \"btn\", \"btn-icon\", \"btn-sm\", \"mx-1\", 3, \"click\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\"], [1, \"modal-body\", \"large-modal-body\"], [1, \"overlay-layer\", \"bg-transparent\"], [1, \"spinner\", \"spinner-lg\", \"spinner-success\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"card-body\", \"response-list\"], [1, \"form-group\", \"row\", \"mb-4\", \"px-10\", \"pt-2\"], [1, \"col-lg-12\"], [1, \"fw-semibold\", \"fs-6\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"name\", \"roleName\", \"placeholder\", \"Type Here\", \"autocomplete\", \"off\", \"formControlName\", \"roleName\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"custom-error-css\", 4, \"ngIf\"], [\"type\", \"text\", \"name\", \"description\", \"placeholder\", \"Type Here\", \"rows\", \"2\", \"autocomplete\", \"off\", \"formControlName\", \"description\", 1, \"form-control\"], [1, \"col-12\", \"d-flex\", \"justify-content-start\"], [1, \"w-100\"], [1, \"p-1\", \"fw-bold\", \"fs-6\", 2, \"width\", \"390px !important\"], [1, \"d-flex\", \"justify-content-between\"], [4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-elevate\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"fw-bold fs-2 text-white\", 4, \"ngIf\"], [1, \"fw-bold\", \"fs-2\", \"text-white\"], [1, \"custom-error-css\"], [1, \"p-1\"], [1, \"fw-semibold\", \"fs-6\"], [3, \"formControlName\"], [\"value\", \"Read\"], [\"value\", \"Write\", 2, \"padding-left\", \"145px\"], [\"value\", \"Delete\", 2, \"padding-left\", \"145px\"]],\n    template: function RoleEditComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵtemplate(3, RoleEditComponent_ng_container_3_Template, 3, 2, \"ng-container\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"a\", 5);\n        i0.ɵɵlistener(\"click\", function RoleEditComponent_Template_a_click_5_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵelement(6, \"i\", 6);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 7);\n        i0.ɵɵelementContainerStart(8);\n        i0.ɵɵelementStart(9, \"div\", 8);\n        i0.ɵɵelement(10, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(11);\n        i0.ɵɵelementStart(12, \"form\", 10)(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"label\", 14);\n        i0.ɵɵtext(17, \"Name\");\n        i0.ɵɵelementStart(18, \"sup\", 15);\n        i0.ɵɵtext(19, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(20, \"input\", 16);\n        i0.ɵɵtemplate(21, RoleEditComponent_span_21_Template, 2, 0, \"span\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 12)(23, \"div\", 13)(24, \"label\", 14);\n        i0.ɵɵtext(25, \"Description\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(26, \"textarea\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 12)(28, \"div\", 19)(29, \"table\", 20)(30, \"th\", 21);\n        i0.ɵɵtext(31, \"Permissions\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"th\", 21)(33, \"div\", 22)(34, \"span\");\n        i0.ɵɵtext(35, \"Read\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"span\");\n        i0.ɵɵtext(37, \"Write\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"span\");\n        i0.ɵɵtext(39, \"Delete\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(40, RoleEditComponent_tr_40_Template, 9, 2, \"tr\", 23);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"div\", 24)(42, \"button\", 25);\n        i0.ɵɵlistener(\"click\", function RoleEditComponent_Template_button_click_42_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵtext(43, \"Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerStart(44);\n        i0.ɵɵelementStart(45, \"button\", 26);\n        i0.ɵɵlistener(\"click\", function RoleEditComponent_Template_button_click_45_listener() {\n          return ctx.save();\n        });\n        i0.ɵɵtext(46, \" Save\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.role);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"formGroup\", ctx.editroles);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.appService.controlHasError(\"required\", \"roleName\", ctx.editroles));\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngForOf\", ctx.perNameArray);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", ctx.editroles.invalid);\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i8.CheckboxComponent, i9.CheckboxGroupComponent],\n    styles: [\"ng-select[_ngcontent-%COMP%]   .custom[_ngcontent-%COMP%] {\\n  height: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9zZXR0aW5nL3JvbGUtZWRpdC9yb2xlLWVkaXQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxZQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyJuZy1zZWxlY3QgLmN1c3RvbSB7XHJcbiAgICBoZWlnaHQ6IDIwcHg7XHJcbiAgfVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "Validators", "_", "each", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "<PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "ɵɵtemplate", "RoleEditComponent_ng_container_3_div_1_Template", "RoleEditComponent_ng_container_3_div_2_Template", "ɵɵproperty", "id", "ɵɵelement", "ɵɵtextInterpolate", "p_r2", "RoleEditComponent", "modal", "cdr", "httpUtilService", "layoutUtilService", "fb", "appService", "UserService", "permissions", "passEntry", "editroles", "role", "permissionArray", "perNameArray", "rolePermissions", "Permissions", "loginUser", "selectedpermission", "constructor", "ngOnInit", "getLoggedInUser", "loadForm", "patchForm", "formGroup", "compose", "required", "p<PERSON><PERSON>y", "for<PERSON>ach", "perm", "push", "Name", "group", "loadingSubject", "next", "getRole", "roleId", "subscribe", "<PERSON><PERSON><PERSON>", "rPerms", "JSON", "parse", "responseData", "patchValue", "description", "self", "r", "value", "key", "changeSystemAccess", "event", "perArray", "save", "roleData", "prepareRole", "console", "log", "controls", "invalid", "Object", "keys", "controlName", "<PERSON><PERSON><PERSON><PERSON>ched", "showError", "create", "edit", "editRole", "res", "showSuccess", "message", "emit", "close", "addRole", "formData", "loggedInUserId", "userId", "status", "r<PERSON><PERSON>", "find", "o", "undefined", "<PERSON><PERSON><PERSON>", "getPermission<PERSON>son", "permission", "newPermission", "dismissModal", "dismiss", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "ChangeDetectorRef", "i2", "HttpUtilsService", "i3", "CustomLayoutUtilsService", "i4", "FormBuilder", "i5", "AppService", "i6", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "RoleEditComponent_Template", "rf", "ctx", "RoleEditComponent_ng_container_3_Template", "ɵɵlistener", "RoleEditComponent_Template_a_click_5_listener", "RoleEditComponent_span_21_Template", "RoleEditComponent_tr_40_Template", "RoleEditComponent_Template_button_click_42_listener", "RoleEditComponent_Template_button_click_45_listener", "controlHasError"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\role-edit\\role-edit.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\role-edit\\role-edit.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport * as _ from 'lodash';\r\nimport { each } from 'lodash';\r\nimport { AppService } from '../../services/app.service';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { UserService } from '../../services/user.service';\r\n\r\n@Component({\r\n  selector: 'app-role-edit',\r\n  templateUrl: './role-edit.component.html',\r\n  styleUrls: ['./role-edit.component.scss']\r\n})\r\nexport class RoleEditComponent {\r\n @Input() id: number; // RolePID -input from the role component\r\n  @Input() permissions: any;// Permissions - input from the role component\r\n  @Output() passEntry: EventEmitter<any> = new EventEmitter(); // output tp the role component\r\n  editroles: FormGroup; //Initialize the form\r\n  role: any = {}; // field to save the role details\r\n  permissionArray: any = [];// field to save the initial values of default permissions\r\n  perNameArray: any = [];// field to save the Name's  of the Permissions in a array\r\n  rolePermissions: any = [];// field to save the final values of the Permissions for the role\r\n  Permissions: any = []; //store permission array\r\n  loginUser:any={}; //store local storage data of logged in user\r\n  selectedpermission:any=[];//store permission array based on role name changes\r\n  roleName:any ='';\r\n  constructor(public modal: NgbActiveModal,\r\n    private cdr:ChangeDetectorRef,\r\n    private httpUtilService:HttpUtilsService,\r\n    private layoutUtilService:CustomLayoutUtilsService,\r\n    private fb: FormBuilder,\r\n    public appService:AppService,\r\n    private UserService:UserService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser()\r\n    this.permissionArray = this.permissions; // get the initial values of the default permissions\r\n    this.loadForm(); // load the form variables\r\n    if (this.id !== 0) {\r\n      //get the Role details and patch the values to the form fields\r\n      this.patchForm();\r\n    }\r\n  }\r\n\r\n    /**\r\n   * Form initialization\r\n   * Default params, validators\r\n   */\r\n\r\n   loadForm() {\r\n    const formGroup: any = {};\r\n    //assign the form fields\r\n    formGroup['roleName'] = new FormControl('', Validators.compose([Validators.required]));\r\n    // formGroup['systemRole'] = new FormControl(null,Validators.compose([Validators.required]));\r\n    formGroup['description'] = new FormControl('');\r\n    // formGroup['status'] = new FormControl('');\r\n\r\n    let pArray: any = [];\r\n    // assign the form fields for Permissions\r\n    this.permissionArray.forEach((perm: any) => {\r\n      pArray.push(perm.Name);\r\n      formGroup[perm.Name] = new FormControl('');\r\n    });\r\n    //Group the form field and assign it to the form group\r\n    this.editroles = this.fb.group(formGroup);\r\n    // field to display the Permission in UI\r\n    this.perNameArray = pArray;\r\n  }\r\n  //function to patch field from API\r\n  patchForm() {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.UserService.getRole({ roleId: this.id }).subscribe((role: any) => {\r\n      this.httpUtilService.loadingSubject.next(false);\r\n      if (!role.isFault) {\r\n        let rPerms = JSON.parse(role.responseData.rolePermissions);\r\n        this.roleName = role.responseData.roleName\r\n        this.editroles.patchValue({\r\n          roleName: role.responseData.roleName,\r\n          description: role.responseData.description,\r\n          // systemRole:role.responseData.DefaultRoleId,\r\n          // status:role.responseData.status\r\n        });\r\n\r\n        // patch the permission values\r\n       let self: any = this;\r\n        each(rPerms, (r) => {\r\n           _.forEach(r, function (value, key) {\r\n             self.editroles.patchValue({\r\n              [key]: value,\r\n            });\r\n\r\n          });\r\n        });\r\n      }\r\n    });\r\n\r\n  }\r\n  //function to change permission array based on role name changes\r\n  changeSystemAccess(event:any){\r\n    this.selectedpermission  = JSON.parse(event.Permissions);\r\n\r\n    let perArray: any = [];\r\n    let self: any = this;\r\n    each(this.selectedpermission, (r) => {\r\n      _.forEach(r, function (value, key) {\r\n        self.editroles.patchValue({\r\n          [key]: value,\r\n        });\r\n      });\r\n    });\r\n\r\n  }\r\n\r\n  //Form submit\r\n  save(){\r\n    let roleData: any = this.prepareRole();\r\n    console.log('roleData ', roleData)\r\n    const controls = this.editroles.controls;\r\n    if (this.editroles.invalid) {\r\n      Object.keys(controls).forEach(controlName =>\r\n        controls[controlName].markAsTouched()\r\n      );\r\n      this.layoutUtilService.showError('Please fill all required fields', '');\r\n      return;\r\n    }\r\n    if (this.id === 0) {\r\n      this.create(roleData);\r\n    } else {\r\n      this.edit(roleData);\r\n    }\r\n  }\r\n   // API to update the role details based on the RolePID\r\n   edit(roleData: any) {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.UserService.editRole(roleData).subscribe((res:any) => {\r\n      this.httpUtilService.loadingSubject.next(false);\r\n      if (!res.isFault) {\r\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\r\n        this.passEntry.emit(true);\r\n        this.modal.close()\r\n      } else {\r\n        this.layoutUtilService.showError(res.responseData.message, '');\r\n      }\r\n    });\r\n\r\n  }\r\n // API to save new role details\r\n  create(roleData: any) {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.UserService.addRole(roleData).subscribe((res:any) => {\r\n      this.httpUtilService.loadingSubject.next(false);\r\n      if (!res.isFault) {\r\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\r\n        this.passEntry.emit(true);\r\n        this.modal.close()\r\n      } else {\r\n        this.layoutUtilService.showError(res.responseData.message, '');\r\n      }\r\n    });\r\n\r\n  }\r\n  //function to map input form fields to API fields\r\n  prepareRole() {\r\n    const formData = this.editroles.value;\r\n    this.role.roleId = this.id;\r\n    // this.role.DefaultRoleId = formData.systemRole;\r\n    // this.role.DefaultRoleId = '';\r\n    this.role.roleName = formData.roleName;\r\n    this.role.description = formData.description;\r\n    this.role.loggedInUserId= this.loginUser.userId;\r\n    this.role.status= 'Active';\r\n    let controls = this.editroles.controls;\r\n\r\n    let perArray: any = [];\r\n    let self: any = this;\r\n    console.log('permissionArray ', self.permissionArray)\r\n    _.forEach(controls, function (value, key) {\r\n      console.log('valeu ', value, 'Key ',key);\r\n      let rjson = _.find(self.permissionArray, function (o) {\r\n        return o.Name === key;\r\n      });\r\n      if (rjson !== undefined) {\r\n        let permissionJson = self.getPermissionJson(rjson, value);\r\n        perArray.push(permissionJson);\r\n      }\r\n    });\r\n    this.role.rolePermissions = perArray;\r\n    return this.role;\r\n  }\r\n\r\n   // format the permission data for each permission\r\n   getPermissionJson(permission: any, controls: any) {\r\n    let newPermission = {\r\n      [permission.Name]:controls.value,\r\n    };\r\n    return newPermission;\r\n  }\r\n\r\n  // Method to handle modal dismiss and reset loading state\r\n  dismissModal() {\r\n    this.httpUtilService.loadingSubject.next(false);\r\n    this.modal.dismiss();\r\n  }\r\n}\r\n", "<div class=\"modal-content\">\r\n    <div class=\"modal-header\">\r\n        <div class=\"modal-title\" id=\"example-modal-sizes-title-lg\">\r\n            <ng-container *ngIf=\"role\">\r\n                <div class=\"fw-bold fs-2 text-white\" *ngIf=\"id !==0\">Edit Role - {{roleName}}</div>\r\n                <div class=\"fw-bold fs-2 text-white\" *ngIf=\"id===0\">Add Role</div>\r\n            </ng-container>\r\n        </div>\r\n        <div class=\"float-right cursor-pointer ir-12 \" >\r\n            <a class=\"btn btn-icon  btn-sm mx-1\" (click)=\"dismissModal()\">\r\n              <i class=\"fa-solid fs-2 fa-xmark text-white\"></i>\r\n            </a>\r\n          </div>\r\n    </div>\r\n    <div class=\"modal-body large-modal-body\">\r\n        <ng-container>\r\n            <div class=\"overlay-layer bg-transparent\">\r\n                <div class=\"spinner spinner-lg spinner-success\"></div>\r\n            </div>\r\n        </ng-container>\r\n        <ng-container>\r\n            <form class=\"form form-label-right\" [formGroup]=\"editroles\">\r\n                <div class=\"card-body response-list\">\r\n                    <div class=\"form-group row mb-4 px-10 pt-2\">\r\n                        <div class=\"col-lg-12\">\r\n                            <label class=\"fw-semibold fs-6 mb-2\">Name<sup class=\"text-danger\">*</sup></label>\r\n                            <input type=\"text\" class=\"form-control form-control-sm\" name=\"roleName\" placeholder=\"Type Here\" autocomplete=\"off\" formControlName=\"roleName\" />\r\n                            <span class=\"custom-error-css\" *ngIf=\"appService.controlHasError('required', 'roleName',editroles)\">Required Field</span>\r\n                        </div>\r\n                        <!-- <div class=\"col-lg-6\">\r\n                            <label class=\"fw-semibold fs-6 mb-2\">System Role<sup class=\"text-danger\">*</sup></label>\r\n                            <ng-select [items]=\"systemRoles\" (change)=\"changeSystemAccess($event)\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"RoleName\"  bindValue=\"RoleId\" formControlName=\"systemRole\"\r\n                              placeholder=\"Select an option\" class=\"custom\">\r\n                            </ng-select>\r\n                            <span class=\"custom-error-css\" *ngIf=\"appService.controlHasError('required', 'systemRole',editroles)\">Required Field</span>\r\n                        </div> -->\r\n                    </div>\r\n                    <div class=\"form-group row mb-4 px-10 pt-2\">\r\n                        <div class=\"col-lg-12\">\r\n                            <label class=\"fw-semibold fs-6 mb-2\">Description</label>\r\n                            <textarea type=\"text\" class=\"form-control\" name=\"description\" placeholder=\"Type Here\" rows=\"2\"\r\n                              autocomplete=\"off\"  formControlName=\"description\"></textarea>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"form-group row mb-4 px-10 pt-2\">\r\n                        <div class=\"col-12 d-flex justify-content-start\">\r\n                            <table class=\"w-100\">\r\n                                <th class=\"p-1 fw-bold fs-6\" style=\"width:390px !important;\">Permissions</th>\r\n                                <th class=\"p-1 fw-bold fs-6\" style=\"width:390px !important;\">\r\n                                    <div class=\"d-flex justify-content-between\">\r\n                                        <span>Read</span>\r\n                                        <span>Write</span>\r\n                                        <span>Delete</span>\r\n                                    </div>\r\n                                </th>\r\n                                <tr *ngFor=\"let p of perNameArray\">\r\n                                    <td class=\"p-1\">\r\n                                      <label class=\"fw-semibold fs-6\">{{p}}</label>\r\n                                    </td>\r\n                                    <td class=\"p-1\">\r\n                                      <checkbox-group [formControlName]=\"p\">\r\n                                        <checkbox value=\"Read\"></checkbox>\r\n                                        <checkbox style=\"padding-left:145px\" value=\"Write\"></checkbox>\r\n                                        <checkbox style=\"padding-left:145px\" value=\"Delete\"></checkbox>\r\n                                      </checkbox-group>\r\n                                    </td>\r\n                                  </tr>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </form>\r\n        </ng-container>\r\n    </div>\r\n    <div class=\"modal-footer\">\r\n        <button type=\"button\" class=\"btn btn-secondary btn-sm btn-elevate mr-2\" (click)=\"dismissModal()\">Cancel</button>\r\n        <ng-container>\r\n            <button type=\"button\" class=\"btn btn-primary btn-elevate btn-sm\" (click)=\"save()\" [disabled]=\"editroles.invalid\">\r\n                Save</button>\r\n        </ng-container>\r\n    </div>\r\n</div>\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAAuCA,YAAY,QAAuB,eAAe;AACzF,SAAiCC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAEhF,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAC3B,SAASC,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;ICAbC,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA9BH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAK,kBAAA,iBAAAC,MAAA,CAAAC,QAAA,KAAwB;;;;;IAC7EP,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFtEH,EAAA,CAAAQ,uBAAA,GAA2B;IAEvBR,EADA,CAAAS,UAAA,IAAAC,+CAAA,kBAAqD,IAAAC,+CAAA,kBACD;;;;;IADdX,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAO,EAAA,OAAa;IACbb,EAAA,CAAAI,SAAA,EAAY;IAAZJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAO,EAAA,OAAY;;;;;IAsBtCb,EAAA,CAAAC,cAAA,eAAoG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA+B/GH,EAFN,CAAAC,cAAA,SAAmC,aACf,gBACkB;IAAAD,EAAA,CAAAE,MAAA,GAAK;IACvCF,EADuC,CAAAG,YAAA,EAAQ,EAC1C;IAEHH,EADF,CAAAC,cAAA,aAAgB,yBACwB;IAGpCD,EAFA,CAAAc,SAAA,mBAAkC,mBAC4B,mBACC;IAGrEd,EAFI,CAAAG,YAAA,EAAiB,EACd,EACF;;;;IAT+BH,EAAA,CAAAI,SAAA,GAAK;IAALJ,EAAA,CAAAe,iBAAA,CAAAC,IAAA,CAAK;IAGrBhB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAY,UAAA,oBAAAI,IAAA,CAAqB;;;AD9C3E,OAAM,MAAOC,iBAAiB;EAaTC,KAAA;EACTC,GAAA;EACAC,eAAA;EACAC,iBAAA;EACAC,EAAA;EACDC,UAAA;EACCC,WAAA;EAlBFX,EAAE,CAAS,CAAC;EACXY,WAAW,CAAM;EAChBC,SAAS,GAAsB,IAAI/B,YAAY,EAAE,CAAC,CAAC;EAC7DgC,SAAS,CAAY,CAAC;EACtBC,IAAI,GAAQ,EAAE,CAAC,CAAC;EAChBC,eAAe,GAAQ,EAAE,CAAC;EAC1BC,YAAY,GAAQ,EAAE,CAAC;EACvBC,eAAe,GAAQ,EAAE,CAAC;EAC1BC,WAAW,GAAQ,EAAE,CAAC,CAAC;EACvBC,SAAS,GAAK,EAAE,CAAC,CAAC;EAClBC,kBAAkB,GAAK,EAAE,CAAC;EAC1B3B,QAAQ,GAAM,EAAE;EAChB4B,YAAmBjB,KAAqB,EAC9BC,GAAqB,EACrBC,eAAgC,EAChCC,iBAA0C,EAC1CC,EAAe,EAChBC,UAAqB,EACpBC,WAAuB;IANd,KAAAN,KAAK,GAALA,KAAK;IACd,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,UAAU,GAAVA,UAAU;IACT,KAAAC,WAAW,GAAXA,WAAW;EAAgB;EAErCY,QAAQA,CAAA;IACN,IAAI,CAACH,SAAS,GAAG,IAAI,CAACV,UAAU,CAACc,eAAe,EAAE;IAClD,IAAI,CAACR,eAAe,GAAG,IAAI,CAACJ,WAAW,CAAC,CAAC;IACzC,IAAI,CAACa,QAAQ,EAAE,CAAC,CAAC;IACjB,IAAI,IAAI,CAACzB,EAAE,KAAK,CAAC,EAAE;MACjB;MACA,IAAI,CAAC0B,SAAS,EAAE;IAClB;EACF;EAEE;;;;EAKDD,QAAQA,CAAA;IACP,MAAME,SAAS,GAAQ,EAAE;IACzB;IACAA,SAAS,CAAC,UAAU,CAAC,GAAG,IAAI5C,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC4C,OAAO,CAAC,CAAC5C,UAAU,CAAC6C,QAAQ,CAAC,CAAC,CAAC;IACtF;IACAF,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI5C,WAAW,CAAC,EAAE,CAAC;IAC9C;IAEA,IAAI+C,MAAM,GAAQ,EAAE;IACpB;IACA,IAAI,CAACd,eAAe,CAACe,OAAO,CAAEC,IAAS,IAAI;MACzCF,MAAM,CAACG,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC;MACtBP,SAAS,CAACK,IAAI,CAACE,IAAI,CAAC,GAAG,IAAInD,WAAW,CAAC,EAAE,CAAC;IAC5C,CAAC,CAAC;IACF;IACA,IAAI,CAAC+B,SAAS,GAAG,IAAI,CAACL,EAAE,CAAC0B,KAAK,CAACR,SAAS,CAAC;IACzC;IACA,IAAI,CAACV,YAAY,GAAGa,MAAM;EAC5B;EACA;EACAJ,SAASA,CAAA;IACP,IAAI,CAACnB,eAAe,CAAC6B,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC1B,WAAW,CAAC2B,OAAO,CAAC;MAAEC,MAAM,EAAE,IAAI,CAACvC;IAAE,CAAE,CAAC,CAACwC,SAAS,CAAEzB,IAAS,IAAI;MACpE,IAAI,CAACR,eAAe,CAAC6B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACtB,IAAI,CAAC0B,OAAO,EAAE;QACjB,IAAIC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC7B,IAAI,CAAC8B,YAAY,CAAC3B,eAAe,CAAC;QAC1D,IAAI,CAACxB,QAAQ,GAAGqB,IAAI,CAAC8B,YAAY,CAACnD,QAAQ;QAC1C,IAAI,CAACoB,SAAS,CAACgC,UAAU,CAAC;UACxBpD,QAAQ,EAAEqB,IAAI,CAAC8B,YAAY,CAACnD,QAAQ;UACpCqD,WAAW,EAAEhC,IAAI,CAAC8B,YAAY,CAACE;UAC/B;UACA;SACD,CAAC;QAEF;QACD,IAAIC,IAAI,GAAQ,IAAI;QACnB9D,IAAI,CAACwD,MAAM,EAAGO,CAAC,IAAI;UAChBhE,CAAC,CAAC8C,OAAO,CAACkB,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG;YAC/BH,IAAI,CAAClC,SAAS,CAACgC,UAAU,CAAC;cACzB,CAACK,GAAG,GAAGD;aACR,CAAC;UAEJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EAEJ;EACA;EACAE,kBAAkBA,CAACC,KAAS;IAC1B,IAAI,CAAChC,kBAAkB,GAAIsB,IAAI,CAACC,KAAK,CAACS,KAAK,CAAClC,WAAW,CAAC;IAExD,IAAImC,QAAQ,GAAQ,EAAE;IACtB,IAAIN,IAAI,GAAQ,IAAI;IACpB9D,IAAI,CAAC,IAAI,CAACmC,kBAAkB,EAAG4B,CAAC,IAAI;MAClChE,CAAC,CAAC8C,OAAO,CAACkB,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG;QAC/BH,IAAI,CAAClC,SAAS,CAACgC,UAAU,CAAC;UACxB,CAACK,GAAG,GAAGD;SACR,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EAEJ;EAEA;EACAK,IAAIA,CAAA;IACF,IAAIC,QAAQ,GAAQ,IAAI,CAACC,WAAW,EAAE;IACtCC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEH,QAAQ,CAAC;IAClC,MAAMI,QAAQ,GAAG,IAAI,CAAC9C,SAAS,CAAC8C,QAAQ;IACxC,IAAI,IAAI,CAAC9C,SAAS,CAAC+C,OAAO,EAAE;MAC1BC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAAC7B,OAAO,CAACiC,WAAW,IACvCJ,QAAQ,CAACI,WAAW,CAAC,CAACC,aAAa,EAAE,CACtC;MACD,IAAI,CAACzD,iBAAiB,CAAC0D,SAAS,CAAC,iCAAiC,EAAE,EAAE,CAAC;MACvE;IACF;IACA,IAAI,IAAI,CAAClE,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAACmE,MAAM,CAACX,QAAQ,CAAC;IACvB,CAAC,MAAM;MACL,IAAI,CAACY,IAAI,CAACZ,QAAQ,CAAC;IACrB;EACF;EACC;EACAY,IAAIA,CAACZ,QAAa;IACjB,IAAI,CAACjD,eAAe,CAAC6B,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC1B,WAAW,CAAC0D,QAAQ,CAACb,QAAQ,CAAC,CAAChB,SAAS,CAAE8B,GAAO,IAAI;MACxD,IAAI,CAAC/D,eAAe,CAAC6B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACiC,GAAG,CAAC7B,OAAO,EAAE;QAChB,IAAI,CAACjC,iBAAiB,CAAC+D,WAAW,CAACD,GAAG,CAACzB,YAAY,CAAC2B,OAAO,EAAE,EAAE,CAAC;QAChE,IAAI,CAAC3D,SAAS,CAAC4D,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACpE,KAAK,CAACqE,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAAClE,iBAAiB,CAAC0D,SAAS,CAACI,GAAG,CAACzB,YAAY,CAAC2B,OAAO,EAAE,EAAE,CAAC;MAChE;IACF,CAAC,CAAC;EAEJ;EACD;EACCL,MAAMA,CAACX,QAAa;IAClB,IAAI,CAACjD,eAAe,CAAC6B,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC1B,WAAW,CAACgE,OAAO,CAACnB,QAAQ,CAAC,CAAChB,SAAS,CAAE8B,GAAO,IAAI;MACvD,IAAI,CAAC/D,eAAe,CAAC6B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACiC,GAAG,CAAC7B,OAAO,EAAE;QAChB,IAAI,CAACjC,iBAAiB,CAAC+D,WAAW,CAACD,GAAG,CAACzB,YAAY,CAAC2B,OAAO,EAAE,EAAE,CAAC;QAChE,IAAI,CAAC3D,SAAS,CAAC4D,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACpE,KAAK,CAACqE,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAAClE,iBAAiB,CAAC0D,SAAS,CAACI,GAAG,CAACzB,YAAY,CAAC2B,OAAO,EAAE,EAAE,CAAC;MAChE;IACF,CAAC,CAAC;EAEJ;EACA;EACAf,WAAWA,CAAA;IACT,MAAMmB,QAAQ,GAAG,IAAI,CAAC9D,SAAS,CAACoC,KAAK;IACrC,IAAI,CAACnC,IAAI,CAACwB,MAAM,GAAG,IAAI,CAACvC,EAAE;IAC1B;IACA;IACA,IAAI,CAACe,IAAI,CAACrB,QAAQ,GAAGkF,QAAQ,CAAClF,QAAQ;IACtC,IAAI,CAACqB,IAAI,CAACgC,WAAW,GAAG6B,QAAQ,CAAC7B,WAAW;IAC5C,IAAI,CAAChC,IAAI,CAAC8D,cAAc,GAAE,IAAI,CAACzD,SAAS,CAAC0D,MAAM;IAC/C,IAAI,CAAC/D,IAAI,CAACgE,MAAM,GAAE,QAAQ;IAC1B,IAAInB,QAAQ,GAAG,IAAI,CAAC9C,SAAS,CAAC8C,QAAQ;IAEtC,IAAIN,QAAQ,GAAQ,EAAE;IACtB,IAAIN,IAAI,GAAQ,IAAI;IACpBU,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEX,IAAI,CAAChC,eAAe,CAAC;IACrD/B,CAAC,CAAC8C,OAAO,CAAC6B,QAAQ,EAAE,UAAUV,KAAK,EAAEC,GAAG;MACtCO,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAET,KAAK,EAAE,MAAM,EAACC,GAAG,CAAC;MACxC,IAAI6B,KAAK,GAAG/F,CAAC,CAACgG,IAAI,CAACjC,IAAI,CAAChC,eAAe,EAAE,UAAUkE,CAAC;QAClD,OAAOA,CAAC,CAAChD,IAAI,KAAKiB,GAAG;MACvB,CAAC,CAAC;MACF,IAAI6B,KAAK,KAAKG,SAAS,EAAE;QACvB,IAAIC,cAAc,GAAGpC,IAAI,CAACqC,iBAAiB,CAACL,KAAK,EAAE9B,KAAK,CAAC;QACzDI,QAAQ,CAACrB,IAAI,CAACmD,cAAc,CAAC;MAC/B;IACF,CAAC,CAAC;IACF,IAAI,CAACrE,IAAI,CAACG,eAAe,GAAGoC,QAAQ;IACpC,OAAO,IAAI,CAACvC,IAAI;EAClB;EAEC;EACAsE,iBAAiBA,CAACC,UAAe,EAAE1B,QAAa;IAC/C,IAAI2B,aAAa,GAAG;MAClB,CAACD,UAAU,CAACpD,IAAI,GAAE0B,QAAQ,CAACV;KAC5B;IACD,OAAOqC,aAAa;EACtB;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAACjF,eAAe,CAAC6B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;IAC/C,IAAI,CAAChC,KAAK,CAACoF,OAAO,EAAE;EACtB;;qCA7LWrF,iBAAiB,EAAAjB,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzG,EAAA,CAAAuG,iBAAA,CAAAvG,EAAA,CAAA0G,iBAAA,GAAA1G,EAAA,CAAAuG,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAA5G,EAAA,CAAAuG,iBAAA,CAAAM,EAAA,CAAAC,wBAAA,GAAA9G,EAAA,CAAAuG,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAhH,EAAA,CAAAuG,iBAAA,CAAAU,EAAA,CAAAC,UAAA,GAAAlH,EAAA,CAAAuG,iBAAA,CAAAY,EAAA,CAAA3F,WAAA;EAAA;;UAAjBP,iBAAiB;IAAAmG,SAAA;IAAAC,MAAA;MAAAxG,EAAA;MAAAY,WAAA;IAAA;IAAA6F,OAAA;MAAA5F,SAAA;IAAA;IAAA6F,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbtB5H,EAFR,CAAAC,cAAA,aAA2B,aACG,aACqC;QACvDD,EAAA,CAAAS,UAAA,IAAAqH,yCAAA,0BAA2B;QAI/B9H,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,aAAgD,WACkB;QAAzBD,EAAA,CAAA+H,UAAA,mBAAAC,8CAAA;UAAA,OAASH,GAAA,CAAAxB,YAAA,EAAc;QAAA,EAAC;QAC3DrG,EAAA,CAAAc,SAAA,WAAiD;QAG3Dd,EAFQ,CAAAG,YAAA,EAAI,EACA,EACN;QACNH,EAAA,CAAAC,cAAA,aAAyC;QACrCD,EAAA,CAAAQ,uBAAA,GAAc;QACVR,EAAA,CAAAC,cAAA,aAA0C;QACtCD,EAAA,CAAAc,SAAA,cAAsD;QAC1Dd,EAAA,CAAAG,YAAA,EAAM;;QAEVH,EAAA,CAAAQ,uBAAA,IAAc;QAKMR,EAJhB,CAAAC,cAAA,gBAA4D,eACnB,eACW,eACjB,iBACkB;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;QACjFH,EAAA,CAAAc,SAAA,iBAAgJ;QAChJd,EAAA,CAAAS,UAAA,KAAAwH,kCAAA,mBAAoG;QAS5GjI,EARI,CAAAG,YAAA,EAAM,EAQJ;QAGEH,EAFR,CAAAC,cAAA,eAA4C,eACjB,iBACkB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxDH,EAAA,CAAAc,SAAA,oBAC+D;QAEvEd,EADI,CAAAG,YAAA,EAAM,EACJ;QAKMH,EAHZ,CAAAC,cAAA,eAA4C,eACS,iBACxB,cAC4C;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGrEH,EAFR,CAAAC,cAAA,cAA6D,eACb,YAClC;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACjBH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAClBH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAEpBF,EAFoB,CAAAG,YAAA,EAAO,EACjB,EACL;QACLH,EAAA,CAAAS,UAAA,KAAAyH,gCAAA,iBAAmC;QAgBvDlI,EAJgB,CAAAG,YAAA,EAAQ,EACN,EACJ,EACJ,EACH;;QAEfH,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAA0B,kBAC2E;QAAzBD,EAAA,CAAA+H,UAAA,mBAAAI,oDAAA;UAAA,OAASN,GAAA,CAAAxB,YAAA,EAAc;QAAA,EAAC;QAACrG,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAChHH,EAAA,CAAAQ,uBAAA,IAAc;QACVR,EAAA,CAAAC,cAAA,kBAAiH;QAAhDD,EAAA,CAAA+H,UAAA,mBAAAK,oDAAA;UAAA,OAASP,GAAA,CAAAzD,IAAA,EAAM;QAAA,EAAC;QAC7EpE,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAS;;QAG7BH,EADI,CAAAG,YAAA,EAAM,EACJ;;;QA/EqBH,EAAA,CAAAI,SAAA,GAAU;QAAVJ,EAAA,CAAAY,UAAA,SAAAiH,GAAA,CAAAjG,IAAA,CAAU;QAkBW5B,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAY,UAAA,cAAAiH,GAAA,CAAAlG,SAAA,CAAuB;QAMX3B,EAAA,CAAAI,SAAA,GAAkE;QAAlEJ,EAAA,CAAAY,UAAA,SAAAiH,GAAA,CAAAtG,UAAA,CAAA8G,eAAA,yBAAAR,GAAA,CAAAlG,SAAA,EAAkE;QA6B5E3B,EAAA,CAAAI,SAAA,IAAe;QAAfJ,EAAA,CAAAY,UAAA,YAAAiH,GAAA,CAAA/F,YAAA,CAAe;QAsB6B9B,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAY,UAAA,aAAAiH,GAAA,CAAAlG,SAAA,CAAA+C,OAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}