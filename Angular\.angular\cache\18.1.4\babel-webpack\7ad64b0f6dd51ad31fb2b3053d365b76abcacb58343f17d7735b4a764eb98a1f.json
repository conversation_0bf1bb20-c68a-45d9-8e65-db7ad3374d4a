{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/permits.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction AddEditInternalReviewComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Internal Review - \", ctx_r0.permitNumber || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Add Review - \", ctx_r0.permitNumber || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"span\", 22);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Review category is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Type/Code/Drawing # is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Internal reviewer is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Internal verification status is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Reviewed date is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Completed date is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23)(2, \"div\", 24)(3, \"label\", 25);\n    i0.ɵɵtext(4, \"Review Category \");\n    i0.ɵɵelementStart(5, \"span\", 26);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"select\", 27)(8, \"option\", 28);\n    i0.ɵɵtext(9, \"Select Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 29);\n    i0.ɵɵtext(11, \"Building\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 30);\n    i0.ɵɵtext(13, \"Electrical\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 31);\n    i0.ɵɵtext(15, \"Mechanical\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 32);\n    i0.ɵɵtext(17, \"Plumbing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 33);\n    i0.ɵɵtext(19, \"Structural\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 34);\n    i0.ɵɵtext(21, \"Other\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, AddEditInternalReviewComponent_div_21_div_22_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 24)(24, \"label\", 25);\n    i0.ɵɵtext(25, \"Type/Code/Drawing # \");\n    i0.ɵɵelementStart(26, \"span\", 26);\n    i0.ɵɵtext(27, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(28, \"input\", 36);\n    i0.ɵɵtemplate(29, AddEditInternalReviewComponent_div_21_div_29_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 37)(31, \"div\", 24)(32, \"label\", 25);\n    i0.ɵɵtext(33, \"Internal Reviewer \");\n    i0.ɵɵelementStart(34, \"span\", 26);\n    i0.ɵɵtext(35, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(36, \"input\", 38);\n    i0.ɵɵtemplate(37, AddEditInternalReviewComponent_div_21_div_37_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 24)(39, \"label\", 25);\n    i0.ɵɵtext(40, \"Internal Verification Status \");\n    i0.ɵɵelementStart(41, \"span\", 26);\n    i0.ɵɵtext(42, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"select\", 39)(44, \"option\", 28);\n    i0.ɵɵtext(45, \"Select Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"option\");\n    i0.ɵɵtext(47, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"option\");\n    i0.ɵɵtext(49, \"In Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"option\");\n    i0.ɵɵtext(51, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"option\");\n    i0.ɵɵtext(53, \"Verified\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"option\");\n    i0.ɵɵtext(55, \"Rejected\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(56, AddEditInternalReviewComponent_div_21_div_56_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 37)(58, \"div\", 24)(59, \"label\", 25);\n    i0.ɵɵtext(60, \"Reviewed Date \");\n    i0.ɵɵelementStart(61, \"span\", 26);\n    i0.ɵɵtext(62, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(63, \"input\", 40);\n    i0.ɵɵtemplate(64, AddEditInternalReviewComponent_div_21_div_64_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 24)(66, \"label\", 25);\n    i0.ɵɵtext(67, \"Completed Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"input\", 41);\n    i0.ɵɵtemplate(69, AddEditInternalReviewComponent_div_21_div_69_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_12_0;\n    let tmp_13_0;\n    let tmp_15_0;\n    let tmp_16_0;\n    let tmp_18_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx_r0.reviewForm.get(\"reviewCategory\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.reviewForm.get(\"reviewCategory\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.reviewForm.get(\"reviewCategory\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.reviewForm.get(\"reviewCategory\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx_r0.reviewForm.get(\"typeCodeDrawing\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.reviewForm.get(\"typeCodeDrawing\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.reviewForm.get(\"typeCodeDrawing\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.reviewForm.get(\"typeCodeDrawing\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx_r0.reviewForm.get(\"internalReviewer\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r0.reviewForm.get(\"internalReviewer\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.reviewForm.get(\"internalReviewer\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r0.reviewForm.get(\"internalReviewer\")) == null ? null : tmp_9_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_10_0 = ctx_r0.reviewForm.get(\"internalVerificationStatus\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r0.reviewForm.get(\"internalVerificationStatus\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r0.reviewForm.get(\"internalVerificationStatus\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r0.reviewForm.get(\"internalVerificationStatus\")) == null ? null : tmp_12_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_13_0 = ctx_r0.reviewForm.get(\"reviewedDate\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx_r0.reviewForm.get(\"reviewedDate\")) == null ? null : tmp_13_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx_r0.reviewForm.get(\"reviewedDate\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r0.reviewForm.get(\"reviewedDate\")) == null ? null : tmp_15_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_16_0 = ctx_r0.reviewForm.get(\"completedDate\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx_r0.reviewForm.get(\"completedDate\")) == null ? null : tmp_16_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx_r0.reviewForm.get(\"completedDate\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx_r0.reviewForm.get(\"completedDate\")) == null ? null : tmp_18_0.touched));\n  }\n}\nfunction AddEditInternalReviewComponent_div_22_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Review comments is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23)(2, \"div\", 9)(3, \"label\", 25);\n    i0.ɵɵtext(4, \"Review Comments \");\n    i0.ɵɵelementStart(5, \"span\", 26);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"textarea\", 43);\n    i0.ɵɵtemplate(8, AddEditInternalReviewComponent_div_22_div_8_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 37)(10, \"div\", 9)(11, \"label\", 25);\n    i0.ɵɵtext(12, \"Non Compliance Items\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"textarea\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 37)(15, \"div\", 9)(16, \"label\", 25);\n    i0.ɵɵtext(17, \"A/E Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"textarea\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx_r0.reviewForm.get(\"reviewComments\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.reviewForm.get(\"reviewComments\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.reviewForm.get(\"reviewComments\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.reviewForm.get(\"reviewComments\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n  }\n}\nfunction AddEditInternalReviewComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.setActiveTab(\"comments\"));\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading || !ctx_r0.isDetailsValid);\n  }\n}\nfunction AddEditInternalReviewComponent_button_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 49);\n  }\n}\nfunction AddEditInternalReviewComponent_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵtemplate(1, AddEditInternalReviewComponent_button_31_span_1_Template, 1, 0, \"span\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading || !ctx_r0.isFormValid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isEdit ? \"Update\" : \"Create\", \" \");\n  }\n}\nexport class AddEditInternalReviewComponent {\n  fb;\n  modal;\n  modalService;\n  permitsService;\n  customLayoutUtilsService;\n  permitId = null;\n  reviewData = null; // For edit mode\n  loggedInUserId = 'user'; // Should be passed from parent\n  permitNumber = '';\n  reviewForm;\n  isEdit = false;\n  isLoading = false;\n  activeTab = 'details';\n  constructor(fb, modal, modalService, permitsService, customLayoutUtilsService) {\n    this.fb = fb;\n    this.modal = modal;\n    this.modalService = modalService;\n    this.permitsService = permitsService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n  }\n  ngOnInit() {\n    this.isEdit = !!this.reviewData;\n    this.reviewForm = this.fb.group({\n      reviewCategory: [this.reviewData?.reviewCategory || '', Validators.required],\n      typeCodeDrawing: [this.reviewData?.typeCodeDrawing || '', Validators.required],\n      reviewComments: [this.reviewData?.reviewComments || '', Validators.required],\n      nonComplianceItems: [this.reviewData?.nonComplianceItems || ''],\n      aeResponse: [this.reviewData?.aeResponse || ''],\n      internalReviewer: [this.reviewData?.internalReviewer || '', Validators.required],\n      internalVerificationStatus: [this.reviewData?.internalVerificationStatus || '', Validators.required],\n      reviewedDate: [this.reviewData?.reviewedDate ? this.formatDateForInput(this.reviewData.reviewedDate) : '', Validators.required],\n      completedDate: [this.reviewData?.completedDate ? this.formatDateForInput(this.reviewData.completedDate) : '']\n    });\n  }\n  setActiveTab(tab) {\n    this.activeTab = tab;\n  }\n  goToPrevious() {\n    if (this.activeTab === 'comments') {\n      this.activeTab = 'details';\n    }\n  }\n  formatDateForInput(date) {\n    if (!date) return '';\n    const d = new Date(date);\n    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type=\"date\"]\n  }\n  onSubmit() {\n    if (this.reviewForm.valid && this.permitId) {\n      this.isLoading = true;\n      const formData = {\n        ...this.reviewForm.value,\n        permitId: this.permitId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.reviewData?.commentsId) {\n        // Update existing review\n        formData.commentsId = this.reviewData.commentsId;\n        this.permitsService.updateInternalReview(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            if (res?.isFault) {\n              //alert(res.faultMessage || 'Failed to update internal review');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n              //alert('Internal review updated successfully!');\n              this.modal.close('updated');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            this.customLayoutUtilsService.showError('error updating internal review', '');\n            //alert('Error updating internal review');\n            console.error(err);\n          }\n        });\n      } else {\n        // Create new review\n        this.permitsService.addInternalReview(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            if (res?.isFault) {\n              //alert(res.faultMessage || 'Failed to create internal review');\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create interval review', '');\n            } else {\n              //alert('Internal review created successfully!');\n              this.customLayoutUtilsService.showSuccess('Internal review created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            this.customLayoutUtilsService.showError('error creating internal review', '');\n            //alert('Error creating internal review');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.reviewForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n        //alert('Permit ID is required');\n      }\n    }\n  }\n  onCancel() {\n    this.modal.dismiss('cancelled');\n  }\n  get isFormValid() {\n    return this.reviewForm.valid;\n  }\n  get isDetailsValid() {\n    if (!this.reviewForm) {\n      return false;\n    }\n    const controls = this.reviewForm.controls;\n    return !!controls.reviewCategory?.valid && !!controls.typeCodeDrawing?.valid && !!controls.internalReviewer?.valid && !!controls.internalVerificationStatus?.valid && !!controls.reviewedDate?.valid;\n  }\n  static ɵfac = function AddEditInternalReviewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AddEditInternalReviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.PermitsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddEditInternalReviewComponent,\n    selectors: [[\"app-add-edit-internal-review\"]],\n    inputs: {\n      permitId: \"permitId\",\n      reviewData: \"reviewData\",\n      loggedInUserId: \"loggedInUserId\",\n      permitNumber: \"permitNumber\"\n    },\n    decls: 32,\n    vars: 16,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\", 2, \"max-height\", \"calc(100vh - 250px)\", \"overflow-y\", \"auto\", \"position\", \"relative\"], [\"class\", \"loading-overlay-inside\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"novalidate\", \"\", 3, \"ngSubmit\", \"formGroup\"], [1, \"modal-footer\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"submit\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"loading-overlay-inside\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"row\", \"mt-3\"], [1, \"col-xl-6\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"formControlName\", \"reviewCategory\", 1, \"form-select\", \"form-select-sm\", 3, \"disabled\"], [\"value\", \"\"], [\"value\", \"Building\"], [\"value\", \"Electrical\"], [\"value\", \"Mechanical\"], [\"value\", \"Plumbing\"], [\"value\", \"Structural\"], [\"value\", \"Other\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"typeCodeDrawing\", \"placeholder\", \"Type/code/drawing\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"row\", \"mt-4\"], [\"type\", \"text\", \"formControlName\", \"internalReviewer\", \"placeholder\", \"Internal reviewer\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"internalVerificationStatus\", 1, \"form-select\", \"form-select-sm\", 3, \"disabled\"], [\"type\", \"date\", \"formControlName\", \"reviewedDate\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"date\", \"formControlName\", \"completedDate\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"invalid-feedback\"], [\"formControlName\", \"reviewComments\", \"rows\", \"3\", \"placeholder\", \"Review comments\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"nonComplianceItems\", \"rows\", \"3\", \"placeholder\", \"Non compliance items\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"aeResponse\", \"rows\", \"3\", \"placeholder\", \"A/E response\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function AddEditInternalReviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, AddEditInternalReviewComponent_div_4_Template, 2, 1, \"div\", 3)(5, AddEditInternalReviewComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_i_click_7_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6);\n        i0.ɵɵtemplate(9, AddEditInternalReviewComponent_div_9_Template, 4, 0, \"div\", 7);\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"ul\", 11)(14, \"li\", 12)(15, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_a_click_15_listener() {\n          return ctx.setActiveTab(\"details\");\n        });\n        i0.ɵɵtext(16, \" Review Details \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"li\", 12)(18, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_a_click_18_listener() {\n          return ctx.setActiveTab(\"comments\");\n        });\n        i0.ɵɵtext(19, \" Review Comments \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(20, \"form\", 14);\n        i0.ɵɵlistener(\"ngSubmit\", function AddEditInternalReviewComponent_Template_form_ngSubmit_20_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtemplate(21, AddEditInternalReviewComponent_div_21_Template, 70, 24, \"div\", 3)(22, AddEditInternalReviewComponent_div_22_Template, 19, 6, \"div\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\")(25, \"button\", 16);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_button_click_25_listener() {\n          return ctx.goToPrevious();\n        });\n        i0.ɵɵtext(26, \" Previous \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\")(28, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_button_click_28_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵtext(29, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(30, AddEditInternalReviewComponent_button_30_Template, 2, 1, \"button\", 18)(31, AddEditInternalReviewComponent_button_31_Template, 3, 3, \"button\", 19);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isEdit);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isEdit);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.activeTab === \"details\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ctx.activeTab === \"comments\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.reviewForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"details\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"comments\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.activeTab === \"details\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"details\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"comments\");\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "permitNumber", "ɵɵtemplate", "AddEditInternalReviewComponent_div_21_div_22_Template", "ɵɵelement", "AddEditInternalReviewComponent_div_21_div_29_Template", "AddEditInternalReviewComponent_div_21_div_37_Template", "AddEditInternalReviewComponent_div_21_div_56_Template", "AddEditInternalReviewComponent_div_21_div_64_Template", "AddEditInternalReviewComponent_div_21_div_69_Template", "ɵɵclassProp", "tmp_1_0", "reviewForm", "get", "invalid", "touched", "ɵɵproperty", "isLoading", "tmp_3_0", "tmp_4_0", "tmp_6_0", "tmp_7_0", "tmp_9_0", "tmp_10_0", "tmp_12_0", "tmp_13_0", "tmp_15_0", "tmp_16_0", "tmp_18_0", "AddEditInternalReviewComponent_div_22_div_8_Template", "ɵɵlistener", "AddEditInternalReviewComponent_button_30_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "setActiveTab", "isDetailsValid", "AddEditInternalReviewComponent_button_31_Template_button_click_0_listener", "_r3", "onSubmit", "AddEditInternalReviewComponent_button_31_span_1_Template", "isFormValid", "isEdit", "AddEditInternalReviewComponent", "fb", "modal", "modalService", "permitsService", "customLayoutUtilsService", "permitId", "reviewData", "loggedInUserId", "activeTab", "constructor", "ngOnInit", "group", "reviewCategory", "required", "typeCodeDrawing", "reviewComments", "nonComplianceItems", "aeResponse", "internalReviewer", "internalVerificationStatus", "reviewedDate", "formatDateForInput", "completedDate", "tab", "goToPrevious", "date", "d", "Date", "toISOString", "split", "valid", "formData", "value", "commentsId", "updateInternalReview", "subscribe", "next", "res", "<PERSON><PERSON><PERSON>", "showSuccess", "responseData", "message", "close", "error", "err", "showError", "console", "addInternalReview", "faultMessage", "mark<PERSON>llAsTouched", "onCancel", "dismiss", "controls", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "NgbActiveModal", "NgbModal", "i3", "PermitsService", "i4", "CustomLayoutUtilsService", "selectors", "inputs", "decls", "vars", "consts", "template", "AddEditInternalReviewComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "AddEditInternalReviewComponent_div_4_Template", "AddEditInternalReviewComponent_div_5_Template", "AddEditInternalReviewComponent_Template_i_click_7_listener", "AddEditInternalReviewComponent_div_9_Template", "AddEditInternalReviewComponent_Template_a_click_15_listener", "AddEditInternalReviewComponent_Template_a_click_18_listener", "AddEditInternalReviewComponent_Template_form_ngSubmit_20_listener", "AddEditInternalReviewComponent_div_21_Template", "AddEditInternalReviewComponent_div_22_Template", "AddEditInternalReviewComponent_Template_button_click_25_listener", "AddEditInternalReviewComponent_Template_button_click_28_listener", "AddEditInternalReviewComponent_button_30_Template", "AddEditInternalReviewComponent_button_31_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review\\add-edit-internal-review.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review\\add-edit-internal-review.component.html"], "sourcesContent": ["import { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Component, Input, OnInit } from '@angular/core';\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\nimport { PermitsService } from '../../services/permits.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\n\n@Component({\n  selector: 'app-add-edit-internal-review',\n  standalone: false,\n  templateUrl: './add-edit-internal-review.component.html',\n  styleUrl: './add-edit-internal-review.component.scss',\n})\nexport class AddEditInternalReviewComponent implements OnInit {\n  @Input() permitId: number | null = null;\n  @Input() reviewData: any = null; // For edit mode\n  @Input() loggedInUserId: string = 'user'; // Should be passed from parent\n  @Input() permitNumber: string = '';\n\n  reviewForm!: FormGroup;\n  isEdit: boolean = false;\n  isLoading: boolean = false;\n  activeTab: 'details' | 'comments' = 'details';\n\n  constructor(\n    private fb: FormBuilder,\n    public modal: NgbActiveModal,\n    private modalService: NgbModal,\n    private permitsService: PermitsService,\n        private customLayoutUtilsService: CustomLayoutUtilsService,\n\n  ) {}\n\n  ngOnInit(): void {\n    this.isEdit = !!this.reviewData;\n\n    this.reviewForm = this.fb.group({\n      reviewCategory: [this.reviewData?.reviewCategory || '', Validators.required],\n      typeCodeDrawing: [this.reviewData?.typeCodeDrawing || '', Validators.required],\n      reviewComments: [this.reviewData?.reviewComments || '', Validators.required],\n      nonComplianceItems: [this.reviewData?.nonComplianceItems || ''],\n      aeResponse: [this.reviewData?.aeResponse || ''],\n      internalReviewer: [this.reviewData?.internalReviewer || '', Validators.required],\n      internalVerificationStatus: [this.reviewData?.internalVerificationStatus || '', Validators.required],\n      reviewedDate: [this.reviewData?.reviewedDate ? this.formatDateForInput(this.reviewData.reviewedDate) : '', Validators.required],\n      completedDate: [\n        this.reviewData?.completedDate\n          ? this.formatDateForInput(this.reviewData.completedDate)\n          : ''\n      ],\n    });\n  }\n\n  setActiveTab(tab: 'details' | 'comments'): void {\n    this.activeTab = tab;\n  }\n\n  goToPrevious(): void {\n    if (this.activeTab === 'comments') {\n      this.activeTab = 'details';\n    }\n  }\n\n  private formatDateForInput(date: string | Date): string {\n    if (!date) return '';\n    const d = new Date(date);\n    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type=\"date\"]\n  }\n\n  onSubmit(): void {\n    if (this.reviewForm.valid && this.permitId) {\n      this.isLoading = true;\n\n      const formData = {\n        ...this.reviewForm.value,\n        permitId: this.permitId,\n        loggedInUserId: this.loggedInUserId\n      };\n\n      if (this.isEdit && this.reviewData?.commentsId) {\n        // Update existing review\n        formData.commentsId = this.reviewData.commentsId;\n\n        this.permitsService.updateInternalReview(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            if (res?.isFault) {\n              //alert(res.faultMessage || 'Failed to update internal review');\n            } else {\n                                  this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n\n              //alert('Internal review updated successfully!');\n              this.modal.close('updated');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n                                this.customLayoutUtilsService.showError('error updating internal review', '');\n\n            //alert('Error updating internal review');\n            console.error(err);\n          }\n        });\n      } else {\n        // Create new review\n        this.permitsService.addInternalReview(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            if (res?.isFault) {\n              //alert(res.faultMessage || 'Failed to create internal review');\n                                  this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create interval review', '');\n\n            } else {\n              //alert('Internal review created successfully!');\n                                  this.customLayoutUtilsService.showSuccess('Internal review created successfully!', '');\n\n              this.modal.close('created');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n                                this.customLayoutUtilsService.showError('error creating internal review', '');\n\n            //alert('Error creating internal review');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.reviewForm.markAllAsTouched();\n      if (!this.permitId) {\n                            this.customLayoutUtilsService.showError('Permit Id is required', '');\n\n        //alert('Permit ID is required');\n      }\n    }\n  }\n\n  onCancel(): void {\n    this.modal.dismiss('cancelled');\n  }\n\n  get isFormValid(): boolean {\n    return this.reviewForm.valid;\n  }\n\n  get isDetailsValid(): boolean {\n    if (!this.reviewForm) { return false; }\n    const controls = this.reviewForm.controls as any;\n    return (\n      !!controls.reviewCategory?.valid &&\n      !!controls.typeCodeDrawing?.valid &&\n      !!controls.internalReviewer?.valid &&\n      !!controls.internalVerificationStatus?.valid &&\n      !!controls.reviewedDate?.valid\n    );\n  }\n}\n", "<div class=\"modal-content h-auto\">\n  <!-- Header -->\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container>\n        <div *ngIf=\"isEdit\">Edit Internal Review - {{ permitNumber || '' }}</div>\n        <div *ngIf=\"!isEdit\">Add Review - {{ permitNumber || '' }}</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i\n        class=\"fa-solid fs-2 fa-xmark text-white\"\n        (click)=\"onCancel()\"\n      ></i>\n    </div>\n  </div>\n\n  <!-- Body -->\n  <div\n    class=\"modal-body large-modal-body\"\n    style=\"max-height: calc(100vh - 250px); overflow-y: auto; position: relative;\"\n  >\n    <!-- Loading Overlay -->\n    <div *ngIf=\"isLoading\" class=\"loading-overlay-inside\">\n      <div class=\"spinner-border text-primary spinner-md\" role=\"status\">\n        <span class=\"visually-hidden\">Loading...</span>\n      </div>\n    </div>\n\n    <!-- Tabs (match Project Popup style) -->\n    <div class=\"row\">\n      <div class=\"col-xl-12\">\n        <div class=\"d-flex\">\n          <ul\n            class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\"\n          >\n            <li class=\"nav-item\">\n              <a\n                class=\"nav-link text-active-primary me-6 cursor-pointer\"\n                data-toggle=\"tab\"\n                [ngClass]=\"{ active: activeTab === 'details' }\"\n                (click)=\"setActiveTab('details')\"\n              >\n                Review Details\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a\n                class=\"nav-link text-active-primary me-6 cursor-pointer\"\n                data-toggle=\"tab\"\n                [ngClass]=\"{ active: activeTab === 'comments' }\"\n                (click)=\"setActiveTab('comments')\"\n              >\n                Review Comments\n              </a>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <!-- Form -->\n    <form [formGroup]=\"reviewForm\" (ngSubmit)=\"onSubmit()\" novalidate>\n        <!-- DETAILS TAB CONTENT -->\n        <div *ngIf=\"activeTab === 'details'\">\n          <!-- Review Category and Type/Code/Drawing Row -->\n          <div class=\"row mt-3\">\n            <div class=\"col-xl-6\">\n              <label class=\"fw-bold form-label mb-2\">Review Category <span class=\"text-danger\">*</span></label>\n              <select\n                formControlName=\"reviewCategory\"\n                class=\"form-select form-select-sm\"\n                [class.is-invalid]=\"reviewForm.get('reviewCategory')?.invalid && reviewForm.get('reviewCategory')?.touched\"\n                [disabled]=\"isLoading\"\n              >\n                <option value=\"\">Select Category</option>\n                <option value=\"Building\">Building</option>\n                <option value=\"Electrical\">Electrical</option>\n                <option value=\"Mechanical\">Mechanical</option>\n                <option value=\"Plumbing\">Plumbing</option>\n                <option value=\"Structural\">Structural</option>\n                <option value=\"Other\">Other</option>\n              </select>\n              <div class=\"invalid-feedback\" *ngIf=\"reviewForm.get('reviewCategory')?.invalid && reviewForm.get('reviewCategory')?.touched\">\n                Review category is required\n              </div>\n            </div>\n            <div class=\"col-xl-6\">\n              <label class=\"fw-bold form-label mb-2\">Type/Code/Drawing # <span class=\"text-danger\">*</span></label>\n              <input\n                type=\"text\"\n                formControlName=\"typeCodeDrawing\"\n                class=\"form-control form-control-sm\"\n                [class.is-invalid]=\"reviewForm.get('typeCodeDrawing')?.invalid && reviewForm.get('typeCodeDrawing')?.touched\"\n                placeholder=\"Type/code/drawing\"\n                [disabled]=\"isLoading\"\n              />\n              <div class=\"invalid-feedback\" *ngIf=\"reviewForm.get('typeCodeDrawing')?.invalid && reviewForm.get('typeCodeDrawing')?.touched\">\n                Type/Code/Drawing # is required\n              </div>\n            </div>\n          </div>\n\n          <!-- Internal Reviewer and Internal Verification Status Row -->\n          <div class=\"row mt-4\">\n            <div class=\"col-xl-6\">\n              <label class=\"fw-bold form-label mb-2\">Internal Reviewer <span class=\"text-danger\">*</span></label>\n              <input\n                type=\"text\"\n                formControlName=\"internalReviewer\"\n                class=\"form-control form-control-sm\"\n                [class.is-invalid]=\"reviewForm.get('internalReviewer')?.invalid && reviewForm.get('internalReviewer')?.touched\"\n                placeholder=\"Internal reviewer\"\n                [disabled]=\"isLoading\"\n              />\n              <div class=\"invalid-feedback\" *ngIf=\"reviewForm.get('internalReviewer')?.invalid && reviewForm.get('internalReviewer')?.touched\">\n                Internal reviewer is required\n              </div>\n            </div>\n            <div class=\"col-xl-6\">\n              <label class=\"fw-bold form-label mb-2\">Internal Verification Status <span class=\"text-danger\">*</span></label>\n              <select\n                formControlName=\"internalVerificationStatus\"\n                class=\"form-select form-select-sm\"\n                [class.is-invalid]=\"reviewForm.get('internalVerificationStatus')?.invalid && reviewForm.get('internalVerificationStatus')?.touched\"\n                [disabled]=\"isLoading\"\n              >\n                <option value=\"\">Select Status</option>\n                <option>Pending</option>\n                <option>In Progress</option>\n                <option>Completed</option>\n                <option>Verified</option>\n                <option>Rejected</option>\n              </select>\n              <div class=\"invalid-feedback\" *ngIf=\"reviewForm.get('internalVerificationStatus')?.invalid && reviewForm.get('internalVerificationStatus')?.touched\">\n                Internal verification status is required\n              </div>\n            </div>\n          </div>\n\n          <!-- Dates Row (Reviewed & Completed) -->\n          <div class=\"row mt-4\">\n            <div class=\"col-xl-6\">\n              <label class=\"fw-bold form-label mb-2\">Reviewed Date <span class=\"text-danger\">*</span></label>\n              <input\n                type=\"date\"\n                formControlName=\"reviewedDate\"\n                class=\"form-control form-control-sm\"\n                [class.is-invalid]=\"reviewForm.get('reviewedDate')?.invalid && reviewForm.get('reviewedDate')?.touched\"\n                [disabled]=\"isLoading\"\n              />\n              <div class=\"invalid-feedback\" *ngIf=\"reviewForm.get('reviewedDate')?.invalid && reviewForm.get('reviewedDate')?.touched\">\n                Reviewed date is required\n              </div>\n            </div>\n            <div class=\"col-xl-6\">\n              <label class=\"fw-bold form-label mb-2\">Completed Date</label>\n              <input\n                type=\"date\"\n                formControlName=\"completedDate\"\n                class=\"form-control form-control-sm\"\n                [class.is-invalid]=\"reviewForm.get('completedDate')?.invalid && reviewForm.get('completedDate')?.touched\"\n                [disabled]=\"isLoading\"\n              />\n              <div class=\"invalid-feedback\" *ngIf=\"reviewForm.get('completedDate')?.invalid && reviewForm.get('completedDate')?.touched\">\n                Completed date is required\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- COMMENTS TAB CONTENT -->\n        <div *ngIf=\"activeTab === 'comments'\">\n          <!-- Review Comments -->\n          <div class=\"row mt-3\">\n            <div class=\"col-xl-12\">\n              <label class=\"fw-bold form-label mb-2\">Review Comments <span class=\"text-danger\">*</span></label>\n              <textarea\n                formControlName=\"reviewComments\"\n                rows=\"3\"\n                class=\"form-control form-control-sm\"\n                [class.is-invalid]=\"reviewForm.get('reviewComments')?.invalid && reviewForm.get('reviewComments')?.touched\"\n                placeholder=\"Review comments\"\n                [disabled]=\"isLoading\"\n              ></textarea>\n              <div class=\"invalid-feedback\" *ngIf=\"reviewForm.get('reviewComments')?.invalid && reviewForm.get('reviewComments')?.touched\">\n                Review comments is required\n              </div>\n            </div>\n          </div>\n\n          <!-- Non Compliance Items -->\n          <div class=\"row mt-4\">\n            <div class=\"col-xl-12\">\n              <label class=\"fw-bold form-label mb-2\">Non Compliance Items</label>\n              <textarea\n                formControlName=\"nonComplianceItems\"\n                rows=\"3\"\n                class=\"form-control form-control-sm\"\n                placeholder=\"Non compliance items\"\n                [disabled]=\"isLoading\"\n              ></textarea>\n            </div>\n          </div>\n\n          <!-- A/E Response -->\n          <div class=\"row mt-4\">\n            <div class=\"col-xl-12\">\n              <label class=\"fw-bold form-label mb-2\">A/E Response</label>\n              <textarea\n                formControlName=\"aeResponse\"\n                rows=\"3\"\n                class=\"form-control form-control-sm\"\n                placeholder=\"A/E response\"\n                [disabled]=\"isLoading\"\n              ></textarea>\n            </div>\n          </div>\n        </div>\n    </form>\n  </div>\n\n  <!-- Footer -->\n  <div class=\"modal-footer d-flex justify-content-between align-items-center\">\n    <div>\n      <button\n        type=\"button\"\n        class=\"btn btn-light btn-sm\"\n        (click)=\"goToPrevious()\"\n        [disabled]=\"isLoading || activeTab === 'details'\"\n      >\n        Previous\n      </button>\n    </div>\n    <div>\n      <button\n        type=\"button\"\n        class=\"btn btn-danger btn-sm btn-elevate me-2\"\n        (click)=\"onCancel()\"\n        [disabled]=\"isLoading\"\n      >\n        Cancel\n      </button>\n      <button\n        *ngIf=\"activeTab === 'details'\"\n        type=\"button\"\n        class=\"btn btn-primary btn-sm\"\n        [disabled]=\"isLoading || !isDetailsValid\"\n        (click)=\"setActiveTab('comments')\"\n      >\n        Next\n      </button>\n      <button\n        *ngIf=\"activeTab === 'comments'\"\n        type=\"submit\"\n        class=\"btn btn-primary btn-sm\"\n        [disabled]=\"isLoading || !isFormValid\"\n        (click)=\"onSubmit()\"\n      >\n        <span *ngIf=\"isLoading\" class=\"spinner-border spinner-border-sm me-2\"></span>\n        {{ isEdit ? 'Update' : 'Create' }}\n      </button>\n    </div>\n  </div>\n</div>\n\n"], "mappings": "AAAA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;ICK3DC,EAAA,CAAAC,cAAA,UAAoB;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAArDH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAK,kBAAA,4BAAAC,MAAA,CAAAC,YAAA,WAA+C;;;;;IACnEP,EAAA,CAAAC,cAAA,UAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA3CH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAK,kBAAA,kBAAAC,MAAA,CAAAC,YAAA,WAAqC;;;;;IAmB1DP,EAFJ,CAAAC,cAAA,cAAsD,cACc,eAClC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAE5CF,EAF4C,CAAAG,YAAA,EAAO,EAC3C,EACF;;;;;IAwDIH,EAAA,CAAAC,cAAA,cAA6H;IAC3HD,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYNH,EAAA,CAAAC,cAAA,cAA+H;IAC7HD,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBNH,EAAA,CAAAC,cAAA,cAAiI;IAC/HD,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAiBNH,EAAA,CAAAC,cAAA,cAAqJ;IACnJD,EAAA,CAAAE,MAAA,iDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAeNH,EAAA,CAAAC,cAAA,cAAyH;IACvHD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAlGNH,EAJN,CAAAC,cAAA,UAAqC,cAEb,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAO/FH,EANF,CAAAC,cAAA,iBAKC,iBACkB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzCH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9CH,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9CH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9CH,EAAA,CAAAC,cAAA,kBAAsB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAC7BF,EAD6B,CAAAG,YAAA,EAAS,EAC7B;IACTH,EAAA,CAAAQ,UAAA,KAAAC,qDAAA,kBAA6H;IAG/HT,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACrGH,EAAA,CAAAU,SAAA,iBAOE;IACFV,EAAA,CAAAQ,UAAA,KAAAG,qDAAA,kBAA+H;IAInIX,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACnGH,EAAA,CAAAU,SAAA,iBAOE;IACFV,EAAA,CAAAQ,UAAA,KAAAI,qDAAA,kBAAiI;IAGnIZ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAO5GH,EANF,CAAAC,cAAA,kBAKC,kBACkB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxBH,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5BH,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1BH,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzBH,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAClBF,EADkB,CAAAG,YAAA,EAAS,EAClB;IACTH,EAAA,CAAAQ,UAAA,KAAAK,qDAAA,kBAAqJ;IAIzJb,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC/FH,EAAA,CAAAU,SAAA,iBAME;IACFV,EAAA,CAAAQ,UAAA,KAAAM,qDAAA,kBAAyH;IAG3Hd,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAU,SAAA,iBAME;IACFV,EAAA,CAAAQ,UAAA,KAAAO,qDAAA,kBAA2H;IAKjIf,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;;;;;;;;;;;;IAjGEH,EAAA,CAAAI,SAAA,GAA2G;IAA3GJ,EAAA,CAAAgB,WAAA,iBAAAC,OAAA,GAAAX,MAAA,CAAAY,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAG,OAAA,OAAAH,OAAA,GAAAX,MAAA,CAAAY,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAI,OAAA,EAA2G;IAC3GrB,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;IAUOvB,EAAA,CAAAI,SAAA,IAA4F;IAA5FJ,EAAA,CAAAsB,UAAA,WAAAE,OAAA,GAAAlB,MAAA,CAAAY,UAAA,CAAAC,GAAA,qCAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAlB,MAAA,CAAAY,UAAA,CAAAC,GAAA,qCAAAK,OAAA,CAAAH,OAAA,EAA4F;IAUzHrB,EAAA,CAAAI,SAAA,GAA6G;IAA7GJ,EAAA,CAAAgB,WAAA,iBAAAS,OAAA,GAAAnB,MAAA,CAAAY,UAAA,CAAAC,GAAA,sCAAAM,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAnB,MAAA,CAAAY,UAAA,CAAAC,GAAA,sCAAAM,OAAA,CAAAJ,OAAA,EAA6G;IAE7GrB,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;IAEOvB,EAAA,CAAAI,SAAA,EAA8F;IAA9FJ,EAAA,CAAAsB,UAAA,WAAAI,OAAA,GAAApB,MAAA,CAAAY,UAAA,CAAAC,GAAA,sCAAAO,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAApB,MAAA,CAAAY,UAAA,CAAAC,GAAA,sCAAAO,OAAA,CAAAL,OAAA,EAA8F;IAc3HrB,EAAA,CAAAI,SAAA,GAA+G;IAA/GJ,EAAA,CAAAgB,WAAA,iBAAAW,OAAA,GAAArB,MAAA,CAAAY,UAAA,CAAAC,GAAA,uCAAAQ,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAArB,MAAA,CAAAY,UAAA,CAAAC,GAAA,uCAAAQ,OAAA,CAAAN,OAAA,EAA+G;IAE/GrB,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;IAEOvB,EAAA,CAAAI,SAAA,EAAgG;IAAhGJ,EAAA,CAAAsB,UAAA,WAAAM,OAAA,GAAAtB,MAAA,CAAAY,UAAA,CAAAC,GAAA,uCAAAS,OAAA,CAAAR,OAAA,OAAAQ,OAAA,GAAAtB,MAAA,CAAAY,UAAA,CAAAC,GAAA,uCAAAS,OAAA,CAAAP,OAAA,EAAgG;IAS7HrB,EAAA,CAAAI,SAAA,GAAmI;IAAnIJ,EAAA,CAAAgB,WAAA,iBAAAa,QAAA,GAAAvB,MAAA,CAAAY,UAAA,CAAAC,GAAA,iDAAAU,QAAA,CAAAT,OAAA,OAAAS,QAAA,GAAAvB,MAAA,CAAAY,UAAA,CAAAC,GAAA,iDAAAU,QAAA,CAAAR,OAAA,EAAmI;IACnIrB,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;IASOvB,EAAA,CAAAI,SAAA,IAAoH;IAApHJ,EAAA,CAAAsB,UAAA,WAAAQ,QAAA,GAAAxB,MAAA,CAAAY,UAAA,CAAAC,GAAA,iDAAAW,QAAA,CAAAV,OAAA,OAAAU,QAAA,GAAAxB,MAAA,CAAAY,UAAA,CAAAC,GAAA,iDAAAW,QAAA,CAAAT,OAAA,EAAoH;IAcjJrB,EAAA,CAAAI,SAAA,GAAuG;IAAvGJ,EAAA,CAAAgB,WAAA,iBAAAe,QAAA,GAAAzB,MAAA,CAAAY,UAAA,CAAAC,GAAA,mCAAAY,QAAA,CAAAX,OAAA,OAAAW,QAAA,GAAAzB,MAAA,CAAAY,UAAA,CAAAC,GAAA,mCAAAY,QAAA,CAAAV,OAAA,EAAuG;IACvGrB,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;IAEOvB,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAsB,UAAA,WAAAU,QAAA,GAAA1B,MAAA,CAAAY,UAAA,CAAAC,GAAA,mCAAAa,QAAA,CAAAZ,OAAA,OAAAY,QAAA,GAAA1B,MAAA,CAAAY,UAAA,CAAAC,GAAA,mCAAAa,QAAA,CAAAX,OAAA,EAAwF;IAUrHrB,EAAA,CAAAI,SAAA,GAAyG;IAAzGJ,EAAA,CAAAgB,WAAA,iBAAAiB,QAAA,GAAA3B,MAAA,CAAAY,UAAA,CAAAC,GAAA,oCAAAc,QAAA,CAAAb,OAAA,OAAAa,QAAA,GAAA3B,MAAA,CAAAY,UAAA,CAAAC,GAAA,oCAAAc,QAAA,CAAAZ,OAAA,EAAyG;IACzGrB,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;IAEOvB,EAAA,CAAAI,SAAA,EAA0F;IAA1FJ,EAAA,CAAAsB,UAAA,WAAAY,QAAA,GAAA5B,MAAA,CAAAY,UAAA,CAAAC,GAAA,oCAAAe,QAAA,CAAAd,OAAA,OAAAc,QAAA,GAAA5B,MAAA,CAAAY,UAAA,CAAAC,GAAA,oCAAAe,QAAA,CAAAb,OAAA,EAA0F;;;;;IAqBzHrB,EAAA,CAAAC,cAAA,cAA6H;IAC3HD,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAXNH,EAJN,CAAAC,cAAA,UAAsC,cAEd,aACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACjGH,EAAA,CAAAU,SAAA,mBAOY;IACZV,EAAA,CAAAQ,UAAA,IAAA2B,oDAAA,kBAA6H;IAIjInC,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAU,SAAA,oBAMY;IAEhBV,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3DH,EAAA,CAAAU,SAAA,oBAMY;IAGlBV,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;;IArCEH,EAAA,CAAAI,SAAA,GAA2G;IAA3GJ,EAAA,CAAAgB,WAAA,iBAAAC,OAAA,GAAAX,MAAA,CAAAY,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAG,OAAA,OAAAH,OAAA,GAAAX,MAAA,CAAAY,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAI,OAAA,EAA2G;IAE3GrB,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;IAEOvB,EAAA,CAAAI,SAAA,EAA4F;IAA5FJ,EAAA,CAAAsB,UAAA,WAAAE,OAAA,GAAAlB,MAAA,CAAAY,UAAA,CAAAC,GAAA,qCAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAlB,MAAA,CAAAY,UAAA,CAAAC,GAAA,qCAAAK,OAAA,CAAAH,OAAA,EAA4F;IAezHrB,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;IActBvB,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,CAAsB;;;;;;IA6BhCvB,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAoC,UAAA,mBAAAC,0EAAA;MAAArC,EAAA,CAAAsC,aAAA,CAAAC,GAAA;MAAA,MAAAjC,MAAA,GAAAN,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAASnC,MAAA,CAAAoC,YAAA,CAAa,UAAU,CAAC;IAAA,EAAC;IAElC1C,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,KAAAjB,MAAA,CAAAqC,cAAA,CAAyC;;;;;IAYzC3C,EAAA,CAAAU,SAAA,eAA6E;;;;;;IAP/EV,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAoC,UAAA,mBAAAQ,0EAAA;MAAA5C,EAAA,CAAAsC,aAAA,CAAAO,GAAA;MAAA,MAAAvC,MAAA,GAAAN,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAASnC,MAAA,CAAAwC,QAAA,EAAU;IAAA,EAAC;IAEpB9C,EAAA,CAAAQ,UAAA,IAAAuC,wDAAA,mBAAsE;IACtE/C,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IALPH,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,SAAA,KAAAjB,MAAA,CAAA0C,WAAA,CAAsC;IAG/BhD,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAiB,SAAA,CAAe;IACtBvB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA2C,MAAA,4BACF;;;ADzPN,OAAM,MAAOC,8BAA8B;EAY/BC,EAAA;EACDC,KAAA;EACCC,YAAA;EACAC,cAAA;EACIC,wBAAA;EAfLC,QAAQ,GAAkB,IAAI;EAC9BC,UAAU,GAAQ,IAAI,CAAC,CAAC;EACxBC,cAAc,GAAW,MAAM,CAAC,CAAC;EACjCnD,YAAY,GAAW,EAAE;EAElCW,UAAU;EACV+B,MAAM,GAAY,KAAK;EACvB1B,SAAS,GAAY,KAAK;EAC1BoC,SAAS,GAA2B,SAAS;EAE7CC,YACUT,EAAe,EAChBC,KAAqB,EACpBC,YAAsB,EACtBC,cAA8B,EAC1BC,wBAAkD;IAJtD,KAAAJ,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACV,KAAAC,wBAAwB,GAAxBA,wBAAwB;EAEnC;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACZ,MAAM,GAAG,CAAC,CAAC,IAAI,CAACQ,UAAU;IAE/B,IAAI,CAACvC,UAAU,GAAG,IAAI,CAACiC,EAAE,CAACW,KAAK,CAAC;MAC9BC,cAAc,EAAE,CAAC,IAAI,CAACN,UAAU,EAAEM,cAAc,IAAI,EAAE,EAAEhE,UAAU,CAACiE,QAAQ,CAAC;MAC5EC,eAAe,EAAE,CAAC,IAAI,CAACR,UAAU,EAAEQ,eAAe,IAAI,EAAE,EAAElE,UAAU,CAACiE,QAAQ,CAAC;MAC9EE,cAAc,EAAE,CAAC,IAAI,CAACT,UAAU,EAAES,cAAc,IAAI,EAAE,EAAEnE,UAAU,CAACiE,QAAQ,CAAC;MAC5EG,kBAAkB,EAAE,CAAC,IAAI,CAACV,UAAU,EAAEU,kBAAkB,IAAI,EAAE,CAAC;MAC/DC,UAAU,EAAE,CAAC,IAAI,CAACX,UAAU,EAAEW,UAAU,IAAI,EAAE,CAAC;MAC/CC,gBAAgB,EAAE,CAAC,IAAI,CAACZ,UAAU,EAAEY,gBAAgB,IAAI,EAAE,EAAEtE,UAAU,CAACiE,QAAQ,CAAC;MAChFM,0BAA0B,EAAE,CAAC,IAAI,CAACb,UAAU,EAAEa,0BAA0B,IAAI,EAAE,EAAEvE,UAAU,CAACiE,QAAQ,CAAC;MACpGO,YAAY,EAAE,CAAC,IAAI,CAACd,UAAU,EAAEc,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACf,UAAU,CAACc,YAAY,CAAC,GAAG,EAAE,EAAExE,UAAU,CAACiE,QAAQ,CAAC;MAC/HS,aAAa,EAAE,CACb,IAAI,CAAChB,UAAU,EAAEgB,aAAa,GAC1B,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACf,UAAU,CAACgB,aAAa,CAAC,GACtD,EAAE;KAET,CAAC;EACJ;EAEA/B,YAAYA,CAACgC,GAA2B;IACtC,IAAI,CAACf,SAAS,GAAGe,GAAG;EACtB;EAEAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAChB,SAAS,KAAK,UAAU,EAAE;MACjC,IAAI,CAACA,SAAS,GAAG,SAAS;IAC5B;EACF;EAEQa,kBAAkBA,CAACI,IAAmB;IAC5C,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAOC,CAAC,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxC;EAEAlC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC5B,UAAU,CAAC+D,KAAK,IAAI,IAAI,CAACzB,QAAQ,EAAE;MAC1C,IAAI,CAACjC,SAAS,GAAG,IAAI;MAErB,MAAM2D,QAAQ,GAAG;QACf,GAAG,IAAI,CAAChE,UAAU,CAACiE,KAAK;QACxB3B,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBE,cAAc,EAAE,IAAI,CAACA;OACtB;MAED,IAAI,IAAI,CAACT,MAAM,IAAI,IAAI,CAACQ,UAAU,EAAE2B,UAAU,EAAE;QAC9C;QACAF,QAAQ,CAACE,UAAU,GAAG,IAAI,CAAC3B,UAAU,CAAC2B,UAAU;QAEhD,IAAI,CAAC9B,cAAc,CAAC+B,oBAAoB,CAACH,QAAQ,CAAC,CAACI,SAAS,CAAC;UAC3DC,IAAI,EAAGC,GAAQ,IAAI;YACjB,IAAI,CAACjE,SAAS,GAAG,KAAK;YACtB,IAAIiE,GAAG,EAAEC,OAAO,EAAE;cAChB;YAAA,CACD,MAAM;cACe,IAAI,CAAClC,wBAAwB,CAACmC,WAAW,CAACF,GAAG,CAACG,YAAY,CAACC,OAAO,EAAE,EAAE,CAAC;cAE3F;cACA,IAAI,CAACxC,KAAK,CAACyC,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAACxE,SAAS,GAAG,KAAK;YACF,IAAI,CAACgC,wBAAwB,CAACyC,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;YAEjG;YACAC,OAAO,CAACH,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAI,CAACzC,cAAc,CAAC4C,iBAAiB,CAAChB,QAAQ,CAAC,CAACI,SAAS,CAAC;UACxDC,IAAI,EAAGC,GAAQ,IAAI;YACjB,IAAI,CAACjE,SAAS,GAAG,KAAK;YACtB,IAAIiE,GAAG,EAAEC,OAAO,EAAE;cAChB;cACoB,IAAI,CAAClC,wBAAwB,CAACyC,SAAS,CAACR,GAAG,CAACW,YAAY,IAAI,kCAAkC,EAAE,EAAE,CAAC;YAEzH,CAAC,MAAM;cACL;cACoB,IAAI,CAAC5C,wBAAwB,CAACmC,WAAW,CAAC,uCAAuC,EAAE,EAAE,CAAC;cAE1G,IAAI,CAACtC,KAAK,CAACyC,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAACxE,SAAS,GAAG,KAAK;YACF,IAAI,CAACgC,wBAAwB,CAACyC,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;YAEjG;YACAC,OAAO,CAACH,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAAC7E,UAAU,CAACkF,gBAAgB,EAAE;MAClC,IAAI,CAAC,IAAI,CAAC5C,QAAQ,EAAE;QACE,IAAI,CAACD,wBAAwB,CAACyC,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;QAExF;MACF;IACF;EACF;EAEAK,QAAQA,CAAA;IACN,IAAI,CAACjD,KAAK,CAACkD,OAAO,CAAC,WAAW,CAAC;EACjC;EAEA,IAAItD,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC9B,UAAU,CAAC+D,KAAK;EAC9B;EAEA,IAAItC,cAAcA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACzB,UAAU,EAAE;MAAE,OAAO,KAAK;IAAE;IACtC,MAAMqF,QAAQ,GAAG,IAAI,CAACrF,UAAU,CAACqF,QAAe;IAChD,OACE,CAAC,CAACA,QAAQ,CAACxC,cAAc,EAAEkB,KAAK,IAChC,CAAC,CAACsB,QAAQ,CAACtC,eAAe,EAAEgB,KAAK,IACjC,CAAC,CAACsB,QAAQ,CAAClC,gBAAgB,EAAEY,KAAK,IAClC,CAAC,CAACsB,QAAQ,CAACjC,0BAA0B,EAAEW,KAAK,IAC5C,CAAC,CAACsB,QAAQ,CAAChC,YAAY,EAAEU,KAAK;EAElC;;qCA/IW/B,8BAA8B,EAAAlD,EAAA,CAAAwG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1G,EAAA,CAAAwG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5G,EAAA,CAAAwG,iBAAA,CAAAG,EAAA,CAAAE,QAAA,GAAA7G,EAAA,CAAAwG,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA/G,EAAA,CAAAwG,iBAAA,CAAAQ,EAAA,CAAAC,wBAAA;EAAA;;UAA9B/D,8BAA8B;IAAAgE,SAAA;IAAAC,MAAA;MAAA3D,QAAA;MAAAC,UAAA;MAAAC,cAAA;MAAAnD,YAAA;IAAA;IAAA6G,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTvCzH,EAHJ,CAAAC,cAAA,aAAkC,aAEW,aACR;QAC/BD,EAAA,CAAA2H,uBAAA,GAAc;QAEZ3H,EADA,CAAAQ,UAAA,IAAAoH,6CAAA,iBAAoB,IAAAC,6CAAA,iBACC;;QAEzB7H,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAItB;QADCD,EAAA,CAAAoC,UAAA,mBAAA0F,2DAAA;UAAA,OAASJ,GAAA,CAAArB,QAAA,EAAU;QAAA,EAAC;QAG1BrG,EAFK,CAAAG,YAAA,EAAI,EACD,EACF;QAGNH,EAAA,CAAAC,cAAA,aAGC;QAECD,EAAA,CAAAQ,UAAA,IAAAuH,6CAAA,iBAAsD;QAc5C/H,EAPV,CAAAC,cAAA,cAAiB,cACQ,eACD,cAGjB,cACsB,aAMlB;QADCD,EAAA,CAAAoC,UAAA,mBAAA4F,4DAAA;UAAA,OAASN,GAAA,CAAAhF,YAAA,CAAa,SAAS,CAAC;QAAA,EAAC;QAEjC1C,EAAA,CAAAE,MAAA,wBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAMlB;QADCD,EAAA,CAAAoC,UAAA,mBAAA6F,4DAAA;UAAA,OAASP,GAAA,CAAAhF,YAAA,CAAa,UAAU,CAAC;QAAA,EAAC;QAElC1C,EAAA,CAAAE,MAAA,yBACF;QAKVF,EALU,CAAAG,YAAA,EAAI,EACD,EACF,EACD,EACF,EACF;QAGNH,EAAA,CAAAC,cAAA,gBAAkE;QAAnCD,EAAA,CAAAoC,UAAA,sBAAA8F,kEAAA;UAAA,OAAYR,GAAA,CAAA5E,QAAA,EAAU;QAAA,EAAC;QA8GlD9C,EA5GA,CAAAQ,UAAA,KAAA2H,8CAAA,mBAAqC,KAAAC,8CAAA,kBA4GC;QAgD5CpI,EADE,CAAAG,YAAA,EAAO,EACH;QAKFH,EAFJ,CAAAC,cAAA,eAA4E,WACrE,kBAMF;QAFCD,EAAA,CAAAoC,UAAA,mBAAAiG,iEAAA;UAAA,OAASX,GAAA,CAAA/C,YAAA,EAAc;QAAA,EAAC;QAGxB3E,EAAA,CAAAE,MAAA,kBACF;QACFF,EADE,CAAAG,YAAA,EAAS,EACL;QAEJH,EADF,CAAAC,cAAA,WAAK,kBAMF;QAFCD,EAAA,CAAAoC,UAAA,mBAAAkG,iEAAA;UAAA,OAASZ,GAAA,CAAArB,QAAA,EAAU;QAAA,EAAC;QAGpBrG,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAUTH,EATA,CAAAQ,UAAA,KAAA+H,iDAAA,qBAMC,KAAAC,iDAAA,qBASA;QAMPxI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;QAnQQH,EAAA,CAAAI,SAAA,GAAY;QAAZJ,EAAA,CAAAsB,UAAA,SAAAoG,GAAA,CAAAzE,MAAA,CAAY;QACZjD,EAAA,CAAAI,SAAA,EAAa;QAAbJ,EAAA,CAAAsB,UAAA,UAAAoG,GAAA,CAAAzE,MAAA,CAAa;QAiBjBjD,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAsB,UAAA,SAAAoG,GAAA,CAAAnG,SAAA,CAAe;QAiBTvB,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAyI,eAAA,KAAAC,GAAA,EAAAhB,GAAA,CAAA/D,SAAA,gBAA+C;QAU/C3D,EAAA,CAAAI,SAAA,GAAgD;QAAhDJ,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAyI,eAAA,KAAAC,GAAA,EAAAhB,GAAA,CAAA/D,SAAA,iBAAgD;QAYtD3D,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAsB,UAAA,cAAAoG,GAAA,CAAAxG,UAAA,CAAwB;QAEpBlB,EAAA,CAAAI,SAAA,EAA6B;QAA7BJ,EAAA,CAAAsB,UAAA,SAAAoG,GAAA,CAAA/D,SAAA,eAA6B;QA4G7B3D,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAsB,UAAA,SAAAoG,GAAA,CAAA/D,SAAA,gBAA8B;QAyDpC3D,EAAA,CAAAI,SAAA,GAAiD;QAAjDJ,EAAA,CAAAsB,UAAA,aAAAoG,GAAA,CAAAnG,SAAA,IAAAmG,GAAA,CAAA/D,SAAA,eAAiD;QAUjD3D,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAsB,UAAA,aAAAoG,GAAA,CAAAnG,SAAA,CAAsB;QAKrBvB,EAAA,CAAAI,SAAA,GAA6B;QAA7BJ,EAAA,CAAAsB,UAAA,SAAAoG,GAAA,CAAA/D,SAAA,eAA6B;QAS7B3D,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAsB,UAAA,SAAAoG,GAAA,CAAA/D,SAAA,gBAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}