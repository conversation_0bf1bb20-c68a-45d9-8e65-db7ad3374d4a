import { Component, Input, Output, EventEmitter, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ProjectsService } from '../../services/projects.service';
import { UserService } from '../../services/user.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { AppService } from '../../services/app.service';

@Component({
  selector: 'app-project-popup',
  templateUrl: './project-popup.component.html',
  styleUrls: ['./project-popup.component.scss']
})
export class ProjectPopupComponent implements OnInit {
  @Input() id: number = 0; // 0 for new project, existing ID for edit
  @Input() project: any = null; // Project data for editing
  @Output() passEntry = new EventEmitter<boolean>();

  projectForm!: FormGroup;
  selectedTab: string = 'basic';
  isLoading: boolean = false;
  projectName: string = '';

  // Data arrays
  managers: any[] = [];
  loginUser: any = {};

  constructor(
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private projectsService: ProjectsService,
    private userService: UserService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private appService: AppService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loginUser = this.appService.getLoggedInUser();
    this.initializeForm();
    this.loadDropdownData();

    if (this.id !== 0 && this.project) {
      this.populateFormForEdit();
      this.projectName = this.project.projectName || '';
    }
  }

  private initializeForm(): void {
    this.projectForm = this.fb.group({
      projectName: ['', [Validators.required]],
      internalProjectNumber: [''],
      externalProjectNumber: [''],
      projectDescription: [''],
      manager: ['', [Validators.required]],
      clientName: [''],
      clientContactName: [''],
      clientContactPhone: [''],
      clientContactEmail: [''],
      projectAddress: [''],
      projectCity: [''],
      projectState: [''],
      projectZip: ['']
    });
  }

  private loadDropdownData(): void {
    this.loadManagers();
  }

  private loadManagers(): void {
    // Load managers/users using UserService
    this.userService.getAllUsers({
      pageSize: 1000,
      pageNumber: 1,
      sortField: 'userFullName',
      sortOrder: 'asc',
      filter: { search: '', columnFilter: [] }
    }).subscribe({
      next: (res: any) => {
        if (!res?.isFault) {
          this.managers = res.responseData?.data || res.data || [];
        }
      },
      error: (err) => {
        console.error('Error loading managers:', err);
        this.managers = [];
      }
    });
  }

  private populateFormForEdit(): void {
    if (this.project) {
      this.projectForm.patchValue({
        projectName: this.project.projectName || '',
        internalProjectNumber: this.project.internalProjectNumber || '',
        externalProjectNumber: this.project.externalProjectNumber || '',
        projectDescription: this.project.projectDescription || '',
        manager: this.project.managerId || this.project.manager || '',
        clientName: this.project.clientName || '',
        clientContactName: this.project.clientContactName || '',
        clientContactPhone: this.project.clientContactPhone || '',
        clientContactEmail: this.project.clientContactEmail || '',
        projectAddress: this.project.projectAddress || '',
        projectCity: this.project.projectCity || '',
        projectState: this.project.projectState || '',
        projectZip: this.project.projectZip || ''
      });
    }
  }

  controlHasError(validation: string, controlName: string): boolean {
    const control = this.projectForm.get(controlName);
    return !!(control && control.hasError(validation) && (control.dirty || control.touched));
  }

  private markFormGroupTouched(): void {
    Object.keys(this.projectForm.controls).forEach(key => {
      const control = this.projectForm.get(key);
      control?.markAsTouched();
    });
  }

  showTab(tab: string, event: any): void {
    event.preventDefault();
    this.selectedTab = tab;
    this.cdr.markForCheck();
  }

  goToNextTab(): void {
    if (this.selectedTab === 'basic') {
      this.selectedTab = 'role';
    }
  }

  goToPreviousTab(): void {
    if (this.selectedTab === 'role') {
      this.selectedTab = 'basic';
    }
  }

  changeInternalManager(managerId: any): void {
    // Handle manager change if needed
    console.log('Manager changed:', managerId);
  }

  save(): void {
    if (this.projectForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    const formData = this.projectForm.value;

    const saveObservable = this.id === 0
      ? this.projectsService.createProject(formData)
      : this.projectsService.updateProject({ ...formData, projectId: this.id });

    saveObservable.subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (!res?.isFault) {
          this.customLayoutUtilsService.showSuccess(
            this.id === 0 ? 'Project created successfully' : 'Project updated successfully',
            ''
          );
          this.passEntry.emit(true);
          this.modal.close(res.responseData || res);
        } else {
          this.customLayoutUtilsService.showError(
            res.faultMessage || 'An error occurred while saving the project',
            ''
          );
        }
        this.cdr.markForCheck();
      },
      error: (err) => {
        this.isLoading = false;
        console.error('Error saving project:', err);
        this.customLayoutUtilsService.showError(
          'An error occurred while saving the project',
          ''
        );
        this.cdr.markForCheck();
      }
    });
  }
}