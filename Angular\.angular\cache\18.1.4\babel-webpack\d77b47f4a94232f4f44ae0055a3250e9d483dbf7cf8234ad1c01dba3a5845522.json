{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AuthRoutingModule } from './auth-routing.module';\nimport { TranslationModule } from '../i18n/translation.module';\nimport { HttpUtilsService } from '../services/http-utils.service';\nimport * as i0 from \"@angular/core\";\nexport let AuthModule = /*#__PURE__*/(() => {\n  class AuthModule {\n    static ɵfac = function AuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [HttpUtilsService],\n      imports: [CommonModule, TranslationModule, AuthRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule]\n    });\n  }\n  return AuthModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}