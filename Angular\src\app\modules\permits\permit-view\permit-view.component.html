<!-- Full Screen Loading Overlay -->
<div *ngIf="isLoading" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 text-primary fs-5">Loading...</div>
  </div>
</div>

<div class="permit-view-container">
  <!-- Permit Details Card -->
  <div class="card shadow-sm rounded-3" *ngIf="permit">
    <!-- Permit Details Header -->
    <div class="permit-details-header">
      <div class="header-content w-100">
        <div class="title-wrap">
          <div class="title-line">
            <span class="permit-title">Permit # {{
              permit.permitNumber || ""
              }}</span>
            <span class="status-text status-under-review">{{ permit.permitReviewType || ""
              }}</span>
          </div>
          <div class="permit-number-line">
            {{ permit.permitName || "" }}
          </div>
        </div>
        <div class="button-group">
          <button type="button" class="btn btn-sm btn-light-primary d-flex align-items-center" (click)="goBack()">
            <i class="fas fa-arrow-left me-2"></i>
            Back
          </button>
        </div>
      </div>
    </div>
    <!-- Card Header with Tabs -->
    <div class="card-header border-0 py-2 d-flex justify-content-between align-items-center">
      <!-- Tabs -->
      <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap">
        <li class="nav-item">
          <a class="nav-link text-active-primary me-6 cursor-pointer" [ngClass]="{ active: selectedTab === 'details' }"
            (click)="showTab('details', $event)">
            Permit Details
          </a>
        </li>
        <li class="nav-item" *ngIf="isInternalReviewEnabled()">
          <a class="nav-link text-active-primary me-6 cursor-pointer" [ngClass]="{ active: selectedTab === 'internal' }"
            (click)="showTab('internal', $event)">
            Internal Reviews
          </a>
        </li>
        <li class="nav-item" *ngIf="isExternalReviewEnabled()">
          <a class="nav-link text-active-primary me-6 cursor-pointer" [ngClass]="{ active: selectedTab === 'external' }"
            (click)="showTab('external', $event)">
            External Reviews
          </a>
        </li>
      </ul>

      <!-- Right side buttons -->
      <div class="d-flex align-items-center gap-2" style="margin-right: 16px;">
        <!-- Edit icon - only show when permit details tab is active -->
        <button type="button" class="btn btn-link p-0" (click)="editPermit()" *ngIf="selectedTab === 'details'" title="Edit Permit">
          <i class="fas fa-edit text-primary" style="font-size: 1.1rem;"></i>
        </button>
      </div>

      <!-- Action Buttons -->
      <div class="button-group" *ngIf="selectedTab === 'internal' && isInternalReviewEnabled()">
        <button type="button" class="btn btn-link p-0 me-3" (click)="downloadInternalReviewsPdf()" [disabled]="isLoading || auditEntries.length === 0" title="Download Internal Reviews PDF">
          <i class="fas fa-download download-pdf-icon" style="color:#3699ff"></i>
        </button>
        <button class="btn btn-success btn-sm" (click)="addPopUp()" [disabled]="isLoading">
          <i class="bi bi-plus-lg"></i>Add Review
        </button>
      </div>
       <div class="button-group" *ngIf="selectedTab === 'external' && isExternalReviewEnabled()">
         <button type="button" class="btn btn-secondary btn-sm me-3" (click)="toggleAllSubmittals()" [disabled]="isLoading || externalSubmittals.length === 0" [title]="areAllSubmittalsExpanded() ? 'Collapse all submittals' : 'Expand all submittals'">
           <i class="fas" [ngClass]="areAllSubmittalsExpanded() ? 'fa-compress-arrows-alt' : 'fa-expand-arrows-alt'"></i>
           <span class="ms-1">{{ areAllSubmittalsExpanded() ? 'Collapse All' : 'Expand All' }}</span>
         </button>
         <button type="button" class="btn btn-primary btn-sm me-3" (click)="syncPermits(true)" [disabled]="isLoading">
           <i class="fas fa-sync-alt"></i> Sync
         </button>
         <button type="button" class="btn btn-secondary btn-sm" 
                 (click)="goToPortal()" 
                 [disabled]="!permit?.permitEntityID"
                 [title]="permit?.permitEntityID ? 'Open Portal' : 'Portal not available - Permit Entity ID required'">
           <i class="fas fa-external-link-alt"></i> Portal
         </button>
       </div>
    </div>


    <!-- Card Body with Tab Content -->
    <div class="card-body">
      <!-- Permit Details Tab Content -->
      <ng-container *ngIf="selectedTab == 'details' && permit">
      <div class=" permit-details-content">
        <div class="permit-details-grid">
          <div class="permit-detail-item">
            <label>Project Name</label>
            <span class="permit-value">{{ permit.projectName || "" }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Permit Type</label>
            <span class="permit-value">{{ permit.permitType || "" }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Primary Contact</label>
            <span class="permit-value">{{
              permit.primaryContact || ""
              }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Permit Status</label>
            <span class="status-text" [ngClass]="getStatusClass(permit.permitStatus)">{{ permit.permitStatus || ""
            }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Location</label>
            <span class="permit-value">{{ permit.location || "" }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Category</label>
            <span class="permit-value">{{
              permit.permitCategory || ""
              }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Issue Date</label>
            <span class="permit-value">{{
              permit.permitIssueDate
              ? (permit.permitIssueDate | date : "MM/dd/yyyy")
              : ""
              }}</span>
            <span class="text-gray-500 fs-7 p-0"> Applied on {{
              permit.permitAppliedDate
              ? (permit.permitAppliedDate | date : "MM/dd/yyyy")
              : ""
              }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Expiration Date</label>
            <span class="permit-value">{{
              permit.permitExpirationDate
              ? (permit.permitExpirationDate | date : "MM/dd/yyyy")
              : ""
              }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Final Date</label>
            <span class="permit-value">{{
              permit.permitFinalDate 
              ? (permit.permitFinalDate | date : "MM/dd/yyyy")
              : ""
              }}</span>
          </div>
          <div class="permit-detail-item">
            <label>Complete Date</label>
            <span class="permit-value">{{
              permit.permitCompleteDate
              ? (permit.permitCompleteDate | date : "MM/dd/yyyy")
              : ""
              }}</span>
          </div>
          <div class="permit-detail-item">
            <label>internal Review Status</label>
            <span class="status-text" [ngClass]="getStatusClass(permit.internalReviewStatus)">{{ permit.internalReviewStatus || ""
            }}</span>
          </div>
        </div>
      </div>
        
        <!-- Notes & Actions fields (inline with other details) -->
         <div class="permit-details-card"> 
          <div class="permit-details-header">
            <h4>Notes / Action</h4>
            <button type="button" class="btn btn-link p-0" (click)="onEdit(notesActionsTemplate)" title="Edit Notes/Actions">
              <i class="fas fa-edit text-primary" style="font-size: 1.1rem;"></i>
            </button>
          </div>
        <div class="permit-details-content">
          <div class="notes-actions-container">
            <div class="permit-detail-item-full">
              <label>Attention Reason</label>
            <span class="permit-value">{{ permit.attentionReason || "" }}</span>
          </div>
            <div class="permit-detail-item-full">
              <label>Internal Notes</label>
            <span class="permit-value">{{ permit.internalNotes || "" }}</span>
          </div>
            <div class="permit-detail-item-full">
            <label>Action Taken</label>
            <span class="permit-value">{{
              permit.actionTaken || ""
              }}</span>
          </div>
        </div>
      </div>
      </div>
      </ng-container>

      <!-- Internal Reviews Tab Content -->
      <ng-container *ngIf="selectedTab == 'internal' && permit && isInternalReviewEnabled()">
        <!-- Empty State for Internal Reviews -->
        <div class="d-flex justify-content-center align-items-center py-5 text-muted" *ngIf="auditEntries.length === 0">
          <div class="text-center">
            <i class="fas fa-clipboard-list fa-3x mb-3"></i>
            <p>No internal reviews found for this permit.</p>
          </div>
        </div>

        <!-- Internal Reviews Table -->
        <div class="table-responsive" *ngIf="auditEntries.length > 0">
          <table class="table table-hover">
            <tbody>
              <tr *ngFor="let audit of auditEntries; let i = index" [class.table-active]="selectedAuditIndex === i"
                (click)="selectAudit(i)" class="audit-table-row">
                <td class="audit-title-cell px-4">
                  <div class="d-flex align-items-center">
                    <h6 class="mb-0 me-3">{{ audit.title }}</h6>
                    <span class="audit-status-badge" 
                          [ngClass]="getStatusClass(audit.internalVerificationStatus)"
                          [ngStyle]="getStatusStyle(audit.internalVerificationStatus)">
                      {{ audit.internalVerificationStatus || 'Pending' }}
                    </span>
                  </div>
                  <div class="mt-1">
                    <small class="text-muted">{{ audit.typeCodeDrawing || '' }}</small>
                  </div>
                </td>
                <td class="audit-dates-cell px-3">
                  <div class="d-flex gap-4">
                    <div class="date-item">
                      <small class="text-muted d-block">Reviewed</small>
                      <span class="fw-medium">{{ audit.reviewedDate ? (audit.reviewedDate | date : "MM/dd/yyyy") : '' }}</span>
                    </div>
                    <div class="date-item">
                      <small class="text-muted d-block">Completed</small>
                      <span class="fw-medium">{{ audit.completedDate ? (audit.completedDate | date : "MM/dd/yyyy") : '' }}</span>
                    </div>
                    <div class="date-item">
                      <small class="text-muted d-block">Reviewer</small>
                      <span class="fw-medium">{{ audit.internalReviewer || '' }}</span>
                    </div>
                  </div>
                </td>
                <td class="audit-actions-cell px-4">
                  <i class="fas fa-edit action-icon edit-icon" (click)="editInternalReview(i); $event.stopPropagation()"
                    title="Edit Review"></i>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </ng-container>

      <!-- External Tab Content -->
      <ng-container *ngIf="selectedTab == 'external' && permit && isExternalReviewEnabled()">
         <div class="external-reviews-card">
           <div class="card shadow-sm rounded-3">
        <!-- Error State for External Reviews -->
           <div class="card-body" *ngIf="reviewsError">
             <div class="d-flex justify-content-center align-items-center py-5 text-danger">
          <div class="text-center">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <p>{{ reviewsError }}</p>
            <button class="btn btn-outline-primary btn-sm" (click)="fetchExternalReviews()" [disabled]="isLoading">
              <i class="fas fa-redo"></i> Retry
            </button>
               </div>
          </div>
        </div>

        <!-- Empty State for External Reviews -->
           <div class="card-body" *ngIf="!reviewsError && externalSubmittals.length === 0">
             <div class="d-flex justify-content-center align-items-center py-5 text-muted">
          <div class="text-center">
            <i class="fas fa-external-link-alt fa-3x mb-3"></i>
            <p>No external reviews found for this permit.</p>
               </div>
          </div>
        </div>

        <!-- External Reviews Table with Accordion -->
           <div class="card-body p-0" *ngIf="!reviewsError && externalSubmittals.length > 0">
             <div class="table-responsive external-reviews-table">
               <table class="table table-hover mb-0">
            <tbody>
              <ng-container *ngFor="let sub of externalSubmittals; let i = index">
                <!-- Submittal Row -->
                     <tr class="external-submittal-row" 
                       (click)="toggleSubmittalAccordion(i)"
                       [class.expanded]="isSubmittalExpanded(i)"
                       style="cursor: pointer;">
                  <td class="submittal-title-cell px-3">
                    <div class="d-flex align-items-center">
                           <div class="accordion-toggle me-2">
                        <i class="fas" [ngClass]="isSubmittalExpanded(i) ? 'fa-chevron-down' : 'fa-chevron-right'"></i>
                           </div>
                      <h6 class="mb-0 me-3">{{ sub.title }}</h6>
                      <span class="submittal-status-badge" [ngClass]="getStatusClass(sub.submittalStatus)">
                        {{ sub.submittalStatus }}
                      </span>
                    </div>
                  </td>
                  <td class="submittal-dates-cell px-3">
                    <div class="d-flex gap-4">
                      <div class="date-item">
                        <small class="text-muted d-block">Due</small>
                        <span class="fw-medium">{{ sub.dueDate | date : "MM/dd/yyyy" }}</span>
                      </div>
                      <div class="date-item">
                        <small class="text-muted d-block">Completed</small>
                        <span class="fw-medium">{{ sub.receivedDate | date : "MM/dd/yyyy" }}</span>
                      </div>
                    </div>
                  </td>
                </tr>
                <!-- Accordion Content for External Reviews - positioned directly below its submittal row -->
                <tr class="accordion-content-row" [class.d-none]="!isSubmittalExpanded(i)">
                  <td colspan="2" class="p-0">
                    <div class="accordion-content">
                      <div class="reviews-container p-3">
                        <div class="review-item" *ngFor="let review of getExternalReviewsForSubmittal(sub.id)">
                                <div class="review-single-line" 
                                  (click)="toggleReviewAccordion(review.commentsId)"
                                  [class.expanded]="isReviewExpanded(review.commentsId)"
                                  style="cursor: pointer;">
                                  <div class="review-accordion-toggle me-2">
                                    <i class="fas" [ngClass]="isReviewExpanded(review.commentsId) ? 'fa-chevron-down' : 'fa-chevron-right'"></i>
                                  </div>
                                  <h6 class="review-title" [style.color]="review.FailureFlag ? 'red' : 'green'">
                                    {{ review.name }}
                                  </h6>
                                  <div class="review-status-container">
                                    <span class="review-status" [ngClass]="getStatusClass(review.status)">
                                      {{ review.status || '' }}
                                    </span>
                                  </div>
                                  <div class="reviewer-container">
                                    <span class="reviewer">{{ review.reviewer || '' }}</span>
                                  </div>
                                  <div class="due-date-container">
                                    <span class="due-date">
                                      {{ review.dueDate ? ('Due: ' + (review.dueDate | date : "MM/dd/yyyy")) : '' }}
                                    </span>
                                  </div>
                                  <div class="completed-date-container">
                                    <span class="completed-date">
                                      {{ review.completedDate ? ('Completed: ' + (review.completedDate | date : "MM/dd/yyyy")) : '' }}
                                    </span>
                                  </div>
                                  <div class="review-actions-container">
                                    <i class="fas fa-download download-pdf-icon" 
                                      (click)="downloadReviewPDF(review); $event.stopPropagation()"
                                      title="Download Review PDF"></i>
                                  </div>
                                </div>
                                
                                <!-- Review Details Accordion Content -->
                                <div class="review-details-accordion" [class.d-none]="!isReviewExpanded(review.commentsId)">
                                  <div class="review-details-content">
                                    <!-- Corrections Section -->
                                    <div class="corrections-section p-3" *ngIf="review?.corrections && review.corrections.length > 0" style="padding-bottom: 0px !important;">
                                      <h6 class="section-title">Corrections ({{ review.corrections.length }})</h6>
                                      <div class="correction-item" *ngFor="let correction of review.corrections; let i = index">
                                        <div class="correction-header d-flex align-items-center">
                                          <div class="correction-number">{{ i + 1 }}</div>
                                          <div class="correction-meta flex-grow-1 ms-3 d-flex align-items-center justify-content-between">
                                            <div class="meta-fields d-flex align-items-center w-100">
                                              <div class="meta-field flex-fill">
                                                <span class="meta-label fw-bold">Correction Type: </span>
                                                <span class="meta-value">{{ correction.CorrectionTypeName || '' }}</span>
                                              </div>
                                              <div class="meta-field flex-fill">
                                                <span class="meta-label fw-bold">Category: </span>
                                                <span class="meta-value">{{ correction.CorrectionCategoryName || '' }}</span>
                                              </div>
                                              <div class="meta-field flex-fill">
                                                <span class="meta-label fw-bold">Resolved: </span>
                                                <span class="meta-value resolved-date">{{ correction.ResolvedDate ? (correction.ResolvedDate | date:'MM/dd/yyyy') : '' }}</span>
                                              </div>
                                            </div>
                                          </div>
                                          <div class="respond-buttons">
                                            <button class="btn btn-primary btn-sm me-3" 
                                              *ngIf="!correction.EORAOROwner_Response && !correction.commentResponsedBy && shouldShowEditResponseButton(correction)"
                                              (click)="openResponseModal(correction, review)"
                                              [disabled]="isLoading"
                                              title="Respond to this correction">
                                              Respond
                                            </button>
                                            <button class="btn btn-primary btn-sm me-3" 
                                              *ngIf="(correction.EORAOROwner_Response || correction.commentResponsedBy) && shouldShowEditResponseButton(correction)"
                                              (click)="openResponseModal(correction, review)"
                                              [disabled]="isLoading"
                                              title="Edit response to this correction">
                                              Edit Response
                                            </button>
                                          </div>
                                        </div>
                                        
                                        <div class="correction-content">
                                          <div class="correction-field">
                                            <label class="field-label">
                                              Corrective Action
                                            </label>
                                            <div class="field-content corrective-action">
                                              {{ correction.CorrectiveAction || '' }}
                                            </div>
                                          </div>
                                          
                                          <div class="correction-field">
                                            <label class="field-label">
                                              Comment
                                            </label>
                                            <div class="field-content comment">
                                              {{ correction.Comments || 'No comment provided' }}
                                            </div>
                                          </div>
                                          
                                          <div class="correction-field" *ngIf="correction.Response">
                                            <label class="field-label">
                                              Response
                                            </label>
                                            <div class="field-content response">
                                              {{ correction.Response }}
                                            </div>
                                          </div>
                                          
                                          <div class="correction-field" *ngIf="correction.EORAOROwner_Response">
                                            <label class="field-label">
                                              EOR / AOR / Owner Response
                                            </label>
                                            <div class="field-content eor-response">
                                              {{ correction.EORAOROwner_Response }}
                                            </div>
                                          </div>
                                          
                                          <div class="correction-field" *ngIf="correction.commentResponsedBy">
                                            <label class="field-label">
                                              Comment Responded By
                                            </label>
                                            <div class="field-content responded-by">
                                              {{ correction.commentResponsedBy }}
                                            </div>
                                          </div>
                                        </div>
                                        
                                        <div class="correction-separator" *ngIf="i < review.corrections.length - 1"></div>
                                      </div>
                                    </div>

                                    <!-- Comments Section (fallback when no corrections) -->
                                    <div class="comments-section p-3"
                                      *ngIf="(!review?.corrections || review.corrections.length === 0) && review?.comments">
                                      <h6 class="section-title">Comments</h6>
                                      <div class="comment-content">
                                        <div class="comment-text">{{ review.comments }}</div>
                                      </div>
                                    </div>

                                    <!-- No Data Message -->
                                    <div class="no-data-section p-3" 
                                      *ngIf="(!review?.corrections || review.corrections.length === 0) && !review?.comments">
                                      <div class="no-data-message text-center text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        <span> No corrections or comments available for this review.</span>
                                      </div>
                                    </div>
                                  </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
             </div>
           </div>
         </div>
        </div>
      </ng-container>
    </div>
  </div>

</div>


<ng-template #notesActionsTemplate let-permit="permit">
 <div class="modal-content h-auto">
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">
      <ng-container>
       
        <div>Edit Notes/Actions</div>
      </ng-container>
    </div>
    <div class="float-right">
      <i
        class="fa-solid fs-2 fa-xmark text-white"
        (click)="closModal()"
      ></i>
    </div>
  </div>

  <div
    class="modal-body medium-modal-body"
    
  >

  <form class="form form-label-right" [formGroup]="notesForm">
  <!-- Project Name -->
  <div class="row mt-4">
    <div class="col-xl-12">
      <div class="form-group">
        <label for="projectName" class="fw-bold form-label mb-2">
          Attention Reason
        </label>
        <textarea
          id="attentionReason"
          class="form-control form-control-sm"
          rows="2"
          formControlName="attentionReason"
          placeholder="Type here..."
        ></textarea>
      </div>
    </div>
  </div>

  <!-- Description -->
  <div class="row mt-4">
    <div class="col-xl-12">
      <div class="form-group">
        <label for="projectDescription" class="fw-bold form-label mb-2">
          Internal Notes
        </label>
        <textarea
          id="internalNotes"
          class="form-control form-control-sm"
          rows="3"
          formControlName="internalNotes"
          placeholder="Type here"
        ></textarea>
      </div>
    </div>
  </div>

  <!-- Internal Project Number & Start Date -->
  <div class="row mt-4">
    <div class="col-xl-12">
      <div class="form-group">
        <label for="internalProjectNo" class="fw-bold form-label mb-2">
          Action Taken 
        </label>
          <textarea
          id="internalNotes"
          class="form-control form-control-sm"
          rows="3"
          formControlName="actionTaken"
          placeholder="Type here"
        ></textarea>
      </div>
    </div>

    
  <!-- </div>
  <div class="row mt-4">
    <div class="col-xl-12">
      <div class="form-group">
        <label for="internalProjectNo" class="fw-bold form-label mb-2">
Status        </label>
        
 <ng-select></ng-select>

      </div>
    </div> -->

    
  </div>


</form>

  </div>

  <div class="modal-footer justify-content-end">
    <div>
      <button
        type="button"
        class="btn btn-danger btn-sm btn-elevate mr-2"
        (click)="closModal()"
      >
        Cancel</button
      >&nbsp;
      <button
        
        type="button"
        class="btn btn-primary btn-sm"
        
       (click)="editNotesandactions()"
      >Update
       <!-- (click)="save()" -->
        <!-- [disabled]="projectForm?.invalid" -->
        <!-- {{ id ? "Update" : "Save" }} -->
      </button>
     
    </div>
  </div>
</div>

</ng-template>


