<div class="modal-content h-auto">
    <div class="modal-header bg-light-primary">
        <div class="modal-title h5 fs-3" id="example-modal-sizes-title-lg">
            <ng-container>
                <div *ngIf="id ===0">Add User</div>
                <div *ngIf="id !==0">Edit User</div>
            </ng-container>
        </div>
        <div class="float-right">
            <!-- <a class="btn btn-icon  btn-sm pl-08 border-gray bg-danger" > -->
                <i class="fa-solid fs-2 fa-xmark text-white" (click)="onCancelClick()"></i>
            <!-- </a> -->
        </div>
    </div>
    <div class="modal-body large-modal-body" >
        <!-- style="max-height: calc(100vh - 250px); overflow-y: auto;" -->
        <ng-container>

             <div class="row">
                <div class="col-xl-12">
                    <div class="d-flex">
                        <ul
                            class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap">
                            <li class="nav-item">
                                <a class="nav-link text-active-primary me-6 cursor-pointer" data-toggle="tab"
                                    [ngClass]="{ 'active':selectedTab==='basic'}" (click)="showTab('basic', $event)">
                                    Profile
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-active-primary me-6  cursor-pointer " data-toggle="tab"
                                    [ngClass]="{ 'active':selectedTab==='role'}" (click)="showTab('role', $event)">
                                    Role & Access
                                </a>
                            </li>

                        </ul>
                    </div>
                </div>

            </div>

            <form class="form form-label-right" [formGroup]="userForm">
                <div class="card-body response-list" *ngIf="selectedTab==='basic'">

                    <div class="row mt-4">
                        <div class="col-xl-6">
                            <div class="form-group">
                                <label class="fw-bold form-label mb-2">First Name<sup
                                        class="text-danger">*</sup></label>
                                <input type="text" class="form-control form-control-sm" name="firstname"
                                    formControlName="firstname" placeholder="Type Here">
                                <span class="custom-error-css"
                                    *ngIf="controlHasError('required', 'firstname')">Required Field</span>
                            </div>
                        </div>
                        <div class="col-xl-6">
                            <div class="form-group">
                                <label class="fw-bold form-label mb-2">Last Name<sup class="text-danger">*</sup></label>
                                <input type="text" class="form-control form-control-sm" name="lastname"
                                    formControlName="lastname" placeholder="Type Here">
                                <span class="custom-error-css" *ngIf="controlHasError('pattern', 'lastname')">Required Field</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-xl-6">
                            <!-- <div class="col-xl-12">
                                <div class="form-group">
                                    <label class="fw-bold form-label mb-2">Company Name<sup
                                            class="text-danger">*</sup></label>
                                    <input type="text" class="form-control form-control-sm" name="companyname"
                                        formControlName="companyname" placeholder="Type Here">
                                    <span class="custom-error-css"
                                        *ngIf="controlHasError('required', 'companyname')">Required Field</span>
                                </div>
                            </div> -->

                            <div class="col-xl-12 mt-4">
                                <div class="form-group">
                                    <label class="fw-bold form-label mb-2">Email<sup class="text-danger">*</sup></label>
                                    <input type="text" class="form-control form-control-sm" name="email"
                                        formControlName="email" placeholder="Type Here">
                                        <span class="custom-error-css"
                                        *ngIf="controlHasError('email', 'email')">Enter a valid email</span>
                                </div>
                            </div>

                            <div class="col-xl-12 mt-4" *ngIf="id === 0">
                                <div class="row d-flex">
                                    <div class="col-xl-4">
                                    <div class="form-group">
                                        <label class="fw-bold form-label mb-2">Auto Password?</label>
                                        <div>
                                        <label class="form-check form-check-custom form-switch form-switch-sm mb-3 wd-fix">
                                            <input class="form-check-input" type="checkbox" name="IsAuto"
                                            (change)="onAutoPasswordChange($event)" formControlName="IsAuto" value=true />&nbsp;
                                        </label>
                                        </div>
                                    </div>
                                    </div>
                                    <div class="col-xl-8">
                                    <div class="form-group">
                                        <label class="fw-bold form-label mb-2">Password<sup class="text-danger">*</sup></label>
                                        <div class="input-group mb-0">
                                        <input [type]="newPasswordShown === true ?'password':'text'" class="form-control form-control-sm"
                                            name="password" placeholder="Type Here" autocomplete="off" formControlName="password" />
                                        <div class="input-group-text">
                                            <span [ngClass]="newPasswordShown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'"
                                            (click)="newPasswordShown===true? newshowpassword(false):newshowpassword(true)"></span>
                                        </div>
                                        </div>


                                    </div>
                                    <span class="custom-error-css" *ngIf="controlHasError('required', 'password')">Required
                                        Field</span>
                                    </div>
                                </div>
                            </div>


                            <div class="col-xl-12 mt-4">
                                <div class="form-group">
                                    <label class="fw-bold form-label mb-2">Phone</label>

                                    <igx-input-group>
                                        <input class="form-control form-control-sm" name="phone" id="tel" igxInput
                                            type="text" autocomplete="off" formControlName="phone"
                                            [igxMask]="'(************* Ext. 9999'" />
                                    </igx-input-group>
                                </div>
                            </div>

                            <div class="col-xl-12 mt-4">
                                <div class="form-group">
                                    <label class="fw-bold form-label mb-2">Title</label>

                                    <input type="text" class="form-control form-control-sm" name="title"
                                    formControlName="title" placeholder="Type Here">
                                </div>
                            </div>

                        </div>

                        <div class="col-xl-6">
                            <div class="col-xl-12 flex-center">
                                <div class="form-group row">
                                    <div class="col-lg-12">

                                        <label class="fs-5 fw-bold form-label">Profile Image</label>
                                        <br>
                                        <div class="d-flex justify-content-center w-100 mb-2">
                                            <div class="image-input image-input-outline" data-kt-image-input="true">
                                                <!--begin::Preview existing avatar-->
                                                <div class="image-input-wrapper w-150px h-150px"
                                                    style="background-image: url({{imageUrl}}); border-radius: 50%;"
                                                    *ngIf="image !==''">
                                                </div>
                                                <div class="image-input-wrapper bg-light-primary w-150px h-150px text-center"
                                                    *ngIf="image ==='' || image ===null" style="border-radius: 50%;">

                                                    <div class="image-input-wrapper w-150px h-150px"
                                                        style="background-image: url('./assets/media/svg/avatars/blank.svg');border-radius: 50%;">
                                                    </div>
                                                </div>
                                                <!--end::Preview existing avatar-->


                                                <!--begin::Label-->
                                                <label
                                                    class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                                    data-kt-image-input-action="change" data-bs-toggle="tooltip" title=""
                                                    data-bs-original-title="Change avatar">
                                                    <i class="bi bi-pencil-fill fs-7" (click)="imguploader.click();"></i>
                                                </label>
                                                <!--begin::Inputs-->
                                                <input class="visually-hidden" type="file" name="avatar" #imguploader
                                                    accept=".png, .jpg, .jpeg" >
                                                <!-- <input class="visually-hidden" type="file" name="avatar" #imguploader
                                                    accept=".png, .jpg, .jpeg" (change)="browseFile($event);"> -->
                                                <input type="hidden" name="avatar_remove">
                                                <!--end::Inputs-->
                                                <!--end::Label-->
                                                <!--begin::Cancel-->
                                                <span
                                                    class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title=""
                                                    data-bs-original-title="Cancel avatar">
                                                    <i class="bi bi-x fs-2"></i>
                                                </span>
                                                <!--end::Cancel-->
                                            </div>
                                        </div>

                                    </div>


                                </div>

                            </div>
                        </div>
                    </div>


                    <div class="row mt-4" *ngIf="id!==0">
                        <div class="col-xl-6">
                            <div class="form-group">
                                <label class="fw-bold form-label mb-2">Status <sup class="text-danger">*</sup></label>
                                <div class="d-flex mt-2">
                                    <div class="form-check form-check-inline form-check-sm">
                                        <input class="form-check-input" type="radio" value='true'
                                            name="status" id="Active" formControlName="status">
                                        <label class="form-check-label" for="Active">
                                            Active
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline form-check-sm ">
                                        <input class="form-check-input" type="radio" value='false'
                                            name="status" id="Inactive" formControlName="status">
                                        <label class="form-check-label" for="Inactive">
                                            Inactive
                                        </label>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-body response-list" *ngIf="selectedTab==='role'">
                    <div class="col-lg-6">
                                <label class="fw-semibold fs-6 mb-2">Role <sup class="text-danger">*</sup></label>
                                <ng-select [items]="roleArray" [clearable]="false" [multiple]="false" bindLabel="roleName"
                                    name="role" formControlName="role" bindValue="roleId"
                                    (change)="changeRoleAccess($event)" placeholder="Select an option">
                                </ng-select>
                                <span class="custom-error-css" *ngIf="appService.controlHasError('required', 'role',userForm)">Required
                                    Field</span>
                            </div>
                            <div class="form-group row mb-4 px-10 pt-2">
                                <div class="col-12 d-flex justify-content-start">
                                    <table class="w-100">
                                        <th class="p-1 fw-bold fs-6" style="width:390px !important;">Permissions</th>
                                        <th class="p-1 fw-bold fs-6" style="width:390px !important;">
                                            <div class="d-flex justify-content-between">
                                                <span>Read</span>
                                                <span>Write</span>
                                                <span>Delete</span>
                                            </div>
                                        </th>
                                        <tr *ngFor="let p of perNameArray">
                                            <td class="p-1">
                                                <label class="fw-semibold fs-6">{{p}}</label>
                                            </td>
                                            <td class="p-1">
                                                <checkbox-group ngDefaultControl formControlName="{{p}}" >
                                                    <checkbox value="Read">Read</checkbox>
                                                    <checkbox style="padding-left:145px" value="Write">Write</checkbox>
                                                    <checkbox style="padding-left:145px" value="Delete">Delete
                                                    </checkbox>
                                                </checkbox-group>

                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                </div>


            </form>
        </ng-container>
    </div>
      <div class="modal-footer justify-content-between">
        <div class="float-left">
            <button type="button" class="btn btn-secondary btn-elevate btn-sm" *ngIf="selectedTab==='role'"
                (click)="showTab('basic',$event)">Previous</button>
        </div>
        <div>
            <button type="button" class="btn btn-danger btn-sm btn-elevate mr-2"
                (click)="onCancelClick()">Cancel</button>
            &nbsp;
            <button type="button" class="btn btn-primary btn-elevate btn-sm" *ngIf="selectedTab==='basic'"
                (click)="showTab('role',$event)">Next</button>
            <button type="button" class="btn btn-primary btn-elevate btn-sm" *ngIf="selectedTab==='role'" [disabled]="userForm.invalid"
                (click)="save()">Save</button>
        </div>
    </div>
</div>
