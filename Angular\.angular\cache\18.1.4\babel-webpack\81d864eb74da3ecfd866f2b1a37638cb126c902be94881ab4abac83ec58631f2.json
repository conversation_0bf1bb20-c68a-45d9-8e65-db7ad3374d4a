{"ast": null, "code": "import { each } from 'lodash';\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/exceljs.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/http-utils.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/kendo-column.service\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"../../services/app.service\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c4 = () => ({\n  filter: true\n});\nconst _c5 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c6 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction ProjectListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"span\", 11);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 12);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"kendo-textbox\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"span\", 16);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 17);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 19);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"kendo-dropdownbutton\", 22);\n    i0.ɵɵlistener(\"itemClick\", function ProjectListComponent_ng_template_4_Template_kendo_dropdownbutton_itemClick_13_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onExportClick($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(15, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(17, \"i\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"data\", ctx_r2.exportOptions);\n  }\n}\nfunction ProjectListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"label\", 31);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_5_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(8, \"i\", 35);\n    i0.ɵɵtext(9, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_5_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(11, \"i\", 37);\n    i0.ɵɵtext(12, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n  }\n}\nfunction ProjectListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProjectListComponent_ng_template_5_div_0_Template, 13, 2, \"div\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"a\", 50);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.projectId));\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 52);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_3_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      const deleteModal_r7 = i0.ɵɵreference(9);\n      return i0.ɵɵresetView(dataItem_r6.isDeletable && ctx_r2.deletePop(deleteModal_r7, dataItem_r6.projectId, dataItem_r6.projectName));\n    });\n    i0.ɵɵelement(4, \"span\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r6 = ctx.dataItem;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"invisible\", !dataItem_r6.isDeletable)(\"disabled\", !dataItem_r6.isDeletable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen027.svg\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 47);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template, 5, 5, \"ng-template\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c5));\n    i0.ɵɵproperty(\"width\", 90)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(10, _c6))(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2191\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2193\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onColumnSort(\"projectName\"));\n    });\n    i0.ɵɵtext(1, \" Project Name \");\n    i0.ɵɵtemplate(2, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_2_Template, 2, 0, \"span\", 58)(3, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_3_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"projectName\"] === \"asc\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"projectName\"] === \"desc\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"strong\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r9.projectName);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r10 = ctx.$implicit;\n    const column_r11 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r11)(\"filter\", filter_r10)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 54);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template, 4, 2, \"ng-template\", 55)(2, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template, 2, 1, \"ng-template\", 48)(3, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_3_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 200)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"projectName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c6))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"projectName\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2191\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2193\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onColumnSort(\"internalProjectNumber\"));\n    });\n    i0.ɵɵtext(1, \" Internal Project # \");\n    i0.ɵɵtemplate(2, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_2_Template, 2, 0, \"span\", 58)(3, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_3_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"internalProjectNumber\"] === \"asc\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"internalProjectNumber\"] === \"desc\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r13 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r13.internalProjectNumber, \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r14 = ctx.$implicit;\n    const column_r15 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r15)(\"filter\", filter_r14)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 61);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template, 4, 2, \"ng-template\", 55)(2, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template, 1, 1, \"ng-template\", 48)(3, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_3_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"internalProjectNumber\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"internalProjectNumber\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r16 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r16.projectStartDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r17 = ctx.$implicit;\n    const column_r18 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r18)(\"filter\", filter_r17)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 62);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template, 5, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 110)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"projectStartDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectStartDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r19 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r19.projectEndDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r20 = ctx.$implicit;\n    const column_r21 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r21)(\"filter\", filter_r20)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 63);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template, 5, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 110)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"projectEndDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectEndDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r22 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r22.projectLocation, \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r23 = ctx.$implicit;\n    const column_r24 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r24)(\"filter\", filter_r23)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 64);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"projectLocation\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectLocation\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r25 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r25.internalProjectManagerName, \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r27 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r27)(\"filter\", filter_r26)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 65);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"internalProjectManagerName\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"internalProjectManagerName\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r28 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r28.externalPMNames, \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r29 = ctx.$implicit;\n    const column_r30 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r30)(\"filter\", filter_r29)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 66);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 220)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"externalPMNames\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"externalPMNames\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2191\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2193\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onColumnSort(\"lastUpdatedDate\"));\n    });\n    i0.ɵɵtext(1, \" Updated date \");\n    i0.ɵɵtemplate(2, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_2_Template, 2, 0, \"span\", 58)(3, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_3_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"lastUpdatedDate\"] === \"asc\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"lastUpdatedDate\"] === \"desc\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r32 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r32.lastUpdatedDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r33 = ctx.$implicit;\n    const column_r34 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r34)(\"filter\", filter_r33)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 67);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template, 4, 2, \"ng-template\", 55)(2, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_2_Template, 1, 1, \"ng-template\", 48)(3, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_3_Template, 5, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 110)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_1_Template, 2, 11, \"kendo-grid-column\", 38)(2, ProjectListComponent_ng_container_6_kendo_grid_column_2_Template, 4, 8, \"kendo-grid-column\", 39)(3, ProjectListComponent_ng_container_6_kendo_grid_column_3_Template, 4, 6, \"kendo-grid-column\", 40)(4, ProjectListComponent_ng_container_6_kendo_grid_column_4_Template, 3, 6, \"kendo-grid-column\", 41)(5, ProjectListComponent_ng_container_6_kendo_grid_column_5_Template, 3, 6, \"kendo-grid-column\", 42)(6, ProjectListComponent_ng_container_6_kendo_grid_column_6_Template, 3, 6, \"kendo-grid-column\", 43)(7, ProjectListComponent_ng_container_6_kendo_grid_column_7_Template, 3, 6, \"kendo-grid-column\", 44)(8, ProjectListComponent_ng_container_6_kendo_grid_column_8_Template, 3, 6, \"kendo-grid-column\", 45)(9, ProjectListComponent_ng_container_6_kendo_grid_column_9_Template, 4, 6, \"kendo-grid-column\", 46);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r35 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"internalProjectNumber\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectStartDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectEndDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectLocation\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"internalProjectManagerName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"externalPMNames\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"lastUpdatedDate\");\n  }\n}\nfunction ProjectListComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70);\n    i0.ɵɵelement(2, \"i\", 71);\n    i0.ɵɵelementStart(3, \"p\", 16);\n    i0.ɵɵtext(4, \"No projects found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_7_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 73);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectListComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProjectListComponent_ng_template_7_div_0_Template, 8, 0, \"div\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading === false && ctx_r2.serverSideRowData.length === 0);\n  }\n}\nfunction ProjectListComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"h5\", 75);\n    i0.ɵɵtext(2, \"Confirm Delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_3_listener() {\n      const modal_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDeleteCancelClick(modal_r38));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 77)(5, \"p\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 79)(8, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_8_listener() {\n      const modal_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDeleteCancelClick(modal_r38));\n    });\n    i0.ɵɵtext(9, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_10_listener() {\n      const modal_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.confirmDelete();\n      return i0.ɵɵresetView(modal_r38.close());\n    });\n    i0.ɵɵtext(11, \" Delete \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Are you sure you want to delete this project? - \", ctx_r2.projectName, \" \");\n  }\n}\nexport class ProjectListComponent {\n  router;\n  execeljsservice;\n  route;\n  projectsService;\n  httpUtilService;\n  customLayoutUtilsService;\n  kendoColumnService;\n  modalService;\n  cdr;\n  appService;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Active',\n    value: 'Active'\n  }, {\n    text: 'Inactive',\n    value: 'Inactive'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }],\n    centers: []\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // Column visibility system\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration\n  gridColumns = [];\n  defaultColumns = [];\n  fixedColumns = [];\n  draggableColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Enhanced Columns with Kendo UI features\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Action',\n    width: 100,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'projectName',\n    title: 'Project name',\n    width: 200,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  }, {\n    field: 'internalProjectNumber',\n    title: 'Internal project #',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 3\n  }, {\n    field: 'projectStartDate',\n    title: 'Start date',\n    width: 110,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 4\n  }, {\n    field: 'projectEndDate',\n    title: 'End date',\n    width: 110,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 5\n  }, {\n    field: 'projectLocation',\n    title: 'Location',\n    width: 150,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 6\n  }, {\n    field: 'internalProjectManagerName',\n    title: 'Internal manager',\n    width: 150,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 7\n  }, {\n    field: 'externalPMNames',\n    title: 'External PM',\n    width: 220,\n    type: 'status',\n    isFixed: false,\n    filterable: true,\n    order: 8\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated date',\n    width: 110,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 9\n  }];\n  // State\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  // Custom sort state tracking for three-state cycle\n  columnSortStates = {\n    'lastUpdatedDate': 'desc' // Default sort state\n  };\n  page = {\n    size: 15,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  // Selection\n  selectedRows = [];\n  isAllSelected = false;\n  // Export options\n  exportOptions = [{\n    text: 'All',\n    value: 'all'\n  }, {\n    text: 'Page Results',\n    value: 'selected'\n  }\n  // { text: 'Export Filtered', value: 'filtered' },\n  ];\n  projectName;\n  projectId;\n  constructor(router, execeljsservice, route, projectsService, httpUtilService, customLayoutUtilsService, kendoColumnService, modalService, cdr, appService) {\n    this.router = router;\n    this.execeljsservice = execeljsservice;\n    this.route = route;\n    this.projectsService = projectsService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.kendoColumnService = kendoColumnService;\n    this.modalService = modalService;\n    this.cdr = cdr;\n    this.appService = appService;\n    // Initialize search subscription with debounced search\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n      // Set loading state immediately for search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    });\n  }\n  ngOnInit() {\n    this.initializeComponent();\n    this.loadTable();\n  }\n  ngAfterViewInit() {\n    this.initializeGrid();\n  }\n  ngOnDestroy() {\n    if (this.searchSubscription) {\n      this.searchTerms.complete();\n    }\n  }\n  initializeComponent() {\n    // Get login user info\n    // this.loginUser = this.customLayoutUtils.getLoginUser();\n    this.loginUser = this.appService.getLoggedInUser();\n    // Initialize column visibility system\n    this.initializeColumnVisibility();\n  }\n  initializeColumnVisibility() {\n    // Set up column arrays first\n    this.setupColumnArrays();\n    // Try to load from local storage first\n    const savedConfig = this.kendoColumnService.getFromLocalStorage('projects', this.loginUser.userId);\n    if (savedConfig) {\n      // Load saved settings from local storage\n      this.kendoHide = savedConfig.hiddenData || [];\n      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.kendoColOrder];\n    } else {\n      // Initialize with default values\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.defaultColumns];\n    }\n    // Apply settings\n    this.applySavedColumnSettings();\n  }\n  loadColumnSettingsFromServer() {\n    const config = {\n      pageName: 'projects',\n      userID: this.loginUser.userId\n    };\n    this.kendoColumnService.getHideFields(config).subscribe({\n      next: response => {\n        if (response.isFault === false && response.Data) {\n          // Parse the saved settings\n          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.kendoColOrder];\n          // Apply the settings\n          this.applySavedColumnSettings();\n          console.log('Column settings loaded from server:', {\n            kendoHide: this.kendoHide,\n            kendoColOrder: this.kendoColOrder\n          });\n        } else {\n          // No saved settings, use defaults\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      },\n      error: error => {\n        console.error('Error loading column settings:', error);\n        // Use defaults on error\n        this.kendoHide = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n        this.applySavedColumnSettings();\n      }\n    });\n  }\n  setupColumnArrays() {\n    this.gridColumns = this.gridColumnConfig.map(col => col.field);\n    this.defaultColumns = [...this.gridColumns];\n    this.fixedColumns = this.gridColumnConfig.filter(col => col.isFixed).map(col => col.field);\n    this.draggableColumns = this.gridColumnConfig.filter(col => !col.isFixed).map(col => col.field);\n    console.log('Column arrays initialized:', {\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      fixedColumns: this.fixedColumns,\n      draggableColumns: this.draggableColumns\n    });\n  }\n  initializeGrid() {\n    if (this.grid) {\n      // Apply saved column settings\n      this.applySavedColumnSettings();\n    }\n  }\n  applySavedColumnSettings() {\n    if (this.kendoHide && this.kendoHide.length > 0) {\n      this.hiddenFields = this.kendoHide;\n    }\n    if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n      // Apply column order\n      this.gridColumnConfig.sort((a, b) => {\n        const aOrder = this.kendoColOrder.indexOf(a.field);\n        const bOrder = this.kendoColOrder.indexOf(b.field);\n        return aOrder - bOrder;\n      });\n    }\n  }\n  // Load table data\n  loadTable() {\n    this.loadTableWithKendoEndpoint();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Safety timeout to prevent loader from getting stuck\n    const loadingTimeout = setTimeout(() => {\n      console.warn('Loading timeout reached, resetting loading states');\n      this.resetLoadingStates();\n    }, 15000); // 15 seconds timeout - reduced from 30 seconds\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId\n    };\n    console.log('Search request state:', {\n      searchTerm: this.searchData,\n      state: state\n    });\n    this.projectsService.getProjectsForKendoGrid(state).subscribe({\n      next: data => {\n        // Clear the safety timeout since we got a response\n        clearTimeout(loadingTimeout);\n        console.log('API Response:', data);\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          // Check if this is an authentication error\n          if (data.responseData?.status === 401 || data.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n          this.handleEmptyResponse();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const projectData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = projectData.length !== 0;\n          this.serverSideRowData = projectData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n          // Create a data source with total count for Kendo Grid\n          this.gridData = {\n            data: projectData,\n            total: total\n          };\n          console.log('this.serverSideRowData ', this.serverSideRowData);\n          console.log('this.gridData ', this.gridData);\n          console.log('this.IsListHasValue ', this.IsListHasValue);\n          console.log('this.page ', this.page);\n          this.cdr.markForCheck();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      },\n      error: error => {\n        // Clear the safety timeout since we got an error\n        clearTimeout(loadingTimeout);\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        // Check if this is an authentication error\n        if (error && typeof error === 'object' && 'status' in error) {\n          const httpError = error;\n          if (httpError.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n        }\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        // Clear the safety timeout\n        clearTimeout(loadingTimeout);\n        // Ensure loading states are reset in complete block as well\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      }\n    });\n  }\n  handleEmptyResponse() {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n    // Ensure loading states are reset when handling empty response\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  // Method to manually reset loading states if they get stuck\n  resetLoadingStates() {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  // Public method to manually refresh the grid and reset any stuck loading states\n  refreshGrid() {\n    console.log('Manually refreshing grid...');\n    this.resetLoadingStates();\n    this.loadTable();\n  }\n  // Search functionality\n  onSearchKeyDown(event) {\n    if (event.key === 'Enter') {\n      console.log('Search triggered by Enter key:', this.searchData);\n      // Set loading state immediately for search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    }\n  }\n  onSearchChange() {\n    console.log('Search changed:', this.searchData);\n    // Trigger search with debounce\n    this.searchTerms.next(this.searchData);\n  }\n  applySearch() {\n    // Set loading state immediately for search\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  clearSearch() {\n    // Clear search data and reset table\n    this.searchData = '';\n    // Set loading state for clearing search\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    // Reload table without search filter\n    this.loadTable();\n  }\n  // Test method for External PM search\n  testExternalPMSearch() {\n    console.log('Testing External PM search...');\n    this.searchData = 'External PM'; // Test search term\n    // Set loading state immediately for test search\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  // Filter functionality\n  filterChange(filter) {\n    this.filter = filter;\n    // Set loading state immediately for filtering\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  applyAdvancedFilters() {\n    // Apply status filter\n    if (this.appliedFilters.status) {\n      this.filter.filters = this.filter.filters.filter(f => {\n        if ('field' in f) {\n          return f.field !== 'projectStatus';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'projectStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    // Apply center filter\n    if (this.appliedFilters.center) {\n      this.filter.filters = this.filter.filters.filter(f => {\n        if ('field' in f) {\n          return f.field !== 'centerId';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'centerId',\n        operator: 'eq',\n        value: this.appliedFilters.center\n      });\n    }\n    this.loadTable();\n  }\n  clearAdvancedFilters() {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n    // Set loading state immediately for clearing filters\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  clearAllFilters() {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n    // Set loading state immediately for clearing filters\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  // Custom sort handler for three-state cycle\n  onColumnSort(field) {\n    console.log('Custom sort triggered for field:', field);\n    // Get current sort state for this column\n    const currentState = this.columnSortStates[field] || 'none';\n    // Determine next state in cycle: none -> asc -> desc -> none\n    let nextState;\n    let nextDir;\n    switch (currentState) {\n      case 'none':\n        nextState = 'asc';\n        nextDir = 'asc';\n        break;\n      case 'asc':\n        nextState = 'desc';\n        nextDir = 'desc';\n        break;\n      case 'desc':\n        nextState = 'none';\n        nextDir = 'desc'; // Will be overridden to default\n        break;\n    }\n    // Update column sort state\n    this.columnSortStates[field] = nextState;\n    // Clear other columns' sort states (single column sorting)\n    Object.keys(this.columnSortStates).forEach(key => {\n      if (key !== field) {\n        this.columnSortStates[key] = 'none';\n      }\n    });\n    // Set sort based on state\n    if (nextState === 'none') {\n      // Return to default sorting and clear the column sort state\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.columnSortStates[field] = 'none'; // Ensure it's set to none\n      console.log('Returning to default sort');\n    } else {\n      // Apply the new sort\n      this.sort = [{\n        field: field,\n        dir: nextDir\n      }];\n      console.log('Applying sort:', this.sort);\n    }\n    // Update page order fields\n    this.page.orderBy = this.sort[0].field;\n    this.page.orderDir = this.sort[0].dir;\n    // Reset to first page\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    console.log('Final sort state:', this.sort);\n    console.log('Column sort states:', this.columnSortStates);\n    this.loadTable();\n  }\n  // Data state change handler (includes sorting and pagination)\n  onDataStateChange(event) {\n    console.log('Data state change:', event);\n    // Handle pagination only (sorting handled by custom method)\n    if (event.skip !== undefined) {\n      this.skip = event.skip;\n      this.page.pageNumber = Math.floor(event.skip / this.page.size);\n    }\n    if (event.take !== undefined) {\n      this.page.size = event.take;\n    }\n    this.loadTable();\n  }\n  // Sorting functionality (kept for compatibility)\n  onSortChange(sort) {\n    console.log('Sort change triggered:', sort);\n    this.sort = sort;\n    this.loadTable();\n  }\n  // Pagination functionality\n  pageChange(event) {\n    // Use Kendo's provided values as source of truth\n    this.skip = event.skip;\n    this.page.size = event.take || this.page.size;\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\n    // Set loading state immediately for pagination\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  // Column management\n  onColumnReorder(event) {\n    // Handle column reordering\n    const reorderedColumns = event.columns.map(col => col.field);\n    this.kendoColOrder = reorderedColumns;\n  }\n  updateColumnVisibility(event) {\n    // Handle column visibility changes\n    const hiddenColumns = event.hiddenColumns || [];\n    this.hiddenFields = hiddenColumns;\n    // Update gridColumns to reflect visible columns only\n    this.gridColumns = this.defaultColumns.filter(column => !hiddenColumns.includes(column));\n    console.log('Column visibility updated:', {\n      hiddenColumns: hiddenColumns,\n      visibleColumns: this.gridColumns,\n      allColumns: this.defaultColumns\n    });\n  }\n  getHiddenField(fieldName) {\n    return this.hiddenFields.includes(fieldName);\n  }\n  // Selection functionality\n  onSelectionChange(event) {\n    this.selectedRows = event.selectedRows || [];\n    this.isAllSelected = this.selectedRows.length === this.serverSideRowData.length;\n  }\n  selectAll() {\n    if (this.isAllSelected) {\n      this.selectedRows = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedRows = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n  }\n  // Grid expansion\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Export functionality\n  // public onExportClick(event: any): void {\n  //   const exportType = event.item.value;\n  //   let selectedIds: number[] = [];\n  //   switch (exportType) {\n  //     case 'selected':\n  //       selectedIds = this.selectedRows.map((row) => row.projectId);\n  //       if (selectedIds.length === 0) {\n  //         //alert('Please select projects to export');\n  //         return;\n  //       }\n  //       break;\n  //     case 'filtered':\n  //       // Export filtered data\n  //       break;\n  //     case 'all':\n  //     default:\n  //       // Export all data\n  //       break;\n  //   }\n  //   this.exportProjects(exportType, selectedIds);\n  // }\n  // private exportProjects(exportType: string, selectedIds: number[]): void {\n  //   this.projectsService.exportProjects(exportType, selectedIds).subscribe({\n  //     next: (response: any) => {\n  //       if (response.data) {\n  //         const blob = new Blob([response.data], {\n  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  //         });\n  //         saveAs(\n  //           blob,\n  //           `projects_${exportType}_${\n  //             new Date().toISOString().split('T')[0]\n  //           }.xlsx`\n  //         );\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       console.error('Export error:', error);\n  //       //alert('Error exporting projects data');\n  //     },\n  //   });\n  // }\n  onExportClick(event) {\n    const selectedOption = event.value; // Get selected option\n    let prdItems = [];\n    if (selectedOption === 'selected') {\n      prdItems = this.serverSideRowData;\n      // declare the title and header data for excel\n      // get the data for excel in a array format\n      this.exportExcel(prdItems);\n    } else if (selectedOption === 'all') {\n      const queryparamsExcel = {\n        pageSize: this.page.totalElements,\n        sortOrder: this.page.orderDir,\n        sortField: this.page.orderBy,\n        pageNumber: this.page.pageNumber\n        // filter: this.filterConfiguration()\n      };\n      // Enable loading indicator\n      this.httpUtilService.loadingSubject.next(true);\n      // API call\n      this.projectsService.getAllProjects(queryparamsExcel)\n      // .pipe(map((data: any) => data as any))\n      .subscribe(data => {\n        // Disable loading indicator\n        this.httpUtilService.loadingSubject.next(false);\n        if (data.isFault) {\n          this.IsListHasValue = false;\n          this.cdr.markForCheck();\n          return; // Exit early if the response has a fault\n        }\n        this.IsListHasValue = true;\n        prdItems = data.responseData.data || [];\n        this.cdr.detectChanges(); // Manually trigger UI update\n        this.exportExcel(prdItems);\n      });\n    }\n  }\n  exportExcel(listOfItems) {\n    // Define local variables for the items and current date\n    let prdItems = listOfItems;\n    let currentDate = this.appService.formatMonthDate(new Date());\n    console.log('prdItems', prdItems);\n    // Check if the data exists and is not empty\n    if (prdItems !== undefined && prdItems.length > 0) {\n      // Define the title for the Excel file\n      const tableTitle = 'Events';\n      // Filter out hidden columns and sort by order\n      // const visibleColumns = this.columnJSONFormat\n      //   .filter((col: any) => !col.hidden)\n      //   .sort((a: any, b: any) => a.order - b.order);\n      // Create header from visible columns\n      const headerArray = ['Project Name', 'Internal Projet #', 'Start Date', 'End Date', 'Location', 'Manager', 'External PM'];\n      // ...visibleColumns.map((col: any) => col.title),\n      // Define which columns should have currency and percentage formatting\n      // const currencyColumns: any = [\n      //   'Pending',\n      //   'ACAT',\n      //   'Annuity',\n      //   'AUM',\n      //   'Total Assets',\n      //   'Event Cost',\n      //   'Gross Profit',\n      // ].filter((col) => headerArray.includes(col));\n      const percentageColumns = [];\n      // Get the data for excel in an array format\n      const respResult = [];\n      // Prepare the data for export based on visible columns\n      each(prdItems, prdItem => {\n        // Create an array with the same length as headerArray\n        const respData = Array(headerArray.length).fill(null);\n        respData[0] = prdItem.eventDescription;\n        respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n        // Fill in data for each visible column\n        headerArray.forEach((col, i) => {\n          const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n          switch (col) {\n            case 'Project Name':\n              respData[adjustedIndex] = prdItem.projectName;\n              break;\n            case 'Internal Projet #':\n              respData[adjustedIndex] = prdItem.internalProjectNumber;\n              break;\n            case 'Start Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectStartDate);\n              break;\n            case 'End Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectEndDate);\n              break;\n            case 'Location':\n              respData[adjustedIndex] = prdItem.projectLocation;\n              break;\n            case 'Manager':\n              respData[adjustedIndex] = prdItem.internalProjectManagerName;\n              break;\n            case 'External PM':\n              respData[adjustedIndex] = prdItem.externalPMNames;\n              break;\n            // case 'kept_appointments':\n            //   respData[adjustedIndex] = prdItem.kept_appointments;\n            //   break;\n            // case 'kept_appt_ratio':\n            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n            //   break;\n            // case 'apptKeptNo':\n            //   respData[adjustedIndex] = prdItem.apptKeptNo;\n            //   break;\n            // case 'has_assets':\n            //   respData[adjustedIndex] = prdItem.has_assets;\n            //   break;\n            // case 'prospects_closed':\n            //   respData[adjustedIndex] = prdItem.prospects_closed;\n            //   break;\n            // case 'closing_ratio':\n            //   respData[adjustedIndex] = prdItem.closing_ratio;\n            //   break;\n            // case 'totalPending':\n            //   respData[adjustedIndex] = prdItem.totalPending;\n            //   break;\n            // case 'acatproduction':\n            //   respData[adjustedIndex] = prdItem.acatproduction;\n            //   break;\n            // case 'annuityproduction':\n            //   respData[adjustedIndex] = prdItem.annuityproduction;\n            //   break;\n            // case 'aumproduction':\n            //   respData[adjustedIndex] = prdItem.aumproduction;\n            //   break;\n            // case 'totalAssets':\n            //   respData[adjustedIndex] = prdItem.totalAssets;\n            //   break;\n            // case 'eventCost':\n            //   respData[adjustedIndex] = prdItem.eventCost;\n            //   break;\n            // case 'grossProfit':\n            //   respData[adjustedIndex] = prdItem.grossProfit;\n            //   break;\n            // case 'status':\n            //   respData[adjustedIndex] = prdItem.status;\n            //   break;\n          }\n        });\n        respResult.push(respData);\n      });\n      // Define column sizes for the Excel file\n      const colSize = headerArray.map((header, index) => ({\n        id: index + 1,\n        width: 20\n      }));\n      // Generate the Excel file using the exceljsService\n      this.execeljsservice.generateExcel(tableTitle, headerArray, respResult, colSize\n      // currencyColumns,\n      // percentageColumns\n      );\n    } else {\n      const message = 'There are no records available to export.';\n      // this.layoutUtilService.showError(message, '');\n    }\n  }\n  // Column settings management\n  saveHead() {\n    const settings = {\n      kendoHide: this.hiddenFields,\n      kendoColOrder: this.kendoColOrder,\n      kendoInitColOrder: this.kendoInitColOrder\n    };\n    // Save to local storage only\n    this.kendoColumnService.saveToLocalStorage({\n      pageName: 'projects',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    });\n    console.log('Column settings saved locally:', settings);\n    this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n    //alert('Column settings saved locally');\n  }\n  saveColumnSettingsToServer(settings) {\n    const config = {\n      pageName: 'projects',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    };\n    this.kendoColumnService.createHideFields(config).subscribe({\n      next: response => {\n        if (response.isFault === false) {\n          console.log('Column settings saved successfully:', response);\n          this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n          //alert('Column settings saved successfully');\n        } else {\n          console.error('Failed to save column settings:', response.message);\n          this.customLayoutUtilsService.showError('Column settings failed to save', '');\n          //alert('Failed to save column settings: ' + response.message);\n        }\n      },\n      error: error => {\n        console.error('Error saving column settings:', error);\n        this.customLayoutUtilsService.showError('Error saving column settings', '');\n        //alert('Error saving column settings. Please try again.');\n      }\n    });\n  }\n  saveResetToServer() {\n    // First delete existing settings\n    const deleteConfig = {\n      pageName: 'projects',\n      userID: this.loginUser.userId\n    };\n    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n      next: response => {\n        console.log('Existing settings deleted:', response);\n        // Then save the reset state (all columns visible)\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      },\n      error: error => {\n        console.error('Error deleting existing settings:', error);\n        // Still try to save the reset state\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      }\n    });\n  }\n  resetTable() {\n    console.log('Resetting Kendo settings for projects');\n    // Clear all saved settings first\n    this.kendoHide = [];\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.kendoInitColOrder = [];\n    // Clear local storage\n    this.kendoColumnService.clearFromLocalStorage('projects');\n    // Reset to default settings\n    this.resetToDefaultSettings();\n    // Trigger change detection to update the template\n    this.cdr.detectChanges();\n    // Force grid refresh to show all columns\n    if (this.grid) {\n      this.grid.refresh();\n    }\n    // Show success message\n    console.log('Table reset to default settings');\n    //alert('Table reset to default settings - all columns restored');\n  }\n  resetToDefaultSettings() {\n    console.log('Resetting to default settings...');\n    // Reset column visibility - show all columns\n    this.hiddenFields = [];\n    this.gridColumns = [...this.defaultColumns];\n    this.kendoColOrder = [...this.defaultColumns];\n    // Reset sort state to default\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    // Reset page state\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Reset all filters - clear everything\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    // Reset advanced filters\n    this.appliedFilters = {};\n    // Reset search\n    this.searchData = '';\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n    console.log('Reset completed:', {\n      hiddenFields: this.hiddenFields,\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      sort: this.sort,\n      filter: this.filter,\n      searchData: this.searchData\n    });\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset column visibility - show all columns\n      this.grid.columns.forEach(column => {\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Trigger change detection\n    this.cdr.detectChanges();\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        // Also try to reset the grid state completely\n        this.grid.reset();\n      }, 100);\n    }\n    // Reload data with clean state\n    this.loadTable();\n  }\n  // Navigation\n  add() {\n    this.edit(0);\n  }\n  view(projectId) {\n    this.router.navigate(['/projects/view', projectId]);\n  }\n  edit(projectId) {\n    if (projectId == 0) {\n      // Open modal for new project\n      const modalRef = this.modalService.open(ProjectPopupComponent, {\n        size: 'lg',\n        centered: true,\n        backdrop: 'static'\n      });\n      modalRef.componentInstance.id = projectId;\n      modalRef.componentInstance.project = null;\n      modalRef.componentInstance.passEntry.subscribe(result => {\n        if (result) {\n          // Refresh the grid after successful add\n          this.loadTable();\n        }\n      });\n    } else {\n      // Navigate to project view for existing projects\n      this.router.navigate(['/projects/view', projectId]);\n    }\n  }\n  delete(projectId) {\n    // if (confirm('Are you sure you want to delete this project?')) {\n    // }\n    this.projectsService.deleteProject({\n      projectId\n    }).subscribe({\n      next: response => {\n        console.log(\"response\", response);\n        if (!response.isFault) {\n          this.customLayoutUtilsService.showSuccess('Project deleted successfully', '');\n          //alert('Project deleted successfully');\n          this.loadTable();\n        }\n      },\n      error: error => {\n        console.error('Delete error:', error);\n        this.customLayoutUtilsService.showError('error deleting project', '');\n        //alert('Error deleting project');\n      }\n    });\n  }\n  // Utility methods\n  getProjectFullName(project) {\n    return `${project.projectFirstName || ''} ${project.projectLastName || ''}`.trim();\n  }\n  getCenterName(project) {\n    return project.medicalCenter?.centerName || '';\n  }\n  formatDate(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const year = date.getFullYear();\n    return `${month}/${day}/${year}`;\n  }\n  getStatusClass(status) {\n    return status === 'Active' ? 'badge-light-success' : 'badge-light-danger';\n  }\n  deletePop(content, projectId, projectName) {\n    this.projectName = projectName;\n    this.projectId = projectId;\n    this.modalService.open(content, {\n      centered: true\n    });\n  }\n  // Method to handle cancel button click in delete modal\n  onDeleteCancelClick(modalRef) {\n    // Reset loading state when cancel is clicked\n    this.httpUtilService.loadingSubject.next(false);\n    modalRef.dismiss();\n  }\n  confirmDelete() {\n    // console.log('Item deleted ✅');\n    this.delete(this.projectId);\n    // your delete logic here\n  }\n  onTabActivated() {\n    // This method is called when the tab is activated\n    // You can add any specific logic here if needed\n    console.log('Projects tab activated');\n  }\n  static ɵfac = function ProjectListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ExceljsService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.HttpUtilsService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.KendoColumnService), i0.ɵɵdirectiveInject(i7.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i8.AppService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectListComponent,\n    selectors: [[\"app-project-list\"]],\n    viewQuery: function ProjectListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    decls: 10,\n    vars: 22,\n    consts: [[\"normalGrid\", \"\"], [\"deleteModal\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"dataStateChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"total\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\", \"loading\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"text\", \"Export Excel\", \"iconClass\", \"fas fa-file-excel\", \"title\", \"Export\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"itemClick\", \"data\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"projectName\", \"title\", \"Project name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"internalProjectNumber\", \"title\", \"Internal Project #\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectStartDate\", \"title\", \"Start Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectEndDate\", \"title\", \"End Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectLocation\", \"title\", \"Location\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"internalProjectManagerName\", \"title\", \"Internal Manager\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"externalPMNames\", \"title\", \"External PM\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", 2, \"min-height\", \"32px\"], [\"title\", \"View\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"32px\", \"height\", \"32px\", 3, \"click\"], [1, \"fas\", \"fa-pencil\", 2, \"color\", \"var(--bs-primary, #0d6efd)\", \"font-size\", \"1rem\"], [\"title\", \"Delete\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"32px\", \"height\", \"32px\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", 3, \"inlineSVG\"], [\"field\", \"projectName\", \"title\", \"Project name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridHeaderTemplate\", \"\"], [\"kendoGridFilterMenuTemplate\", \"\"], [2, \"cursor\", \"pointer\", \"user-select\", \"none\", 3, \"click\"], [\"style\", \"color: red; font-weight: bold;\", 4, \"ngIf\"], [2, \"color\", \"red\", \"font-weight\", \"bold\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"internalProjectNumber\", \"title\", \"Internal Project #\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectStartDate\", \"title\", \"Start Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectEndDate\", \"title\", \"End Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectLocation\", \"title\", \"Location\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"internalProjectManagerName\", \"title\", \"Internal Manager\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"externalPMNames\", \"title\", \"External PM\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"fas\", \"fa-folder-open\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"], [1, \"modal-header\", \"bg-danger\", \"text-white\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"delete-modal-body\", \"mt-4\", \"text-center\"], [1, \"fs-5\"], [1, \"modal-footer\", \"delete-modal-footer\", \"ms-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function ProjectListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, ProjectListComponent_div_0_Template, 7, 0, \"div\", 2);\n        i0.ɵɵelementStart(1, \"div\", 3)(2, \"kendo-grid\", 4, 0);\n        i0.ɵɵlistener(\"columnReorder\", function ProjectListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onColumnReorder($event));\n        })(\"selectionChange\", function ProjectListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function ProjectListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function ProjectListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"dataStateChange\", function ProjectListComponent_Template_kendo_grid_dataStateChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDataStateChange($event));\n        })(\"columnVisibilityChange\", function ProjectListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(4, ProjectListComponent_ng_template_4_Template, 18, 11, \"ng-template\", 5)(5, ProjectListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 5)(6, ProjectListComponent_ng_container_6_Template, 10, 9, \"ng-container\", 6)(7, ProjectListComponent_ng_template_7_Template, 1, 1, \"ng-template\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, ProjectListComponent_ng_template_8_Template, 12, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.gridData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(18, _c2, i0.ɵɵpureFunction0(17, _c1)))(\"total\", ctx.page.totalElements)(\"sortable\", false)(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(20, _c3))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(21, _c4))(\"loading\", false);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n      }\n    },\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 9999;\\n  background: white;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  min-height: 250px;\\n}\\n\\n.delete-modal-footer[_ngcontent-%COMP%] {\\n  min-height: 10px;\\n}\\n\\n.delete-modal-body[_ngcontent-%COMP%] {\\n  min-height: 52px;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #646367;\\n  box-shadow: 0 0 6px rgba(57, 58, 58, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 6px;\\n}\\n\\n[_nghost-%COMP%]     .k-grid {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header {\\n  background: #f8f9fa;\\n  border-bottom: 2px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header {\\n  background: #f8f9fa;\\n  border-color: #dee2e6;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar {\\n  background: #f8f9fa;\\n  border-bottom: 1px solid #dee2e6;\\n  padding: 1rem;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button {\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary {\\n  background: #007bff;\\n  border-color: #007bff;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary:hover {\\n  background: #0056b3;\\n  border-color: #0056b3;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager {\\n  background: #f8f9fa;\\n  border-top: 1px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-info {\\n  color: #6c757d;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link {\\n  border-radius: 4px;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected {\\n  background: #007bff;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  font-size: 0.75rem !important;\\n  padding: 0.25rem 0.5rem !important;\\n  background-color: #6c757d !important;\\n  border-color: #6c757d !important;\\n  color: #fff !important;\\n  margin-right: 0.5rem !important;\\n  transition: all 0.2s ease !important;\\n  height: auto !important;\\n  min-height: 31px !important;\\n}\\n[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button:hover {\\n  background-color: #5a6268 !important;\\n  border-color: #545b62 !important;\\n  transform: translateY(-1px) !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\\n}\\n[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5) !important;\\n}\\n[_nghost-%COMP%]     .k-textbox {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-textbox:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n[_nghost-%COMP%]     .k-dropdownlist {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-dropdownlist:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 0.5em 0.75em;\\n  font-size: 0.75em;\\n  font-weight: 600;\\n  border-radius: 6px;\\n}\\n.badge.badge-light-success[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n.badge.badge-light-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n}\\n.badge.badge-light-danger[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n.badge.badge-light-info[_ngcontent-%COMP%] {\\n  background: #d1ecf1;\\n  color: #0c5460;\\n}\\n.badge.badge-light-secondary[_ngcontent-%COMP%] {\\n  background: #e2e3e5;\\n  color: #383d41;\\n}\\n.badge.badge-light-primary[_ngcontent-%COMP%] {\\n  background: #cce7ff;\\n  color: #004085;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n.btn.btn-success[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  border-color: #28a745;\\n}\\n.btn.btn-success[_ngcontent-%COMP%]:hover {\\n  background: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%] {\\n  background: #ffc107;\\n  border-color: #ffc107;\\n  color: #212529;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%]:hover {\\n  background: #e0a800;\\n  border-color: #d39e00;\\n}\\n.btn.btn-info[_ngcontent-%COMP%] {\\n  background: #17a2b8;\\n  border-color: #17a2b8;\\n}\\n.btn.btn-info[_ngcontent-%COMP%]:hover {\\n  background: #138496;\\n  border-color: #138496;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #6c757d;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background: #6c757d;\\n  color: white;\\n}\\n\\n.btn-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  padding: 0;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.btn-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] {\\n  padding: 4px 8px;\\n  vertical-align: middle;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex {\\n  min-height: 32px;\\n  align-items: center;\\n  justify-content: center;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon {\\n  flex-shrink: 0;\\n  margin: 0 2px;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon.invisible {\\n  visibility: hidden;\\n  pointer-events: none;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon.disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n\\n.text-muted[_ngcontent-%COMP%]   .fas[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.text-muted[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  margin-top: 1rem;\\n}\\n.text-muted[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], \\n   .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n}\\n.grid-container[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_expandGrid 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_expandGrid {\\n  from {\\n    opacity: 0.8;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["each", "Subject", "debounceTime", "distinctUntilChanged", "ProjectPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "ProjectListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchData", "ɵɵresetView", "ɵɵlistener", "ProjectListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "onSearchChange", "ɵɵelement", "ProjectListComponent_ng_template_4_Template_button_click_8_listener", "add", "ProjectListComponent_ng_template_4_Template_button_click_11_listener", "toggleExpand", "ProjectListComponent_ng_template_4_Template_kendo_dropdownbutton_itemClick_13_listener", "onExportClick", "ProjectListComponent_ng_template_4_Template_button_click_14_listener", "resetTable", "ProjectListComponent_ng_template_4_Template_button_click_16_listener", "refreshGrid", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "exportOptions", "ProjectListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r4", "appliedFilters", "status", "ProjectListComponent_ng_template_5_div_0_Template_button_click_7_listener", "applyAdvancedFilters", "ProjectListComponent_ng_template_5_div_0_Template_button_click_10_listener", "clearAllFilters", "advancedFilterOptions", "ɵɵtemplate", "ProjectListComponent_ng_template_5_div_0_Template", "showAdvancedFilters", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener", "dataItem_r6", "_r5", "dataItem", "edit", "projectId", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_3_listener", "deleteModal_r7", "ɵɵreference", "isDeletable", "deletePop", "projectName", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c5", "fixedColumns", "includes", "_c6", "getHiddenField", "ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template_div_click_0_listener", "_r8", "onColumnSort", "ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_3_Template", "columnSortStates", "dataItem_r9", "column_r11", "filter_r10", "ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_3_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template_div_click_0_listener", "_r12", "ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_3_Template", "ɵɵtextInterpolate1", "dataItem_r13", "internalProjectNumber", "column_r15", "filter_r14", "ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_3_Template", "formatDate", "dataItem_r16", "projectStartDate", "column_r18", "filter_r17", "ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template", "dataItem_r19", "projectEndDate", "column_r21", "filter_r20", "ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template", "dataItem_r22", "projectLocation", "column_r24", "filter_r23", "ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template", "dataItem_r25", "internalProjectManagerName", "column_r27", "filter_r26", "ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template", "dataItem_r28", "externalPMNames", "column_r30", "filter_r29", "ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template_div_click_0_listener", "_r31", "ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_3_Template", "dataItem_r32", "lastUpdatedDate", "column_r34", "filter_r33", "ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_3_Template", "ɵɵelementContainerStart", "ProjectListComponent_ng_container_6_kendo_grid_column_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_3_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_4_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_5_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_6_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_7_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_8_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_9_Template", "column_r35", "ProjectListComponent_ng_template_7_div_0_Template_button_click_5_listener", "_r36", "loadTable", "ProjectListComponent_ng_template_7_div_0_Template", "loading", "serverSideRowData", "length", "ProjectListComponent_ng_template_8_Template_button_click_3_listener", "modal_r38", "_r37", "$implicit", "onDeleteCancelClick", "ProjectListComponent_ng_template_8_Template_button_click_8_listener", "ProjectListComponent_ng_template_8_Template_button_click_10_listener", "confirmDelete", "close", "ProjectListComponent", "router", "execeljsservice", "route", "projectsService", "httpUtilService", "customLayoutUtilsService", "kendoColumnService", "modalService", "cdr", "appService", "grid", "gridData", "IsListHasValue", "isLoading", "loginUser", "searchTerms", "searchSubscription", "filter", "logic", "filters", "gridFilter", "activeFilters", "filterOptions", "text", "value", "centers", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "draggableColumns", "normalGrid", "expandedGrid", "gridColumnConfig", "field", "title", "width", "isFixed", "type", "order", "filterable", "sort", "dir", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "selectedRows", "isAllSelected", "constructor", "pipe", "subscribe", "loadingSubject", "next", "ngOnInit", "initializeComponent", "ngAfterViewInit", "initializeGrid", "ngOnDestroy", "complete", "getLoggedInUser", "initializeColumnVisibility", "setupColumnArrays", "savedConfig", "getFromLocalStorage", "userId", "applySavedColumnSettings", "loadColumnSettingsFromServer", "config", "pageName", "userID", "getHideFields", "response", "<PERSON><PERSON><PERSON>", "Data", "hideData", "JSON", "parse", "console", "log", "error", "map", "col", "a", "b", "aOrder", "indexOf", "b<PERSON>rder", "loadTableWithKendoEndpoint", "loadingTimeout", "setTimeout", "warn", "resetLoadingStates", "state", "take", "search", "loggedInUserId", "searchTerm", "getProjectsForKendoGrid", "data", "clearTimeout", "responseData", "errors", "handleEmptyResponse", "projectData", "total", "Math", "ceil", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "httpError", "event", "key", "applySearch", "clearSearch", "testExternalPMSearch", "filterChange", "f", "push", "operator", "center", "clearAdvancedFilters", "currentState", "nextState", "nextDir", "Object", "keys", "for<PERSON>ach", "onDataStateChange", "undefined", "floor", "onSortChange", "pageChange", "onColumnReorder", "reorderedColumns", "columns", "updateColumnVisibility", "hiddenColumns", "column", "visibleColumns", "allColumns", "fieldName", "onSelectionChange", "selectAll", "gridContainer", "document", "querySelector", "classList", "toggle", "refresh", "selectedOption", "prdItems", "exportExcel", "queryparamsExcel", "pageSize", "sortOrder", "sortField", "getAllProjects", "detectChanges", "listOfItems", "currentDate", "formatMonthDate", "Date", "tableTitle", "headerArray", "percentageColumns", "respResult", "prdItem", "respData", "Array", "fill", "eventDescription", "event_date", "i", "adjustedIndex", "colSize", "header", "index", "id", "generateExcel", "message", "saveHead", "settings", "saveToLocalStorage", "LoggedId", "showSuccess", "saveColumnSettingsToServer", "createHideFields", "showError", "saveResetToServer", "deleteConfig", "deleteHideFields", "clearFromLocalStorage", "resetToDefaultSettings", "hidden", "reset", "view", "navigate", "modalRef", "open", "centered", "backdrop", "componentInstance", "project", "passEntry", "result", "delete", "deleteProject", "getProjectFullName", "projectFirstName", "projectLastName", "trim", "getCenterName", "medicalCenter", "centerName", "dateString", "date", "month", "getMonth", "toString", "padStart", "day", "getDate", "year", "getFullYear", "getStatusClass", "content", "dismiss", "onTabActivated", "ɵɵdirectiveInject", "i1", "Router", "i2", "ExceljsService", "ActivatedRoute", "i3", "ProjectsService", "i4", "HttpUtilsService", "i5", "CustomLayoutUtilsService", "i6", "KendoColumnService", "i7", "NgbModal", "ChangeDetectorRef", "i8", "AppService", "selectors", "viewQuery", "ProjectListComponent_Query", "rf", "ctx", "ProjectListComponent_div_0_Template", "ProjectListComponent_Template_kendo_grid_columnReorder_2_listener", "_r1", "ProjectListComponent_Template_kendo_grid_selectionChange_2_listener", "ProjectListComponent_Template_kendo_grid_filterChange_2_listener", "ProjectListComponent_Template_kendo_grid_pageChange_2_listener", "ProjectListComponent_Template_kendo_grid_dataStateChange_2_listener", "ProjectListComponent_Template_kendo_grid_columnVisibilityChange_2_listener", "ProjectListComponent_ng_template_4_Template", "ProjectListComponent_ng_template_5_Template", "ProjectListComponent_ng_container_6_Template", "ProjectListComponent_ng_template_7_Template", "ProjectListComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-list\\project-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-list\\project-list.component.html"], "sourcesContent": ["import { formatDate } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectorRef,\n  Component,\n  OnDestroy,\n  OnInit,\n  ViewChild,\n} from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\n\nimport saveAs from 'file-saver';\nimport { add, each } from 'lodash';\nimport {\n  Subject,\n  Subscription,\n  debounceTime,\n  distinctUntilChanged,\n  filter,\n} from 'rxjs';\nimport { AppService } from '../../services/app.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { KendoColumnService } from '../../services/kendo-column.service';\nimport {\n  CompositeFilterDescriptor,\n  SortDescriptor,\n} from '@progress/kendo-data-query';\nimport { ProjectsService } from '../../services/projects.service';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport { ExceljsService } from '../../services/exceljs.service';\n\n@Component({\n  selector: 'app-project-list',\n  templateUrl: './project-list.component.html',\n  styleUrl: './project-list.component.scss',\n})\nexport class ProjectListComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('normalGrid') grid: any;\n\n  // Data\n  public serverSideRowData: any[] = [];\n  public gridData: any = [];\n  public IsListHasValue: boolean = false;\n\n  public loading: boolean = false;\n  public isLoading: boolean = false;\n\n  loginUser: any = {};\n\n  // Search\n  public searchData: string = '';\n  private searchTerms = new Subject<string>();\n  private searchSubscription: Subscription;\n\n  // Enhanced Filters for Kendo UI\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public activeFilters: Array<{\n    field: string;\n    operator: string;\n    value: any;\n  }> = [];\n\n  public filterOptions: Array<{ text: string; value: string | null }> = [\n    { text: 'All', value: null },\n    { text: 'Active', value: 'Active' },\n    { text: 'Inactive', value: 'Inactive' },\n  ];\n\n  // Advanced filter options\n  public advancedFilterOptions = {\n    status: [\n      { text: 'All', value: null },\n      { text: 'Active', value: 'Active' },\n      { text: 'Inactive', value: 'Inactive' },\n    ] as Array<{ text: string; value: string | null }>,\n    centers: [] as Array<{ text: string; value: string | null }>,\n  };\n\n  // Filter state\n  public showAdvancedFilters = false;\n  public appliedFilters: {\n    status?: string | null;\n    center?: string | null;\n  } = {};\n\n  // Column visibility system\n  public kendoHide: any;\n  public hiddenData: any = [];\n  public kendoColOrder: any = [];\n  public kendoInitColOrder: any = [];\n  public hiddenFields: any = [];\n\n  // Column configuration\n  public gridColumns: string[] = [];\n  public defaultColumns: string[] = [];\n  public fixedColumns: string[] = [];\n  public draggableColumns: string[] = [];\n  public normalGrid: any;\n  public expandedGrid: any;\n  public isExpanded = false;\n\n  // Enhanced Columns with Kendo UI features\n  public gridColumnConfig: Array<{\n    field: string;\n    title: string;\n    width: number;\n    isFixed: boolean;\n    type: string;\n    filterable?: boolean;\n    order: number;\n  }> = [\n    {\n      field: 'action',\n      title: 'Action',\n      width: 100,\n      isFixed: true,\n      type: 'action',\n      order: 1,\n    },\n    {\n      field: 'projectName',\n      title: 'Project name',\n      width: 200,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2,\n    },\n    {\n      field: 'internalProjectNumber',\n      title: 'Internal project #',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 3,\n    },\n    {\n      field: 'projectStartDate',\n      title: 'Start date',\n      width: 110,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 4,\n    },\n    {\n      field: 'projectEndDate',\n      title: 'End date',\n      width: 110,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5,\n    },\n    {\n      field: 'projectLocation',\n      title: 'Location',\n      width: 150,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6,\n    },\n    {\n      field: 'internalProjectManagerName',\n      title: 'Internal manager',\n      width: 150,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 7,\n    },\n    {\n      field: 'externalPMNames',\n      title: 'External PM',\n      width: 220,\n      type: 'status',\n      isFixed: false,\n      filterable: true,\n      order: 8,\n    },\n    {\n      field: 'lastUpdatedDate',\n      title: 'Updated date',\n      width: 110,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 9,\n    },\n  ];\n\n  // State\n  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n  \n  // Custom sort state tracking for three-state cycle\n  public columnSortStates: { [key: string]: 'none' | 'asc' | 'desc' } = {\n    'lastUpdatedDate': 'desc' // Default sort state\n  };\n\n  public page: any = {\n    size: 15,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc',\n  };\n\n  public skip: number = 0;\n\n  // Selection\n  public selectedRows: any[] = [];\n  public isAllSelected: boolean = false;\n\n  // Export options\n  public exportOptions = [\n    { text: 'All', value: 'all' },\n    { text: 'Page Results', value: 'selected' },\n    // { text: 'Export Filtered', value: 'filtered' },\n  ];\n  projectName: any;\n  projectId: any;\n\n  constructor(\n    private router: Router,\n    private execeljsservice: ExceljsService,\n    private route: ActivatedRoute,\n    private projectsService: ProjectsService,\n    private httpUtilService: HttpUtilsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private kendoColumnService: KendoColumnService,\n    private modalService: NgbModal,\n    private cdr: ChangeDetectorRef,\n    public appService: AppService\n  ) {\n    // Initialize search subscription with debounced search\n    this.searchSubscription = this.searchTerms\n      .pipe(debounceTime(500), distinctUntilChanged())\n      .subscribe(() => {\n        // Set loading state immediately for search\n        this.loading = true;\n        this.isLoading = true;\n        this.httpUtilService.loadingSubject.next(true);\n        \n        this.loadTable();\n      });\n  }\n\n  ngOnInit(): void {\n    this.initializeComponent();\n    this.loadTable();\n  }\n\n  ngAfterViewInit(): void {\n    this.initializeGrid();\n  }\n\n  ngOnDestroy(): void {\n    if (this.searchSubscription) {\n      this.searchTerms.complete();\n    }\n  }\n\n  private initializeComponent(): void {\n    // Get login user info\n    // this.loginUser = this.customLayoutUtils.getLoginUser();\n    this.loginUser = this.appService.getLoggedInUser();\n    // Initialize column visibility system\n    this.initializeColumnVisibility();\n  }\n\n  private initializeColumnVisibility(): void {\n    // Set up column arrays first\n    this.setupColumnArrays();\n    \n    // Try to load from local storage first\n    const savedConfig = this.kendoColumnService.getFromLocalStorage('projects', this.loginUser.userId);\n    \n    if (savedConfig) {\n      // Load saved settings from local storage\n      this.kendoHide = savedConfig.hiddenData || [];\n      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.kendoColOrder];\n    } else {\n      // Initialize with default values\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.defaultColumns];\n    }\n    \n    // Apply settings\n    this.applySavedColumnSettings();\n  }\n\n  private loadColumnSettingsFromServer(): void {\n    const config = {\n      pageName: 'projects',\n      userID: this.loginUser.userId\n    };\n\n    this.kendoColumnService.getHideFields(config).subscribe({\n      next: (response) => {\n        if (response.isFault === false && response.Data) {\n          // Parse the saved settings\n          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.kendoColOrder];\n          \n          // Apply the settings\n          this.applySavedColumnSettings();\n          \n          console.log('Column settings loaded from server:', {\n            kendoHide: this.kendoHide,\n            kendoColOrder: this.kendoColOrder\n          });\n        } else {\n          // No saved settings, use defaults\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      },\n      error: (error) => {\n        console.error('Error loading column settings:', error);\n        // Use defaults on error\n        this.kendoHide = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n        this.applySavedColumnSettings();\n      }\n    });\n  }\n\n  private setupColumnArrays(): void {\n    this.gridColumns = this.gridColumnConfig.map((col) => col.field);\n    this.defaultColumns = [...this.gridColumns];\n    this.fixedColumns = this.gridColumnConfig\n      .filter((col) => col.isFixed)\n      .map((col) => col.field);\n    this.draggableColumns = this.gridColumnConfig\n      .filter((col) => !col.isFixed)\n      .map((col) => col.field);\n\n    console.log('Column arrays initialized:', {\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      fixedColumns: this.fixedColumns,\n      draggableColumns: this.draggableColumns\n    });\n  }\n\n  private initializeGrid(): void {\n    if (this.grid) {\n      // Apply saved column settings\n      this.applySavedColumnSettings();\n    }\n  }\n\n  private applySavedColumnSettings(): void {\n    if (this.kendoHide && this.kendoHide.length > 0) {\n      this.hiddenFields = this.kendoHide;\n    }\n\n    if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n      // Apply column order\n      this.gridColumnConfig.sort((a, b) => {\n        const aOrder = this.kendoColOrder.indexOf(a.field);\n        const bOrder = this.kendoColOrder.indexOf(b.field);\n        return aOrder - bOrder;\n      });\n    }\n  }\n\n  // Load table data\n  public loadTable(): void {\n    this.loadTableWithKendoEndpoint();\n  }\n\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n\n    // Safety timeout to prevent loader from getting stuck\n    const loadingTimeout = setTimeout(() => {\n      console.warn('Loading timeout reached, resetting loading states');\n      this.resetLoadingStates();\n    }, 15000); // 15 seconds timeout - reduced from 30 seconds\n\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId,\n    };\n\n    console.log('Search request state:', {\n      searchTerm: this.searchData,\n      state: state\n    });\n\n    this.projectsService.getProjectsForKendoGrid(state).subscribe({\n      next: (data: {\n        isFault?: boolean;\n        responseData?: {\n          data: any[];\n          total: number;\n          errors?: string[];\n          status?: number;\n        };\n        data?: any[];\n        total?: number;\n        errors?: string[];\n        status?: number;\n      }) => {\n        // Clear the safety timeout since we got a response\n        clearTimeout(loadingTimeout);\n\n        console.log('API Response:', data);\n\n        // Handle the new API response structure\n        if (\n          data.isFault ||\n          (data.responseData &&\n            data.responseData.errors &&\n            data.responseData.errors.length > 0)\n        ) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n\n          // Check if this is an authentication error\n          if (data.responseData?.status === 401 || data.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n\n          this.handleEmptyResponse();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const projectData = responseData.data || [];\n          const total = responseData.total || 0;\n\n          this.IsListHasValue = projectData.length !== 0;\n          this.serverSideRowData = projectData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n          \n          // Create a data source with total count for Kendo Grid\n          this.gridData = {\n            data: projectData,\n            total: total\n          };\n          console.log('this.serverSideRowData ', this.serverSideRowData);\n          console.log('this.gridData ', this.gridData);\n          console.log('this.IsListHasValue ', this.IsListHasValue);\n          console.log('this.page ', this.page);\n          this.cdr.markForCheck();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      },\n      error: (error: unknown) => {\n        // Clear the safety timeout since we got an error\n        clearTimeout(loadingTimeout);\n\n        console.error('Error loading data with Kendo UI endpoint:', error);\n\n        // Check if this is an authentication error\n        if (error && typeof error === 'object' && 'status' in error) {\n          const httpError = error as any;\n          if (httpError.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n        }\n\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        // Clear the safety timeout\n        clearTimeout(loadingTimeout);\n\n        // Ensure loading states are reset in complete block as well\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n    });\n  }\n\n  private handleEmptyResponse(): void {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n\n    // Ensure loading states are reset when handling empty response\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n\n  // Method to manually reset loading states if they get stuck\n  private resetLoadingStates(): void {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n\n  // Public method to manually refresh the grid and reset any stuck loading states\n  public refreshGrid(): void {\n    console.log('Manually refreshing grid...');\n    this.resetLoadingStates();\n    this.loadTable();\n  }\n\n\n  // Search functionality\n  public onSearchKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter') {\n      console.log('Search triggered by Enter key:', this.searchData);\n\n      // Set loading state immediately for search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n\n      this.loadTable();\n    }\n  }\n\n  public onSearchChange(): void {\n    console.log('Search changed:', this.searchData);\n    // Trigger search with debounce\n    this.searchTerms.next(this.searchData);\n  }\n\n\n  private applySearch(): void {\n    // Set loading state immediately for search\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n\n    this.loadTable();\n  }\n\n  public clearSearch(): void {\n    // Clear search data and reset table\n    this.searchData = '';\n\n    // Set loading state for clearing search\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n\n    // Reload table without search filter\n    this.loadTable();\n  }\n\n  // Test method for External PM search\n  public testExternalPMSearch(): void {\n    console.log('Testing External PM search...');\n    this.searchData = 'External PM'; // Test search term\n\n    // Set loading state immediately for test search\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n\n    this.loadTable();\n  }\n\n  // Filter functionality\n  public filterChange(filter: CompositeFilterDescriptor): void {\n    this.filter = filter;\n\n    // Set loading state immediately for filtering\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n\n    this.loadTable();\n  }\n\n  public applyAdvancedFilters(): void {\n    // Apply status filter\n    if (this.appliedFilters.status) {\n      this.filter.filters = this.filter.filters.filter((f) => {\n        if ('field' in f) {\n          return f.field !== 'projectStatus';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'projectStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status,\n      });\n    }\n\n    // Apply center filter\n    if (this.appliedFilters.center) {\n      this.filter.filters = this.filter.filters.filter((f) => {\n        if ('field' in f) {\n          return f.field !== 'centerId';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'centerId',\n        operator: 'eq',\n        value: this.appliedFilters.center,\n      });\n    }\n\n    this.loadTable();\n  }\n\n  public clearAdvancedFilters(): void {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n\n    // Set loading state immediately for clearing filters\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n\n    this.loadTable();\n  }\n\n  public clearAllFilters(): void {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n\n    // Set loading state immediately for clearing filters\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n\n    this.loadTable();\n  }\n\n  // Custom sort handler for three-state cycle\n  public onColumnSort(field: string): void {\n    console.log('Custom sort triggered for field:', field);\n    \n    // Get current sort state for this column\n    const currentState = this.columnSortStates[field] || 'none';\n    \n    // Determine next state in cycle: none -> asc -> desc -> none\n    let nextState: 'none' | 'asc' | 'desc';\n    let nextDir: 'asc' | 'desc';\n    \n    switch (currentState) {\n      case 'none':\n        nextState = 'asc';\n        nextDir = 'asc';\n        break;\n      case 'asc':\n        nextState = 'desc';\n        nextDir = 'desc';\n        break;\n      case 'desc':\n        nextState = 'none';\n        nextDir = 'desc'; // Will be overridden to default\n        break;\n    }\n    \n    // Update column sort state\n    this.columnSortStates[field] = nextState;\n    \n    // Clear other columns' sort states (single column sorting)\n    Object.keys(this.columnSortStates).forEach(key => {\n      if (key !== field) {\n        this.columnSortStates[key] = 'none';\n      }\n    });\n    \n    // Set sort based on state\n    if (nextState === 'none') {\n      // Return to default sorting and clear the column sort state\n      this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n      this.columnSortStates[field] = 'none'; // Ensure it's set to none\n      console.log('Returning to default sort');\n    } else {\n      // Apply the new sort\n      this.sort = [{ field: field, dir: nextDir }];\n      console.log('Applying sort:', this.sort);\n    }\n    \n    // Update page order fields\n    this.page.orderBy = this.sort[0].field;\n    this.page.orderDir = this.sort[0].dir;\n    \n    // Reset to first page\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    \n    console.log('Final sort state:', this.sort);\n    console.log('Column sort states:', this.columnSortStates);\n    \n    this.loadTable();\n  }\n\n  // Data state change handler (includes sorting and pagination)\n  public onDataStateChange(event: any): void {\n    console.log('Data state change:', event);\n    \n    // Handle pagination only (sorting handled by custom method)\n    if (event.skip !== undefined) {\n      this.skip = event.skip;\n      this.page.pageNumber = Math.floor(event.skip / this.page.size);\n    }\n    \n    if (event.take !== undefined) {\n      this.page.size = event.take;\n    }\n    \n    this.loadTable();\n  }\n\n  // Sorting functionality (kept for compatibility)\n  public onSortChange(sort: SortDescriptor[]): void {\n    console.log('Sort change triggered:', sort);\n    this.sort = sort;\n    this.loadTable();\n  }\n\n  // Pagination functionality\n  public pageChange(event: any): void {\n    // Use Kendo's provided values as source of truth\n    this.skip = event.skip;\n    this.page.size = event.take || this.page.size;\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\n\n    // Set loading state immediately for pagination\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n\n    this.loadTable();\n  }\n\n  // Column management\n  public onColumnReorder(event: any): void {\n    // Handle column reordering\n    const reorderedColumns = event.columns.map((col: any) => col.field);\n    this.kendoColOrder = reorderedColumns;\n  }\n\n  public updateColumnVisibility(event: any): void {\n    // Handle column visibility changes\n    const hiddenColumns = event.hiddenColumns || [];\n    this.hiddenFields = hiddenColumns;\n\n    // Update gridColumns to reflect visible columns only\n    this.gridColumns = this.defaultColumns.filter(column =>\n      !hiddenColumns.includes(column)\n    );\n\n    console.log('Column visibility updated:', {\n      hiddenColumns: hiddenColumns,\n      visibleColumns: this.gridColumns,\n      allColumns: this.defaultColumns\n    });\n  }\n\n  public getHiddenField(fieldName: string): boolean {\n    return this.hiddenFields.includes(fieldName);\n  }\n\n  // Selection functionality\n  public onSelectionChange(event: any): void {\n    this.selectedRows = event.selectedRows || [];\n    this.isAllSelected =\n      this.selectedRows.length === this.serverSideRowData.length;\n  }\n\n  public selectAll(): void {\n    if (this.isAllSelected) {\n      this.selectedRows = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedRows = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n  }\n\n  // Grid expansion\n  public toggleExpand(): void {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector(\n      '.grid-container'\n    ) as HTMLElement;\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n\n  // Export functionality\n  // public onExportClick(event: any): void {\n  //   const exportType = event.item.value;\n  //   let selectedIds: number[] = [];\n\n  //   switch (exportType) {\n  //     case 'selected':\n  //       selectedIds = this.selectedRows.map((row) => row.projectId);\n  //       if (selectedIds.length === 0) {\n  //         //alert('Please select projects to export');\n  //         return;\n  //       }\n  //       break;\n  //     case 'filtered':\n  //       // Export filtered data\n  //       break;\n  //     case 'all':\n  //     default:\n  //       // Export all data\n  //       break;\n  //   }\n\n  //   this.exportProjects(exportType, selectedIds);\n  // }\n\n  // private exportProjects(exportType: string, selectedIds: number[]): void {\n  //   this.projectsService.exportProjects(exportType, selectedIds).subscribe({\n  //     next: (response: any) => {\n  //       if (response.data) {\n  //         const blob = new Blob([response.data], {\n  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  //         });\n  //         saveAs(\n  //           blob,\n  //           `projects_${exportType}_${\n  //             new Date().toISOString().split('T')[0]\n  //           }.xlsx`\n  //         );\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       console.error('Export error:', error);\n  //       //alert('Error exporting projects data');\n  //     },\n  //   });\n  // }\n\n  onExportClick(event: any) {\n    const selectedOption = event.value; // Get selected option\n\n    let prdItems: any = [];\n    if (selectedOption === 'selected') {\n      prdItems = this.serverSideRowData;\n\n      // declare the title and header data for excel\n      // get the data for excel in a array format\n      this.exportExcel(prdItems);\n    } else if (selectedOption === 'all') {\n      const queryparamsExcel = {\n        pageSize: this.page.totalElements,\n        sortOrder: this.page.orderDir,\n        sortField: this.page.orderBy,\n        pageNumber: this.page.pageNumber,\n        // filter: this.filterConfiguration()\n      };\n\n      // Enable loading indicator\n      this.httpUtilService.loadingSubject.next(true);\n      // API call\n      this.projectsService\n        .getAllProjects(queryparamsExcel)\n        // .pipe(map((data: any) => data as any))\n        .subscribe((data) => {\n          // Disable loading indicator\n          this.httpUtilService.loadingSubject.next(false);\n          if (data.isFault) {\n            this.IsListHasValue = false;\n            this.cdr.markForCheck();\n            return; // Exit early if the response has a fault\n          }\n\n          this.IsListHasValue = true;\n          prdItems = data.responseData.data || [];\n\n          this.cdr.detectChanges(); // Manually trigger UI update\n          this.exportExcel(prdItems);\n        });\n    }\n  }\n\n  exportExcel(listOfItems: any): void {\n    // Define local variables for the items and current date\n    let prdItems: any = listOfItems;\n    let currentDate: Date = this.appService.formatMonthDate(new Date());\n\n    console.log('prdItems', prdItems);\n\n    // Check if the data exists and is not empty\n    if (prdItems !== undefined && prdItems.length > 0) {\n      // Define the title for the Excel file\n      const tableTitle = 'Events';\n\n      // Filter out hidden columns and sort by order\n      // const visibleColumns = this.columnJSONFormat\n      //   .filter((col: any) => !col.hidden)\n      //   .sort((a: any, b: any) => a.order - b.order);\n\n      // Create header from visible columns\n\n      const headerArray = [\n        'Project Name',\n        'Internal Projet #',\n        'Start Date',\n        'End Date',\n        'Location',\n        'Manager',\n        'External PM',\n      ];\n      // ...visibleColumns.map((col: any) => col.title),\n\n      // Define which columns should have currency and percentage formatting\n      // const currencyColumns: any = [\n      //   'Pending',\n      //   'ACAT',\n      //   'Annuity',\n      //   'AUM',\n      //   'Total Assets',\n      //   'Event Cost',\n      //   'Gross Profit',\n      // ].filter((col) => headerArray.includes(col));\n\n      const percentageColumns: any = [];\n\n      // Get the data for excel in an array format\n      const respResult: any = [];\n\n      // Prepare the data for export based on visible columns\n      each(prdItems, (prdItem: any) => {\n        // Create an array with the same length as headerArray\n        const respData = Array(headerArray.length).fill(null);\n        respData[0] = prdItem.eventDescription;\n        respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n        // Fill in data for each visible column\n        headerArray.forEach((col: any, i: number) => {\n          const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n          switch (col) {\n            case 'Project Name':\n              respData[adjustedIndex] = prdItem.projectName;\n              break;\n            case 'Internal Projet #':\n              respData[adjustedIndex] = prdItem.internalProjectNumber;\n              break;\n            case 'Start Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectStartDate);\n              break;\n            case 'End Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectEndDate);\n              break;\n            case 'Location':\n              respData[adjustedIndex] = prdItem.projectLocation;\n              break;\n            case 'Manager':\n              respData[adjustedIndex] = prdItem.internalProjectManagerName;\n              break;\n            case 'External PM':\n              respData[adjustedIndex] = prdItem.externalPMNames;\n              break;\n            // case 'kept_appointments':\n            //   respData[adjustedIndex] = prdItem.kept_appointments;\n            //   break;\n            // case 'kept_appt_ratio':\n            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n            //   break;\n            // case 'apptKeptNo':\n            //   respData[adjustedIndex] = prdItem.apptKeptNo;\n            //   break;\n            // case 'has_assets':\n            //   respData[adjustedIndex] = prdItem.has_assets;\n            //   break;\n            // case 'prospects_closed':\n            //   respData[adjustedIndex] = prdItem.prospects_closed;\n            //   break;\n            // case 'closing_ratio':\n            //   respData[adjustedIndex] = prdItem.closing_ratio;\n            //   break;\n            // case 'totalPending':\n            //   respData[adjustedIndex] = prdItem.totalPending;\n            //   break;\n            // case 'acatproduction':\n            //   respData[adjustedIndex] = prdItem.acatproduction;\n            //   break;\n            // case 'annuityproduction':\n            //   respData[adjustedIndex] = prdItem.annuityproduction;\n            //   break;\n            // case 'aumproduction':\n            //   respData[adjustedIndex] = prdItem.aumproduction;\n            //   break;\n            // case 'totalAssets':\n            //   respData[adjustedIndex] = prdItem.totalAssets;\n            //   break;\n            // case 'eventCost':\n            //   respData[adjustedIndex] = prdItem.eventCost;\n            //   break;\n            // case 'grossProfit':\n            //   respData[adjustedIndex] = prdItem.grossProfit;\n            //   break;\n            // case 'status':\n            //   respData[adjustedIndex] = prdItem.status;\n            //   break;\n          }\n        });\n\n        respResult.push(respData);\n      });\n\n      // Define column sizes for the Excel file\n      const colSize = headerArray.map((header, index) => ({\n        id: index + 1,\n        width: 20,\n      }));\n\n      // Generate the Excel file using the exceljsService\n      this.execeljsservice.generateExcel(\n        tableTitle,\n        headerArray,\n        respResult,\n        colSize\n        // currencyColumns,\n        // percentageColumns\n      );\n    } else {\n      const message = 'There are no records available to export.';\n      // this.layoutUtilService.showError(message, '');\n    }\n  }\n  // Column settings management\n  public saveHead(): void {\n    const settings = {\n      kendoHide: this.hiddenFields,\n      kendoColOrder: this.kendoColOrder,\n      kendoInitColOrder: this.kendoInitColOrder,\n    };\n\n    // Save to local storage only\n    this.kendoColumnService.saveToLocalStorage({\n      pageName: 'projects',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    });\n\n    console.log('Column settings saved locally:', settings);\n                            this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n\n    //alert('Column settings saved locally');\n  }\n\n  private saveColumnSettingsToServer(settings: any): void {\n    const config = {\n      pageName: 'projects',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    };\n\n    this.kendoColumnService.createHideFields(config).subscribe({\n      next: (response) => {\n        if (response.isFault === false) {\n          console.log('Column settings saved successfully:', response);\n          \n                            this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n          //alert('Column settings saved successfully');\n        } else {\n          console.error('Failed to save column settings:', response.message);\n                            this.customLayoutUtilsService.showError('Column settings failed to save', '');\n\n          //alert('Failed to save column settings: ' + response.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error saving column settings:', error);\n                            this.customLayoutUtilsService.showError('Error saving column settings', '');\n\n        //alert('Error saving column settings. Please try again.');\n      }\n    });\n  }\n\n  private saveResetToServer(): void {\n    // First delete existing settings\n    const deleteConfig = {\n      pageName: 'projects',\n      userID: this.loginUser.userId\n    };\n\n    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n      next: (response) => {\n        console.log('Existing settings deleted:', response);\n        // Then save the reset state (all columns visible)\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      },\n      error: (error) => {\n        console.error('Error deleting existing settings:', error);\n        // Still try to save the reset state\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      }\n    });\n  }\n\n  public resetTable(): void {\n    console.log('Resetting Kendo settings for projects');\n    \n    // Clear all saved settings first\n    this.kendoHide = [];\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.kendoInitColOrder = [];\n    \n    // Clear local storage\n    this.kendoColumnService.clearFromLocalStorage('projects');\n    \n    // Reset to default settings\n    this.resetToDefaultSettings();\n    \n    // Trigger change detection to update the template\n    this.cdr.detectChanges();\n    \n    // Force grid refresh to show all columns\n    if (this.grid) {\n      this.grid.refresh();\n    }\n\n    // Show success message\n    console.log('Table reset to default settings');\n    \n    //alert('Table reset to default settings - all columns restored');\n  }\n  private resetToDefaultSettings(): void {\n    console.log('Resetting to default settings...');\n\n    // Reset column visibility - show all columns\n    this.hiddenFields = [];\n    this.gridColumns = [...this.defaultColumns];\n    this.kendoColOrder = [...this.defaultColumns];\n\n    // Reset sort state to default\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n\n    // Reset page state\n    this.page.pageNumber = 0;\n    this.skip = 0;\n\n    // Reset all filters - clear everything\n    this.filter = { logic: 'and', filters: [] };\n    this.activeFilters = [];\n\n    // Reset advanced filters\n    this.appliedFilters = {};\n\n    // Reset search\n    this.searchData = '';\n\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n\n    console.log('Reset completed:', {\n      hiddenFields: this.hiddenFields,\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      sort: this.sort,\n      filter: this.filter,\n      searchData: this.searchData\n    });\n\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = { logic: 'and', filters: [] };\n\n      // Reset sorting\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n\n      // Reset column visibility - show all columns\n      this.grid.columns.forEach((column: any) => {\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n\n    // Trigger change detection\n    this.cdr.detectChanges();\n\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        // Also try to reset the grid state completely\n        this.grid.reset();\n      }, 100);\n    }\n\n    // Reload data with clean state\n    this.loadTable();\n  }\n  // Navigation\n  public add(): void {\n    this.edit(0);\n  }\n\n  public view(projectId: number): void {\n    this.router.navigate(['/projects/view', projectId]);\n  }\n\n  public edit(projectId: number): void {\n    if (projectId == 0) {\n      // Open modal for new project\n      const modalRef = this.modalService.open(ProjectPopupComponent, {\n        size: 'lg',\n        centered: true,\n        backdrop: 'static'\n      });\n      \n      modalRef.componentInstance.id = projectId;\n      modalRef.componentInstance.project = null;\n      \n      modalRef.componentInstance.passEntry.subscribe((result: boolean) => {\n        if (result) {\n          // Refresh the grid after successful add\n          this.loadTable();\n        }\n      });\n    } else {\n      // Navigate to project view for existing projects\n      this.router.navigate(['/projects/view', projectId]);\n    }\n  }\n\n  public delete(projectId: number): void {\n    // if (confirm('Are you sure you want to delete this project?')) {\n     \n    // }\n     this.projectsService.deleteProject({ projectId }).subscribe({\n        next: (response: any) => {\n\n          console.log(\"response\",response)\n          if (!response.isFault) {\n                            this.customLayoutUtilsService.showSuccess('Project deleted successfully', '');\n\n            //alert('Project deleted successfully');\n            this.loadTable();\n          }\n        },\n        error: (error: any) => {\n          console.error('Delete error:', error);\n                            this.customLayoutUtilsService.showError('error deleting project', '');\n\n          //alert('Error deleting project');\n        },\n      });\n  }\n\n  // Utility methods\n  public getProjectFullName(project: any): string {\n    return `${project.projectFirstName || ''} ${\n      project.projectLastName || ''\n    }`.trim();\n  }\n\n  public getCenterName(project: any): string {\n    return project.medicalCenter?.centerName || '';\n  }\n\n  public formatDate(dateString: string): string {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const year = date.getFullYear();\n    return `${month}/${day}/${year}`;\n  }\n\n  public getStatusClass(status: string): string {\n    return status === 'Active' ? 'badge-light-success' : 'badge-light-danger';\n  }\n  deletePop(content: any, projectId: any, projectName: any) {\n    this.projectName = projectName;\n    this.projectId = projectId;\n    this.modalService.open(content, { centered: true });\n  }\n\n  // Method to handle cancel button click in delete modal\n  onDeleteCancelClick(modalRef: any): void {\n    // Reset loading state when cancel is clicked\n    this.httpUtilService.loadingSubject.next(false);\n    modalRef.dismiss();\n  }\n\n  confirmDelete() {\n    // console.log('Item deleted ✅');\n    this.delete(this.projectId);\n    // your delete logic here\n  }\n\n  onTabActivated() {\n    // This method is called when the tab is activated\n    // You can add any specific logic here if needed\n    console.log('Projects tab activated');\n  }\n}\n", "<!-- Full Screen Loading Overlay -->\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"custom-colored-spinner\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<div class=\"grid-container\">\n  <kendo-grid\n    #normalGrid\n    [data]=\"gridData\"\n    [pageSize]=\"page.size\"\n    [sort]=\"sort\"\n    [pageable]=\"{\n      pageSizes: [15, 20, 50, 100],\n      previousNext: true,\n      info: true,\n      type: 'numeric',\n      buttonCount: 5\n    }\"\n    [total]=\"page.totalElements\"\n    [sortable]=\"false\"\n    [groupable]=\"false\"\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\n    (columnReorder)=\"onColumnReorder($event)\"\n    (selectionChange)=\"onSelectionChange($event)\"\n    [reorderable]=\"true\"\n    style=\"width: auto; overflow-x: auto\"\n    [resizable]=\"false\"\n    [height]=\"720\"\n    [skip]=\"skip\"\n    [filter]=\"filter\"\n    [columnMenu]=\"{ filter: true }\"\n    (filterChange)=\"filterChange($event)\"\n    (pageChange)=\"pageChange($event)\"\n    (dataStateChange)=\"onDataStateChange($event)\"\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\n    [loading]=\"false\"\n  >\n    <ng-template kendoGridToolbarTemplate>\n      <!-- Search Section -->\n      <div class=\"d-flex align-items-center me-3 search-section\">\n        <kendo-textbox\n          [style.width.px]=\"500\"\n          placeholder=\"Search...\"\n          [(ngModel)]=\"searchData\"\n          [clearButton]=\"true\"\n          (keydown)=\"onSearchKeyDown($event)\"\n          (ngModelChange)=\"onSearchChange()\"\n        ></kendo-textbox>\n      </div>\n\n      <kendo-grid-spacer></kendo-grid-spacer>\n\n      <!-- Total Count - Repositioned to the right -->\n      <div class=\"d-flex align-items-center me-3\">\n        <span class=\"text-muted\">Total: </span>\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\n      </div>\n\n      <!-- Action Buttons -->\n      <button\n        type=\"button\"\n        class=\"btn btn-primary btn-sm me-2\"\n        (click)=\"add()\"\n      >\n        <span\n          [inlineSVG]=\"'./assets/media/icons/duotune/arrows/arr075.svg'\"\n          class=\"svg-icon svg-icon-3\"\n        ></span>\n        Add\n      </button>\n\n      <button\n      type=\"button\"\n      class=\"btn btn-secondary btn-sm me-2\"\n      (click)=\"toggleExpand()\"\n      title=\"Toggle Grid Expansion\"\n    >\n      <i\n        class=\"fas\"\n        [class.fa-expand]=\"!isExpanded\"\n        [class.fa-compress]=\"isExpanded\"\n      ></i>\n    </button>\n\n      <kendo-dropdownbutton\n        text=\"Export Excel\"\n        iconClass=\"fas fa-file-excel\"\n        [data]=\"exportOptions\"\n        class=\"btn btn-secondary btn-sm me-2\"\n        (itemClick)=\"onExportClick($event)\"\n        title=\"Export\"\n      >\n      </kendo-dropdownbutton>\n\n      <!-- Save Column Settings Button -->\n      <!-- <button\n        type=\"button\"\n        class=\"btn btn-success btn-sm me-2\"\n        (click)=\"saveHead()\"\n        title=\"Save Column Settings\"\n      >\n        <i class=\"fas fa-save\"></i>\n      </button> -->\n\n      <!-- Reset Button -->\n      <button\n        type=\"button\"\n        class=\"btn btn-warning btn-sm me-2\"\n        (click)=\"resetTable()\"\n        title=\"Reset to Default\"\n      >\n        <i class=\"fas fa-undo\"></i>\n      </button>\n\n      <!-- Refresh Button -->\n      <button\n        type=\"button\"\n        class=\"btn btn-info btn-sm me-2\"\n        (click)=\"refreshGrid()\"\n        title=\"Refresh Grid Data\"\n      >\n        <i class=\"fas fa-sync-alt\"></i>\n      </button>\n    </ng-template>\n\n    <!-- Advanced Filters Panel -->\n    <ng-template kendoGridToolbarTemplate>\n      <div\n        *ngIf=\"showAdvancedFilters\"\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\n      >\n        <div class=\"row\">\n          <div class=\"col-md-3\">\n            <label class=\"form-label\">Status</label>\n            \n            <kendo-dropdownlist\n              [data]=\"advancedFilterOptions.status\"\n              [(ngModel)]=\"appliedFilters.status\"\n              textField=\"text\"\n              valueField=\"value\"\n              placeholder=\"Select Status\"\n            >\n            </kendo-dropdownlist>\n          </div>\n          <div class=\"col-md-3 d-flex align-items-end\">\n            <button\n              kendoButton\n              (click)=\"applyAdvancedFilters()\"\n              class=\"btn-primary me-2\"\n            >\n              <i class=\"fas fa-check\"></i> Apply Filters\n            </button>\n            <button\n              kendoButton\n              (click)=\"clearAllFilters()\"\n              class=\"btn-secondary\"\n            >\n              <i class=\"fas fa-times\"></i> Clear\n            </button>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n    <ng-container *ngFor=\"let column of gridColumns\">\n      <!-- Action Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'action'\"\n        title=\"Actions\"\n        [width]=\"90\"\n        [sticky]=\"true\"\n        [reorderable]=\"!fixedColumns.includes('action')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [includeInChooser]=\"false\"\n        [columnMenu]=\"false\"\n        [style]=\"{ 'background-color': '#efefef !important' }\"\n        [hidden]=\"getHiddenField('action')\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\n          <div class=\"d-flex align-items-center justify-content-center gap-1\" style=\"min-height: 32px;\">\n            <!-- <a\n              title=\"View\"\n              class=\"btn btn-icon btn-sm\"\n              (click)=\"view(dataItem.patientId)\"\n            >\n              <span\n                [inlineSVG]=\"'./assets/media/icons/duotune/general/gen019.svg'\"\n                class=\"svg-icon svg-icon-3 svg-icon-primary\"\n              >\n              </span>\n            </a> -->\n            <!-- View icon - always on the left -->\n            <a\n              title=\"View\"\n              class=\"btn btn-icon btn-sm d-flex align-items-center justify-content-center\"\n              style=\"width: 32px; height: 32px;\"\n              (click)=\"edit(dataItem.projectId)\"\n            >\n              <i class=\"fas fa-pencil\" style=\"color: var(--bs-primary, #0d6efd); font-size: 1rem;\"></i>\n            </a>\n            <!-- Delete icon - always on the right, with consistent spacing -->\n            <a\n              title=\"Delete\"\n              class=\"btn btn-icon  btn-sm d-flex align-items-center justify-content-center\"\n              style=\"width: 32px; height: 32px;\"\n              [class.invisible]=\"!dataItem.isDeletable\"\n              [class.disabled]=\"!dataItem.isDeletable\"\n              (click)=\"dataItem.isDeletable && deletePop(deleteModal, dataItem.projectId, dataItem.projectName)\"\n            >\n              <span\n                [inlineSVG]=\"'./assets/media/icons/duotune/general/gen027.svg'\"\n                class=\"svg-icon svg-icon-3 svg-icon-danger\"\n              >\n              </span>\n            </a>\n          </div>\n\n          <!-- <div class=\"d-flex gap-1\">\n          <button class=\"btn btn-sm btn-light-primary\" (click)=\"view(dataItem.patientId)\" title=\"View\">\n            <i class=\"fas fa-eye\"></i>\n          </button>\n          <button class=\"btn btn-sm btn-light-warning\" (click)=\"edit(dataItem.patientId)\" title=\"Edit\">\n            <i class=\"fas fa-edit\"></i>\n          </button>\n          <button class=\"btn btn-sm btn-light-danger\" (click)=\"delete(dataItem.patientId)\" title=\"Delete\">\n            <i class=\"fas fa-trash\"></i>\n          </button>\n        </div> -->\n        </ng-template>\n      </kendo-grid-column>\n      <!-- Project Name Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'projectName'\"\n        field=\"projectName\"\n        title=\"Project name\"\n        [width]=\"200\"\n        [sticky]=\"true\"\n        [reorderable]=\"!fixedColumns.includes('projectName')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [includeInChooser]=\"false\"\n        [hidden]=\"getHiddenField('projectName')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridHeaderTemplate>\n          <div (click)=\"onColumnSort('projectName')\" style=\"cursor: pointer; user-select: none;\">\n            Project Name\n            <span *ngIf=\"columnSortStates['projectName'] === 'asc'\" style=\"color: red; font-weight: bold;\">↑</span>\n            <span *ngIf=\"columnSortStates['projectName'] === 'desc'\" style=\"color: red; font-weight: bold;\">↓</span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridCellTemplate let-dataItem>\n           <strong>{{ dataItem.projectName }}</strong>\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"false\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n      <!-- Internal Project Number Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'internalProjectNumber'\"\n        field=\"internalProjectNumber\"\n        title=\"Internal Project #\"\n        [width]=\"150\"\n        [reorderable]=\"!fixedColumns.includes('internalProjectNumber')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('internalProjectNumber')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridHeaderTemplate>\n          <div (click)=\"onColumnSort('internalProjectNumber')\" style=\"cursor: pointer; user-select: none;\">\n            Internal Project #\n            <span *ngIf=\"columnSortStates['internalProjectNumber'] === 'asc'\" style=\"color: red; font-weight: bold;\">↑</span>\n            <span *ngIf=\"columnSortStates['internalProjectNumber'] === 'desc'\" style=\"color: red; font-weight: bold;\">↓</span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridCellTemplate let-dataItem>\n           {{ dataItem.internalProjectNumber }}\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"false\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Project Start Date Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'projectStartDate'\"\n        field=\"projectStartDate\"\n        title=\"Start Date\"\n        [width]=\"110\"\n        [reorderable]=\"!fixedColumns.includes('projectStartDate')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('projectStartDate')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          {{ formatDate(dataItem.projectStartDate) }}\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-date-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"true\"\n          >\n            <kendo-filter-gte-operator></kendo-filter-gte-operator>\n            <kendo-filter-lte-operator></kendo-filter-lte-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n          </kendo-grid-date-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n      <!-- Project End Date Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'projectEndDate'\"\n        field=\"projectEndDate\"\n        title=\"End Date\"\n        [width]=\"110\"\n        [reorderable]=\"!fixedColumns.includes('projectEndDate')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('projectEndDate')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n         {{ formatDate(dataItem.projectEndDate) }}\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-date-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"true\"\n          >\n            <kendo-filter-gte-operator></kendo-filter-gte-operator>\n            <kendo-filter-lte-operator></kendo-filter-lte-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n          </kendo-grid-date-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Project Location Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'projectLocation'\"\n        field=\"projectLocation\"\n        title=\"Location\"\n        [width]=\"180\"\n        [reorderable]=\"!fixedColumns.includes('projectLocation')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('projectLocation')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          {{ dataItem.projectLocation }}\n          <!-- <div>\n            <span class=\"fw-bolder\">\n              \n            </span>\n          </div> -->\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"false\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Internal Project Manager Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'internalProjectManagerName'\"\n        field=\"internalProjectManagerName\"\n        title=\"Internal Manager\"\n        [width]=\"180\"\n        [reorderable]=\"!fixedColumns.includes('internalProjectManagerName')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('internalProjectManagerName')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          {{ dataItem.internalProjectManagerName }}\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"false\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- External PM Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'externalPMNames'\"\n        field=\"externalPMNames\"\n        title=\"External PM\"\n        [width]=\"220\"\n        [reorderable]=\"!fixedColumns.includes('externalPMNames')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('externalPMNames')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n       {{ dataItem.externalPMNames }}\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"false\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Last Updated Date Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'lastUpdatedDate'\"\n        field=\"lastUpdatedDate\"\n        title=\"Updated Date\"\n        [width]=\"110\"\n        [reorderable]=\"!fixedColumns.includes('lastUpdatedDate')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('lastUpdatedDate')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridHeaderTemplate>\n          <div (click)=\"onColumnSort('lastUpdatedDate')\" style=\"cursor: pointer; user-select: none;\">\n            Updated date\n            <span *ngIf=\"columnSortStates['lastUpdatedDate'] === 'asc'\" style=\"color: red; font-weight: bold;\">↑</span>\n            <span *ngIf=\"columnSortStates['lastUpdatedDate'] === 'desc'\" style=\"color: red; font-weight: bold;\">↓</span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridCellTemplate let-dataItem>\n           {{ formatDate(dataItem.lastUpdatedDate) }}\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-date-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"true\"\n          >\n            <kendo-filter-gte-operator></kendo-filter-gte-operator>\n            <kendo-filter-lte-operator></kendo-filter-lte-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n          </kendo-grid-date-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n    </ng-container>\n\n    <!-- No Data Template -->\n    <ng-template kendoGridNoRecordsTemplate>\n      <div\n        class=\"custom-no-records\"\n        *ngIf=\"loading === false && serverSideRowData.length === 0\"\n      >\n        <div class=\"text-center\">\n          <i\n            class=\"fas fa-folder-open text-muted mb-2\"\n            style=\"font-size: 2rem\"\n          ></i>\n          <p class=\"text-muted\">No projects found</p>\n          <button kendoButton (click)=\"loadTable()\" class=\"btn-primary\">\n            <i class=\"fas fa-refresh me-2\"></i>Refresh\n          </button>\n        </div>\n      </div>\n    </ng-template>\n  </kendo-grid>\n</div>\n\n<!-- Delete Confirmation Modal -->\n<ng-template #deleteModal let-modal>\n  <div class=\"modal-header bg-danger text-white\">\n    <h5 class=\"modal-title\">Confirm Delete</h5>\n    <button\n      type=\"button\"\n      class=\"btn-close\"\n      aria-label=\"Close\"\n      (click)=\"onDeleteCancelClick(modal)\"\n    ></button>\n  </div>\n\n  <div class=\"delete-modal-body mt-4 text-center\">\n    <p class=\"fs-5\">\n      Are you sure you want to delete this project? - {{ this.projectName }}\n    </p>\n  </div>\n\n  <div class=\"modal-footer delete-modal-footer ms-2\">\n    <button type=\"button\" class=\"btn btn-danger\" (click)=\"onDeleteCancelClick(modal)\">\n      Cancel\n    </button>\n    <button\n      type=\"button\"\n      class=\"btn btn-primary \"\n      (click)=\"confirmDelete(); modal.close()\"\n    >\n      Delete\n    </button>\n  </div>\n</ng-template>\n"], "mappings": "AAaA,SAAcA,IAAI,QAAQ,QAAQ;AAClC,SACEC,OAAO,EAEPC,YAAY,EACZC,oBAAoB,QAEf,MAAM;AAUb,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC1B1EC,EAHN,CAAAC,cAAA,aAAqE,aACtC,cACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IAqCEH,EADF,CAAAC,cAAA,cAA2D,wBAQxD;IAJCD,EAAA,CAAAI,gBAAA,2BAAAC,mFAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,UAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,UAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAGxBN,EADA,CAAAc,UAAA,qBAAAC,6EAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWJ,MAAA,CAAAO,eAAA,CAAAV,MAAA,CAAuB;IAAA,EAAC,2BAAAD,mFAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAClBJ,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC;IAEtCjB,EADG,CAAAG,YAAA,EAAgB,EACb;IAENH,EAAA,CAAAkB,SAAA,wBAAuC;IAIrClB,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAc,UAAA,mBAAAK,oEAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAW,GAAA,EAAK;IAAA,EAAC;IAEfpB,EAAA,CAAAkB,SAAA,eAGQ;IACRlB,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKD;IAFCD,EAAA,CAAAc,UAAA,mBAAAO,qEAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IAGxBtB,EAAA,CAAAkB,SAAA,aAIK;IACPlB,EAAA,CAAAG,YAAA,EAAS;IAEPH,EAAA,CAAAC,cAAA,gCAOC;IAFCD,EAAA,CAAAc,UAAA,uBAAAS,uFAAAjB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAaJ,MAAA,CAAAe,aAAA,CAAAlB,MAAA,CAAqB;IAAA,EAAC;IAGrCN,EAAA,CAAAG,YAAA,EAAuB;IAavBH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAW,qEAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAiB,UAAA,EAAY;IAAA,EAAC;IAGtB1B,EAAA,CAAAkB,SAAA,aAA2B;IAC7BlB,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAa,qEAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmB,WAAA,EAAa;IAAA,EAAC;IAGvB5B,EAAA,CAAAkB,SAAA,aAA+B;IACjClB,EAAA,CAAAG,YAAA,EAAS;;;;IAjFLH,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAA8B,WAAA,oBAAsB;IAEtB9B,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAG,UAAA,CAAwB;IACxBZ,EAAA,CAAAgC,UAAA,qBAAoB;IAWKhC,EAAA,CAAA6B,SAAA,GAA6B;IAA7B7B,EAAA,CAAAiC,iBAAA,CAAAxB,MAAA,CAAAyB,IAAA,CAAAC,aAAA,MAA6B;IAUtDnC,EAAA,CAAA6B,SAAA,GAA8D;IAA9D7B,EAAA,CAAAgC,UAAA,+DAA8D;IAchEhC,EAAA,CAAA6B,SAAA,GAA+B;IAC/B7B,EADA,CAAAoC,WAAA,eAAA3B,MAAA,CAAA4B,UAAA,CAA+B,gBAAA5B,MAAA,CAAA4B,UAAA,CACC;IAOhCrC,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA6B,aAAA,CAAsB;;;;;;IA8ClBtC,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAExCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAmC,8FAAAjC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAgC,cAAA,CAAAC,MAAA,EAAApC,MAAA,MAAAG,MAAA,CAAAgC,cAAA,CAAAC,MAAA,GAAApC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAMvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAA6C,iBAK1C;IAFCD,EAAA,CAAAc,UAAA,mBAAA6B,0EAAA;MAAA3C,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmC,oBAAA,EAAsB;IAAA,EAAC;IAGhC5C,EAAA,CAAAkB,SAAA,YAA4B;IAAClB,EAAA,CAAAE,MAAA,sBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAc,UAAA,mBAAA+B,2EAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAqC,eAAA,EAAiB;IAAA,EAAC;IAG3B9C,EAAA,CAAAkB,SAAA,aAA4B;IAAClB,EAAA,CAAAE,MAAA,eAC/B;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAzBEH,EAAA,CAAA6B,SAAA,GAAqC;IAArC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAsC,qBAAA,CAAAL,MAAA,CAAqC;IACrC1C,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAgC,cAAA,CAAAC,MAAA,CAAmC;;;;;IAV3C1C,EAAA,CAAAgD,UAAA,IAAAC,iDAAA,mBAGC;;;;IAFEjD,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAyC,mBAAA,CAAyB;;;;;;IA+DtBlD,EAbF,CAAAC,cAAA,cAA8F,YAkB3F;IADCD,EAAA,CAAAc,UAAA,mBAAAqC,kGAAA;MAAA,MAAAC,WAAA,GAAApD,EAAA,CAAAO,aAAA,CAAA8C,GAAA,EAAAC,QAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA8C,IAAA,CAAAH,WAAA,CAAAI,SAAA,CAAwB;IAAA,EAAC;IAElCxD,EAAA,CAAAkB,SAAA,YAAyF;IAC3FlB,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,YAOC;IADCD,EAAA,CAAAc,UAAA,mBAAA2C,kGAAA;MAAA,MAAAL,WAAA,GAAApD,EAAA,CAAAO,aAAA,CAAA8C,GAAA,EAAAC,QAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,MAAAgD,cAAA,GAAA1D,EAAA,CAAA2D,WAAA;MAAA,OAAA3D,EAAA,CAAAa,WAAA,CAAAuC,WAAA,CAAAQ,WAAA,IAAiCnD,MAAA,CAAAoD,SAAA,CAAAH,cAAA,EAAAN,WAAA,CAAAI,SAAA,EAAAJ,WAAA,CAAAU,WAAA,CAAgE;IAAA,EAAC;IAElG9D,EAAA,CAAAkB,SAAA,eAIO;IAEXlB,EADE,CAAAG,YAAA,EAAI,EACA;;;;IAVFH,EAAA,CAAA6B,SAAA,GAAyC;IACzC7B,EADA,CAAAoC,WAAA,eAAAgB,WAAA,CAAAQ,WAAA,CAAyC,cAAAR,WAAA,CAAAQ,WAAA,CACD;IAItC5D,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IA5CzEhC,EAAA,CAAAC,cAAA,4BAWC;IACCD,EAAA,CAAAgD,UAAA,IAAAe,8EAAA,0BAA2D;IAmD7D/D,EAAA,CAAAG,YAAA,EAAoB;;;;IAtDlBH,EAAA,CAAAgE,UAAA,CAAAhE,EAAA,CAAAiE,eAAA,IAAAC,GAAA,EAAsD;IACtDlE,EAPA,CAAAgC,UAAA,aAAY,gBACG,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,WACiC,gBAAApE,EAAA,CAAAiE,eAAA,KAAAI,GAAA,EACuB,2BAC7C,qBACN,WAAA5D,MAAA,CAAA6D,cAAA,WAEe;;;;;IAsE/BtE,EAAA,CAAAC,cAAA,eAA+F;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACvGH,EAAA,CAAAC,cAAA,eAAgG;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAH1GH,EAAA,CAAAC,cAAA,cAAuF;IAAlFD,EAAA,CAAAc,UAAA,mBAAAyD,oGAAA;MAAAvE,EAAA,CAAAO,aAAA,CAAAiE,GAAA;MAAA,MAAA/D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgE,YAAA,CAAa,aAAa,CAAC;IAAA,EAAC;IACxCzE,EAAA,CAAAE,MAAA,qBACA;IACAF,EADA,CAAAgD,UAAA,IAAA0B,qFAAA,mBAA+F,IAAAC,qFAAA,mBACC;IAClG3E,EAAA,CAAAG,YAAA,EAAM;;;;IAFGH,EAAA,CAAA6B,SAAA,GAA+C;IAA/C7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmE,gBAAA,0BAA+C;IAC/C5E,EAAA,CAAA6B,SAAA,EAAgD;IAAhD7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmE,gBAAA,2BAAgD;;;;;IAIxD5E,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAnCH,EAAA,CAAA6B,SAAA,EAA0B;IAA1B7B,EAAA,CAAAiC,iBAAA,CAAA4C,WAAA,CAAAf,WAAA,CAA0B;;;;;IAGnC9D,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IAKnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAA8C,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IA1BrB/E,EAAA,CAAAC,cAAA,4BAWC;IAWCD,EAVA,CAAAgD,UAAA,IAAAgC,8EAAA,0BAAqC,IAAAC,8EAAA,0BAOW,IAAAC,8EAAA,0BAGwB;IAa1ElF,EAAA,CAAAG,YAAA,EAAoB;;;;IAzBlBH,EANA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,gBACsC,gBAAApE,EAAA,CAAAiE,eAAA,IAAAI,GAAA,EACkB,2BAC7C,WAAA5D,MAAA,CAAA6D,cAAA,gBACc,oBACrB;;;;;IAwCftE,EAAA,CAAAC,cAAA,eAAyG;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACjHH,EAAA,CAAAC,cAAA,eAA0G;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAHpHH,EAAA,CAAAC,cAAA,cAAiG;IAA5FD,EAAA,CAAAc,UAAA,mBAAAqE,oGAAA;MAAAnF,EAAA,CAAAO,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgE,YAAA,CAAa,uBAAuB,CAAC;IAAA,EAAC;IAClDzE,EAAA,CAAAE,MAAA,2BACA;IACAF,EADA,CAAAgD,UAAA,IAAAqC,qFAAA,mBAAyG,IAAAC,qFAAA,mBACC;IAC5GtF,EAAA,CAAAG,YAAA,EAAM;;;;IAFGH,EAAA,CAAA6B,SAAA,GAAyD;IAAzD7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmE,gBAAA,oCAAyD;IACzD5E,EAAA,CAAA6B,SAAA,EAA0D;IAA1D7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmE,gBAAA,qCAA0D;;;;;IAIlE5E,EAAA,CAAAE,MAAA,GACH;;;;IADGF,EAAA,CAAAuF,kBAAA,MAAAC,YAAA,CAAAC,qBAAA,MACH;;;;;IAEEzF,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IAKnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAA0D,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAxBrB3F,EAAA,CAAAC,cAAA,4BASC;IAWCD,EAVA,CAAAgD,UAAA,IAAA4C,8EAAA,0BAAqC,IAAAC,8EAAA,0BAOW,IAAAC,8EAAA,0BAGwB;IAa1E9F,EAAA,CAAAG,YAAA,EAAoB;;;;IAzBlBH,EAJA,CAAAgC,UAAA,cAAa,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,0BACkD,gBAAApE,EAAA,CAAAiE,eAAA,IAAAI,GAAA,EACQ,WAAA5D,MAAA,CAAA6D,cAAA,0BACrB,oBAC/B;;;;;IAuCjBtE,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAuF,kBAAA,MAAA9E,MAAA,CAAAsF,UAAA,CAAAC,YAAA,CAAAC,gBAAA,OACF;;;;;IAEEjG,EAAA,CAAAC,cAAA,sCAIC;IAICD,EAHA,CAAAkB,SAAA,gCAAuD,gCACA,+BACF,gCACE;IACzDlB,EAAA,CAAAG,YAAA,EAA8B;;;;;IAN5BH,EAFA,CAAAgC,UAAA,WAAAkE,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAjBpBnG,EAAA,CAAAC,cAAA,4BASC;IAICD,EAHA,CAAAgD,UAAA,IAAAoD,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAY1ErG,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EAJA,CAAAgC,UAAA,cAAa,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,qBAC6C,gBAAApE,EAAA,CAAAiE,eAAA,IAAAI,GAAA,EACa,WAAA5D,MAAA,CAAA6D,cAAA,qBAC1B,oBAC1B;;;;;IA8BlBtE,EAAA,CAAAE,MAAA,GACD;;;;;IADCF,EAAA,CAAAuF,kBAAA,MAAA9E,MAAA,CAAAsF,UAAA,CAAAO,YAAA,CAAAC,cAAA,OACD;;;;;IAEEvG,EAAA,CAAAC,cAAA,sCAIC;IAICD,EAHA,CAAAkB,SAAA,gCAAuD,gCACA,+BACF,gCACE;IACzDlB,EAAA,CAAAG,YAAA,EAA8B;;;;;IAN5BH,EAFA,CAAAgC,UAAA,WAAAwE,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAjBpBzG,EAAA,CAAAC,cAAA,4BASC;IAICD,EAHA,CAAAgD,UAAA,IAAA0D,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAY1E3G,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EAJA,CAAAgC,UAAA,cAAa,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,mBAC2C,gBAAApE,EAAA,CAAAiE,eAAA,IAAAI,GAAA,EACe,WAAA5D,MAAA,CAAA6D,cAAA,mBAC5B,oBACxB;;;;;IA+BjBtE,EAAA,CAAAE,MAAA,GACA;;;;IADAF,EAAA,CAAAuF,kBAAA,MAAAqB,YAAA,CAAAC,eAAA,MACA;;;;;IAOA7G,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IAKnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAA8E,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAtBrB/G,EAAA,CAAAC,cAAA,4BASC;IASCD,EARA,CAAAgD,UAAA,IAAAgE,8EAAA,0BAAgD,IAAAC,8EAAA,0BAQwB;IAa1EjH,EAAA,CAAAG,YAAA,EAAoB;;;;IAvBlBH,EAJA,CAAAgC,UAAA,cAAa,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,oBAC4C,gBAAApE,EAAA,CAAAiE,eAAA,IAAAI,GAAA,EACc,WAAA5D,MAAA,CAAA6D,cAAA,oBAC3B,oBACzB;;;;;IAqCjBtE,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAuF,kBAAA,MAAA2B,YAAA,CAAAC,0BAAA,MACF;;;;;IAEEnH,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IAKnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAAoF,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAjBrBrH,EAAA,CAAAC,cAAA,4BASC;IAICD,EAHA,CAAAgD,UAAA,IAAAsE,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAa1EvH,EAAA,CAAAG,YAAA,EAAoB;;;;IAlBlBH,EAJA,CAAAgC,UAAA,cAAa,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,+BACuD,gBAAApE,EAAA,CAAAiE,eAAA,IAAAI,GAAA,EACG,WAAA5D,MAAA,CAAA6D,cAAA,+BAChB,oBACpC;;;;;IAgCpBtE,EAAA,CAAAE,MAAA,GACC;;;;IADDF,EAAA,CAAAuF,kBAAA,MAAAiC,YAAA,CAAAC,eAAA,MACC;;;;;IAEEzH,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IAKnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAA0F,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAjBrB3H,EAAA,CAAAC,cAAA,4BASC;IAICD,EAHA,CAAAgD,UAAA,IAAA4E,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAa1E7H,EAAA,CAAAG,YAAA,EAAoB;;;;IAlBlBH,EAJA,CAAAgC,UAAA,cAAa,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,oBAC4C,gBAAApE,EAAA,CAAAiE,eAAA,IAAAI,GAAA,EACc,WAAA5D,MAAA,CAAA6D,cAAA,oBAC3B,oBACzB;;;;;IAkCftE,EAAA,CAAAC,cAAA,eAAmG;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC3GH,EAAA,CAAAC,cAAA,eAAoG;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAH9GH,EAAA,CAAAC,cAAA,cAA2F;IAAtFD,EAAA,CAAAc,UAAA,mBAAAgH,oGAAA;MAAA9H,EAAA,CAAAO,aAAA,CAAAwH,IAAA;MAAA,MAAAtH,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgE,YAAA,CAAa,iBAAiB,CAAC;IAAA,EAAC;IAC5CzE,EAAA,CAAAE,MAAA,qBACA;IACAF,EADA,CAAAgD,UAAA,IAAAgF,qFAAA,mBAAmG,IAAAC,qFAAA,mBACC;IACtGjI,EAAA,CAAAG,YAAA,EAAM;;;;IAFGH,EAAA,CAAA6B,SAAA,GAAmD;IAAnD7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmE,gBAAA,8BAAmD;IACnD5E,EAAA,CAAA6B,SAAA,EAAoD;IAApD7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmE,gBAAA,+BAAoD;;;;;IAI5D5E,EAAA,CAAAE,MAAA,GACH;;;;;IADGF,EAAA,CAAAuF,kBAAA,MAAA9E,MAAA,CAAAsF,UAAA,CAAAmC,YAAA,CAAAC,eAAA,OACH;;;;;IAEEnI,EAAA,CAAAC,cAAA,sCAIC;IAICD,EAHA,CAAAkB,SAAA,gCAAuD,gCACA,+BACF,gCACE;IACzDlB,EAAA,CAAAG,YAAA,EAA8B;;;;;IAN5BH,EAFA,CAAAgC,UAAA,WAAAoG,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAxBpBrI,EAAA,CAAAC,cAAA,4BASC;IAWCD,EAVA,CAAAgD,UAAA,IAAAsF,8EAAA,0BAAqC,IAAAC,8EAAA,0BAOW,IAAAC,8EAAA,0BAGwB;IAY1ExI,EAAA,CAAAG,YAAA,EAAoB;;;;IAxBlBH,EAJA,CAAAgC,UAAA,cAAa,iBAAAvB,MAAA,CAAA0D,YAAA,CAAAC,QAAA,oBAC4C,gBAAApE,EAAA,CAAAiE,eAAA,IAAAI,GAAA,EACc,WAAA5D,MAAA,CAAA6D,cAAA,oBAC3B,oBACzB;;;;;IAvSvBtE,EAAA,CAAAyI,uBAAA,GAAiD;IA+R/CzI,EA7RA,CAAAgD,UAAA,IAAA0F,gEAAA,iCAWC,IAAAC,gEAAA,gCAiEA,IAAAC,gEAAA,gCAmCA,IAAAC,gEAAA,gCAoCA,IAAAC,gEAAA,gCA2BA,IAAAC,gEAAA,gCA4BA,IAAAC,gEAAA,gCAkCA,IAAAC,gEAAA,gCA6BA,IAAAC,gEAAA,gCA6BA;;;;;IArSElJ,EAAA,CAAA6B,SAAA,EAAyB;IAAzB7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,cAAyB;IAiEzBnJ,EAAA,CAAA6B,SAAA,EAA8B;IAA9B7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,mBAA8B;IAqC9BnJ,EAAA,CAAA6B,SAAA,EAAwC;IAAxC7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,6BAAwC;IAoCxCnJ,EAAA,CAAA6B,SAAA,EAAmC;IAAnC7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,wBAAmC;IA2BnCnJ,EAAA,CAAA6B,SAAA,EAAiC;IAAjC7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,sBAAiC;IA4BjCnJ,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,uBAAkC;IAkClCnJ,EAAA,CAAA6B,SAAA,EAA6C;IAA7C7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,kCAA6C;IA6B7CnJ,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,uBAAkC;IA6BlCnJ,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAmH,UAAA,uBAAkC;;;;;;IAwCnCnJ,EAJF,CAAAC,cAAA,cAGC,cAC0B;IACvBD,EAAA,CAAAkB,SAAA,YAGK;IACLlB,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC3CH,EAAA,CAAAC,cAAA,iBAA8D;IAA1CD,EAAA,CAAAc,UAAA,mBAAAsI,0EAAA;MAAApJ,EAAA,CAAAO,aAAA,CAAA8I,IAAA;MAAA,MAAA5I,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA6I,SAAA,EAAW;IAAA,EAAC;IACvCtJ,EAAA,CAAAkB,SAAA,YAAmC;IAAAlB,EAAA,CAAAE,MAAA,eACrC;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAdNH,EAAA,CAAAgD,UAAA,IAAAuG,iDAAA,kBAGC;;;;IADEvJ,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA+I,OAAA,cAAA/I,MAAA,CAAAgJ,iBAAA,CAAAC,MAAA,OAAyD;;;;;;IAoB9D1J,EADF,CAAAC,cAAA,cAA+C,aACrB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAc,UAAA,mBAAA6I,oEAAA;MAAA,MAAAC,SAAA,GAAA5J,EAAA,CAAAO,aAAA,CAAAsJ,IAAA,EAAAC,SAAA;MAAA,MAAArJ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAsJ,mBAAA,CAAAH,SAAA,CAA0B;IAAA,EAAC;IAExC5J,EADG,CAAAG,YAAA,EAAS,EACN;IAGJH,EADF,CAAAC,cAAA,cAAgD,YAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAGJH,EADF,CAAAC,cAAA,cAAmD,iBACiC;IAArCD,EAAA,CAAAc,UAAA,mBAAAkJ,oEAAA;MAAA,MAAAJ,SAAA,GAAA5J,EAAA,CAAAO,aAAA,CAAAsJ,IAAA,EAAAC,SAAA;MAAA,MAAArJ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAsJ,mBAAA,CAAAH,SAAA,CAA0B;IAAA,EAAC;IAC/E5J,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAc,UAAA,mBAAAmJ,qEAAA;MAAA,MAAAL,SAAA,GAAA5J,EAAA,CAAAO,aAAA,CAAAsJ,IAAA,EAAAC,SAAA;MAAA,MAAArJ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAyJ,aAAA,EAAe;MAAA,OAAAlK,EAAA,CAAAa,WAAA,CAAE+I,SAAA,CAAAO,KAAA,EAAa;IAAA,EAAC;IAExCnK,EAAA,CAAAE,MAAA,gBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAfFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAuF,kBAAA,sDAAA9E,MAAA,CAAAqD,WAAA,MACF;;;ADxeJ,OAAM,MAAOsG,oBAAoB;EA+LrBC,MAAA;EACAC,eAAA;EACAC,KAAA;EACAC,eAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,kBAAA;EACAC,YAAA;EACAC,GAAA;EACDC,UAAA;EAvMgBC,IAAI;EAE7B;EACOtB,iBAAiB,GAAU,EAAE;EAC7BuB,QAAQ,GAAQ,EAAE;EAClBC,cAAc,GAAY,KAAK;EAE/BzB,OAAO,GAAY,KAAK;EACxB0B,SAAS,GAAY,KAAK;EAEjCC,SAAS,GAAQ,EAAE;EAEnB;EACOvK,UAAU,GAAW,EAAE;EACtBwK,WAAW,GAAG,IAAIxL,OAAO,EAAU;EACnCyL,kBAAkB;EAE1B;EACOC,MAAM,GAA8B;IAAEC,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACjEC,UAAU,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACrEE,aAAa,GAIf,EAAE;EAEAC,aAAa,GAAkD,CACpE;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,CACxC;EAED;EACO9I,qBAAqB,GAAG;IAC7BL,MAAM,EAAE,CACN;MAAEkJ,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,CACS;IAClDC,OAAO,EAAE;GACV;EAED;EACO5I,mBAAmB,GAAG,KAAK;EAC3BT,cAAc,GAGjB,EAAE;EAEN;EACOsJ,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7BlI,YAAY,GAAa,EAAE;EAC3BmI,gBAAgB,GAAa,EAAE;EAC/BC,UAAU;EACVC,YAAY;EACZnK,UAAU,GAAG,KAAK;EAEzB;EACOoK,gBAAgB,GAQlB,CACH;IACEC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,4BAA4B;IACnCC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,GAAG;IACVE,IAAI,EAAE,QAAQ;IACdD,OAAO,EAAE,KAAK;IACdG,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,CACF;EAED;EACOE,IAAI,GAAqB,CAAC;IAAEP,KAAK,EAAE,iBAAiB;IAAEQ,GAAG,EAAE;EAAM,CAAE,CAAC;EAE3E;EACOtI,gBAAgB,GAA+C;IACpE,iBAAiB,EAAE,MAAM,CAAC;GAC3B;EAEM1C,IAAI,GAAQ;IACjBiL,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACbjL,aAAa,EAAE,CAAC;IAChBkL,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EAEMC,IAAI,GAAW,CAAC;EAEvB;EACOC,YAAY,GAAU,EAAE;EACxBC,aAAa,GAAY,KAAK;EAErC;EACOpL,aAAa,GAAG,CACrB;IAAEsJ,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAK,CAAE,EAC7B;IAAED,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAU;EACzC;EAAA,CACD;EACD/H,WAAW;EACXN,SAAS;EAETmK,YACUtD,MAAc,EACdC,eAA+B,EAC/BC,KAAqB,EACrBC,eAAgC,EAChCC,eAAiC,EACjCC,wBAAkD,EAClDC,kBAAsC,EACtCC,YAAsB,EACtBC,GAAsB,EACvBC,UAAsB;IATrB,KAAAT,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACJ,KAAAC,UAAU,GAAVA,UAAU;IAEjB;IACA,IAAI,CAACO,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvCwC,IAAI,CAAC/N,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/C+N,SAAS,CAAC,MAAK;MACd;MACA,IAAI,CAACrE,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;MACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAE9C,IAAI,CAACzE,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EAEA0E,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAC3E,SAAS,EAAE;EAClB;EAEA4E,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC/C,kBAAkB,EAAE;MAC3B,IAAI,CAACD,WAAW,CAACiD,QAAQ,EAAE;IAC7B;EACF;EAEQJ,mBAAmBA,CAAA;IACzB;IACA;IACA,IAAI,CAAC9C,SAAS,GAAG,IAAI,CAACL,UAAU,CAACwD,eAAe,EAAE;IAClD;IACA,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEQA,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,MAAMC,WAAW,GAAG,IAAI,CAAC9D,kBAAkB,CAAC+D,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACvD,SAAS,CAACwD,MAAM,CAAC;IAElG,IAAIF,WAAW,EAAE;MACf;MACA,IAAI,CAAC1C,SAAS,GAAG0C,WAAW,CAACzC,UAAU,IAAI,EAAE;MAC7C,IAAI,CAACC,aAAa,GAAGwC,WAAW,CAACxC,aAAa,IAAI,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;MAC1E,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC;IAClD,CAAC,MAAM;MACL;MACA,IAAI,CAACF,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;MAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;IACnD;IAEA;IACA,IAAI,CAACuC,wBAAwB,EAAE;EACjC;EAEQC,4BAA4BA,CAAA;IAClC,MAAMC,MAAM,GAAG;MACbC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,IAAI,CAAC7D,SAAS,CAACwD;KACxB;IAED,IAAI,CAAChE,kBAAkB,CAACsE,aAAa,CAACH,MAAM,CAAC,CAACjB,SAAS,CAAC;MACtDE,IAAI,EAAGmB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,KAAK,KAAK,IAAID,QAAQ,CAACE,IAAI,EAAE;UAC/C;UACA,IAAI,CAACrD,SAAS,GAAGmD,QAAQ,CAACE,IAAI,CAACC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAAC,GAAG,EAAE;UACjF,IAAI,CAACpD,aAAa,GAAGiD,QAAQ,CAACE,IAAI,CAACnD,aAAa,GAAGqD,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACnD,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;UACrH,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC;UAEhD;UACA,IAAI,CAAC2C,wBAAwB,EAAE;UAE/BY,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;YACjD1D,SAAS,EAAE,IAAI,CAACA,SAAS;YACzBE,aAAa,EAAE,IAAI,CAACA;WACrB,CAAC;QACJ,CAAC,MAAM;UACL;UACA,IAAI,CAACF,SAAS,GAAG,EAAE;UACnB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;UAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;UACjD,IAAI,CAACuC,wBAAwB,EAAE;QACjC;MACF,CAAC;MACDc,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,IAAI,CAAC3D,SAAS,GAAG,EAAE;QACnB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;QAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;QACjD,IAAI,CAACuC,wBAAwB,EAAE;MACjC;KACD,CAAC;EACJ;EAEQJ,iBAAiBA,CAAA;IACvB,IAAI,CAACpC,WAAW,GAAG,IAAI,CAACK,gBAAgB,CAACkD,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAClD,KAAK,CAAC;IAChE,IAAI,CAACL,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,WAAW,CAAC;IAC3C,IAAI,CAACjI,YAAY,GAAG,IAAI,CAACsI,gBAAgB,CACtCnB,MAAM,CAAEsE,GAAG,IAAKA,GAAG,CAAC/C,OAAO,CAAC,CAC5B8C,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAClD,KAAK,CAAC;IAC1B,IAAI,CAACJ,gBAAgB,GAAG,IAAI,CAACG,gBAAgB,CAC1CnB,MAAM,CAAEsE,GAAG,IAAK,CAACA,GAAG,CAAC/C,OAAO,CAAC,CAC7B8C,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAClD,KAAK,CAAC;IAE1B8C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;MACxCrD,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnClI,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BmI,gBAAgB,EAAE,IAAI,CAACA;KACxB,CAAC;EACJ;EAEQ6B,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACpD,IAAI,EAAE;MACb;MACA,IAAI,CAAC6D,wBAAwB,EAAE;IACjC;EACF;EAEQA,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAAC7C,SAAS,IAAI,IAAI,CAACA,SAAS,CAACrC,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACyC,YAAY,GAAG,IAAI,CAACJ,SAAS;IACpC;IAEA,IAAI,IAAI,CAACE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACvC,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAI,CAAC+C,gBAAgB,CAACQ,IAAI,CAAC,CAAC4C,CAAC,EAAEC,CAAC,KAAI;QAClC,MAAMC,MAAM,GAAG,IAAI,CAAC9D,aAAa,CAAC+D,OAAO,CAACH,CAAC,CAACnD,KAAK,CAAC;QAClD,MAAMuD,MAAM,GAAG,IAAI,CAAChE,aAAa,CAAC+D,OAAO,CAACF,CAAC,CAACpD,KAAK,CAAC;QAClD,OAAOqD,MAAM,GAAGE,MAAM;MACxB,CAAC,CAAC;IACJ;EACF;EAEA;EACO3G,SAASA,CAAA;IACd,IAAI,CAAC4G,0BAA0B,EAAE;EACnC;EAEA;EACAA,0BAA0BA,CAAA;IACxB,IAAI,CAAC1G,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,MAAMoC,cAAc,GAAGC,UAAU,CAAC,MAAK;MACrCZ,OAAO,CAACa,IAAI,CAAC,mDAAmD,CAAC;MACjE,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX;IACA,MAAMC,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAACtO,IAAI,CAACiL,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfP,IAAI,EAAE,IAAI,CAACA,IAAI;MACf3B,MAAM,EAAE,IAAI,CAACA,MAAM,CAACE,OAAO;MAC3BiF,MAAM,EAAE,IAAI,CAAC7P,UAAU;MACvB8P,cAAc,EAAE,IAAI,CAACvF,SAAS,CAACwD;KAChC;IAEDa,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnCkB,UAAU,EAAE,IAAI,CAAC/P,UAAU;MAC3B2P,KAAK,EAAEA;KACR,CAAC;IAEF,IAAI,CAAC/F,eAAe,CAACoG,uBAAuB,CAACL,KAAK,CAAC,CAAC1C,SAAS,CAAC;MAC5DE,IAAI,EAAG8C,IAYN,IAAI;QACH;QACAC,YAAY,CAACX,cAAc,CAAC;QAE5BX,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEoB,IAAI,CAAC;QAElC;QACA,IACEA,IAAI,CAAC1B,OAAO,IACX0B,IAAI,CAACE,YAAY,IAChBF,IAAI,CAACE,YAAY,CAACC,MAAM,IACxBH,IAAI,CAACE,YAAY,CAACC,MAAM,CAACtH,MAAM,GAAG,CAAE,EACtC;UACA,MAAMsH,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7DxB,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEsB,MAAM,CAAC;UAE9C;UACA,IAAIH,IAAI,CAACE,YAAY,EAAErO,MAAM,KAAK,GAAG,IAAImO,IAAI,CAACnO,MAAM,KAAK,GAAG,EAAE;YAC5D8M,OAAO,CAACa,IAAI,CAAC,6CAA6C,CAAC;YAC3D;YACA;UACF;UAEA,IAAI,CAACY,mBAAmB,EAAE;UAC1B;UACA,IAAI,CAACzH,OAAO,GAAG,KAAK;UACpB,IAAI,CAAC0B,SAAS,GAAG,KAAK;UACtB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QACjD,CAAC,MAAM;UACL;UACA,MAAMgD,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMK,WAAW,GAAGH,YAAY,CAACF,IAAI,IAAI,EAAE;UAC3C,MAAMM,KAAK,GAAGJ,YAAY,CAACI,KAAK,IAAI,CAAC;UAErC,IAAI,CAAClG,cAAc,GAAGiG,WAAW,CAACxH,MAAM,KAAK,CAAC;UAC9C,IAAI,CAACD,iBAAiB,GAAGyH,WAAW;UACpC,IAAI,CAAChP,IAAI,CAACC,aAAa,GAAGgP,KAAK;UAC/B,IAAI,CAACjP,IAAI,CAACmL,UAAU,GAAG+D,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACjP,IAAI,CAACiL,IAAI,CAAC;UAExD;UACA,IAAI,CAACnC,QAAQ,GAAG;YACd6F,IAAI,EAAEK,WAAW;YACjBC,KAAK,EAAEA;WACR;UACD3B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAChG,iBAAiB,CAAC;UAC9D+F,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACzE,QAAQ,CAAC;UAC5CwE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACxE,cAAc,CAAC;UACxDuE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACvN,IAAI,CAAC;UACpC,IAAI,CAAC2I,GAAG,CAACyG,YAAY,EAAE;UACvB;UACA,IAAI,CAAC9H,OAAO,GAAG,KAAK;UACpB,IAAI,CAAC0B,SAAS,GAAG,KAAK;UACtB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QACjD;MACF,CAAC;MACD2B,KAAK,EAAGA,KAAc,IAAI;QACxB;QACAoB,YAAY,CAACX,cAAc,CAAC;QAE5BX,OAAO,CAACE,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAElE;QACA,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAIA,KAAK,EAAE;UAC3D,MAAM6B,SAAS,GAAG7B,KAAY;UAC9B,IAAI6B,SAAS,CAAC7O,MAAM,KAAK,GAAG,EAAE;YAC5B8M,OAAO,CAACa,IAAI,CAAC,6CAA6C,CAAC;YAC3D;YACA;UACF;QACF;QAEA,IAAI,CAACY,mBAAmB,EAAE;QAC1B,IAAI,CAACzH,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC0B,SAAS,GAAG,KAAK;QACtB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDM,QAAQ,EAAEA,CAAA,KAAK;QACb;QACAyC,YAAY,CAACX,cAAc,CAAC;QAE5B;QACA,IAAI,CAAC3G,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC0B,SAAS,GAAG,KAAK;QACtB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEQkD,mBAAmBA,CAAA;IACzB,IAAI,CAAChG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACxB,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACuB,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC9I,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAACmL,UAAU,GAAG,CAAC;IAExB;IACA,IAAI,CAAC7D,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC0B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;EACjD;EAEA;EACQuC,kBAAkBA,CAAA;IACxB,IAAI,CAAC9G,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC0B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;EACjD;EAEA;EACOnM,WAAWA,CAAA;IAChB4N,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,IAAI,CAACa,kBAAkB,EAAE;IACzB,IAAI,CAAChH,SAAS,EAAE;EAClB;EAGA;EACOtI,eAAeA,CAACwQ,KAAoB;IACzC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzBjC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC7O,UAAU,CAAC;MAE9D;MACA,IAAI,CAAC4I,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;MACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAE9C,IAAI,CAACzE,SAAS,EAAE;IAClB;EACF;EAEOrI,cAAcA,CAAA;IACnBuO,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC7O,UAAU,CAAC;IAC/C;IACA,IAAI,CAACwK,WAAW,CAAC2C,IAAI,CAAC,IAAI,CAACnN,UAAU,CAAC;EACxC;EAGQ8Q,WAAWA,CAAA;IACjB;IACA,IAAI,CAAClI,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAACzE,SAAS,EAAE;EAClB;EAEOqI,WAAWA,CAAA;IAChB;IACA,IAAI,CAAC/Q,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAAC4I,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACzE,SAAS,EAAE;EAClB;EAEA;EACOsI,oBAAoBA,CAAA;IACzBpC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAAC7O,UAAU,GAAG,aAAa,CAAC,CAAC;IAEjC;IACA,IAAI,CAAC4I,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAACzE,SAAS,EAAE;EAClB;EAEA;EACOuI,YAAYA,CAACvG,MAAiC;IACnD,IAAI,CAACA,MAAM,GAAGA,MAAM;IAEpB;IACA,IAAI,CAAC9B,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAACzE,SAAS,EAAE;EAClB;EAEO1G,oBAAoBA,CAAA;IACzB;IACA,IAAI,IAAI,CAACH,cAAc,CAACC,MAAM,EAAE;MAC9B,IAAI,CAAC4I,MAAM,CAACE,OAAO,GAAG,IAAI,CAACF,MAAM,CAACE,OAAO,CAACF,MAAM,CAAEwG,CAAC,IAAI;QACrD,IAAI,OAAO,IAAIA,CAAC,EAAE;UAChB,OAAOA,CAAC,CAACpF,KAAK,KAAK,eAAe;QACpC;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAACpB,MAAM,CAACE,OAAO,CAACuG,IAAI,CAAC;QACvBrF,KAAK,EAAE,eAAe;QACtBsF,QAAQ,EAAE,IAAI;QACdnG,KAAK,EAAE,IAAI,CAACpJ,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACD,cAAc,CAACwP,MAAM,EAAE;MAC9B,IAAI,CAAC3G,MAAM,CAACE,OAAO,GAAG,IAAI,CAACF,MAAM,CAACE,OAAO,CAACF,MAAM,CAAEwG,CAAC,IAAI;QACrD,IAAI,OAAO,IAAIA,CAAC,EAAE;UAChB,OAAOA,CAAC,CAACpF,KAAK,KAAK,UAAU;QAC/B;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAACpB,MAAM,CAACE,OAAO,CAACuG,IAAI,CAAC;QACvBrF,KAAK,EAAE,UAAU;QACjBsF,QAAQ,EAAE,IAAI;QACdnG,KAAK,EAAE,IAAI,CAACpJ,cAAc,CAACwP;OAC5B,CAAC;IACJ;IAEA,IAAI,CAAC3I,SAAS,EAAE;EAClB;EAEO4I,oBAAoBA,CAAA;IACzB,IAAI,CAACzP,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC6I,MAAM,CAACE,OAAO,GAAG,EAAE;IAExB;IACA,IAAI,CAAChC,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAACzE,SAAS,EAAE;EAClB;EAEOxG,eAAeA,CAAA;IACpB,IAAI,CAACL,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC6I,MAAM,CAACE,OAAO,GAAG,EAAE;IAExB;IACA,IAAI,CAAChC,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAACzE,SAAS,EAAE;EAClB;EAEA;EACO7E,YAAYA,CAACiI,KAAa;IAC/B8C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE/C,KAAK,CAAC;IAEtD;IACA,MAAMyF,YAAY,GAAG,IAAI,CAACvN,gBAAgB,CAAC8H,KAAK,CAAC,IAAI,MAAM;IAE3D;IACA,IAAI0F,SAAkC;IACtC,IAAIC,OAAuB;IAE3B,QAAQF,YAAY;MAClB,KAAK,MAAM;QACTC,SAAS,GAAG,KAAK;QACjBC,OAAO,GAAG,KAAK;QACf;MACF,KAAK,KAAK;QACRD,SAAS,GAAG,MAAM;QAClBC,OAAO,GAAG,MAAM;QAChB;MACF,KAAK,MAAM;QACTD,SAAS,GAAG,MAAM;QAClBC,OAAO,GAAG,MAAM,CAAC,CAAC;QAClB;IACJ;IAEA;IACA,IAAI,CAACzN,gBAAgB,CAAC8H,KAAK,CAAC,GAAG0F,SAAS;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3N,gBAAgB,CAAC,CAAC4N,OAAO,CAACf,GAAG,IAAG;MAC/C,IAAIA,GAAG,KAAK/E,KAAK,EAAE;QACjB,IAAI,CAAC9H,gBAAgB,CAAC6M,GAAG,CAAC,GAAG,MAAM;MACrC;IACF,CAAC,CAAC;IAEF;IACA,IAAIW,SAAS,KAAK,MAAM,EAAE;MACxB;MACA,IAAI,CAACnF,IAAI,GAAG,CAAC;QAAEP,KAAK,EAAE,iBAAiB;QAAEQ,GAAG,EAAE;MAAM,CAAE,CAAC;MACvD,IAAI,CAACtI,gBAAgB,CAAC8H,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;MACvC8C,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C,CAAC,MAAM;MACL;MACA,IAAI,CAACxC,IAAI,GAAG,CAAC;QAAEP,KAAK,EAAEA,KAAK;QAAEQ,GAAG,EAAEmF;MAAO,CAAE,CAAC;MAC5C7C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACxC,IAAI,CAAC;IAC1C;IAEA;IACA,IAAI,CAAC/K,IAAI,CAACoL,OAAO,GAAG,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC,CAACP,KAAK;IACtC,IAAI,CAACxK,IAAI,CAACqL,QAAQ,GAAG,IAAI,CAACN,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG;IAErC;IACA,IAAI,CAAChL,IAAI,CAACkL,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IAEbgC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACxC,IAAI,CAAC;IAC3CuC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC7K,gBAAgB,CAAC;IAEzD,IAAI,CAAC0E,SAAS,EAAE;EAClB;EAEA;EACOmJ,iBAAiBA,CAACjB,KAAU;IACjChC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE+B,KAAK,CAAC;IAExC;IACA,IAAIA,KAAK,CAAChE,IAAI,KAAKkF,SAAS,EAAE;MAC5B,IAAI,CAAClF,IAAI,GAAGgE,KAAK,CAAChE,IAAI;MACtB,IAAI,CAACtL,IAAI,CAACkL,UAAU,GAAGgE,IAAI,CAACuB,KAAK,CAACnB,KAAK,CAAChE,IAAI,GAAG,IAAI,CAACtL,IAAI,CAACiL,IAAI,CAAC;IAChE;IAEA,IAAIqE,KAAK,CAAChB,IAAI,KAAKkC,SAAS,EAAE;MAC5B,IAAI,CAACxQ,IAAI,CAACiL,IAAI,GAAGqE,KAAK,CAAChB,IAAI;IAC7B;IAEA,IAAI,CAAClH,SAAS,EAAE;EAClB;EAEA;EACOsJ,YAAYA,CAAC3F,IAAsB;IACxCuC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAExC,IAAI,CAAC;IAC3C,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC3D,SAAS,EAAE;EAClB;EAEA;EACOuJ,UAAUA,CAACrB,KAAU;IAC1B;IACA,IAAI,CAAChE,IAAI,GAAGgE,KAAK,CAAChE,IAAI;IACtB,IAAI,CAACtL,IAAI,CAACiL,IAAI,GAAGqE,KAAK,CAAChB,IAAI,IAAI,IAAI,CAACtO,IAAI,CAACiL,IAAI;IAC7C,IAAI,CAACjL,IAAI,CAACkL,UAAU,GAAGgE,IAAI,CAACuB,KAAK,CAAC,IAAI,CAACnF,IAAI,GAAG,IAAI,CAACtL,IAAI,CAACiL,IAAI,CAAC;IAE7D;IACA,IAAI,CAAC3D,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAACzE,SAAS,EAAE;EAClB;EAEA;EACOwJ,eAAeA,CAACtB,KAAU;IAC/B;IACA,MAAMuB,gBAAgB,GAAGvB,KAAK,CAACwB,OAAO,CAACrD,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAAClD,KAAK,CAAC;IACnE,IAAI,CAACT,aAAa,GAAG8G,gBAAgB;EACvC;EAEOE,sBAAsBA,CAACzB,KAAU;IACtC;IACA,MAAM0B,aAAa,GAAG1B,KAAK,CAAC0B,aAAa,IAAI,EAAE;IAC/C,IAAI,CAAC/G,YAAY,GAAG+G,aAAa;IAEjC;IACA,IAAI,CAAC9G,WAAW,GAAG,IAAI,CAACC,cAAc,CAACf,MAAM,CAAC6H,MAAM,IAClD,CAACD,aAAa,CAAC9O,QAAQ,CAAC+O,MAAM,CAAC,CAChC;IAED3D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;MACxCyD,aAAa,EAAEA,aAAa;MAC5BE,cAAc,EAAE,IAAI,CAAChH,WAAW;MAChCiH,UAAU,EAAE,IAAI,CAAChH;KAClB,CAAC;EACJ;EAEO/H,cAAcA,CAACgP,SAAiB;IACrC,OAAO,IAAI,CAACnH,YAAY,CAAC/H,QAAQ,CAACkP,SAAS,CAAC;EAC9C;EAEA;EACOC,iBAAiBA,CAAC/B,KAAU;IACjC,IAAI,CAAC/D,YAAY,GAAG+D,KAAK,CAAC/D,YAAY,IAAI,EAAE;IAC5C,IAAI,CAACC,aAAa,GAChB,IAAI,CAACD,YAAY,CAAC/D,MAAM,KAAK,IAAI,CAACD,iBAAiB,CAACC,MAAM;EAC9D;EAEO8J,SAASA,CAAA;IACd,IAAI,IAAI,CAAC9F,aAAa,EAAE;MACtB,IAAI,CAACD,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACD,YAAY,GAAG,CAAC,GAAG,IAAI,CAAChE,iBAAiB,CAAC;MAC/C,IAAI,CAACiE,aAAa,GAAG,IAAI;IAC3B;EACF;EAEA;EACOpM,YAAYA,CAAA;IACjB;IACA,MAAMmS,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiB,CACH;IAChB,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAACxR,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAAC0I,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAAC+I,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAtS,aAAaA,CAACgQ,KAAU;IACtB,MAAMuC,cAAc,GAAGvC,KAAK,CAAC3F,KAAK,CAAC,CAAC;IAEpC,IAAImI,QAAQ,GAAQ,EAAE;IACtB,IAAID,cAAc,KAAK,UAAU,EAAE;MACjCC,QAAQ,GAAG,IAAI,CAACvK,iBAAiB;MAEjC;MACA;MACA,IAAI,CAACwK,WAAW,CAACD,QAAQ,CAAC;IAC5B,CAAC,MAAM,IAAID,cAAc,KAAK,KAAK,EAAE;MACnC,MAAMG,gBAAgB,GAAG;QACvBC,QAAQ,EAAE,IAAI,CAACjS,IAAI,CAACC,aAAa;QACjCiS,SAAS,EAAE,IAAI,CAAClS,IAAI,CAACqL,QAAQ;QAC7B8G,SAAS,EAAE,IAAI,CAACnS,IAAI,CAACoL,OAAO;QAC5BF,UAAU,EAAE,IAAI,CAAClL,IAAI,CAACkL;QACtB;OACD;MAED;MACA,IAAI,CAAC3C,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAC9C;MACA,IAAI,CAACvD,eAAe,CACjB8J,cAAc,CAACJ,gBAAgB;MAChC;MAAA,CACCrG,SAAS,CAAEgD,IAAI,IAAI;QAClB;QACA,IAAI,CAACpG,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI8C,IAAI,CAAC1B,OAAO,EAAE;UAChB,IAAI,CAAClE,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACJ,GAAG,CAACyG,YAAY,EAAE;UACvB,OAAO,CAAC;QACV;QAEA,IAAI,CAACrG,cAAc,GAAG,IAAI;QAC1B+I,QAAQ,GAAGnD,IAAI,CAACE,YAAY,CAACF,IAAI,IAAI,EAAE;QAEvC,IAAI,CAAChG,GAAG,CAAC0J,aAAa,EAAE,CAAC,CAAC;QAC1B,IAAI,CAACN,WAAW,CAACD,QAAQ,CAAC;MAC5B,CAAC,CAAC;IACN;EACF;EAEAC,WAAWA,CAACO,WAAgB;IAC1B;IACA,IAAIR,QAAQ,GAAQQ,WAAW;IAC/B,IAAIC,WAAW,GAAS,IAAI,CAAC3J,UAAU,CAAC4J,eAAe,CAAC,IAAIC,IAAI,EAAE,CAAC;IAEnEnF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuE,QAAQ,CAAC;IAEjC;IACA,IAAIA,QAAQ,KAAKtB,SAAS,IAAIsB,QAAQ,CAACtK,MAAM,GAAG,CAAC,EAAE;MACjD;MACA,MAAMkL,UAAU,GAAG,QAAQ;MAE3B;MACA;MACA;MACA;MAEA;MAEA,MAAMC,WAAW,GAAG,CAClB,cAAc,EACd,mBAAmB,EACnB,YAAY,EACZ,UAAU,EACV,UAAU,EACV,SAAS,EACT,aAAa,CACd;MACD;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA,MAAMC,iBAAiB,GAAQ,EAAE;MAEjC;MACA,MAAMC,UAAU,GAAQ,EAAE;MAE1B;MACApV,IAAI,CAACqU,QAAQ,EAAGgB,OAAY,IAAI;QAC9B;QACA,MAAMC,QAAQ,GAAGC,KAAK,CAACL,WAAW,CAACnL,MAAM,CAAC,CAACyL,IAAI,CAAC,IAAI,CAAC;QACrDF,QAAQ,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACI,gBAAgB;QACtCH,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAACnK,UAAU,CAAC4J,eAAe,CAACM,OAAO,CAACK,UAAU,CAAC;QACjE;QACAR,WAAW,CAACrC,OAAO,CAAC,CAAC5C,GAAQ,EAAE0F,CAAS,KAAI;UAC1C,MAAMC,aAAa,GAAGD,CAAC,CAAC,CAAC;UACzB,QAAQ1F,GAAG;YACT,KAAK,cAAc;cACjBqF,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAAClR,WAAW;cAC7C;YACF,KAAK,mBAAmB;cACtBmR,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAACvP,qBAAqB;cACvD;YACF,KAAK,YAAY;cACfwP,QAAQ,CAACM,aAAa,CAAC,GAAG,IAAI,CAACzK,UAAU,CAAC/E,UAAU,CAACiP,OAAO,CAAC/O,gBAAgB,CAAC;cAC9E;YACF,KAAK,UAAU;cACbgP,QAAQ,CAACM,aAAa,CAAC,GAAG,IAAI,CAACzK,UAAU,CAAC/E,UAAU,CAACiP,OAAO,CAACzO,cAAc,CAAC;cAC5E;YACF,KAAK,UAAU;cACb0O,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAACnO,eAAe;cACjD;YACF,KAAK,SAAS;cACZoO,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAAC7N,0BAA0B;cAC5D;YACF,KAAK,aAAa;cAChB8N,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAACvN,eAAe;cACjD;YACF;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACF;QACF,CAAC,CAAC;QAEFsN,UAAU,CAAChD,IAAI,CAACkD,QAAQ,CAAC;MAC3B,CAAC,CAAC;MAEF;MACA,MAAMO,OAAO,GAAGX,WAAW,CAAClF,GAAG,CAAC,CAAC8F,MAAM,EAAEC,KAAK,MAAM;QAClDC,EAAE,EAAED,KAAK,GAAG,CAAC;QACb9I,KAAK,EAAE;OACR,CAAC,CAAC;MAEH;MACA,IAAI,CAACtC,eAAe,CAACsL,aAAa,CAChChB,UAAU,EACVC,WAAW,EACXE,UAAU,EACVS;MACA;MACA;OACD;IACH,CAAC,MAAM;MACL,MAAMK,OAAO,GAAG,2CAA2C;MAC3D;IACF;EACF;EACA;EACOC,QAAQA,CAAA;IACb,MAAMC,QAAQ,GAAG;MACfhK,SAAS,EAAE,IAAI,CAACI,YAAY;MAC5BF,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,iBAAiB,EAAE,IAAI,CAACA;KACzB;IAED;IACA,IAAI,CAACvB,kBAAkB,CAACqL,kBAAkB,CAAC;MACzCjH,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,IAAI,CAAC7D,SAAS,CAACwD,MAAM;MAC7B3C,UAAU,EAAE+J,QAAQ,CAAChK,SAAS;MAC9BE,aAAa,EAAE8J,QAAQ,CAAC9J,aAAa;MACrCgK,QAAQ,EAAE,IAAI,CAAC9K,SAAS,CAACwD;KAC1B,CAAC;IAEFa,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEsG,QAAQ,CAAC;IAC/B,IAAI,CAACrL,wBAAwB,CAACwL,WAAW,CAAC,+BAA+B,EAAE,EAAE,CAAC;IAEtG;EACF;EAEQC,0BAA0BA,CAACJ,QAAa;IAC9C,MAAMjH,MAAM,GAAG;MACbC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,IAAI,CAAC7D,SAAS,CAACwD,MAAM;MAC7B3C,UAAU,EAAE+J,QAAQ,CAAChK,SAAS;MAC9BE,aAAa,EAAE8J,QAAQ,CAAC9J,aAAa;MACrCgK,QAAQ,EAAE,IAAI,CAAC9K,SAAS,CAACwD;KAC1B;IAED,IAAI,CAAChE,kBAAkB,CAACyL,gBAAgB,CAACtH,MAAM,CAAC,CAACjB,SAAS,CAAC;MACzDE,IAAI,EAAGmB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,KAAK,KAAK,EAAE;UAC9BK,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEP,QAAQ,CAAC;UAE1C,IAAI,CAACxE,wBAAwB,CAACwL,WAAW,CAAC,oCAAoC,EAAE,EAAE,CAAC;UACrG;QACF,CAAC,MAAM;UACL1G,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAER,QAAQ,CAAC2G,OAAO,CAAC;UAChD,IAAI,CAACnL,wBAAwB,CAAC2L,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;UAE/F;QACF;MACF,CAAC;MACD3G,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACjC,IAAI,CAAChF,wBAAwB,CAAC2L,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;QAE/F;MACF;KACD,CAAC;EACJ;EAEQC,iBAAiBA,CAAA;IACvB;IACA,MAAMC,YAAY,GAAG;MACnBxH,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,IAAI,CAAC7D,SAAS,CAACwD;KACxB;IAED,IAAI,CAAChE,kBAAkB,CAAC6L,gBAAgB,CAACD,YAAY,CAAC,CAAC1I,SAAS,CAAC;MAC/DE,IAAI,EAAGmB,QAAQ,IAAI;QACjBM,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEP,QAAQ,CAAC;QACnD;QACA,IAAI,CAACiH,0BAA0B,CAAC;UAC9BpK,SAAS,EAAE,EAAE;UACbE,aAAa,EAAE,IAAI,CAACI,cAAc;UAClCH,iBAAiB,EAAE,IAAI,CAACG;SACzB,CAAC;MACJ,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD;QACA,IAAI,CAACyG,0BAA0B,CAAC;UAC9BpK,SAAS,EAAE,EAAE;UACbE,aAAa,EAAE,IAAI,CAACI,cAAc;UAClCH,iBAAiB,EAAE,IAAI,CAACG;SACzB,CAAC;MACJ;KACD,CAAC;EACJ;EAEO3K,UAAUA,CAAA;IACf8N,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IAEpD;IACA,IAAI,CAAC1D,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAE3B;IACA,IAAI,CAACvB,kBAAkB,CAAC8L,qBAAqB,CAAC,UAAU,CAAC;IAEzD;IACA,IAAI,CAACC,sBAAsB,EAAE;IAE7B;IACA,IAAI,CAAC7L,GAAG,CAAC0J,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACxJ,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC+I,OAAO,EAAE;IACrB;IAEA;IACAtE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;EACF;EACQiH,sBAAsBA,CAAA;IAC5BlH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAE/C;IACA,IAAI,CAACtD,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC3C,IAAI,CAACJ,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;IAE7C;IACA,IAAI,CAACY,IAAI,GAAG,CAAC;MAAEP,KAAK,EAAE,iBAAiB;MAAEQ,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAChL,IAAI,CAACoL,OAAO,GAAG,iBAAiB;IACrC,IAAI,CAACpL,IAAI,CAACqL,QAAQ,GAAG,MAAM;IAE3B;IACA,IAAI,CAACrL,IAAI,CAACkL,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IAEb;IACA,IAAI,CAAClC,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACE,aAAa,GAAG,EAAE;IAEvB;IACA,IAAI,CAACjJ,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAC7B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACsC,mBAAmB,GAAG,KAAK;IAEhCsM,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9BtD,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCY,IAAI,EAAE,IAAI,CAACA,IAAI;MACf3B,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB1K,UAAU,EAAE,IAAI,CAACA;KAClB,CAAC;IAEF;IACA,IAAI,IAAI,CAACmK,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACO,MAAM,GAAG;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACT,IAAI,CAACkC,IAAI,GAAG,CAAC;QAAEP,KAAK,EAAE,iBAAiB;QAAEQ,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAACnC,IAAI,CAACiI,OAAO,CAACR,OAAO,CAAEW,MAAW,IAAI;QACxC,IAAIA,MAAM,CAACzG,KAAK,IAAIyG,MAAM,CAACzG,KAAK,KAAK,QAAQ,EAAE;UAC7CyG,MAAM,CAACwD,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAC5L,IAAI,CAACyC,IAAI,GAAG,CAAC;MAClB,IAAI,CAACzC,IAAI,CAACoJ,QAAQ,GAAG,IAAI,CAACjS,IAAI,CAACiL,IAAI;IACrC;IAEA;IACA,IAAI,CAACtC,GAAG,CAAC0J,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACxJ,IAAI,EAAE;MACbqF,UAAU,CAAC,MAAK;QACd,IAAI,CAACrF,IAAI,CAAC+I,OAAO,EAAE;QACnB;QACA,IAAI,CAAC/I,IAAI,CAAC6L,KAAK,EAAE;MACnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACA,IAAI,CAACtN,SAAS,EAAE;EAClB;EACA;EACOlI,GAAGA,CAAA;IACR,IAAI,CAACmC,IAAI,CAAC,CAAC,CAAC;EACd;EAEOsT,IAAIA,CAACrT,SAAiB;IAC3B,IAAI,CAAC6G,MAAM,CAACyM,QAAQ,CAAC,CAAC,gBAAgB,EAAEtT,SAAS,CAAC,CAAC;EACrD;EAEOD,IAAIA,CAACC,SAAiB;IAC3B,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB;MACA,MAAMuT,QAAQ,GAAG,IAAI,CAACnM,YAAY,CAACoM,IAAI,CAACjX,qBAAqB,EAAE;QAC7DoN,IAAI,EAAE,IAAI;QACV8J,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;OACX,CAAC;MAEFH,QAAQ,CAACI,iBAAiB,CAACxB,EAAE,GAAGnS,SAAS;MACzCuT,QAAQ,CAACI,iBAAiB,CAACC,OAAO,GAAG,IAAI;MAEzCL,QAAQ,CAACI,iBAAiB,CAACE,SAAS,CAACxJ,SAAS,CAAEyJ,MAAe,IAAI;QACjE,IAAIA,MAAM,EAAE;UACV;UACA,IAAI,CAAChO,SAAS,EAAE;QAClB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACe,MAAM,CAACyM,QAAQ,CAAC,CAAC,gBAAgB,EAAEtT,SAAS,CAAC,CAAC;IACrD;EACF;EAEO+T,MAAMA,CAAC/T,SAAiB;IAC7B;IAEA;IACC,IAAI,CAACgH,eAAe,CAACgN,aAAa,CAAC;MAAEhU;IAAS,CAAE,CAAC,CAACqK,SAAS,CAAC;MACzDE,IAAI,EAAGmB,QAAa,IAAI;QAEtBM,OAAO,CAACC,GAAG,CAAC,UAAU,EAACP,QAAQ,CAAC;QAChC,IAAI,CAACA,QAAQ,CAACC,OAAO,EAAE;UACL,IAAI,CAACzE,wBAAwB,CAACwL,WAAW,CAAC,8BAA8B,EAAE,EAAE,CAAC;UAE7F;UACA,IAAI,CAAC5M,SAAS,EAAE;QAClB;MACF,CAAC;MACDoG,KAAK,EAAGA,KAAU,IAAI;QACpBF,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACnB,IAAI,CAAChF,wBAAwB,CAAC2L,SAAS,CAAC,wBAAwB,EAAE,EAAE,CAAC;QAEvF;MACF;KACD,CAAC;EACN;EAEA;EACOoB,kBAAkBA,CAACL,OAAY;IACpC,OAAO,GAAGA,OAAO,CAACM,gBAAgB,IAAI,EAAE,IACtCN,OAAO,CAACO,eAAe,IAAI,EAC7B,EAAE,CAACC,IAAI,EAAE;EACX;EAEOC,aAAaA,CAACT,OAAY;IAC/B,OAAOA,OAAO,CAACU,aAAa,EAAEC,UAAU,IAAI,EAAE;EAChD;EAEOhS,UAAUA,CAACiS,UAAkB;IAClC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAItD,IAAI,CAACqD,UAAU,CAAC;IACjC,MAAME,KAAK,GAAG,CAACD,IAAI,CAACE,QAAQ,EAAE,GAAG,CAAC,EAAEC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/D,MAAMC,GAAG,GAAGL,IAAI,CAACM,OAAO,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,MAAMG,IAAI,GAAGP,IAAI,CAACQ,WAAW,EAAE;IAC/B,OAAO,GAAGP,KAAK,IAAII,GAAG,IAAIE,IAAI,EAAE;EAClC;EAEOE,cAAcA,CAAChW,MAAc;IAClC,OAAOA,MAAM,KAAK,QAAQ,GAAG,qBAAqB,GAAG,oBAAoB;EAC3E;EACAmB,SAASA,CAAC8U,OAAY,EAAEnV,SAAc,EAAEM,WAAgB;IACtD,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACN,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACoH,YAAY,CAACoM,IAAI,CAAC2B,OAAO,EAAE;MAAE1B,QAAQ,EAAE;IAAI,CAAE,CAAC;EACrD;EAEA;EACAlN,mBAAmBA,CAACgN,QAAa;IAC/B;IACA,IAAI,CAACtM,eAAe,CAACqD,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;IAC/CgJ,QAAQ,CAAC6B,OAAO,EAAE;EACpB;EAEA1O,aAAaA,CAAA;IACX;IACA,IAAI,CAACqN,MAAM,CAAC,IAAI,CAAC/T,SAAS,CAAC;IAC3B;EACF;EAEAqV,cAAcA,CAAA;IACZ;IACA;IACArJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC;;qCAryCWrF,oBAAoB,EAAApK,EAAA,CAAA8Y,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhZ,EAAA,CAAA8Y,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAlZ,EAAA,CAAA8Y,iBAAA,CAAAC,EAAA,CAAAI,cAAA,GAAAnZ,EAAA,CAAA8Y,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAArZ,EAAA,CAAA8Y,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAvZ,EAAA,CAAA8Y,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAAzZ,EAAA,CAAA8Y,iBAAA,CAAAY,EAAA,CAAAC,kBAAA,GAAA3Z,EAAA,CAAA8Y,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAA7Z,EAAA,CAAA8Y,iBAAA,CAAA9Y,EAAA,CAAA8Z,iBAAA,GAAA9Z,EAAA,CAAA8Y,iBAAA,CAAAiB,EAAA,CAAAC,UAAA;EAAA;;UAApB5P,oBAAoB;IAAA6P,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QCrCjCpa,EAAA,CAAAgD,UAAA,IAAAsX,mCAAA,iBAAqE;QAUnEta,EADF,CAAAC,cAAA,aAA4B,uBA+BzB;QAFCD,EAZA,CAAAc,UAAA,2BAAAyZ,kEAAAja,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAia,GAAA;UAAA,OAAAxa,EAAA,CAAAa,WAAA,CAAiBwZ,GAAA,CAAAvH,eAAA,CAAAxS,MAAA,CAAuB;QAAA,EAAC,6BAAAma,oEAAAna,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAia,GAAA;UAAA,OAAAxa,EAAA,CAAAa,WAAA,CACtBwZ,GAAA,CAAA9G,iBAAA,CAAAjT,MAAA,CAAyB;QAAA,EAAC,0BAAAoa,iEAAApa,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAia,GAAA;UAAA,OAAAxa,EAAA,CAAAa,WAAA,CAQ7BwZ,GAAA,CAAAxI,YAAA,CAAAvR,MAAA,CAAoB;QAAA,EAAC,wBAAAqa,+DAAAra,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAia,GAAA;UAAA,OAAAxa,EAAA,CAAAa,WAAA,CACvBwZ,GAAA,CAAAxH,UAAA,CAAAvS,MAAA,CAAkB;QAAA,EAAC,6BAAAsa,oEAAAta,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAia,GAAA;UAAA,OAAAxa,EAAA,CAAAa,WAAA,CACdwZ,GAAA,CAAA5H,iBAAA,CAAAnS,MAAA,CAAyB;QAAA,EAAC,oCAAAua,2EAAAva,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAia,GAAA;UAAA,OAAAxa,EAAA,CAAAa,WAAA,CACnBwZ,GAAA,CAAApH,sBAAA,CAAA3S,MAAA,CAA8B;QAAA,EAAC;QAoczDN,EAjcA,CAAAgD,UAAA,IAAA8X,2CAAA,2BAAsC,IAAAC,2CAAA,yBAyFA,IAAAC,4CAAA,2BAqCW,IAAAC,2CAAA,yBAmUT;QAkB5Cjb,EADE,CAAAG,YAAA,EAAa,EACT;QAGNH,EAAA,CAAAgD,UAAA,IAAAkY,2CAAA,iCAAAlb,EAAA,CAAAmb,sBAAA,CAAoC;;;QA/f9Bnb,EAAA,CAAAgC,UAAA,SAAAqY,GAAA,CAAA7Q,OAAA,IAAA6Q,GAAA,CAAAnP,SAAA,CAA0B;QAY5BlL,EAAA,CAAA6B,SAAA,GAAiB;QA2BjB7B,EA3BA,CAAAgC,UAAA,SAAAqY,GAAA,CAAArP,QAAA,CAAiB,aAAAqP,GAAA,CAAAnY,IAAA,CAAAiL,IAAA,CACK,SAAAkN,GAAA,CAAApN,IAAA,CACT,aAAAjN,EAAA,CAAAob,eAAA,KAAAC,GAAA,EAAArb,EAAA,CAAAiE,eAAA,KAAAqX,GAAA,GAOX,UAAAjB,GAAA,CAAAnY,IAAA,CAAAC,aAAA,CAC0B,mBACV,oBACC,eAAAnC,EAAA,CAAAiE,eAAA,KAAAsX,GAAA,EACoC,qBAGnC,oBAED,eACL,SAAAlB,GAAA,CAAA7M,IAAA,CACD,WAAA6M,GAAA,CAAA/O,MAAA,CACI,eAAAtL,EAAA,CAAAiE,eAAA,KAAAuX,GAAA,EACc,kBAKd;QAgIgBxb,EAAA,CAAA6B,SAAA,GAAc;QAAd7B,EAAA,CAAAgC,UAAA,YAAAqY,GAAA,CAAAjO,WAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}