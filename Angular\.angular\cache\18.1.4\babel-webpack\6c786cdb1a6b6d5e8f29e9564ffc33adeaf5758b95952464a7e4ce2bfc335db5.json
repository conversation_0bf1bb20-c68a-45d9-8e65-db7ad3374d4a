{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { jwtDecode } from 'jwt-decode';\nimport { ChangePasswordComponent } from '../change-password/change-password.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/modules/services/app.service\";\nimport * as i5 from \"src/app/modules/services/http-utils.service\";\nimport * as i6 from \"src/app/modules/services/custom-layout.utils.service\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"src/app/_metronic/layout/core/page-info.service\";\nimport * as i9 from \"@angular/platform-browser\";\nimport * as i10 from \"@angular/common\";\nconst _c0 = (a0, a1) => ({\n  \"is-invalid\": a0,\n  \"is-valid\": a1\n});\nconst _c1 = a0 => ({\n  validation: \"required\",\n  message: \"Email is required\",\n  control: a0\n});\nconst _c2 = a0 => ({\n  validation: \"email\",\n  message: \"Email is invalid\",\n  control: a0\n});\nconst _c3 = a0 => ({\n  validation: \"required\",\n  message: \"Password is required\",\n  control: a0\n});\nfunction LoginComponent_ng_template_36_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"span\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r2 = i0.ɵɵnextContext().message;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", message_r2, \" \");\n  }\n}\nfunction LoginComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LoginComponent_ng_template_36_ng_container_0_Template, 4, 1, \"ng-container\", 25);\n  }\n  if (rf & 2) {\n    const control_r3 = ctx.control;\n    const validation_r4 = ctx.validation;\n    i0.ɵɵproperty(\"ngIf\", control_r3.hasError(validation_r4) && (control_r3.dirty || control_r3.touched));\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    fb;\n    authService;\n    route;\n    appService;\n    httpUtilService;\n    customLayoutUtilsService;\n    router;\n    modalService;\n    pageInfo;\n    titleService;\n    // KeenThemes mock, change it to:\n    defaultAuth = {\n      email: '<EMAIL>',\n      password: 'SwiJZf'\n    };\n    loginForm;\n    hasError;\n    returnUrl;\n    isLoading$;\n    passwordshown = true; // password show/hide\n    userData = {};\n    // private fields\n    unsubscribe = []; // Read more: => https://brianflove.com/2016/12/11/anguar-2-unsubscribe-observables/\n    constructor(fb, authService, route, appService, httpUtilService, customLayoutUtilsService, router, modalService, pageInfo, titleService) {\n      this.fb = fb;\n      this.authService = authService;\n      this.route = route;\n      this.appService = appService;\n      this.httpUtilService = httpUtilService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.router = router;\n      this.modalService = modalService;\n      this.pageInfo = pageInfo;\n      this.titleService = titleService;\n      this.isLoading$ = this.authService.isLoading$;\n      // redirect to home if already logged in\n      if (this.authService.currentUserValue) {\n        this.router.navigate(['/']);\n      }\n    }\n    ngOnInit() {\n      // Ensure title is set in auth layout where global scripts may be inactive\n      this.pageInfo.updateTitle('Login');\n      this.titleService.setTitle('Login - Permit Tracker');\n      this.initForm();\n      // get return url from route parameters or default to '/'\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'.toString()] || '/';\n    }\n    // convenience getter for easy access to form fields\n    get f() {\n      return this.loginForm.controls;\n    }\n    initForm() {\n      this.loginForm = this.fb.group({\n        email: ['', Validators.compose([Validators.required, Validators.email, Validators.minLength(3), Validators.maxLength(320) // https://stackoverflow.com/questions/386294/what-is-the-maximum-length-of-a-valid-email-address\n        ])],\n        password: ['', Validators.compose([Validators.required, Validators.minLength(3), Validators.maxLength(100)])],\n        remember: [false]\n      });\n      let rememberMeLocalStorage = this.appService.getLocalStorageItem('permitRemember', false);\n      let userLocallyStoredData = this.appService.getLocalStorageItem('permitUserAuth', true);\n      //condition for patching  remember me based on boolean\n      if (rememberMeLocalStorage === 'true' || rememberMeLocalStorage === true) {\n        this.loginForm.patchValue({\n          email: userLocallyStoredData.userName,\n          password: userLocallyStoredData.password,\n          remember: true\n        });\n      }\n    }\n    submit() {\n      this.hasError = false;\n      const controlsOfForm = this.loginForm.controls;\n      if (this.loginForm.invalid) {\n        return;\n      }\n      const authData = {\n        userName: controlsOfForm.email.value,\n        password: controlsOfForm.password.value\n      };\n      // Show global loader\n      this.httpUtilService.loadingSubject.next(true);\n      // //API call for login\n      this.authService.login(authData).subscribe(data => {\n        if (data.isFault === false) {\n          this.userData = data.responseData;\n          console.log(\"data.responseData\", data.responseData);\n          if (this.loginForm.value.remember === true) {\n            this.appService.setLocalStorageItem('permitRemember', 'true', false);\n          } else {\n            this.appService.setLocalStorageItem('permitRemember', 'false', false);\n          }\n          this.appService.setLocalStorageItem('permitUserAuth', authData, true);\n          // set the local storage for the user details\n          this.appService.setLocalStorageItem(\"permitUser\", data.responseData, true);\n          this.appService.setLocalStorageItem(\"permitToken\", data.responseData.token, true);\n          // this.layoutUtilService.showSuccess(data.responseData.message, '');\n          const token = data.responseData.token;\n          const decodedToken = jwtDecode(token);\n          const expirationTime = decodedToken.exp;\n          console.log('decodedToken ', decodedToken);\n          console.log('expirationTime ', expirationTime);\n          this.authService.setToken(data.responseData.token, expirationTime);\n          if (data.responseData.isPasswordChanged === false) {\n            this.changePassword();\n          } else {\n            // Keep loader active during navigation and page reload\n            this.router.navigate(['/dashboard'], {\n              replaceUrl: true\n            }).then(() => {\n              location.reload();\n            });\n          }\n        } else {\n          // Hide loader on error\n          this.httpUtilService.loadingSubject.next(false);\n          this.customLayoutUtilsService.showSuccess(data.responseData.message, '');\n          // //alert(data.responseData.message);\n        }\n      }, error => {\n        // Hide loader on error\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Login error:', error);\n      });\n    }\n    //function for opening pop up for change password\n    changePassword() {\n      // define the NgbModels options.\n      const modalOption = {};\n      modalOption.backdrop = 'static';\n      modalOption.keyboard = false;\n      modalOption.size = 'md';\n      // open the NgbModel for Confirmation.\n      const modalRef = this.modalService.open(ChangePasswordComponent, modalOption);\n      modalRef.componentInstance.showClose = false;\n      //get response from edit user modal\n      modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n        if (receivedEntry == true) {\n          // this.router.navigate(['/dashboard'], {relativeTo: this.route});\n          this.router.navigate(['/dashboard'], {\n            replaceUrl: true\n          }).then(() => {\n            location.reload();\n          });\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe.forEach(sb => sb.unsubscribe());\n    }\n    //function for show hide password\n    showpassword(event) {\n      this.passwordshown = event;\n    }\n    static ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.HttpUtilsService), i0.ɵɵdirectiveInject(i6.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i7.NgbModal), i0.ɵɵdirectiveInject(i8.PageInfoService), i0.ɵɵdirectiveInject(i9.Title));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 38,\n      vars: 26,\n      consts: [[\"formError\", \"\"], [\"novalidate\", \"novalidate\", \"id\", \"kt_login_signin_form\", 1, \"form\", \"w-100\", 3, \"ngSubmit\", \"formGroup\"], [1, \"text-center\", \"mb-10\"], [\"src\", \"./assets/media/logos/cropped-Pacifica-Logo.png\", \"alt\", \"Pacifica Engineering Services\", 1, \"h-120px\", \"logo\", 2, \"width\", \"250px\"], [1, \"fv-row\", \"mb-10\"], [1, \"form-label\", \"fw-bold\"], [1, \"text-danger\"], [\"type\", \"email\", \"name\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"off\", 1, \"form-control\", \"form-control-sm\", \"form-control-solid\", 3, \"ngClass\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"fv-row\", \"mb-4\"], [1, \"d-flex\", \"justify-content-between\", \"mt-n5\"], [1, \"d-flex\", \"flex-stack\", \"mb-2\"], [1, \"form-label\", \"fw-bold\", \"mb-0\"], [1, \"mb-0\"], [\"name\", \"password\", \"autocomplete\", \"off\", \"formControlName\", \"password\", 1, \"form-control\", \"form-control-sm\", \"form-control-solid\", 3, \"type\", \"ngClass\"], [1, \"toggle-password\"], [2, \"margin-top\", \"-29px\", 3, \"click\", \"ngClass\"], [1, \"d-flex\", \"flex-stack\", \"flex-wrap\", \"gap-3\", \"fs-base\", \"fw-semibold\", \"mb-8\"], [1, \"form-check\", \"form-check-sm\", \"form-check-custom\", \"me-5\"], [\"type\", \"checkbox\", \"formControlName\", \"remember\", 1, \"form-check-input\"], [1, \"form-check-label\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"link-primary\"], [1, \"text-center\"], [\"type\", \"submit\", \"id\", \"kt_sign_in_submit\", 1, \"btn\", \"btn-sm\", \"btn-primary\", \"mb-5\", 3, \"disabled\"], [1, \"indicator-label\"], [4, \"ngIf\"], [1, \"fv-plugins-message-container\"], [\"role\", \"alert\", 1, \"text-danger\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.submit());\n          });\n          i0.ɵɵelementStart(1, \"div\", 2);\n          i0.ɵɵelement(2, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 4)(4, \"label\", 5);\n          i0.ɵɵtext(5, \"Email\");\n          i0.ɵɵelementStart(6, \"sup\", 6);\n          i0.ɵɵtext(7, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(8, \"input\", 7);\n          i0.ɵɵelementContainer(9, 8)(10, 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"label\", 12);\n          i0.ɵɵtext(15, \"Password\");\n          i0.ɵɵelementStart(16, \"sup\", 6);\n          i0.ɵɵtext(17, \"*\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(18, \"div\", 13);\n          i0.ɵɵelement(19, \"input\", 14);\n          i0.ɵɵelementStart(20, \"div\", 15)(21, \"span\", 16);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_span_click_21_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.passwordshown === true ? ctx.showpassword(false) : ctx.showpassword(true));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementContainer(22, 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\")(25, \"label\", 18);\n          i0.ɵɵelement(26, \"input\", 19);\n          i0.ɵɵelementStart(27, \"span\", 20);\n          i0.ɵɵtext(28, \"Remember me ?\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"a\", 21);\n          i0.ɵɵtext(30, \" Forgot Password ? \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 22)(32, \"button\", 23);\n          i0.ɵɵpipe(33, \"async\");\n          i0.ɵɵelementStart(34, \"span\", 24);\n          i0.ɵɵtext(35, \"Login\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(36, LoginComponent_ng_template_36_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const formError_r5 = i0.ɵɵreference(37);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c0, ctx.loginForm.controls[\"email\"].invalid, ctx.loginForm.controls[\"email\"].valid));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", formError_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(17, _c1, ctx.loginForm.controls[\"email\"]));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", formError_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(19, _c2, ctx.loginForm.controls[\"email\"]));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"type\", ctx.passwordshown === true ? \"password\" : \"text\")(\"ngClass\", i0.ɵɵpureFunction2(21, _c0, ctx.loginForm.controls[\"password\"].invalid, ctx.loginForm.controls[\"password\"].valid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.passwordshown === true ? \"bi bi-eye-slash-fill\" : \"bi bi-eye-fill\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", formError_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(24, _c3, ctx.loginForm.controls[\"password\"]));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || i0.ɵɵpipeBind1(33, 12, ctx.isLoading$));\n        }\n      },\n      dependencies: [i10.NgClass, i10.NgIf, i10.NgTemplateOutlet, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i10.AsyncPipe],\n      styles: [\"[_nghost-%COMP%]{width:100%}@media (min-width: 992px){[_nghost-%COMP%]   .login-form[_ngcontent-%COMP%]{width:100%;max-width:450px}[_nghost-%COMP%]   .login-form[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%}}.toggle-password[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{float:right;cursor:pointer;margin-right:43px;margin-top:-33px;font-size:16px}\"]\n    });\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}