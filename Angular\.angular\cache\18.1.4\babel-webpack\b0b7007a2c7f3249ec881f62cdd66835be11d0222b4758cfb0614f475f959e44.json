{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport _, { each } from 'lodash';\nimport { map, Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { AppSettings } from 'src/app/app.settings';\nimport { ConfirmationDialogComponent } from '../../shared/confirmation-dialog/confirmation-dialog.component';\nimport { RoleEditComponent } from '../role-edit/role-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/exceljs.service\";\nimport * as i3 from \"../../services/http-utils.service\";\nimport * as i4 from \"../../services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/user.service\";\nimport * as i7 from \"../../services/role.service\";\nimport * as i8 from \"../../services/kendo-column.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@progress/kendo-angular-grid\";\nimport * as i12 from \"@progress/kendo-angular-inputs\";\nimport * as i13 from \"@progress/kendo-angular-buttons\";\nimport * as i14 from \"ng-inline-svg-2\";\nimport * as i15 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c7 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction RoleListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 11);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RoleListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"kendo-textbox\", 13);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function RoleListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function RoleListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"span\", 15);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.create());\n    });\n    i0.ɵɵelement(9, \"span\", 18);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_4_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(14, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_4_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(16, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n  }\n}\nfunction RoleListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 31)(7, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_5_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(8, \"i\", 33);\n    i0.ɵɵtext(9, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_5_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(11, \"i\", 35);\n    i0.ɵɵtext(12, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n  }\n}\nfunction RoleListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoleListComponent_ng_template_5_div_0_Template, 13, 2, \"div\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.roleId));\n    });\n    i0.ɵɵelement(1, \"span\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen055.svg\");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 41);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template, 2, 1, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(10, _c6));\n    i0.ɵɵproperty(\"width\", 90)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(11, _c7))(\"includeInChooser\", false)(\"columnMenu\", false)(\"sortable\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r7.roleName, \" \");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 48);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r8 = ctx.$implicit;\n    const column_r9 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r9)(\"filter\", filter_r8)(\"extra\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 45);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template, 3, 1, \"ng-template\", 42)(2, RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template, 6, 3, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 200)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"roleName\"))(\"headerStyle\", i0.ɵɵpureFunction0(8, _c7))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"roleName\"))(\"filterable\", true)(\"sortable\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 50);\n  }\n  if (rf & 2) {\n    const dataItem_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.formatPermission(dataItem_r10.rolePermissions), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 48);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r11 = ctx.$implicit;\n    const column_r12 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r12)(\"filter\", filter_r11)(\"extra\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 49);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template, 1, 1, \"ng-template\", 42)(2, RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template, 6, 3, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 400)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"rolePermissions\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"rolePermissions\"))(\"filterable\", true)(\"sortable\", false);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r13.status, \" \");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen040.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r13.status, \" \");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_0_Template, 2, 2, \"span\", 52)(1, RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_1_Template, 2, 2, \"span\", 53);\n  }\n  if (rf & 2) {\n    const dataItem_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", dataItem_r13.status === \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r13.status === \"Inactive\");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 48);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r14 = ctx.$implicit;\n    const column_r15 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r15)(\"filter\", filter_r14)(\"extra\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 51);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template, 2, 2, \"ng-template\", 42)(2, RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template, 6, 3, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 100)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"status\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"status\"))(\"filterable\", true)(\"sortable\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(3, 3, dataItem_r16.lastUpdatedDate, \"shortDate\"), \" \", i0.ɵɵpipeBind2(4, 6, dataItem_r16.lastUpdatedDate, \"shortTime\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(dataItem_r16.lastUpdatedByFullName);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 48);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r17 = ctx.$implicit;\n    const column_r18 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r18)(\"filter\", filter_r17)(\"extra\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 56);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template, 8, 9, \"ng-template\", 42)(2, RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template, 5, 3, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true)(\"sortable\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_1_Template, 2, 12, \"kendo-grid-column\", 36)(2, RoleListComponent_ng_container_6_kendo_grid_column_2_Template, 3, 9, \"kendo-grid-column\", 37)(3, RoleListComponent_ng_container_6_kendo_grid_column_3_Template, 3, 7, \"kendo-grid-column\", 38)(4, RoleListComponent_ng_container_6_kendo_grid_column_4_Template, 3, 7, \"kendo-grid-column\", 39)(5, RoleListComponent_ng_container_6_kendo_grid_column_5_Template, 3, 7, \"kendo-grid-column\", 40);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r19 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"roleName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"rolePermissions\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"status\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"lastUpdatedDate\");\n  }\n}\nfunction RoleListComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵelementStart(3, \"p\", 15);\n    i0.ɵɵtext(4, \"No roles found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_7_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 63);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RoleListComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoleListComponent_ng_template_7_div_0_Template, 8, 0, \"div\", 58);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading === false && ctx_r2.serverSideRowData.length === 0);\n  }\n}\nexport let RoleListComponent = /*#__PURE__*/(() => {\n  class RoleListComponent {\n    cdr;\n    modalService;\n    exceljsService;\n    httpUtilService;\n    AppService;\n    layoutUtilService;\n    UserService;\n    roleService;\n    kendoColumnService;\n    grid;\n    // Data\n    serverSideRowData = [];\n    gridData = [];\n    IsListHasValue = false;\n    loading = false;\n    isLoading = false;\n    loginUser = {};\n    // Search\n    searchData = '';\n    searchTerms = new Subject();\n    searchSubscription;\n    // Enhanced Filters for Kendo UI\n    filter = {\n      logic: 'and',\n      filters: []\n    };\n    gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    activeFilters = [];\n    filterOptions = [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }];\n    // Advanced filter options\n    advancedFilterOptions = {\n      status: [{\n        text: 'All',\n        value: null\n      }, {\n        text: 'Active',\n        value: 'Active'\n      }, {\n        text: 'Inactive',\n        value: 'Inactive'\n      }]\n    };\n    // Filter state\n    showAdvancedFilters = false;\n    appliedFilters = {\n      status: null\n    };\n    // Kendo Grid properties\n    page = {\n      size: 10,\n      pageNumber: 0,\n      totalElements: 0,\n      totalPages: 0,\n      orderBy: 'lastUpdatedDate',\n      orderDir: 'desc'\n    };\n    skip = 0;\n    sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    // Column visibility system properties\n    kendoHide;\n    hiddenData = [];\n    kendoColOrder = [];\n    kendoInitColOrder = [];\n    hiddenFields = [];\n    // Column configuration for the new system\n    gridColumns = [];\n    defaultColumns = [];\n    fixedColumns = [];\n    draggableColumns = [];\n    normalGrid;\n    expandedGrid;\n    isExpanded = false;\n    // Enhanced Columns with Kendo UI features\n    gridColumnConfig = [{\n      field: 'action',\n      title: 'Action',\n      width: 80,\n      isFixed: true,\n      type: 'action',\n      order: 1\n    }, {\n      field: 'roleName',\n      title: 'Name',\n      width: 200,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2\n    }, {\n      field: 'rolePermissions',\n      title: 'Permissions',\n      width: 300,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 3\n    }, {\n      field: 'status',\n      title: 'Status',\n      width: 120,\n      type: 'status',\n      isFixed: false,\n      filterable: true,\n      order: 4\n    }, {\n      field: 'lastUpdatedDate',\n      title: 'Updated Date',\n      width: 180,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 5\n    }];\n    // OLD SYSTEM - to be removed\n    columnData = [{\n      name: 'Name',\n      prop: 'roleName',\n      order: 'desc',\n      width: '17%',\n      sort: true\n    }, {\n      name: 'Permissions',\n      prop: 'rolePermissions',\n      order: 'desc',\n      width: '40%',\n      sort: true\n    }, {\n      name: 'Status',\n      prop: 'status',\n      order: 'desc',\n      width: '11%',\n      sort: true\n    }, {\n      name: 'Updated',\n      prop: 'lastUpdatedDate',\n      order: 'desc',\n      width: '18%',\n      sort: true\n    }, {\n      name: 'Action',\n      prop: 'Action',\n      order: 'desc',\n      width: '12%',\n      sort: false\n    }];\n    // Router subscription for saving state on navigation\n    routerSubscription;\n    // Storage key for state persistence\n    GRID_STATE_KEY = 'roles-grid-state';\n    // Export options\n    exportOptions = [{\n      text: 'Export All',\n      value: 'all'\n    }, {\n      text: 'Export Selected',\n      value: 'selected'\n    }, {\n      text: 'Export Filtered',\n      value: 'filtered'\n    }];\n    // Selection state\n    selectedRoles = [];\n    isAllSelected = false;\n    // Statistics\n    roleStatistics = {\n      activeRoles: 0,\n      inactiveRoles: 0,\n      totalRoles: 0\n    };\n    // Bulk operations\n    showBulkActions = false;\n    bulkActionStatus = 'Active';\n    // Legacy properties (keeping for backward compatibility)\n    pageSize = AppSettings.PAGE_SIZE;\n    pageSizeOptions = AppSettings.PAGE_SIZE_OPTIONS;\n    itemsPerPage = new FormControl(this.pageSize);\n    defaultOrder = 'desc';\n    defaultOrderBy = 'lastUpdatedDate';\n    defaultRoles = [];\n    statusData = false;\n    permissionArray = [];\n    selectedTab = 'All';\n    innerWidth;\n    displayMobile = false;\n    constructor(cdr, modalService, exceljsService, httpUtilService, AppService, layoutUtilService, UserService, roleService, kendoColumnService) {\n      this.cdr = cdr;\n      this.modalService = modalService;\n      this.exceljsService = exceljsService;\n      this.httpUtilService = httpUtilService;\n      this.AppService = AppService;\n      this.layoutUtilService = layoutUtilService;\n      this.UserService = UserService;\n      this.roleService = roleService;\n      this.kendoColumnService = kendoColumnService;\n      // set the default paging options\n      this.page.pageNumber = 0;\n      this.page.size = this.pageSize;\n      this.page.orderBy = 'LastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n    ngOnInit() {\n      this.loginUser = this.AppService.getLoggedInUser();\n      console.log('Login user loaded:', this.loginUser);\n      this.innerWidth = window.innerWidth;\n      if (this.innerWidth >= 320 && this.innerWidth < 768) {\n        this.displayMobile = true;\n      } else {\n        this.displayMobile = false;\n      }\n      // Setup search with debounce\n      this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(searchTerm => {\n        console.log('Search triggered with term:', searchTerm);\n        this.page.pageNumber = 0;\n        this.skip = 0;\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        this.loadTable();\n      });\n      // Load roles for advanced filters\n      this.loadRoles();\n      // Initialize with default page load\n      this.onPageLoad();\n      // Initialize new column visibility system\n      this.initializeColumnVisibilitySystem();\n      // Load column configuration after a short delay to ensure loginUser is available\n      setTimeout(() => {\n        this.loadColumnConfigFromDatabase();\n      }, 100);\n      localStorage.removeItem('keyword');\n    }\n    /**\n     * Initialize the new column visibility system\n     */\n    initializeColumnVisibilitySystem() {\n      // Initialize default columns\n      this.defaultColumns = this.gridColumnConfig.map(col => col.field);\n      this.gridColumns = [...this.defaultColumns];\n      // Set fixed columns (first 2 columns)\n      this.fixedColumns = ['action', 'roleName'];\n      // Set draggable columns (all except fixed)\n      this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\n      // Initialize normal and expanded grid references\n      this.normalGrid = this.grid;\n      this.expandedGrid = this.grid;\n    }\n    ngAfterViewInit() {\n      // Load the table after the view is initialized\n      // Small delay to ensure the grid is properly rendered\n      setTimeout(() => {\n        this.loadTable();\n      }, 200);\n    }\n    // Method to handle initial page load\n    onPageLoad() {\n      // Initialize the component with default data\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.searchData = '';\n      // Load the data\n      this.loadTable();\n    }\n    // Load roles for advanced filters\n    loadRoles() {\n      this.UserService.getDefaultPermissions({}).subscribe(permissions => {\n        this.permissionArray = permissions.responseData;\n      });\n    }\n    // Method to handle when the component becomes visible\n    onTabActivated() {\n      // Refresh the data when the tab is activated\n      this.loadTable();\n    }\n    ngOnDestroy() {\n      // Clean up subscriptions\n      if (this.routerSubscription) {\n        this.routerSubscription.unsubscribe();\n      }\n      if (this.searchSubscription) {\n        this.searchSubscription.unsubscribe();\n      }\n      this.searchTerms.complete();\n    }\n    // function to get roles data from API\n    loadTable() {\n      // Use the new Kendo UI specific endpoint\n      this.loadTableWithKendoEndpoint();\n    }\n    // New method to load data using Kendo UI specific endpoint\n    loadTableWithKendoEndpoint() {\n      this.loading = true;\n      this.isLoading = true;\n      // Enable loader\n      this.httpUtilService.loadingSubject.next(true);\n      // Prepare state object for Kendo UI endpoint\n      const state = {\n        take: this.page.size,\n        skip: this.skip,\n        sort: this.sort,\n        filter: this.filter,\n        search: this.searchData,\n        loggedInUserId: this.loginUser.userId,\n        orderBy: this.page.orderBy,\n        orderDir: this.page.orderDir\n      };\n      console.log('Loading roles table with search term:', this.searchData);\n      console.log('Full state object:', state);\n      this.roleService.getRolesForKendoGrid(state).subscribe({\n        next: data => {\n          // Handle the new API response structure\n          if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n            const errors = data.responseData?.errors || data.errors || [];\n            console.error('Kendo UI Grid errors:', errors);\n            this.handleEmptyResponse();\n          } else {\n            // Handle both old and new response structures\n            const responseData = data.responseData || data;\n            const roleData = responseData.data || [];\n            const total = responseData.total || 0;\n            this.IsListHasValue = roleData.length !== 0;\n            this.serverSideRowData = this.sortGridDataClientSide(roleData, this.sort);\n            this.gridData = this.serverSideRowData;\n            this.page.totalElements = total;\n            this.page.totalPages = Math.ceil(total / this.page.size);\n          }\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.markForCheck();\n        },\n        error: error => {\n          console.error('Error loading data with Kendo UI endpoint:', error);\n          this.handleEmptyResponse();\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    // Handle empty response\n    handleEmptyResponse() {\n      this.IsListHasValue = false;\n      this.serverSideRowData = [];\n      this.gridData = [];\n      this.page.totalElements = 0;\n      this.page.totalPages = 0;\n      this.loading = false;\n      this.isLoading = false;\n    }\n    // Kendo Grid event handlers\n    pageChange(event) {\n      this.skip = event.skip;\n      this.page.size = event.take;\n      this.page.pageNumber = event.skip / event.take;\n      // Set loading state for pagination\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTableWithKendoEndpoint();\n    }\n    onSortChange(sort) {\n      // Normalize field names to match backend mapping\n      const normalizeField = field => {\n        switch (field) {\n          case 'Name':\n            return 'roleName';\n          case 'Status':\n            return 'status';\n          case 'UpdatedDate':\n          case 'LastUpdatedDate':\n          case 'lastUpdatedByFullName':\n            return 'lastUpdatedDate';\n          default:\n            return field || 'lastUpdatedDate';\n        }\n      };\n      // Map the incoming sort to normalized version\n      const incomingSort = Array.isArray(sort) ? sort : [];\n      this.sort = incomingSort.length > 0 ? [{\n        field: normalizeField(incomingSort[0].field),\n        dir: incomingSort[0].dir\n      }] : [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Update page order fields for consistency\n      this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = this.sort[0].dir || 'desc';\n      // Reset to first page\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Set loading state for sorting\n      this.loading = true;\n      this.isLoading = true;\n      console.log('Sort change -> sending sort:', this.sort);\n      this.loadTableWithKendoEndpoint();\n    }\n    filterChange(event) {\n      this.filter = event.filter;\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Set loading state for filtering\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTableWithKendoEndpoint();\n    }\n    // Client-side fallback sorting to guarantee visible order\n    sortGridDataClientSide(data, sort) {\n      if (!Array.isArray(data) || !sort || sort.length === 0) return data;\n      const s = sort[0];\n      const field = s.field || 'lastUpdatedDate';\n      const dir = (s.dir || 'asc') === 'asc' ? 1 : -1;\n      const parseUsDate = val => {\n        // Supports formats like 8/21/25 1:58 PM\n        const m = val && val.match(/^(\\d{1,2})\\/(\\d{1,2})\\/(\\d{2,4})\\s+(\\d{1,2}):(\\d{2})\\s*(AM|PM)$/i);\n        if (!m) return NaN;\n        let month = parseInt(m[1], 10) - 1;\n        const day = parseInt(m[2], 10);\n        let year = parseInt(m[3], 10);\n        if (year < 100) year += 2000;\n        let hour = parseInt(m[4], 10);\n        const minute = parseInt(m[5], 10);\n        const ampm = m[6].toUpperCase();\n        if (ampm === 'PM' && hour < 12) hour += 12;\n        if (ampm === 'AM' && hour === 12) hour = 0;\n        return new Date(year, month, day, hour, minute).getTime();\n      };\n      const getVal = item => {\n        const v = item[field];\n        if (field === 'lastUpdatedDate') {\n          if (v instanceof Date) return v.getTime();\n          const ts = Date.parse(v);\n          if (!isNaN(ts)) return ts;\n          const us = parseUsDate(String(v));\n          return isNaN(us) ? 0 : us;\n        }\n        if (typeof v === 'string') return v.toLowerCase();\n        return v;\n      };\n      try {\n        const sorted = [...data].sort((a, b) => {\n          const av = getVal(a);\n          const bv = getVal(b);\n          if (av == null && bv == null) return 0;\n          if (av == null) return -1 * dir;\n          if (bv == null) return 1 * dir;\n          if (av > bv) return 1 * dir;\n          if (av < bv) return -1 * dir;\n          return 0;\n        });\n        return sorted;\n      } catch {\n        return data;\n      }\n    }\n    onSelectionChange(event) {\n      this.selectedRoles = event.selectedRows || [];\n      this.isAllSelected = this.selectedRoles.length === this.serverSideRowData.length;\n    }\n    // Search methods\n    onSearchKeyDown(event) {\n      if (event.key === 'Enter') {\n        this.searchTerms.next(this.searchData);\n      }\n    }\n    // Handle search input changes\n    onSearchInput() {\n      // Trigger search on every input change with debouncing\n      console.log('Search input changed:', this.searchData);\n      this.searchTerms.next(this.searchData);\n    }\n    // Handle search model changes\n    onSearchChange() {\n      // Trigger search when model changes\n      console.log('Search model changed:', this.searchData);\n      this.searchTerms.next(this.searchData);\n    }\n    clearSearch() {\n      // Clear search data and trigger search\n      this.searchData = '';\n      // Set loading state for clear search\n      this.loading = true;\n      this.isLoading = true;\n      this.searchTerms.next('');\n    }\n    // Advanced filter methods\n    toggleAdvancedFilters() {\n      this.showAdvancedFilters = !this.showAdvancedFilters;\n    }\n    applyAdvancedFilters() {\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.loadTableWithKendoEndpoint();\n    }\n    clearAllFilters() {\n      this.appliedFilters = {\n        status: null\n      };\n      this.activeFilters = [];\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.loadTableWithKendoEndpoint();\n    }\n    // Grid expansion methods\n    toggleExpand() {\n      // Find grid container element and toggle fullscreen class\n      const gridContainer = document.querySelector('.grid-container');\n      if (gridContainer) {\n        gridContainer.classList.toggle('fullscreen-grid');\n        this.isExpanded = !this.isExpanded;\n        // Refresh grid after resize to ensure proper rendering\n        if (this.grid) {\n          this.grid.refresh();\n        }\n      }\n    }\n    // Export methods\n    onExportClick(event) {\n      switch (event.item.value) {\n        case 'all':\n          this.exportall();\n          break;\n        case 'selected':\n          if (this.selectedRoles.length > 0) {\n            this.exportRowData(this.selectedRoles);\n          } else {\n            this.layoutUtilService.showError('Please select roles to export', '');\n          }\n          break;\n        case 'filtered':\n          this.exportRowData(this.serverSideRowData);\n          break;\n        default:\n          this.exportall();\n      }\n    }\n    // Column visibility methods\n    /**\n     * Saves the current state of column visibility and order in the grid.\n     */\n    saveHead() {\n      // Check if loginUser is available\n      if (!this.loginUser || !this.loginUser.userId) {\n        console.error('loginUser not available:', this.loginUser);\n        this.layoutUtilService.showError('User not logged in. Please refresh the page.', '');\n        return;\n      }\n      const nonHiddenColumns = [];\n      const hiddenColumns = [];\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          if (!column.hidden) {\n            const columnData = {\n              title: column.title,\n              field: column.field,\n              hidden: column.hidden\n            };\n            nonHiddenColumns.push(columnData);\n          } else {\n            const columnData = {\n              title: column.title,\n              field: column.field,\n              hidden: column.hidden\n            };\n            hiddenColumns.push(columnData);\n          }\n        });\n      }\n      const draggableColumnsOrder = this.gridColumns.filter(col => !this.fixedColumns.includes(col)).map((field, index) => ({\n        field,\n        orderIndex: index\n      }));\n      // Prepare data for backend\n      const userData = {\n        pageName: 'Roles',\n        userID: this.loginUser.userId,\n        hiddenData: hiddenColumns,\n        kendoColOrder: draggableColumnsOrder,\n        LoggedId: this.loginUser.userId\n      };\n      // Show loading state\n      this.httpUtilService.loadingSubject.next(true);\n      // Save to backend\n      this.kendoColumnService.createHideFields(userData).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!res.isFault) {\n            // Update local state\n            this.hiddenData = hiddenColumns;\n            this.kendoColOrder = draggableColumnsOrder;\n            this.hiddenFields = this.hiddenData.map(col => col.field);\n            // Also save to localStorage as backup\n            this.kendoColumnService.saveToLocalStorage(userData);\n            this.layoutUtilService.showSuccess(res.message || 'Column settings saved successfully.', '');\n          } else {\n            this.layoutUtilService.showError(res.message || 'Failed to save column settings.', '');\n          }\n          this.cdr.markForCheck();\n        },\n        error: error => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('Error saving column settings:', error);\n          // Fallback to localStorage on error\n          this.kendoColumnService.saveToLocalStorage(userData);\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          this.layoutUtilService.showError('Failed to save to server. Settings saved locally.', '');\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    /**\n     * Reset the current state of column visibility and order in the grid to its original state.\n     */\n    resetTable() {\n      // Check if loginUser is available\n      if (!this.loginUser || !this.loginUser.userId) {\n        console.error('loginUser not available:', this.loginUser);\n        this.layoutUtilService.showError('User not logged in. Please refresh the page and try again.', '');\n        return;\n      }\n      // Double-check authentication token\n      const token = this.AppService.getLocalStorageItem('permitToken', true);\n      if (!token) {\n        console.error('Authentication token not found');\n        this.layoutUtilService.showError('Authentication token not found. Please login again.', '');\n        return;\n      }\n      // Reset all state variables\n      this.searchData = '';\n      this.activeFilters = [];\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.skip = 0;\n      this.page.pageNumber = 0;\n      this.gridColumns = [...this.defaultColumns];\n      // Reset sort state to default\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n      // Reset advanced filters\n      this.appliedFilters = {\n        status: null\n      };\n      // Reset advanced filters visibility\n      this.showAdvancedFilters = false;\n      // Reset column order index\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          const index = this.gridColumns.indexOf(column.field);\n          if (index !== -1) {\n            column.orderIndex = index;\n          }\n          // Reset column visibility - show all columns\n          if (column.field && column.field !== 'action') {\n            column.hidden = false;\n          }\n        });\n      }\n      // Clear hidden columns\n      this.hiddenData = [];\n      this.kendoColOrder = [];\n      this.hiddenFields = [];\n      // Reset the Kendo Grid's internal state\n      if (this.grid) {\n        // Clear all filters\n        this.grid.filter = {\n          logic: 'and',\n          filters: []\n        };\n        // Reset sorting\n        this.grid.sort = [{\n          field: 'lastUpdatedDate',\n          dir: 'desc'\n        }];\n        // Reset to first page\n        this.grid.skip = 0;\n        this.grid.pageSize = this.page.size;\n      }\n      // Prepare reset data\n      const userData = {\n        pageName: 'Roles',\n        userID: this.loginUser.userId,\n        hiddenData: [],\n        kendoColOrder: [],\n        LoggedId: this.loginUser.userId\n      };\n      // Only clear local settings; do not call server\n      this.kendoColumnService.clearFromLocalStorage('Roles');\n      // Show loader and refresh grid\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.cdr.detectChanges();\n      // Force grid refresh to apply all changes\n      if (this.grid) {\n        setTimeout(() => {\n          this.grid.refresh();\n          this.grid.reset();\n        }, 100);\n      }\n      this.loadTable();\n    }\n    /**\n     * Refresh grid data\n     */\n    refreshGrid() {\n      // Set loading state to show full-screen loader\n      this.loading = true;\n      this.isLoading = true;\n      // Reset to first page and clear any applied filters\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.gridFilter = {\n        logic: 'and',\n        filters: []\n      };\n      this.activeFilters = [];\n      this.appliedFilters = {\n        status: null\n      };\n      // Clear search data\n      this.searchData = '';\n      // Load fresh data from API\n      this.loadTable();\n    }\n    /**\n     * Loads and applies the saved column order from the user preferences or configuration.\n     */\n    loadSavedColumnOrder(kendoColOrder) {\n      try {\n        const savedOrder = kendoColOrder;\n        if (savedOrder) {\n          const parsedOrder = savedOrder;\n          if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n            // Get only the draggable columns from saved order\n            const savedDraggableColumns = parsedOrder.sort((a, b) => a.orderIndex - b.orderIndex).map(col => col.field).filter(field => !this.fixedColumns.includes(field));\n            // Add any missing draggable columns at the end\n            const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\n            // Combine fixed columns with saved draggable columns\n            this.gridColumns = [...this.fixedColumns, ...savedDraggableColumns, ...missingColumns];\n          } else {\n            this.gridColumns = [...this.defaultColumns];\n          }\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } catch (error) {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    }\n    /**\n     * Checks if a given column is marked as hidden.\n     */\n    getHiddenField(columnName) {\n      return this.hiddenFields.indexOf(columnName) > -1;\n    }\n    /**\n     * Handles the column reordering event triggered when a column is moved by the user.\n     */\n    onColumnReorder(event) {\n      const {\n        columns,\n        newIndex,\n        oldIndex\n      } = event;\n      // Prevent reordering of fixed columns\n      if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\n        return;\n      }\n      // Update the gridColumns array\n      const reorderedColumns = [...this.gridColumns];\n      const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n      reorderedColumns.splice(newIndex, 0, movedColumn);\n      this.gridColumns = reorderedColumns;\n      this.cdr.markForCheck();\n    }\n    /**\n     * Handles column visibility changes from the Kendo Grid.\n     */\n    updateColumnVisibility(event) {\n      const {\n        column,\n        hidden\n      } = event;\n      // Update hiddenData array\n      const existingIndex = this.hiddenData.findIndex(item => item.field === column.field);\n      if (hidden && existingIndex === -1) {\n        // Add to hidden columns\n        this.hiddenData.push({\n          title: column.title,\n          field: column.field,\n          hidden: true\n        });\n      } else if (!hidden && existingIndex !== -1) {\n        // Remove from hidden columns\n        this.hiddenData.splice(existingIndex, 1);\n      }\n      // Update hiddenFields array\n      this.hiddenFields = this.hiddenData.map(col => col.field);\n      this.cdr.markForCheck();\n    }\n    /**\n     * Loads the saved column configuration from the backend or localStorage as fallback.\n     */\n    loadColumnConfigFromDatabase() {\n      try {\n        // First try to load from backend\n        if (this.loginUser && this.loginUser.userId) {\n          this.kendoColumnService.getHideFields({\n            pageName: 'Roles',\n            userID: this.loginUser.userId\n          }).subscribe({\n            next: res => {\n              if (!res.isFault && res.Data) {\n                this.kendoHide = res.Data;\n                this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\n                this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\n                this.hiddenFields = this.hiddenData.map(col => col.field);\n                // Update grid columns based on the hidden fields\n                if (this.grid && this.grid.columns) {\n                  this.grid.columns.forEach(column => {\n                    if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                      column.includeInChooser = true;\n                      column.hidden = true;\n                    } else {\n                      column.hidden = false;\n                    }\n                  });\n                }\n                // Load saved column order and update grid\n                this.loadSavedColumnOrder(this.kendoInitColOrder);\n                // Also save to localStorage as backup\n                this.kendoColumnService.saveToLocalStorage({\n                  pageName: 'Roles',\n                  userID: this.loginUser.userId,\n                  hiddenData: this.hiddenData,\n                  kendoColOrder: this.kendoInitColOrder\n                });\n              }\n            },\n            error: error => {\n              console.error('Error loading from backend, falling back to localStorage:', error);\n              this.loadFromLocalStorageFallback();\n            }\n          });\n        } else {\n          // Fallback to localStorage if no user ID\n          this.loadFromLocalStorageFallback();\n        }\n      } catch (error) {\n        console.error('Error loading column configuration:', error);\n        this.loadFromLocalStorageFallback();\n      }\n    }\n    /**\n     * Fallback method to load column configuration from localStorage\n     */\n    loadFromLocalStorageFallback() {\n      try {\n        const savedConfig = this.kendoColumnService.getFromLocalStorage('Roles', this.loginUser?.UserId || 0);\n        if (savedConfig) {\n          this.kendoHide = savedConfig;\n          this.hiddenData = savedConfig.hiddenData || [];\n          this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          // Update grid columns based on the hidden fields\n          if (this.grid && this.grid.columns) {\n            this.grid.columns.forEach(column => {\n              if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                column.includeInChooser = true;\n                column.hidden = true;\n              } else {\n                column.hidden = false;\n              }\n            });\n          }\n          // Load saved column order and update grid\n          this.loadSavedColumnOrder(this.kendoInitColOrder);\n        }\n      } catch (error) {\n        console.error('Error loading from localStorage fallback:', error);\n      }\n    }\n    //function to form permission from API\n    formatPermission(pemissions) {\n      let permArray = JSON.parse(pemissions);\n      let permData = '';\n      each(permArray, (r, i) => {\n        _.forEach(r, function (value, key) {\n          let rArray = [];\n          value.indexOf('Read') === -1 ? '' : rArray.push('Read');\n          value.indexOf('Write') === -1 ? '' : rArray.push('Write');\n          value.indexOf('Delete') === -1 ? '' : rArray.push('Delete');\n          let nIndex = value.length > 0 ? rArray.join(', ') : 'None';\n          permData += \"<span class='badge badge-light-primary me-2 mt-1'>\" + key + \" : \" + nIndex + \"</span>\";\n        });\n      });\n      return permData;\n    }\n    // function to search the data when there is a typing in the search box\n    search(event) {\n      this.page.pageNumber = 0;\n      this.loadTable();\n    }\n    //function to filter data from search\n    filterConfiguration() {\n      let filter = {};\n      let searchText;\n      if (this.searchData === null) {\n        searchText = ' ';\n      } else {\n        searchText = this.searchData;\n      }\n      filter.paginate = true;\n      filter.Category = this.selectedTab;\n      filter.search = searchText.trim();\n      return filter;\n    }\n    // function to get the roles data based on the page selection in Pagination\n    serverSideSetPage(event) {\n      this.page.pageNumber = event - 1;\n      this.loadTable();\n    }\n    // function to get the roles data based on the sort selection\n    // Params : Orderby - field to be sorted, OrderDir - asc/desc\n    changeOrder(Orderby, OrderDir) {\n      this.defaultOrder = OrderDir === 'desc' ? 'asc' : 'desc';\n      let indexColumn = this.columnData.findIndex(cd => cd.prop === Orderby);\n      this.columnData[indexColumn].order = this.defaultOrder;\n      this.cdr.markForCheck();\n      this.defaultOrderBy = Orderby;\n      this.page.orderDir = this.defaultOrder;\n      this.page.orderBy = Orderby;\n      this.loadTable();\n    }\n    // function to get the roles based on the items per page selection\n    pageLimit(num) {\n      this.pageSize = Number(num);\n      this.page.pageNumber = 0;\n      if (this.serverSideRowData) this.page.size = Number(num);\n      this.loadTable();\n    }\n    // function to create a new roles\n    create() {\n      this.edit(0);\n    }\n    // function to edit a particular role\n    edit(id) {\n      var NgbModalOptions = {\n        size: 'lg',\n        backdrop: 'static',\n        keyboard: false,\n        scrollable: true\n      };\n      const modalRef = this.modalService.open(RoleEditComponent, NgbModalOptions);\n      modalRef.componentInstance.id = id;\n      modalRef.componentInstance.permissions = this.permissionArray;\n      //get response from edit user modal\n      modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n        if (receivedEntry == true) {\n          this.loadTable();\n        }\n      });\n    }\n    //function to export particular page data\n    exportRowData(rowdata) {\n      if (rowdata !== undefined) {\n        if (rowdata.length > 0) {\n          // declare the title and header data for excel\n          const tableTitle = 'Roles';\n          const headerArray = ['Name', 'Permissions', 'Status', 'Description', 'Created Date', 'Created Time', 'Created By User', 'Modified Date', 'Modified Time', 'Modified By User'];\n          // get the data for excel in a array format\n          const respResult = [];\n          each(rowdata, rowdata => {\n            const respData = [];\n            respData.push(rowdata.Name);\n            respData.push(this.formatexcelPermission(rowdata.Permissions));\n            respData.push(rowdata.Status);\n            respData.push(rowdata.Description);\n            respData.push(this.AppService.unixDate(rowdata.CreatedDate));\n            respData.push(this.AppService.unixTime(rowdata.CreatedDate));\n            respData.push(rowdata.CreatedUserFullName);\n            respData.push(this.AppService.unixDate(rowdata.LastUpdatedDate));\n            respData.push(this.AppService.unixTime(rowdata.LastUpdatedDate));\n            respData.push(rowdata.LastUpdatedUserFullName);\n            respResult.push(respData);\n          });\n          // assign the width for each column\n          const colSize = [{\n            id: 1,\n            width: 30\n          }, {\n            id: 2,\n            width: 50\n          }, {\n            id: 3,\n            width: 40\n          }, {\n            id: 4,\n            width: 30\n          }, {\n            id: 5,\n            width: 30\n          }, {\n            id: 6,\n            width: 30\n          }, {\n            id: 7,\n            width: 30\n          }, {\n            id: 8,\n            width: 30\n          }, {\n            id: 9,\n            width: 30\n          }, {\n            id: 10,\n            width: 30\n          }];\n          this.cdr.markForCheck();\n          this.exceljsService.generateExcel(tableTitle, headerArray, respResult, colSize);\n        } else {\n          this.layoutUtilService.showError('There is no available data to export', '');\n        }\n      } else {\n        this.layoutUtilService.showError('There is no available data to export', '');\n      }\n    }\n    // function to get all the users data from api call and then export those in a excel file\n    exportall() {\n      const queryparams = {\n        paginate: false\n      };\n      this.httpUtilService.loadingSubject.next(true);\n      this.UserService.getAllRolesWithUserInfo(queryparams).pipe(map(data => data)).subscribe(data => {\n        if (!data.isFault) {\n          this.exportRowData(data.responseData);\n          this.cdr.markForCheck();\n          this.httpUtilService.loadingSubject.next(false);\n        } else {\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      });\n    }\n    // function to display the popup to get confirmation to delete roles\n    deleteRoles(role) {\n      var NgbModalOptions = {\n        size: 'md',\n        backdrop: 'static',\n        keyboard: false,\n        scrollable: true\n      };\n      const modalRef = this.modalService.open(ConfirmationDialogComponent, NgbModalOptions);\n      modalRef.componentInstance.id = role.roleId;\n      modalRef.componentInstance.showClose = true;\n      modalRef.componentInstance.description = \"Are you sure to delete this role?\";\n      modalRef.componentInstance.actionButtonText = \"Yes\";\n      modalRef.componentInstance.cancelButtonText = \"Cancel\";\n      modalRef.componentInstance.title = \"Delete Role - \" + role.roleName;\n      modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n        console.log('receivedEntry ', receivedEntry);\n        if (receivedEntry.success == true) {\n          let reStart = {\n            roleId: role.roleId,\n            loggedInUserId: this.loginUser.userId\n          };\n          this.httpUtilService.loadingSubject.next(true);\n          this.UserService.deleteRole(reStart).subscribe(data => {\n            if (!data.isFault) {\n              this.httpUtilService.loadingSubject.next(false);\n              this.layoutUtilService.showSuccess(data.responseData.message, '');\n              this.ngOnInit();\n            }\n          });\n        }\n      });\n    }\n    //function to format permission for export excel\n    formatexcelPermission(pemissions) {\n      let permArray = JSON.parse(pemissions);\n      let permData = '';\n      each(permArray, (r, i) => {\n        _.forEach(r, function (value, key) {\n          let rArray = [];\n          value.indexOf('Read') === -1 ? '' : rArray.push('Read');\n          value.indexOf('Write') === -1 ? '' : rArray.push('Write');\n          value.indexOf('Delete') === -1 ? '' : rArray.push('Delete');\n          let nIndex = value.length > 0 ? rArray.join(', ') : 'None';\n          permData += key + \" : \" + nIndex + '\\n';\n        });\n      });\n      return permData;\n    }\n    static ɵfac = function RoleListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoleListComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.ExceljsService), i0.ɵɵdirectiveInject(i3.HttpUtilsService), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.RoleService), i0.ɵɵdirectiveInject(i8.KendoColumnService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RoleListComponent,\n      selectors: [[\"app-role-list\"]],\n      viewQuery: function RoleListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      decls: 8,\n      vars: 22,\n      consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\", \"loading\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"sortable\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"roleName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", \"sortable\", 4, \"ngIf\"], [\"field\", \"rolePermissions\", \"title\", \"Permissions\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", \"sortable\", 4, \"ngIf\"], [\"field\", \"status\", \"title\", \"Status\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", \"sortable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", \"sortable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"sortable\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [\"title\", \"Edit\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-primary\", 3, \"inlineSVG\"], [\"field\", \"roleName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", \"sortable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bolder\", \"cursor-pointer\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"rolePermissions\", \"title\", \"Permissions\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", \"sortable\"], [3, \"innerHTML\"], [\"field\", \"status\", \"title\", \"Status\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", \"sortable\"], [\"ngbTooltip\", \"Active\", \"class\", \"svg-icon svg-icon-3 svg-icon-success\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Inactive\", \"class\", \"svg-icon svg-icon-3 svg-icon-danger text-danger\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Active\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-success\", 3, \"inlineSVG\"], [\"ngbTooltip\", \"Inactive\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", \"text-danger\", 3, \"inlineSVG\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", \"sortable\"], [1, \"fw-bolder\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"fas\", \"fa-shield-alt\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n      template: function RoleListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, RoleListComponent_div_0_Template, 7, 0, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"kendo-grid\", 3, 0);\n          i0.ɵɵlistener(\"columnReorder\", function RoleListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          })(\"selectionChange\", function RoleListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChange($event));\n          })(\"filterChange\", function RoleListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterChange($event));\n          })(\"pageChange\", function RoleListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChange($event));\n          })(\"sortChange\", function RoleListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSortChange($event));\n          })(\"columnVisibilityChange\", function RoleListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n          });\n          i0.ɵɵtemplate(4, RoleListComponent_ng_template_4_Template, 17, 10, \"ng-template\", 4)(5, RoleListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 4)(6, RoleListComponent_ng_container_6_Template, 6, 5, \"ng-container\", 5)(7, RoleListComponent_ng_template_7_Template, 1, 1, \"ng-template\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.serverSideRowData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(17, _c2, i0.ɵɵpureFunction0(16, _c1)))(\"sortable\", i0.ɵɵpureFunction0(19, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(20, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(21, _c5))(\"loading\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n        }\n      },\n      dependencies: [i9.NgForOf, i9.NgIf, i10.NgControlStatus, i10.NgModel, i1.NgbTooltip, i11.GridComponent, i11.ToolbarTemplateDirective, i11.GridSpacerComponent, i11.ColumnComponent, i11.CellTemplateDirective, i11.NoRecordsTemplateDirective, i11.ContainsFilterOperatorComponent, i11.EndsWithFilterOperatorComponent, i11.EqualFilterOperatorComponent, i11.NotEqualFilterOperatorComponent, i11.StartsWithFilterOperatorComponent, i11.GreaterOrEqualToFilterOperatorComponent, i11.LessOrEqualToFilterOperatorComponent, i11.StringFilterMenuComponent, i11.FilterMenuTemplateDirective, i11.DateFilterMenuComponent, i12.TextBoxComponent, i13.ButtonComponent, i14.InlineSVGDirective, i15.DropDownListComponent, i9.DatePipe],\n      styles: [\".grid-container[_ngcontent-%COMP%]{padding:20px;display:flex;flex-direction:column;height:100%;position:relative}.fullscreen-grid[_ngcontent-%COMP%]{position:fixed;inset:0;z-index:9999;background:#fff;padding:20px;overflow:auto}.grid-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:500}.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{width:300px}.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{border-radius:.375rem;padding:.5rem .75rem;width:80%;border:2px solid #646367;box-shadow:0 0 6px #393a3a80}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{border-radius:.375rem;padding:.75rem 1.25rem;min-width:120px;background-color:#4c4e4f;color:#fff;font-weight:500;transition:background .3s,transform .2s}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{background-color:#4c4e4f;transform:scale(1.05)}.grid-toolbar[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:center;margin-bottom:10px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;gap:5px;font-size:.875rem;font-weight:500;padding:.5rem;border-radius:.375rem;transition:all .15s ease-in-out;min-width:40px;height:40px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:not(.btn-primary){padding:.5rem;min-width:40px;width:40px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]{background-color:#198754;border-color:#198754;color:#fff}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover{background-color:#157347;border-color:#146c43}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%]{background-color:#ffc107;border-color:#ffc107;color:#000}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%]:hover{background-color:#ffca2c;border-color:#ffc720}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%]{background-color:#0dcaf0;border-color:#0dcaf0;color:#000}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%]:hover{background-color:#31d2f2;border-color:#25cff2}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d;color:#fff}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover{background-color:#5c636a;border-color:#565e64}.total-count[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:.875rem}.total-count[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{color:#6c757d!important}.total-count[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%]{font-weight:600!important;color:#495057}.k-grid[_ngcontent-%COMP%]{border-radius:6px;overflow:hidden;box-shadow:0 2px 5px #0000001a}.no-data[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:40px 0;font-size:16px;color:#888;background-color:#f9f9f9;border-radius:6px;margin-top:20px}.detail-container[_ngcontent-%COMP%]{padding:15px;background-color:#f9f9f9;border-radius:4px}.detail-row[_ngcontent-%COMP%]{display:flex;margin-bottom:8px}.detail-row[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%]{width:120px;font-weight:500;color:#666}.status-active[_ngcontent-%COMP%]{padding:4px 8px;background-color:#e8f5e9;color:#2e7d32;border-radius:4px;font-size:12px;font-weight:500}.status-inactive[_ngcontent-%COMP%]{padding:4px 8px;background-color:#ffebee;color:#c62828;border-radius:4px;font-size:12px;font-weight:500}.advanced-filters-panel[_ngcontent-%COMP%]{border-top:1px solid #dee2e6;margin-top:10px}.custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d;color:#fff}.custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{background-color:#5c636a;border-color:#565e64}.badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem;margin:.125rem;border-radius:.25rem}.badge-light-primary[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1976d2}@media (max-width: 768px){.grid-container[_ngcontent-%COMP%]{padding:10px}.k-grid-toolbar[_ngcontent-%COMP%]{flex-wrap:wrap;gap:5px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-width:35px;height:35px;font-size:.75rem}}\"]\n    });\n  }\n  return RoleListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}