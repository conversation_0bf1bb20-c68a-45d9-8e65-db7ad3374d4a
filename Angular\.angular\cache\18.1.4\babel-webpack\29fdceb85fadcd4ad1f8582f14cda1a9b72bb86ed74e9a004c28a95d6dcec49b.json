{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { AppSettings } from 'src/app/app.settings';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/exceljs.service\";\nimport * as i3 from \"../../services/http-utils.service\";\nimport * as i4 from \"../../services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/activity-log.service\";\nimport * as i7 from \"../../services/kendo-column.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@progress/kendo-angular-grid\";\nimport * as i11 from \"@progress/kendo-angular-inputs\";\nimport * as i12 from \"@progress/kendo-angular-buttons\";\nimport * as i13 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nfunction ActivityLogListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"span\", 28);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActivityLogListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"kendo-textbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActivityLogListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function ActivityLogListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function ActivityLogListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 32)(4, \"span\", 33);\n    i0.ɵɵtext(5, \"Total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 34);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(9, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_ng_template_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(11, \"i\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements);\n  }\n}\nfunction ActivityLogListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"div\", 41)(3, \"label\", 42);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActivityLogListComponent_div_5_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 41)(7, \"label\", 42);\n    i0.ɵɵtext(8, \"User Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActivityLogListComponent_div_5_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.userType, $event) || (ctx_r2.appliedFilters.userType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 44)(11, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_div_5_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵtext(12, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_div_5_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearAdvancedFilters());\n    });\n    i0.ɵɵtext(14, \" Clear Filters \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status)(\"textField\", \"text\")(\"valueField\", \"value\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.userTypes)(\"textField\", \"text\")(\"valueField\", \"value\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.userType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"themeColor\", \"primary\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"themeColor\", \"secondary\");\n  }\n}\nfunction ActivityLogListComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_ng_template_27_Template_button_click_1_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewLog(dataItem_r6));\n    });\n    i0.ɵɵelement(2, \"i\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"themeColor\", \"primary\");\n  }\n}\nfunction ActivityLogListComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵelementStart(2, \"span\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r7 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.getEventIcon(dataItem_r7.activityEvent));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(dataItem_r7.activityEvent);\n  }\n}\nfunction ActivityLogListComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.truncateText(dataItem_r8.eventDescription, 40), \" \");\n  }\n}\nfunction ActivityLogListComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r9 = ctx.dataItem;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r9.tableName);\n  }\n}\nfunction ActivityLogListComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r10 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r10.activityStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r10.activityStatus, \" \");\n  }\n}\nfunction ActivityLogListComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.dataItem;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r11.activityUserType);\n  }\n}\nfunction ActivityLogListComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r12 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r12.createdDate), \" \");\n  }\n}\nfunction ActivityLogListComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r13 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r13.createdByUserFullName || \"System\", \" \");\n  }\n}\nfunction ActivityLogListComponent_ng_template_42_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"div\", 58)(3, \"span\", 28);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 11);\n    i0.ɵɵtext(6, \"Loading activity logs... Please wait...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActivityLogListComponent_ng_template_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 59);\n    i0.ɵɵelement(2, \"i\", 60);\n    i0.ɵɵelementStart(3, \"p\", 11);\n    i0.ɵɵtext(4, \"No activity logs found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_ng_template_42_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 62);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActivityLogListComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ActivityLogListComponent_ng_template_42_div_0_Template, 7, 0, \"div\", 55)(1, ActivityLogListComponent_ng_template_42_div_1_Template, 8, 0, \"div\", 55);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading === false && ctx_r2.serverSideRowData.length === 0);\n  }\n}\nfunction ActivityLogListComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵelementStart(2, \"h3\", 65);\n    i0.ɵɵtext(3, \"No Activity Logs Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 66);\n    i0.ɵɵtext(5, \"Activity logs will appear here as users perform actions in the system.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ActivityLogListComponent = /*#__PURE__*/(() => {\n  class ActivityLogListComponent {\n    cdr;\n    modalService;\n    exceljsService;\n    httpUtilService;\n    AppService;\n    layoutUtilService;\n    activityLogService;\n    kendoColumnService;\n    grid;\n    // Data\n    serverSideRowData = [];\n    gridData = [];\n    IsListHasValue = false;\n    loading = false;\n    isLoading = false;\n    loginUser = {};\n    // Search\n    searchData = '';\n    searchTerms = new Subject();\n    searchSubscription;\n    // Enhanced Filters for Kendo UI\n    filter = {\n      logic: 'and',\n      filters: []\n    };\n    gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    activeFilters = [];\n    filterOptions = [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Success',\n      value: 'SUCCESS'\n    }, {\n      text: 'Failed',\n      value: 'FAILED'\n    }];\n    // Advanced filter options\n    advancedFilterOptions = {\n      status: [{\n        text: 'All',\n        value: null\n      }, {\n        text: 'Success',\n        value: 'SUCCESS'\n      }, {\n        text: 'Failed',\n        value: 'FAILED'\n      }],\n      userTypes: [{\n        text: 'All Types',\n        value: null\n      }, {\n        text: 'Admin',\n        value: 'ADMIN'\n      }, {\n        text: 'User',\n        value: 'USER'\n      }]\n    };\n    // Filter state\n    showAdvancedFilters = false;\n    appliedFilters = {};\n    // Kendo Grid properties\n    page = {\n      size: 10,\n      pageNumber: 0,\n      totalElements: 0,\n      totalPages: 0,\n      orderBy: 'createdDate',\n      orderDir: 'desc'\n    };\n    skip = 0;\n    sort = [{\n      field: 'createdDate',\n      dir: 'desc'\n    }];\n    // Column visibility system properties\n    kendoHide;\n    hiddenData = [];\n    kendoColOrder = [];\n    kendoInitColOrder = [];\n    hiddenFields = [];\n    // Column configuration for the new system\n    gridColumns = [];\n    defaultColumns = [];\n    fixedColumns = [];\n    draggableColumns = [];\n    GRID_STATE_KEY = 'activity-logs-grid-state';\n    // Export options\n    exportOptions = [{\n      text: 'Export All',\n      value: 'all'\n    }, {\n      text: 'Export Selected',\n      value: 'selected'\n    }, {\n      text: 'Export Filtered',\n      value: 'filtered'\n    }];\n    // Selection state\n    selectedLogs = [];\n    isAllSelected = false;\n    // Statistics\n    logStatistics = {\n      successLogs: 0,\n      failedLogs: 0,\n      totalLogs: 0\n    };\n    // Legacy properties (keeping for backward compatibility)\n    pageSize = AppSettings.PAGE_SIZE;\n    pageSizeOptions = AppSettings.PAGE_SIZE_OPTIONS;\n    itemsPerPage = new FormControl(this.pageSize);\n    defaultOrder = 'desc';\n    defaultOrderBy = 'createdDate';\n    statusData = false;\n    selectedTab = 'All';\n    innerWidth;\n    displayMobile = false;\n    constructor(cdr, modalService, exceljsService, httpUtilService, AppService, layoutUtilService, activityLogService, kendoColumnService) {\n      this.cdr = cdr;\n      this.modalService = modalService;\n      this.exceljsService = exceljsService;\n      this.httpUtilService = httpUtilService;\n      this.AppService = AppService;\n      this.layoutUtilService = layoutUtilService;\n      this.activityLogService = activityLogService;\n      this.kendoColumnService = kendoColumnService;\n      // set the default paging options\n      this.page.pageNumber = 0;\n      this.page.size = this.pageSize;\n      this.page.orderBy = 'createdDate';\n      this.page.orderDir = 'desc';\n    }\n    ngOnInit() {\n      this.loginUser = this.AppService.getLoggedInUser();\n      this.setupSearchSubscription();\n      this.loadTable();\n      this.loadLogStatistics();\n    }\n    ngAfterViewInit() {\n      this.loadGridState();\n      this.setupGridEventHandlers();\n    }\n    // Method to handle when the component becomes visible\n    onTabActivated() {\n      // Set loading state for tab activation\n      this.loading = true;\n      this.isLoading = true;\n      // Refresh the data when the tab is activated\n      this.loadTable();\n      this.loadLogStatistics();\n    }\n    // Refresh grid data - only refresh the grid with latest API call\n    refreshGrid() {\n      // Set loading state to show full-screen loader\n      this.loading = true;\n      this.isLoading = true;\n      // Refresh the data\n      this.loadTable();\n    }\n    ngOnDestroy() {\n      if (this.searchSubscription) {\n        this.searchSubscription.unsubscribe();\n      }\n      this.searchTerms.complete();\n    }\n    // Setup search subscription with debouncing\n    setupSearchSubscription() {\n      this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        this.loadTable();\n      });\n    }\n    // Setup grid event handlers\n    setupGridEventHandlers() {\n      if (this.grid) {\n        this.grid.pageChange.subscribe(event => {\n          this.pageChange(event);\n        });\n      }\n    }\n    // Load grid state from localStorage\n    loadGridState() {\n      try {\n        const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n        if (savedState) {\n          const state = JSON.parse(savedState);\n          this.page.size = state.pageSize || this.page.size;\n          this.sort = state.sort || this.sort;\n          this.filter = state.filter || this.filter;\n          this.skip = state.skip || 0;\n        }\n      } catch (error) {\n        console.warn('Error loading grid state:', error);\n      }\n    }\n    // Save grid state to localStorage\n    saveGridState() {\n      try {\n        const state = {\n          pageSize: this.page.size,\n          sort: this.sort,\n          filter: this.filter,\n          skip: this.skip\n        };\n        localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n      } catch (error) {\n        console.warn('Error saving grid state:', error);\n      }\n    }\n    // Load activity logs table\n    loadTable() {\n      this.loadTableWithKendoEndpoint();\n    }\n    // Load data using Kendo UI specific endpoint\n    loadTableWithKendoEndpoint() {\n      this.loading = true;\n      this.isLoading = true;\n      // Enable loader\n      this.httpUtilService.loadingSubject.next(true);\n      // Prepare state object for Kendo UI endpoint\n      const state = {\n        take: this.page.size,\n        skip: this.skip,\n        sort: this.sort,\n        filter: this.filter,\n        search: this.searchData\n      };\n      this.activityLogService.getActivityLogsForKendoGrid(state).subscribe({\n        next: data => {\n          // Handle the new API response structure\n          if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n            const errors = data.responseData?.errors || data.errors || [];\n            console.error('Kendo UI Grid errors:', errors);\n            this.handleEmptyResponse();\n          } else {\n            // Handle both old and new response structures\n            const responseData = data.responseData || data;\n            const logData = responseData.data || [];\n            const total = responseData.total || 0;\n            this.IsListHasValue = logData.length !== 0;\n            this.serverSideRowData = logData;\n            this.page.totalElements = total;\n            this.page.totalPages = Math.ceil(total / this.page.size);\n            // Create a data source with total count for Kendo Grid\n            this.gridData = {\n              data: logData,\n              total: total\n            };\n          }\n          this.httpUtilService.loadingSubject.next(false);\n        },\n        error: error => {\n          console.error('Error loading data with Kendo UI endpoint:', error);\n          this.handleEmptyResponse();\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        },\n        complete: () => {\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n    // Handle empty response\n    handleEmptyResponse() {\n      this.IsListHasValue = false;\n      this.serverSideRowData = [];\n      this.gridData = [];\n      this.page.totalElements = 0;\n      this.page.totalPages = 0;\n    }\n    // Load log statistics\n    loadLogStatistics() {\n      // This would be implemented if there's a statistics endpoint\n      // For now, we'll calculate from the current data\n      this.updateLogStatistics();\n    }\n    // Update log statistics\n    updateLogStatistics() {\n      const logs = this.serverSideRowData;\n      this.logStatistics = {\n        successLogs: logs.filter(l => l.activityStatus === 'SUCCESS').length,\n        failedLogs: logs.filter(l => l.activityStatus === 'FAILED').length,\n        totalLogs: logs.length\n      };\n    }\n    // Search functionality\n    onSearchKeyDown(event) {\n      if (event.key === 'Enter') {\n        this.loadTable();\n      }\n    }\n    onSearchChange() {\n      console.log('Search changed:', this.searchData);\n      // Trigger search with debounce\n      this.searchTerms.next(this.searchData || '');\n    }\n    clearSearch() {\n      if (!this.searchData || this.searchData.trim() === '') {\n        this.searchTerms.next('');\n      }\n    }\n    // Advanced filters\n    toggleAdvancedFilters() {\n      this.showAdvancedFilters = !this.showAdvancedFilters;\n    }\n    applyAdvancedFilters() {\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      if (this.appliedFilters.status) {\n        this.filter.filters.push({\n          field: 'activityStatus',\n          operator: 'eq',\n          value: this.appliedFilters.status\n        });\n      }\n      if (this.appliedFilters.userType) {\n        this.filter.filters.push({\n          field: 'activityUserType',\n          operator: 'eq',\n          value: this.appliedFilters.userType\n        });\n      }\n      this.loadTable();\n    }\n    clearAdvancedFilters() {\n      this.appliedFilters = {};\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.loadTable();\n    }\n    // Kendo UI Grid event handlers\n    onSortChange(event) {\n      console.log('Sort change triggered:', event.sort);\n      // Handle empty sort array (normalize/unsort case)\n      const incomingSort = Array.isArray(event.sort) ? event.sort : [];\n      if (incomingSort.length === 0) {\n        // Normalize/unsort case - return to default sorting\n        console.log('Normalize triggered - returning to default sort');\n        this.sort = [{\n          field: 'createdDate',\n          dir: 'desc'\n        }];\n        // Reset the grid's sort state to default\n        if (this.grid) {\n          this.grid.sort = [{\n            field: 'createdDate',\n            dir: 'desc'\n          }];\n        }\n      } else {\n        // Normal sorting case\n        this.sort = incomingSort;\n      }\n      console.log('Final sort state:', this.sort);\n      this.skip = 0;\n      this.page.pageNumber = 0;\n      this.saveGridState();\n      // Set loading state for sorting\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    }\n    onSelectionChange(event) {\n      this.selectedLogs = event.selectedRows || [];\n      this.isAllSelected = this.selectedLogs.length === this.serverSideRowData.length;\n    }\n    onColumnReorder(event) {\n      // Handle column reordering\n      this.kendoColOrder = event.columns;\n    }\n    updateColumnVisibility(event) {\n      // Handle column visibility changes\n      this.kendoHide = event.columns;\n    }\n    filterChange(event) {\n      this.filter = event.filter;\n      this.skip = 0;\n      this.page.pageNumber = 0;\n      this.saveGridState();\n      // Set loading state for filtering\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    }\n    pageChange(event) {\n      // Use Kendo's provided values as source of truth\n      this.skip = event.skip;\n      this.page.size = event.take || this.page.size;\n      this.page.pageNumber = Math.floor(this.skip / this.page.size);\n      this.saveGridState();\n      // Set loading state for pagination\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    }\n    /**\n     * Reset the current state of column visibility and order in the grid to its original state.\n     */\n    resetTable() {\n      // Check if loginUser is available\n      if (!this.loginUser || !this.loginUser.userId) {\n        console.error('loginUser not available:', this.loginUser);\n        this.layoutUtilService.showError('User not logged in. Please refresh the page and try again.', '');\n        return;\n      }\n      // Reset all grid state to default\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.sort = [{\n        field: 'createdDate',\n        dir: 'desc'\n      }];\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.searchData = '';\n      this.appliedFilters = {};\n      this.showAdvancedFilters = false;\n      // Reset column visibility and order\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          const index = this.gridColumns.indexOf(column.field);\n          if (index !== -1) {\n            column.orderIndex = index;\n          }\n          // Reset column visibility - show all columns\n          if (column.field && column.field !== 'action') {\n            column.hidden = false;\n          }\n        });\n      }\n      // Clear hidden columns\n      this.hiddenData = [];\n      this.kendoColOrder = [];\n      this.hiddenFields = [];\n      // Reset the Kendo Grid's internal state\n      if (this.grid) {\n        // Clear all filters\n        this.grid.filter = {\n          logic: 'and',\n          filters: []\n        };\n        // Reset sorting\n        this.grid.sort = [{\n          field: 'createdDate',\n          dir: 'desc'\n        }];\n        // Reset to first page\n        this.grid.skip = 0;\n        this.grid.pageSize = this.page.size;\n      }\n      // Prepare reset data\n      const userData = {\n        pageName: 'ActivityLogs',\n        userID: this.loginUser.userId,\n        hiddenData: [],\n        kendoColOrder: [],\n        LoggedId: this.loginUser.userId\n      };\n      // Show loading state\n      this.httpUtilService.loadingSubject.next(true);\n      // Save reset state to backend\n      this.kendoColumnService.createHideFields(userData).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!res.isFault) {\n            // Also clear from localStorage\n            this.kendoColumnService.clearFromLocalStorage('ActivityLogs');\n            this.layoutUtilService.showSuccess(res.message || 'Column settings reset successfully.', '');\n          } else {\n            this.layoutUtilService.showError(res.message || 'Failed to reset column settings.', '');\n          }\n          // Trigger change detection and refresh grid\n          this.cdr.detectChanges();\n          // Small delay to ensure the grid is updated\n          setTimeout(() => {\n            if (this.grid) {\n              this.grid.refresh();\n            }\n          }, 100);\n          this.loadTable();\n        },\n        error: error => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('Error resetting column settings:', error);\n          // Check if it's an authentication error\n          if (error.status === 401 || error.error && error.error.status === 401) {\n            this.layoutUtilService.showError('Authentication failed. Please login again.', '');\n            // Optionally redirect to login page\n          } else {\n            this.layoutUtilService.showError('Error resetting column settings. Please try again.', '');\n          }\n        }\n      });\n    }\n    // Export functionality\n    exportData(exportType) {\n      let dataToExport = [];\n      switch (exportType) {\n        case 'all':\n          dataToExport = this.serverSideRowData;\n          break;\n        case 'selected':\n          dataToExport = this.selectedLogs;\n          break;\n        case 'filtered':\n          dataToExport = this.serverSideRowData;\n          break;\n      }\n      if (dataToExport.length === 0) {\n        return;\n      }\n      const exportData = dataToExport.map(log => ({\n        'Event': log.activityEvent,\n        'Description': log.eventDescription,\n        'Details': log.eventDetails,\n        'Table': log.tableName,\n        'Status': log.activityStatus,\n        'User Type': log.activityUserType,\n        'Created Date': this.AppService.formatDate(log.createdDate),\n        'User': log.createdByUserFullName\n      }));\n      const headers = Object.keys(exportData[0]);\n      const rows = exportData.map(item => Object.values(item));\n      const colSize = headers.map((_, index) => ({\n        id: index + 1,\n        width: 20\n      }));\n      this.exceljsService.generateExcel('Activity_Logs_Export', headers, rows, colSize);\n    }\n    // Log actions\n    viewLog(log) {\n      // Navigate to view log page\n      console.log('View log:', log);\n    }\n    // Utility methods\n    getStatusClass(status) {\n      return status === 'SUCCESS' ? 'badge-success' : 'badge-danger';\n    }\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const day = date.getDate().toString().padStart(2, '0');\n      const year = date.getFullYear();\n      const timeString = date.toLocaleTimeString();\n      return `${month}/${day}/${year} ${timeString}`;\n    }\n    truncateText(text, maxLength = 50) {\n      if (!text) return '';\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n    }\n    getEventIcon(event) {\n      const eventIcons = {\n        'USER_CREATED': 'fas fa-user-plus',\n        'USER_UPDATED': 'fas fa-user-edit',\n        'USER_DELETED': 'fas fa-user-minus',\n        'ROLE_CREATED': 'fas fa-shield-plus',\n        'ROLE_UPDATED': 'fas fa-shield-edit',\n        'ROLE_DELETED': 'fas fa-shield-minus',\n        'LOGIN': 'fas fa-sign-in-alt',\n        'LOGOUT': 'fas fa-sign-out-alt',\n        'PASSWORD_CHANGED': 'fas fa-key',\n        'default': 'fas fa-info-circle'\n      };\n      return eventIcons[event] || eventIcons['default'];\n    }\n    static ɵfac = function ActivityLogListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ActivityLogListComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.ExceljsService), i0.ɵɵdirectiveInject(i3.HttpUtilsService), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.ActivityLogService), i0.ɵɵdirectiveInject(i7.KendoColumnService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActivityLogListComponent,\n      selectors: [[\"app-activity-log-list\"]],\n      viewQuery: function ActivityLogListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      decls: 44,\n      vars: 43,\n      consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"total\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\"], [\"kendoGridToolbarTemplate\", \"\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"log-statistics\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\", \"text-center\"], [1, \"col-md-4\"], [1, \"stat-item\"], [1, \"text-success\", \"mb-0\"], [1, \"text-muted\"], [1, \"text-danger\", \"mb-0\"], [1, \"text-primary\", \"mb-0\"], [\"field\", \"action\", \"title\", \"Actions\", 3, \"width\", \"sortable\", \"filterable\"], [\"kendoGridCellTemplate\", \"\"], [\"field\", \"activityEvent\", \"title\", \"Event\", 3, \"width\", \"filterable\"], [\"field\", \"eventDescription\", \"title\", \"Description\", 3, \"width\", \"filterable\"], [\"field\", \"tableName\", \"title\", \"Table\", 3, \"width\", \"filterable\"], [\"field\", \"activityStatus\", \"title\", \"Status\", 3, \"width\", \"filterable\"], [\"field\", \"activityUserType\", \"title\", \"User Type\", 3, \"width\", \"filterable\"], [\"field\", \"createdDate\", \"title\", \"Created Date\", 3, \"width\", \"filterable\"], [\"field\", \"createdByUserFullName\", \"title\", \"User\", 3, \"width\", \"filterable\"], [\"kendoGridNoRecordsTemplate\", \"\"], [\"class\", \"text-center p-5\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\", \"me-2\"], [1, \"fw-bold\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [1, \"w-100\", 3, \"ngModelChange\", \"data\", \"textField\", \"valueField\", \"ngModel\"], [1, \"col-md-6\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"me-2\", 3, \"click\", \"themeColor\"], [\"kendoButton\", \"\", 3, \"click\", \"themeColor\"], [1, \"btn-group\", \"btn-group-sm\"], [\"kendoButton\", \"\", 1, \"btn-sm\", 3, \"click\", \"themeColor\"], [1, \"fas\", \"fa-eye\"], [1, \"d-flex\", \"align-items-center\"], [1, \"me-2\", \"text-primary\"], [1, \"badge\", \"bg-info\"], [1, \"badge\", 3, \"ngClass\"], [1, \"badge\", \"bg-secondary\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [\"role\", \"status\", 1, \"custom-colored-spinner-sm\", \"me-3\"], [1, \"text-center\"], [1, \"fas\", \"fa-clipboard-list\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"], [1, \"text-center\", \"p-5\"], [1, \"fas\", \"fa-clipboard-list\", \"fa-4x\", \"text-muted\", \"mb-4\"], [1, \"text-muted\", \"mb-3\"], [1, \"text-muted\", \"mb-4\"]],\n      template: function ActivityLogListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, ActivityLogListComponent_div_0_Template, 7, 0, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"kendo-grid\", 3, 0);\n          i0.ɵɵlistener(\"columnReorder\", function ActivityLogListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          })(\"selectionChange\", function ActivityLogListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChange($event));\n          })(\"filterChange\", function ActivityLogListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterChange($event));\n          })(\"pageChange\", function ActivityLogListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChange($event));\n          })(\"sortChange\", function ActivityLogListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSortChange($event));\n          })(\"columnVisibilityChange\", function ActivityLogListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n          });\n          i0.ɵɵtemplate(4, ActivityLogListComponent_ng_template_4_Template, 12, 5, \"ng-template\", 4)(5, ActivityLogListComponent_div_5_Template, 15, 10, \"div\", 5);\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"h4\", 10);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"small\", 11);\n          i0.ɵɵtext(13, \"Success Logs\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 9)(16, \"h4\", 12);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"small\", 11);\n          i0.ɵɵtext(19, \"Failed Logs\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 8)(21, \"div\", 9)(22, \"h4\", 13);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"small\", 11);\n          i0.ɵɵtext(25, \"Total Logs\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(26, \"kendo-grid-column\", 14);\n          i0.ɵɵtemplate(27, ActivityLogListComponent_ng_template_27_Template, 3, 1, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"kendo-grid-column\", 16);\n          i0.ɵɵtemplate(29, ActivityLogListComponent_ng_template_29_Template, 4, 3, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"kendo-grid-column\", 17);\n          i0.ɵɵtemplate(31, ActivityLogListComponent_ng_template_31_Template, 1, 1, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"kendo-grid-column\", 18);\n          i0.ɵɵtemplate(33, ActivityLogListComponent_ng_template_33_Template, 2, 1, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"kendo-grid-column\", 19);\n          i0.ɵɵtemplate(35, ActivityLogListComponent_ng_template_35_Template, 2, 2, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"kendo-grid-column\", 20);\n          i0.ɵɵtemplate(37, ActivityLogListComponent_ng_template_37_Template, 2, 1, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"kendo-grid-column\", 21);\n          i0.ɵɵtemplate(39, ActivityLogListComponent_ng_template_39_Template, 1, 1, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"kendo-grid-column\", 22);\n          i0.ɵɵtemplate(41, ActivityLogListComponent_ng_template_41_Template, 1, 1, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, ActivityLogListComponent_ng_template_42_Template, 2, 2, \"ng-template\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(43, ActivityLogListComponent_div_43_Template, 6, 0, \"div\", 24);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.gridData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(38, _c2, i0.ɵɵpureFunction0(37, _c1)))(\"total\", ctx.page.totalElements)(\"sortable\", i0.ɵɵpureFunction0(40, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(41, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(42, _c5));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAdvancedFilters);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.logStatistics.successLogs);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.logStatistics.failedLogs);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.logStatistics.totalLogs);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"width\", 100)(\"sortable\", false)(\"filterable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", 180)(\"filterable\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", 250)(\"filterable\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", 120)(\"filterable\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", 100)(\"filterable\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", 120)(\"filterable\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", 180)(\"filterable\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", 150)(\"filterable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.isLoading && !ctx.IsListHasValue);\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgIf, i9.NgControlStatus, i9.NgModel, i10.GridComponent, i10.ToolbarTemplateDirective, i10.GridSpacerComponent, i10.ColumnComponent, i10.CellTemplateDirective, i10.NoRecordsTemplateDirective, i11.TextBoxComponent, i12.ButtonComponent, i13.DropDownListComponent],\n      styles: [\".grid-container[_ngcontent-%COMP%]{padding:20px;display:flex;flex-direction:column;height:100%;position:relative}.grid-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:500}.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{width:300px}.grid-toolbar[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:center;margin-bottom:10px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:8px 16px;border-radius:6px;font-weight:500;transition:all .2s ease}.advanced-filters-panel[_ngcontent-%COMP%], .log-statistics[_ngcontent-%COMP%]{background-color:#f8f9fa;border:1px solid #dee2e6;border-radius:8px;margin-bottom:20px}.log-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{padding:15px}.log-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;margin-bottom:5px}.log-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{border-radius:6px}.badge[_ngcontent-%COMP%]{padding:6px 12px;border-radius:20px;font-size:.75rem;font-weight:500}.badge-success[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724}.badge-danger[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24}.badge-info[_ngcontent-%COMP%]{background-color:#d1ecf1;color:#0c5460}.badge-secondary[_ngcontent-%COMP%]{background-color:#e2e3e5;color:#383d41}.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:4px 8px;font-size:.875rem;border-radius:4px}.custom-no-records[_ngcontent-%COMP%]{padding:40px;text-align:center}.custom-no-records[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{width:2rem;height:2rem}\"]\n    });\n  }\n  return ActivityLogListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}