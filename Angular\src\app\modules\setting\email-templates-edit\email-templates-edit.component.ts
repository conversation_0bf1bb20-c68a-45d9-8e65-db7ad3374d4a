import { Component } from '@angular/core';
import {
  ChangeDetectorRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AppService } from 'src/app/modules/services/app.service';
import { CustomLayoutUtilsService } from 'src/app/modules/services/custom-layout.utils.service';
import { HttpUtilsService } from 'src/app/modules/services/http-utils.service';
import { EmailTemplateService } from '../../services/email-template.service';
@Component({
  selector: 'app-email-templates-edit',
  templateUrl: './email-templates-edit.component.html',
  styleUrl: './email-templates-edit.component.scss'
})
export class EmailTemplatesEditComponent {
 @Input() template: any; //get template from modal
  @Input() id: any; //get template from modal
  @Output() passEntry: EventEmitter<any> = new EventEmitter();
  formGroup: FormGroup;
  loginUser: any = {}; //store localstorage user data

  selected: any = 'view'; //set default navigation tab
  FieldList: any = []; //store category from API
  constructor(
    // private CompanyService: CompanyService,
    private httpUtilService: HttpUtilsService,
    private layoutUtilService: CustomLayoutUtilsService,
    private emailTemplateservice: EmailTemplateService,
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private cdr: ChangeDetectorRef,
    public appService: AppService
  ) {}

  // Method to handle cancel button click
  onCancelClick(): void {
    // Reset loading state when cancel is clicked
    this.httpUtilService.loadingSubject.next(false);
    this.modal.dismiss();
  }
  //on init
  ngOnInit(): void {
    this.loginUser = this.appService.getLoggedInUser();
    this.getAllTemplateVariables();
    this.loadForm();
    console.log('this.id', this.id);
    if (this.id && this.id != 0) {
      this.patchForm();
    }
  }
  /**
   * Form initialization
   * Default params, validators
   */
  loadForm() {
    const formGroup: any = {};
    formGroup['TemplateName'] = new FormControl(
      '',
      Validators.compose([Validators.required])
    );
    formGroup['TemplateSubject'] = new FormControl(
      '',
      Validators.compose([Validators.required])
    );
    formGroup['emailBody'] = new FormControl(
      '',
      Validators.compose([Validators.required])
    );
    // formGroup['TemplateStatus'] = new FormControl('', Validators.compose([Validators.required]));

    this.formGroup = this.fb.group(formGroup);
    // this.patchForm();
  }

  //function for patching API email template data
  patchForm() {
    this.emailTemplateservice
      .getEmailTemplate(this.id)
      .subscribe((res: any) => {
        console.log('res.responseData.data.templateName', res);
        if (res && res.isFault == false) {
          this.formGroup.patchValue({
            TemplateName: res.responseData.templateName,
            TemplateSubject: res.responseData.emailSubject,
            emailBody: res.responseData.emailBody,
            // TemplateStatus: this.template.TemplateStatus
          });
        }
      });
  }

  controlHasError(validation: any, controlName: string | number): boolean {
    const control = this.formGroup.controls[controlName];
    if (!control) {
      return false;
    }
    let result =
      control.hasError(validation) && (control.dirty || control.touched);
    return result;
  }

  //Form Submit
  save() {
    const formData = this.formGroup.value;
    var query = {
      // TemplateId: this.template.template_id,
      templateName: formData.TemplateName,
      emailSubject: formData.TemplateSubject,
      emailBody: formData.emailBody,
      // TemplateStatus: formData.TemplateStatus,
      LoggedId: this.loginUser.userId,
    };

    // console.log('this.loginUser', this.loginUser);
    this.httpUtilService.loadingSubject.next(true);
    this.emailTemplateservice
      .createEmailTemplate(query)
      .subscribe((res: any) => {
        this.passEntry.emit(true);
        this.httpUtilService.loadingSubject.next(false);
        this.modal.close();
        this.layoutUtilService.showSuccess(res.responseData.message, '');
      });
  }

  //function to navigate tabs in email body
  showTab(tab: any, $event: any) {
    this.selected = tab;
    this.cdr.markForCheck();
  }
  //get template catgory from API
  getAllTemplateVariables() {
    var query = {};
    this.httpUtilService.loadingSubject.next(true);
    this.emailTemplateservice.getEmailCategories().subscribe((res: any) => {
      var templatecategory = [];
      templatecategory = res.responseData;

      this.httpUtilService.loadingSubject.next(false);
      const indexTemplate = templatecategory.findIndex(
        (temp: any) => temp.CategoryName === this.template.emailType
      );
      if (indexTemplate !== -1) {
        this.FieldList = templatecategory[indexTemplate].FieldsList;
      }
    });
  }

  update() {
    const formData = this.formGroup.value;

    let data = {
      templatePID: this.id,
      templateName: formData.TemplateName,
      emailSubject: formData.TemplateSubject,
      emailBody: formData.emailBody,
      LoggedId: this.loginUser.userId,
    };
    this.emailTemplateservice.updateEmailTemplate(data).subscribe((res) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.layoutUtilService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true);
        this.modal.close();
      } else {
        this.layoutUtilService.showError(res.responseData.message, '');
        this.passEntry.emit(false);
      }
    });
  }
}
