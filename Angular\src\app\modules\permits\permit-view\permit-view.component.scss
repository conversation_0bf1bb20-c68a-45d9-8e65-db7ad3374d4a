.permit-view-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

// Permit Details Header
.permit-details-header {
  padding: 0 1.5rem;
  border-bottom: 1px solid #e5eaee;
  background: transparent;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.5rem;
  }

  h4 {
    margin: 0;
    color: #3f4254;
    font-weight: 600;
    font-size: 1.1rem;
  }

  .button-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-left: auto; /* Ensure it stays at the right end */

    .btn-sm {
      font-size: 0.875rem !important;
      padding: 0.375rem 0.75rem !important;
      line-height: 1.5 !important;
    }
  }

  .back-button,
  .portal-button {
    display: inline-flex;
    align-items: center;
    gap: .3rem;
    padding: .15rem .5rem; /* smaller with explicit top/bottom padding */
    border-radius: .55rem;
    background-color: #f3f6f9;
    color: #3f4254;
    border: 1px solid #e5eaee;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    font-weight: 600;
    font-size: .8rem;
    line-height: 1; /* keep compact */
    transition: background-color .2s ease, box-shadow .2s ease, transform .02s ease;

    i {
      color: #5e6e82;
      font-size: .75rem;
    }

    &:hover {
      background-color: #eef2f7;
      box-shadow: 0 3px 10px rgba(0,0,0,0.07);
      text-decoration: none;
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 1px 4px rgba(0,0,0,0.06);
    }
  }

  .portal-button {
    i {
      margin-right: 0.25rem;
    }
  }
}

// Tab row right side buttons styling
.card-header {
  .d-flex.align-items-center.gap-2 {
    .btn-link {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 2rem; /* Fixed height for consistent alignment */
      
      &:hover {
        background-color: transparent;
        color: #3699ff !important;
        text-decoration: none;
      }
      
      &:focus {
        box-shadow: none;
        outline: none;
      }
      
      i {
        transition: color 0.2s ease;
        line-height: 1;
      }
      
      &:hover i {
        color: #3699ff !important;
      }
    }
  }
}

// Disabled button styling
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5 !important;
  color: #999 !important;
  border-color: #ddd !important;
  
  &:hover {
    background-color: #f5f5f5 !important;
    color: #999 !important;
    border-color: #ddd !important;
    box-shadow: none !important;
  }
}
.permit-details-card {
  background: #fff;
  // border-radius: 0.75rem;
  // box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  margin: 1rem 0;
  overflow: hidden;
}

.permit-details-header {
  background: #f5f6f8;
  padding: 0.75rem 1.25rem;
  border-bottom: 1px solid #e3e6ea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permit-details-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

// Notes/Actions edit button styling to match tab row edit icon
.permit-details-card .permit-details-header {
  .btn-link {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2rem; /* Same height as tab row edit icon */
    
    &:hover {
      background-color: transparent;
      color: #3699ff !important;
      text-decoration: none;
    }
    
    &:focus {
      box-shadow: none;
      outline: none;
    }
    
    i {
      transition: color 0.2s ease;
      line-height: 1;
    }
    
    &:hover i {
      color: #3699ff !important;
    }
  }
}
.edit-btn {
  background: #1b7e6c;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 0.35rem 0.75rem;
  font-size: 0.85rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: background 0.2s ease;
}

.edit-btn:hover {
  background: #166354;
}
.permit-details-content {
  padding: 1rem 1.5rem;
    border-radius: 0.75rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  margin: 1rem 0;
  overflow: hidden;
}

.permit-details-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr; /* Make Project Name wider */
  gap: 1.5rem;
}

// Notes & Actions full-width layout
.notes-actions-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.permit-detail-item-full {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  width: 100%;

  label {
    display: block;
    line-height: 1.2;
    font-size: 0.875rem;
    font-weight: 600;
    color: #6c7293;
    text-transform: capitalize;
    letter-spacing: 0.1rem;
    margin: 0;
  }

  .permit-value {
    font-size: 1rem;
    color: #3f4254;
    font-weight: 500;
    padding: 0.5rem 0;
    border-bottom: none;
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}

.permit-detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;

  label {
    display: block;
    line-height: 1.2;
    font-size: 0.875rem;
    font-weight: 600;
    color: #111111;
    text-transform: capitalize;
    letter-spacing: 0.1rem;
    margin: 0;
  }

  .permit-value,
  .permit-status {
    margin-top: 0.1rem;
  }

  .permit-value {
    font-size: 1rem;
    color: #3f4254;
    font-weight: 500;
    padding: 0.5rem 0;
    border-bottom: none; /* remove divider line */
  }

  .permit-status {
    display: block;
    vertical-align: top;
    padding: 0.5rem 0; /* match spacing with other fields */
    font-size: 1rem;
    font-weight: 600;
    text-align: left; /* align from left */
    background: transparent; /* no badge background */
    border: none; /* no border */
    min-width: 0; /* allow natural width */
    border-radius: 0;
  }

  // Status color variations
  .status-approved {
    background-color: #e8f5e8;
    color: #1b5e20;
    border: 1px solid #c8e6c9;
  }

  .status-pending {
    background-color: #fff3e0;
    color: #e65100;
    border: 1px solid #ffcc02;
  }

  .status-rejected {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }

  .status-submitted {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
  }

  .status-void {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
  }

  .status-unknown {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
  }

  .status-requires-resubmit {
    background-color: #fff8e1;
    color: #f57f17;
    border: 1px solid #ffecb3;
  }

  .status-conditional-approval {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
  }

  .status-under-review {
    background-color: #e8eaf6;
    color: #3949ab;
    border: 1px solid #c5cae9;
  }
}

// Loading Section
.loading-section {
  background: #fff;
  border-radius: .475rem;
  padding: 2rem;
  text-align: center;
  border: 1px solid #e5eaee;

  .spinner-border {
    width: 3rem;
    height: 3rem;
  }

  p {
    color: #6c7293;
    margin: 0;
  }

  .loading-tips {
    .text-muted {
      color: #a1a5b7 !important;
      font-size: 0.875rem;

      i {
        margin-right: 0.5rem;
        color: #3699ff;
      }
    }
  }
}



// No Permit Data Message
.no-permit-data {
  margin-bottom: 1rem;

  .alert {
    border-radius: .475rem;
    border: 1px solid #e5eaee;

    h5 {
      margin: 0 0 0.5rem 0;
      color: #3f4254;
      font-weight: 600;
    }

    p {
      margin: 0 0 0.5rem 0;
      color: #6c7293;
    }

    ul {
      margin: 0 0 1rem 0;
      padding-left: 1.5rem;
      color: #6c7293;

      li {
        margin-bottom: 0.25rem;
      }
    }

    .btn {
      font-size: 0.875rem;
      padding: 0.5rem 1rem;

      i {
        margin-right: 0.5rem;
      }
    }
  }
}

// Table Styling for Reviews
.table-responsive {
  overflow-x: auto;
  padding: 0 0.5rem 0 0.5rem;
  margin-top: 0;
}

// Remove top padding from external reviews table
.external-reviews-table {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

// Remove top padding from the card body containing the external reviews
.card-body {
  padding-top: 0 !important;
}

.table {
  margin-bottom: 0;
  overflow: hidden;

  .audit-table-row,
  .external-submittal-row {
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #e5eaee;
    background: #ffffff;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: #f8f9fa;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    &.expanded {
      background: #e3f2fd;
      border-color: #2196f3;
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
    }

    &.table-active {
      background-color: #e3f2fd;
    }
  }

  .audit-title-cell,
  .submittal-title-cell {
    width: 75%;
    vertical-align: middle;

    .audit-status-badge,
    .submittal-status-badge {
      font-size: 0.7rem;
      padding: 0.2rem 0.4rem;
      border-radius: 0.25rem;
      font-weight: 600;
      border: 1px solid transparent;
      text-transform: uppercase;
      letter-spacing: 0.05rem;
      display: inline-block;
      min-width: 60px;
      
      // Ensure status classes are applied with higher specificity
      &.status-1-cycle-completed {
        background-color: #e8f5e8 !important;
        color: #2e7d32 !important;
        border: 1px solid #a5d6a7 !important;
      }
      
      &.status-pacifica-verification {
        background-color: #e1f5fe !important;
        color: #0277bd !important;
        border: 1px solid #81d4fa !important;
      }
      
      &.status-dis-approved {
        background-color: #ffebee !important;
        color: #c62828 !important;
        border: 1px solid #ffcdd2 !important;
      }
      
      &.status-not-required {
        background-color: #f5f5f5 !important;
        color: #757575 !important;
        border: 1px solid #e0e0e0 !important;
      }
      
      &.status-in-review {
        background-color: #e8eaf6 !important;
        color: #3949ab !important;
        border: 1px solid #c5cae9 !important;
      }
      text-align: center;

      // Status color variations
      &.status-approved {
        background-color: #e8f5e8;
        color: #1b5e20;
        border: 1px solid #c8e6c9;
      }

      &.status-pending {
        background-color: #fff3e0;
        color: #e65100;
        border: 1px solid #ffcc02;
      }

      &.status-under-review {
        background-color: #e8eaf6;
        color: #3949ab;
        border: 1px solid #c5cae9;
      }

      &.status-rejected {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ffcdd2;
      }

      &.status-requires-re-submit {
        background-color: #fff8e1;
        color: #f57f17;
        border: 1px solid #ffecb3;
      }

      &.status-approved-w-conditions {
        background-color: #f3e5f5;
        color: #7b1fa2;
        border: 1px solid #e1bee7;
      }

      &.status-complete {
        background-color: #e8f5e8;
        color: #1b5e20;
        border: 1px solid #c8e6c9;
      }

      &.status-n-a {
        background-color: #f5f5f5;
        color: #757575;
        border: 1px solid #e0e0e0;
      }

      // Internal Verification Status styles
      &.status-pending {
        background-color: #fff3e0;
        color: #e65100;
        border: 1px solid #ffcc02;
      }

      &.status-in-progress {
        background-color: #e3f2fd;
        color: #1565c0;
        border: 1px solid #90caf9;
      }

      &.status-completed {
        background-color: #e8f5e8;
        color: #1b5e20;
        border: 1px solid #c8e6c9;
      }

      &.status-verified {
        background-color: #e8f5e8;
        color: #2e7d32;
        border: 1px solid #a5d6a7;
      }

      &.status-rejected {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ffcdd2;
      }

      &.status-approved {
        background-color: #e8f5e8;
        color: #1b5e20;
        border: 1px solid #c8e6c9;
      }

      &.status-under-review {
        background-color: #e8eaf6;
        color: #3949ab;
        border: 1px solid #c5cae9;
      }

      &.status-requires-resubmit {
        background-color: #fff8e1;
        color: #f57f17;
        border: 1px solid #ffecb3;
      }

      &.status-on-hold {
        background-color: #f3e5f5;
        color: #7b1fa2;
        border: 1px solid #e1bee7;
      }

      // Additional specific status styles
      &.status-pacifica-verification {
        background-color: #e1f5fe !important;
        color: #0277bd !important;
        border: 1px solid #81d4fa !important;
      }

      &.status-dis-approved {
        background-color: #ffebee !important;
        color: #c62828 !important;
        border: 1px solid #ffcdd2 !important;
      }

      &.status-not-required {
        background-color: #f5f5f5 !important;
        color: #757575 !important;
        border: 1px solid #e0e0e0 !important;
      }

      &.status-in-review {
        background-color: #e8eaf6 !important;
        color: #3949ab !important;
        border: 1px solid #c5cae9 !important;
      }

      &.status-1-cycle-completed {
        background-color: #e8f5e8 !important;
        color: #2e7d32 !important;
        border: 1px solid #a5d6a7 !important;
      }
    }
  }

  .audit-dates-cell,
  .submittal-dates-cell {
    width: 25%;
    vertical-align: middle;
    text-align: right;
    padding-right: 0;

    .d-flex {
      gap: 1rem;
      justify-content: flex-end;
    }

    .date-item {
      text-align: center;
      width: 120px; /* fixed width to align columns */
      min-width: 120px;
      max-width: 120px;

      small {
        font-size: 0.7rem;
        font-weight: 500;
        display: block; /* ensure label takes its own line */
      }

      .fw-medium {
        font-size: 0.85rem;
        display: block; /* force date text to align vertically */
        min-height: 1.25rem; /* reserve space when empty */
      }
    }
  }


  .audit-actions-cell {
    width: 15%;
    vertical-align: middle;
    text-align: center;

    .action-icon {
      font-size: 1rem;
      color: #3699ff;
      cursor: pointer;
      transition: color 0.2s ease, transform 0.2s ease;

      &:hover {
        color: #2b7ce0;
        transform: scale(1.1);
      }

      &.edit-icon {
        color: #3699ff;

        &:hover {
          color: #2b7ce0;
        }
      }
    }
  }

  .accordion-toggle {
    color: #3699ff;
    font-size: 0.8rem;
    transition: transform 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;

    i {
      transition: transform 0.2s ease;
    }
  }

  .accordion-content-row {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    border-left: 4px solid #3699ff;
    box-shadow: inset 0 2px 4px rgba(54, 153, 255, 0.1);
    transition: all 0.3s ease;

    .accordion-content {
      border-top: 1px solid #d1e7ff;
      background: transparent;

      .reviews-container {
        max-height: 400px;
        overflow-y: auto;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 0.375rem;
        margin: 0.5rem;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(54, 153, 255, 0.15);
      }
    }
  }

  // Highlight the parent submittal row when expanded
  .external-submittal-row:has(+ .accordion-content-row:not(.d-none)) {
    background: linear-gradient(135deg, #f0f8ff 0%, #e8f2ff 100%);
    box-shadow: 0 2px 8px rgba(54, 153, 255, 0.2);
    transition: all 0.3s ease;
    margin-right: 0;

    .submittal-title-cell {
      .accordion-toggle {
        color: #2b7ce0;
        transform: scale(1.1);
      }
    }
  }

  // Add subtle animation for expansion
  .external-submittal-row {
    transition: all 0.3s ease;

    &:hover {
      background-color: #f8f9fa;
    }
  }
}

.section-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5eaee;
  background: #f8f9fa;
  border-radius: .475rem .475rem 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h5 {
    margin: 0;
    color: #3f4254;
    font-weight: 600;
    font-size: 1rem;
  }

  .section-actions {
    .btn {
      font-size: 0.75rem;
      padding: 0.375rem 0.75rem;
    }
  }

  .submittal-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    border: 1px solid transparent;

    // Status color variations for submittal status
    &.status-approved {
      background-color: #e8f5e8;
      color: #1b5e20;
      border: 1px solid #c8e6c9;
    }

    &.status-pending {
      background-color: #fff3e0;
      color: #e65100;
      border: 1px solid #ffcc02;
    }

    &.status-under-review {
      background-color: #e8eaf6;
      color: #3949ab;
      border: 1px solid #c5cae9;
    }

    // Map API status "In Review" → class "status-in-review"
    &.status-in-review {
      background-color: #e8eaf6;
      color: #3949ab;
      border: 1px solid #c5cae9;
    }

    &.status-rejected {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }

    &.status-requires-re-submit {
      background-color: #fff8e1;
      color: #f57f17;
      border: 1px solid #ffecb3;
    }

    &.status-approved-w-conditions {
      background-color: #f3e5f5;
      color: #7b1fa2;
      border: 1px solid #e1bee7;
    }

    &.status-complete {
      background-color: #e8f5e8;
      color: #1b5e20;
      border: 1px solid #c8e6c9;
    }

    &.status-n-a {
      background-color: #f5f5f5;
      color: #757575;
      border: 1px solid #e0e0e0;
    }
  }
}

// Reviews Container Styling (for accordion content)
.reviews-container {
  max-height: 400px;
  overflow-y: auto;
}


// Status Colors for Audit Entries - Global styles
.status-approved {
  background-color: #e8f5e8 !important;
  color: #1b5e20 !important;
  border: 1px solid #c8e6c9 !important;
}

.status-pending {
  background-color: #fff3e0 !important;
  color: #e65100 !important;
  border: 1px solid #ffcc02 !important;
}

.status-requires-resubmit {
  background-color: #ffebee !important;
  color: #c62828 !important;
  border: 1px solid #ffcdd2 !important;
}

.status-under-review {
  background-color: #e3f2fd !important;
  color: #1565c0 !important;
  border: 1px solid #bbdefb !important;
}

.status-complete {
  background-color: #e8f5e8 !important;
  color: #1b5e20 !important;
  border: 1px solid #c8e6c9 !important;
}

// Specific status classes for internal reviews
.status-1-cycle-completed {
  background-color: #e8f5e8 !important;
  color: #2e7d32 !important;
  border: 1px solid #a5d6a7 !important;
}

.status-pacifica-verification {
  background-color: #e1f5fe !important;
  color: #0277bd !important;
  border: 1px solid #81d4fa !important;
}

.status-dis-approved {
  background-color: #ffebee !important;
  color: #c62828 !important;
  border: 1px solid #ffcdd2 !important;
}

.status-not-required {
  background-color: #f5f5f5 !important;
  color: #757575 !important;
  border: 1px solid #e0e0e0 !important;
}

.status-in-review {
  background-color: #e8eaf6 !important;
  color: #3949ab !important;
  border: 1px solid #c5cae9 !important;
}

// Reviews Container Styling
.reviews-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 0.75rem;
}

.review-item {
  border-bottom: 1px solid #e5eaee;
  padding: 0.5rem 0;
  background: transparent;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

.review-single-line {
  display: grid;
  grid-template-columns: 20px 1fr 120px 120px 140px 140px 30px;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem;
  background: transparent;
  border-radius: 0.375rem;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.expanded {
    background: #e3f2fd;
    border: 1px solid #2196f3;
  }

  .review-title {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #3f4254;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .review-status-container,
  .reviewer-container,
  .due-date-container,
  .completed-date-container,
  .review-actions-container {
    display: flex;
    align-items: center;
    min-height: 20px;
  }

  .review-actions-container {
    gap: 0.5rem;
    justify-content: flex-end;
  }

  .review-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    border: 1px solid transparent;
    white-space: nowrap;
    display: inline-block;

    // Status color variations for review status
    &.status-approved {
      background-color: #e8f5e8;
      color: #1b5e20;
      border: 1px solid #c8e6c9;
    }

    &.status-pending {
      background-color: #fff3e0;
      color: #e65100;
      border: 1px solid #ffcc02;
    }

    &.status-under-review {
      background-color: #e8eaf6;
      color: #3949ab;
      border: 1px solid #c5cae9;
    }

    &.status-rejected {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }

    &.status-requires-re-submit {
      background-color: #fff8e1;
      color: #f57f17;
      border: 1px solid #ffecb3;
    }

    &.status-approved-w-conditions {
      background-color: #f3e5f5;
      color: #7b1fa2;
      border: 1px solid #e1bee7;
    }

    &.status-complete {
      background-color: #e8f5e8;
      color: #1b5e20;
      border: 1px solid #c8e6c9;
    }

    &.status-n-a {
      background-color: #f5f5f5;
      color: #757575;
      border: 1px solid #e0e0e0;
    }

    // Additional status variations for comprehensive coverage
    &.status-submitted {
      background-color: #e3f2fd;
      color: #1565c0;
      border: 1px solid #bbdefb;
    }

    &.status-void {
      background-color: #f5f5f5;
      color: #757575;
      border: 1px solid #e0e0e0;
    }

    &.status-conditional-approval {
      background-color: #f3e5f5;
      color: #7b1fa2;
      border: 1px solid #e1bee7;
    }

    &.status-requires-resubmit {
      background-color: #fff8e1;
      color: #f57f17;
      border: 1px solid #ffecb3;
    }

    &.status-unknown {
      background-color: #f5f5f5;
      color: #757575;
      border: 1px solid #e0e0e0;
    }
  }

  .reviewer {
    font-size: 0.8rem;
    color: #6c7293;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .due-date {
    font-size: 0.7rem;
    color: #6c7293;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .completed-date {
    font-size: 0.7rem;
    color: #6c7293;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .action-icon {
    font-size: 1rem;
    color: #3699ff;
    cursor: pointer;
    transition: color 0.2s ease, transform 0.2s ease;
    margin-left: 0.5rem;

    &:hover {
      color: #2b7ce0;
      transform: scale(1.1);
    }

    &.edit-icon {
      color: #3699ff;

      &:hover {
        color: #2b7ce0;
      }
    }

    &.view-icon {
      color: #6c7293;

      &:hover {
        color: #3699ff;
      }
    }
  }

  // Review accordion toggle icon
  .review-accordion-toggle {
    color: #3699ff;
    font-size: 0.8rem;
    transition: transform 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;

    i {
      transition: transform 0.2s ease;
    }
  }

  // Edit Review icon
  .edit-review-icon {
    color: #28a745;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0.25rem;

    &:hover {
      color: #1e7e34;
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // Download PDF icon
  .download-pdf-icon {
    color: #3699ff;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0.25rem;

    &:hover {
      color: #2b7ce0;
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // Internal reviews download button styling
  .button-group .btn-link {
    border: none;
    background: none;
    box-shadow: none;
    text-decoration: none;
    
    &:hover, &:focus, &:active {
      background: none;
      box-shadow: none;
      text-decoration: none;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      
      .download-pdf-icon {
        cursor: not-allowed;
        color: #6c757d;
        
        &:hover {
          transform: none;
          color: #6c757d;
        }
      }
    }
  }

  // External Reviews Card Styling
  .external-reviews-card {
    .card {
      border: 1px solid #e5eaee;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .card-body {
      padding: 1.5rem;
    }

    .table {
      margin-bottom: 0;
    }
  }

  // Review details accordion
  .review-details-accordion {
    margin-top: 1rem;
    border-top: 1px solid #e5eaee;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    border-radius: 0.375rem;
    box-shadow: inset 0 2px 4px rgba(54, 153, 255, 0.1);
    transition: all 0.3s ease;

    .review-details-content {
      padding: 1rem;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 0.375rem;
      margin: 0.5rem;
      box-shadow: 0 2px 8px rgba(54, 153, 255, 0.15);

      // Ensure corrections section styles are applied within review-details-content
      ::ng-deep .corrections-section,
      .corrections-section {
        background: #ffffff !important;
        border-radius: 0.75rem !important;
        padding: 2rem !important;
        border: 1px solid #e5eaee !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
        margin: 0 !important;

        .section-title {
          font-size: 1.4rem !important;
          font-weight: 800 !important;
          color: #1a202c !important;
          margin-bottom: 2rem !important;
          padding-bottom: 1rem !important;
          border-bottom: 3px solid #3699ff !important;
          position: relative !important;
          display: flex !important;
          align-items: center !important;
          gap: 1rem !important;

          &::before {
            content: '📋';
            font-size: 1.8rem;
            filter: drop-shadow(0 2px 4px rgba(54, 153, 255, 0.3));
          }

          &::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #3699ff, #1bc5bd);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(54, 153, 255, 0.4);
          }
        }

        .correction-item {
          background: #fff !important;
          border: 1px solid #e5eaee !important;
          border-radius: 0.75rem !important;
          padding: 1.5rem !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
          position: relative !important;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
          overflow: hidden !important;
          margin-bottom: 2rem !important;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #3699ff, #1bc5bd);
            border-radius: 0.75rem 0 0 0.75rem;
          }

          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(54, 153, 255, 0.15);

            &::before {
              background: linear-gradient(180deg, #1bc5bd, #3699ff);
            }
          }

          .correction-header {
            display: flex;
            align-items: flex-start;
            gap: 1.25rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f1f3f4;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: -2px;
              left: 0;
              width: 50px;
              height: 2px;
              background: linear-gradient(90deg, #3699ff, #1bc5bd);
              border-radius: 1px;
            }

            .correction-number {
              background: linear-gradient(135deg, #3699ff, #1bc5bd) !important;
              color: white !important;
              width: 32px !important;
              height: 32px !important;
              border-radius: 50% !important;
              display: flex !important;
              align-items: center !important;
              justify-content: center !important;
              font-size: 0.9rem !important;
              font-weight: 800 !important;
              box-shadow: 0 4px 12px rgba(54, 153, 255, 0.4) !important;
              flex-shrink: 0 !important;
              position: relative !important;
              transition: all 0.3s ease !important;

              &::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                background: linear-gradient(135deg, #3699ff, #1bc5bd);
                border-radius: 50%;
                z-index: -1;
                opacity: 0.3;
                animation: pulse 2s ease-in-out infinite;
              }

              @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 0.3; }
                50% { transform: scale(1.1); opacity: 0.6; }
              }
            }

            .correction-meta {
              display: flex;
              flex-direction: column;
              gap: 0.75rem;
              flex: 1;

              .meta-row {
                display: flex !important;
                align-items: center !important;
                gap: 0.75rem !important;
                padding: 0.5rem 0 !important;
                background: transparent !important;
                border-radius: 0 !important;
                border: none !important;
                transition: all 0.2s ease !important;

                &:hover {
                  background: transparent;
                  transform: none;
                }

                .meta-label {
                  font-size: 0.85rem;
                  font-weight: 700;
                  color: #4a5568;
                  text-transform: capitalize;
                  letter-spacing: 0.05rem;
                  min-width: 80px;
                }

                .meta-value {
                  font-size: 0.9rem;
                  font-weight: 600;
                  color: #2d3748;
                  flex: 1;
                }

                .resolved-date {
                  color: inherit;
                  background: transparent;
                  padding: 0.25rem 0;
                  border-radius: 0;
                  font-weight: 700;
                }
              }
            }
          }

          .correction-content {
            .correction-field {
              margin-bottom: 1.5rem;
              position: relative;

              &:last-child {
                margin-bottom: 0;
              }

              .field-label {
                font-size: 0.85rem;
                font-weight: 800;
                color: #2d3748;
                text-transform: capitalize;
                letter-spacing: 0.15rem;
                margin-bottom: 0.75rem;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                position: relative;

                i {
                  color: #3699ff;
                  font-size: 0.9rem;
                  width: 16px;
                  text-align: center;
                }

                &::before {
                  content: '▶';
                  color: #3699ff;
                  font-size: 0.8rem;
                  animation: slideRight 1.5s ease-in-out infinite;
                }

                @keyframes slideRight {
                  0%, 100% { transform: translateX(0); }
                  50% { transform: translateX(3px); }
                }
              }

              .field-content {
                font-size: 1rem !important;
                color: #2d3748 !important;
                line-height: 1.7 !important;
                padding: 1.25rem !important;
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
                border-radius: 0.75rem !important;
                border: 1px solid #e2e8f0 !important;
                box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04) !important;
                position: relative !important;
                transition: all 0.3s ease !important;

                &::before {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 4px;
                  height: 100%;
                  background: linear-gradient(180deg, #e2e8f0, #cbd5e0);
                  border-radius: 0.75rem 0 0 0.75rem;
                }

                &:hover {
                  box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);
                  transform: translateY(-1px);
                }

                &.corrective-action {
                  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
                  border-color: #fed7d7;

                  &::before {
                    background: linear-gradient(180deg, #feb2b2, #fc8181);
                  }
                }

                &.comment {
                  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
                  border-color: #bee3f8;

                  &::before {
                    background: linear-gradient(180deg, #90cdf4, #63b3ed);
                  }
                }

                &.response {
                  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%);
                  border-color: #c6f6d5;

                  &::before {
                    background: linear-gradient(180deg, #38a169, #2f855a);
                  }

                  &::after {
                    content: '✓';
                    position: absolute;
                    top: 1rem;
                    right: 1rem;
                    color: #38a169;
                    font-weight: bold;
                    font-size: 1.2rem;
                    animation: checkmark 0.6s ease-in-out;
                  }

                  @keyframes checkmark {
                    0% { transform: scale(0) rotate(0deg); }
                    50% { transform: scale(1.2) rotate(180deg); }
                    100% { transform: scale(1) rotate(360deg); }
                  }
                }

                &.eor-response {
                  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%) !important;
                  border-color: #c6f6d5 !important;
                  box-shadow: inset 0 2px 8px rgba(56, 161, 105, 0.1) !important;

                  &::before {
                    background: linear-gradient(180deg, #38a169, #2f855a) !important;
                    width: 4px !important;
                    height: 100% !important;
                    border-radius: 0.75rem 0 0 0.75rem !important;
                  }
                }

                &.responded-by {
                  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%) !important;
                  border-color: #c6f6d5 !important;
                  box-shadow: inset 0 2px 8px rgba(56, 161, 105, 0.1) !important;

                  &::before {
                    background: linear-gradient(180deg, #38a169, #2f855a) !important;
                    width: 4px !important;
                    height: 100% !important;
                    border-radius: 0.75rem 0 0 0.75rem !important;
                  }
                }
              }
            }
          }

          .correction-separator {
            height: 2px;
            background: linear-gradient(90deg, transparent, #e5eaee, transparent);
            margin: 2rem 0;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              top: -2px;
              left: 50%;
              transform: translateX(-50%);
              width: 60px;
              height: 6px;
              background: linear-gradient(90deg, #3699ff, #1bc5bd);
              border-radius: 3px;
              box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);
            }
          }
        }
      }
    }
  }


  // Corrections section styling - scoped for review-details-accordion
  ::ng-deep .review-details-accordion .corrections-section,
  .review-details-accordion .corrections-section,
  .corrections-section {
    background: #ffffff !important;
    border-radius: 0.75rem !important;
    padding: 2rem !important;
    border: 1px solid #e5eaee !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;

    .section-title {
      font-size: 1.4rem !important;
      font-weight: 800 !important;
      color: #1a202c !important;
      margin-bottom: 2rem !important;
      padding-bottom: 1rem !important;
      border-bottom: 3px solid #3699ff !important;
      position: relative !important;
      display: flex !important;
      align-items: center !important;
      gap: 1rem !important;

      &::before {
        content: '📋';
        font-size: 1.8rem;
        filter: drop-shadow(0 2px 4px rgba(54, 153, 255, 0.3));
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #3699ff, #1bc5bd);
        border-radius: 2px;
        box-shadow: 0 2px 8px rgba(54, 153, 255, 0.4);
      }
    }

    .correction-item {
      background: #fff !important;
      border: 1px solid #e5eaee !important;
      border-radius: 0.75rem !important;
      padding: 1.5rem !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
      position: relative !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      overflow: hidden !important;
      margin-bottom: 2rem !important;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, #3699ff, #1bc5bd);
        border-radius: 0.75rem 0 0 0.75rem;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(54, 153, 255, 0.15);

        &::before {
          background: linear-gradient(180deg, #1bc5bd, #3699ff);
        }
      }

      .correction-header {
        display: flex;
        align-items: flex-start;
        gap: 1.25rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f1f3f4;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 50px;
          height: 2px;
          background: linear-gradient(90deg, #3699ff, #1bc5bd);
          border-radius: 1px;
        }

        .correction-number {
          background: linear-gradient(135deg, #3699ff, #1bc5bd) !important;
          color: white !important;
          width: 32px !important;
          height: 32px !important;
          border-radius: 50% !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          font-size: 0.9rem !important;
          font-weight: 800 !important;
          box-shadow: 0 4px 12px rgba(54, 153, 255, 0.4) !important;
          flex-shrink: 0 !important;
          position: relative !important;
          transition: all 0.3s ease !important;

          &::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #3699ff, #1bc5bd);
            border-radius: 50%;
            z-index: -1;
            opacity: 0.3;
            animation: pulse 2s ease-in-out infinite;
          }

          @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.1); opacity: 0.6; }
          }
        }

        .correction-meta {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          flex: 1;

          .meta-row {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            background: transparent;
            border-radius: 0;
            border: none;
            transition: all 0.2s ease;

            &:hover {
              background: transparent;
              transform: none;
            }

            .meta-label {
              font-size: 0.85rem;
              font-weight: 700;
              color: #4a5568;
              text-transform: capitalize;
              letter-spacing: 0.05rem;
              min-width: 80px;
            }

            .meta-value {
              font-size: 0.9rem;
              font-weight: 600;
              color: #2d3748;
              flex: 1;
            }

            .resolved-date {
              color: inherit;
              background: transparent;
              padding: 0.25rem 0;
              border-radius: 0;
              font-weight: 700;
            }

            .respond-btn {
              // Remove custom styling to use Bootstrap btn-secondary styling
              margin-left: 0.5rem !important;
            }
          }
        }
      }

      .correction-content {
        .correction-field {
          margin-bottom: 1.5rem;
          position: relative;

          &:last-child {
            margin-bottom: 0;
          }

          .field-label {
            font-size: 0.85rem;
            font-weight: 800;
            color: #2d3748;
            text-transform: capitalize;
            letter-spacing: 0.15rem;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            position: relative;

            i {
              color: #3699ff;
              font-size: 0.9rem;
              width: 16px;
              text-align: center;
            }

            &::before {
              content: '▶';
              color: #3699ff;
              font-size: 0.8rem;
              animation: slideRight 1.5s ease-in-out infinite;
            }

            @keyframes slideRight {
              0%, 100% { transform: translateX(0); }
              50% { transform: translateX(3px); }
            }
          }

          .field-content {
            font-size: 1rem;
            color: #2d3748;
            line-height: 1.7;
            padding: 1.25rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04);
            position: relative;
            transition: all 0.3s ease;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 4px;
              height: 100%;
              background: linear-gradient(180deg, #e2e8f0, #cbd5e0);
              border-radius: 0.75rem 0 0 0.75rem;
            }

            &:hover {
              box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);
              transform: translateY(-1px);
            }

            &.corrective-action {
              background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
              border-color: #fed7d7;

              &::before {
                background: linear-gradient(180deg, #feb2b2, #fc8181);
              }
            }

            &.comment {
              background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
              border-color: #bee3f8;

              &::before {
                background: linear-gradient(180deg, #90cdf4, #63b3ed);
              }
            }

            &.response {
              background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%);
              border-color: #c6f6d5;

              &::before {
                background: linear-gradient(180deg, #38a169, #2f855a);
              }

              &::after {
                content: '✓';
                position: absolute;
                top: 1rem;
                right: 1rem;
                color: #38a169;
                font-weight: bold;
                font-size: 1.2rem;
                animation: checkmark 0.6s ease-in-out;
              }

              @keyframes checkmark {
                0% { transform: scale(0) rotate(0deg); }
                50% { transform: scale(1.2) rotate(180deg); }
                100% { transform: scale(1) rotate(360deg); }
              }
            }
          }
        }
      }

      .correction-separator {
        height: 2px;
        background: linear-gradient(90deg, transparent, #e5eaee, transparent);
        margin: 2rem 0;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -2px;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 6px;
          background: linear-gradient(90deg, #3699ff, #1bc5bd);
          border-radius: 3px;
          box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);
        }
      }
    }
  }

  // Comments section styling
  .comments-section {
    .section-title {
      font-size: 1rem;
      font-weight: 600;
      color: #3f4254;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #3699ff;
    }

    .comment-content {
      background: #fff;
      border: 1px solid #e5eaee;
      border-radius: 0.375rem;
      padding: 1rem;

      .comment-text {
        font-size: 0.9rem;
        color: #3f4254;
        line-height: 1.5;
      }
    }
  }

  // No data section styling
  .no-data-section {
    .no-data-message {
      padding: 2rem;
      text-align: center;
      color: #6c7293;

      i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: block;
        color: #3699ff;
      }

      span {
        font-size: 0.9rem;
      }
    }
  }

  // Review form section styling
  .review-form-section {
    background: #fff;
    border: 1px solid #e5eaee;
    border-radius: 0.375rem;

    .form-label {
      font-size: 0.875rem;
      font-weight: 600;
      color: #3f4254;
      margin-bottom: 0.5rem;
    }

    .form-control {
      border: 1px solid #e5eaee;
      border-radius: 0.375rem;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;

      &:focus {
        border-color: #3699ff;
        box-shadow: 0 0 0 0.2rem rgba(54, 153, 255, 0.25);
      }
    }

    .btn-success {
      background-color: #1bc5bd;
      border-color: #1bc5bd;
      font-size: 0.875rem;
      padding: 0.5rem 1rem;

      &:hover {
        background-color: #17a2b8;
        border-color: #17a2b8;
      }
    }
  }
}

.review-body {
  .review-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
    font-size: 0.75rem;
    color: #6c7293;

    .reviewer,
    .completed-date {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      &::before {
        content: '';
        width: 4px;
        height: 4px;
        background-color: #6c7293;
        border-radius: 50%;
      }
    }

    .view-btn {
      margin-left: auto;
      font-size: 0.65rem;
      padding: 6px 10px;
      line-height: 1;
      border-radius: 0.25rem;
      font-weight: 500;
    }
  }
}

.no-selection {
  padding: 2rem;
  text-align: center;
  color: #6c7293;
}

// Responsive Design
@media (max-width: 768px) {
  .table {
    .audit-dates-cell,
    .submittal-dates-cell {
      .d-flex {
        flex-direction: column;
        gap: 0.5rem;
      }
    }
  }

  .review-single-line {
    grid-template-columns: 20px 1fr 100px 100px 120px 120px 30px;
    gap: 0.5rem;
    font-size: 0.8rem;

    .review-status {
      font-size: 0.7rem;
      padding: 0.2rem 0.4rem;
    }

    .reviewer,
    .due-date,
    .completed-date {
      font-size: 0.65rem;
    }

    .download-pdf-icon {
      font-size: 0.9rem;
    }
  }
}

@media (max-width: 480px) {
  .review-single-line {
    grid-template-columns: 20px 1fr 80px 80px 100px 100px 25px;
    gap: 0.25rem;
    padding: 0.25rem;

    .review-status {
      font-size: 0.65rem;
      padding: 0.15rem 0.3rem;
    }

    .reviewer,
    .due-date,
    .completed-date {
      font-size: 0.6rem;
    }

    .download-pdf-icon {
      font-size: 0.8rem;
    }
  }
}

.title-wrap {
  display: flex;
  flex-direction: column;
  gap: .25rem;
}

.title-line {
  display: flex;
  align-items: baseline;
  gap: .75rem;
}

.permit-title {
  font-size: 1.05rem;
  font-weight: 700;
  color: #181c32;
}

.status-text {
  font-size: .9rem;
  font-weight: 600;
  color: #3f4254;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid transparent;
  width: fit-content;

  // Status color variations for permit status
  &.status-approved {
    background-color: #e8f5e8;
    color: #1b5e20;
    border: 1px solid #c8e6c9;
  }

  &.status-pending {
    background-color: #fff3e0;
    color: #e65100;
    border: 1px solid #ffcc02;
  }

  &.status-under-review {
    background-color: #e8eaf6;
    color: #3949ab;
    border: 1px solid #c5cae9;
  }

  &.status-rejected {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }

  &.status-submitted {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
  }

  &.status-requires-resubmit {
    background-color: #fff8e1;
    color: #f57f17;
    border: 1px solid #ffecb3;
  }

  &.status-conditional-approval {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
  }

  &.status-void {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
  }

  &.status-n-a {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
  }
}

.permit-number-line {
  font-size: .85rem;
  color: #6c7293;
  padding-bottom: .25rem;
}

// Full Screen Loading Overlay
.fullscreen-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .loading-content {
    text-align: center;
    color: #3699ff;
    padding: 2rem;
  }
}

// Specific styling for EOR/AOR/Owner Response and Comment Responded By sections
.correction-content .field-content.eor-response,
.correction-content .field-content.responded-by {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
  border-color: #e2e8f0 !important;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04) !important;

  &::before {
    background: linear-gradient(180deg, #38a169, #2f855a) !important;
    width: 4px !important;
    height: 100% !important;
    border-radius: 0.75rem 0 0 0.75rem !important;
  }
}


/* Card Container */
.notes-actions-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 1.5rem;
  max-width: 700px;
  margin: auto;
}

/* Header */
.notes-actions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.2rem;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 0.8rem;
}

.notes-actions-header .card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.edit-btn {
  background: #007bff;
  color: #fff;
  border: none;
  padding: 6px 14px;
  border-radius: 6px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}
.edit-btn:hover {
  background: #0056b3;
}

/* Body */
.notes-actions-body {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.permit-detail-item-full {
  display: flex;
  flex-direction: column;
}

.permit-detail-item-full label {
  font-weight: 600;
  margin-bottom: 0.4rem;
  color: #333;
}

.permit-detail-item-full textarea {
  width: 100%;
  min-height: 60px;
  padding: 0.6rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  resize: vertical;
  font-size: 0.95rem;
}

/* Kendo Dropdown Styling */
.kendo-dropdown {
  width: 100%;
  border-radius: 8px;
}

