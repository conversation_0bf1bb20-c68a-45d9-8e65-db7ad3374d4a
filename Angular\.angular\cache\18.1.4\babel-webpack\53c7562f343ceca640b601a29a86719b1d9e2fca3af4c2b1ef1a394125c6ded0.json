{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { NavigationStart } from '@angular/router';\nimport { AddUserComponent } from '../add-user/user-add.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"src/app/modules/services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/kendo-column.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@progress/kendo-angular-grid\";\nimport * as i11 from \"@progress/kendo-angular-inputs\";\nimport * as i12 from \"@progress/kendo-angular-buttons\";\nimport * as i13 from \"ng-inline-svg-2\";\nimport * as i14 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c7 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction UserListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 11);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"kendo-textbox\", 13);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function UserListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function UserListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    })(\"clear\", function UserListComponent_ng_template_4_Template_kendo_textbox_clear_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"span\", 15);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 18);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(14, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(16, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n  }\n}\nfunction UserListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 28)(7, \"label\", 29);\n    i0.ɵɵtext(8, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.role, $event) || (ctx_r2.appliedFilters.role = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_5_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(12, \"i\", 34);\n    i0.ɵɵtext(13, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_5_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(15, \"i\", 36);\n    i0.ɵɵtext(16, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.roles);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.role);\n  }\n}\nfunction UserListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_template_5_div_0_Template, 17, 4, \"div\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 50);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const dataItem_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.unlockUser(dataItem_r6));\n    });\n    i0.ɵɵelement(1, \"span\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 47);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.userId));\n    });\n    i0.ɵɵelement(1, \"span\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template, 2, 1, \"a\", 49);\n  }\n  if (rf & 2) {\n    const dataItem_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen055.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r6.IsLocked);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 45);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template, 3, 2, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c6));\n    i0.ɵɵproperty(\"width\", 125)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(10, _c7))(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r8.userFullName, \" \");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r9 = ctx.$implicit;\n    const column_r10 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r10)(\"filter\", filter_r9)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 52);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template, 3, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userFullName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"userFullName\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r11.email || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r12 = ctx.$implicit;\n    const column_r13 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r13)(\"filter\", filter_r12)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 56);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 250)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"email\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"email\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r14.title || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 59);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r15 = ctx.$implicit;\n    const column_r16 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r16)(\"filter\", filter_r15)(\"extra\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 58);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template, 6, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"title\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"title\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r17.phoneNo || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r18 = ctx.$implicit;\n    const column_r19 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r19)(\"filter\", filter_r18)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 60);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"phoneNo\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"phoneNo\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r20.roleName || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r21 = ctx.$implicit;\n    const column_r22 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r22)(\"filter\", filter_r21)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 61);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"roleName\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"roleName\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 65);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 66);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen040.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_0_Template, 1, 1, \"span\", 63)(1, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_1_Template, 1, 1, \"span\", 64);\n  }\n  if (rf & 2) {\n    const dataItem_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", dataItem_r23.userStatus === \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r23.userStatus === \"Inactive\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 67);\n    i0.ɵɵlistener(\"valueChange\", function UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r24 = i0.ɵɵrestoreView(_r24);\n      const filter_r26 = ctx_r24.$implicit;\n      const column_r27 = ctx_r24.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onStatusFilterChange($event, filter_r26, column_r27));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r27 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"data\", ctx_r2.filterOptions)(\"value\", ctx_r2.getFilterValue(filter_r26, column_r27));\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 62);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template, 2, 2, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template, 1, 2, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 100)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userStatus\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"userStatus\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"span\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r28 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, dataItem_r28.lastUpdatedDate, \"MM/dd/yyyy hh:mm a\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(dataItem_r28.lastUpdatedByFullName);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r29 = ctx.$implicit;\n    const column_r30 = ctx.column;\n    const filterService_r31 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r30)(\"filter\", filter_r29)(\"filterService\", filterService_r31);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 68);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template, 6, 5, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template, 7, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 160)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"maxResizableWidth\", 240)(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_1_Template, 2, 11, \"kendo-grid-column\", 37)(2, UserListComponent_ng_container_6_kendo_grid_column_2_Template, 3, 8, \"kendo-grid-column\", 38)(3, UserListComponent_ng_container_6_kendo_grid_column_3_Template, 3, 7, \"kendo-grid-column\", 39)(4, UserListComponent_ng_container_6_kendo_grid_column_4_Template, 3, 7, \"kendo-grid-column\", 40)(5, UserListComponent_ng_container_6_kendo_grid_column_5_Template, 3, 7, \"kendo-grid-column\", 41)(6, UserListComponent_ng_container_6_kendo_grid_column_6_Template, 3, 7, \"kendo-grid-column\", 42)(7, UserListComponent_ng_container_6_kendo_grid_column_7_Template, 3, 7, \"kendo-grid-column\", 43)(8, UserListComponent_ng_container_6_kendo_grid_column_8_Template, 3, 8, \"kendo-grid-column\", 44);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r32 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"userFullName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"email\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"title\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"phoneNo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"roleName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"userStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"lastUpdatedDate\");\n  }\n}\nfunction UserListComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"div\", 74)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 15);\n    i0.ɵɵtext(6, \"Loading users... Please wait...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserListComponent_ng_template_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"i\", 76);\n    i0.ɵɵelementStart(3, \"p\", 15);\n    i0.ɵɵtext(4, \"No users found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_7_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 78);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserListComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_template_7_div_0_Template, 7, 0, \"div\", 71)(1, UserListComponent_ng_template_7_div_1_Template, 8, 0, \"div\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading || ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && !ctx_r2.isLoading && ctx_r2.serverSideRowData.length === 0);\n  }\n}\nexport let UserListComponent = /*#__PURE__*/(() => {\n  class UserListComponent {\n    usersService;\n    cdr;\n    router;\n    route;\n    modalService;\n    AppService;\n    customLayoutUtilsService;\n    httpUtilService;\n    kendoColumnService;\n    grid;\n    // Data\n    serverSideRowData = [];\n    gridData = [];\n    IsListHasValue = false;\n    loading = false;\n    isLoading = false;\n    loginUser = {};\n    // Search\n    searchData = '';\n    searchTerms = new Subject();\n    searchSubscription;\n    // Enhanced Filters for Kendo UI\n    filter = {\n      logic: 'and',\n      filters: []\n    };\n    gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    activeFilters = [];\n    filterOptions = [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }];\n    // Advanced filter options\n    advancedFilterOptions = {\n      status: [{\n        text: 'All',\n        value: null\n      }, {\n        text: 'Active',\n        value: 'Active'\n      }, {\n        text: 'Inactive',\n        value: 'Inactive'\n      }],\n      roles: [] // Will be populated from backend\n    };\n    // Filter state\n    showAdvancedFilters = false;\n    appliedFilters = {};\n    // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\n    kendoHide;\n    hiddenData = [];\n    kendoColOrder = [];\n    kendoInitColOrder = [];\n    hiddenFields = [];\n    // Column configuration for the new system\n    gridColumns = [];\n    defaultColumns = [];\n    fixedColumns = [];\n    draggableColumns = [];\n    normalGrid;\n    expandedGrid;\n    isExpanded = false;\n    // Enhanced Columns with Kendo UI features\n    gridColumnConfig = [{\n      field: 'action',\n      title: 'Action',\n      width: 80,\n      isFixed: true,\n      type: 'action',\n      order: 1\n    }, {\n      field: 'userFullName',\n      title: 'Name',\n      width: 150,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2\n    },\n    // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\n    {\n      field: 'email',\n      title: 'Email',\n      width: 250,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 4\n    }, {\n      field: 'title',\n      title: 'Title',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5\n    }, {\n      field: 'phoneNo',\n      title: 'Phone',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6\n    }, {\n      field: 'roleName',\n      title: 'Role',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 7\n    }, {\n      field: 'Status',\n      title: 'Status',\n      width: 100,\n      type: 'status',\n      isFixed: false,\n      filterable: true,\n      order: 8\n    }, {\n      field: 'lastUpdatedDate',\n      title: 'Updated Date',\n      width: 160,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 9\n    }];\n    // OLD SYSTEM - to be removed\n    columnsVisibility = {};\n    // Old column configuration management removed - replaced with new system\n    // State\n    sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    // Router subscription for saving state on navigation\n    routerSubscription;\n    // Storage key for state persistence\n    GRID_STATE_KEY = 'form-templates-grid-state';\n    // Pagination\n    page = {\n      size: 10,\n      pageNumber: 0,\n      totalElements: 0,\n      totalPages: 0,\n      orderBy: 'lastUpdatedDate',\n      orderDir: 'desc'\n    };\n    skip = 0;\n    // Export options\n    exportOptions = [{\n      text: 'Export All',\n      value: 'all'\n    }, {\n      text: 'Export Selected',\n      value: 'selected'\n    }, {\n      text: 'Export Filtered',\n      value: 'filtered'\n    }];\n    // Selection state\n    selectedUsers = [];\n    isAllSelected = false;\n    // Statistics\n    userStatistics = {\n      activeUsers: 0,\n      inactiveUsers: 0,\n      suspendedUsers: 0,\n      lockedUsers: 0,\n      totalUsers: 0\n    };\n    // Bulk operations\n    showBulkActions = false;\n    bulkActionStatus = 'Active';\n    //add or edit default paramters\n    permissionArray = [];\n    constructor(usersService, cdr, router, route, modalService,\n    // Provides modal functionality to display modals\n    AppService, customLayoutUtilsService, httpUtilService, kendoColumnService) {\n      this.usersService = usersService;\n      this.cdr = cdr;\n      this.router = router;\n      this.route = route;\n      this.modalService = modalService;\n      this.AppService = AppService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.httpUtilService = httpUtilService;\n      this.kendoColumnService = kendoColumnService;\n    }\n    ngOnInit() {\n      this.loginUser = this.AppService.getLoggedInUser();\n      console.log('Login user loaded:', this.loginUser);\n      // Setup search with debounce\n      this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(searchTerm => {\n        console.log('Search triggered with term:', searchTerm);\n        this.page.pageNumber = 0;\n        this.skip = 0;\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        // Force change detection to show loader\n        this.cdr.detectChanges();\n        this.loadTable();\n      });\n      // Subscribe to router events to save state before navigation\n      this.routerSubscription = this.router.events.subscribe(event => {\n        if (event instanceof NavigationStart) {\n          this.saveGridState();\n        }\n      });\n      // Load saved state if available\n      this.loadGridState();\n      // Load roles for advanced filters\n      this.loadRoles();\n      // Load user statistics\n      this.loadUserStatistics();\n      // Initialize with default page load\n      this.onPageLoad();\n      // Initialize new column visibility system\n      this.initializeColumnVisibilitySystem();\n      // Load column configuration after a short delay to ensure loginUser is available\n      setTimeout(() => {\n        this.loadColumnConfigFromDatabase();\n      }, 100);\n    }\n    /**\n     * Initialize the new column visibility system\n     */\n    initializeColumnVisibilitySystem() {\n      // Initialize default columns\n      this.defaultColumns = this.gridColumnConfig.map(col => col.field);\n      this.gridColumns = [...this.defaultColumns];\n      // Set fixed columns (first 3 columns)\n      this.fixedColumns = ['action', 'FirstName', 'LastName'];\n      // Set draggable columns (all except fixed)\n      this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\n      // Initialize normal and expanded grid references\n      this.normalGrid = this.grid;\n      this.expandedGrid = this.grid;\n    }\n    ngAfterViewInit() {\n      // Load the table after the view is initialized\n      // Small delay to ensure the grid is properly rendered\n      setTimeout(() => {\n        this.loadTable();\n      }, 200);\n    }\n    // Method to handle when the component becomes visible\n    onTabActivated() {\n      // Set loading state for tab activation\n      this.loading = true;\n      this.isLoading = true;\n      // Refresh the data when the tab is activated\n      this.loadTable();\n      this.loadUserStatistics();\n    }\n    // Method to handle initial page load\n    onPageLoad() {\n      // Initialize the component with default data\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.sort = [{\n        field: 'LastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.searchData = '';\n      // Set loading state for initial page load\n      this.loading = true;\n      this.isLoading = true;\n      // Load the data\n      this.loadTable();\n      this.loadUserStatistics();\n    }\n    // Refresh grid data - only refresh the grid with latest API call\n    refreshGrid() {\n      // Set loading state to show full-screen loader\n      this.loading = true;\n      this.isLoading = true;\n      // Reset to first page and clear any applied filters\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.gridFilter = {\n        logic: 'and',\n        filters: []\n      };\n      this.activeFilters = [];\n      this.appliedFilters = {};\n      // Clear search data\n      this.searchData = '';\n      // Load fresh data from API\n      this.loadTable();\n    }\n    // Old column configuration methods removed - replaced with new system\n    // Old column selector methods removed - replaced with new system\n    ngOnDestroy() {\n      // Clean up subscriptions\n      if (this.routerSubscription) {\n        this.routerSubscription.unsubscribe();\n      }\n      if (this.searchSubscription) {\n        this.searchSubscription.unsubscribe();\n      }\n      this.searchTerms.complete();\n    }\n    // New method to load data using Kendo UI specific endpoint\n    loadTableWithKendoEndpoint() {\n      this.loading = true;\n      this.isLoading = true;\n      // Enable loader\n      this.httpUtilService.loadingSubject.next(true);\n      // Force change detection to show loader\n      this.cdr.detectChanges();\n      // Prepare state object for Kendo UI endpoint\n      const state = {\n        take: this.page.size,\n        skip: this.skip,\n        sort: this.sort,\n        filter: this.filter.filters,\n        search: this.searchData,\n        loggedInUserId: this.loginUser.userId\n      };\n      console.log('Loading table with search term:', this.searchData);\n      console.log('Full state object:', state);\n      console.log('Loading states - loading:', this.loading, 'isLoading:', this.isLoading);\n      this.usersService.getUsersForKendoGrid(state).subscribe({\n        next: data => {\n          // Handle the new API response structure\n          if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n            const errors = data.responseData?.errors || data.errors || [];\n            console.error('Kendo UI Grid errors:', errors);\n            this.handleEmptyResponse();\n          } else {\n            // Handle both old and new response structures\n            const responseData = data.responseData || data;\n            const userData = responseData.data || [];\n            const total = responseData.total || 0;\n            this.IsListHasValue = userData.length !== 0;\n            this.serverSideRowData = userData;\n            this.gridData = this.serverSideRowData;\n            this.page.totalElements = total;\n            this.page.totalPages = Math.ceil(total / this.page.size);\n          }\n          this.httpUtilService.loadingSubject.next(false);\n        },\n        error: error => {\n          console.error('Error loading data with Kendo UI endpoint:', error);\n          this.handleEmptyResponse();\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        },\n        complete: () => {\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.detectChanges();\n        }\n      });\n    }\n    // Enhanced loadTable method that can use either endpoint\n    loadTable() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        // Use the new Kendo UI specific endpoint for better performance\n        _this.loadTableWithKendoEndpoint();\n      })();\n    }\n    handleEmptyResponse() {\n      this.IsListHasValue = false;\n      this.serverSideRowData = [];\n      this.gridData = [];\n      this.page.totalElements = 0;\n      this.page.totalPages = 0;\n    }\n    // Enhanced search handling\n    clearSearch() {\n      // Clear search data and trigger search\n      this.searchData = '';\n      // Set loading state for clear search\n      this.loading = true;\n      this.isLoading = true;\n      this.searchTerms.next('');\n    }\n    // Clear all filters and search\n    clearAllFilters() {\n      this.searchData = '';\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.gridFilter = {\n        logic: 'and',\n        filters: []\n      };\n      this.activeFilters = [];\n      this.appliedFilters = {};\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Set loading state for clear all filters\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    }\n    // Apply advanced filters\n    applyAdvancedFilters() {\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Set loading state for advanced filters\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    }\n    // Toggle advanced filters panel\n    toggleAdvancedFilters() {\n      this.showAdvancedFilters = !this.showAdvancedFilters;\n    }\n    // Load roles for advanced filters\n    loadRoles() {\n      const queryParams = {\n        pageSize: 1000,\n        sortOrder: 'ASC',\n        sortField: 'roleName',\n        pageNumber: 0\n      };\n      this.usersService.getAllRoles(queryParams).subscribe({\n        next: data => {\n          if (data && data.responseData && data.responseData.content) {\n            this.advancedFilterOptions.roles = [{\n              text: 'All Roles',\n              value: null\n            }, ...data.responseData.content.map(role => ({\n              text: role.roleName,\n              value: role.roleName\n            }))];\n          }\n        },\n        error: error => {\n          console.error('Error loading roles:', error);\n          // Set default roles if loading fails\n          this.advancedFilterOptions.roles = [{\n            text: 'All Roles',\n            value: null\n          }];\n        }\n      });\n      this.usersService.getDefaultPermissions({}).subscribe(permissions => {\n        this.permissionArray = permissions.responseData;\n      });\n    }\n    // Load user statistics\n    loadUserStatistics() {\n      this.usersService.getUserStatistics().subscribe({\n        next: data => {\n          if (data && data.statistics) {\n            this.userStatistics = data.statistics;\n          }\n        },\n        error: error => {\n          console.error('Error loading user statistics:', error);\n        }\n      });\n    }\n    // Selection handling\n    onSelectionChange(selection) {\n      this.selectedUsers = selection.selectedRows || [];\n      this.isAllSelected = this.selectedUsers.length === this.serverSideRowData.length;\n      this.showBulkActions = this.selectedUsers.length > 0;\n    }\n    // Select all users\n    selectAllUsers() {\n      if (this.isAllSelected) {\n        this.selectedUsers = [];\n        this.isAllSelected = false;\n      } else {\n        this.selectedUsers = [...this.serverSideRowData];\n        this.isAllSelected = true;\n      }\n      this.showBulkActions = this.selectedUsers.length > 0;\n    }\n    // Delete user\n    deleteUser(user) {\n      if (confirm(`Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`)) {\n        // Show loading state\n        this.loading = true;\n        this.isLoading = true;\n        const deleteData = {\n          userId: user.userId,\n          loggedInUserId: this.loginUser.userId || 0\n        };\n        this.usersService.deleteUser(deleteData).subscribe({\n          next: response => {\n            if (response && response.message) {\n              //alert(response.message);\n              this.customLayoutUtilsService.showSuccess(response.message, '');\n              this.loadTable(); // Reload the table\n              this.loadUserStatistics(); // Reload statistics\n            }\n          },\n          error: error => {\n            console.error('Error deleting user:', error);\n            this.customLayoutUtilsService.showError('Error deleting user', '');\n            //alert('Error deleting user. Please try again.');\n            // Reset loading state on error\n            this.loading = false;\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    // Bulk update user status\n    bulkUpdateUserStatus() {\n      if (this.selectedUsers.length === 0) {\n        //alert('Please select users to update.');\n        this.customLayoutUtilsService.showSuccess('Please select users to complete', '');\n        return;\n      }\n      if (confirm(`Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`)) {\n        // Show loading state\n        this.loading = true;\n        this.isLoading = true;\n        const bulkUpdateData = {\n          userIds: this.selectedUsers.map(user => user.userId),\n          status: this.bulkActionStatus,\n          loggedInUserId: this.loginUser.userId || 0\n        };\n        this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\n          next: response => {\n            if (response && response.message) {\n              //alert(response.message);\n              this.loadTable(); // Reload the table\n              this.loadUserStatistics(); // Reload statistics\n              this.selectedUsers = []; // Clear selection\n              this.showBulkActions = false;\n            }\n          },\n          error: error => {\n            console.error('Error updating users:', error);\n            //alert('Error updating users. Please try again.');\n            this.customLayoutUtilsService.showError('Error updating users. Please try again', '');\n            // Reset loading state on error\n            this.loading = false;\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    // Unlock user\n    unlockUser(user) {\n      if (confirm(`Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`)) {\n        // Show loading state\n        this.loading = true;\n        this.isLoading = true;\n        const unlockData = {\n          userId: user.userId,\n          loggedInUserId: this.loginUser.userId || 0\n        };\n        this.usersService.unlockUser(unlockData).subscribe({\n          next: response => {\n            if (response && response.message) {\n              this.customLayoutUtilsService.showSuccess(response.message, '');\n              //alert(response.message);\n              this.loadTable(); // Reload the table\n              this.loadUserStatistics(); // Reload statistics\n            }\n          },\n          error: error => {\n            console.error('Error unlocking user:', error);\n            this.customLayoutUtilsService.showError('Error unlocking user', '');\n            //alert('Error unlocking user. Please try again.');\n            // Reset loading state on error\n            this.loading = false;\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    onSearchKeyDown(event) {\n      console.log('Search keydown event:', event.key, 'Search data:', this.searchData);\n      if (event.key === 'Enter') {\n        // Trigger search immediately on Enter key\n        console.log('Triggering search on Enter key');\n        this.searchTerms.next(this.searchData || '');\n      }\n    }\n    // Handle search model changes\n    onSearchChange() {\n      // Trigger search when model changes with debouncing\n      console.log('Search model changed:', this.searchData);\n      console.log('Triggering search with debounce');\n      // Ensure search is triggered even for empty strings\n      this.searchTerms.next(this.searchData || '');\n    }\n    // Enhanced function to filter data from search and advanced filters\n    filterConfiguration() {\n      let filter = {\n        paginate: true,\n        search: '',\n        columnFilter: []\n      };\n      // Handle search text\n      let searchText;\n      if (this.searchData === null || this.searchData === undefined) {\n        searchText = '';\n      } else {\n        searchText = this.searchData;\n      }\n      filter.search = searchText.trim();\n      // Handle Kendo UI grid filters\n      if (this.activeFilters && this.activeFilters.length > 0) {\n        filter.columnFilter = [...this.activeFilters];\n      }\n      // Add advanced filters\n      if (this.appliedFilters.status && this.appliedFilters.status !== null) {\n        filter.columnFilter.push({\n          field: 'userStatus',\n          operator: 'eq',\n          value: this.appliedFilters.status\n        });\n      }\n      if (this.appliedFilters.role && this.appliedFilters.role !== null) {\n        filter.columnFilter.push({\n          field: 'roleName',\n          operator: 'eq',\n          value: this.appliedFilters.role\n        });\n      }\n      return filter;\n    }\n    // Grid event handlers\n    pageChange(event) {\n      this.skip = event.skip;\n      this.page.pageNumber = event.skip / event.take;\n      this.page.size = event.take;\n      // Set loading state for pagination\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    }\n    onSortChange(sort) {\n      console.log('Sort change triggered:', sort);\n      // Handle empty sort array (normalize/unsort case)\n      const incomingSort = Array.isArray(sort) ? sort : [];\n      this.sort = incomingSort.length > 0 ? incomingSort : [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Update page order fields for consistency\n      this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = this.sort[0].dir || 'desc';\n      console.log('Final sort state:', this.sort);\n      console.log('Page order:', {\n        orderBy: this.page.orderBy,\n        orderDir: this.page.orderDir\n      });\n      // Set loading state for sorting\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    }\n    filterChange(filter) {\n      this.filter = filter;\n      this.gridFilter = filter;\n      this.activeFilters = this.flattenFilters(filter);\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Set loading state for filtering\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    }\n    // Old column visibility methods removed - replaced with new system\n    // Fix 2: More robust getFilterValue method\n    getFilterValue(filter, column) {\n      if (!filter || !filter.filters || !column) {\n        return null;\n      }\n      const predicate = filter.filters.find(f => f && 'field' in f && f.field === column.field);\n      return predicate && 'value' in predicate ? predicate.value : null;\n    }\n    // Fix 3: More robust onStatusFilterChange method\n    onStatusFilterChange(value, filter, column) {\n      if (!filter || !filter.filters || !column) {\n        console.error('Invalid filter or column:', {\n          filter,\n          column\n        });\n        return;\n      }\n      const exists = filter.filters.findIndex(f => f && 'field' in f && f.field === column.field);\n      if (exists > -1) {\n        filter.filters.splice(exists, 1);\n      }\n      if (value !== null) {\n        filter.filters.push({\n          field: column.field,\n          operator: 'eq',\n          value: value\n        });\n      }\n      this.filterChange(filter);\n    }\n    // Fix 4: More robust flattenFilters method\n    flattenFilters(filter) {\n      const filters = [];\n      if (!filter || !filter.filters) {\n        return filters;\n      }\n      filter.filters.forEach(f => {\n        if (f && 'field' in f) {\n          // It's a FilterDescriptor\n          filters.push({\n            field: f.field,\n            operator: f.operator,\n            value: f.value\n          });\n        } else if (f && 'filters' in f) {\n          // It's a CompositeFilterDescriptor\n          filters.push(...this.flattenFilters(f));\n        }\n      });\n      return filters;\n    }\n    // Fix 5: More robust loadGridState method\n    loadGridState() {\n      try {\n        const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n        if (!savedState) {\n          return;\n        }\n        const state = JSON.parse(savedState);\n        // Restore sort state\n        if (state && state.sort) {\n          this.sort = state.sort;\n          if (this.sort && this.sort.length > 0 && this.sort[0]) {\n            this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n            this.page.orderDir = this.sort[0].dir || 'desc';\n          }\n        }\n        // Restore filter state\n        if (state && state.filter) {\n          this.filter = state.filter;\n          this.gridFilter = state.filter;\n          this.activeFilters = state.activeFilters || [];\n        }\n        // Restore pagination state\n        if (state && state.page) {\n          this.page = state.page;\n        }\n        if (state && state.skip !== undefined) {\n          this.skip = state.skip;\n        }\n        // Restore column visibility\n        if (state && state.columnsVisibility) {\n          this.columnsVisibility = state.columnsVisibility;\n        }\n        // Restore search state\n        if (state && state.searchData) {\n          this.searchData = state.searchData;\n        }\n        // Restore advanced filter states\n        if (state && state.appliedFilters) {\n          this.appliedFilters = state.appliedFilters;\n        }\n        if (state && state.showAdvancedFilters !== undefined) {\n          this.showAdvancedFilters = state.showAdvancedFilters;\n        }\n      } catch (error) {\n        console.error('Error loading grid state:', error);\n        // If there's an error, use default state\n      }\n    }\n    // Old getHiddenField method removed - replaced with new system\n    // Grid state persistence methods\n    saveGridState() {\n      const state = {\n        sort: this.sort,\n        filter: this.filter,\n        page: this.page,\n        skip: this.skip,\n        columnsVisibility: this.columnsVisibility,\n        searchData: this.searchData,\n        activeFilters: this.activeFilters,\n        appliedFilters: this.appliedFilters,\n        showAdvancedFilters: this.showAdvancedFilters\n      };\n      localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n    }\n    // Function to add a new company (calls edit function with ID 0)\n    add() {\n      this.edit(0);\n    }\n    // Function to open the edit modal for adding/editing a company\n    edit(id) {\n      console.log('Line: 413', 'call edit function: ', id);\n      // Configuration options for the modal dialog\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Open the modal and load the AddCompaniesComponent\n      const modalRef = this.modalService.open(AddUserComponent, NgbModalOptions);\n      // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n      modalRef.componentInstance.id = id;\n      modalRef.componentInstance.defaultPermissions = this.permissionArray;\n      // Subscribe to the modal event when data is updated\n      modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n        if (receivedEntry === true) {\n          // Reload the table data after a successful update\n          this.loadTable();\n        }\n      });\n    }\n    deleteTemplate(item) {\n      console.log('Delete template:', item);\n      // Implement delete functionality\n    }\n    toggleExpand() {\n      // Find grid container element and toggle fullscreen class\n      const gridContainer = document.querySelector('.grid-container');\n      if (gridContainer) {\n        gridContainer.classList.toggle('fullscreen-grid');\n        this.isExpanded = !this.isExpanded;\n        // Refresh grid after resize to ensure proper rendering\n        if (this.grid) {\n          this.grid.refresh();\n        }\n      }\n    }\n    // Enhanced export functionality\n    onExportClick(event) {\n      switch (event.item.value) {\n        case 'all':\n          this.exportAllUsers();\n          break;\n        case 'selected':\n          this.exportSelectedUsers();\n          break;\n        case 'filtered':\n          this.exportFilteredUsers();\n          break;\n        default:\n          console.warn('Unknown export option:', event.item.value);\n      }\n    }\n    exportAllUsers() {\n      const exportParams = {\n        filters: {},\n        format: 'excel'\n      };\n      this.usersService.exportUsers(exportParams).subscribe({\n        next: response => {\n          if (response && response.exportData) {\n            this.downloadExcel(response.exportData, 'All_Users');\n          }\n        },\n        error: error => {\n          console.error('Error exporting users:', error);\n          this.customLayoutUtilsService.showError('Error exporting users', '');\n          //alert('Error exporting users. Please try again.');\n        }\n      });\n    }\n    exportSelectedUsers() {\n      if (this.selectedUsers.length === 0) {\n        this.customLayoutUtilsService.showError('Please select users to export', '');\n        //alert('Please select users to export.');\n        return;\n      }\n      const exportParams = {\n        filters: {\n          userIds: this.selectedUsers.map(user => user.UserId)\n        },\n        format: 'excel'\n      };\n      this.usersService.exportUsers(exportParams).subscribe({\n        next: response => {\n          if (response && response.exportData) {\n            this.downloadExcel(response.exportData, 'Selected_Users');\n          }\n        },\n        error: error => {\n          console.error('Error exporting selected users:', error);\n          this.customLayoutUtilsService.showError('Error exporting selected users', '');\n          //alert('Error exporting selected users. Please try again.');\n        }\n      });\n    }\n    exportFilteredUsers() {\n      const exportParams = {\n        filters: {\n          status: this.appliedFilters.status,\n          role: this.appliedFilters.role,\n          searchTerm: this.searchData\n        },\n        format: 'excel'\n      };\n      this.usersService.exportUsers(exportParams).subscribe({\n        next: response => {\n          if (response && response.exportData) {\n            this.downloadExcel(response.exportData, 'Filtered_Users');\n          }\n        },\n        error: error => {\n          console.error('Error exporting filtered users:', error);\n          this.customLayoutUtilsService.showError('Error exporting filtered users', '');\n          //alert('Error exporting filtered users. Please try again.');\n        }\n      });\n    }\n    downloadExcel(data, filename) {\n      // This would typically use a library like xlsx or similar\n      // For now, we'll create a simple CSV download\n      const csvContent = this.convertToCSV(data);\n      const blob = new Blob([csvContent], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    convertToCSV(data) {\n      if (data.length === 0) return '';\n      const headers = Object.keys(data[0]);\n      const csvRows = [headers.join(',')];\n      for (const row of data) {\n        const values = headers.map(header => {\n          const value = row[header];\n          return typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\n        });\n        csvRows.push(values.join(','));\n      }\n      return csvRows.join('\\n');\n    }\n    // NEW COLUMN VISIBILITY SYSTEM METHODS\n    /**\n     * Saves the current state of column visibility and order in the grid.\n     * This function categorizes columns into visible and hidden columns, records their titles,\n     * fields, and visibility status, and also captures the order of draggable columns.\n     * After gathering the necessary data, it sends this information to the backend for saving.\n     */\n    saveHead() {\n      // Check if loginUser is available\n      if (!this.loginUser || !this.loginUser.userId) {\n        console.error('loginUser not available:', this.loginUser);\n        this.customLayoutUtilsService.showError('User not logged in. Please refresh the page.', '');\n        return;\n      }\n      const nonHiddenColumns = [];\n      const hiddenColumns = [];\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          if (!column.hidden) {\n            const columnData = {\n              title: column.title,\n              field: column.field,\n              hidden: column.hidden\n            };\n            nonHiddenColumns.push(columnData);\n          } else {\n            const columnData = {\n              title: column.title,\n              field: column.field,\n              hidden: column.hidden\n            };\n            hiddenColumns.push(columnData);\n          }\n        });\n      }\n      const draggableColumnsOrder = this.gridColumns.filter(col => !this.fixedColumns.includes(col)).map((field, index) => ({\n        field,\n        orderIndex: index\n      }));\n      // Prepare data for backend\n      const userData = {\n        pageName: 'Users',\n        userID: this.loginUser.userId,\n        hiddenData: hiddenColumns,\n        kendoColOrder: draggableColumnsOrder,\n        LoggedId: this.loginUser.userId\n      };\n      // Show loading state\n      this.httpUtilService.loadingSubject.next(true);\n      // Save to backend\n      this.kendoColumnService.createHideFields(userData).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!res.isFault) {\n            // Update local state\n            this.hiddenData = hiddenColumns;\n            this.kendoColOrder = draggableColumnsOrder;\n            this.hiddenFields = this.hiddenData.map(col => col.field);\n            // Also save to localStorage as backup\n            this.kendoColumnService.saveToLocalStorage(userData);\n            this.customLayoutUtilsService.showSuccess(res.message || 'Column settings saved successfully.', '');\n          } else {\n            this.customLayoutUtilsService.showError(res.message || 'Failed to save column settings.', '');\n          }\n          this.cdr.markForCheck();\n        },\n        error: error => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('Error saving column settings:', error);\n          // Fallback to localStorage on error\n          this.kendoColumnService.saveToLocalStorage(userData);\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          this.customLayoutUtilsService.showError('Failed to save to server. Settings saved locally.', '');\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    /**\n     * Reset the current state of column visibility and order in the grid to its original state.\n     * This function resets columns to default visibility and order, and saves the reset state.\n     */\n    resetTable() {\n      // Check if loginUser is available\n      if (!this.loginUser || !this.loginUser.userId) {\n        console.error('loginUser not available:', this.loginUser);\n        this.customLayoutUtilsService.showError('User not logged in. Please refresh the page and try again.', '');\n        return;\n      }\n      // Double-check authentication token\n      const token = this.AppService.getLocalStorageItem('permitToken', true);\n      if (!token) {\n        console.error('Authentication token not found');\n        this.customLayoutUtilsService.showError('Authentication token not found. Please login again.', '');\n        return;\n      }\n      // Reset all state variables\n      this.searchData = '';\n      this.activeFilters = [];\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.skip = 0;\n      this.page.pageNumber = 0;\n      this.gridColumns = [...this.defaultColumns];\n      // Reset sort state to default\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n      // Reset advanced filters\n      this.appliedFilters = {};\n      // Reset advanced filters visibility\n      this.showAdvancedFilters = false;\n      // Reset column order index\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          const index = this.gridColumns.indexOf(column.field);\n          if (index !== -1) {\n            column.orderIndex = index;\n          }\n          // Reset column visibility - show all columns\n          if (column.field && column.field !== 'action') {\n            column.hidden = false;\n          }\n        });\n      }\n      // Clear hidden columns\n      this.hiddenData = [];\n      this.kendoColOrder = [];\n      this.hiddenFields = [];\n      // Reset the Kendo Grid's internal state\n      if (this.grid) {\n        // Clear all filters\n        this.grid.filter = {\n          logic: 'and',\n          filters: []\n        };\n        // Reset sorting\n        this.grid.sort = [{\n          field: 'lastUpdatedDate',\n          dir: 'desc'\n        }];\n        // Reset to first page\n        this.grid.skip = 0;\n        this.grid.pageSize = this.page.size;\n      }\n      // Prepare reset data\n      const userData = {\n        pageName: 'Users',\n        userID: this.loginUser.userId,\n        hiddenData: [],\n        kendoColOrder: [],\n        LoggedId: this.loginUser.userId\n      };\n      // Only clear local settings; do not call server\n      this.kendoColumnService.clearFromLocalStorage('Users');\n      // Show loader and refresh grid\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.cdr.detectChanges();\n      // Force grid refresh to apply all changes\n      if (this.grid) {\n        setTimeout(() => {\n          this.grid.refresh();\n          this.grid.reset();\n        }, 100);\n      }\n      this.loadTable();\n    }\n    /**\n     * Loads and applies the saved column order from the user preferences or configuration.\n     * This function updates the grid column order, ensuring the fixed columns remain in place\n     * and the draggable columns are ordered according to the saved preferences.\n     */\n    loadSavedColumnOrder(kendoColOrder) {\n      try {\n        const savedOrder = kendoColOrder;\n        if (savedOrder) {\n          const parsedOrder = savedOrder;\n          if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n            // Get only the draggable columns from saved order\n            const savedDraggableColumns = parsedOrder.sort((a, b) => a.orderIndex - b.orderIndex).map(col => col.field).filter(field => !this.fixedColumns.includes(field));\n            // Add any missing draggable columns at the end\n            const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\n            // Combine fixed columns with saved draggable columns\n            this.gridColumns = [...this.fixedColumns, ...savedDraggableColumns, ...missingColumns];\n          } else {\n            this.gridColumns = [...this.defaultColumns];\n          }\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } catch (error) {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    }\n    /**\n     * Checks if a given column is marked as hidden.\n     * This function searches the `hiddenFields` array to determine if the column should be hidden.\n     */\n    getHiddenField(columnName) {\n      return this.hiddenFields.indexOf(columnName) > -1;\n    }\n    /**\n     * Handles the column reordering event triggered when a column is moved by the user.\n     * The function checks if the column being moved is in the fixed columns and prevents reordering\n     * of fixed columns.\n     */\n    onColumnReorder(event) {\n      const {\n        columns,\n        newIndex,\n        oldIndex\n      } = event;\n      // Prevent reordering of fixed columns\n      if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\n        return;\n      }\n      // Update the gridColumns array\n      const reorderedColumns = [...this.gridColumns];\n      const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n      reorderedColumns.splice(newIndex, 0, movedColumn);\n      this.gridColumns = reorderedColumns;\n      this.cdr.markForCheck();\n    }\n    /**\n     * Handles column visibility changes from the Kendo Grid.\n     * Updates the local state when columns are shown or hidden.\n     */\n    updateColumnVisibility(event) {\n      if (this.isExpanded === false) {\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach(column => {\n            const columnData = {\n              title: column.title,\n              field: column.field,\n              hidden: column.hidden\n            };\n            if (column.hidden) {\n              const exists = this.hiddenData.some(item => item.field === columnData.field && item.hidden === true);\n              if (!exists) {\n                this.hiddenData.push(columnData);\n              }\n            } else {\n              let indexExists = this.hiddenData.findIndex(item => item.field === columnData.field && item.hidden === true);\n              if (indexExists !== -1) {\n                this.hiddenData.splice(indexExists, 1);\n              }\n            }\n          });\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          this.cdr.markForCheck();\n        }\n      }\n    }\n    /**\n     * Loads the saved column configuration from the backend or localStorage as fallback.\n     * This method is called during component initialization to restore user preferences.\n     */\n    loadColumnConfigFromDatabase() {\n      try {\n        // First try to load from backend\n        if (this.loginUser && this.loginUser.userId) {\n          this.kendoColumnService.getHideFields({\n            pageName: 'Users',\n            userID: this.loginUser.userId\n          }).subscribe({\n            next: res => {\n              if (!res.isFault && res.Data) {\n                this.kendoHide = res.Data;\n                this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\n                this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\n                this.hiddenFields = this.hiddenData.map(col => col.field);\n                // Update grid columns based on the hidden fields\n                if (this.grid && this.grid.columns) {\n                  this.grid.columns.forEach(column => {\n                    if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                      column.includeInChooser = true;\n                      column.hidden = true;\n                    } else {\n                      column.hidden = false;\n                    }\n                  });\n                }\n                // Load saved column order and update grid\n                this.loadSavedColumnOrder(this.kendoInitColOrder);\n                // Also save to localStorage as backup\n                this.kendoColumnService.saveToLocalStorage({\n                  pageName: 'Users',\n                  userID: this.loginUser.userId,\n                  hiddenData: this.hiddenData,\n                  kendoColOrder: this.kendoInitColOrder\n                });\n              }\n            },\n            error: error => {\n              console.error('Error loading from backend, falling back to localStorage:', error);\n              this.loadFromLocalStorageFallback();\n            }\n          });\n        } else {\n          // Fallback to localStorage if no user ID\n          this.loadFromLocalStorageFallback();\n        }\n      } catch (error) {\n        console.error('Error loading column configuration:', error);\n        this.loadFromLocalStorageFallback();\n      }\n    }\n    /**\n     * Fallback method to load column configuration from localStorage\n     */\n    loadFromLocalStorageFallback() {\n      try {\n        const savedConfig = this.kendoColumnService.getFromLocalStorage('Users', this.loginUser?.UserId || 0);\n        if (savedConfig) {\n          this.kendoHide = savedConfig;\n          this.hiddenData = savedConfig.hiddenData || [];\n          this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          // Update grid columns based on the hidden fields\n          if (this.grid && this.grid.columns) {\n            this.grid.columns.forEach(column => {\n              if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                column.includeInChooser = true;\n                column.hidden = true;\n              } else {\n                column.hidden = false;\n              }\n            });\n          }\n          // Load saved column order and update grid\n          this.loadSavedColumnOrder(this.kendoInitColOrder);\n        }\n      } catch (error) {\n        console.error('Error loading from localStorage fallback:', error);\n      }\n    }\n    static ɵfac = function UserListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.KendoColumnService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserListComponent,\n      selectors: [[\"app-user-list\"]],\n      viewQuery: function UserListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      decls: 8,\n      vars: 21,\n      consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"clear\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Role\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"userFullName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"email\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"title\", \"title\", \"Title\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"phoneNo\", \"title\", \"Phone\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"roleName\", \"title\", \"Role\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [\"title\", \"Edit\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-primary\", 3, \"inlineSVG\"], [\"title\", \"Unlock\", \"class\", \"btn btn-icon btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Unlock\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-warning\", 3, \"inlineSVG\"], [\"field\", \"userFullName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bolder\", \"cursor-pointer\"], [\"operator\", \"contains\", 3, \"column\", \"filter\", \"extra\"], [\"field\", \"email\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [3, \"innerHTML\"], [\"field\", \"title\", \"title\", \"Title\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"phoneNo\", \"title\", \"Phone\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"roleName\", \"title\", \"Role\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"ngbTooltip\", \"Active\", \"class\", \"svg-icon svg-icon-3 svg-icon-success\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Inactive\", \"class\", \"svg-icon svg-icon-3 svg-icon-danger text-danger\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Active\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-success\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"ngbTooltip\", \"Inactive\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", \"text-danger\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"valueChange\", \"data\", \"value\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\"], [1, \"text-gray-600\", \"fs-1r\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [\"role\", \"status\", 1, \"custom-colored-spinner-sm\", \"me-3\"], [1, \"text-center\"], [1, \"fas\", \"fa-users\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n      template: function UserListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, UserListComponent_div_0_Template, 7, 0, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"kendo-grid\", 3, 0);\n          i0.ɵɵlistener(\"columnReorder\", function UserListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          })(\"selectionChange\", function UserListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChange($event));\n          })(\"filterChange\", function UserListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterChange($event));\n          })(\"pageChange\", function UserListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChange($event));\n          })(\"sortChange\", function UserListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSortChange($event));\n          })(\"columnVisibilityChange\", function UserListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n          });\n          i0.ɵɵtemplate(4, UserListComponent_ng_template_4_Template, 17, 10, \"ng-template\", 4)(5, UserListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 4)(6, UserListComponent_ng_container_6_Template, 9, 8, \"ng-container\", 5)(7, UserListComponent_ng_template_7_Template, 2, 2, \"ng-template\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.serverSideRowData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(16, _c2, i0.ɵɵpureFunction0(15, _c1)))(\"sortable\", i0.ɵɵpureFunction0(18, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(19, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.page.pageNumber * ctx.page.size)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(20, _c5));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n        }\n      },\n      dependencies: [i8.NgForOf, i8.NgIf, i9.NgControlStatus, i9.NgModel, i3.NgbTooltip, i10.GridComponent, i10.ToolbarTemplateDirective, i10.GridSpacerComponent, i10.ColumnComponent, i10.CellTemplateDirective, i10.NoRecordsTemplateDirective, i10.ContainsFilterOperatorComponent, i10.EndsWithFilterOperatorComponent, i10.EqualFilterOperatorComponent, i10.NotEqualFilterOperatorComponent, i10.StartsWithFilterOperatorComponent, i10.AfterFilterOperatorComponent, i10.AfterEqFilterOperatorComponent, i10.BeforeEqFilterOperatorComponent, i10.BeforeFilterOperatorComponent, i10.StringFilterMenuComponent, i10.FilterMenuTemplateDirective, i10.DateFilterMenuComponent, i11.TextBoxComponent, i12.ButtonComponent, i13.InlineSVGDirective, i14.DropDownListComponent, i8.DatePipe],\n      styles: [\".grid-container[_ngcontent-%COMP%]{padding:20px;display:flex;flex-direction:column;height:100%;position:relative}.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{border-radius:.375rem;padding:.5rem .75rem;width:80%;border:2px solid #646367;box-shadow:0 0 6px #393a3a80}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{border-radius:.375rem;padding:.75rem 1.25rem;min-width:120px;background-color:#4c4e4f;color:#fff;font-weight:500;transition:background .3s,transform .2s}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{background-color:#4c4e4f;transform:scale(1.05)}.grid-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:500}.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{width:300px}.grid-toolbar[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:center;margin-bottom:10px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;gap:5px;font-size:.875rem;font-weight:500;padding:.375rem .75rem;border-radius:.375rem;transition:all .15s ease-in-out;min-width:40px;height:40px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:not(.btn-primary){padding:.375rem;min-width:40px;width:40px}.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{padding:.375rem .75rem;gap:.5rem}.k-grid-toolbar[_ngcontent-%COMP%]   kendo-dropdownbutton[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;gap:.5rem;font-size:.875rem;font-weight:500;padding:.375rem .75rem;border-radius:.375rem;transition:all .15s ease-in-out;min-width:40px;height:40px}.k-grid-toolbar[_ngcontent-%COMP%]   kendo-dropdownbutton[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.k-grid-toolbar[_ngcontent-%COMP%]   .custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{background-color:#6f42c1;border-color:#6f42c1;color:#fff}.k-grid-toolbar[_ngcontent-%COMP%]   .custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{background-color:#5a32a3;border-color:#5a32a3}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]{background-color:#198754;border-color:#198754;color:#fff}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover{background-color:#157347;border-color:#146c43}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%]{background-color:#ffc107;border-color:#ffc107;color:#000}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%]:hover{background-color:#ffca2c;border-color:#ffc720}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%]{background-color:#0dcaf0;border-color:#0dcaf0;color:#000}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%]:hover{background-color:#31d2f2;border-color:#25cff2}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d;color:#fff}.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover{background-color:#5c636a;border-color:#565e64}.search-section[_ngcontent-%COMP%]   .kendo-textbox[_ngcontent-%COMP%]{border-radius:.375rem;border:1px solid #dee2e6}.search-section[_ngcontent-%COMP%]   .kendo-textbox[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{border-radius:.375rem;border:1px solid #dee2e6;transition:all .15s ease-in-out}.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]:hover{border-color:#adb5bd}.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]:focus, .search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox.k-state-focused[_ngcontent-%COMP%]{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;gap:.5rem;font-size:.875rem;font-weight:500;padding:.375rem .75rem;border-radius:.375rem;transition:all .15s ease-in-out;min-width:40px;height:40px}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.total-count[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:.875rem}.total-count[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{color:#6c757d!important}.total-count[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%]{font-weight:600!important;color:#495057}.k-grid[_ngcontent-%COMP%]{border-radius:6px;overflow:hidden;box-shadow:0 2px 5px #0000001a}.no-data[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:40px 0;font-size:16px;color:#888;background-color:#f9f9f9;border-radius:6px;margin-top:20px}.detail-container[_ngcontent-%COMP%]{padding:15px;background-color:#f9f9f9;border-radius:4px}.detail-row[_ngcontent-%COMP%]{display:flex;margin-bottom:8px}.detail-row[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%]{width:120px;font-weight:500;color:#666}.status-active[_ngcontent-%COMP%]{padding:4px 8px;background-color:#e8f5e9;color:#2e7d32;border-radius:4px;font-size:12px;font-weight:500}.status-inactive[_ngcontent-%COMP%]{padding:4px 8px;background-color:#ffebee;color:#c62828;border-radius:4px;font-size:12px;font-weight:500}.status-pending[_ngcontent-%COMP%]{padding:4px 8px;background-color:#fff8e1;color:#ff8f00;border-radius:4px;font-size:12px;font-weight:500}[_nghost-%COMP%]     .k-grid-header{background-color:#f5f5f5}[_nghost-%COMP%]     .k-grid td .btn-icon{display:inline-flex;align-items:center;justify-content:center;width:32px;height:32px;padding:.25rem;border-radius:.25rem;transition:all .15s ease-in-out}[_nghost-%COMP%]     .k-grid td .btn-icon:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}[_nghost-%COMP%]     .k-button[title=Refresh]{background-color:#f8f9fa;border:1px solid #dee2e6;border-radius:6px;padding:8px 12px;transition:all .2s ease;cursor:pointer}[_nghost-%COMP%]     .k-button[title=Refresh]:hover{background-color:#e9ecef;border-color:#adb5bd;transform:scale(1.05)}[_nghost-%COMP%]     .k-button[title=Refresh] .fas.fa-sync-alt{color:#6c757d;font-size:14px}[_nghost-%COMP%]     .column-config-panel{background:linear-gradient(135deg,#667eea,#764ba2)!important;color:#fff}[_nghost-%COMP%]     .column-config-panel .form-check{margin-bottom:10px}[_nghost-%COMP%]     .column-config-panel .form-check .form-check-input{margin-right:8px}[_nghost-%COMP%]     .column-config-panel .form-check .form-check-input:checked{background-color:#28a745;border-color:#28a745}[_nghost-%COMP%]     .column-config-panel .form-check .form-check-label{color:#fff;font-weight:500;cursor:pointer}[_nghost-%COMP%]     .column-config-panel .btn-primary{background-color:#28a745;border-color:#28a745}[_nghost-%COMP%]     .column-config-panel .btn-primary:hover{background-color:#218838;border-color:#1e7e34}[_nghost-%COMP%]     .column-config-panel .btn-secondary{background-color:#6c757d;border-color:#6c757d}[_nghost-%COMP%]     .column-config-panel .btn-secondary:hover{background-color:#5a6268;border-color:#545b62}[_nghost-%COMP%]     .k-grid-column-sticky{background-color:#f8f9fa!important;border-right:2px solid #dee2e6!important}[_nghost-%COMP%]     .k-grid-column-sticky .k-grid-header{background-color:#e9ecef!important}[_nghost-%COMP%]     .k-grid-header th{font-weight:600}[_nghost-%COMP%]     .k-pager-numbers .k-link.k-state-selected{background-color:#007bff;color:#fff}[_nghost-%COMP%]     .k-button{margin-right:5px}[_nghost-%COMP%]     .k-icon{font-size:16px}[_nghost-%COMP%]     .k-grid-content{overflow-y:auto}[_nghost-%COMP%]     .k-grid tr:hover{background-color:#f0f7ff}[_nghost-%COMP%]     .k-loading-mask{background-color:#ffffffb3}[_nghost-%COMP%]     .k-loading-image:before, [_nghost-%COMP%]     .k-loading-image:after{border-color:#007bff transparent}  .k-grid td,   .k-grid th{border:none!important}  kendo-grid.k-grid .k-table-alt-row .k-grid-content-sticky{background-color:#fafafa!important}  kendo-grid.k-grid .k-grid-content-sticky{border-top-color:#00000014;border-left-color:#0000004d;border-right-color:#0000004d;background-color:#fafafa!important}  .k-grid .k-table-row.k-selected>td, .k-grid[_ngcontent-%COMP%]   td.k-selected[_ngcontent-%COMP%], .k-grid[_ngcontent-%COMP%]   .k-table-row.k-selected[_ngcontent-%COMP%] > td[_ngcontent-%COMP%], .k-grid[_ngcontent-%COMP%]   .k-table-td.k-selected[_ngcontent-%COMP%], .k-grid[_ngcontent-%COMP%]   .k-table-row.k-selected[_ngcontent-%COMP%] > .k-table-td[_ngcontent-%COMP%]{background-color:transparent!important}  .k-grid .k-table-row.k-selected:hover .k-grid-content-sticky{background-color:#fafafa!important}  .k-clear-value{color:red!important}.fullscreen-grid[_ngcontent-%COMP%]{position:fixed;inset:0;z-index:9999;background:#fff;padding:20px;overflow:auto}.advanced-filters-panel[_ngcontent-%COMP%]{background:#f8f9fa;border:1px solid #dee2e6;border-radius:.375rem;margin-bottom:1rem}.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-weight:600;color:#495057;margin-bottom:.5rem}.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]{width:100%}.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%]{border-radius:.375rem;border:1px solid #dee2e6;transition:all .15s ease-in-out}.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%]:hover{border-color:#adb5bd}.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%]:focus, .advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap.k-state-focused[_ngcontent-%COMP%]{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-select[_ngcontent-%COMP%]{border-radius:0 .375rem .375rem 0}.advanced-filters-panel[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;gap:.5rem;font-size:.875rem;font-weight:500;padding:.375rem .75rem;border-radius:.375rem;transition:all .15s ease-in-out;min-width:40px;height:40px}.advanced-filters-panel[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-primary[_ngcontent-%COMP%]{background-color:#007bff;border-color:#007bff;color:#fff}.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-primary[_ngcontent-%COMP%]:hover{background-color:#0056b3;border-color:#0056b3}.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d;color:#fff}.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#5c636a;border-color:#565e64}[_nghost-%COMP%]     .k-grid .k-grid-header{background-color:#edf0f3}[_nghost-%COMP%]     .k-grid .k-grid-header .k-header{font-weight:600;color:#495057;border-color:#dee2e6}[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover{background-color:#e9ecef}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover{background-color:#f8f9fa}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt{background-color:#f8f9fa}[_nghost-%COMP%]     .k-grid .k-pager{background-color:#f8f9fa;border-top:1px solid #dee2e6}[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content{padding:1rem}[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content .k-filter-menu-item{margin-bottom:.5rem}[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content .k-filter-menu-item .k-textbox{width:100%}[_nghost-%COMP%]     .k-button{border-radius:.375rem;font-weight:500}[_nghost-%COMP%]     .k-button.k-primary{background-color:#007bff;border-color:#007bff}[_nghost-%COMP%]     .k-button.k-primary:hover{background-color:#0056b3;border-color:#0056b3}.custom-no-records[_ngcontent-%COMP%]{text-align:center;padding:2rem;color:#6c757d;font-style:italic}@media (max-width: 768px){.advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]{margin-bottom:1rem}.k-grid-toolbar[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{margin-bottom:1rem}.k-grid-toolbar[_ngcontent-%COMP%]   .kendo-grid-spacer[_ngcontent-%COMP%]{display:none}.column-list[_ngcontent-%COMP%]{max-height:400px;overflow-y:auto}.column-item[_ngcontent-%COMP%]{border:1px solid #dee2e6;border-radius:6px;margin-bottom:8px;background-color:#fff;transition:all .2s ease}.column-item[_ngcontent-%COMP%]:hover{border-color:#adb5bd;box-shadow:0 2px 4px #0000001a}.column-item.dragging[_ngcontent-%COMP%]{opacity:.5;transform:rotate(5deg)}.column-controls[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px;gap:12px}.drag-handle[_ngcontent-%COMP%]{cursor:grab;color:#6c757d}.drag-handle[_ngcontent-%COMP%]:active{cursor:grabbing}.column-checkbox[_ngcontent-%COMP%]{flex-shrink:0}.column-info[_ngcontent-%COMP%]{flex:1}.column-info[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;margin-bottom:0;cursor:pointer}.column-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.75rem}.column-actions[_ngcontent-%COMP%]{flex-shrink:0}}  .k-grid-toolbar .k-button{display:inline-flex!important;align-items:center!important;justify-content:center!important;gap:.5rem!important;font-size:.875rem!important;font-weight:500!important;padding:.375rem .75rem!important;border-radius:.375rem!important;transition:all .15s ease-in-out!important;min-width:40px!important;height:40px!important}  .k-grid-toolbar .k-button:hover{transform:translateY(-1px)!important;box-shadow:0 2px 4px #0000001a!important}  .k-grid-toolbar kendo-dropdownbutton .k-button{padding:.375rem .75rem!important;gap:.5rem!important}  .k-grid-toolbar>*{margin-right:.5rem!important}  .k-grid-toolbar>*:last-child{margin-right:0!important}  .k-grid-toolbar .btn,   .k-grid-toolbar .k-button,   .k-grid-toolbar kendo-dropdownbutton{margin-right:.5rem!important}  .k-grid-toolbar .btn:last-child,   .k-grid-toolbar .k-button:last-child,   .k-grid-toolbar kendo-dropdownbutton:last-child{margin-right:0!important}  .k-grid .k-grid-header .k-header{background-color:#edf0f3!important;font-weight:600!important;border-color:#dee2e6!important}  .k-grid .k-grid-content .k-table-row:hover{background-color:#f0f7ff!important}  .k-grid .k-pager .k-pager-numbers .k-link{border-radius:.25rem!important;transition:all .15s ease-in-out!important}  .k-grid .k-pager .k-pager-numbers .k-link:hover{background-color:#e9ecef!important}  .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected{background-color:#007bff!important;color:#fff!important}\"]\n    });\n  }\n  return UserListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}