import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { TranslationService } from './modules/i18n';
// language list
import { locale as enLang } from './modules/i18n/vocabs/en';
import { locale as chLang } from './modules/i18n/vocabs/ch';
import { locale as esLang } from './modules/i18n/vocabs/es';
import { locale as jpLang } from './modules/i18n/vocabs/jp';
import { locale as deLang } from './modules/i18n/vocabs/de';
import { locale as frLang } from './modules/i18n/vocabs/fr';
import { ThemeModeService } from './_metronic/partials/layout/theme-mode-switcher/theme-mode.service';

@Component({
  // tslint:disable-next-line:component-selector
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'body[root]',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnInit {
  constructor(
    private translationService: TranslationService,
    private modeService: ThemeModeService
  ) {
    // register translations
    this.translationService.loadTranslations(
      enLang,
      chLang,
      esLang,
      jpLang,
      deLang,
      frLang
    );
  }

  ngOnInit() {
    this.modeService.init();
    
    // Hide splash screen when app is fully loaded
    this.hideSplashScreenWhenReady();
    
    // Test Kendo UI setup
    console.log('Kendo UI setup check:', {
      kendoAvailable: typeof window !== 'undefined' && (window as any).kendo,
      angularVersion: '18.1.4'
    });
  }

  private hideSplashScreenWhenReady() {
    // Wait for the application to be fully loaded before hiding splash screen
    // This ensures all initial data loading and component initialization is complete
    const checkAppReady = () => {
      // Check if the main content is loaded and ready
      const mainContent = document.querySelector('router-outlet') || document.querySelector('app-layout');
      const isAppReady = mainContent && document.readyState === 'complete';
      
      if (isAppReady) {
        // Additional delay to ensure smooth transition
        setTimeout(() => {
          this.hideSplashScreen();
        }, 500);
      } else {
        // Check again in 100ms
        setTimeout(checkAppReady, 100);
      }
    };
    
    // Start checking after a minimum delay
    setTimeout(checkAppReady, 1000);
  }

  private hideSplashScreen() {
    // Hide the static splash screen from index.html
    const splashScreen = document.getElementById('splash-screen');
    if (splashScreen) {
      splashScreen.style.opacity = '0';
      splashScreen.style.transition = 'opacity 0.8s ease-out';
      setTimeout(() => {
        splashScreen.style.display = 'none';
      }, 800);
    }
  }
}
