{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/http-utils.service\";\nexport class ConfirmationDialogComponent {\n  modal;\n  httpUtilService;\n  showClose = true;\n  description = '';\n  actionButtonText = '';\n  cancelButtonText = '';\n  title = '';\n  selectedTab;\n  passEntry = new EventEmitter();\n  constructor(modal, httpUtilService) {\n    this.modal = modal;\n    this.httpUtilService = httpUtilService;\n  }\n  ngOnInit() {}\n  onYesClick() {\n    this.passEntry.emit({\n      success: true,\n      selectedTab: this.selectedTab\n    });\n    // this.passEntry.emit(true)\n    this.modal.close();\n  }\n  onCancelClick() {\n    // Reset loading state before closing\n    this.httpUtilService.loadingSubject.next(false);\n    this.passEntry.emit({\n      success: false,\n      selectedTab: this.selectedTab\n    });\n    // this.passEntry.emit(false)\n    this.modal.close();\n  }\n  ngOnDestroy() {\n    // Reset loading state when component is destroyed\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  static ɵfac = function ConfirmationDialogComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfirmationDialogComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ConfirmationDialogComponent,\n    selectors: [[\"app-confirmation-dialog\"]],\n    inputs: {\n      showClose: \"showClose\",\n      description: \"description\",\n      actionButtonText: \"actionButtonText\",\n      cancelButtonText: \"cancelButtonText\",\n      title: \"title\",\n      selectedTab: \"selectedTab\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 20,\n    vars: 4,\n    consts: [[1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"example-modal-sizes-title-lg\", 1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [1, \"btn\", \"btn-icon\", \"btn-sm\", \"pl-08\", 3, \"click\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 2, \"margin-left\", \"50%\"], [1, \"modal-body\", \"medium-modal-body\", 2, \"min-height\", \"50px\"], [1, \"col-lg-12\", \"form-label\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-elevate\", \"btn-sm\", \"mr-2\", 3, \"click\"]],\n    template: function ConfirmationDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3)(4);\n        i0.ɵɵtext(5);\n        i0.ɵɵelementContainerEnd()();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 3)(7, \"a\", 4);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_a_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelement(8, \"i\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelementContainerStart(10);\n        i0.ɵɵelementStart(11, \"div\", 7);\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 8);\n        i0.ɵɵelementContainerStart(14);\n        i0.ɵɵelementStart(15, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_15_listener() {\n          return ctx.onCancelClick();\n        });\n        i0.ɵɵtext(16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtext(17, \"\\u00A0 \");\n        i0.ɵɵelementStart(18, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_18_listener() {\n          return ctx.onYesClick();\n        });\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.title);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\" \", ctx.description, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.cancelButtonText);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.actionButtonText, \"\");\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "ConfirmationDialogComponent", "modal", "httpUtilService", "showClose", "description", "actionButtonText", "cancelButtonText", "title", "selectedTab", "passEntry", "constructor", "ngOnInit", "onYesClick", "emit", "success", "close", "onCancelClick", "loadingSubject", "next", "ngOnDestroy", "i0", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "HttpUtilsService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ConfirmationDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ConfirmationDialogComponent_Template_a_click_7_listener", "dismiss", "ɵɵelement", "ConfirmationDialogComponent_Template_button_click_15_listener", "ConfirmationDialogComponent_Template_button_click_18_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\shared\\confirmation-dialog\\confirmation-dialog.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\shared\\confirmation-dialog\\confirmation-dialog.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, OnD<PERSON>roy } from '@angular/core';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { HttpUtilsService } from '../../services/http-utils.service';\n\n@Component({\n  selector: 'app-confirmation-dialog',\n  templateUrl: './confirmation-dialog.component.html',\n  styleUrls: ['./confirmation-dialog.component.scss']\n})\nexport class ConfirmationDialogComponent implements OnInit, OnDestroy {\n\n  @Input() showClose:boolean = true;\n  @Input() description:string ='';\n  @Input() actionButtonText:string ='';\n  @Input() cancelButtonText:string='';\n  @Input() title:string = '';\n  @Input() selectedTab: string;\n\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\n  constructor(public modal: NgbActiveModal,\n    private httpUtilService: HttpUtilsService\n  ) { }\n\n  ngOnInit(): void {\n  }\n\n  onYesClick(): void {\n    this.passEntry.emit({ success: true, selectedTab: this.selectedTab });\n    // this.passEntry.emit(true)\n    this.modal.close();\n\t}\n\n  onCancelClick(): void {\n    // Reset loading state before closing\n    this.httpUtilService.loadingSubject.next(false);\n    this.passEntry.emit({ success: false, selectedTab: this.selectedTab });\n    // this.passEntry.emit(false)\n    this.modal.close();\n\t}\n\n  ngOnDestroy(): void {\n    // Reset loading state when component is destroyed\n    this.httpUtilService.loadingSubject.next(false);\n  }\n\n}\n", "<div class=\"modal-content\">\r\n  <div class=\"modal-header\">\r\n    <div class=\"modal-title h5 fs-3\" id=\"example-modal-sizes-title-lg\">\r\n      <ng-container>\r\n        <ng-container>{{title}}</ng-container>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n     <a class=\"btn btn-icon  btn-sm pl-08\" (click)=\"modal.dismiss()\">\r\n        <i class=\"fa-solid fs-2 fa-xmark text-white\" style=\"    margin-left: 50%;\"></i>\r\n    </a>\r\n    </div>\r\n  </div>\r\n  <div class=\"modal-body medium-modal-body\" style=\"min-height: 50px;\">\r\n    <ng-container>\r\n      <div class=\"col-lg-12 form-label\">\r\n        {{description}}\r\n      </div>\r\n    </ng-container>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <ng-container>\r\n       <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate \" (click)=\"onCancelClick()\">{{cancelButtonText}}</button>\r\n    </ng-container>&nbsp;\r\n    <button type=\"button\" class=\"btn btn-primary btn-elevate btn-sm mr-2\" (click)=\"onYesClick()\">\r\n      {{actionButtonText}}</button>\r\n\r\n\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;;;;AASzF,OAAM,MAAOC,2BAA2B;EAUnBC,KAAA;EACTC,eAAA;EATDC,SAAS,GAAW,IAAI;EACxBC,WAAW,GAAS,EAAE;EACtBC,gBAAgB,GAAS,EAAE;EAC3BC,gBAAgB,GAAQ,EAAE;EAC1BC,KAAK,GAAU,EAAE;EACjBC,WAAW;EAEVC,SAAS,GAAsB,IAAIV,YAAY,EAAE;EAC3DW,YAAmBT,KAAqB,EAC9BC,eAAiC;IADxB,KAAAD,KAAK,GAALA,KAAK;IACd,KAAAC,eAAe,GAAfA,eAAe;EACrB;EAEJS,QAAQA,CAAA,GACR;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACH,SAAS,CAACI,IAAI,CAAC;MAAEC,OAAO,EAAE,IAAI;MAAEN,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC;IACrE;IACA,IAAI,CAACP,KAAK,CAACc,KAAK,EAAE;EACrB;EAECC,aAAaA,CAAA;IACX;IACA,IAAI,CAACd,eAAe,CAACe,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;IAC/C,IAAI,CAACT,SAAS,CAACI,IAAI,CAAC;MAAEC,OAAO,EAAE,KAAK;MAAEN,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC;IACtE;IACA,IAAI,CAACP,KAAK,CAACc,KAAK,EAAE;EACrB;EAECI,WAAWA,CAAA;IACT;IACA,IAAI,CAACjB,eAAe,CAACe,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;EACjD;;qCAlCWlB,2BAA2B,EAAAoB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;;UAA3BzB,2BAA2B;IAAA0B,SAAA;IAAAC,MAAA;MAAAxB,SAAA;MAAAC,WAAA;MAAAC,gBAAA;MAAAC,gBAAA;MAAAC,KAAA;MAAAC,WAAA;IAAA;IAAAoB,OAAA;MAAAnB,SAAA;IAAA;IAAAoB,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPpCd,EAFJ,CAAAgB,cAAA,aAA2B,aACC,aAC2C;QAE/DhB,EADF,CAAAiB,uBAAA,GAAc,GACE;QAAAjB,EAAA,CAAAkB,MAAA,GAAS;;QAE3BlB,EAAA,CAAAmB,YAAA,EAAM;QAELnB,EADD,CAAAgB,cAAA,aAAyB,WACwC;QAA1BhB,EAAA,CAAAoB,UAAA,mBAAAC,wDAAA;UAAA,OAASN,GAAA,CAAAlC,KAAA,CAAAyC,OAAA,EAAe;QAAA,EAAC;QAC5DtB,EAAA,CAAAuB,SAAA,WAA+E;QAGrFvB,EAFE,CAAAmB,YAAA,EAAI,EACE,EACF;QACNnB,EAAA,CAAAgB,cAAA,aAAoE;QAClEhB,EAAA,CAAAiB,uBAAA,IAAc;QACZjB,EAAA,CAAAgB,cAAA,cAAkC;QAChChB,EAAA,CAAAkB,MAAA,IACF;QAAAlB,EAAA,CAAAmB,YAAA,EAAM;;QAEVnB,EAAA,CAAAmB,YAAA,EAAM;QACNnB,EAAA,CAAAgB,cAAA,cAA0B;QACxBhB,EAAA,CAAAiB,uBAAA,IAAc;QACXjB,EAAA,CAAAgB,cAAA,iBAA2F;QAA1BhB,EAAA,CAAAoB,UAAA,mBAAAI,8DAAA;UAAA,OAAST,GAAA,CAAAnB,aAAA,EAAe;QAAA,EAAC;QAACI,EAAA,CAAAkB,MAAA,IAAoB;QAAAlB,EAAA,CAAAmB,YAAA,EAAS;;QAC5GnB,EAAA,CAAAkB,MAAA,eACf;QAAAlB,EAAA,CAAAgB,cAAA,kBAA6F;QAAvBhB,EAAA,CAAAoB,UAAA,mBAAAK,8DAAA;UAAA,OAASV,GAAA,CAAAvB,UAAA,EAAY;QAAA,EAAC;QAC1FQ,EAAA,CAAAkB,MAAA,IAAoB;QAI1BlB,EAJ0B,CAAAmB,YAAA,EAAS,EAG3B,EACF;;;QAzBgBnB,EAAA,CAAA0B,SAAA,GAAS;QAAT1B,EAAA,CAAA2B,iBAAA,CAAAZ,GAAA,CAAA5B,KAAA,CAAS;QAYvBa,EAAA,CAAA0B,SAAA,GACF;QADE1B,EAAA,CAAA4B,kBAAA,MAAAb,GAAA,CAAA/B,WAAA,MACF;QAK4FgB,EAAA,CAAA0B,SAAA,GAAoB;QAApB1B,EAAA,CAAA2B,iBAAA,CAAAZ,GAAA,CAAA7B,gBAAA,CAAoB;QAGhHc,EAAA,CAAA0B,SAAA,GAAoB;QAApB1B,EAAA,CAAA4B,kBAAA,MAAAb,GAAA,CAAA9B,gBAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}