{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { InviteUsersModalComponent } from './invite-users-modal/invite-users-modal.component';\nimport { MainModalComponent } from './main-modal/main-modal.component';\nimport { UpgradePlanModalComponent } from './upgrade-plan-modal/upgrade-plan-modal.component';\nimport { ModalComponent } from './modal/modal.component';\nimport { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';\nimport { SharedModule } from \"../../../shared/shared.module\";\nlet ModalsModule = class ModalsModule {};\nModalsModule = __decorate([NgModule({\n  declarations: [InviteUsersModalComponent, MainModalComponent, UpgradePlanModalComponent, ModalComponent],\n  imports: [CommonModule, InlineSVGModule, RouterModule, NgbModalModule, SharedModule],\n  exports: [InviteUsersModalComponent, MainModalComponent, UpgradePlanModalComponent, ModalComponent]\n})], ModalsModule);\nexport { ModalsModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "InlineSVGModule", "InviteUsersModalComponent", "MainModalComponent", "UpgradePlanModalComponent", "ModalComponent", "NgbModalModule", "SharedModule", "ModalsModule", "__decorate", "declarations", "imports", "exports"], "sources": ["D:\\permittracker\\Angular\\src\\app\\_metronic\\partials\\layout\\modals\\modals.module.ts"], "sourcesContent": ["import {NgModule} from '@angular/core';\nimport {CommonModule} from '@angular/common';\nimport {RouterModule} from '@angular/router';\nimport {InlineSVGModule} from 'ng-inline-svg-2';\nimport {InviteUsersModalComponent} from './invite-users-modal/invite-users-modal.component';\nimport {MainModalComponent} from './main-modal/main-modal.component';\nimport {UpgradePlanModalComponent} from './upgrade-plan-modal/upgrade-plan-modal.component';\nimport {ModalComponent} from './modal/modal.component';\nimport {NgbModalModule} from '@ng-bootstrap/ng-bootstrap';\nimport {SharedModule} from \"../../../shared/shared.module\";\n\n@NgModule({\n  declarations: [\n    InviteUsersModalComponent,\n    MainModalComponent,\n    UpgradePlanModalComponent,\n    ModalComponent,\n  ],\n  imports: [\n    CommonModule,\n    InlineSVGModule,\n    RouterModule,\n    NgbModalModule,\n    SharedModule,\n  ],\n  exports: [\n    InviteUsersModalComponent,\n    MainModalComponent,\n    UpgradePlanModalComponent,\n    ModalComponent,\n  ],\n})\nexport class ModalsModule {\n}\n"], "mappings": ";AAAA,SAAQA,QAAQ,QAAO,eAAe;AACtC,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,eAAe,QAAO,iBAAiB;AAC/C,SAAQC,yBAAyB,QAAO,mDAAmD;AAC3F,SAAQC,kBAAkB,QAAO,mCAAmC;AACpE,SAAQC,yBAAyB,QAAO,mDAAmD;AAC3F,SAAQC,cAAc,QAAO,yBAAyB;AACtD,SAAQC,cAAc,QAAO,4BAA4B;AACzD,SAAQC,YAAY,QAAO,+BAA+B;AAuBnD,IAAMC,YAAY,GAAlB,MAAMA,YAAY,GACxB;AADYA,YAAY,GAAAC,UAAA,EArBxBX,QAAQ,CAAC;EACRY,YAAY,EAAE,CACZR,yBAAyB,EACzBC,kBAAkB,EAClBC,yBAAyB,EACzBC,cAAc,CACf;EACDM,OAAO,EAAE,CACPZ,YAAY,EACZE,eAAe,EACfD,YAAY,EACZM,cAAc,EACdC,YAAY,CACb;EACDK,OAAO,EAAE,CACPV,yBAAyB,EACzBC,kBAAkB,EAClBC,yBAAyB,EACzBC,cAAc;CAEjB,CAAC,C,EACWG,YAAY,CACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}