import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { AppService } from '../../services/app.service';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';

import { HttpUtilsService } from '../../services/http-utils.service';
import { AppSettings } from 'src/app/app.settings';


import * as _ from 'lodash';
import { map } from 'rxjs';
import { UserService } from '../../services/user.service';
import { each } from 'lodash';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';

// import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';



@Component({
  selector: 'app-user-add',
  templateUrl: './user-add.component.html',
  styleUrls: ['./user-add.component.scss']
})
export class AddUserComponent implements OnInit {

    @Input() id: number;
    @Input() defaultPermissions:any;
    @Output() passEntry: EventEmitter<any> = new EventEmitter();
    userForm: FormGroup;
    users:any={};
    loginUser:any={};
    roleArray: any = []
    medicalCentersArray:any=[];
    pharmacyArray:any =[]
    image = '';
    imageUrl = '';
    perNameArray: any = [];//store permission in UI
    permissionArray: any = [];// field to save the initial values of default permissions
    selectedTab: string = 'basic'; //store navigation tab
    IsAutoPassword: boolean = true;
    newPasswordShown = true; //boolean for password shown
    showExternalPassword = false;

  constructor(public modal: NgbActiveModal,
    private cdr: ChangeDetectorRef,
    private httpUtilService: HttpUtilsService,
    private layoutUtilService: CustomLayoutUtilsService,
    private fb: FormBuilder,
    public appService: AppService,
    private userService: UserService,
    ) {

     }

  // Method to handle cancel button click
  onCancelClick(): void {
    // Reset loading state when cancel is clicked
    this.httpUtilService.loadingSubject.next(false);
    this.modal.dismiss();
  }
    //policyForm: FormGroup;

  ngOnInit(): void {
    console.log("Call add client component", this.id)
    this.loginUser = this.appService.getLoggedInUser();
    this.permissionArray = this.defaultPermissions;
    this.loadForm();
    if (this.id !== 0) {
      this.patchForm();
    }
    this.userService.getAllRoles({ paginate: false }).pipe(
      map((data: any) => data as any)).subscribe((data:any) => {
        if (!data.isFault) {
          console.log("Line: 78", data)
          //disable the loading
          this.roleArray = data.responseData.roles;
          console.log('this.roleArray', this.roleArray)
          this.cdr.markForCheck();
        } else {
          this.roleArray = [];
          this.cdr.markForCheck();
        }
      });

    this.cdr.markForCheck();
  }


  loadForm() {
    const formGroup: any = {};
    formGroup['firstname'] = new FormControl('', Validators.compose([Validators.required]));
    formGroup['lastname'] = new FormControl('', Validators.compose([Validators.required]));
    formGroup['email'] = new FormControl('',Validators.compose([Validators.email]));
    formGroup['phone'] = new FormControl('');
    formGroup['password'] = new FormControl('');
    formGroup['IsAuto'] = new FormControl('');
    formGroup['role'] = new FormControl('');
    formGroup['title'] = new FormControl('');
     formGroup['role'] = new FormControl('');
    formGroup['status'] = new FormControl('');
    let pArray: any = [];
    // assign the form fields for Permissions
    this.permissionArray.forEach((perm: any) => {
      pArray.push(perm.Name);
      formGroup[perm.Name] = new FormControl('');
    });
    this.perNameArray = pArray;
    this.userForm = this.fb.group(formGroup);
    // field to display the Permission in UI
    this.perNameArray = pArray;
  }


  //function to modify boolean depending on whether the  password eye symbol is on or off
  newshowpassword(event: any) {
    this.newPasswordShown = event;
  }
  togglePasswordVisibility(): void {
    this.showExternalPassword = !this.showExternalPassword;
  }

  onAutoPasswordChange(event: any) {
    const controls = this.userForm.controls
    this.IsAutoPassword = controls.IsAuto.value;
    if (this.IsAutoPassword == false) {
      this.userForm.get('password')?.setValidators(Validators.compose([Validators.required]));
      this.userForm.get('password')?.updateValueAndValidity();
      this.userForm.controls['password'].enable()
    }
    else {
      this.userForm.controls['password'].disable()
    }

  }

  patchForm(){
    this.httpUtilService.loadingSubject.next(true);
    console.log("Call patch form component line: 151" , this.id)
    this.userService.getUser({ userId: this.id,loggedInUserId :this.loginUser.userId  }).subscribe({
      next: (user: any) => {
        console.log("Line: 153", user.responseData.data); // <-- This should now print

        this.httpUtilService.loadingSubject.next(false);

        if (!user.isFault) {
          const userData = user.responseData;
          let rPerms = JSON.parse(user.responseData.rolePermissions );
          this.image = user.responseData.imageName
          this.imageUrl = this.appService.ImageUrl(this.image)
          this.userForm.patchValue({
            firstname: userData.firstName,
            lastname: userData.lastName,
            phone: userData.phoneNo?.replace(/[()\-\sExt.]/g, '') || '',
            status: userData.userStatus === 'Active' ? 'true' : 'false',
            email: userData.email,
            title: userData.title,
            role: userData.roleId,
            pharmacyId:userData.pharmacyId,
            medicalCenterId:userData.medicalCenterId
          });

              // patch the permission values
          let self: any = this;
          each(rPerms, (r:any) => {
            _.forEach(r, function (value, key) {
              self.userForm.patchValue({
                [key]: value,
              });

            });
          });
        } else {
          console.warn("User response has isFault = true", user.responseData);
        }
      },
      error: (err) => {
        this.httpUtilService.loadingSubject.next(false);
        console.error("API call failed", err);
      }
    });

  }


  //function to browse imgae file
  browseFile(event: any) {
    const fSize = Math.round(event.target.files[0].size / 1024);
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.readAsDataURL(file);
    let selectedImage = file.name;
    const fileData = new FormData();
    fileData.append('files', file)
    if (fSize > 500) {
      this.layoutUtilService.showError(selectedImage + ' - File size upto 500KB.Couldnt upload the files', '');
    } else {
      this.userService.uploadImage(fileData).subscribe((res: any) => {
        if (!res.isFault) {
          this.image = res.responseData.fileName;
          this.imageUrl = AppSettings.IMAGEPATH + this.image;
          this.cdr.markForCheck();
        }
      });
    }
  }

  controlHasError(validation: any, controlName: string | number): boolean {
    const control = this.userForm.controls[controlName];
    if (!control) {
      return false;
    }
    let result = control.hasError(validation) && (control.dirty || control.touched);
    return result;
  }

  save(){
    let controls = this.userForm.controls
    if (this.userForm.invalid) {
      Object.keys(controls).forEach(controlName =>
        controls[controlName].markAsTouched()
      );
      //this.layoutUtilService.showError('Please fill all required fields', '')
      return;
    }
    let userData: any = this.prepareuser();


    console.log("Line: 203", userData)

    if (this.id === 0) {
      this.create(userData);

    } else {
      this.edit(userData);

    }

  }



   // API to update the user details based on the userid
   edit(userData: any) {
    this.httpUtilService.loadingSubject.next(true);
    this.userService.updateUser(userData).subscribe(res => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.layoutUtilService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true)
        this.modal.close()
      } else {
        this.layoutUtilService.showError(res.responseData.message, '');
        this.passEntry.emit(false)
      }
    });
  }
  // API to save new user details
  create(userData: any) {
    this.httpUtilService.loadingSubject.next(true);
    this.userService.createUser(userData).subscribe((res: any) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.layoutUtilService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true)
        this.modal.close()
      } else {
        this.layoutUtilService.showError(res.responseData.message, '');
        this.passEntry.emit(false)
      }
    });
  }



  //function to map input form fields to API fields
  prepareuser() {
    const formData = this.userForm.value;
    this.users.firstName = formData.firstname;
    this.users.lastName = formData.lastname;
    this.users.phoneNo = formData.phone !== '' ? this.appService.getPhoneFormat(formData.phone) : ''
    this.users.userStatus = formData.status === 'true' ? 'Active' : 'Inactive';
    this.users.roleId = formData.role;
    this.users.title = formData.title;
    this.users.password = formData.password;
    this.users.isAuto = formData.IsAuto;
    this.users.imageName = this.image;
    this.users.loggedInUserId = this.loginUser.userId;
    this.users.userId = this.id
    this.users.email = formData.email;
    this.users.isEmailNotificationEnabled = true;
    this.users.pharmacyId = formData.pharmacyId;
    this.users.medicalCenterId = formData.medicalCenterId;
    // let controls = this.userForm.controls;
    // let perArray: any = [];
    // let self: any = this;
    // _.forEach(controls, function (value, key) {
    //   let rjson = _.find(self.permissionArray, function (o) {
    //     return o.Name === key;
    //   });
    //   if (rjson !== undefined) {
    //     let permissionJson = self.getPermissionJson(rjson, value);
    //     perArray.push(perm+issionJson);
    //   }
    // });
    // this.users.permissions = perArray;
    return this.users;
  }


  //function to navigate tab
  showTab(tab: any, $event: any) {
    this.selectedTab = tab;
    this.cdr.markForCheck();
  }

  //function to change permission based on role name
  changeRoleAccess(event: any) {
    console.log('event ', event)

    this.httpUtilService.loadingSubject.next(true);
    this.userService.getRole({ roleId: event.roleId }).subscribe((role: any) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!role.isFault) {
        let rPerms = JSON.parse(role.responseData.rolePermissions);
        // patch the permission values
        let self: any = this;
        each(rPerms, (r) => {
          _.forEach(r, function (value, key) {
            console.log('value ', value )
                   console.log('key ', key )
            self.userForm.patchValue({
              [key]: value,
            });
          });
        })
        console.log('form data ', self.userForm)
        this.cdr.markForCheck();
        this.cdr.detectChanges();
      }
    });
  }
  changePharmacy(event:any){

  }
  changeMedicalCenter(event:any){

  }
}
