{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport * as _ from 'lodash';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class HttpUtilsService {\n  loadingSubject = new BehaviorSubject(null);\n  isDrawerOpenSubject = new BehaviorSubject(false);\n  isDrawerOpen$ = this.isDrawerOpenSubject.asObservable();\n  // Track active loading operations\n  activeLoadingOperations = new Set();\n  loadingOperationCounter = 0;\n  toggleDrawerState() {\n    this.isDrawerOpenSubject.next(!this.isDrawerOpenSubject.value);\n  }\n  // Enhanced loading state management\n  startLoading(operationId) {\n    const id = operationId || `loading_${++this.loadingOperationCounter}`;\n    this.activeLoadingOperations.add(id);\n    this.updateLoadingState();\n    return id;\n  }\n  stopLoading(operationId) {\n    this.activeLoadingOperations.delete(operationId);\n    this.updateLoadingState();\n  }\n  stopAllLoading() {\n    this.activeLoadingOperations.clear();\n    this.updateLoadingState();\n  }\n  updateLoadingState() {\n    const isLoading = this.activeLoadingOperations.size > 0;\n    this.loadingSubject.next(isLoading);\n  }\n  // Legacy methods for backward compatibility\n  setLoading(loading) {\n    if (loading) {\n      this.startLoading();\n    } else {\n      this.stopAllLoading();\n    }\n  }\n  // Method specifically for modal dismissals to ensure loading stops\n  resetLoadingOnModalDismiss() {\n    console.log('Modal dismissed - resetting all loading states');\n    this.stopAllLoading();\n  }\n  getFindHTTPParams(queryParams) {\n    const params = new HttpParams().set('lastNamefilter', queryParams.filter).set('sortOrder', queryParams.sortOrder).set('sortField', queryParams.sortField).set('pageNumber', queryParams.pageNumber.toString()).set('pageSize', queryParams.pageSize.toString());\n    return params;\n  }\n  getHTTPHeaders() {\n    const result = new HttpHeaders();\n    result.set('Content-Type', 'application/json');\n    return result;\n  }\n  getHttpErrorMessages(error) {\n    let errorMessages = [];\n    if (typeof error.field !== 'undefined' && error.field.length > 0) {\n      _.forEach(error.field, function (fieldError) {\n        if (fieldError.code === 'NotEmpty') {\n          errorMessages.push(fieldError.field + ' ' + fieldError.defaultMessage);\n        } else {\n          errorMessages.push(fieldError.defaultMessage);\n        }\n      });\n      return errorMessages.toString();\n    } else {\n      if (typeof error.status !== 'undefined' && typeof error.message !== 'undefined') {\n        return error.message;\n      }\n      return error;\n    }\n  }\n  static ɵfac = function HttpUtilsService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpUtilsService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: HttpUtilsService,\n    factory: HttpUtilsService.ɵfac,\n    providedIn: 'root' // ✅ This makes it globally available\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "_", "BehaviorSubject", "HttpUtilsService", "loadingSubject", "isDrawerOpenSubject", "isDrawerOpen$", "asObservable", "activeLoadingOperations", "Set", "loadingOperationCounter", "toggleDrawerState", "next", "value", "startLoading", "operationId", "id", "add", "updateLoadingState", "stopLoading", "delete", "stopAllLoading", "clear", "isLoading", "size", "setLoading", "loading", "resetLoadingOnModalDismiss", "console", "log", "getFindHTTPParams", "queryParams", "params", "set", "filter", "sortOrder", "sortField", "pageNumber", "toString", "pageSize", "getHTTPHeaders", "result", "getHttpErrorMessages", "error", "errorMessages", "field", "length", "for<PERSON>ach", "fieldError", "code", "push", "defaultMessage", "status", "message", "factory", "ɵfac", "providedIn"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\services\\http-utils.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {HttpHeaders, HttpParams} from '@angular/common/http';\r\nimport * as _ from 'lodash';\r\nimport {BehaviorSubject} from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root' // ✅ This makes it globally available\r\n})\r\nexport class HttpUtilsService {\r\n\r\n  loadingSubject: BehaviorSubject<any> = new BehaviorSubject(null);\r\n  isDrawerOpenSubject = new BehaviorSubject<boolean>(false);\r\n  isDrawerOpen$ = this.isDrawerOpenSubject.asObservable();\r\n  \r\n  // Track active loading operations\r\n  private activeLoadingOperations = new Set<string>();\r\n  private loadingOperationCounter = 0;\r\n\r\n  toggleDrawerState() {\r\n    this.isDrawerOpenSubject.next(!this.isDrawerOpenSubject.value);\r\n  }\r\n\r\n  // Enhanced loading state management\r\n  startLoading(operationId?: string): string {\r\n    const id = operationId || `loading_${++this.loadingOperationCounter}`;\r\n    this.activeLoadingOperations.add(id);\r\n    this.updateLoadingState();\r\n    return id;\r\n  }\r\n\r\n  stopLoading(operationId: string): void {\r\n    this.activeLoadingOperations.delete(operationId);\r\n    this.updateLoadingState();\r\n  }\r\n\r\n  stopAllLoading(): void {\r\n    this.activeLoadingOperations.clear();\r\n    this.updateLoadingState();\r\n  }\r\n\r\n  private updateLoadingState(): void {\r\n    const isLoading = this.activeLoadingOperations.size > 0;\r\n    this.loadingSubject.next(isLoading);\r\n  }\r\n\r\n  // Legacy methods for backward compatibility\r\n  setLoading(loading: boolean): void {\r\n    if (loading) {\r\n      this.startLoading();\r\n    } else {\r\n      this.stopAllLoading();\r\n    }\r\n  }\r\n\r\n  // Method specifically for modal dismissals to ensure loading stops\r\n  resetLoadingOnModalDismiss(): void {\r\n    console.log('Modal dismissed - resetting all loading states');\r\n    this.stopAllLoading();\r\n  }\r\n  getFindHTTPParams(queryParams:any): HttpParams {\r\n    const params = new HttpParams()\r\n      .set('lastNamefilter', queryParams.filter)\r\n      .set('sortOrder', queryParams.sortOrder)\r\n      .set('sortField', queryParams.sortField)\r\n      .set('pageNumber', queryParams.pageNumber.toString())\r\n      .set('pageSize', queryParams.pageSize.toString());\r\n\r\n    return params;\r\n  }\r\n\r\n  getHTTPHeaders(): HttpHeaders {\r\n    const result = new HttpHeaders();\r\n    result.set('Content-Type', 'application/json');\r\n    return result;\r\n  }\r\n\r\n  getHttpErrorMessages(error: any): any {\r\n    let errorMessages:any = [];\r\n    if (typeof error.field !== 'undefined' && error.field.length > 0) {\r\n      _.forEach(error.field, function(fieldError:any) {\r\n        if (fieldError.code === 'NotEmpty') {\r\n          errorMessages.push(fieldError.field + ' ' + fieldError.defaultMessage);\r\n        } else {\r\n          errorMessages.push(fieldError.defaultMessage);\r\n        }\r\n      });\r\n      return errorMessages.toString();\r\n    } else {\r\n      if (typeof error.status !== 'undefined' && typeof error.message !== 'undefined') {\r\n        return error.message;\r\n      }\r\n      return error;\r\n    }\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,EAAEC,UAAU,QAAO,sBAAsB;AAC5D,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAC3B,SAAQC,eAAe,QAAO,MAAM;;AAKpC,OAAM,MAAOC,gBAAgB;EAE3BC,cAAc,GAAyB,IAAIF,eAAe,CAAC,IAAI,CAAC;EAChEG,mBAAmB,GAAG,IAAIH,eAAe,CAAU,KAAK,CAAC;EACzDI,aAAa,GAAG,IAAI,CAACD,mBAAmB,CAACE,YAAY,EAAE;EAEvD;EACQC,uBAAuB,GAAG,IAAIC,GAAG,EAAU;EAC3CC,uBAAuB,GAAG,CAAC;EAEnCC,iBAAiBA,CAAA;IACf,IAAI,CAACN,mBAAmB,CAACO,IAAI,CAAC,CAAC,IAAI,CAACP,mBAAmB,CAACQ,KAAK,CAAC;EAChE;EAEA;EACAC,YAAYA,CAACC,WAAoB;IAC/B,MAAMC,EAAE,GAAGD,WAAW,IAAI,WAAW,EAAE,IAAI,CAACL,uBAAuB,EAAE;IACrE,IAAI,CAACF,uBAAuB,CAACS,GAAG,CAACD,EAAE,CAAC;IACpC,IAAI,CAACE,kBAAkB,EAAE;IACzB,OAAOF,EAAE;EACX;EAEAG,WAAWA,CAACJ,WAAmB;IAC7B,IAAI,CAACP,uBAAuB,CAACY,MAAM,CAACL,WAAW,CAAC;IAChD,IAAI,CAACG,kBAAkB,EAAE;EAC3B;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAACb,uBAAuB,CAACc,KAAK,EAAE;IACpC,IAAI,CAACJ,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,MAAMK,SAAS,GAAG,IAAI,CAACf,uBAAuB,CAACgB,IAAI,GAAG,CAAC;IACvD,IAAI,CAACpB,cAAc,CAACQ,IAAI,CAACW,SAAS,CAAC;EACrC;EAEA;EACAE,UAAUA,CAACC,OAAgB;IACzB,IAAIA,OAAO,EAAE;MACX,IAAI,CAACZ,YAAY,EAAE;IACrB,CAAC,MAAM;MACL,IAAI,CAACO,cAAc,EAAE;IACvB;EACF;EAEA;EACAM,0BAA0BA,CAAA;IACxBC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACR,cAAc,EAAE;EACvB;EACAS,iBAAiBA,CAACC,WAAe;IAC/B,MAAMC,MAAM,GAAG,IAAIhC,UAAU,EAAE,CAC5BiC,GAAG,CAAC,gBAAgB,EAAEF,WAAW,CAACG,MAAM,CAAC,CACzCD,GAAG,CAAC,WAAW,EAAEF,WAAW,CAACI,SAAS,CAAC,CACvCF,GAAG,CAAC,WAAW,EAAEF,WAAW,CAACK,SAAS,CAAC,CACvCH,GAAG,CAAC,YAAY,EAAEF,WAAW,CAACM,UAAU,CAACC,QAAQ,EAAE,CAAC,CACpDL,GAAG,CAAC,UAAU,EAAEF,WAAW,CAACQ,QAAQ,CAACD,QAAQ,EAAE,CAAC;IAEnD,OAAON,MAAM;EACf;EAEAQ,cAAcA,CAAA;IACZ,MAAMC,MAAM,GAAG,IAAI1C,WAAW,EAAE;IAChC0C,MAAM,CAACR,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAC9C,OAAOQ,MAAM;EACf;EAEAC,oBAAoBA,CAACC,KAAU;IAC7B,IAAIC,aAAa,GAAO,EAAE;IAC1B,IAAI,OAAOD,KAAK,CAACE,KAAK,KAAK,WAAW,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAChE7C,CAAC,CAAC8C,OAAO,CAACJ,KAAK,CAACE,KAAK,EAAE,UAASG,UAAc;QAC5C,IAAIA,UAAU,CAACC,IAAI,KAAK,UAAU,EAAE;UAClCL,aAAa,CAACM,IAAI,CAACF,UAAU,CAACH,KAAK,GAAG,GAAG,GAAGG,UAAU,CAACG,cAAc,CAAC;QACxE,CAAC,MAAM;UACLP,aAAa,CAACM,IAAI,CAACF,UAAU,CAACG,cAAc,CAAC;QAC/C;MACF,CAAC,CAAC;MACF,OAAOP,aAAa,CAACN,QAAQ,EAAE;IACjC,CAAC,MAAM;MACL,IAAI,OAAOK,KAAK,CAACS,MAAM,KAAK,WAAW,IAAI,OAAOT,KAAK,CAACU,OAAO,KAAK,WAAW,EAAE;QAC/E,OAAOV,KAAK,CAACU,OAAO;MACtB;MACA,OAAOV,KAAK;IACd;EACF;;qCArFWxC,gBAAgB;EAAA;;WAAhBA,gBAAgB;IAAAmD,OAAA,EAAhBnD,gBAAgB,CAAAoD,IAAA;IAAAC,UAAA,EAFf,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}