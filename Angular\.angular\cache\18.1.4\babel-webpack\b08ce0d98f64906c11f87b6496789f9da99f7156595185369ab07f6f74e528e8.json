{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport { ReviewDetailsModalComponent } from '../review-details-modal/review-details-modal.component';\nimport { ResponseModalComponent } from '../response-modal/response-modal.component';\nimport { AddEditInternalReviewComponent } from '../add-edit-internal-review/add-edit-internal-review.component';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport { EditExternalReviewComponent } from '../edit-external-review/edit-external-review.component';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport { autoTable } from 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../../services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/permits.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 8);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_li_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_li_20_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showTab(\"internal\", $event));\n    });\n    i0.ɵɵtext(2, \" Internal Reviews \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r1.selectedTab === \"internal\"));\n  }\n}\nfunction PermitViewComponent_div_2_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editPermit());\n    });\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitViewComponent_div_2_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_26_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadInternalReviewsPdf());\n    });\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_26_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addPopUp());\n    });\n    i0.ɵɵelement(4, \"i\", 35);\n    i0.ɵɵtext(5, \"Add Review \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.auditEntries.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_27_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleAllSubmittals());\n    });\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"span\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_27_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.syncPermits(true));\n    });\n    i0.ɵɵelement(6, \"i\", 40);\n    i0.ɵɵtext(7, \" Sync \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_27_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPortal());\n    });\n    i0.ɵɵelement(9, \"i\", 42);\n    i0.ɵɵtext(10, \" Portal \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.externalSubmittals.length === 0)(\"title\", ctx_r1.areAllSubmittalsExpanded() ? \"Collapse all submittals\" : \"Expand all submittals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.areAllSubmittalsExpanded() ? \"fa-compress-arrows-alt\" : \"fa-expand-arrows-alt\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.areAllSubmittalsExpanded() ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r1.permit == null ? null : ctx_r1.permit.permitEntityID))(\"title\", (ctx_r1.permit == null ? null : ctx_r1.permit.permitEntityID) ? \"Open Portal\" : \"Portal not available - Permit Entity ID required\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 43)(2, \"div\", 44)(3, \"div\", 45)(4, \"label\");\n    i0.ɵɵtext(5, \"Project Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 45)(9, \"label\");\n    i0.ɵɵtext(10, \"Permit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 46);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 45)(14, \"label\");\n    i0.ɵɵtext(15, \"Primary Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 46);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 45)(19, \"label\");\n    i0.ɵɵtext(20, \"Permit Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 47);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 45)(24, \"label\");\n    i0.ɵɵtext(25, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 46);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 45)(29, \"label\");\n    i0.ɵɵtext(30, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 46);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 45)(34, \"label\");\n    i0.ɵɵtext(35, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 46);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\", 48);\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 45)(43, \"label\");\n    i0.ɵɵtext(44, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"span\", 46);\n    i0.ɵɵtext(46);\n    i0.ɵɵpipe(47, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 45)(49, \"label\");\n    i0.ɵɵtext(50, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 46);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 45)(55, \"label\");\n    i0.ɵɵtext(56, \"Complete Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 46);\n    i0.ɵɵtext(58);\n    i0.ɵɵpipe(59, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 45)(61, \"label\");\n    i0.ɵɵtext(62, \"internal Review Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"span\", 47);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(65, \"div\", 49)(66, \"div\", 10)(67, \"h4\");\n    i0.ɵɵtext(68, \"Notes / Action\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_Template_button_click_69_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      const notesActionsTemplate_r8 = i0.ɵɵreference(4);\n      return i0.ɵɵresetView(ctx_r1.onEdit(notesActionsTemplate_r8));\n    });\n    i0.ɵɵelement(70, \"i\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 43)(72, \"div\", 51)(73, \"div\", 52)(74, \"label\");\n    i0.ɵɵtext(75, \"Attention Reason\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"span\", 46);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 52)(79, \"label\");\n    i0.ɵɵtext(80, \"Internal Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"span\", 46);\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 52)(84, \"label\");\n    i0.ɵɵtext(85, \"Action Taken\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"span\", 46);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.projectName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitType || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.primaryContact || \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.permit.permitStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitStatus || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.location || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitCategory || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitIssueDate ? i0.ɵɵpipeBind2(38, 17, ctx_r1.permit.permitIssueDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Applied on \", ctx_r1.permit.permitAppliedDate ? i0.ɵɵpipeBind2(41, 20, ctx_r1.permit.permitAppliedDate, \"MM/dd/yyyy\") : \"\", \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitExpirationDate ? i0.ɵɵpipeBind2(47, 23, ctx_r1.permit.permitExpirationDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitFinalDate ? i0.ɵɵpipeBind2(53, 26, ctx_r1.permit.permitFinalDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitCompleteDate ? i0.ɵɵpipeBind2(59, 29, ctx_r1.permit.permitCompleteDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.permit.internalReviewStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.permit.internalReviewStatus || \"\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.attentionReason || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.internalNotes || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.actionTaken || \"N/A\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56);\n    i0.ɵɵelement(2, \"i\", 57);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No internal reviews found for this permit.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 61);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template_tr_click_0_listener() {\n      const i_r10 = i0.ɵɵrestoreView(_r9).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectAudit(i_r10));\n    });\n    i0.ɵɵelementStart(1, \"td\", 62)(2, \"div\", 63)(3, \"h6\", 64);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 65);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 66)(8, \"small\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"td\", 68)(11, \"div\", 69)(12, \"div\", 70)(13, \"small\", 71);\n    i0.ɵɵtext(14, \"Reviewed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 72);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 70)(19, \"small\", 71);\n    i0.ɵɵtext(20, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 72);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 70)(25, \"small\", 71);\n    i0.ɵɵtext(26, \"Reviewer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 72);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"td\", 73)(30, \"i\", 74);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template_i_click_30_listener($event) {\n      const i_r10 = i0.ɵɵrestoreView(_r9).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      ctx_r1.editInternalReview(i_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const audit_r11 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"table-active\", ctx_r1.selectedAuditIndex === i_r10);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(audit_r11.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(audit_r11.internalVerificationStatus))(\"ngStyle\", ctx_r1.getStatusStyle(audit_r11.internalVerificationStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", audit_r11.internalVerificationStatus || \"Pending\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(audit_r11.typeCodeDrawing || \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(audit_r11.reviewedDate ? i0.ɵɵpipeBind2(17, 10, audit_r11.reviewedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(audit_r11.completedDate ? i0.ɵɵpipeBind2(23, 13, audit_r11.completedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(audit_r11.internalReviewer || \"\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_30_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"table\", 59)(2, \"tbody\");\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template, 31, 16, \"tr\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.auditEntries);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitViewComponent_div_2_ng_container_30_div_1_Template, 5, 0, \"div\", 53)(2, PermitViewComponent_div_2_ng_container_30_div_2_Template, 4, 1, \"div\", 54);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.auditEntries.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.auditEntries.length > 0);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 78)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\", 79);\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.fetchExternalReviews());\n    });\n    i0.ɵɵelement(7, \"i\", 81);\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.reviewsError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 55)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\", 82);\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"No external reviews found for this permit.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const correction_r18 = i0.ɵɵnextContext().$implicit;\n      const review_r16 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.openResponseModal(correction_r18, review_r16));\n    });\n    i0.ɵɵtext(1, \" Respond \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const correction_r18 = i0.ɵɵnextContext().$implicit;\n      const review_r16 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.openResponseModal(correction_r18, review_r16));\n    });\n    i0.ɵɵtext(1, \" Edit Response \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 140);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.Response, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" EOR / AOR / Owner Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 141);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.EORAOROwner_Response, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" Comment Responded By \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.commentResponsedBy, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 143);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 120)(2, \"div\", 121);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 122)(5, \"div\", 123)(6, \"div\", 124)(7, \"span\", 125);\n    i0.ɵɵtext(8, \"Correction Type: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 126);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 124)(12, \"span\", 125);\n    i0.ɵɵtext(13, \"Category: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 126);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 124)(17, \"span\", 125);\n    i0.ɵɵtext(18, \"Resolved: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 127);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 128);\n    i0.ɵɵtemplate(23, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template, 2, 1, \"button\", 129)(24, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template, 2, 1, \"button\", 130);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 131)(26, \"div\", 132)(27, \"label\", 133);\n    i0.ɵɵtext(28, \" Corrective Action \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 134);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 132)(32, \"label\", 133);\n    i0.ɵɵtext(33, \" Comment \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 135);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_36_Template, 5, 1, \"div\", 136)(37, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_37_Template, 5, 1, \"div\", 136)(38, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_38_Template, 5, 1, \"div\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_39_Template, 1, 0, \"div\", 137);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const correction_r18 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    const review_r16 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r20 + 1);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(correction_r18.CorrectionTypeName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r18.CorrectionCategoryName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r18.ResolvedDate ? i0.ɵɵpipeBind2(21, 12, correction_r18.ResolvedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !correction_r18.EORAOROwner_Response && !correction_r18.commentResponsedBy && ctx_r1.shouldShowEditResponseButton(correction_r18));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (correction_r18.EORAOROwner_Response || correction_r18.commentResponsedBy) && ctx_r1.shouldShowEditResponseButton(correction_r18));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.CorrectiveAction || \"N/A\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.Comments || \"No comment provided\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r18.Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r18.EORAOROwner_Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r18.commentResponsedBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r20 < review_r16.corrections.length - 1);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"h6\", 117);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_Template, 40, 15, \"div\", 118);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const review_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Corrections (\", review_r16.corrections.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", review_r16.corrections);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"h6\", 117);\n    i0.ɵɵtext(2, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 145)(4, \"div\", 146);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const review_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(review_r16.comments);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"div\", 148);\n    i0.ɵɵelement(2, \"i\", 149);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \" No corrections or comments available for this review.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template_div_click_1_listener() {\n      const review_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.toggleReviewAccordion(review_r16.commentsId));\n    });\n    i0.ɵɵelementStart(2, \"div\", 99);\n    i0.ɵɵelement(3, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h6\", 100);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 101)(7, \"span\", 102);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 103)(10, \"span\", 104);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 105)(13, \"span\", 106);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 107)(17, \"span\", 108);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 109)(21, \"i\", 110);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template_i_click_21_listener($event) {\n      const review_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      ctx_r1.downloadReviewPDF(review_r16);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 111)(23, \"div\", 112);\n    i0.ɵɵtemplate(24, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_Template, 4, 2, \"div\", 113)(25, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_25_Template, 6, 1, \"div\", 114)(26, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_26_Template, 5, 0, \"div\", 115);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const review_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isReviewExpanded(review_r16.commentsId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isReviewExpanded(review_r16.commentsId) ? \"fa-chevron-down\" : \"fa-chevron-right\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", review_r16.FailureFlag ? \"red\" : \"green\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", review_r16.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(review_r16.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", review_r16.status || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(review_r16.reviewer || \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", review_r16.dueDate ? \"Due: \" + i0.ɵɵpipeBind2(15, 16, review_r16.dueDate, \"MM/dd/yyyy\") : \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", review_r16.completedDate ? \"Completed: \" + i0.ɵɵpipeBind2(19, 19, review_r16.completedDate, \"MM/dd/yyyy\") : \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"d-none\", !ctx_r1.isReviewExpanded(review_r16.commentsId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (review_r16 == null ? null : review_r16.corrections) && review_r16.corrections.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(review_r16 == null ? null : review_r16.corrections) || review_r16.corrections.length === 0) && (review_r16 == null ? null : review_r16.comments));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(review_r16 == null ? null : review_r16.corrections) || review_r16.corrections.length === 0) && !(review_r16 == null ? null : review_r16.comments));\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\", 87);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template_tr_click_1_listener() {\n      const i_r14 = i0.ɵɵrestoreView(_r13).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.toggleSubmittalAccordion(i_r14));\n    });\n    i0.ɵɵelementStart(2, \"td\", 88)(3, \"div\", 63)(4, \"div\", 89);\n    i0.ɵɵelement(5, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h6\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 90);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"td\", 91)(11, \"div\", 69)(12, \"div\", 70)(13, \"small\", 71);\n    i0.ɵɵtext(14, \"Due\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 72);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 70)(19, \"small\", 71);\n    i0.ɵɵtext(20, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 72);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"tr\", 92)(25, \"td\", 93)(26, \"div\", 94)(27, \"div\", 95);\n    i0.ɵɵtemplate(28, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template, 27, 22, \"div\", 96);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const sub_r21 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isSubmittalExpanded(i_r14));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isSubmittalExpanded(i_r14) ? \"fa-chevron-down\" : \"fa-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sub_r21.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(sub_r21.submittalStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", sub_r21.submittalStatus, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 11, sub_r21.dueDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 14, sub_r21.receivedDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"d-none\", !ctx_r1.isSubmittalExpanded(i_r14));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getExternalReviewsForSubmittal(sub_r21.id));\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84)(2, \"table\", 85)(3, \"tbody\");\n    i0.ɵɵtemplate(4, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template, 29, 17, \"ng-container\", 86);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.externalSubmittals);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 75)(2, \"div\", 9);\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_31_div_3_Template, 9, 2, \"div\", 76)(4, PermitViewComponent_div_2_ng_container_31_div_4_Template, 6, 0, \"div\", 76)(5, PermitViewComponent_div_2_ng_container_31_div_5_Template, 5, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.reviewsError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reviewsError && ctx_r1.externalSubmittals.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reviewsError && ctx_r1.externalSubmittals.length > 0);\n  }\n}\nfunction PermitViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"div\", 12)(4, \"div\", 13)(5, \"span\", 14);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 17)(12, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(13, \"i\", 19);\n    i0.ɵɵtext(14, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"ul\", 21)(17, \"li\", 22)(18, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_a_click_18_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(19, \" Permit Details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, PermitViewComponent_div_2_li_20_Template, 3, 3, \"li\", 24);\n    i0.ɵɵelementStart(21, \"li\", 22)(22, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_a_click_22_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"external\", $event));\n    });\n    i0.ɵɵtext(23, \" External Reviews \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 25);\n    i0.ɵɵtemplate(25, PermitViewComponent_div_2_button_25_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, PermitViewComponent_div_2_div_26_Template, 6, 2, \"div\", 27)(27, PermitViewComponent_div_2_div_27_Template, 11, 7, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 28);\n    i0.ɵɵtemplate(29, PermitViewComponent_div_2_ng_container_29_Template, 88, 32, \"ng-container\", 29)(30, PermitViewComponent_div_2_ng_container_30_Template, 3, 2, \"ng-container\", 29)(31, PermitViewComponent_div_2_ng_container_31_Template, 6, 3, \"ng-container\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Permit #: \", ctx_r1.permit.permitNumber || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitReviewType || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.permit.permitName || \"\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ctx_r1.selectedTab === \"external\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"details\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"internal\" && ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"external\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.permit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"internal\" && ctx_r1.permit && ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"external\" && ctx_r1.permit);\n  }\n}\nfunction PermitViewComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 150)(1, \"div\", 151)(2, \"div\", 152);\n    i0.ɵɵelementContainerStart(3);\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5, \"Edit Notes/Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 153)(7, \"i\", 154);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_i_click_7_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closModal());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 155)(9, \"form\", 156)(10, \"div\", 157)(11, \"div\", 158)(12, \"div\", 159)(13, \"label\", 160);\n    i0.ɵɵtext(14, \" Attention Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"textarea\", 161);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 157)(17, \"div\", 158)(18, \"div\", 159)(19, \"label\", 162);\n    i0.ɵɵtext(20, \" Internal Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 163);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 157)(23, \"div\", 158)(24, \"div\", 159)(25, \"label\", 164);\n    i0.ɵɵtext(26, \" Action Taken \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"textarea\", 165);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(28, \"div\", 166)(29, \"div\")(30, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closModal());\n    });\n    i0.ɵɵtext(31, \" Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \"\\u00A0 \");\n    i0.ɵɵelementStart(33, \"button\", 168);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editNotesandactions());\n    });\n    i0.ɵɵtext(34, \"Update \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.notesForm);\n  }\n}\nexport class PermitViewComponent {\n  route;\n  router;\n  modalService;\n  cdr;\n  fb;\n  appService;\n  customLayoutUtilsService;\n  permitsService;\n  notesForm;\n  permitId = null;\n  permit = null;\n  isLoading = false; // Main page loader\n  auditEntries = [];\n  selectedAuditIndex = 0; // Set to 0 to make first item initially active\n  selectedAuditName = '';\n  selectedAuditStatus = '';\n  selectedTab = 'details';\n  permitReviewType = '';\n  externalReviews = [];\n  reviewsError = '';\n  externalSubmittals = [];\n  selectedExternalSubmittalId = null;\n  internalReviews = [];\n  loginUser = {};\n  isAdmin = false;\n  singlePermit;\n  expandedSubmittals = new Set();\n  expandedReviews = new Set();\n  reviewSelectedTabs = {};\n  routeSubscription = new Subscription();\n  queryParamsSubscription = new Subscription();\n  statusList = [{\n    text: 'Pending',\n    value: 'Pending'\n  }, {\n    text: 'In Progress',\n    value: 'In Progress'\n  }, {\n    text: 'Completed',\n    value: 'Completed'\n  }, {\n    text: 'On Hold',\n    value: 'On Hold'\n  }];\n  // Navigation tracking\n  previousPage = 'permit-list'; // Default fallback\n  projectId = null;\n  constructor(route, router, modalService, cdr, fb,\n  // private modal: NgbActiveModal,\n  appService, customLayoutUtilsService, permitsService) {\n    this.route = route;\n    this.router = router;\n    this.modalService = modalService;\n    this.cdr = cdr;\n    this.fb = fb;\n    this.appService = appService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.permitsService = permitsService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.isAdmin = this.checkIfAdmin();\n    // Read query parameters for navigation tracking\n    this.queryParamsSubscription = this.route.queryParams.subscribe(params => {\n      this.previousPage = params['from'] || 'permit-list';\n      this.projectId = params['projectId'] ? Number(params['projectId']) : null;\n      console.log('Permit view - query params:', {\n        previousPage: this.previousPage,\n        projectId: this.projectId\n      });\n    });\n    // Listen for route parameter changes\n    this.routeSubscription = this.route.paramMap.subscribe(params => {\n      const idParam = params.get('id');\n      this.permitId = idParam ? Number(idParam) : null;\n      if (this.permitId) {\n        this.fetchPermitDetails();\n        this.fetchExternalReviews();\n        this.fetchInternalReviews();\n      }\n    });\n    this.loadForm();\n  }\n  loadForm() {\n    this.notesForm = this.fb.group({\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: ['']\n    });\n    // Trigger change detection to update the view\n    this.cdr.detectChanges();\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n    if (this.queryParamsSubscription) {\n      this.queryParamsSubscription.unsubscribe();\n    }\n  }\n  fetchPermitDetails() {\n    if (!this.permitId) {\n      return;\n    }\n    this.isLoading = true;\n    this.permitsService.getPermit({\n      permitId: this.permitId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Permit API Response:', res);\n        if (!res?.isFault) {\n          this.permit = res.responseData?.data || res.responseData || null;\n          console.log('Permit data assigned:', this.permit);\n          console.log('Permit permitName field:', this.permit?.permitName);\n          console.log('All permit fields:', Object.keys(this.permit || {}));\n          this.permitReviewType = this.permit?.permitReviewType || '';\n          // Default to details tab, user can navigate to reviews as needed\n          this.selectedTab = 'details';\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.permit = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchExternalReviews() {\n    if (!this.permitId) {\n      return;\n    }\n    this.isLoading = true;\n    this.reviewsError = '';\n    this.permitsService.getAllReviews({\n      permitId: this.permitId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault) {\n          this.reviewsError = res.faultMessage || 'Failed to load reviews';\n        } else {\n          const reviews = res.responseData?.reviews || [];\n          this.externalReviews = reviews.map(r => ({\n            commentsId: r.commentsId,\n            name: r.TypeName,\n            reviewer: r.AssignedTo,\n            status: r.StatusText,\n            completedDate: r.CompletedDate ? new Date(r.CompletedDate) : null,\n            dueDate: r.DueDate ? new Date(r.DueDate) : null,\n            receivedDate: r.receivedDate ? new Date(r.receivedDate) : null,\n            comments: r.Comments,\n            corrections: r.Corrections || [],\n            submittalId: r.SubmittalId,\n            FailureFlag: r.FailureFlag,\n            reviewCategory: r.reviewCategory,\n            EORAOROwner_Response: r.EORAOROwner_Response,\n            commentResponsedBy: r.commentResponsedBy\n          }));\n          // Build submittal list grouped from reviews\n          const idToReviews = {};\n          this.externalReviews.forEach(rv => {\n            const key = String(rv.submittalId ?? 'unknown');\n            if (!idToReviews[key]) {\n              idToReviews[key] = [];\n            }\n            idToReviews[key].push(rv);\n          });\n          this.externalSubmittals = Object.keys(idToReviews).map(key => {\n            const items = idToReviews[key];\n            // Determine status priority: Requires Re-submit > Under Review > Approved w/ Conditions > Approved\n            const statusOrder = {\n              'Requires Re-submit': 4,\n              'Under Review': 3,\n              'Approved w/ Conditions': 2,\n              'Approved': 1\n            };\n            const submittalStatus = items.reduce((acc, it) => {\n              const a = statusOrder[acc] || 0;\n              const b = statusOrder[it.status] || 0;\n              return b > a ? it.status : acc;\n            }, '');\n            // Aggregate dates\n            const dueDate = items.reduce((acc, it) => {\n              if (!it.dueDate) {\n                return acc;\n              }\n              if (!acc) {\n                return it.dueDate;\n              }\n              return acc > it.dueDate ? it.dueDate : acc; // earliest due date\n            }, null);\n            const completedDate = items.reduce((acc, it) => {\n              if (!it.completedDate) {\n                return acc;\n              }\n              if (!acc) {\n                return it.completedDate;\n              }\n              return acc < it.completedDate ? it.completedDate : acc; // latest completed date\n            }, null);\n            // Get received date from the first item that has it\n            const receivedDate = items.find(it => it.receivedDate)?.receivedDate || items.find(it => it.createdDate)?.createdDate || null;\n            // Get submittal name from the first item (all items in this group have same submittalId)\n            const reviewCategory = items[0]?.reviewCategory || `Submittal ${key}`;\n            return {\n              id: key,\n              title: reviewCategory,\n              submittalStatus: submittalStatus || items[0]?.status || '',\n              receivedDate: receivedDate ? new Date(receivedDate) : null,\n              dueDate: dueDate,\n              completedDate: completedDate\n            };\n          }).sort((a, b) => {\n            // Sort by received date in descending order (latest first)\n            if (!a.receivedDate && !b.receivedDate) return 0;\n            if (!a.receivedDate) return 1;\n            if (!b.receivedDate) return -1;\n            return b.receivedDate.getTime() - a.receivedDate.getTime();\n          });\n          // Select first submittal by default\n          if (this.externalSubmittals.length > 0) {\n            this.selectedExternalSubmittalId = this.externalSubmittals[0].id;\n            this.selectedAuditName = this.externalSubmittals[0].title;\n            this.selectedAuditStatus = this.externalSubmittals[0].submittalStatus;\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        this.reviewsError = 'Failed to load reviews';\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchInternalReviews() {\n    if (!this.permitId) {\n      return;\n    }\n    this.isLoading = true;\n    this.permitsService.getInternalReviews({\n      permitId: this.permitId,\n      take: 50,\n      skip: 0\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault) {\n          console.error('Failed to load internal reviews:', res.faultMessage);\n        } else {\n          this.internalReviews = res.responseData?.data || res.data || [];\n          // Transform internal reviews to match the auditEntries format\n          this.auditEntries = this.internalReviews.map(review => ({\n            commentsId: review.commentsId,\n            title: review.reviewCategory,\n            reviewCategory: review.reviewCategory,\n            // Preserve reviewCategory for edit modal\n            typeCodeDrawing: review.typeCodeDrawing,\n            reviewComments: review.reviewComments,\n            nonComplianceItems: review.nonComplianceItems,\n            aeResponse: review.aeResponse,\n            internalReviewer: review.internalReviewer,\n            internalVerificationStatus: review.internalVerificationStatus,\n            reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n            completedDate: review.completedDate ? new Date(review.completedDate) : null,\n            reviews: [{\n              name: review.reviewCategory,\n              typeCodeDrawing: review.typeCodeDrawing,\n              reviewComments: review.reviewComments,\n              nonComplianceItems: review.nonComplianceItems,\n              aeResponse: review.aeResponse,\n              internalReviewer: review.internalReviewer,\n              internalVerificationStatus: review.internalVerificationStatus,\n              reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n              completedDate: review.completedDate ? new Date(review.completedDate) : null\n            }]\n          }));\n          // Select first internal review by default\n          if (this.auditEntries.length > 0) {\n            this.selectedAuditIndex = 0;\n            this.selectedAuditName = this.auditEntries[0].title;\n            this.selectedAuditStatus = this.auditEntries[0].internalVerificationStatus || 'Pending';\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error loading internal reviews:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  downloadInternalReviewsPdf() {\n    if (!this.internalReviews || this.internalReviews.length === 0) {\n      return;\n    }\n    const grouped = {};\n    this.internalReviews.forEach(r => {\n      const key = r.reviewCategory || 'Uncategorized';\n      if (!grouped[key]) {\n        grouped[key] = [];\n      }\n      grouped[key].push(r);\n    });\n    const doc = new jsPDF({\n      orientation: 'portrait',\n      unit: 'pt',\n      format: 'a4'\n    });\n    const pageWidth = doc.internal.pageSize.getWidth();\n    // Use more horizontal space: reduce side margins to ~0.5 inch (36pt)\n    const margin = {\n      left: 36,\n      right: 36,\n      top: 40\n    };\n    let y = margin.top;\n    const addCategory = (category, items) => {\n      // Category block title (centered, uppercase)\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`${category.toUpperCase()} REVIEW COMMENTS`, pageWidth / 2, y, {\n        align: 'center'\n      });\n      y += 12;\n      // Reviewer line (take distinct non-empty names, join with comma)\n      const reviewers = Array.from(new Set(items.map(it => (it.internalReviewer || '').toString().trim()).filter(v => v)));\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Reviewer: ${reviewers.join(', ') || 'N/A'}`, pageWidth / 2, y, {\n        align: 'center'\n      });\n      y += 12;\n      // Dates line (Reviewed Date / Responses Date)\n      const reviewedDate = items.find(it => it.reviewedDate)?.reviewedDate;\n      const responsesDate = items.find(it => it.completedDate)?.completedDate;\n      doc.setFont('helvetica', 'italic');\n      doc.setFontSize(9);\n      doc.text(`Reviewed Date: ${reviewedDate ? new Date(reviewedDate).toLocaleDateString() : ''}`, margin.left, y);\n      doc.text(`Responses Date: ${responsesDate ? new Date(responsesDate).toLocaleDateString() : ''}`, pageWidth - margin.right, y, {\n        align: 'right'\n      });\n      y += 6;\n      const rows = items.map((it, idx) => [(idx + 1).toString(), it.typeCodeDrawing || '', (it.reviewComments || '').toString(), (it.aeResponse || '').toString(), it.internalVerificationStatus || '']);\n      autoTable(doc, {\n        startY: y + 5,\n        head: [['#', 'Drawing #', 'Review Comments', 'A/E Response', 'Status']],\n        body: rows,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 8,\n          cellPadding: 5,\n          valign: 'top'\n        },\n        headStyles: {\n          fillColor: [33, 150, 243],\n          textColor: 255,\n          halign: 'center',\n          fontSize: 9\n        },\n        // Fit exactly into available width (pageWidth - margins)\n        // A4 width ~595pt; with 36pt margins each side → 523pt content width\n        columnStyles: {\n          0: {\n            cellWidth: 24,\n            halign: 'center'\n          },\n          // #\n          1: {\n            cellWidth: 55\n          },\n          // Drawing # (even narrower)\n          2: {\n            cellWidth: 198\n          },\n          // Review Comments (half of remaining)\n          3: {\n            cellWidth: 197\n          },\n          // A/E Response (other half)\n          4: {\n            cellWidth: 49\n          } // Verification Status (fits)\n        },\n        theme: 'grid'\n      });\n      // update y for next section\n      // @ts-ignore\n      y = doc.lastAutoTable.finalY + 20;\n      // add page if needed\n      if (y > doc.internal.pageSize.getHeight() - 100) {\n        doc.addPage();\n        y = margin.top;\n      }\n    };\n    Object.keys(grouped).forEach((category, idx) => {\n      if (idx > 0 && y > margin.top + 10) {\n        // add a small divider between categories on same page\n        doc.setDrawColor(230);\n        doc.line(margin.left, y - 10, pageWidth - margin.right, y - 10);\n      }\n      addCategory(category, grouped[category]);\n    });\n    const fileName = `Internal_Reviews_${this.permit?.permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  }\n  selectExternalSubmittal(id) {\n    this.selectedExternalSubmittalId = id;\n    const sel = this.externalSubmittals.find(s => String(s.id) === String(id));\n    if (sel) {\n      this.selectedAuditName = sel.title;\n      this.selectedAuditStatus = sel.submittalStatus;\n    }\n  }\n  getExternalReviewsForSelectedSubmittal() {\n    if (!this.selectedExternalSubmittalId) {\n      return [];\n    }\n    return this.getExternalReviewsForSubmittal(this.selectedExternalSubmittalId);\n  }\n  getExternalReviewsForSubmittal(submittalId) {\n    const reviews = this.externalReviews.filter(r => String(r.submittalId) === String(submittalId));\n    // Sort by FailureFlag: false reviews first (desc), then true reviews (desc)\n    return reviews.sort((a, b) => {\n      // If both have the same FailureFlag value, maintain original order (reverse for desc)\n      if (a.FailureFlag === b.FailureFlag) {\n        return 0;\n      }\n      // False reviews (FailureFlag = false) come first\n      if (!a.FailureFlag && b.FailureFlag) {\n        return -1;\n      }\n      // True reviews (FailureFlag = true) come after false reviews\n      if (a.FailureFlag && !b.FailureFlag) {\n        return 1;\n      }\n      return 0;\n    }).reverse(); // Reverse to get descending order within each group\n  }\n  toggleSubmittalAccordion(index) {\n    if (this.expandedSubmittals.has(index)) {\n      this.expandedSubmittals.delete(index);\n    } else {\n      this.expandedSubmittals.add(index);\n    }\n  }\n  isSubmittalExpanded(index) {\n    return this.expandedSubmittals.has(index);\n  }\n  areAllSubmittalsExpanded() {\n    return this.externalSubmittals && this.externalSubmittals.length > 0 && this.expandedSubmittals.size === this.externalSubmittals.length;\n  }\n  toggleAllSubmittals() {\n    if (!this.externalSubmittals || this.externalSubmittals.length === 0) {\n      return;\n    }\n    if (this.areAllSubmittalsExpanded()) {\n      this.expandedSubmittals.clear();\n    } else {\n      this.expandedSubmittals = new Set(this.externalSubmittals.map((_, idx) => idx));\n    }\n    this.cdr.markForCheck();\n  }\n  toggleReviewAccordion(reviewId) {\n    if (this.expandedReviews.has(reviewId)) {\n      this.expandedReviews.delete(reviewId);\n    } else {\n      this.expandedReviews.add(reviewId);\n      // Set default tab for this review if not already set\n      if (!this.reviewSelectedTabs[reviewId]) {\n        this.reviewSelectedTabs[reviewId] = 'corrections';\n      }\n    }\n  }\n  isReviewExpanded(reviewId) {\n    return this.expandedReviews.has(reviewId);\n  }\n  showReviewTab(reviewId, tab, $event) {\n    $event.stopPropagation();\n    this.reviewSelectedTabs[reviewId] = tab;\n    this.cdr.markForCheck();\n  }\n  updateReviewResponse(review) {\n    this.isLoading = true;\n    const formData = {\n      EORAOROwner_Response: review.EORAOROwner_Response,\n      commentResponsedBy: review.commentResponsedBy,\n      permitId: this.permitId,\n      commentsId: review.commentsId,\n      loggedInUserId: this.loginUser.userId\n    };\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          // Refresh external reviews to get updated data\n          this.fetchExternalReviews();\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage || '❌ Error syncing permit', '');\n          //alert(res.faultMessage || 'Failed to update review response');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('❌ Error updating review response', '');\n        //alert('Error updating review response');\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  isSelectedSubmittal(submittalId) {\n    return String(this.selectedExternalSubmittalId) === String(submittalId);\n  }\n  selectAudit(index) {\n    this.selectedAuditIndex = index;\n    this.selectedAuditName = this.auditEntries[this.selectedAuditIndex].title;\n    this.selectedAuditStatus = this.auditEntries[this.selectedAuditIndex].submittalStatus;\n  }\n  getReviewsForSelectedAudit() {\n    if (this.selectedAuditIndex === null || this.selectedAuditIndex >= this.auditEntries.length) {\n      return [];\n    }\n    return this.auditEntries[this.selectedAuditIndex].reviews || [];\n  }\n  goBack() {\n    console.log('goBack called - previousPage:', this.previousPage, 'projectId:', this.projectId);\n    if (this.previousPage === 'project' && this.projectId) {\n      // Navigate back to the specific project view with permits tab active\n      console.log('Navigating to project view with permits tab active');\n      this.router.navigate(['/projects/view', this.projectId], {\n        queryParams: {\n          activeTab: 'permits'\n        }\n      });\n    } else {\n      // Default to permit list\n      console.log('Navigating to permit list');\n      this.router.navigate(['/permits/list']);\n    }\n  }\n  goToPortal() {\n    window.open(`${this.permit.cityReviewLink + this.permit.permitEntityID}`, '_blank');\n  }\n  openReviewDetails(review) {\n    const modalRef = this.modalService.open(ReviewDetailsModalComponent, {\n      size: 'lg'\n    });\n    modalRef.componentInstance.review = review;\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.permitDetails = this.permit;\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchExternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    console.log('Status mapping - Original:', status, 'Normalized:', normalized);\n    // Map common synonyms from API to a single class we style\n    const aliasMap = {\n      'in-review': 'in-review',\n      'requires-re-submit': 'requires-re-submit',\n      'approved-w-conditions': 'approved-w-conditions',\n      'in-progress': 'in-progress',\n      'completed': 'completed',\n      'verified': 'verified',\n      'pending': 'pending',\n      'rejected': 'rejected',\n      'approved': 'approved',\n      'under-review': 'under-review',\n      'requires-resubmit': 'requires-resubmit',\n      'pacifica-verification': 'pacifica-verification',\n      'dis-approved': 'dis-approved',\n      'not-required': 'not-required',\n      '1-cycle-completed': '1-cycle-completed',\n      '1 cycle completed': '1-cycle-completed',\n      'cycle completed': '1-cycle-completed'\n    };\n    const resolved = aliasMap[normalized] || normalized;\n    const finalClass = 'status-' + resolved;\n    console.log('Final status class:', finalClass);\n    console.log('Available CSS classes for debugging:', ['status-pending', 'status-in-progress', 'status-completed', 'status-verified', 'status-rejected', 'status-approved', 'status-under-review', 'status-requires-resubmit', 'status-pacifica-verification', 'status-dis-approved', 'status-not-required', 'status-in-review', 'status-1-cycle-completed']);\n    return finalClass;\n  }\n  getStatusStyle(status) {\n    if (!status) return {};\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    const styleMap = {\n      '1-cycle-completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      '1 cycle completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      'cycle completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      'pacifica-verification': {\n        backgroundColor: '#e1f5fe',\n        color: '#0277bd',\n        border: '1px solid #81d4fa'\n      },\n      'dis-approved': {\n        backgroundColor: '#ffebee',\n        color: '#c62828',\n        border: '1px solid #ffcdd2'\n      },\n      'not-required': {\n        backgroundColor: '#f5f5f5',\n        color: '#757575',\n        border: '1px solid #e0e0e0'\n      },\n      'in-review': {\n        backgroundColor: '#e8eaf6',\n        color: '#3949ab',\n        border: '1px solid #c5cae9'\n      },\n      'pending': {\n        backgroundColor: '#fff3e0',\n        color: '#e65100',\n        border: '1px solid #ffcc02'\n      },\n      'approved': {\n        backgroundColor: '#e8f5e8',\n        color: '#1b5e20',\n        border: '1px solid #c8e6c9'\n      },\n      'completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#1b5e20',\n        border: '1px solid #c8e6c9'\n      },\n      'rejected': {\n        backgroundColor: '#ffebee',\n        color: '#c62828',\n        border: '1px solid #ffcdd2'\n      }\n    };\n    return styleMap[normalized] || {};\n  }\n  showTab(tab, $event) {\n    if (tab === 'internal' && !this.isInternalReviewEnabled()) {\n      return;\n    }\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  isInternalReviewEnabled() {\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n    // show only when type is 'internal' or 'both'\n    return type === 'internal' || type === 'both';\n  }\n  addPopUp() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddEditInternalReviewComponent\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n    // Pass data to the modal\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchInternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  editInternalReview(reviewIndex) {\n    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false,\n      scrollable: true\n    };\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n    // Pass data to the modal for editing\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.reviewData = this.auditEntries[reviewIndex];\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'updated') {\n        // Refresh internal reviews\n        this.fetchInternalReviews();\n      }\n    }).catch(error => {\n      console.log('Modal dismissed');\n    });\n  }\n  editPopUp() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n  }\n  editPermit() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(PermitPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.permitId;\n    // Listen for passEntry event to refresh permit details when permit is saved\n    modalRef.componentInstance.passEntry.subscribe(saved => {\n      if (saved) {\n        this.fetchPermitDetails();\n      }\n    });\n  }\n  syncPermits(i) {\n    this.isLoading = true;\n    this.singlePermit = i || false;\n    this.permitsService.syncPermits({\n      permitId: this.permitId,\n      singlePermit: this.singlePermit,\n      autoLogin: true\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Sync response:', res);\n        console.log('Response type:', typeof res);\n        console.log('Response keys:', Object.keys(res || {}));\n        console.log('Response success:', res?.success);\n        console.log('Response message:', res?.message);\n        console.log('Response responseData:', res?.responseData);\n        // Handle various response structures\n        let responseData = res;\n        // Check different possible response structures\n        if (res?.responseData) {\n          responseData = res.responseData;\n        } else if (res?.body?.responseData) {\n          responseData = res.body.responseData;\n        } else if (res?.body) {\n          responseData = res.body;\n        }\n        console.log('Final responseData:', responseData);\n        console.log('Final success:', responseData?.success);\n        console.log('Final message:', responseData?.message);\n        if (responseData?.isFault) {\n          //alert(responseData.faultMessage || 'Failed to sync permit');\n          this.customLayoutUtilsService.showError(responseData.faultMessage, '');\n        } else if (responseData?.success === false) {\n          // Handle specific error messages from the API\n          if (responseData.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (responseData.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n          }\n        } else if (responseData?.success === true || responseData?.data) {\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n          //alert('✅ Permit synced successfully');\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n        } else {\n          // Fallback for unknown response structure\n          console.log('Unknown response structure, showing generic success');\n          //alert('✅ Permit synced successfully');\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        // Handle HTTP error responses\n        console.log('Error response:', err);\n        console.log('Error type:', typeof err);\n        console.log('Error keys:', Object.keys(err || {}));\n        console.log('Error status:', err?.status);\n        console.log('Error message:', err?.message);\n        console.log('Error error:', err?.error);\n        // The interceptor passes err.error to the error handler\n        // So err might actually be the response data\n        if (err?.success === false) {\n          // Handle specific error messages from the API\n          if (err.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n          }\n        } else if (err?.error?.message) {\n          if (err.error.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.error.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.error.message}`);\n          }\n        } else if (err?.status === 404) {\n          this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          // Handle 404 specifically for permit not found\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n        } else {\n          //alert('❌ Error syncing permit');\n        }\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  editExternalReview(review) {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddEditInternalReviewComponent\n    const modalRef = this.modalService.open(EditExternalReviewComponent, NgbModalOptions);\n    console.log('reviewData ', review);\n    // Pass data to the modal\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.reviewData = review;\n    modalRef.componentInstance.permitDetails = this.permit;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchExternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  openResponseModal(correction, review) {\n    // Open the modal using NgbModal\n    const modalRef = this.modalService.open(ResponseModalComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false\n    });\n    // Pass data to the modal\n    modalRef.componentInstance.correction = correction;\n    modalRef.componentInstance.review = review;\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;\n    modalRef.componentInstance.isAdmin = this.isAdmin;\n    // Handle modal result\n    modalRef.componentInstance.responseSubmitted.subscribe(formData => {\n      this.submitResponse(formData, modalRef);\n    });\n    // Handle response completion to reset loading state\n    modalRef.componentInstance.responseCompleted.subscribe(success => {\n      if (!success) {\n        // Reset loading state if submission failed\n        modalRef.componentInstance.isLoading = false;\n      }\n    });\n    modalRef.result.then(() => {\n      // Modal was closed\n    }).catch(() => {\n      // Modal was dismissed\n    });\n  }\n  submitResponse(formData, modalRef) {\n    if (!formData) {\n      return;\n    }\n    this.isLoading = true;\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          //alert(res.responseData.message || 'Response submitted successfully');\n          this.fetchExternalReviews(); // Refresh external reviews to get updated data\n          // Emit success completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(true);\n          }\n          // Close the modal if it was passed\n          if (modalRef) {\n            modalRef.close();\n          }\n        } else {\n          //alert(res.faultMessage || 'Failed to submit response');\n          this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to submit response', '');\n          // Emit failure completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(false);\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        //alert('Error submitting response');\n        console.error(err);\n        this.customLayoutUtilsService.showSuccess('Error submitting response', '');\n        // Emit failure completion event\n        if (modalRef && modalRef.componentInstance) {\n          modalRef.componentInstance.responseCompleted.emit(false);\n        }\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  downloadReviewPDF(review) {\n    if (!review) {\n      return;\n    }\n    try {\n      const permitNumber = this.permit?.permitNumber || '';\n      const reviewer = (review?.AssignedTo || review?.municipalityReviewer || review?.reviewer || '').toString();\n      const cityComments = (review?.Comments || (review?.cityComments ?? '')).toString();\n      const ownerResponse = (review?.EORAOROwner_Response ?? '').toString();\n      // Calculate submittal count for this review\n      const submittalCount = this.externalSubmittals ? this.externalSubmittals.length : 0;\n      const cycle = submittalCount > 0 ? submittalCount.toString() : (review?.cycle || review?.Cycle || '').toString();\n      const status = (review?.StatusName || review?.commentstatus || review?.status || '').toString();\n      const displayDate = review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate ? new Date(review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate).toLocaleDateString() : new Date().toLocaleDateString();\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'pt',\n        format: 'a4'\n      });\n      const pageWidth = doc.internal.pageSize.getWidth();\n      const pageHeight = doc.internal.pageSize.getHeight();\n      const margin = {\n        left: 40,\n        right: 40,\n        top: 40,\n        bottom: 40\n      };\n      // Header: Permit Number\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Permit Number: ${permitNumber || 'N/A'}`, margin.left, margin.top);\n      const startY = margin.top + 20;\n      // Table with one row matching the screenshot\n      const headers = ['REVIEWED BY', 'CITY COMMENTS', 'EOR/AOR/OWNER COMMENT RESPONSE', 'CYCLE', 'STATUS', 'DATE'];\n      // Build body rows. If there are corrections, show one row per correction; otherwise single row\n      const rawCorrections = (review && review.Corrections) ?? review.corrections ?? [];\n      const correctionsArray = Array.isArray(rawCorrections) ? rawCorrections : rawCorrections ? [rawCorrections] : [];\n      const bodyRows = correctionsArray.length > 0 ? correctionsArray.map(c => [reviewer || 'N/A', c?.Comments || cityComments || 'N/A', c?.Response || c?.EORAOROwner_Response || ownerResponse || 'N/A', cycle || 'N/A', status || 'N/A', (c?.ResolvedDate ? new Date(c.ResolvedDate).toLocaleDateString() : displayDate) || 'N/A']) : [[reviewer || 'N/A', cityComments || 'N/A', ownerResponse || 'N/A', cycle || 'N/A', status || 'N/A', displayDate || 'N/A']];\n      autoTable(doc, {\n        startY,\n        head: [headers],\n        body: bodyRows,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 8,\n          cellPadding: 5,\n          overflow: 'linebreak'\n        },\n        headStyles: {\n          fillColor: [240, 240, 240],\n          textColor: [0, 0, 0],\n          fontStyle: 'bold',\n          fontSize: 8\n        },\n        columnStyles: {\n          0: {\n            cellWidth: 90\n          },\n          1: {\n            cellWidth: (pageWidth - margin.left - margin.right) * 0.26\n          },\n          2: {\n            cellWidth: (pageWidth - margin.left - margin.right) * 0.24\n          },\n          3: {\n            cellWidth: 45,\n            halign: 'center'\n          },\n          4: {\n            cellWidth: 60,\n            halign: 'center'\n          },\n          5: {\n            cellWidth: 60,\n            halign: 'center'\n          }\n        },\n        didParseCell: data => {\n          // City comments text in red\n          if (data.section === 'body' && data.column.index === 1) {\n            data.cell.styles.textColor = [192, 0, 0];\n          }\n        },\n        didDrawCell: data => {\n          // Fully colored background for Status cell\n          if (data.section === 'body' && data.column.index === 4) {\n            const value = String(data.cell.raw || '');\n            const isApproved = value.toLowerCase() === 'approved';\n            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\n            const textColor = [255, 255, 255];\n            // fill whole cell\n            doc.setFillColor(bg[0], bg[1], bg[2]);\n            doc.rect(data.cell.x + 0.5, data.cell.y + 0.5, data.cell.width - 1, data.cell.height - 1, 'F');\n            // write centered white text\n            doc.setTextColor(textColor[0], textColor[1], textColor[2]);\n            doc.setFont('helvetica', 'bold');\n            doc.setFontSize(8);\n            const textWidth = doc.getTextWidth(value);\n            const textX = data.cell.x + data.cell.width / 2 - textWidth / 2;\n            const textY = data.cell.y + data.cell.height / 2 + 3;\n            doc.text(value, textX, textY);\n            data.cell.text = [];\n          }\n        },\n        theme: 'grid'\n      });\n      // Footer\n      const pageCount = doc.getNumberOfPages();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        doc.setDrawColor(200, 200, 200);\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(8);\n        doc.setTextColor(100, 100, 100);\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n      }\n      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      this.customLayoutUtilsService.showSuccess('Error generation PDF. Please try agaiin', '');\n      //alert('Error generating PDF. Please try again.');\n    }\n  }\n  checkIfAdmin() {\n    // Check if the user is an admin based on roleId\n    // Assuming roleId 1 is admin - adjust this based on your role system\n    return this.loginUser && this.loginUser.roleId === 1;\n  }\n  shouldShowEditResponseButton(correction) {\n    // Show edit response button if:\n    // 1. User is admin (can always edit)\n    // 2. User is not admin but lockResponse is false (unlocked by admin)\n    if (this.isAdmin) {\n      return true;\n    }\n    // For non-admin users, only show if lockResponse is explicitly false\n    return correction.lockResponse === false;\n  }\n  onEdit(template) {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    this.modalService.open(template, NgbModalOptions);\n    // console.log(\"this.permit\", this.permit)\n    this.notesForm.patchValue({\n      actionTaken: this.permit.actionTaken,\n      attentionReason: this.permit.attentionReason,\n      internalNotes: this.permit.internalNotes\n      // actionTaken:'helo',\n    });\n  }\n  closModal() {\n    this.modalService.dismissAll();\n  }\n  editNotesandactions() {\n    this.isLoading = true;\n    const formData = {\n      permitId: this.permitId,\n      actionTaken: this.notesForm.value.actionTaken,\n      internalNotes: this.notesForm.value.internalNotes,\n      attentionReason: this.notesForm.value.attentionReason\n    };\n    this.permitsService.editNotesAndActions(formData).subscribe({\n      next: res => {\n        this.closModal();\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          // Refresh permit details to get updated data from server\n          this.fetchPermitDetails();\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage || '❌ Error in update notes and actions', '');\n          //alert(res.faultMessage || 'Failed to update review response');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('❌ Error in update notes and actions', '');\n        //alert('Error updating review response');\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  static ɵfac = function PermitViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.PermitsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitViewComponent,\n    selectors: [[\"app-permit-view\"]],\n    decls: 5,\n    vars: 2,\n    consts: [[\"notesActionsTemplate\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"permit-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"permit-details-header\"], [1, \"header-content\", \"w-100\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"permit-title\"], [1, \"status-text\", \"status-under-review\"], [1, \"permit-number-line\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\", 2, \"margin-right\", \"16px\"], [\"type\", \"button\", \"class\", \"btn btn-link p-0\", \"title\", \"Edit Permit\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"button-group\", 4, \"ngIf\"], [1, \"card-body\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Edit Permit\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\", 2, \"font-size\", \"1.1rem\"], [\"type\", \"button\", \"title\", \"Download Internal Reviews PDF\", 1, \"btn\", \"btn-link\", \"p-0\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"download-pdf-icon\", 2, \"color\", \"#3699ff\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"bi\", \"bi-plus-lg\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", 3, \"ngClass\"], [1, \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-sync-alt\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"permit-details-content\"], [1, \"permit-details-grid\"], [1, \"permit-detail-item\"], [1, \"permit-value\"], [1, \"status-text\", 3, \"ngClass\"], [1, \"text-gray-500\", \"fs-7\", \"p-0\"], [1, \"permit-details-card\"], [\"type\", \"button\", \"title\", \"Edit Notes/Actions\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"notes-actions-container\"], [1, \"permit-detail-item-full\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-clipboard-list\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [\"class\", \"audit-table-row\", 3, \"table-active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"audit-table-row\", 3, \"click\"], [1, \"audit-title-cell\", \"px-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"mb-0\", \"me-3\"], [1, \"audit-status-badge\", 3, \"ngClass\", \"ngStyle\"], [1, \"mt-1\"], [1, \"text-muted\"], [1, \"audit-dates-cell\", \"px-3\"], [1, \"d-flex\", \"gap-4\"], [1, \"date-item\"], [1, \"text-muted\", \"d-block\"], [1, \"fw-medium\"], [1, \"audit-actions-cell\", \"px-4\"], [\"title\", \"Edit Review\", 1, \"fas\", \"fa-edit\", \"action-icon\", \"edit-icon\", 3, \"click\"], [1, \"external-reviews-card\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"class\", \"card-body p-0\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"fa-3x\", \"mb-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-redo\"], [1, \"fas\", \"fa-external-link-alt\", \"fa-3x\", \"mb-3\"], [1, \"card-body\", \"p-0\"], [1, \"table-responsive\", \"external-reviews-table\"], [1, \"table\", \"table-hover\", \"mb-0\"], [4, \"ngFor\", \"ngForOf\"], [1, \"external-submittal-row\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"submittal-title-cell\", \"px-3\"], [1, \"accordion-toggle\", \"me-2\"], [1, \"submittal-status-badge\", 3, \"ngClass\"], [1, \"submittal-dates-cell\", \"px-3\"], [1, \"accordion-content-row\"], [\"colspan\", \"2\", 1, \"p-0\"], [1, \"accordion-content\"], [1, \"reviews-container\", \"p-3\"], [\"class\", \"review-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"review-item\"], [1, \"review-single-line\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"review-accordion-toggle\", \"me-2\"], [1, \"review-title\"], [1, \"review-status-container\"], [1, \"review-status\", 3, \"ngClass\"], [1, \"reviewer-container\"], [1, \"reviewer\"], [1, \"due-date-container\"], [1, \"due-date\"], [1, \"completed-date-container\"], [1, \"completed-date\"], [1, \"review-actions-container\"], [\"title\", \"Download Review PDF\", 1, \"fas\", \"fa-download\", \"download-pdf-icon\", 3, \"click\"], [1, \"review-details-accordion\"], [1, \"review-details-content\"], [\"class\", \"corrections-section p-3\", \"style\", \"padding-bottom: 0px !important;\", 4, \"ngIf\"], [\"class\", \"comments-section p-3\", 4, \"ngIf\"], [\"class\", \"no-data-section p-3\", 4, \"ngIf\"], [1, \"corrections-section\", \"p-3\", 2, \"padding-bottom\", \"0px !important\"], [1, \"section-title\"], [\"class\", \"correction-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"correction-item\"], [1, \"correction-header\", \"d-flex\", \"align-items-center\"], [1, \"correction-number\"], [1, \"correction-meta\", \"flex-grow-1\", \"ms-3\", \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"meta-fields\", \"d-flex\", \"align-items-center\", \"w-100\"], [1, \"meta-field\", \"flex-fill\"], [1, \"meta-label\", \"fw-bold\"], [1, \"meta-value\"], [1, \"meta-value\", \"resolved-date\"], [1, \"respond-buttons\"], [\"class\", \"btn btn-primary btn-sm me-3\", \"title\", \"Respond to this correction\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary btn-sm me-3\", \"title\", \"Edit response to this correction\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"correction-content\"], [1, \"correction-field\"], [1, \"field-label\"], [1, \"field-content\", \"corrective-action\"], [1, \"field-content\", \"comment\"], [\"class\", \"correction-field\", 4, \"ngIf\"], [\"class\", \"correction-separator\", 4, \"ngIf\"], [\"title\", \"Respond to this correction\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [\"title\", \"Edit response to this correction\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"field-content\", \"response\"], [1, \"field-content\", \"eor-response\"], [1, \"field-content\", \"responded-by\"], [1, \"correction-separator\"], [1, \"comments-section\", \"p-3\"], [1, \"comment-content\"], [1, \"comment-text\"], [1, \"no-data-section\", \"p-3\"], [1, \"no-data-message\", \"text-center\", \"text-muted\"], [1, \"fas\", \"fa-info-circle\"], [1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"medium-modal-body\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [1, \"form-group\"], [\"for\", \"projectName\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"attentionReason\", \"rows\", \"2\", \"formControlName\", \"attentionReason\", \"placeholder\", \"Tyed here...\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"projectDescription\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"internalNotes\", \"rows\", \"3\", \"formControlName\", \"internalNotes\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"internalProjectNo\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"internalNotes\", \"rows\", \"3\", \"formControlName\", \"actionTaken\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [1, \"modal-footer\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n    template: function PermitViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, PermitViewComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2);\n        i0.ɵɵtemplate(2, PermitViewComponent_div_2_Template, 32, 16, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, PermitViewComponent_ng_template_3_Template, 35, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.permit);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i7.NgStyle, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i7.DatePipe],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.permit-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: transparent;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n  margin-left: auto; \\n\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  padding: 0.375rem 0.75rem !important;\\n  line-height: 1.5 !important;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  padding: 0.15rem 0.5rem; \\n\\n  border-radius: 0.55rem;\\n  background-color: #f3f6f9;\\n  color: #3f4254;\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1; \\n\\n  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.02s ease;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #5e6e82;\\n  font-size: 0.75rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover {\\n  background-color: #eef2f7;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);\\n  text-decoration: none;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(1px);\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 2rem; \\n\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover {\\n  background-color: transparent;\\n  color: #3699ff !important;\\n  text-decoration: none;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: color 0.2s ease;\\n  line-height: 1;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #3699ff !important;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  background-color: #f5f5f5 !important;\\n  color: #999 !important;\\n  border-color: #ddd !important;\\n}\\n.btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: #f5f5f5 !important;\\n  color: #999 !important;\\n  border-color: #ddd !important;\\n  box-shadow: none !important;\\n}\\n\\n.permit-details-card[_ngcontent-%COMP%] {\\n  background: #fff;\\n  margin: 1rem 0;\\n  overflow: hidden;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%] {\\n  background: #f5f6f8;\\n  padding: 0.75rem 1.25rem;\\n  border-bottom: 1px solid #e3e6ea;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 2rem; \\n\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover {\\n  background-color: transparent;\\n  color: #3699ff !important;\\n  text-decoration: none;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: color 0.2s ease;\\n  line-height: 1;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #3699ff !important;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%] {\\n  background: #1b7e6c;\\n  color: #fff;\\n  border: none;\\n  border-radius: 6px;\\n  padding: 0.35rem 0.75rem;\\n  font-size: 0.85rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  transition: background 0.2s ease;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%]:hover {\\n  background: #166354;\\n}\\n\\n.permit-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);\\n  margin: 1rem 0;\\n  overflow: hidden;\\n}\\n\\n.permit-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr; \\n\\n  gap: 1.5rem;\\n}\\n\\n.notes-actions-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.4rem;\\n  width: 100%;\\n}\\n.permit-detail-item-full[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  line-height: 1.2;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #6c7293;\\n  text-transform: capitalize;\\n  letter-spacing: 0.1rem;\\n  margin: 0;\\n}\\n.permit-detail-item-full[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  padding: 0.5rem 0;\\n  border-bottom: none;\\n  word-wrap: break-word;\\n  white-space: pre-wrap;\\n}\\n\\n.permit-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.4rem;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  line-height: 1.2;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #111111;\\n  text-transform: capitalize;\\n  letter-spacing: 0.1rem;\\n  margin: 0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%], \\n.permit-detail-item[_ngcontent-%COMP%]   .permit-status[_ngcontent-%COMP%] {\\n  margin-top: 0.1rem;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  padding: 0.5rem 0;\\n  border-bottom: none; \\n\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-status[_ngcontent-%COMP%] {\\n  display: block;\\n  vertical-align: top;\\n  padding: 0.5rem 0; \\n\\n  font-size: 1rem;\\n  font-weight: 600;\\n  text-align: left; \\n\\n  background: transparent; \\n\\n  border: none; \\n\\n  min-width: 0; \\n\\n  border-radius: 0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n\\n.loading-section[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 0.475rem;\\n  padding: 2rem;\\n  text-align: center;\\n  border: 1px solid #e5eaee;\\n}\\n.loading-section[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n.loading-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n  margin: 0;\\n}\\n.loading-section[_ngcontent-%COMP%]   .loading-tips[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #a1a5b7 !important;\\n  font-size: 0.875rem;\\n}\\n.loading-section[_ngcontent-%COMP%]   .loading-tips[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #3699ff;\\n}\\n\\n.no-permit-data[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  border-radius: 0.475rem;\\n  border: 1px solid #e5eaee;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #6c7293;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  padding-left: 1.5rem;\\n  color: #6c7293;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  padding: 0 0.5rem 0 0.5rem;\\n  margin-top: 0;\\n}\\n\\n.external-reviews-table[_ngcontent-%COMP%] {\\n  padding-top: 0 !important;\\n  margin-top: 0 !important;\\n}\\n\\n.card-body[_ngcontent-%COMP%] {\\n  padding-top: 0 !important;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  overflow: hidden;\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border-bottom: 1px solid #e5eaee;\\n  background: #ffffff;\\n  border-radius: 0.5rem;\\n  margin-bottom: 0.5rem;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row[_ngcontent-%COMP%]:hover, \\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row.expanded[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row.expanded[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border-color: #2196f3;\\n  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row.table-active[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row.table-active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%] {\\n  width: 75%;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n  display: inline-block;\\n  min-width: 60px;\\n  text-align: center;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-progress[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #90caf9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-verified[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n  border: 1px solid #a5d6a7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-on-hold[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%] {\\n  width: 25%;\\n  vertical-align: middle;\\n  text-align: right;\\n  padding-right: 0;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  gap: 1rem;\\n  justify-content: flex-end;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 120px; \\n\\n  min-width: 120px;\\n  max-width: 120px;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n  display: block; \\n\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   .fw-medium[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   .fw-medium[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  display: block; \\n\\n  min-height: 1.25rem; \\n\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%] {\\n  width: 15%;\\n  vertical-align: middle;\\n  text-align: center;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3699ff;\\n  cursor: pointer;\\n  transition: color 0.2s ease, transform 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  transition: transform 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\\n  border-left: 4px solid #3699ff;\\n  box-shadow: inset 0 2px 4px rgba(54, 153, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]   .accordion-content[_ngcontent-%COMP%] {\\n  border-top: 1px solid #d1e7ff;\\n  background: transparent;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]   .accordion-content[_ngcontent-%COMP%]   .reviews-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 0.375rem;\\n  margin: 0.5rem;\\n  padding: 1rem;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.15);\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:has( + .accordion-content-row[_ngcontent-%COMP%]:not(.d-none)) {\\n  background: linear-gradient(135deg, #f0f8ff 0%, #e8f2ff 100%);\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.2);\\n  transition: all 0.3s ease;\\n  margin-right: 0;\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:has( + .accordion-content-row[_ngcontent-%COMP%]:not(.d-none))   .submittal-title-cell[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: #f8f9fa;\\n  border-radius: 0.475rem 0.475rem 0 0;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1rem;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.reviews-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #1b5e20 !important;\\n  border: 1px solid #c8e6c9 !important;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0 !important;\\n  color: #e65100 !important;\\n  border: 1px solid #ffcc02 !important;\\n}\\n\\n.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n\\n.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd !important;\\n  color: #1565c0 !important;\\n  border: 1px solid #bbdefb !important;\\n}\\n\\n.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #1b5e20 !important;\\n  border: 1px solid #c8e6c9 !important;\\n}\\n\\n.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n\\n.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n\\n.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n\\n.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n\\n.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n\\n.reviews-container[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n  overflow-y: auto;\\n  padding: 0.75rem;\\n}\\n\\n.review-item[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5eaee;\\n  padding: 0.5rem 0;\\n  background: transparent;\\n  transition: background-color 0.2s ease;\\n}\\n.review-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.review-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.review-single-line[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 20px 1fr 120px 120px 140px 140px 30px;\\n  align-items: center;\\n  gap: 0.75rem;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  background: transparent;\\n  border-radius: 0.375rem;\\n  transition: all 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.review-single-line.expanded[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 1px solid #2196f3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .reviewer-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .due-date-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .completed-date-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .review-actions-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  min-height: 20px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-actions-container[_ngcontent-%COMP%] {\\n  gap: 0.5rem;\\n  justify-content: flex-end;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n  white-space: nowrap;\\n  display: inline-block;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3699ff;\\n  cursor: pointer;\\n  transition: color 0.2s ease, transform 0.2s ease;\\n  margin-left: 0.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.view-icon[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.view-icon[_ngcontent-%COMP%]:hover {\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-accordion-toggle[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  transition: transform 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-accordion-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 0.25rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]:hover {\\n  color: #1e7e34;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 0.25rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  border: none;\\n  background: none;\\n  box-shadow: none;\\n  text-decoration: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover, .review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus, .review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:active {\\n  background: none;\\n  box-shadow: none;\\n  text-decoration: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled   .download-pdf-icon[_ngcontent-%COMP%] {\\n  cursor: not-allowed;\\n  color: #6c757d;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled   .download-pdf-icon[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n  color: #6c757d;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  border-top: 1px solid #e5eaee;\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\\n  border-radius: 0.375rem;\\n  box-shadow: inset 0 2px 4px rgba(54, 153, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 0.375rem;\\n  margin: 0.5rem;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-radius: 0.75rem !important;\\n  padding: 2rem !important;\\n  border: 1px solid #e5eaee !important;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;\\n  margin: 0 !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem !important;\\n  font-weight: 800 !important;\\n  color: #1a202c !important;\\n  margin-bottom: 2rem !important;\\n  padding-bottom: 1rem !important;\\n  border-bottom: 3px solid #3699ff !important;\\n  position: relative !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 1rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: 1.8rem;\\n  filter: drop-shadow(0 2px 4px rgba(54, 153, 255, 0.3));\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 2px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.4);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%] {\\n  background: #fff !important;\\n  border: 1px solid #e5eaee !important;\\n  border-radius: 0.75rem !important;\\n  padding: 1.5rem !important;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\\n  position: relative !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\\n  overflow: hidden !important;\\n  margin-bottom: 2rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #3699ff, #1bc5bd);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before {\\n  background: linear-gradient(180deg, #1bc5bd, #3699ff);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1.25rem;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 2px solid #f1f3f4;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  width: 50px;\\n  height: 2px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 1px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-number, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd) !important;\\n  color: white !important;\\n  width: 32px !important;\\n  height: 32px !important;\\n  border-radius: 50% !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  font-size: 0.9rem !important;\\n  font-weight: 800 !important;\\n  box-shadow: 0 4px 12px rgba(54, 153, 255, 0.4) !important;\\n  flex-shrink: 0 !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-number::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd);\\n  border-radius: 50%;\\n  z-index: -1;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_pulse 2s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 0.75rem !important;\\n  padding: 0.5rem 0 !important;\\n  background: transparent !important;\\n  border-radius: 0 !important;\\n  border: none !important;\\n  transition: all 0.2s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover {\\n  background: transparent;\\n  transform: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 700;\\n  color: #4a5568;\\n  text-transform: capitalize;\\n  letter-spacing: 0.05rem;\\n  min-width: 80px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-value, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .resolved-date, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%] {\\n  color: inherit;\\n  background: transparent;\\n  padding: 0.25rem 0;\\n  border-radius: 0;\\n  font-weight: 700;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 800;\\n  color: #2d3748;\\n  text-transform: capitalize;\\n  letter-spacing: 0.15rem;\\n  margin-bottom: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label i, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.9rem;\\n  width: 16px;\\n  text-align: center;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u25B6\\\";\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  animation: _ngcontent-%COMP%_slideRight 1.5s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_slideRight {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  50% {\\n    transform: translateX(3px);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  color: #2d3748 !important;\\n  line-height: 1.7 !important;\\n  padding: 1.25rem !important;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;\\n  border-radius: 0.75rem !important;\\n  border: 1px solid #e2e8f0 !important;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04) !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #e2e8f0, #cbd5e0);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);\\n  border-color: #fed7d7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #feb2b2, #fc8181);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.comment, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);\\n  border-color: #bee3f8;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.comment::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #90cdf4, #63b3ed);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%);\\n  border-color: #c6f6d5;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  color: #38a169;\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  animation: _ngcontent-%COMP%_checkmark 0.6s ease-in-out;\\n}\\n@keyframes _ngcontent-%COMP%_checkmark {\\n  0% {\\n    transform: scale(0) rotate(0deg);\\n  }\\n  50% {\\n    transform: scale(1.2) rotate(180deg);\\n  }\\n  100% {\\n    transform: scale(1) rotate(360deg);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.eor-response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%) !important;\\n  border-color: #c6f6d5 !important;\\n  box-shadow: inset 0 2px 8px rgba(56, 161, 105, 0.1) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.eor-response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.responded-by, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%) !important;\\n  border-color: #c6f6d5 !important;\\n  box-shadow: inset 0 2px 8px rgba(56, 161, 105, 0.1) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.responded-by::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-separator, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent, #e5eaee, transparent);\\n  margin: 2rem 0;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-separator::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 6px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 3px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-radius: 0.75rem !important;\\n  padding: 2rem !important;\\n  border: 1px solid #e5eaee !important;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem !important;\\n  font-weight: 800 !important;\\n  color: #1a202c !important;\\n  margin-bottom: 2rem !important;\\n  padding-bottom: 1rem !important;\\n  border-bottom: 3px solid #3699ff !important;\\n  position: relative !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 1rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: 1.8rem;\\n  filter: drop-shadow(0 2px 4px rgba(54, 153, 255, 0.3));\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 2px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.4);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%] {\\n  background: #fff !important;\\n  border: 1px solid #e5eaee !important;\\n  border-radius: 0.75rem !important;\\n  padding: 1.5rem !important;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\\n  position: relative !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\\n  overflow: hidden !important;\\n  margin-bottom: 2rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #3699ff, #1bc5bd);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before {\\n  background: linear-gradient(180deg, #1bc5bd, #3699ff);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1.25rem;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 2px solid #f1f3f4;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  width: 50px;\\n  height: 2px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 1px;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-number, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd) !important;\\n  color: white !important;\\n  width: 32px !important;\\n  height: 32px !important;\\n  border-radius: 50% !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  font-size: 0.9rem !important;\\n  font-weight: 800 !important;\\n  box-shadow: 0 4px 12px rgba(54, 153, 255, 0.4) !important;\\n  flex-shrink: 0 !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-number::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd);\\n  border-radius: 50%;\\n  z-index: -1;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_pulse 2s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.5rem 0;\\n  background: transparent;\\n  border-radius: 0;\\n  border: none;\\n  transition: all 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover {\\n  background: transparent;\\n  transform: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 700;\\n  color: #4a5568;\\n  text-transform: capitalize;\\n  letter-spacing: 0.05rem;\\n  min-width: 80px;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-value, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .resolved-date, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%] {\\n  color: inherit;\\n  background: transparent;\\n  padding: 0.25rem 0;\\n  border-radius: 0;\\n  font-weight: 700;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .respond-btn, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .respond-btn[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .respond-btn[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 800;\\n  color: #2d3748;\\n  text-transform: capitalize;\\n  letter-spacing: 0.15rem;\\n  margin-bottom: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label i, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.9rem;\\n  width: 16px;\\n  text-align: center;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u25B6\\\";\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  animation: _ngcontent-%COMP%_slideRight 1.5s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_slideRight {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  50% {\\n    transform: translateX(3px);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #2d3748;\\n  line-height: 1.7;\\n  padding: 1.25rem;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\\n  border-radius: 0.75rem;\\n  border: 1px solid #e2e8f0;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04);\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #e2e8f0, #cbd5e0);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);\\n  border-color: #fed7d7;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #feb2b2, #fc8181);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.comment, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);\\n  border-color: #bee3f8;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.comment::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #90cdf4, #63b3ed);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%);\\n  border-color: #c6f6d5;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  color: #38a169;\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  animation: _ngcontent-%COMP%_checkmark 0.6s ease-in-out;\\n}\\n@keyframes _ngcontent-%COMP%_checkmark {\\n  0% {\\n    transform: scale(0) rotate(0deg);\\n  }\\n  50% {\\n    transform: scale(1.2) rotate(180deg);\\n  }\\n  100% {\\n    transform: scale(1) rotate(360deg);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-separator, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent, #e5eaee, transparent);\\n  margin: 2rem 0;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-separator::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 6px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 3px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  margin-bottom: 1rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 2px solid #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #3f4254;\\n  line-height: 1.5;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  text-align: center;\\n  color: #6c7293;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  display: block;\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  margin-bottom: 0.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.875rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #3699ff;\\n  box-shadow: 0 0 0 0.2rem rgba(54, 153, 255, 0.25);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background-color: #1bc5bd;\\n  border-color: #1bc5bd;\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  background-color: #17a2b8;\\n  border-color: #17a2b8;\\n}\\n\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-bottom: 0.75rem;\\n  font-size: 0.75rem;\\n  color: #6c7293;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%]::before, \\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  width: 4px;\\n  height: 4px;\\n  background-color: #6c7293;\\n  border-radius: 50%;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .view-btn[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-size: 0.65rem;\\n  padding: 6px 10px;\\n  line-height: 1;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n}\\n\\n.no-selection[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  text-align: center;\\n  color: #6c7293;\\n}\\n\\n@media (max-width: 768px) {\\n  .table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%], \\n   .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%] {\\n    grid-template-columns: 20px 1fr 100px 100px 120px 120px 30px;\\n    gap: 0.5rem;\\n    font-size: 0.8rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.2rem 0.4rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .review-single-line[_ngcontent-%COMP%] {\\n    grid-template-columns: 20px 1fr 80px 80px 100px 100px 25px;\\n    gap: 0.25rem;\\n    padding: 0.25rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n    padding: 0.15rem 0.3rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n    font-size: 0.6rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.permit-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: white;\\n  padding: 2rem;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n.correction-content[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%], \\n.correction-content[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;\\n  border-color: #e2e8f0 !important;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04) !important;\\n}\\n.correction-content[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]::before, \\n.correction-content[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n\\n\\n\\n.notes-actions-card[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  padding: 1.5rem;\\n  max-width: 700px;\\n  margin: auto;\\n}\\n\\n\\n\\n.notes-actions-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1.2rem;\\n  border-bottom: 1px solid #e5e5e5;\\n  padding-bottom: 0.8rem;\\n}\\n\\n.notes-actions-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n  border: none;\\n  padding: 6px 14px;\\n  border-radius: 6px;\\n  font-size: 0.9rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n\\n\\n.notes-actions-body[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 0.4rem;\\n  color: #333;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 60px;\\n  padding: 0.6rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  resize: vertical;\\n  font-size: 0.95rem;\\n}\\n\\n\\n\\n.kendo-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9wZXJtaXRzL3Blcm1pdC12aWV3L3Blcm1pdC12aWV3LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGdCQUFnQjtBQUFoQjtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLE1BQUE7QUFFRjs7QUFFQTtFQUNFLGlCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx1QkFBQTtBQUNGO0FBQ0U7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBQ0o7QUFFRTtFQUNFLFNBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtBQUFKO0FBR0U7RUFDRSxhQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUEsRUFBQSxxQ0FBQTtBQURKO0FBR0k7RUFDRSw4QkFBQTtFQUNBLG9DQUFBO0VBQ0EsMkJBQUE7QUFETjtBQUtFOztFQUVFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsdUJBQUEsRUFBQSw2Q0FBQTtFQUNBLHNCQUFBO0VBQ0EseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7RUFDQSx5Q0FBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBLEVBQUEsaUJBQUE7RUFDQSxrRkFBQTtBQUhKO0FBS0k7O0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0FBRk47QUFLSTs7RUFDRSx5QkFBQTtFQUNBLDBDQUFBO0VBQ0EscUJBQUE7QUFGTjtBQUtJOztFQUNFLDBCQUFBO0VBQ0EseUNBQUE7QUFGTjtBQU9JO0VBQ0UscUJBQUE7QUFMTjs7QUFhSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQSxFQUFBLDBDQUFBO0FBVk47QUFZTTtFQUNFLDZCQUFBO0VBQ0EseUJBQUE7RUFDQSxxQkFBQTtBQVZSO0FBYU07RUFDRSxnQkFBQTtFQUNBLGFBQUE7QUFYUjtBQWNNO0VBQ0UsMkJBQUE7RUFDQSxjQUFBO0FBWlI7QUFlTTtFQUNFLHlCQUFBO0FBYlI7O0FBb0JBO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0VBQ0Esb0NBQUE7RUFDQSxzQkFBQTtFQUNBLDZCQUFBO0FBakJGO0FBbUJFO0VBQ0Usb0NBQUE7RUFDQSxzQkFBQTtFQUNBLDZCQUFBO0VBQ0EsMkJBQUE7QUFqQko7O0FBb0JBO0VBQ0UsZ0JBQUE7RUFHQSxjQUFBO0VBQ0EsZ0JBQUE7QUFuQkY7O0FBc0JBO0VBQ0UsbUJBQUE7RUFDQSx3QkFBQTtFQUNBLGdDQUFBO0VBQ0EsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7QUFuQkY7O0FBc0JBO0VBQ0UsU0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7QUFuQkY7O0FBd0JFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBLEVBQUEscUNBQUE7QUFyQko7QUF1Qkk7RUFDRSw2QkFBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7QUFyQk47QUF3Qkk7RUFDRSxnQkFBQTtFQUNBLGFBQUE7QUF0Qk47QUF5Qkk7RUFDRSwyQkFBQTtFQUNBLGNBQUE7QUF2Qk47QUEwQkk7RUFDRSx5QkFBQTtBQXhCTjs7QUE0QkE7RUFDRSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSx3QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxnQ0FBQTtBQXpCRjs7QUE0QkE7RUFDRSxtQkFBQTtBQXpCRjs7QUEyQkE7RUFDRSxvQkFBQTtFQUNFLHNCQUFBO0VBQ0YsMENBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUF4QkY7O0FBMkJBO0VBQ0UsYUFBQTtFQUNBLHNDQUFBLEVBQUEsNEJBQUE7RUFDQSxXQUFBO0FBeEJGOztBQTRCQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFdBQUE7QUF6QkY7O0FBNEJBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7QUF6QkY7QUEyQkU7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDBCQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBekJKO0FBNEJFO0VBQ0UsZUFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxxQkFBQTtFQUNBLHFCQUFBO0FBMUJKOztBQThCQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFdBQUE7QUEzQkY7QUE2QkU7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDBCQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBM0JKO0FBOEJFOztFQUVFLGtCQUFBO0FBNUJKO0FBK0JFO0VBQ0UsZUFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUEsRUFBQSx3QkFBQTtBQTdCSjtBQWdDRTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBLEVBQUEsb0NBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQSxFQUFBLG9CQUFBO0VBQ0EsdUJBQUEsRUFBQSx3QkFBQTtFQUNBLFlBQUEsRUFBQSxjQUFBO0VBQ0EsWUFBQSxFQUFBLHdCQUFBO0VBQ0EsZ0JBQUE7QUE5Qko7QUFrQ0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWhDSjtBQW1DRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBakNKO0FBb0NFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFsQ0o7QUFxQ0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQW5DSjtBQXNDRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBcENKO0FBdUNFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFyQ0o7QUF3Q0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXRDSjtBQXlDRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBdkNKO0FBMENFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF4Q0o7O0FBNkNBO0VBQ0UsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBMUNGO0FBNENFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUExQ0o7QUE2Q0U7RUFDRSxjQUFBO0VBQ0EsU0FBQTtBQTNDSjtBQStDSTtFQUNFLHlCQUFBO0VBQ0EsbUJBQUE7QUE3Q047QUErQ007RUFDRSxvQkFBQTtFQUNBLGNBQUE7QUE3Q1I7O0FBc0RBO0VBQ0UsbUJBQUE7QUFuREY7QUFxREU7RUFDRSx1QkFBQTtFQUNBLHlCQUFBO0FBbkRKO0FBcURJO0VBQ0Usb0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUFuRE47QUFzREk7RUFDRSxvQkFBQTtFQUNBLGNBQUE7QUFwRE47QUF1REk7RUFDRSxrQkFBQTtFQUNBLG9CQUFBO0VBQ0EsY0FBQTtBQXJETjtBQXVETTtFQUNFLHNCQUFBO0FBckRSO0FBeURJO0VBQ0UsbUJBQUE7RUFDQSxvQkFBQTtBQXZETjtBQXlETTtFQUNFLG9CQUFBO0FBdkRSOztBQThEQTtFQUNFLGdCQUFBO0VBQ0EsMEJBQUE7RUFDQSxhQUFBO0FBM0RGOztBQStEQTtFQUNFLHlCQUFBO0VBQ0Esd0JBQUE7QUE1REY7O0FBZ0VBO0VBQ0UseUJBQUE7QUE3REY7O0FBZ0VBO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtBQTdERjtBQStERTs7RUFFRSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxnQ0FBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7RUFDQSxxQkFBQTtFQUNBLHdDQUFBO0FBN0RKO0FBK0RJOztFQUNFLHlCQUFBO0VBQ0EsMkJBQUE7RUFDQSx5Q0FBQTtBQTVETjtBQStESTs7RUFDRSxtQkFBQTtFQUNBLHFCQUFBO0VBQ0EsNkNBQUE7QUE1RE47QUErREk7O0VBQ0UseUJBQUE7QUE1RE47QUFnRUU7O0VBRUUsVUFBQTtFQUNBLHNCQUFBO0FBOURKO0FBZ0VJOzs7O0VBRUUsaUJBQUE7RUFDQSxzQkFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7RUFDQSw2QkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxxQkFBQTtFQUNBLGVBQUE7RUFnQ0Esa0JBQUE7QUEzRk47QUE4RE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUF6RFI7QUE0RE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUF2RFI7QUEwRE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFyRFI7QUF3RE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFuRFI7QUFzRE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFqRFI7QUFzRE07Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWpEUjtBQW9ETTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBL0NSO0FBa0RNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUE3Q1I7QUFnRE07Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTNDUjtBQThDTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBekNSO0FBNENNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF2Q1I7QUEwQ007Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXJDUjtBQXdDTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBbkNSO0FBdUNNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFsQ1I7QUFxQ007Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWhDUjtBQW1DTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBOUJSO0FBaUNNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUE1QlI7QUErQk07Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFCUjtBQTZCTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBeEJSO0FBMkJNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF0QlI7QUF5Qk07Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXBCUjtBQXVCTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBbEJSO0FBc0JNOzs7O0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBakJSO0FBb0JNOzs7O0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBZlI7QUFrQk07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFiUjtBQWdCTTs7OztFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQVhSO0FBY007Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFUUjtBQWNFOztFQUVFLFVBQUE7RUFDQSxzQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFaSjtBQWNJOztFQUNFLFNBQUE7RUFDQSx5QkFBQTtBQVhOO0FBY0k7O0VBQ0Usa0JBQUE7RUFDQSxZQUFBLEVBQUEsaUNBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBWE47QUFhTTs7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQSxFQUFBLG9DQUFBO0FBVlI7QUFhTTs7RUFDRSxrQkFBQTtFQUNBLGNBQUEsRUFBQSx3Q0FBQTtFQUNBLG1CQUFBLEVBQUEsNkJBQUE7QUFWUjtBQWdCRTtFQUNFLFVBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0FBZEo7QUFnQkk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxnREFBQTtBQWROO0FBZ0JNO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBZFI7QUFpQk07RUFDRSxjQUFBO0FBZlI7QUFpQlE7RUFDRSxjQUFBO0FBZlY7QUFxQkU7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFuQko7QUFxQkk7RUFDRSwrQkFBQTtBQW5CTjtBQXVCRTtFQUNFLDZEQUFBO0VBQ0EsOEJBQUE7RUFDQSxtREFBQTtFQUNBLHlCQUFBO0FBckJKO0FBdUJJO0VBQ0UsNkJBQUE7RUFDQSx1QkFBQTtBQXJCTjtBQXVCTTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQ0FBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtFQUNBLGFBQUE7RUFDQSw4Q0FBQTtBQXJCUjtBQTJCRTtFQUNFLDZEQUFBO0VBQ0EsNkNBQUE7RUFDQSx5QkFBQTtFQUNBLGVBQUE7QUF6Qko7QUE0Qk07RUFDRSxjQUFBO0VBQ0EscUJBQUE7QUExQlI7QUFnQ0U7RUFDRSx5QkFBQTtBQTlCSjtBQWdDSTtFQUNFLHlCQUFBO0FBOUJOOztBQW1DQTtFQUNFLG9CQUFBO0VBQ0EsZ0NBQUE7RUFDQSxtQkFBQTtFQUNBLG9DQUFBO0VBQ0EsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7QUFoQ0Y7QUFrQ0U7RUFDRSxTQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtBQWhDSjtBQW9DSTtFQUNFLGtCQUFBO0VBQ0EseUJBQUE7QUFsQ047QUFzQ0U7RUFDRSxrQkFBQTtFQUNBLHVCQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtFQUNBLDZCQUFBO0FBcENKO0FBdUNJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFyQ047QUF3Q0k7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXRDTjtBQXlDSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBdkNOO0FBMkNJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF6Q047QUE0Q0k7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFDTjtBQTZDSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBM0NOO0FBOENJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUE1Q047QUErQ0k7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTdDTjtBQWdESTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBOUNOOztBQW9EQTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7QUFqREY7O0FBc0RBO0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBbkRGOztBQXNEQTtFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQW5ERjs7QUFzREE7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFuREY7O0FBc0RBO0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBbkRGOztBQXNEQTtFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQW5ERjs7QUF1REE7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFwREY7O0FBdURBO0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBcERGOztBQXVEQTtFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQXBERjs7QUF1REE7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFwREY7O0FBdURBO0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBcERGOztBQXdEQTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQXJERjs7QUF3REE7RUFDRSxnQ0FBQTtFQUNBLGlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQ0FBQTtBQXJERjtBQXVERTtFQUNFLHlCQUFBO0FBckRKO0FBd0RFO0VBQ0UsbUJBQUE7QUF0REo7O0FBMERBO0VBQ0UsYUFBQTtFQUNBLDREQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSx1QkFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7QUF2REY7QUF5REU7RUFDRSx5QkFBQTtFQUNBLDJCQUFBO0VBQ0Esd0NBQUE7QUF2REo7QUEwREU7RUFDRSxtQkFBQTtFQUNBLHlCQUFBO0FBeERKO0FBMkRFO0VBQ0UsU0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0FBekRKO0FBNERFOzs7OztFQUtFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0FBMURKO0FBNkRFO0VBQ0UsV0FBQTtFQUNBLHlCQUFBO0FBM0RKO0FBOERFO0VBQ0Usa0JBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7QUE1REo7QUErREk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTdETjtBQWdFSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBOUROO0FBaUVJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUEvRE47QUFrRUk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWhFTjtBQW1FSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBakVOO0FBb0VJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFsRU47QUFxRUk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQW5FTjtBQXNFSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBcEVOO0FBd0VJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF0RU47QUF5RUk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXZFTjtBQTBFSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBeEVOO0FBMkVJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF6RU47QUE0RUk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFFTjtBQThFRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTVFSjtBQStFRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTdFSjtBQWdGRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTlFSjtBQWlGRTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdEQUFBO0VBQ0EsbUJBQUE7QUEvRUo7QUFpRkk7RUFDRSxjQUFBO0VBQ0EscUJBQUE7QUEvRU47QUFrRkk7RUFDRSxjQUFBO0FBaEZOO0FBa0ZNO0VBQ0UsY0FBQTtBQWhGUjtBQW9GSTtFQUNFLGNBQUE7QUFsRk47QUFvRk07RUFDRSxjQUFBO0FBbEZSO0FBd0ZFO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsK0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBdEZKO0FBd0ZJO0VBQ0UsK0JBQUE7QUF0Rk47QUEyRkU7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGdCQUFBO0FBekZKO0FBMkZJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBekZOO0FBNEZJO0VBQ0Usc0JBQUE7QUExRk47QUErRkU7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGdCQUFBO0FBN0ZKO0FBK0ZJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBN0ZOO0FBZ0dJO0VBQ0Usc0JBQUE7QUE5Rk47QUFtR0U7RUFDRSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0FBakdKO0FBbUdJO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0FBakdOO0FBb0dJO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0FBbEdOO0FBb0dNO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBbEdSO0FBb0dRO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUFsR1Y7QUEwR0k7RUFDRSx5QkFBQTtFQUNBLHdDQUFBO0FBeEdOO0FBMkdJO0VBQ0UsZUFBQTtBQXpHTjtBQTRHSTtFQUNFLGdCQUFBO0FBMUdOO0FBK0dFO0VBQ0UsZ0JBQUE7RUFDQSw2QkFBQTtFQUNBLDZEQUFBO0VBQ0EsdUJBQUE7RUFDQSxtREFBQTtFQUNBLHlCQUFBO0FBN0dKO0FBK0dJO0VBQ0UsYUFBQTtFQUNBLG9DQUFBO0VBQ0EsdUJBQUE7RUFDQSxjQUFBO0VBQ0EsOENBQUE7QUE3R047QUFnSE07O0VBRUUsOEJBQUE7RUFDQSxpQ0FBQTtFQUNBLHdCQUFBO0VBQ0Esb0NBQUE7RUFDQSxxREFBQTtFQUNBLG9CQUFBO0FBOUdSO0FBZ0hROztFQUNFLDRCQUFBO0VBQ0EsMkJBQUE7RUFDQSx5QkFBQTtFQUNBLDhCQUFBO0VBQ0EsK0JBQUE7RUFDQSwyQ0FBQTtFQUNBLDZCQUFBO0VBQ0Esd0JBQUE7RUFDQSw4QkFBQTtFQUNBLG9CQUFBO0FBN0dWO0FBK0dVOztFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLHNEQUFBO0FBNUdaO0FBK0dVOztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxvREFBQTtFQUNBLGtCQUFBO0VBQ0EsNkNBQUE7QUE1R1o7QUFnSFE7O0VBQ0UsMkJBQUE7RUFDQSxvQ0FBQTtFQUNBLGlDQUFBO0VBQ0EsMEJBQUE7RUFDQSxxREFBQTtFQUNBLDZCQUFBO0VBQ0EsNERBQUE7RUFDQSwyQkFBQTtFQUNBLDhCQUFBO0FBN0dWO0FBK0dVOztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7RUFDQSxxREFBQTtFQUNBLGtDQUFBO0FBNUdaO0FBK0dVOztFQUNFLDJCQUFBO0VBQ0EsK0NBQUE7QUE1R1o7QUE4R1k7O0VBQ0UscURBQUE7QUEzR2Q7QUErR1U7O0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQ0FBQTtFQUNBLGtCQUFBO0FBNUdaO0FBOEdZOztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxvREFBQTtFQUNBLGtCQUFBO0FBM0dkO0FBOEdZOztFQUNFLGdFQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0VBQ0EsNkJBQUE7RUFDQSx3QkFBQTtFQUNBLDhCQUFBO0VBQ0Esa0NBQUE7RUFDQSw0QkFBQTtFQUNBLDJCQUFBO0VBQ0EseURBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0Esb0NBQUE7QUEzR2Q7QUE2R2M7O0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHFEQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHdDQUFBO0FBMUdoQjtBQTZHYztFQUNFO0lBQVcsbUJBQUE7SUFBcUIsWUFBQTtFQXpHOUM7RUEwR2M7SUFBTSxxQkFBQTtJQUF1QixZQUFBO0VBdEczQztBQUNGO0FBeUdZOztFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0FBdEdkO0FBd0djOztFQUNFLHdCQUFBO0VBQ0EsOEJBQUE7RUFDQSx1QkFBQTtFQUNBLDRCQUFBO0VBQ0Esa0NBQUE7RUFDQSwyQkFBQTtFQUNBLHVCQUFBO0VBQ0Esb0NBQUE7QUFyR2hCO0FBdUdnQjs7RUFDRSx1QkFBQTtFQUNBLGVBQUE7QUFwR2xCO0FBdUdnQjs7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDBCQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0FBcEdsQjtBQXVHZ0I7O0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxPQUFBO0FBcEdsQjtBQXVHZ0I7O0VBQ0UsY0FBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBcEdsQjtBQTJHWTs7RUFDRSxxQkFBQTtFQUNBLGtCQUFBO0FBeEdkO0FBMEdjOztFQUNFLGdCQUFBO0FBdkdoQjtBQTBHYzs7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDBCQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtBQXZHaEI7QUF5R2dCOztFQUNFLGNBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtBQXRHbEI7QUF5R2dCOztFQUNFLFlBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQ0FBQTtBQXRHbEI7QUF5R2dCO0VBQ0U7SUFBVyx3QkFBQTtFQXRHM0I7RUF1R2dCO0lBQU0sMEJBQUE7RUFwR3RCO0FBQ0Y7QUF1R2M7O0VBQ0UsMEJBQUE7RUFDQSx5QkFBQTtFQUNBLDJCQUFBO0VBQ0EsMkJBQUE7RUFDQSx3RUFBQTtFQUNBLGlDQUFBO0VBQ0Esb0NBQUE7RUFDQSwwREFBQTtFQUNBLDZCQUFBO0VBQ0Esb0NBQUE7QUFwR2hCO0FBc0dnQjs7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFVBQUE7RUFDQSxZQUFBO0VBQ0EscURBQUE7RUFDQSxrQ0FBQTtBQW5HbEI7QUFzR2dCOztFQUNFLGdEQUFBO0VBQ0EsMkJBQUE7QUFuR2xCO0FBc0dnQjs7RUFDRSw2REFBQTtFQUNBLHFCQUFBO0FBbkdsQjtBQXFHa0I7O0VBQ0UscURBQUE7QUFsR3BCO0FBc0dnQjs7RUFDRSw2REFBQTtFQUNBLHFCQUFBO0FBbkdsQjtBQXFHa0I7O0VBQ0UscURBQUE7QUFsR3BCO0FBc0dnQjs7RUFDRSw2REFBQTtFQUNBLHFCQUFBO0FBbkdsQjtBQXFHa0I7O0VBQ0UscURBQUE7QUFsR3BCO0FBcUdrQjs7RUFDRSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0VBQ0EscUNBQUE7QUFsR3BCO0FBcUdrQjtFQUNFO0lBQUssZ0NBQUE7RUFsR3ZCO0VBbUdrQjtJQUFNLG9DQUFBO0VBaEd4QjtFQWlHa0I7SUFBTyxrQ0FBQTtFQTlGekI7QUFDRjtBQWlHZ0I7O0VBQ0Usd0VBQUE7RUFDQSxnQ0FBQTtFQUNBLDhEQUFBO0FBOUZsQjtBQWdHa0I7O0VBQ0UsZ0VBQUE7RUFDQSxxQkFBQTtFQUNBLHVCQUFBO0VBQ0EsNkNBQUE7QUE3RnBCO0FBaUdnQjs7RUFDRSx3RUFBQTtFQUNBLGdDQUFBO0VBQ0EsOERBQUE7QUE5RmxCO0FBZ0drQjs7RUFDRSxnRUFBQTtFQUNBLHFCQUFBO0VBQ0EsdUJBQUE7RUFDQSw2Q0FBQTtBQTdGcEI7QUFvR1U7O0VBQ0UsV0FBQTtFQUNBLHFFQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBakdaO0FBbUdZOztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLG9EQUFBO0VBQ0Esa0JBQUE7RUFDQSw2Q0FBQTtBQWhHZDtBQTBHRTs7O0VBR0UsOEJBQUE7RUFDQSxpQ0FBQTtFQUNBLHdCQUFBO0VBQ0Esb0NBQUE7RUFDQSxxREFBQTtBQXhHSjtBQTBHSTs7O0VBQ0UsNEJBQUE7RUFDQSwyQkFBQTtFQUNBLHlCQUFBO0VBQ0EsOEJBQUE7RUFDQSwrQkFBQTtFQUNBLDJDQUFBO0VBQ0EsNkJBQUE7RUFDQSx3QkFBQTtFQUNBLDhCQUFBO0VBQ0Esb0JBQUE7QUF0R047QUF3R007OztFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLHNEQUFBO0FBcEdSO0FBdUdNOzs7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxXQUFBO0VBQ0Esb0RBQUE7RUFDQSxrQkFBQTtFQUNBLDZDQUFBO0FBbkdSO0FBdUdJOzs7RUFDRSwyQkFBQTtFQUNBLG9DQUFBO0VBQ0EsaUNBQUE7RUFDQSwwQkFBQTtFQUNBLHFEQUFBO0VBQ0EsNkJBQUE7RUFDQSw0REFBQTtFQUNBLDJCQUFBO0VBQ0EsOEJBQUE7QUFuR047QUFxR007OztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7RUFDQSxxREFBQTtFQUNBLGtDQUFBO0FBakdSO0FBb0dNOzs7RUFDRSwyQkFBQTtFQUNBLCtDQUFBO0FBaEdSO0FBa0dROzs7RUFDRSxxREFBQTtBQTlGVjtBQWtHTTs7O0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQ0FBQTtFQUNBLGtCQUFBO0FBOUZSO0FBZ0dROzs7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxXQUFBO0VBQ0Esb0RBQUE7RUFDQSxrQkFBQTtBQTVGVjtBQStGUTs7O0VBQ0UsZ0VBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSw2QkFBQTtFQUNBLHdCQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQ0FBQTtFQUNBLDRCQUFBO0VBQ0EsMkJBQUE7RUFDQSx5REFBQTtFQUNBLHlCQUFBO0VBQ0EsNkJBQUE7RUFDQSxvQ0FBQTtBQTNGVjtBQTZGVTs7O0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHFEQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHdDQUFBO0FBekZaO0FBNEZVO0VBQ0U7SUFBVyxtQkFBQTtJQUFxQixZQUFBO0VBeEYxQztFQXlGVTtJQUFNLHFCQUFBO0lBQXVCLFlBQUE7RUFyRnZDO0FBQ0Y7QUF3RlE7OztFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0FBcEZWO0FBc0ZVOzs7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLHlCQUFBO0FBbEZaO0FBb0ZZOzs7RUFDRSx1QkFBQTtFQUNBLGVBQUE7QUFoRmQ7QUFtRlk7OztFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsMEJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7QUEvRWQ7QUFrRlk7OztFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsT0FBQTtBQTlFZDtBQWlGWTs7O0VBQ0UsY0FBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBN0VkO0FBZ0ZZOzs7RUFFRSw4QkFBQTtBQTdFZDtBQW9GUTs7O0VBQ0UscUJBQUE7RUFDQSxrQkFBQTtBQWhGVjtBQWtGVTs7O0VBQ0UsZ0JBQUE7QUE5RVo7QUFpRlU7OztFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsMEJBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0FBN0VaO0FBK0VZOzs7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxXQUFBO0VBQ0Esa0JBQUE7QUEzRWQ7QUE4RVk7OztFQUNFLFlBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQ0FBQTtBQTFFZDtBQTZFWTtFQUNFO0lBQVcsd0JBQUE7RUExRXZCO0VBMkVZO0lBQU0sMEJBQUE7RUF4RWxCO0FBQ0Y7QUEyRVU7OztFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLDZEQUFBO0VBQ0Esc0JBQUE7RUFDQSx5QkFBQTtFQUNBLCtDQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQXZFWjtBQXlFWTs7O0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxVQUFBO0VBQ0EsWUFBQTtFQUNBLHFEQUFBO0VBQ0Esa0NBQUE7QUFyRWQ7QUF3RVk7OztFQUNFLGdEQUFBO0VBQ0EsMkJBQUE7QUFwRWQ7QUF1RVk7OztFQUNFLDZEQUFBO0VBQ0EscUJBQUE7QUFuRWQ7QUFxRWM7OztFQUNFLHFEQUFBO0FBakVoQjtBQXFFWTs7O0VBQ0UsNkRBQUE7RUFDQSxxQkFBQTtBQWpFZDtBQW1FYzs7O0VBQ0UscURBQUE7QUEvRGhCO0FBbUVZOzs7RUFDRSw2REFBQTtFQUNBLHFCQUFBO0FBL0RkO0FBaUVjOzs7RUFDRSxxREFBQTtBQTdEaEI7QUFnRWM7OztFQUNFLFlBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQ0FBQTtBQTVEaEI7QUErRGM7RUFDRTtJQUFLLGdDQUFBO0VBNURuQjtFQTZEYztJQUFNLG9DQUFBO0VBMURwQjtFQTJEYztJQUFPLGtDQUFBO0VBeERyQjtBQUNGO0FBOERNOzs7RUFDRSxXQUFBO0VBQ0EscUVBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUExRFI7QUE0RFE7OztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLG9EQUFBO0VBQ0Esa0JBQUE7RUFDQSw2Q0FBQTtBQXhEVjtBQWdFSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0NBQUE7QUE5RE47QUFpRUk7RUFDRSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxhQUFBO0FBL0ROO0FBaUVNO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUEvRFI7QUFzRUk7RUFDRSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBcEVOO0FBc0VNO0VBQ0UsZUFBQTtFQUNBLHFCQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7QUFwRVI7QUF1RU07RUFDRSxpQkFBQTtBQXJFUjtBQTJFRTtFQUNFLGdCQUFBO0VBQ0EseUJBQUE7RUFDQSx1QkFBQTtBQXpFSjtBQTJFSTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EscUJBQUE7QUF6RU47QUE0RUk7RUFDRSx5QkFBQTtFQUNBLHVCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTFFTjtBQTRFTTtFQUNFLHFCQUFBO0VBQ0EsaURBQUE7QUExRVI7QUE4RUk7RUFDRSx5QkFBQTtFQUNBLHFCQUFBO0VBQ0EsbUJBQUE7RUFDQSxvQkFBQTtBQTVFTjtBQThFTTtFQUNFLHlCQUFBO0VBQ0EscUJBQUE7QUE1RVI7O0FBbUZFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLHNCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBaEZKO0FBa0ZJOztFQUVFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUFoRk47QUFrRk07O0VBQ0UsV0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtBQS9FUjtBQW1GSTtFQUNFLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxzQkFBQTtFQUNBLGdCQUFBO0FBakZOOztBQXNGQTtFQUNFLGFBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7QUFuRkY7O0FBdUZBO0VBSU07O0lBQ0Usc0JBQUE7SUFDQSxXQUFBO0VBdEZOO0VBMkZBO0lBQ0UsNERBQUE7SUFDQSxXQUFBO0lBQ0EsaUJBQUE7RUF6RkY7RUEyRkU7SUFDRSxpQkFBQTtJQUNBLHNCQUFBO0VBekZKO0VBNEZFOzs7SUFHRSxrQkFBQTtFQTFGSjtFQTZGRTtJQUNFLGlCQUFBO0VBM0ZKO0FBQ0Y7QUErRkE7RUFDRTtJQUNFLDBEQUFBO0lBQ0EsWUFBQTtJQUNBLGdCQUFBO0VBN0ZGO0VBK0ZFO0lBQ0Usa0JBQUE7SUFDQSx1QkFBQTtFQTdGSjtFQWdHRTs7O0lBR0UsaUJBQUE7RUE5Rko7RUFpR0U7SUFDRSxpQkFBQTtFQS9GSjtBQUNGO0FBbUdBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtBQWpHRjs7QUFvR0E7RUFDRSxhQUFBO0VBQ0EscUJBQUE7RUFDQSxZQUFBO0FBakdGOztBQW9HQTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBakdGOztBQW9HQTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQkFBQTtFQUNBLDZCQUFBO0VBQ0EsdUJBQUE7RUFBQSxrQkFBQTtBQWpHRjtBQW9HRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBbEdKO0FBcUdFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFuR0o7QUFzR0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXBHSjtBQXVHRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBckdKO0FBd0dFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF0R0o7QUF5R0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXZHSjtBQTBHRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBeEdKO0FBMkdFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF6R0o7QUE0R0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFHSjs7QUE4R0E7RUFDRSxrQkFBQTtFQUNBLGNBQUE7RUFDQSx1QkFBQTtBQTNHRjs7QUErR0E7RUFDRSxlQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG9DQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0FBNUdGO0FBOEdFO0VBQ0Usa0JBQUE7RUFDQSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLHlDQUFBO0FBNUdKOztBQWlIQTs7RUFFRSx3RUFBQTtFQUNBLGdDQUFBO0VBQ0EsMERBQUE7QUE5R0Y7QUFnSEU7O0VBQ0UsZ0VBQUE7RUFDQSxxQkFBQTtFQUNBLHVCQUFBO0VBQ0EsNkNBQUE7QUE3R0o7O0FBa0hBLG1CQUFBO0FBQ0E7RUFDRSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EsMENBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0FBL0dGOztBQWtIQSxXQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0VBQ0EsZ0NBQUE7RUFDQSxzQkFBQTtBQS9HRjs7QUFrSEE7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtBQS9HRjs7QUFrSEE7RUFDRSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLGVBQUE7RUFDQSxnQ0FBQTtBQS9HRjs7QUFpSEE7RUFDRSxtQkFBQTtBQTlHRjs7QUFpSEEsU0FBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtBQTlHRjs7QUFpSEE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7QUE5R0Y7O0FBaUhBO0VBQ0UsZ0JBQUE7RUFDQSxxQkFBQTtFQUNBLFdBQUE7QUE5R0Y7O0FBaUhBO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBOUdGOztBQWlIQSwyQkFBQTtBQUNBO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0FBOUdGIiwic291cmNlc0NvbnRlbnQiOlsiLnBlcm1pdC12aWV3LWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogMDtcclxufVxyXG5cclxuLy8gUGVybWl0IERldGFpbHMgSGVhZGVyXHJcbi5wZXJtaXQtZGV0YWlscy1oZWFkZXIge1xyXG4gIHBhZGRpbmc6IDAgMS41cmVtO1xyXG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlYWVlO1xyXG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG5cclxuICAuaGVhZGVyLWNvbnRlbnQge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBwYWRkaW5nLXRvcDogMC41cmVtO1xyXG4gIH1cclxuXHJcbiAgaDQge1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gICAgY29sb3I6ICMzZjQyNTQ7XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgfVxyXG5cclxuICAuYnV0dG9uLWdyb3VwIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBnYXA6IDAuNXJlbTtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBtYXJnaW4tbGVmdDogYXV0bzsgLyogRW5zdXJlIGl0IHN0YXlzIGF0IHRoZSByaWdodCBlbmQgKi9cclxuXHJcbiAgICAuYnRuLXNtIHtcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICBwYWRkaW5nOiAwLjM3NXJlbSAwLjc1cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjUgIWltcG9ydGFudDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5iYWNrLWJ1dHRvbixcclxuICAucG9ydGFsLWJ1dHRvbiB7XHJcbiAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBnYXA6IC4zcmVtO1xyXG4gICAgcGFkZGluZzogLjE1cmVtIC41cmVtOyAvKiBzbWFsbGVyIHdpdGggZXhwbGljaXQgdG9wL2JvdHRvbSBwYWRkaW5nICovXHJcbiAgICBib3JkZXItcmFkaXVzOiAuNTVyZW07XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNmNmY5O1xyXG4gICAgY29sb3I6ICMzZjQyNTQ7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlYWVlO1xyXG4gICAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoMCwwLDAsMC4wNSk7XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgZm9udC1zaXplOiAuOHJlbTtcclxuICAgIGxpbmUtaGVpZ2h0OiAxOyAvKiBrZWVwIGNvbXBhY3QgKi9cclxuICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgLjJzIGVhc2UsIGJveC1zaGFkb3cgLjJzIGVhc2UsIHRyYW5zZm9ybSAuMDJzIGVhc2U7XHJcblxyXG4gICAgaSB7XHJcbiAgICAgIGNvbG9yOiAjNWU2ZTgyO1xyXG4gICAgICBmb250LXNpemU6IC43NXJlbTtcclxuICAgIH1cclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2VlZjJmNztcclxuICAgICAgYm94LXNoYWRvdzogMCAzcHggMTBweCByZ2JhKDAsMCwwLDAuMDcpO1xyXG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgICB9XHJcblxyXG4gICAgJjphY3RpdmUge1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMXB4KTtcclxuICAgICAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwwLDAsMC4wNik7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAucG9ydGFsLWJ1dHRvbiB7XHJcbiAgICBpIHtcclxuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gVGFiIHJvdyByaWdodCBzaWRlIGJ1dHRvbnMgc3R5bGluZ1xyXG4uY2FyZC1oZWFkZXIge1xyXG4gIC5kLWZsZXguYWxpZ24taXRlbXMtY2VudGVyLmdhcC0yIHtcclxuICAgIC5idG4tbGluayB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICBoZWlnaHQ6IDJyZW07IC8qIEZpeGVkIGhlaWdodCBmb3IgY29uc2lzdGVudCBhbGlnbm1lbnQgKi9cclxuICAgICAgXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gICAgICAgIGNvbG9yOiAjMzY5OWZmICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAmOmZvY3VzIHtcclxuICAgICAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIGkge1xyXG4gICAgICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZTtcclxuICAgICAgICBsaW5lLWhlaWdodDogMTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgJjpob3ZlciBpIHtcclxuICAgICAgICBjb2xvcjogIzM2OTlmZiAhaW1wb3J0YW50O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBEaXNhYmxlZCBidXR0b24gc3R5bGluZ1xyXG4uYnRuOmRpc2FibGVkIHtcclxuICBvcGFjaXR5OiAwLjY7XHJcbiAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1ICFpbXBvcnRhbnQ7XHJcbiAgY29sb3I6ICM5OTkgIWltcG9ydGFudDtcclxuICBib3JkZXItY29sb3I6ICNkZGQgIWltcG9ydGFudDtcclxuICBcclxuICAmOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjUgIWltcG9ydGFudDtcclxuICAgIGNvbG9yOiAjOTk5ICFpbXBvcnRhbnQ7XHJcbiAgICBib3JkZXItY29sb3I6ICNkZGQgIWltcG9ydGFudDtcclxuICAgIGJveC1zaGFkb3c6IG5vbmUgIWltcG9ydGFudDtcclxuICB9XHJcbn1cclxuLnBlcm1pdC1kZXRhaWxzLWNhcmQge1xyXG4gIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgLy8gYm9yZGVyLXJhZGl1czogMC43NXJlbTtcclxuICAvLyBib3gtc2hhZG93OiAwIDRweCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XHJcbiAgbWFyZ2luOiAxcmVtIDA7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG5cclxuLnBlcm1pdC1kZXRhaWxzLWhlYWRlciB7XHJcbiAgYmFja2dyb3VuZDogI2Y1ZjZmODtcclxuICBwYWRkaW5nOiAwLjc1cmVtIDEuMjVyZW07XHJcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlM2U2ZWE7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxufVxyXG5cclxuLnBlcm1pdC1kZXRhaWxzLWhlYWRlciBoNCB7XHJcbiAgbWFyZ2luOiAwO1xyXG4gIGZvbnQtc2l6ZTogMXJlbTtcclxuICBmb250LXdlaWdodDogNjAwO1xyXG4gIGNvbG9yOiAjMzMzO1xyXG59XHJcblxyXG4vLyBOb3Rlcy9BY3Rpb25zIGVkaXQgYnV0dG9uIHN0eWxpbmcgdG8gbWF0Y2ggdGFiIHJvdyBlZGl0IGljb25cclxuLnBlcm1pdC1kZXRhaWxzLWNhcmQgLnBlcm1pdC1kZXRhaWxzLWhlYWRlciB7XHJcbiAgLmJ0bi1saW5rIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBoZWlnaHQ6IDJyZW07IC8qIFNhbWUgaGVpZ2h0IGFzIHRhYiByb3cgZWRpdCBpY29uICovXHJcbiAgICBcclxuICAgICY6aG92ZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDtcclxuICAgICAgY29sb3I6ICMzNjk5ZmYgIWltcG9ydGFudDtcclxuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAmOmZvY3VzIHtcclxuICAgICAgYm94LXNoYWRvdzogbm9uZTtcclxuICAgICAgb3V0bGluZTogbm9uZTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgaSB7XHJcbiAgICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZTtcclxuICAgICAgbGluZS1oZWlnaHQ6IDE7XHJcbiAgICB9XHJcbiAgICBcclxuICAgICY6aG92ZXIgaSB7XHJcbiAgICAgIGNvbG9yOiAjMzY5OWZmICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbi5lZGl0LWJ0biB7XHJcbiAgYmFja2dyb3VuZDogIzFiN2U2YztcclxuICBjb2xvcjogI2ZmZjtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xyXG4gIHBhZGRpbmc6IDAuMzVyZW0gMC43NXJlbTtcclxuICBmb250LXNpemU6IDAuODVyZW07XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDRweDtcclxuICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuMnMgZWFzZTtcclxufVxyXG5cclxuLmVkaXQtYnRuOmhvdmVyIHtcclxuICBiYWNrZ3JvdW5kOiAjMTY2MzU0O1xyXG59XHJcbi5wZXJtaXQtZGV0YWlscy1jb250ZW50IHtcclxuICBwYWRkaW5nOiAxcmVtIDEuNXJlbTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW07XHJcbiAgYm94LXNoYWRvdzogMCA0cHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xyXG4gIG1hcmdpbjogMXJlbSAwO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuXHJcbi5wZXJtaXQtZGV0YWlscy1ncmlkIHtcclxuICBkaXNwbGF5OiBncmlkO1xyXG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMmZyIDFmciAxZnIgMWZyOyAvKiBNYWtlIFByb2plY3QgTmFtZSB3aWRlciAqL1xyXG4gIGdhcDogMS41cmVtO1xyXG59XHJcblxyXG4vLyBOb3RlcyAmIEFjdGlvbnMgZnVsbC13aWR0aCBsYXlvdXRcclxuLm5vdGVzLWFjdGlvbnMtY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgZ2FwOiAxLjVyZW07XHJcbn1cclxuXHJcbi5wZXJtaXQtZGV0YWlsLWl0ZW0tZnVsbCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogMC40cmVtO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG5cclxuICBsYWJlbCB7XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICAgIGxpbmUtaGVpZ2h0OiAxLjI7XHJcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIGNvbG9yOiAjNmM3MjkzO1xyXG4gICAgdGV4dC10cmFuc2Zvcm06IGNhcGl0YWxpemU7XHJcbiAgICBsZXR0ZXItc3BhY2luZzogMC4xcmVtO1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gIH1cclxuXHJcbiAgLnBlcm1pdC12YWx1ZSB7XHJcbiAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICBjb2xvcjogIzNmNDI1NDtcclxuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICBwYWRkaW5nOiAwLjVyZW0gMDtcclxuICAgIGJvcmRlci1ib3R0b206IG5vbmU7XHJcbiAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7XHJcbiAgICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7XHJcbiAgfVxyXG59XHJcblxyXG4ucGVybWl0LWRldGFpbC1pdGVtIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgZ2FwOiAwLjRyZW07XHJcblxyXG4gIGxhYmVsIHtcclxuICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgbGluZS1oZWlnaHQ6IDEuMjtcclxuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgY29sb3I6ICMxMTExMTE7XHJcbiAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcclxuICAgIGxldHRlci1zcGFjaW5nOiAwLjFyZW07XHJcbiAgICBtYXJnaW46IDA7XHJcbiAgfVxyXG5cclxuICAucGVybWl0LXZhbHVlLFxyXG4gIC5wZXJtaXQtc3RhdHVzIHtcclxuICAgIG1hcmdpbi10b3A6IDAuMXJlbTtcclxuICB9XHJcblxyXG4gIC5wZXJtaXQtdmFsdWUge1xyXG4gICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgY29sb3I6ICMzZjQyNTQ7XHJcbiAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgcGFkZGluZzogMC41cmVtIDA7XHJcbiAgICBib3JkZXItYm90dG9tOiBub25lOyAvKiByZW1vdmUgZGl2aWRlciBsaW5lICovXHJcbiAgfVxyXG5cclxuICAucGVybWl0LXN0YXR1cyB7XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICAgIHZlcnRpY2FsLWFsaWduOiB0b3A7XHJcbiAgICBwYWRkaW5nOiAwLjVyZW0gMDsgLyogbWF0Y2ggc3BhY2luZyB3aXRoIG90aGVyIGZpZWxkcyAqL1xyXG4gICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIHRleHQtYWxpZ246IGxlZnQ7IC8qIGFsaWduIGZyb20gbGVmdCAqL1xyXG4gICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7IC8qIG5vIGJhZGdlIGJhY2tncm91bmQgKi9cclxuICAgIGJvcmRlcjogbm9uZTsgLyogbm8gYm9yZGVyICovXHJcbiAgICBtaW4td2lkdGg6IDA7IC8qIGFsbG93IG5hdHVyYWwgd2lkdGggKi9cclxuICAgIGJvcmRlci1yYWRpdXM6IDA7XHJcbiAgfVxyXG5cclxuICAvLyBTdGF0dXMgY29sb3IgdmFyaWF0aW9uc1xyXG4gIC5zdGF0dXMtYXBwcm92ZWQge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjVlODtcclxuICAgIGNvbG9yOiAjMWI1ZTIwO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2M4ZTZjOTtcclxuICB9XHJcblxyXG4gIC5zdGF0dXMtcGVuZGluZyB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmM2UwO1xyXG4gICAgY29sb3I6ICNlNjUxMDA7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjYzAyO1xyXG4gIH1cclxuXHJcbiAgLnN0YXR1cy1yZWplY3RlZCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlO1xyXG4gICAgY29sb3I6ICNjNjI4Mjg7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjZGQyO1xyXG4gIH1cclxuXHJcbiAgLnN0YXR1cy1zdWJtaXR0ZWQge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgIGNvbG9yOiAjMTU2NWMwO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2JiZGVmYjtcclxuICB9XHJcblxyXG4gIC5zdGF0dXMtdm9pZCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xyXG4gICAgY29sb3I6ICM3NTc1NzU7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xyXG4gIH1cclxuXHJcbiAgLnN0YXR1cy11bmtub3duIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XHJcbiAgICBjb2xvcjogIzc1NzU3NTtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgfVxyXG5cclxuICAuc3RhdHVzLXJlcXVpcmVzLXJlc3VibWl0IHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY4ZTE7XHJcbiAgICBjb2xvcjogI2Y1N2YxNztcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmVjYjM7XHJcbiAgfVxyXG5cclxuICAuc3RhdHVzLWNvbmRpdGlvbmFsLWFwcHJvdmFsIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmM2U1ZjU7XHJcbiAgICBjb2xvcjogIzdiMWZhMjtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMWJlZTc7XHJcbiAgfVxyXG5cclxuICAuc3RhdHVzLXVuZGVyLXJldmlldyB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThlYWY2O1xyXG4gICAgY29sb3I6ICMzOTQ5YWI7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjYzVjYWU5O1xyXG4gIH1cclxufVxyXG5cclxuLy8gTG9hZGluZyBTZWN0aW9uXHJcbi5sb2FkaW5nLXNlY3Rpb24ge1xyXG4gIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgYm9yZGVyLXJhZGl1czogLjQ3NXJlbTtcclxuICBwYWRkaW5nOiAycmVtO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZTVlYWVlO1xyXG5cclxuICAuc3Bpbm5lci1ib3JkZXIge1xyXG4gICAgd2lkdGg6IDNyZW07XHJcbiAgICBoZWlnaHQ6IDNyZW07XHJcbiAgfVxyXG5cclxuICBwIHtcclxuICAgIGNvbG9yOiAjNmM3MjkzO1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gIH1cclxuXHJcbiAgLmxvYWRpbmctdGlwcyB7XHJcbiAgICAudGV4dC1tdXRlZCB7XHJcbiAgICAgIGNvbG9yOiAjYTFhNWI3ICFpbXBvcnRhbnQ7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcblxyXG4gICAgICBpIHtcclxuICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcclxuICAgICAgICBjb2xvcjogIzM2OTlmZjtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuXHJcblxyXG4vLyBObyBQZXJtaXQgRGF0YSBNZXNzYWdlXHJcbi5uby1wZXJtaXQtZGF0YSB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuXHJcbiAgLmFsZXJ0IHtcclxuICAgIGJvcmRlci1yYWRpdXM6IC40NzVyZW07XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlYWVlO1xyXG5cclxuICAgIGg1IHtcclxuICAgICAgbWFyZ2luOiAwIDAgMC41cmVtIDA7XHJcbiAgICAgIGNvbG9yOiAjM2Y0MjU0O1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgfVxyXG5cclxuICAgIHAge1xyXG4gICAgICBtYXJnaW46IDAgMCAwLjVyZW0gMDtcclxuICAgICAgY29sb3I6ICM2YzcyOTM7XHJcbiAgICB9XHJcblxyXG4gICAgdWwge1xyXG4gICAgICBtYXJnaW46IDAgMCAxcmVtIDA7XHJcbiAgICAgIHBhZGRpbmctbGVmdDogMS41cmVtO1xyXG4gICAgICBjb2xvcjogIzZjNzI5MztcclxuXHJcbiAgICAgIGxpIHtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLmJ0biB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xyXG5cclxuICAgICAgaSB7XHJcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiAwLjVyZW07XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIFRhYmxlIFN0eWxpbmcgZm9yIFJldmlld3NcclxuLnRhYmxlLXJlc3BvbnNpdmUge1xyXG4gIG92ZXJmbG93LXg6IGF1dG87XHJcbiAgcGFkZGluZzogMCAwLjVyZW0gMCAwLjVyZW07XHJcbiAgbWFyZ2luLXRvcDogMDtcclxufVxyXG5cclxuLy8gUmVtb3ZlIHRvcCBwYWRkaW5nIGZyb20gZXh0ZXJuYWwgcmV2aWV3cyB0YWJsZVxyXG4uZXh0ZXJuYWwtcmV2aWV3cy10YWJsZSB7XHJcbiAgcGFkZGluZy10b3A6IDAgIWltcG9ydGFudDtcclxuICBtYXJnaW4tdG9wOiAwICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi8vIFJlbW92ZSB0b3AgcGFkZGluZyBmcm9tIHRoZSBjYXJkIGJvZHkgY29udGFpbmluZyB0aGUgZXh0ZXJuYWwgcmV2aWV3c1xyXG4uY2FyZC1ib2R5IHtcclxuICBwYWRkaW5nLXRvcDogMCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gIG1hcmdpbi1ib3R0b206IDA7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuXHJcbiAgLmF1ZGl0LXRhYmxlLXJvdyxcclxuICAuZXh0ZXJuYWwtc3VibWl0dGFsLXJvdyB7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWVhZWU7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMC41cmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG4gICAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xyXG4gICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcclxuICAgIH1cclxuXHJcbiAgICAmLmV4cGFuZGVkIHtcclxuICAgICAgYmFja2dyb3VuZDogI2UzZjJmZDtcclxuICAgICAgYm9yZGVyLWNvbG9yOiAjMjE5NmYzO1xyXG4gICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgzMywgMTUwLCAyNDMsIDAuMik7XHJcbiAgICB9XHJcblxyXG4gICAgJi50YWJsZS1hY3RpdmUge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTNmMmZkO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmF1ZGl0LXRpdGxlLWNlbGwsXHJcbiAgLnN1Ym1pdHRhbC10aXRsZS1jZWxsIHtcclxuICAgIHdpZHRoOiA3NSU7XHJcbiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xyXG5cclxuICAgIC5hdWRpdC1zdGF0dXMtYmFkZ2UsXHJcbiAgICAuc3VibWl0dGFsLXN0YXR1cy1iYWRnZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC43cmVtO1xyXG4gICAgICBwYWRkaW5nOiAwLjJyZW0gMC40cmVtO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDtcclxuICAgICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuMDVyZW07XHJcbiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICAgICAgbWluLXdpZHRoOiA2MHB4O1xyXG4gICAgICBcclxuICAgICAgLy8gRW5zdXJlIHN0YXR1cyBjbGFzc2VzIGFyZSBhcHBsaWVkIHdpdGggaGlnaGVyIHNwZWNpZmljaXR5XHJcbiAgICAgICYuc3RhdHVzLTEtY3ljbGUtY29tcGxldGVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgY29sb3I6ICMyZTdkMzIgIWltcG9ydGFudDtcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjYTVkNmE3ICFpbXBvcnRhbnQ7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgICYuc3RhdHVzLXBhY2lmaWNhLXZlcmlmaWNhdGlvbiB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UxZjVmZSAhaW1wb3J0YW50O1xyXG4gICAgICAgIGNvbG9yOiAjMDI3N2JkICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzgxZDRmYSAhaW1wb3J0YW50O1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAmLnN0YXR1cy1kaXMtYXBwcm92ZWQge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmViZWUgIWltcG9ydGFudDtcclxuICAgICAgICBjb2xvcjogI2M2MjgyOCAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDIgIWltcG9ydGFudDtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgJi5zdGF0dXMtbm90LXJlcXVpcmVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgY29sb3I6ICM3NTc1NzUgIWltcG9ydGFudDtcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwICFpbXBvcnRhbnQ7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgICYuc3RhdHVzLWluLXJldmlldyB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZWFmNiAhaW1wb3J0YW50O1xyXG4gICAgICAgIGNvbG9yOiAjMzk0OWFiICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M1Y2FlOSAhaW1wb3J0YW50O1xyXG4gICAgICB9XHJcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuXHJcbiAgICAgIC8vIFN0YXR1cyBjb2xvciB2YXJpYXRpb25zXHJcbiAgICAgICYuc3RhdHVzLWFwcHJvdmVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xyXG4gICAgICAgIGNvbG9yOiAjMWI1ZTIwO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuc3RhdHVzLXBlbmRpbmcge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzZTA7XHJcbiAgICAgICAgY29sb3I6ICNlNjUxMDA7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2ZmY2MwMjtcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5zdGF0dXMtdW5kZXItcmV2aWV3IHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThlYWY2O1xyXG4gICAgICAgIGNvbG9yOiAjMzk0OWFiO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjNWNhZTk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuc3RhdHVzLXJlamVjdGVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlO1xyXG4gICAgICAgIGNvbG9yOiAjYzYyODI4O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDI7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuc3RhdHVzLXJlcXVpcmVzLXJlLXN1Ym1pdCB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjhlMTtcclxuICAgICAgICBjb2xvcjogI2Y1N2YxNztcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZlY2IzO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmLnN0YXR1cy1hcHByb3ZlZC13LWNvbmRpdGlvbnMge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmM2U1ZjU7XHJcbiAgICAgICAgY29sb3I6ICM3YjFmYTI7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UxYmVlNztcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5zdGF0dXMtY29tcGxldGUge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTg7XHJcbiAgICAgICAgY29sb3I6ICMxYjVlMjA7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M4ZTZjOTtcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5zdGF0dXMtbi1hIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xyXG4gICAgICAgIGNvbG9yOiAjNzU3NTc1O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEludGVybmFsIFZlcmlmaWNhdGlvbiBTdGF0dXMgc3R5bGVzXHJcbiAgICAgICYuc3RhdHVzLXBlbmRpbmcge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzZTA7XHJcbiAgICAgICAgY29sb3I6ICNlNjUxMDA7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2ZmY2MwMjtcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5zdGF0dXMtaW4tcHJvZ3Jlc3Mge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlM2YyZmQ7XHJcbiAgICAgICAgY29sb3I6ICMxNTY1YzA7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzkwY2FmOTtcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5zdGF0dXMtY29tcGxldGVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xyXG4gICAgICAgIGNvbG9yOiAjMWI1ZTIwO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuc3RhdHVzLXZlcmlmaWVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xyXG4gICAgICAgIGNvbG9yOiAjMmU3ZDMyO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNhNWQ2YTc7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuc3RhdHVzLXJlamVjdGVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlO1xyXG4gICAgICAgIGNvbG9yOiAjYzYyODI4O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDI7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuc3RhdHVzLWFwcHJvdmVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xyXG4gICAgICAgIGNvbG9yOiAjMWI1ZTIwO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuc3RhdHVzLXVuZGVyLXJldmlldyB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZWFmNjtcclxuICAgICAgICBjb2xvcjogIzM5NDlhYjtcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjYzVjYWU5O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmLnN0YXR1cy1yZXF1aXJlcy1yZXN1Ym1pdCB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjhlMTtcclxuICAgICAgICBjb2xvcjogI2Y1N2YxNztcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZlY2IzO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmLnN0YXR1cy1vbi1ob2xkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNlNWY1O1xyXG4gICAgICAgIGNvbG9yOiAjN2IxZmEyO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMWJlZTc7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEFkZGl0aW9uYWwgc3BlY2lmaWMgc3RhdHVzIHN0eWxlc1xyXG4gICAgICAmLnN0YXR1cy1wYWNpZmljYS12ZXJpZmljYXRpb24ge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlMWY1ZmUgIWltcG9ydGFudDtcclxuICAgICAgICBjb2xvcjogIzAyNzdiZCAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICM4MWQ0ZmEgIWltcG9ydGFudDtcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5zdGF0dXMtZGlzLWFwcHJvdmVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgY29sb3I6ICNjNjI4MjggIWltcG9ydGFudDtcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjZGQyICFpbXBvcnRhbnQ7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuc3RhdHVzLW5vdC1yZXF1aXJlZCB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNSAhaW1wb3J0YW50O1xyXG4gICAgICAgIGNvbG9yOiAjNzU3NTc1ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMCAhaW1wb3J0YW50O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmLnN0YXR1cy1pbi1yZXZpZXcge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjYgIWltcG9ydGFudDtcclxuICAgICAgICBjb2xvcjogIzM5NDlhYiAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjNWNhZTkgIWltcG9ydGFudDtcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5zdGF0dXMtMS1jeWNsZS1jb21wbGV0ZWQge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTggIWltcG9ydGFudDtcclxuICAgICAgICBjb2xvcjogIzJlN2QzMiAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNhNWQ2YTcgIWltcG9ydGFudDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmF1ZGl0LWRhdGVzLWNlbGwsXHJcbiAgLnN1Ym1pdHRhbC1kYXRlcy1jZWxsIHtcclxuICAgIHdpZHRoOiAyNSU7XHJcbiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xyXG4gICAgdGV4dC1hbGlnbjogcmlnaHQ7XHJcbiAgICBwYWRkaW5nLXJpZ2h0OiAwO1xyXG5cclxuICAgIC5kLWZsZXgge1xyXG4gICAgICBnYXA6IDFyZW07XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7XHJcbiAgICB9XHJcblxyXG4gICAgLmRhdGUtaXRlbSB7XHJcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgd2lkdGg6IDEyMHB4OyAvKiBmaXhlZCB3aWR0aCB0byBhbGlnbiBjb2x1bW5zICovXHJcbiAgICAgIG1pbi13aWR0aDogMTIwcHg7XHJcbiAgICAgIG1heC13aWR0aDogMTIwcHg7XHJcblxyXG4gICAgICBzbWFsbCB7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjdyZW07XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICBkaXNwbGF5OiBibG9jazsgLyogZW5zdXJlIGxhYmVsIHRha2VzIGl0cyBvd24gbGluZSAqL1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuZnctbWVkaXVtIHtcclxuICAgICAgICBmb250LXNpemU6IDAuODVyZW07XHJcbiAgICAgICAgZGlzcGxheTogYmxvY2s7IC8qIGZvcmNlIGRhdGUgdGV4dCB0byBhbGlnbiB2ZXJ0aWNhbGx5ICovXHJcbiAgICAgICAgbWluLWhlaWdodDogMS4yNXJlbTsgLyogcmVzZXJ2ZSBzcGFjZSB3aGVuIGVtcHR5ICovXHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG5cclxuICAuYXVkaXQtYWN0aW9ucy1jZWxsIHtcclxuICAgIHdpZHRoOiAxNSU7XHJcbiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG5cclxuICAgIC5hY3Rpb24taWNvbiB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgY29sb3I6ICMzNjk5ZmY7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4ycyBlYXNlLCB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgY29sb3I6ICMyYjdjZTA7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmLmVkaXQtaWNvbiB7XHJcbiAgICAgICAgY29sb3I6ICMzNjk5ZmY7XHJcblxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgY29sb3I6ICMyYjdjZTA7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuYWNjb3JkaW9uLXRvZ2dsZSB7XHJcbiAgICBjb2xvcjogIzM2OTlmZjtcclxuICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICB3aWR0aDogMjBweDtcclxuICAgIGhlaWdodDogMjBweDtcclxuXHJcbiAgICBpIHtcclxuICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5hY2NvcmRpb24tY29udGVudC1yb3cge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmZiAwJSwgI2U4ZjJmZiAxMDAlKTtcclxuICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM2OTlmZjtcclxuICAgIGJveC1zaGFkb3c6IGluc2V0IDAgMnB4IDRweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4xKTtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcblxyXG4gICAgLmFjY29yZGlvbi1jb250ZW50IHtcclxuICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNkMWU3ZmY7XHJcbiAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG5cclxuICAgICAgLnJldmlld3MtY29udGFpbmVyIHtcclxuICAgICAgICBtYXgtaGVpZ2h0OiA0MDBweDtcclxuICAgICAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuICAgICAgICBtYXJnaW46IDAuNXJlbTtcclxuICAgICAgICBwYWRkaW5nOiAxcmVtO1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4xNSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIEhpZ2hsaWdodCB0aGUgcGFyZW50IHN1Ym1pdHRhbCByb3cgd2hlbiBleHBhbmRlZFxyXG4gIC5leHRlcm5hbC1zdWJtaXR0YWwtcm93OmhhcygrIC5hY2NvcmRpb24tY29udGVudC1yb3c6bm90KC5kLW5vbmUpKSB7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjBmOGZmIDAlLCAjZThmMmZmIDEwMCUpO1xyXG4gICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjIpO1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgIG1hcmdpbi1yaWdodDogMDtcclxuXHJcbiAgICAuc3VibWl0dGFsLXRpdGxlLWNlbGwge1xyXG4gICAgICAuYWNjb3JkaW9uLXRvZ2dsZSB7XHJcbiAgICAgICAgY29sb3I6ICMyYjdjZTA7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBBZGQgc3VidGxlIGFuaW1hdGlvbiBmb3IgZXhwYW5zaW9uXHJcbiAgLmV4dGVybmFsLXN1Ym1pdHRhbC1yb3cge1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5zZWN0aW9uLWhlYWRlciB7XHJcbiAgcGFkZGluZzogMXJlbSAxLjVyZW07XHJcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWVhZWU7XHJcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICBib3JkZXItcmFkaXVzOiAuNDc1cmVtIC40NzVyZW0gMCAwO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcblxyXG4gIGg1IHtcclxuICAgIG1hcmdpbjogMDtcclxuICAgIGNvbG9yOiAjM2Y0MjU0O1xyXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICB9XHJcblxyXG4gIC5zZWN0aW9uLWFjdGlvbnMge1xyXG4gICAgLmJ0biB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgcGFkZGluZzogMC4zNzVyZW0gMC43NXJlbTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5zdWJtaXR0YWwtc3RhdHVzIHtcclxuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcclxuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDtcclxuXHJcbiAgICAvLyBTdGF0dXMgY29sb3IgdmFyaWF0aW9ucyBmb3Igc3VibWl0dGFsIHN0YXR1c1xyXG4gICAgJi5zdGF0dXMtYXBwcm92ZWQge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xyXG4gICAgICBjb2xvcjogIzFiNWUyMDtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M4ZTZjOTtcclxuICAgIH1cclxuXHJcbiAgICAmLnN0YXR1cy1wZW5kaW5nIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjNlMDtcclxuICAgICAgY29sb3I6ICNlNjUxMDA7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNjMDI7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtdW5kZXItcmV2aWV3IHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZWFmNjtcclxuICAgICAgY29sb3I6ICMzOTQ5YWI7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjNWNhZTk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTWFwIEFQSSBzdGF0dXMgXCJJbiBSZXZpZXdcIiDDosKGwpIgY2xhc3MgXCJzdGF0dXMtaW4tcmV2aWV3XCJcclxuICAgICYuc3RhdHVzLWluLXJldmlldyB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjY7XHJcbiAgICAgIGNvbG9yOiAjMzk0OWFiO1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjYzVjYWU5O1xyXG4gICAgfVxyXG5cclxuICAgICYuc3RhdHVzLXJlamVjdGVkIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZWJlZTtcclxuICAgICAgY29sb3I6ICNjNjI4Mjg7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDI7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtcmVxdWlyZXMtcmUtc3VibWl0IHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjhlMTtcclxuICAgICAgY29sb3I6ICNmNTdmMTc7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmVjYjM7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtYXBwcm92ZWQtdy1jb25kaXRpb25zIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2YzZTVmNTtcclxuICAgICAgY29sb3I6ICM3YjFmYTI7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMWJlZTc7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtY29tcGxldGUge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xyXG4gICAgICBjb2xvcjogIzFiNWUyMDtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M4ZTZjOTtcclxuICAgIH1cclxuXHJcbiAgICAmLnN0YXR1cy1uLWEge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xyXG4gICAgICBjb2xvcjogIzc1NzU3NTtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIFJldmlld3MgQ29udGFpbmVyIFN0eWxpbmcgKGZvciBhY2NvcmRpb24gY29udGVudClcclxuLnJldmlld3MtY29udGFpbmVyIHtcclxuICBtYXgtaGVpZ2h0OiA0MDBweDtcclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG59XHJcblxyXG5cclxuLy8gU3RhdHVzIENvbG9ycyBmb3IgQXVkaXQgRW50cmllcyAtIEdsb2JhbCBzdHlsZXNcclxuLnN0YXR1cy1hcHByb3ZlZCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjVlOCAhaW1wb3J0YW50O1xyXG4gIGNvbG9yOiAjMWI1ZTIwICFpbXBvcnRhbnQ7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgI2M4ZTZjOSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uc3RhdHVzLXBlbmRpbmcge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmYzZTAgIWltcG9ydGFudDtcclxuICBjb2xvcjogI2U2NTEwMCAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNmZmNjMDIgIWltcG9ydGFudDtcclxufVxyXG5cclxuLnN0YXR1cy1yZXF1aXJlcy1yZXN1Ym1pdCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZWJlZSAhaW1wb3J0YW50O1xyXG4gIGNvbG9yOiAjYzYyODI4ICFpbXBvcnRhbnQ7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgI2ZmY2RkMiAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uc3RhdHVzLXVuZGVyLXJldmlldyB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZCAhaW1wb3J0YW50O1xyXG4gIGNvbG9yOiAjMTU2NWMwICFpbXBvcnRhbnQ7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgI2JiZGVmYiAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uc3RhdHVzLWNvbXBsZXRlIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4ICFpbXBvcnRhbnQ7XHJcbiAgY29sb3I6ICMxYjVlMjAgIWltcG9ydGFudDtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjYzhlNmM5ICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi8vIFNwZWNpZmljIHN0YXR1cyBjbGFzc2VzIGZvciBpbnRlcm5hbCByZXZpZXdzXHJcbi5zdGF0dXMtMS1jeWNsZS1jb21wbGV0ZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTggIWltcG9ydGFudDtcclxuICBjb2xvcjogIzJlN2QzMiAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNhNWQ2YTcgIWltcG9ydGFudDtcclxufVxyXG5cclxuLnN0YXR1cy1wYWNpZmljYS12ZXJpZmljYXRpb24ge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlMWY1ZmUgIWltcG9ydGFudDtcclxuICBjb2xvcjogIzAyNzdiZCAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICM4MWQ0ZmEgIWltcG9ydGFudDtcclxufVxyXG5cclxuLnN0YXR1cy1kaXMtYXBwcm92ZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmViZWUgIWltcG9ydGFudDtcclxuICBjb2xvcjogI2M2MjgyOCAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDIgIWltcG9ydGFudDtcclxufVxyXG5cclxuLnN0YXR1cy1ub3QtcmVxdWlyZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjUgIWltcG9ydGFudDtcclxuICBjb2xvcjogIzc1NzU3NSAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTAgIWltcG9ydGFudDtcclxufVxyXG5cclxuLnN0YXR1cy1pbi1yZXZpZXcge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjYgIWltcG9ydGFudDtcclxuICBjb2xvcjogIzM5NDlhYiAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNjNWNhZTkgIWltcG9ydGFudDtcclxufVxyXG5cclxuLy8gUmV2aWV3cyBDb250YWluZXIgU3R5bGluZ1xyXG4ucmV2aWV3cy1jb250YWluZXIge1xyXG4gIG1heC1oZWlnaHQ6IDUwMHB4O1xyXG4gIG92ZXJmbG93LXk6IGF1dG87XHJcbiAgcGFkZGluZzogMC43NXJlbTtcclxufVxyXG5cclxuLnJldmlldy1pdGVtIHtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZWFlZTtcclxuICBwYWRkaW5nOiAwLjVyZW0gMDtcclxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnMgZWFzZTtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gIH1cclxuXHJcbiAgJjpsYXN0LWNoaWxkIHtcclxuICAgIGJvcmRlci1ib3R0b206IG5vbmU7XHJcbiAgfVxyXG59XHJcblxyXG4ucmV2aWV3LXNpbmdsZS1saW5lIHtcclxuICBkaXNwbGF5OiBncmlkO1xyXG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMjBweCAxZnIgMTIwcHggMTIwcHggMTQwcHggMTQwcHggMzBweDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogMC43NXJlbTtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgcGFkZGluZzogMC41cmVtO1xyXG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgfVxyXG5cclxuICAmLmV4cGFuZGVkIHtcclxuICAgIGJhY2tncm91bmQ6ICNlM2YyZmQ7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjMjE5NmYzO1xyXG4gIH1cclxuXHJcbiAgLnJldmlldy10aXRsZSB7XHJcbiAgICBtYXJnaW46IDA7XHJcbiAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICBjb2xvcjogIzNmNDI1NDtcclxuICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcclxuICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XHJcbiAgfVxyXG5cclxuICAucmV2aWV3LXN0YXR1cy1jb250YWluZXIsXHJcbiAgLnJldmlld2VyLWNvbnRhaW5lcixcclxuICAuZHVlLWRhdGUtY29udGFpbmVyLFxyXG4gIC5jb21wbGV0ZWQtZGF0ZS1jb250YWluZXIsXHJcbiAgLnJldmlldy1hY3Rpb25zLWNvbnRhaW5lciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIG1pbi1oZWlnaHQ6IDIwcHg7XHJcbiAgfVxyXG5cclxuICAucmV2aWV3LWFjdGlvbnMtY29udGFpbmVyIHtcclxuICAgIGdhcDogMC41cmVtO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDtcclxuICB9XHJcblxyXG4gIC5yZXZpZXctc3RhdHVzIHtcclxuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcclxuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDtcclxuICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XHJcbiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcblxyXG4gICAgLy8gU3RhdHVzIGNvbG9yIHZhcmlhdGlvbnMgZm9yIHJldmlldyBzdGF0dXNcclxuICAgICYuc3RhdHVzLWFwcHJvdmVkIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjVlODtcclxuICAgICAgY29sb3I6ICMxYjVlMjA7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtcGVuZGluZyB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzZTA7XHJcbiAgICAgIGNvbG9yOiAjZTY1MTAwO1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjYzAyO1xyXG4gICAgfVxyXG5cclxuICAgICYuc3RhdHVzLXVuZGVyLXJldmlldyB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjY7XHJcbiAgICAgIGNvbG9yOiAjMzk0OWFiO1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjYzVjYWU5O1xyXG4gICAgfVxyXG5cclxuICAgICYuc3RhdHVzLXJlamVjdGVkIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZWJlZTtcclxuICAgICAgY29sb3I6ICNjNjI4Mjg7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDI7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtcmVxdWlyZXMtcmUtc3VibWl0IHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjhlMTtcclxuICAgICAgY29sb3I6ICNmNTdmMTc7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmVjYjM7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtYXBwcm92ZWQtdy1jb25kaXRpb25zIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2YzZTVmNTtcclxuICAgICAgY29sb3I6ICM3YjFmYTI7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMWJlZTc7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtY29tcGxldGUge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xyXG4gICAgICBjb2xvcjogIzFiNWUyMDtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M4ZTZjOTtcclxuICAgIH1cclxuXHJcbiAgICAmLnN0YXR1cy1uLWEge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xyXG4gICAgICBjb2xvcjogIzc1NzU3NTtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBBZGRpdGlvbmFsIHN0YXR1cyB2YXJpYXRpb25zIGZvciBjb21wcmVoZW5zaXZlIGNvdmVyYWdlXHJcbiAgICAmLnN0YXR1cy1zdWJtaXR0ZWQge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTNmMmZkO1xyXG4gICAgICBjb2xvcjogIzE1NjVjMDtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2JiZGVmYjtcclxuICAgIH1cclxuXHJcbiAgICAmLnN0YXR1cy12b2lkIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcclxuICAgICAgY29sb3I6ICM3NTc1NzU7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgICB9XHJcblxyXG4gICAgJi5zdGF0dXMtY29uZGl0aW9uYWwtYXBwcm92YWwge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNlNWY1O1xyXG4gICAgICBjb2xvcjogIzdiMWZhMjtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UxYmVlNztcclxuICAgIH1cclxuXHJcbiAgICAmLnN0YXR1cy1yZXF1aXJlcy1yZXN1Ym1pdCB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY4ZTE7XHJcbiAgICAgIGNvbG9yOiAjZjU3ZjE3O1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZlY2IzO1xyXG4gICAgfVxyXG5cclxuICAgICYuc3RhdHVzLXVua25vd24ge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xyXG4gICAgICBjb2xvcjogIzc1NzU3NTtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5yZXZpZXdlciB7XHJcbiAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgIGNvbG9yOiAjNmM3MjkzO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xyXG4gICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICB9XHJcblxyXG4gIC5kdWUtZGF0ZSB7XHJcbiAgICBmb250LXNpemU6IDAuN3JlbTtcclxuICAgIGNvbG9yOiAjNmM3MjkzO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xyXG4gICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICB9XHJcblxyXG4gIC5jb21wbGV0ZWQtZGF0ZSB7XHJcbiAgICBmb250LXNpemU6IDAuN3JlbTtcclxuICAgIGNvbG9yOiAjNmM3MjkzO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xyXG4gICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICB9XHJcblxyXG4gIC5hY3Rpb24taWNvbiB7XHJcbiAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICBjb2xvcjogIzM2OTlmZjtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZSwgdHJhbnNmb3JtIDAuMnMgZWFzZTtcclxuICAgIG1hcmdpbi1sZWZ0OiAwLjVyZW07XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGNvbG9yOiAjMmI3Y2UwO1xyXG4gICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICB9XHJcblxyXG4gICAgJi5lZGl0LWljb24ge1xyXG4gICAgICBjb2xvcjogIzM2OTlmZjtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIGNvbG9yOiAjMmI3Y2UwO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgJi52aWV3LWljb24ge1xyXG4gICAgICBjb2xvcjogIzZjNzI5MztcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIGNvbG9yOiAjMzY5OWZmO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBSZXZpZXcgYWNjb3JkaW9uIHRvZ2dsZSBpY29uXHJcbiAgLnJldmlldy1hY2NvcmRpb24tdG9nZ2xlIHtcclxuICAgIGNvbG9yOiAjMzY5OWZmO1xyXG4gICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIHdpZHRoOiAyMHB4O1xyXG4gICAgaGVpZ2h0OiAyMHB4O1xyXG5cclxuICAgIGkge1xyXG4gICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gRWRpdCBSZXZpZXcgaWNvblxyXG4gIC5lZGl0LXJldmlldy1pY29uIHtcclxuICAgIGNvbG9yOiAjMjhhNzQ1O1xyXG4gICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgIHBhZGRpbmc6IDAuMjVyZW07XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGNvbG9yOiAjMWU3ZTM0O1xyXG4gICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICB9XHJcblxyXG4gICAgJjphY3RpdmUge1xyXG4gICAgICB0cmFuc2Zvcm06IHNjYWxlKDAuOTUpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gRG93bmxvYWQgUERGIGljb25cclxuICAuZG93bmxvYWQtcGRmLWljb24ge1xyXG4gICAgY29sb3I6ICMzNjk5ZmY7XHJcbiAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG4gICAgcGFkZGluZzogMC4yNXJlbTtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgY29sb3I6ICMyYjdjZTA7XHJcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcclxuICAgIH1cclxuXHJcbiAgICAmOmFjdGl2ZSB7XHJcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMC45NSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBJbnRlcm5hbCByZXZpZXdzIGRvd25sb2FkIGJ1dHRvbiBzdHlsaW5nXHJcbiAgLmJ1dHRvbi1ncm91cCAuYnRuLWxpbmsge1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgYmFja2dyb3VuZDogbm9uZTtcclxuICAgIGJveC1zaGFkb3c6IG5vbmU7XHJcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgICBcclxuICAgICY6aG92ZXIsICY6Zm9jdXMsICY6YWN0aXZlIHtcclxuICAgICAgYmFja2dyb3VuZDogbm9uZTtcclxuICAgICAgYm94LXNoYWRvdzogbm9uZTtcclxuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAmOmRpc2FibGVkIHtcclxuICAgICAgb3BhY2l0eTogMC41O1xyXG4gICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG4gICAgICBcclxuICAgICAgLmRvd25sb2FkLXBkZi1pY29uIHtcclxuICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG4gICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgIFxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgdHJhbnNmb3JtOiBub25lO1xyXG4gICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBFeHRlcm5hbCBSZXZpZXdzIENhcmQgU3R5bGluZ1xyXG4gIC5leHRlcm5hbC1yZXZpZXdzLWNhcmQge1xyXG4gICAgLmNhcmQge1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlYWVlO1xyXG4gICAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gICAgfVxyXG5cclxuICAgIC5jYXJkLWJvZHkge1xyXG4gICAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLnRhYmxlIHtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIFJldmlldyBkZXRhaWxzIGFjY29yZGlvblxyXG4gIC5yZXZpZXctZGV0YWlscy1hY2NvcmRpb24ge1xyXG4gICAgbWFyZ2luLXRvcDogMXJlbTtcclxuICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTVlYWVlO1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmZiAwJSwgI2U4ZjJmZiAxMDAlKTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xyXG4gICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggNHB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjEpO1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAucmV2aWV3LWRldGFpbHMtY29udGVudCB7XHJcbiAgICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XHJcbiAgICAgIG1hcmdpbjogMC41cmVtO1xyXG4gICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuMTUpO1xyXG5cclxuICAgICAgLy8gRW5zdXJlIGNvcnJlY3Rpb25zIHNlY3Rpb24gc3R5bGVzIGFyZSBhcHBsaWVkIHdpdGhpbiByZXZpZXctZGV0YWlscy1jb250ZW50XHJcbiAgICAgIDo6bmctZGVlcCAuY29ycmVjdGlvbnMtc2VjdGlvbixcclxuICAgICAgLmNvcnJlY3Rpb25zLXNlY3Rpb24ge1xyXG4gICAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmYgIWltcG9ydGFudDtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAwLjc1cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgcGFkZGluZzogMnJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWVhZWUgIWltcG9ydGFudDtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4wOCkgIWltcG9ydGFudDtcclxuICAgICAgICBtYXJnaW46IDAgIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgLnNlY3Rpb24tdGl0bGUge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxLjRyZW0gIWltcG9ydGFudDtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA4MDAgIWltcG9ydGFudDtcclxuICAgICAgICAgIGNvbG9yOiAjMWEyMDJjICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAycmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogMXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgYm9yZGVyLWJvdHRvbTogM3B4IHNvbGlkICMzNjk5ZmYgIWltcG9ydGFudDtcclxuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgZ2FwOiAxcmVtICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAgICAgJjo6YmVmb3JlIHtcclxuICAgICAgICAgICAgY29udGVudDogJ8Owwp/Ck8KLJztcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxLjhyZW07XHJcbiAgICAgICAgICAgIGZpbHRlcjogZHJvcC1zaGFkb3coMCAycHggNHB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjMpKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmOjphZnRlciB7XHJcbiAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIGJvdHRvbTogLTNweDtcclxuICAgICAgICAgICAgbGVmdDogMDtcclxuICAgICAgICAgICAgd2lkdGg6IDYwcHg7XHJcbiAgICAgICAgICAgIGhlaWdodDogM3B4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNjk5ZmYsICMxYmM1YmQpO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC40KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5jb3JyZWN0aW9uLWl0ZW0ge1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogI2ZmZiAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgcGFkZGluZzogMS41cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCkgIWltcG9ydGFudDtcclxuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMnJlbSAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIHRvcDogMDtcclxuICAgICAgICAgICAgbGVmdDogMDtcclxuICAgICAgICAgICAgd2lkdGg6IDRweDtcclxuICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjMzY5OWZmLCAjMWJjNWJkKTtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAwIDAgMC43NXJlbTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpO1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjE1KTtcclxuXHJcbiAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzFiYzViZCwgIzM2OTlmZik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAuY29ycmVjdGlvbi1oZWFkZXIge1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgICAgICAgICAgZ2FwOiAxLjI1cmVtO1xyXG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcbiAgICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAxcmVtO1xyXG4gICAgICAgICAgICBib3JkZXItYm90dG9tOiAycHggc29saWQgI2YxZjNmNDtcclxuICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG5cclxuICAgICAgICAgICAgJjo6YWZ0ZXIge1xyXG4gICAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICAgICAgICBib3R0b206IC0ycHg7XHJcbiAgICAgICAgICAgICAgbGVmdDogMDtcclxuICAgICAgICAgICAgICB3aWR0aDogNTBweDtcclxuICAgICAgICAgICAgICBoZWlnaHQ6IDJweDtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNjk5ZmYsICMxYmM1YmQpO1xyXG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDFweDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLmNvcnJlY3Rpb24tbnVtYmVyIHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzY5OWZmLCAjMWJjNWJkKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgIHdpZHRoOiAzMnB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgaGVpZ2h0OiAzMnB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlciAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDgwMCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuNCkgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICBmbGV4LXNocmluazogMCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UgIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgICAgICAgJjo6YmVmb3JlIHtcclxuICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICAgICAgdG9wOiAtMnB4O1xyXG4gICAgICAgICAgICAgICAgbGVmdDogLTJweDtcclxuICAgICAgICAgICAgICAgIHJpZ2h0OiAtMnB4O1xyXG4gICAgICAgICAgICAgICAgYm90dG9tOiAtMnB4O1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM2OTlmZiwgIzFiYzViZCk7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgICAgICAgICB6LWluZGV4OiAtMTtcclxuICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuMztcclxuICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogcHVsc2UgMnMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICBAa2V5ZnJhbWVzIHB1bHNlIHtcclxuICAgICAgICAgICAgICAgIDAlLCAxMDAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxKTsgb3BhY2l0eTogMC4zOyB9XHJcbiAgICAgICAgICAgICAgICA1MCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7IG9wYWNpdHk6IDAuNjsgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLmNvcnJlY3Rpb24tbWV0YSB7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgICAgICAgIGdhcDogMC43NXJlbTtcclxuICAgICAgICAgICAgICBmbGV4OiAxO1xyXG5cclxuICAgICAgICAgICAgICAubWV0YS1yb3cge1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgZ2FwOiAwLjc1cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjVyZW0gMCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZSAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBub25lO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC5tZXRhLWxhYmVsIHtcclxuICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xyXG4gICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogIzRhNTU2ODtcclxuICAgICAgICAgICAgICAgICAgdGV4dC10cmFuc2Zvcm06IGNhcGl0YWxpemU7XHJcbiAgICAgICAgICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAwLjA1cmVtO1xyXG4gICAgICAgICAgICAgICAgICBtaW4td2lkdGg6IDgwcHg7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLm1ldGEtdmFsdWUge1xyXG4gICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgICAgICAgICAgY29sb3I6ICMyZDM3NDg7XHJcbiAgICAgICAgICAgICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLnJlc29sdmVkLWRhdGUge1xyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogaW5oZXJpdDtcclxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuMjVyZW0gMDtcclxuICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMDtcclxuICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAuY29ycmVjdGlvbi1jb250ZW50IHtcclxuICAgICAgICAgICAgLmNvcnJlY3Rpb24tZmllbGQge1xyXG4gICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcclxuICAgICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgICAgICAgICAgICY6bGFzdC1jaGlsZCB7XHJcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgLmZpZWxkLWxhYmVsIHtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcclxuICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA4MDA7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogIzJkMzc0ODtcclxuICAgICAgICAgICAgICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xyXG4gICAgICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuMTVyZW07XHJcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICBnYXA6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgICAgICAgICAgICAgaSB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAjMzY5OWZmO1xyXG4gICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICAgICAgICAgICAgd2lkdGg6IDE2cHg7XHJcbiAgICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICAgICAgICBjb250ZW50OiAnw6LClsK2JztcclxuICAgICAgICAgICAgICAgICAgY29sb3I6ICMzNjk5ZmY7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgICAgICAgICAgICAgICBhbmltYXRpb246IHNsaWRlUmlnaHQgMS41cyBlYXNlLWluLW91dCBpbmZpbml0ZTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBAa2V5ZnJhbWVzIHNsaWRlUmlnaHQge1xyXG4gICAgICAgICAgICAgICAgICAwJSwgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTsgfVxyXG4gICAgICAgICAgICAgICAgICA1MCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoM3B4KTsgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgLmZpZWxkLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxcmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogIzJkMzc0OCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNyAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMS4yNXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2ZmZmZmZiAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UyZThmMCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNCkgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZSAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgICAgICAgIHRvcDogMDtcclxuICAgICAgICAgICAgICAgICAgbGVmdDogMDtcclxuICAgICAgICAgICAgICAgICAgd2lkdGg6IDRweDtcclxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjZTJlOGYwLCAjY2JkNWUwKTtcclxuICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAwIDAgMC43NXJlbTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICAgICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xyXG4gICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgJi5jb3JyZWN0aXZlLWFjdGlvbiB7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmY1ZjUgMCUsICNmZmZmZmYgMTAwJSk7XHJcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2ZlZDdkNztcclxuXHJcbiAgICAgICAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgI2ZlYjJiMiwgI2ZjODE4MSk7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAmLmNvbW1lbnQge1xyXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjBmOWZmIDAlLCAjZmZmZmZmIDEwMCUpO1xyXG4gICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNiZWUzZjg7XHJcblxyXG4gICAgICAgICAgICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICM5MGNkZjQsICM2M2IzZWQpO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgJi5yZXNwb25zZSB7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlOGY1ZTggMCUsICNmMGZmZjQgMTAwJSk7XHJcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2M2ZjZkNTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzM4YTE2OSwgIzJmODU1YSk7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICY6OmFmdGVyIHtcclxuICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAnw6LCnMKTJztcclxuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgICAgICAgICAgdG9wOiAxcmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIHJpZ2h0OiAxcmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjMzhhMTY5O1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogY2hlY2ttYXJrIDAuNnMgZWFzZS1pbi1vdXQ7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgIEBrZXlmcmFtZXMgY2hlY2ttYXJrIHtcclxuICAgICAgICAgICAgICAgICAgICAwJSB7IHRyYW5zZm9ybTogc2NhbGUoMCkgcm90YXRlKDBkZWcpOyB9XHJcbiAgICAgICAgICAgICAgICAgICAgNTAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxLjIpIHJvdGF0ZSgxODBkZWcpOyB9XHJcbiAgICAgICAgICAgICAgICAgICAgMTAwJSB7IHRyYW5zZm9ybTogc2NhbGUoMSkgcm90YXRlKDM2MGRlZyk7IH1cclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICYuZW9yLXJlc3BvbnNlIHtcclxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2U4ZjVlOCAwJSwgI2YwZmZmNCAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNjNmY2ZDUgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggOHB4IHJnYmEoNTYsIDE2MSwgMTA1LCAwLjEpICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAgICAgICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICMzOGExNjksICMyZjg1NWEpICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDRweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogMTAwJSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW0gIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICYucmVzcG9uZGVkLWJ5IHtcclxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2U4ZjVlOCAwJSwgI2YwZmZmNCAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNjNmY2ZDUgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggOHB4IHJnYmEoNTYsIDE2MSwgMTA1LCAwLjEpICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAgICAgICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICMzOGExNjksICMyZjg1NWEpICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDRweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogMTAwJSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW0gIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC5jb3JyZWN0aW9uLXNlcGFyYXRvciB7XHJcbiAgICAgICAgICAgIGhlaWdodDogMnB4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50LCAjZTVlYWVlLCB0cmFuc3BhcmVudCk7XHJcbiAgICAgICAgICAgIG1hcmdpbjogMnJlbSAwO1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICAgICAgICB0b3A6IC0ycHg7XHJcbiAgICAgICAgICAgICAgbGVmdDogNTAlO1xyXG4gICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcclxuICAgICAgICAgICAgICB3aWR0aDogNjBweDtcclxuICAgICAgICAgICAgICBoZWlnaHQ6IDZweDtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNjk5ZmYsICMxYmM1YmQpO1xyXG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuMyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG5cclxuICAvLyBDb3JyZWN0aW9ucyBzZWN0aW9uIHN0eWxpbmcgLSBzY29wZWQgZm9yIHJldmlldy1kZXRhaWxzLWFjY29yZGlvblxyXG4gIDo6bmctZGVlcCAucmV2aWV3LWRldGFpbHMtYWNjb3JkaW9uIC5jb3JyZWN0aW9ucy1zZWN0aW9uLFxyXG4gIC5yZXZpZXctZGV0YWlscy1hY2NvcmRpb24gLmNvcnJlY3Rpb25zLXNlY3Rpb24sXHJcbiAgLmNvcnJlY3Rpb25zLXNlY3Rpb24ge1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZmZmZiAhaW1wb3J0YW50O1xyXG4gICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAhaW1wb3J0YW50O1xyXG4gICAgcGFkZGluZzogMnJlbSAhaW1wb3J0YW50O1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZSAhaW1wb3J0YW50O1xyXG4gICAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMDgpICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgLnNlY3Rpb24tdGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDEuNHJlbSAhaW1wb3J0YW50O1xyXG4gICAgICBmb250LXdlaWdodDogODAwICFpbXBvcnRhbnQ7XHJcbiAgICAgIGNvbG9yOiAjMWEyMDJjICFpbXBvcnRhbnQ7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDJyZW0gIWltcG9ydGFudDtcclxuICAgICAgcGFkZGluZy1ib3R0b206IDFyZW0gIWltcG9ydGFudDtcclxuICAgICAgYm9yZGVyLWJvdHRvbTogM3B4IHNvbGlkICMzNjk5ZmYgIWltcG9ydGFudDtcclxuICAgICAgcG9zaXRpb246IHJlbGF0aXZlICFpbXBvcnRhbnQ7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xyXG4gICAgICBnYXA6IDFyZW0gIWltcG9ydGFudDtcclxuXHJcbiAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgY29udGVudDogJ8Owwp/Ck8KLJztcclxuICAgICAgICBmb250LXNpemU6IDEuOHJlbTtcclxuICAgICAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMnB4IDRweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4zKSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICY6OmFmdGVyIHtcclxuICAgICAgICBjb250ZW50OiAnJztcclxuICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgYm90dG9tOiAtM3B4O1xyXG4gICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgd2lkdGg6IDYwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiAzcHg7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjMzY5OWZmLCAjMWJjNWJkKTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjQpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLmNvcnJlY3Rpb24taXRlbSB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmZmYgIWltcG9ydGFudDtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZSAhaW1wb3J0YW50O1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAwLjc1cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgIHBhZGRpbmc6IDEuNXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCkgIWltcG9ydGFudDtcclxuICAgICAgcG9zaXRpb246IHJlbGF0aXZlICFpbXBvcnRhbnQ7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSkgIWltcG9ydGFudDtcclxuICAgICAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAycmVtICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICB0b3A6IDA7XHJcbiAgICAgICAgbGVmdDogMDtcclxuICAgICAgICB3aWR0aDogNHB4O1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjMzY5OWZmLCAjMWJjNWJkKTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAwLjc1cmVtIDAgMCAwLjc1cmVtO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCk7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4xNSk7XHJcblxyXG4gICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjMWJjNWJkLCAjMzY5OWZmKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5jb3JyZWN0aW9uLWhlYWRlciB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgICAgICBnYXA6IDEuMjVyZW07XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xyXG4gICAgICAgIHBhZGRpbmctYm90dG9tOiAxcmVtO1xyXG4gICAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjZjFmM2Y0O1xyXG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcbiAgICAgICAgJjo6YWZ0ZXIge1xyXG4gICAgICAgICAgY29udGVudDogJyc7XHJcbiAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICBib3R0b206IC0ycHg7XHJcbiAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgICAgd2lkdGg6IDUwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDJweDtcclxuICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzM2OTlmZiwgIzFiYzViZCk7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxcHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY29ycmVjdGlvbi1udW1iZXIge1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM2OTlmZiwgIzFiYzViZCkgIWltcG9ydGFudDtcclxuICAgICAgICAgIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgd2lkdGg6IDMycHggIWltcG9ydGFudDtcclxuICAgICAgICAgIGhlaWdodDogMzJweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlciAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjlyZW0gIWltcG9ydGFudDtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA4MDAgIWltcG9ydGFudDtcclxuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuNCkgIWltcG9ydGFudDtcclxuICAgICAgICAgIGZsZXgtc2hyaW5rOiAwICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmUgIWltcG9ydGFudDtcclxuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UgIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICBjb250ZW50OiAnJztcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICB0b3A6IC0ycHg7XHJcbiAgICAgICAgICAgIGxlZnQ6IC0ycHg7XHJcbiAgICAgICAgICAgIHJpZ2h0OiAtMnB4O1xyXG4gICAgICAgICAgICBib3R0b206IC0ycHg7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNjk5ZmYsICMxYmM1YmQpO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgICAgIHotaW5kZXg6IC0xO1xyXG4gICAgICAgICAgICBvcGFjaXR5OiAwLjM7XHJcbiAgICAgICAgICAgIGFuaW1hdGlvbjogcHVsc2UgMnMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgQGtleWZyYW1lcyBwdWxzZSB7XHJcbiAgICAgICAgICAgIDAlLCAxMDAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxKTsgb3BhY2l0eTogMC4zOyB9XHJcbiAgICAgICAgICAgIDUwJSB7IHRyYW5zZm9ybTogc2NhbGUoMS4xKTsgb3BhY2l0eTogMC42OyB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY29ycmVjdGlvbi1tZXRhIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgICAgZ2FwOiAwLjc1cmVtO1xyXG4gICAgICAgICAgZmxleDogMTtcclxuXHJcbiAgICAgICAgICAubWV0YS1yb3cge1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICBnYXA6IDAuNzVyZW07XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbSAwO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMDtcclxuICAgICAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiBub25lO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAubWV0YS1sYWJlbCB7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xyXG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICAgICAgICAgICAgY29sb3I6ICM0YTU1Njg7XHJcbiAgICAgICAgICAgICAgdGV4dC10cmFuc2Zvcm06IGNhcGl0YWxpemU7XHJcbiAgICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuMDVyZW07XHJcbiAgICAgICAgICAgICAgbWluLXdpZHRoOiA4MHB4O1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAubWV0YS12YWx1ZSB7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgICAgICBjb2xvcjogIzJkMzc0ODtcclxuICAgICAgICAgICAgICBmbGV4OiAxO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAucmVzb2x2ZWQtZGF0ZSB7XHJcbiAgICAgICAgICAgICAgY29sb3I6IGluaGVyaXQ7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgICAgICAgcGFkZGluZzogMC4yNXJlbSAwO1xyXG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDA7XHJcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnJlc3BvbmQtYnRuIHtcclxuICAgICAgICAgICAgICAvLyBSZW1vdmUgY3VzdG9tIHN0eWxpbmcgdG8gdXNlIEJvb3RzdHJhcCBidG4tc2Vjb25kYXJ5IHN0eWxpbmdcclxuICAgICAgICAgICAgICBtYXJnaW4tbGVmdDogMC41cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5jb3JyZWN0aW9uLWNvbnRlbnQge1xyXG4gICAgICAgIC5jb3JyZWN0aW9uLWZpZWxkIHtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcclxuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcbiAgICAgICAgICAmOmxhc3QtY2hpbGQge1xyXG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC5maWVsZC1sYWJlbCB7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDgwMDtcclxuICAgICAgICAgICAgY29sb3I6ICMyZDM3NDg7XHJcbiAgICAgICAgICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xyXG4gICAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC4xNXJlbTtcclxuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC43NXJlbTtcclxuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgZ2FwOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgICAgICAgICBpIHtcclxuICAgICAgICAgICAgICBjb2xvcjogIzM2OTlmZjtcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICAgICAgICB3aWR0aDogMTZweDtcclxuICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgY29udGVudDogJ8OiwpbCtic7XHJcbiAgICAgICAgICAgICAgY29sb3I6ICMzNjk5ZmY7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgICAgICAgICAgYW5pbWF0aW9uOiBzbGlkZVJpZ2h0IDEuNXMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIEBrZXlmcmFtZXMgc2xpZGVSaWdodCB7XHJcbiAgICAgICAgICAgICAgMCUsIDEwMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7IH1cclxuICAgICAgICAgICAgICA1MCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoM3B4KTsgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLmZpZWxkLWNvbnRlbnQge1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgICAgICAgIGNvbG9yOiAjMmQzNzQ4O1xyXG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMS43O1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAxLjI1cmVtO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZmZmZmZmIDEwMCUpO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTJlOGYwO1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiBpbnNldCAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA0KTtcclxuICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG5cclxuICAgICAgICAgICAgJjo6YmVmb3JlIHtcclxuICAgICAgICAgICAgICBjb250ZW50OiAnJztcclxuICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgICAgdG9wOiAwO1xyXG4gICAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgICAgd2lkdGg6IDRweDtcclxuICAgICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgI2UyZThmMCwgI2NiZDVlMCk7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAwIDAgMC43NXJlbTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xyXG4gICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJi5jb3JyZWN0aXZlLWFjdGlvbiB7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZjVmNSAwJSwgI2ZmZmZmZiAxMDAlKTtcclxuICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNmZWQ3ZDc7XHJcblxyXG4gICAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjZmViMmIyLCAjZmM4MTgxKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICYuY29tbWVudCB7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwZjlmZiAwJSwgI2ZmZmZmZiAxMDAlKTtcclxuICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNiZWUzZjg7XHJcblxyXG4gICAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjOTBjZGY0LCAjNjNiM2VkKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICYucmVzcG9uc2Uge1xyXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlOGY1ZTggMCUsICNmMGZmZjQgMTAwJSk7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYzZmNmQ1O1xyXG5cclxuICAgICAgICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzM4YTE2OSwgIzJmODU1YSk7XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAmOjphZnRlciB7XHJcbiAgICAgICAgICAgICAgICBjb250ZW50OiAnw6LCnMKTJztcclxuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICAgICAgICAgIHRvcDogMXJlbTtcclxuICAgICAgICAgICAgICAgIHJpZ2h0OiAxcmVtO1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICMzOGExNjk7XHJcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xyXG4gICAgICAgICAgICAgICAgYW5pbWF0aW9uOiBjaGVja21hcmsgMC42cyBlYXNlLWluLW91dDtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIEBrZXlmcmFtZXMgY2hlY2ttYXJrIHtcclxuICAgICAgICAgICAgICAgIDAlIHsgdHJhbnNmb3JtOiBzY2FsZSgwKSByb3RhdGUoMGRlZyk7IH1cclxuICAgICAgICAgICAgICAgIDUwJSB7IHRyYW5zZm9ybTogc2NhbGUoMS4yKSByb3RhdGUoMTgwZGVnKTsgfVxyXG4gICAgICAgICAgICAgICAgMTAwJSB7IHRyYW5zZm9ybTogc2NhbGUoMSkgcm90YXRlKDM2MGRlZyk7IH1cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5jb3JyZWN0aW9uLXNlcGFyYXRvciB7XHJcbiAgICAgICAgaGVpZ2h0OiAycHg7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgI2U1ZWFlZSwgdHJhbnNwYXJlbnQpO1xyXG4gICAgICAgIG1hcmdpbjogMnJlbSAwO1xyXG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcbiAgICAgICAgJjo6YmVmb3JlIHtcclxuICAgICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgdG9wOiAtMnB4O1xyXG4gICAgICAgICAgbGVmdDogNTAlO1xyXG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpO1xyXG4gICAgICAgICAgd2lkdGg6IDYwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDZweDtcclxuICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzM2OTlmZiwgIzFiYzViZCk7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7XHJcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuMyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBDb21tZW50cyBzZWN0aW9uIHN0eWxpbmdcclxuICAuY29tbWVudHMtc2VjdGlvbiB7XHJcbiAgICAuc2VjdGlvbi10aXRsZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMzZjQyNTQ7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgICAgIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XHJcbiAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzY5OWZmO1xyXG4gICAgfVxyXG5cclxuICAgIC5jb21tZW50LWNvbnRlbnQge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZmZmO1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlYWVlO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuICAgICAgcGFkZGluZzogMXJlbTtcclxuXHJcbiAgICAgIC5jb21tZW50LXRleHQge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgIGNvbG9yOiAjM2Y0MjU0O1xyXG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIE5vIGRhdGEgc2VjdGlvbiBzdHlsaW5nXHJcbiAgLm5vLWRhdGEtc2VjdGlvbiB7XHJcbiAgICAubm8tZGF0YS1tZXNzYWdlIHtcclxuICAgICAgcGFkZGluZzogMnJlbTtcclxuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICBjb2xvcjogIzZjNzI5MztcclxuXHJcbiAgICAgIGkge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgY29sb3I6ICMzNjk5ZmY7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHNwYW4ge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBSZXZpZXcgZm9ybSBzZWN0aW9uIHN0eWxpbmdcclxuICAucmV2aWV3LWZvcm0tc2VjdGlvbiB7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZmZmO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xyXG5cclxuICAgIC5mb3JtLWxhYmVsIHtcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMzZjQyNTQ7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxuICAgIH1cclxuXHJcbiAgICAuZm9ybS1jb250cm9sIHtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XHJcbiAgICAgIHBhZGRpbmc6IDAuNXJlbSAwLjc1cmVtO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG5cclxuICAgICAgJjpmb2N1cyB7XHJcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjMzY5OWZmO1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDAuMnJlbSByZ2JhKDU0LCAxNTMsIDI1NSwgMC4yNSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuYnRuLXN1Y2Nlc3Mge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWJjNWJkO1xyXG4gICAgICBib3JkZXItY29sb3I6ICMxYmM1YmQ7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzE3YTJiODtcclxuICAgICAgICBib3JkZXItY29sb3I6ICMxN2EyYjg7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5yZXZpZXctYm9keSB7XHJcbiAgLnJldmlldy1tZXRhIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgZ2FwOiAxcmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMC43NXJlbTtcclxuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgIGNvbG9yOiAjNmM3MjkzO1xyXG5cclxuICAgIC5yZXZpZXdlcixcclxuICAgIC5jb21wbGV0ZWQtZGF0ZSB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogMC4yNXJlbTtcclxuXHJcbiAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgY29udGVudDogJyc7XHJcbiAgICAgICAgd2lkdGg6IDRweDtcclxuICAgICAgICBoZWlnaHQ6IDRweDtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNmM3MjkzO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC52aWV3LWJ0biB7XHJcbiAgICAgIG1hcmdpbi1sZWZ0OiBhdXRvO1xyXG4gICAgICBmb250LXNpemU6IDAuNjVyZW07XHJcbiAgICAgIHBhZGRpbmc6IDZweCAxMHB4O1xyXG4gICAgICBsaW5lLWhlaWdodDogMTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5uby1zZWxlY3Rpb24ge1xyXG4gIHBhZGRpbmc6IDJyZW07XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIGNvbG9yOiAjNmM3MjkzO1xyXG59XHJcblxyXG4vLyBSZXNwb25zaXZlIERlc2lnblxyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAudGFibGUge1xyXG4gICAgLmF1ZGl0LWRhdGVzLWNlbGwsXHJcbiAgICAuc3VibWl0dGFsLWRhdGVzLWNlbGwge1xyXG4gICAgICAuZC1mbGV4IHtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgIGdhcDogMC41cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAucmV2aWV3LXNpbmdsZS1saW5lIHtcclxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMjBweCAxZnIgMTAwcHggMTAwcHggMTIwcHggMTIwcHggMzBweDtcclxuICAgIGdhcDogMC41cmVtO1xyXG4gICAgZm9udC1zaXplOiAwLjhyZW07XHJcblxyXG4gICAgLnJldmlldy1zdGF0dXMge1xyXG4gICAgICBmb250LXNpemU6IDAuN3JlbTtcclxuICAgICAgcGFkZGluZzogMC4ycmVtIDAuNHJlbTtcclxuICAgIH1cclxuXHJcbiAgICAucmV2aWV3ZXIsXHJcbiAgICAuZHVlLWRhdGUsXHJcbiAgICAuY29tcGxldGVkLWRhdGUge1xyXG4gICAgICBmb250LXNpemU6IDAuNjVyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLmRvd25sb2FkLXBkZi1pY29uIHtcclxuICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcclxuICAucmV2aWV3LXNpbmdsZS1saW5lIHtcclxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMjBweCAxZnIgODBweCA4MHB4IDEwMHB4IDEwMHB4IDI1cHg7XHJcbiAgICBnYXA6IDAuMjVyZW07XHJcbiAgICBwYWRkaW5nOiAwLjI1cmVtO1xyXG5cclxuICAgIC5yZXZpZXctc3RhdHVzIHtcclxuICAgICAgZm9udC1zaXplOiAwLjY1cmVtO1xyXG4gICAgICBwYWRkaW5nOiAwLjE1cmVtIDAuM3JlbTtcclxuICAgIH1cclxuXHJcbiAgICAucmV2aWV3ZXIsXHJcbiAgICAuZHVlLWRhdGUsXHJcbiAgICAuY29tcGxldGVkLWRhdGUge1xyXG4gICAgICBmb250LXNpemU6IDAuNnJlbTtcclxuICAgIH1cclxuXHJcbiAgICAuZG93bmxvYWQtcGRmLWljb24ge1xyXG4gICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi50aXRsZS13cmFwIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgZ2FwOiAuMjVyZW07XHJcbn1cclxuXHJcbi50aXRsZS1saW5lIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBiYXNlbGluZTtcclxuICBnYXA6IC43NXJlbTtcclxufVxyXG5cclxuLnBlcm1pdC10aXRsZSB7XHJcbiAgZm9udC1zaXplOiAxLjA1cmVtO1xyXG4gIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgY29sb3I6ICMxODFjMzI7XHJcbn1cclxuXHJcbi5zdGF0dXMtdGV4dCB7XHJcbiAgZm9udC1zaXplOiAuOXJlbTtcclxuICBmb250LXdlaWdodDogNjAwO1xyXG4gIGNvbG9yOiAjM2Y0MjU0O1xyXG4gIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgYm9yZGVyOiAxcHggc29saWQgdHJhbnNwYXJlbnQ7XHJcbiAgd2lkdGg6IGZpdC1jb250ZW50O1xyXG5cclxuICAvLyBTdGF0dXMgY29sb3IgdmFyaWF0aW9ucyBmb3IgcGVybWl0IHN0YXR1c1xyXG4gICYuc3RhdHVzLWFwcHJvdmVkIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTg7XHJcbiAgICBjb2xvcjogIzFiNWUyMDtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XHJcbiAgfVxyXG5cclxuICAmLnN0YXR1cy1wZW5kaW5nIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzZTA7XHJcbiAgICBjb2xvcjogI2U2NTEwMDtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNjMDI7XHJcbiAgfVxyXG5cclxuICAmLnN0YXR1cy11bmRlci1yZXZpZXcge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZWFmNjtcclxuICAgIGNvbG9yOiAjMzk0OWFiO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2M1Y2FlOTtcclxuICB9XHJcblxyXG4gICYuc3RhdHVzLXJlamVjdGVkIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmViZWU7XHJcbiAgICBjb2xvcjogI2M2MjgyODtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDI7XHJcbiAgfVxyXG5cclxuICAmLnN0YXR1cy1zdWJtaXR0ZWQge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgIGNvbG9yOiAjMTU2NWMwO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2JiZGVmYjtcclxuICB9XHJcblxyXG4gICYuc3RhdHVzLXJlcXVpcmVzLXJlc3VibWl0IHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY4ZTE7XHJcbiAgICBjb2xvcjogI2Y1N2YxNztcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmVjYjM7XHJcbiAgfVxyXG5cclxuICAmLnN0YXR1cy1jb25kaXRpb25hbC1hcHByb3ZhbCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNlNWY1O1xyXG4gICAgY29sb3I6ICM3YjFmYTI7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTFiZWU3O1xyXG4gIH1cclxuXHJcbiAgJi5zdGF0dXMtdm9pZCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xyXG4gICAgY29sb3I6ICM3NTc1NzU7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xyXG4gIH1cclxuXHJcbiAgJi5zdGF0dXMtbi1hIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XHJcbiAgICBjb2xvcjogIzc1NzU3NTtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgfVxyXG59XHJcblxyXG4ucGVybWl0LW51bWJlci1saW5lIHtcclxuICBmb250LXNpemU6IC44NXJlbTtcclxuICBjb2xvcjogIzZjNzI5MztcclxuICBwYWRkaW5nLWJvdHRvbTogLjI1cmVtO1xyXG59XHJcblxyXG4vLyBGdWxsIFNjcmVlbiBMb2FkaW5nIE92ZXJsYXlcclxuLmZ1bGxzY3JlZW4tbG9hZGluZy1vdmVybGF5IHtcclxuICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgdG9wOiAwO1xyXG4gIGxlZnQ6IDA7XHJcbiAgd2lkdGg6IDEwMHZ3O1xyXG4gIGhlaWdodDogMTAwdmg7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICB6LWluZGV4OiA5OTk5O1xyXG5cclxuICAubG9hZGluZy1jb250ZW50IHtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gICAgcGFkZGluZzogMnJlbTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gIH1cclxufVxyXG5cclxuLy8gU3BlY2lmaWMgc3R5bGluZyBmb3IgRU9SL0FPUi9Pd25lciBSZXNwb25zZSBhbmQgQ29tbWVudCBSZXNwb25kZWQgQnkgc2VjdGlvbnNcclxuLmNvcnJlY3Rpb24tY29udGVudCAuZmllbGQtY29udGVudC5lb3ItcmVzcG9uc2UsXHJcbi5jb3JyZWN0aW9uLWNvbnRlbnQgLmZpZWxkLWNvbnRlbnQucmVzcG9uZGVkLWJ5IHtcclxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZmZmZmZmIDEwMCUpICFpbXBvcnRhbnQ7XHJcbiAgYm9yZGVyLWNvbG9yOiAjZTJlOGYwICFpbXBvcnRhbnQ7XHJcbiAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNCkgIWltcG9ydGFudDtcclxuXHJcbiAgJjo6YmVmb3JlIHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICMzOGExNjksICMyZjg1NWEpICFpbXBvcnRhbnQ7XHJcbiAgICB3aWR0aDogNHB4ICFpbXBvcnRhbnQ7XHJcbiAgICBoZWlnaHQ6IDEwMCUgIWltcG9ydGFudDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW0gIWltcG9ydGFudDtcclxuICB9XHJcbn1cclxuXHJcblxyXG4vKiBDYXJkIENvbnRhaW5lciAqL1xyXG4ubm90ZXMtYWN0aW9ucy1jYXJkIHtcclxuICBiYWNrZ3JvdW5kOiAjZmZmO1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xyXG4gIHBhZGRpbmc6IDEuNXJlbTtcclxuICBtYXgtd2lkdGg6IDcwMHB4O1xyXG4gIG1hcmdpbjogYXV0bztcclxufVxyXG5cclxuLyogSGVhZGVyICovXHJcbi5ub3Rlcy1hY3Rpb25zLWhlYWRlciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBtYXJnaW4tYm90dG9tOiAxLjJyZW07XHJcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU1ZTU7XHJcbiAgcGFkZGluZy1ib3R0b206IDAuOHJlbTtcclxufVxyXG5cclxuLm5vdGVzLWFjdGlvbnMtaGVhZGVyIC5jYXJkLXRpdGxlIHtcclxuICBmb250LXNpemU6IDEuMjVyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICBtYXJnaW46IDA7XHJcbn1cclxuXHJcbi5lZGl0LWJ0biB7XHJcbiAgYmFja2dyb3VuZDogIzAwN2JmZjtcclxuICBjb2xvcjogI2ZmZjtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgcGFkZGluZzogNnB4IDE0cHg7XHJcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xyXG4gIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDZweDtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjNzIGVhc2U7XHJcbn1cclxuLmVkaXQtYnRuOmhvdmVyIHtcclxuICBiYWNrZ3JvdW5kOiAjMDA1NmIzO1xyXG59XHJcblxyXG4vKiBCb2R5ICovXHJcbi5ub3Rlcy1hY3Rpb25zLWJvZHkge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBnYXA6IDFyZW07XHJcbn1cclxuXHJcbi5wZXJtaXQtZGV0YWlsLWl0ZW0tZnVsbCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG59XHJcblxyXG4ucGVybWl0LWRldGFpbC1pdGVtLWZ1bGwgbGFiZWwge1xyXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgbWFyZ2luLWJvdHRvbTogMC40cmVtO1xyXG4gIGNvbG9yOiAjMzMzO1xyXG59XHJcblxyXG4ucGVybWl0LWRldGFpbC1pdGVtLWZ1bGwgdGV4dGFyZWEge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG1pbi1oZWlnaHQ6IDYwcHg7XHJcbiAgcGFkZGluZzogMC42cmVtO1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNkMWQ1ZGI7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIHJlc2l6ZTogdmVydGljYWw7XHJcbiAgZm9udC1zaXplOiAwLjk1cmVtO1xyXG59XHJcblxyXG4vKiBLZW5kbyBEcm9wZG93biBTdHlsaW5nICovXHJcbi5rZW5kby1kcm9wZG93biB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG59XHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "ReviewDetailsModalComponent", "ResponseModalComponent", "AddEditInternalReviewComponent", "PermitPopupComponent", "EditExternalReviewComponent", "jsPDF", "autoTable", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PermitViewComponent_div_2_li_20_Template_a_click_1_listener", "$event", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showTab", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "selectedTab", "PermitViewComponent_div_2_button_25_Template_button_click_0_listener", "_r4", "editPermit", "ɵɵelement", "PermitViewComponent_div_2_div_26_Template_button_click_1_listener", "_r5", "downloadInternalReviewsPdf", "PermitViewComponent_div_2_div_26_Template_button_click_3_listener", "addPopUp", "isLoading", "auditEntries", "length", "PermitViewComponent_div_2_div_27_Template_button_click_1_listener", "_r6", "toggleAllSubmittals", "PermitViewComponent_div_2_div_27_Template_button_click_5_listener", "syncPermits", "PermitViewComponent_div_2_div_27_Template_button_click_8_listener", "goToPortal", "externalSubmittals", "areAllSubmittalsExpanded", "ɵɵtextInterpolate", "permit", "permitEntityID", "ɵɵelementContainerStart", "PermitViewComponent_div_2_ng_container_29_Template_button_click_69_listener", "_r7", "notesActionsTemplate_r8", "ɵɵreference", "onEdit", "projectName", "permitType", "primaryContact", "getStatusClass", "permitStatus", "location", "permitCategory", "permitIssueDate", "ɵɵpipeBind2", "ɵɵtextInterpolate1", "permitAppliedDate", "permitExpirationDate", "permitFinalDate", "permitCompleteDate", "internalReviewStatus", "attentionReason", "internalNotes", "actionTaken", "PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template_tr_click_0_listener", "i_r10", "_r9", "index", "selectAudit", "PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template_i_click_30_listener", "editInternalReview", "stopPropagation", "ɵɵclassProp", "selectedAuditIndex", "audit_r11", "title", "internalVerificationStatus", "getStatusStyle", "typeCodeDrawing", "reviewedDate", "completedDate", "internalReviewer", "ɵɵtemplate", "PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template", "PermitViewComponent_div_2_ng_container_30_div_1_Template", "PermitViewComponent_div_2_ng_container_30_div_2_Template", "PermitViewComponent_div_2_ng_container_31_div_3_Template_button_click_6_listener", "_r12", "fetchExternalReviews", "reviewsError", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template_button_click_0_listener", "_r17", "correction_r18", "$implicit", "review_r16", "openResponseModal", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template_button_click_0_listener", "_r19", "Response", "EORAOROwner_Response", "commentResponsedBy", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_36_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_37_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_38_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_39_Template", "i_r20", "CorrectionTypeName", "CorrectionCategoryName", "ResolvedDate", "shouldShowEditResponseButton", "CorrectiveAction", "Comments", "corrections", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_Template", "comments", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template_div_click_1_listener", "_r15", "toggleReviewAccordion", "commentsId", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template_i_click_21_listener", "downloadReviewPDF", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_25_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_26_Template", "isReviewExpanded", "ɵɵstyleProp", "FailureFlag", "name", "status", "reviewer", "dueDate", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template_tr_click_1_listener", "i_r14", "_r13", "toggleSubmittalA<PERSON>rdion", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template", "isSubmittalExpanded", "sub_r21", "submittalStatus", "receivedDate", "getExternalReviewsForSubmittal", "id", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template", "PermitViewComponent_div_2_ng_container_31_div_3_Template", "PermitViewComponent_div_2_ng_container_31_div_4_Template", "PermitViewComponent_div_2_ng_container_31_div_5_Template", "PermitViewComponent_div_2_Template_button_click_12_listener", "_r1", "goBack", "PermitViewComponent_div_2_Template_a_click_18_listener", "PermitViewComponent_div_2_li_20_Template", "PermitViewComponent_div_2_Template_a_click_22_listener", "PermitViewComponent_div_2_button_25_Template", "PermitViewComponent_div_2_div_26_Template", "PermitViewComponent_div_2_div_27_Template", "PermitViewComponent_div_2_ng_container_29_Template", "PermitViewComponent_div_2_ng_container_30_Template", "PermitViewComponent_div_2_ng_container_31_Template", "permitNumber", "permitReviewType", "permitName", "isInternalReviewEnabled", "PermitViewComponent_ng_template_3_Template_i_click_7_listener", "_r22", "closModal", "PermitViewComponent_ng_template_3_Template_button_click_30_listener", "PermitViewComponent_ng_template_3_Template_button_click_33_listener", "editNotesandactions", "notesForm", "PermitViewComponent", "route", "router", "modalService", "cdr", "fb", "appService", "customLayoutUtilsService", "permitsService", "permitId", "selectedAuditName", "selectedAuditStatus", "externalReviews", "selectedExternalSubmittalId", "internalReviews", "loginUser", "isAdmin", "singlePermit", "expandedSubmittals", "Set", "expandedReviews", "reviewSelectedTabs", "routeSubscription", "queryParamsSubscription", "statusList", "text", "value", "previousPage", "projectId", "constructor", "ngOnInit", "getLoggedInUser", "checkIfAdmin", "queryParams", "subscribe", "params", "Number", "console", "log", "paramMap", "idParam", "get", "fetchPermitDetails", "fetchInternalReviews", "loadForm", "group", "detectChanges", "ngOnDestroy", "unsubscribe", "get<PERSON><PERSON><PERSON>", "next", "res", "<PERSON><PERSON><PERSON>", "responseData", "data", "Object", "keys", "error", "faultMessage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAllReviews", "reviews", "map", "r", "TypeName", "AssignedTo", "StatusText", "CompletedDate", "Date", "DueDate", "Corrections", "submittalId", "SubmittalId", "reviewCategory", "idToReviews", "for<PERSON>ach", "rv", "key", "String", "push", "items", "statusOrder", "reduce", "acc", "it", "a", "b", "find", "createdDate", "sort", "getTime", "err", "getInternalReviews", "take", "skip", "review", "reviewComments", "nonComplianceItems", "aeResponse", "grouped", "doc", "orientation", "unit", "format", "pageWidth", "internal", "pageSize", "getWidth", "margin", "left", "right", "top", "y", "addCategory", "category", "setFont", "setFontSize", "toUpperCase", "align", "reviewers", "Array", "from", "toString", "trim", "filter", "v", "join", "responsesDate", "toLocaleDateString", "rows", "idx", "startY", "head", "body", "styles", "font", "fontSize", "cellPadding", "valign", "headStyles", "fillColor", "textColor", "halign", "columnStyles", "cellWidth", "theme", "lastAutoTable", "finalY", "getHeight", "addPage", "setDrawColor", "line", "fileName", "toISOString", "split", "save", "selectExternalSubmittal", "sel", "s", "getExternalReviewsForSelectedSubmittal", "reverse", "has", "delete", "add", "size", "clear", "_", "reviewId", "showReviewTab", "tab", "updateReviewResponse", "formData", "loggedInUserId", "userId", "updateExternalReview", "showSuccess", "message", "showError", "isSelectedSubmittal", "getReviewsForSelectedAudit", "navigate", "activeTab", "window", "open", "cityReviewLink", "openReviewDetails", "modalRef", "componentInstance", "permitDetails", "result", "then", "catch", "normalized", "toLowerCase", "replace", "aliasMap", "resolved", "finalClass", "styleMap", "backgroundColor", "color", "border", "type", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "reviewIndex", "reviewData", "editPopUp", "passEntry", "saved", "i", "autoLogin", "success", "editEx<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "correction", "responseSubmitted", "submitResponse", "responseCompleted", "emit", "close", "municipalityReviewer", "cityComments", "ownerResponse", "submittalCount", "cycle", "Cycle", "StatusName", "commentstatus", "displayDate", "pageHeight", "bottom", "headers", "rawCorrections", "correctionsArray", "isArray", "bodyRows", "c", "overflow", "fontStyle", "didParseCell", "section", "column", "cell", "didDrawCell", "raw", "isApproved", "bg", "setFillColor", "rect", "x", "width", "height", "setTextColor", "textWidth", "getTextWidth", "textX", "textY", "pageCount", "getNumberOfPages", "setPage", "toLocaleString", "roleId", "lockResponse", "template", "patchValue", "dismissAll", "editNotesAndActions", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "NgbModal", "ChangeDetectorRef", "i3", "FormBuilder", "i4", "AppService", "i5", "CustomLayoutUtilsService", "i6", "PermitsService", "selectors", "decls", "vars", "consts", "PermitViewComponent_Template", "rf", "ctx", "PermitViewComponent_div_0_Template", "PermitViewComponent_div_2_Template", "PermitViewComponent_ng_template_3_Template", "ɵɵtemplateRefExtractor"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-view\\permit-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-view\\permit-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { ActivatedRoute, ActivationEnd, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ReviewDetailsModalComponent } from '../review-details-modal/review-details-modal.component';\r\nimport { ResponseModalComponent } from '../response-modal/response-modal.component';\r\nimport { AddEditInternalReviewComponent } from '../add-edit-internal-review/add-edit-internal-review.component';\r\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { EditExternalReviewComponent } from '../edit-external-review/edit-external-review.component';\r\nimport { AppService } from '../../services/app.service';\r\nimport jsPDF from 'jspdf';\r\nimport 'jspdf-autotable';\r\nimport { autoTable } from 'jspdf-autotable';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-permit-view',\r\n  templateUrl: './permit-view.component.html',\r\n  styleUrls: ['./permit-view.component.scss'],\r\n})\r\nexport class PermitViewComponent implements OnInit, OnDestroy {\r\n    notesForm: FormGroup;\r\n\r\n  public permitId: number | null = null;\r\n  public permit: any = null;\r\n  public isLoading: boolean = false; // Main page loader\r\n  public auditEntries: any[] = [];\r\n  public selectedAuditIndex: number = 0; // Set to 0 to make first item initially active\r\n  public selectedAuditName: any = '';\r\n  public selectedAuditStatus: any = '';\r\n  selectedTab: any = 'details';\r\n  permitReviewType:any = '';\r\n  public externalReviews: any[] = [];\r\n  public reviewsError: string = '';\r\n  public externalSubmittals: Array<{ id: any; title: string; submittalStatus: string; receivedDate: Date | null; dueDate: Date | null; completedDate: Date | null; }> = [];\r\n  public selectedExternalSubmittalId: any = null;\r\n  public internalReviews: any[] = [];\r\n  public loginUser:any ={};\r\n  public isAdmin: boolean = false;\r\n  singlePermit: boolean;\r\n  public expandedSubmittals: Set<number> = new Set();\r\n  public expandedReviews: Set<string> = new Set();\r\n  public reviewSelectedTabs: { [key: string]: string } = {};\r\n  private routeSubscription: Subscription = new Subscription();\r\n  private queryParamsSubscription: Subscription = new Subscription();\r\nstatusList = [\r\n  { text: 'Pending', value: 'Pending' },\r\n  { text: 'In Progress', value: 'In Progress' },\r\n  { text: 'Completed', value: 'Completed' },\r\n  { text: 'On Hold', value: 'On Hold' }\r\n];\r\n  // Navigation tracking\r\n  public previousPage: string = 'permit-list'; // Default fallback\r\n  public projectId: number | null = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private modalService: NgbModal,\r\n    private cdr: ChangeDetectorRef,\r\n    private fb: FormBuilder,\r\n// private modal: NgbActiveModal,\r\n\r\n    private appService:AppService,\r\n        private customLayoutUtilsService: CustomLayoutUtilsService,\r\n\r\n    private permitsService: PermitsService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n    this.isAdmin = this.checkIfAdmin();\r\n\r\n    // Read query parameters for navigation tracking\r\n    this.queryParamsSubscription = this.route.queryParams.subscribe(params => {\r\n      this.previousPage = params['from'] || 'permit-list';\r\n      this.projectId = params['projectId'] ? Number(params['projectId']) : null;\r\n      console.log('Permit view - query params:', { previousPage: this.previousPage, projectId: this.projectId });\r\n    });\r\n\r\n    // Listen for route parameter changes\r\n    this.routeSubscription = this.route.paramMap.subscribe(params => {\r\n      const idParam = params.get('id');\r\n      this.permitId = idParam ? Number(idParam) : null;\r\n\r\n      if (this.permitId) {\r\n        this.fetchPermitDetails();\r\n        this.fetchExternalReviews();\r\n        this.fetchInternalReviews();\r\n      }\r\n    });\r\n    this.loadForm()\r\n  }\r\n  loadForm() {\r\n    this.notesForm = this.fb.group({\r\n     attentionReason:[''],\r\n     internalNotes:[''],\r\n     actionTaken:[''],\r\n    });\r\n\r\n    // Trigger change detection to update the view\r\n    this.cdr.detectChanges();\r\n  }\r\n  ngOnDestroy(): void {\r\n    if (this.routeSubscription) {\r\n      this.routeSubscription.unsubscribe();\r\n    }\r\n    if (this.queryParamsSubscription) {\r\n      this.queryParamsSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  public fetchPermitDetails(): void {\r\n    if (!this.permitId) { return; }\r\n    this.isLoading = true;\r\n    this.permitsService.getPermit({ permitId: this.permitId }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        console.log('Permit API Response:', res);\r\n        if (!res?.isFault) {\r\n          this.permit = res.responseData?.data || res.responseData || null;\r\n          console.log('Permit data assigned:', this.permit);\r\n          console.log('Permit permitName field:', this.permit?.permitName);\r\n          console.log('All permit fields:', Object.keys(this.permit || {}));\r\n          this.permitReviewType = this.permit?.permitReviewType || '';\r\n          // Default to details tab, user can navigate to reviews as needed\r\n          this.selectedTab = 'details'\r\n        } else {\r\n          console.error('API returned fault:', res.faultMessage);\r\n          this.permit = null;\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: () => {\r\n        this.isLoading = false;\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public fetchExternalReviews(): void {\r\n    if (!this.permitId) { return; }\r\n    this.isLoading = true;\r\n    this.reviewsError = '';\r\n    this.permitsService.getAllReviews({ permitId: this.permitId }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        if (res?.isFault) {\r\n          this.reviewsError = res.faultMessage || 'Failed to load reviews';\r\n        } else {\r\n          const reviews = res.responseData?.reviews || [];\r\n          this.externalReviews = reviews.map((r: any) => ({\r\n            commentsId:r.commentsId,\r\n            name: r.TypeName,\r\n            reviewer: r.AssignedTo,\r\n            status: r.StatusText,\r\n            completedDate: r.CompletedDate ? new Date(r.CompletedDate) : null,\r\n            dueDate: r.DueDate ? new Date(r.DueDate) : null,\r\n            receivedDate: r.receivedDate ? new Date(r.receivedDate) : null,\r\n            comments: r.Comments,\r\n            corrections: r.Corrections || [],\r\n            submittalId: r.SubmittalId,\r\n            FailureFlag: r.FailureFlag,\r\n            reviewCategory: r.reviewCategory,\r\n            EORAOROwner_Response: r.EORAOROwner_Response,\r\n            commentResponsedBy: r.commentResponsedBy\r\n          }));\r\n\r\n          // Build submittal list grouped from reviews\r\n          const idToReviews: { [key: string]: any[] } = {};\r\n          this.externalReviews.forEach((rv: any) => {\r\n            const key = String(rv.submittalId ?? 'unknown');\r\n            if (!idToReviews[key]) { idToReviews[key] = []; }\r\n            idToReviews[key].push(rv);\r\n          });\r\n\r\n          this.externalSubmittals = Object.keys(idToReviews).map((key) => {\r\n            const items = idToReviews[key];\r\n            // Determine status priority: Requires Re-submit > Under Review > Approved w/ Conditions > Approved\r\n            const statusOrder: any = {\r\n              'Requires Re-submit': 4,\r\n              'Under Review': 3,\r\n              'Approved w/ Conditions': 2,\r\n              'Approved': 1\r\n            };\r\n            const submittalStatus = items.reduce((acc: string, it: any) => {\r\n              const a = statusOrder[acc] || 0; const b = statusOrder[it.status] || 0; return b > a ? it.status : acc;\r\n            }, '');\r\n\r\n            // Aggregate dates\r\n            const dueDate = items.reduce((acc: Date | null, it: any) => {\r\n              if (!it.dueDate) { return acc; }\r\n              if (!acc) { return it.dueDate; }\r\n              return acc > it.dueDate ? it.dueDate : acc; // earliest due date\r\n            }, null as Date | null);\r\n\r\n            const completedDate = items.reduce((acc: Date | null, it: any) => {\r\n              if (!it.completedDate) { return acc; }\r\n              if (!acc) { return it.completedDate; }\r\n              return acc < it.completedDate ? it.completedDate : acc; // latest completed date\r\n            }, null as Date | null);\r\n\r\n            // Get received date from the first item that has it\r\n            const receivedDate = items.find((it: any) => it.receivedDate)?.receivedDate || \r\n                                items.find((it: any) => it.createdDate)?.createdDate || \r\n                                null;\r\n\r\n            // Get submittal name from the first item (all items in this group have same submittalId)\r\n            const reviewCategory = items[0]?.reviewCategory || `Submittal ${key}`;\r\n\r\n            return {\r\n              id: key,\r\n              title: reviewCategory,\r\n              submittalStatus: submittalStatus || (items[0]?.status || ''),\r\n              receivedDate: receivedDate ? new Date(receivedDate) : null,\r\n              dueDate: dueDate,\r\n              completedDate: completedDate\r\n            };\r\n          }).sort((a, b) => {\r\n            // Sort by received date in descending order (latest first)\r\n            if (!a.receivedDate && !b.receivedDate) return 0;\r\n            if (!a.receivedDate) return 1;\r\n            if (!b.receivedDate) return -1;\r\n            return b.receivedDate.getTime() - a.receivedDate.getTime();\r\n          });\r\n\r\n          // Select first submittal by default\r\n          if (this.externalSubmittals.length > 0) {\r\n            this.selectedExternalSubmittalId = this.externalSubmittals[0].id;\r\n            this.selectedAuditName = this.externalSubmittals[0].title;\r\n            this.selectedAuditStatus = this.externalSubmittals[0].submittalStatus;\r\n          }\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        this.reviewsError = 'Failed to load reviews';\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public fetchInternalReviews(): void {\r\n    if (!this.permitId) { return; }\r\n    this.isLoading = true;\r\n\r\n    this.permitsService.getInternalReviews({\r\n      permitId: this.permitId,\r\n      take: 50,\r\n      skip: 0\r\n    }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        if (res?.isFault) {\r\n          console.error('Failed to load internal reviews:', res.faultMessage);\r\n        } else {\r\n          this.internalReviews = res.responseData?.data || res.data || [];\r\n\r\n          // Transform internal reviews to match the auditEntries format\r\n          this.auditEntries = this.internalReviews.map((review: any) => ({\r\n            commentsId: review.commentsId,\r\n            title: review.reviewCategory,\r\n            reviewCategory: review.reviewCategory, // Preserve reviewCategory for edit modal\r\n            typeCodeDrawing: review.typeCodeDrawing,\r\n            reviewComments: review.reviewComments,\r\n            nonComplianceItems: review.nonComplianceItems,\r\n            aeResponse: review.aeResponse,\r\n            internalReviewer: review.internalReviewer,\r\n            internalVerificationStatus: review.internalVerificationStatus,\r\n            reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\r\n            completedDate: review.completedDate ? new Date(review.completedDate) : null,\r\n            reviews: [{\r\n              name: review.reviewCategory,\r\n              typeCodeDrawing: review.typeCodeDrawing,\r\n              reviewComments: review.reviewComments,\r\n              nonComplianceItems: review.nonComplianceItems,\r\n              aeResponse: review.aeResponse,\r\n              internalReviewer: review.internalReviewer,\r\n              internalVerificationStatus: review.internalVerificationStatus,\r\n              reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\r\n              completedDate: review.completedDate ? new Date(review.completedDate) : null\r\n            }]\r\n          }));\r\n\r\n          // Select first internal review by default\r\n          if (this.auditEntries.length > 0) {\r\n            this.selectedAuditIndex = 0;\r\n            this.selectedAuditName = this.auditEntries[0].title;\r\n            this.selectedAuditStatus = this.auditEntries[0].internalVerificationStatus || 'Pending';\r\n          }\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        console.error('Error loading internal reviews:', err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public downloadInternalReviewsPdf(): void {\r\n    if (!this.internalReviews || this.internalReviews.length === 0) { return; }\r\n\r\n    const grouped: { [category: string]: any[] } = {};\r\n    this.internalReviews.forEach((r: any) => {\r\n      const key = r.reviewCategory || 'Uncategorized';\r\n      if (!grouped[key]) { grouped[key] = []; }\r\n      grouped[key].push(r);\r\n    });\r\n\r\n    const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\r\n    const pageWidth = doc.internal.pageSize.getWidth();\r\n    // Use more horizontal space: reduce side margins to ~0.5 inch (36pt)\r\n    const margin = { left: 36, right: 36, top: 40 };\r\n    let y = margin.top;\r\n\r\n    const addCategory = (category: string, items: any[]) => {\r\n      // Category block title (centered, uppercase)\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(10);\r\n      doc.text(`${category.toUpperCase()} REVIEW COMMENTS`, pageWidth / 2, y, {\r\n        align: 'center'\r\n      });\r\n      y += 12;\r\n\r\n      // Reviewer line (take distinct non-empty names, join with comma)\r\n      const reviewers = Array.from(new Set(items\r\n        .map((it: any) => (it.internalReviewer || '').toString().trim())\r\n        .filter((v: string) => v)));\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(10);\r\n      doc.text(`Reviewer: ${reviewers.join(', ') || 'N/A'}`, pageWidth / 2, y, { align: 'center' });\r\n      y += 12;\r\n\r\n      // Dates line (Reviewed Date / Responses Date)\r\n      const reviewedDate = items.find((it: any) => it.reviewedDate)?.reviewedDate;\r\n      const responsesDate = items.find((it: any) => it.completedDate)?.completedDate;\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.setFontSize(9);\r\n      doc.text(\r\n        `Reviewed Date: ${reviewedDate ? new Date(reviewedDate).toLocaleDateString() : ''}`,\r\n        margin.left,\r\n        y\r\n      );\r\n      doc.text(\r\n        `Responses Date: ${responsesDate ? new Date(responsesDate).toLocaleDateString() : ''}`,\r\n        pageWidth - margin.right,\r\n        y,\r\n        { align: 'right' }\r\n      );\r\n      y += 6;\r\n\r\n      const rows = items.map((it, idx) => [\r\n        (idx + 1).toString(),\r\n        it.typeCodeDrawing || '',\r\n        (it.reviewComments || '').toString(),\r\n        (it.aeResponse || '').toString(),\r\n        it.internalVerificationStatus || ''\r\n      ]);\r\n\r\n      autoTable(doc, {\r\n        startY: y + 5,\r\n        head: [[\r\n          '#',\r\n          'Drawing #',\r\n          'Review Comments',\r\n          'A/E Response',\r\n          'Status'\r\n        ]],\r\n        body: rows,\r\n        margin: { left: margin.left, right: margin.right },\r\n        styles: { font: 'helvetica', fontSize: 8, cellPadding: 5, valign: 'top' },\r\n        headStyles: { fillColor: [33, 150, 243], textColor: 255, halign: 'center', fontSize: 9 },\r\n        // Fit exactly into available width (pageWidth - margins)\r\n        // A4 width ~595pt; with 36pt margins each side → 523pt content width\r\n        columnStyles: {\r\n          0: { cellWidth: 24, halign: 'center' },   // #\r\n          1: { cellWidth: 55 },                     // Drawing # (even narrower)\r\n          2: { cellWidth: 198 },                    // Review Comments (half of remaining)\r\n          3: { cellWidth: 197 },                    // A/E Response (other half)\r\n          4: { cellWidth: 49 }                      // Verification Status (fits)\r\n        },\r\n        theme: 'grid'\r\n      });\r\n\r\n      // update y for next section\r\n      // @ts-ignore\r\n      y = (doc as any).lastAutoTable.finalY + 20;\r\n\r\n      // add page if needed\r\n      if (y > doc.internal.pageSize.getHeight() - 100) {\r\n        doc.addPage();\r\n        y = margin.top;\r\n      }\r\n    };\r\n\r\n    Object.keys(grouped).forEach((category, idx) => {\r\n      if (idx > 0 && y > margin.top + 10) {\r\n        // add a small divider between categories on same page\r\n        doc.setDrawColor(230);\r\n        doc.line(margin.left, y - 10, pageWidth - margin.right, y - 10);\r\n      }\r\n      addCategory(category, grouped[category]);\r\n    });\r\n\r\n    const fileName = `Internal_Reviews_${this.permit?.permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\r\n    doc.save(fileName);\r\n  }\r\n\r\n  public selectExternalSubmittal(id: any): void {\r\n    this.selectedExternalSubmittalId = id;\r\n    const sel = this.externalSubmittals.find(s => String(s.id) === String(id));\r\n    if (sel) {\r\n      this.selectedAuditName = sel.title;\r\n      this.selectedAuditStatus = sel.submittalStatus;\r\n    }\r\n  }\r\n\r\n  public getExternalReviewsForSelectedSubmittal(): any[] {\r\n    if (!this.selectedExternalSubmittalId) { return []; }\r\n    return this.getExternalReviewsForSubmittal(this.selectedExternalSubmittalId);\r\n  }\r\n\r\n  public getExternalReviewsForSubmittal(submittalId: any): any[] {\r\n    const reviews = this.externalReviews.filter(r => String(r.submittalId) === String(submittalId));\r\n    \r\n    // Sort by FailureFlag: false reviews first (desc), then true reviews (desc)\r\n    return reviews.sort((a, b) => {\r\n      // If both have the same FailureFlag value, maintain original order (reverse for desc)\r\n      if (a.FailureFlag === b.FailureFlag) {\r\n        return 0;\r\n      }\r\n      \r\n      // False reviews (FailureFlag = false) come first\r\n      if (!a.FailureFlag && b.FailureFlag) {\r\n        return -1;\r\n      }\r\n      \r\n      // True reviews (FailureFlag = true) come after false reviews\r\n      if (a.FailureFlag && !b.FailureFlag) {\r\n        return 1;\r\n      }\r\n      \r\n      return 0;\r\n    }).reverse(); // Reverse to get descending order within each group\r\n  }\r\n\r\n  public toggleSubmittalAccordion(index: number): void {\r\n    if (this.expandedSubmittals.has(index)) {\r\n      this.expandedSubmittals.delete(index);\r\n    } else {\r\n      this.expandedSubmittals.add(index);\r\n    }\r\n  }\r\n\r\n  public isSubmittalExpanded(index: number): boolean {\r\n    return this.expandedSubmittals.has(index);\r\n  }\r\n\r\n  public areAllSubmittalsExpanded(): boolean {\r\n    return this.externalSubmittals && this.externalSubmittals.length > 0 && this.expandedSubmittals.size === this.externalSubmittals.length;\r\n  }\r\n\r\n  public toggleAllSubmittals(): void {\r\n    if (!this.externalSubmittals || this.externalSubmittals.length === 0) {\r\n      return;\r\n    }\r\n    if (this.areAllSubmittalsExpanded()) {\r\n      this.expandedSubmittals.clear();\r\n    } else {\r\n      this.expandedSubmittals = new Set(this.externalSubmittals.map((_, idx) => idx));\r\n    }\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  public toggleReviewAccordion(reviewId: string): void {\r\n    if (this.expandedReviews.has(reviewId)) {\r\n      this.expandedReviews.delete(reviewId);\r\n    } else {\r\n      this.expandedReviews.add(reviewId);\r\n      // Set default tab for this review if not already set\r\n      if (!this.reviewSelectedTabs[reviewId]) {\r\n        this.reviewSelectedTabs[reviewId] = 'corrections';\r\n      }\r\n    }\r\n  }\r\n\r\n  public isReviewExpanded(reviewId: string): boolean {\r\n    return this.expandedReviews.has(reviewId);\r\n  }\r\n\r\n  public showReviewTab(reviewId: string, tab: string, $event: any): void {\r\n    $event.stopPropagation();\r\n    this.reviewSelectedTabs[reviewId] = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  public updateReviewResponse(review: any): void {\r\n    this.isLoading = true;\r\n    const formData = {\r\n      EORAOROwner_Response: review.EORAOROwner_Response,\r\n      commentResponsedBy: review.commentResponsedBy,\r\n      permitId: this.permitId,\r\n      commentsId: review.commentsId,\r\n      loggedInUserId: this.loginUser.userId\r\n    };\r\n\r\n    this.permitsService.updateExternalReview(formData).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        if (res?.isFault === false) {\r\n          //alert(res.responseData.message);\r\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n          // Refresh external reviews to get updated data\r\n          this.fetchExternalReviews();\r\n        } else {\r\n          this.customLayoutUtilsService.showError(res.faultMessage||'❌ Error syncing permit', '');\r\n          //alert(res.faultMessage || 'Failed to update review response');\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        this.customLayoutUtilsService.showError('❌ Error updating review response', '');\r\n        //alert('Error updating review response');\r\n        console.error(err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public isSelectedSubmittal(submittalId: any): boolean {\r\n    return String(this.selectedExternalSubmittalId) === String(submittalId);\r\n  }\r\n\r\n  public selectAudit(index: number): void {\r\n    this.selectedAuditIndex = index;\r\n    this.selectedAuditName = this.auditEntries[this.selectedAuditIndex].title;\r\n    this.selectedAuditStatus =\r\n      this.auditEntries[this.selectedAuditIndex].submittalStatus;\r\n  }\r\n\r\n  public getReviewsForSelectedAudit(): any[] {\r\n    if (\r\n      this.selectedAuditIndex === null ||\r\n      this.selectedAuditIndex >= this.auditEntries.length\r\n    ) {\r\n      return [];\r\n    }\r\n\r\n    return this.auditEntries[this.selectedAuditIndex].reviews || [];\r\n  }\r\n\r\n\r\n\r\n  public goBack(): void {\r\n    console.log('goBack called - previousPage:', this.previousPage, 'projectId:', this.projectId);\r\n    if (this.previousPage === 'project' && this.projectId) {\r\n      // Navigate back to the specific project view with permits tab active\r\n      console.log('Navigating to project view with permits tab active');\r\n      this.router.navigate(['/projects/view', this.projectId], { \r\n        queryParams: { activeTab: 'permits' } \r\n      });\r\n    } else {\r\n      // Default to permit list\r\n      console.log('Navigating to permit list');\r\n      this.router.navigate(['/permits/list']);\r\n    }\r\n  }\r\n\r\n  public goToPortal(): void {\r\n    window.open(\r\n      `${this.permit.cityReviewLink + this.permit.permitEntityID}`,\r\n      '_blank'\r\n    );\r\n  }\r\n\r\n  public openReviewDetails(review: any): void {\r\n    const modalRef = this.modalService.open(ReviewDetailsModalComponent, {\r\n      size: 'lg',\r\n    });\r\n    modalRef.componentInstance.review = review;\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.permitDetails = this.permit;\r\n\r\n    // Handle modal result\r\n    modalRef.result.then((result) => {\r\n      if (result === 'created' || result === 'updated') {\r\n        // Refresh internal reviews\r\n        this.fetchExternalReviews();\r\n      }\r\n    }).catch((error) => {\r\n      // Modal was dismissed\r\n      console.log('Modal dismissed');\r\n    });\r\n  }\r\n\r\n  public getStatusClass(status: string): string {\r\n    if (!status) return 'status-n-a';\r\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\r\n    console.log('Status mapping - Original:', status, 'Normalized:', normalized);\r\n    // Map common synonyms from API to a single class we style\r\n    const aliasMap: { [k: string]: string } = {\r\n      'in-review': 'in-review',\r\n      'requires-re-submit': 'requires-re-submit',\r\n      'approved-w-conditions': 'approved-w-conditions',\r\n      'in-progress': 'in-progress',\r\n      'completed': 'completed',\r\n      'verified': 'verified',\r\n      'pending': 'pending',\r\n      'rejected': 'rejected',\r\n      'approved': 'approved',\r\n      'under-review': 'under-review',\r\n      'requires-resubmit': 'requires-resubmit',\r\n      'pacifica-verification': 'pacifica-verification',\r\n      'dis-approved': 'dis-approved',\r\n      'not-required': 'not-required',\r\n      '1-cycle-completed': '1-cycle-completed',\r\n      '1 cycle completed': '1-cycle-completed',\r\n      'cycle completed': '1-cycle-completed'\r\n    };\r\n    const resolved = aliasMap[normalized] || normalized;\r\n    const finalClass = 'status-' + resolved;\r\n    console.log('Final status class:', finalClass);\r\n    console.log('Available CSS classes for debugging:', [\r\n      'status-pending', 'status-in-progress', 'status-completed', 'status-verified',\r\n      'status-rejected', 'status-approved', 'status-under-review', 'status-requires-resubmit',\r\n      'status-pacifica-verification', 'status-dis-approved', 'status-not-required',\r\n      'status-in-review', 'status-1-cycle-completed'\r\n    ]);\r\n    return finalClass;\r\n  }\r\n\r\n  public getStatusStyle(status: string): any {\r\n    if (!status) return {};\r\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\r\n    \r\n    const styleMap: { [k: string]: any } = {\r\n      '1-cycle-completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\r\n      '1 cycle completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\r\n      'cycle completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\r\n      'pacifica-verification': { backgroundColor: '#e1f5fe', color: '#0277bd', border: '1px solid #81d4fa' },\r\n      'dis-approved': { backgroundColor: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' },\r\n      'not-required': { backgroundColor: '#f5f5f5', color: '#757575', border: '1px solid #e0e0e0' },\r\n      'in-review': { backgroundColor: '#e8eaf6', color: '#3949ab', border: '1px solid #c5cae9' },\r\n      'pending': { backgroundColor: '#fff3e0', color: '#e65100', border: '1px solid #ffcc02' },\r\n      'approved': { backgroundColor: '#e8f5e8', color: '#1b5e20', border: '1px solid #c8e6c9' },\r\n      'completed': { backgroundColor: '#e8f5e8', color: '#1b5e20', border: '1px solid #c8e6c9' },\r\n      'rejected': { backgroundColor: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' }\r\n    };\r\n    \r\n    return styleMap[normalized] || {};\r\n  }\r\n\r\n  showTab(tab: any, $event: any) {\r\n    if (tab === 'internal' && !this.isInternalReviewEnabled()) {\r\n      return;\r\n    }\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  public isInternalReviewEnabled(): boolean {\r\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\r\n    // show only when type is 'internal' or 'both'\r\n    return type === 'internal' || type === 'both';\r\n  }\r\n\r\n  addPopUp() {\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the AddEditInternalReviewComponent\r\n    const modalRef = this.modalService.open(\r\n      AddEditInternalReviewComponent,\r\n      NgbModalOptions\r\n    );\r\n\r\n    // Pass data to the modal\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\r\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\r\n\r\n    // Handle modal result\r\n    modalRef.result.then((result) => {\r\n      if (result === 'created' || result === 'updated') {\r\n        // Refresh internal reviews\r\n        this.fetchInternalReviews();\r\n      }\r\n    }).catch((error) => {\r\n      // Modal was dismissed\r\n      console.log('Modal dismissed');\r\n    });\r\n  }\r\n\r\n  editInternalReview(reviewIndex: number) {\r\n    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {\r\n      return;\r\n    }\r\n\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg',\r\n      backdrop: 'static',\r\n      keyboard: false,\r\n      scrollable: true,\r\n    };\r\n\r\n    const modalRef = this.modalService.open(\r\n      AddEditInternalReviewComponent,\r\n      NgbModalOptions\r\n    );\r\n\r\n    // Pass data to the modal for editing\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.reviewData = this.auditEntries[reviewIndex];\r\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\r\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\r\n\r\n    // Handle modal result\r\n    modalRef.result.then((result) => {\r\n      if (result === 'updated') {\r\n        // Refresh internal reviews\r\n        this.fetchInternalReviews();\r\n      }\r\n    }).catch((error) => {\r\n      console.log('Modal dismissed');\r\n    });\r\n  }\r\n\r\n  editPopUp() {\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the ProjectPopup\r\n    const modalRef = this.modalService.open(\r\n      AddEditInternalReviewComponent,\r\n      NgbModalOptions\r\n    );\r\n  }\r\n  editPermit() {\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the ProjectPopup\r\n    const modalRef = this.modalService.open(\r\n      PermitPopupComponent,\r\n      NgbModalOptions\r\n    );\r\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\r\n    modalRef.componentInstance.id = this.permitId;\r\n    \r\n    // Listen for passEntry event to refresh permit details when permit is saved\r\n    modalRef.componentInstance.passEntry.subscribe((saved: boolean) => {\r\n      if (saved) {\r\n        this.fetchPermitDetails();\r\n      }\r\n    });\r\n  }\r\n\r\n  syncPermits(i: any) {\r\n    this.isLoading = true;\r\n    this.singlePermit = i || false;\r\n    this.permitsService.syncPermits({ permitId: this.permitId, singlePermit: this.singlePermit, autoLogin: true }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        console.log('Sync response:', res);\r\n        console.log('Response type:', typeof res);\r\n        console.log('Response keys:', Object.keys(res || {}));\r\n        console.log('Response success:', res?.success);\r\n        console.log('Response message:', res?.message);\r\n        console.log('Response responseData:', res?.responseData);\r\n        \r\n        // Handle various response structures\r\n        let responseData = res;\r\n        \r\n        // Check different possible response structures\r\n        if (res?.responseData) {\r\n          responseData = res.responseData;\r\n        } else if (res?.body?.responseData) {\r\n          responseData = res.body.responseData;\r\n        } else if (res?.body) {\r\n          responseData = res.body;\r\n        }\r\n        \r\n        console.log('Final responseData:', responseData);\r\n        console.log('Final success:', responseData?.success);\r\n        console.log('Final message:', responseData?.message);\r\n        \r\n        if (responseData?.isFault) {\r\n          //alert(responseData.faultMessage || 'Failed to sync permit');\r\n          this.customLayoutUtilsService.showError(responseData.faultMessage, '');\r\n        } else if (responseData?.success === false) {\r\n          // Handle specific error messages from the API\r\n          if (responseData.message === 'Permit not found in Energov system') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\r\n          } else if (responseData.message === 'No permits found for any keywords') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\r\n          } else {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\r\n          }\r\n        } else if (responseData?.success === true || responseData?.data) {\r\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\r\n          //alert('✅ Permit synced successfully');\r\n          this.fetchPermitDetails();\r\n          this.fetchExternalReviews();\r\n        } else {\r\n          // Fallback for unknown response structure\r\n          console.log('Unknown response structure, showing generic success');\r\n          //alert('✅ Permit synced successfully');\r\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\r\n\r\n          this.fetchPermitDetails();\r\n          this.fetchExternalReviews();\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        // Handle HTTP error responses\r\n        console.log('Error response:', err);\r\n        console.log('Error type:', typeof err);\r\n        console.log('Error keys:', Object.keys(err || {}));\r\n        console.log('Error status:', err?.status);\r\n        console.log('Error message:', err?.message);\r\n        console.log('Error error:', err?.error);\r\n        \r\n        // The interceptor passes err.error to the error handler\r\n        // So err might actually be the response data\r\n        if (err?.success === false) {\r\n          // Handle specific error messages from the API\r\n          if (err.message === 'Permit not found in Energov system') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\r\n          } else if (err.message === 'No permits found for any keywords') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\r\n          } else {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\r\n          }\r\n        } else if (err?.error?.message) {\r\n          if (err.error.message === 'Permit not found in Energov system') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\r\n          } else if (err.error.message === 'No permits found for any keywords') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\r\n          } else {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert(`❌ ${err.error.message}`);\r\n          }\r\n        } else if (err?.status === 404) {\r\n                                  this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n          // Handle 404 specifically for permit not found\r\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\r\n        } else {\r\n          //alert('❌ Error syncing permit');\r\n        }\r\n        console.error(err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  editExternalReview(review:any) {\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the AddEditInternalReviewComponent\r\n    const modalRef = this.modalService.open(\r\n      EditExternalReviewComponent,\r\n      NgbModalOptions\r\n    );\r\n\r\n    console.log('reviewData ', review)\r\n    // Pass data to the modal\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.reviewData = review;\r\n    modalRef.componentInstance.permitDetails = this.permit;\r\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\r\n\r\n    // Handle modal result\r\n    modalRef.result.then((result) => {\r\n      if (result === 'created' || result === 'updated') {\r\n        // Refresh internal reviews\r\n        this.fetchExternalReviews();\r\n      }\r\n    }).catch((error) => {\r\n      // Modal was dismissed\r\n      console.log('Modal dismissed');\r\n    });\r\n  }\r\n\r\n  public openResponseModal(correction: any, review: any): void {\r\n    // Open the modal using NgbModal\r\n    const modalRef = this.modalService.open(ResponseModalComponent, {\r\n      size: 'lg',\r\n      backdrop: 'static',\r\n      keyboard: false\r\n    });\r\n\r\n    // Pass data to the modal\r\n    modalRef.componentInstance.correction = correction;\r\n    modalRef.componentInstance.review = review;\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;\r\n    modalRef.componentInstance.isAdmin = this.isAdmin;\r\n\r\n    // Handle modal result\r\n    modalRef.componentInstance.responseSubmitted.subscribe((formData: any) => {\r\n      this.submitResponse(formData, modalRef);\r\n    });\r\n\r\n    // Handle response completion to reset loading state\r\n    modalRef.componentInstance.responseCompleted.subscribe((success: boolean) => {\r\n      if (!success) {\r\n        // Reset loading state if submission failed\r\n        modalRef.componentInstance.isLoading = false;\r\n      }\r\n    });\r\n\r\n    modalRef.result.then(() => {\r\n      // Modal was closed\r\n    }).catch(() => {\r\n      // Modal was dismissed\r\n    });\r\n  }\r\n\r\n  public submitResponse(formData: any, modalRef?: any): void {\r\n    if (!formData) {\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n\r\n    this.permitsService.updateExternalReview(formData).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        if (res?.isFault === false) {\r\n                                  this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n\r\n          //alert(res.responseData.message || 'Response submitted successfully');\r\n          this.fetchExternalReviews(); // Refresh external reviews to get updated data\r\n          \r\n          // Emit success completion event\r\n          if (modalRef && modalRef.componentInstance) {\r\n            modalRef.componentInstance.responseCompleted.emit(true);\r\n          }\r\n          \r\n          // Close the modal if it was passed\r\n          if (modalRef) {\r\n            modalRef.close();\r\n          }\r\n        } else {\r\n          //alert(res.faultMessage || 'Failed to submit response');\r\n                                  this.customLayoutUtilsService.showError(res.faultMessage||'Failed to submit response', '');\r\n\r\n          // Emit failure completion event\r\n          if (modalRef && modalRef.componentInstance) {\r\n            modalRef.componentInstance.responseCompleted.emit(false);\r\n          }\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        //alert('Error submitting response');\r\n        console.error(err);\r\n                                this.customLayoutUtilsService.showSuccess('Error submitting response', '');\r\n\r\n        // Emit failure completion event\r\n        if (modalRef && modalRef.componentInstance) {\r\n          modalRef.componentInstance.responseCompleted.emit(false);\r\n        }\r\n        \r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public downloadReviewPDF(review: any): void {\r\n    if (!review) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const permitNumber = this.permit?.permitNumber || '';\r\n      const reviewer = (review?.AssignedTo || review?.municipalityReviewer || review?.reviewer || '').toString();\r\n      const cityComments = (review?.Comments || (review?.cityComments ?? '')).toString();\r\n      const ownerResponse = (review?.EORAOROwner_Response ?? '').toString();\r\n      \r\n      // Calculate submittal count for this review\r\n      const submittalCount = this.externalSubmittals ? this.externalSubmittals.length : 0;\r\n      const cycle = submittalCount > 0 ? submittalCount.toString() : (review?.cycle || review?.Cycle || '').toString();\r\n      \r\n      const status = (review?.StatusName || review?.commentstatus || review?.status || '').toString();\r\n      const displayDate = (review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate)\r\n        ? new Date(review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate).toLocaleDateString()\r\n        : new Date().toLocaleDateString();\r\n\r\n      const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\r\n\r\n      const pageWidth = doc.internal.pageSize.getWidth();\r\n      const pageHeight = doc.internal.pageSize.getHeight();\r\n      const margin = { left: 40, right: 40, top: 40, bottom: 40 };\r\n\r\n      // Header: Permit Number\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(10);\r\n      doc.text(`Permit Number: ${permitNumber || 'N/A'}`, margin.left, margin.top);\r\n\r\n      const startY = margin.top + 20;\r\n\r\n      // Table with one row matching the screenshot\r\n      const headers = [\r\n        'REVIEWED BY',\r\n        'CITY COMMENTS',\r\n        'EOR/AOR/OWNER COMMENT RESPONSE',\r\n        'CYCLE',\r\n        'STATUS',\r\n        'DATE'\r\n      ];\r\n\r\n      // Build body rows. If there are corrections, show one row per correction; otherwise single row\r\n      const rawCorrections: any = (review && (review as any).Corrections) ?? (review as any).corrections ?? [];\r\n      const correctionsArray = Array.isArray(rawCorrections) ? rawCorrections : (rawCorrections ? [rawCorrections] : []);\r\n      const bodyRows: any[] = correctionsArray.length > 0\r\n        ? correctionsArray.map((c: any) => [\r\n            reviewer || 'N/A',\r\n            c?.Comments || cityComments || 'N/A',\r\n            c?.Response || c?.EORAOROwner_Response || ownerResponse || 'N/A',\r\n            cycle || 'N/A',\r\n            status || 'N/A',\r\n            (c?.ResolvedDate ? new Date(c.ResolvedDate).toLocaleDateString() : displayDate) || 'N/A'\r\n          ])\r\n        : [[\r\n            reviewer || 'N/A',\r\n            cityComments || 'N/A',\r\n            ownerResponse || 'N/A',\r\n            cycle || 'N/A',\r\n            status || 'N/A',\r\n            displayDate || 'N/A'\r\n          ]];\r\n\r\n      autoTable(doc, {\r\n        startY,\r\n        head: [headers],\r\n        body: bodyRows,\r\n        margin: { left: margin.left, right: margin.right },\r\n        styles: { font: 'helvetica', fontSize: 8, cellPadding: 5, overflow: 'linebreak' },\r\n        headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold', fontSize: 8 },\r\n        columnStyles: {\r\n          0: { cellWidth: 90 },\r\n          1: { cellWidth: (pageWidth - margin.left - margin.right) * 0.26 },\r\n          2: { cellWidth: (pageWidth - margin.left - margin.right) * 0.24 },\r\n          3: { cellWidth: 45, halign: 'center' },\r\n          4: { cellWidth: 60, halign: 'center' },\r\n          5: { cellWidth: 60, halign: 'center' }\r\n        },\r\n        didParseCell: (data: any) => {\r\n          // City comments text in red\r\n          if (data.section === 'body' && data.column.index === 1) {\r\n            data.cell.styles.textColor = [192, 0, 0];\r\n          }\r\n        },\r\n        didDrawCell: (data: any) => {\r\n          // Fully colored background for Status cell\r\n          if (data.section === 'body' && data.column.index === 4) {\r\n            const value = String(data.cell.raw || '');\r\n            const isApproved = value.toLowerCase() === 'approved';\r\n            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\r\n            const textColor = [255, 255, 255];\r\n\r\n            // fill whole cell\r\n            doc.setFillColor(bg[0], bg[1], bg[2]);\r\n            doc.rect(data.cell.x + 0.5, data.cell.y + 0.5, data.cell.width - 1, data.cell.height - 1, 'F');\r\n\r\n            // write centered white text\r\n            doc.setTextColor(textColor[0], textColor[1], textColor[2]);\r\n            doc.setFont('helvetica', 'bold');\r\n            doc.setFontSize(8);\r\n            const textWidth = doc.getTextWidth(value);\r\n            const textX = data.cell.x + data.cell.width / 2 - textWidth / 2;\r\n            const textY = data.cell.y + data.cell.height / 2 + 3;\r\n            doc.text(value, textX, textY);\r\n\r\n            data.cell.text = [];\r\n          }\r\n        },\r\n        theme: 'grid'\r\n      });\r\n\r\n      // Footer\r\n      const pageCount = doc.getNumberOfPages();\r\n      for (let i = 1; i <= pageCount; i++) {\r\n        doc.setPage(i);\r\n        doc.setDrawColor(200, 200, 200);\r\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\r\n        doc.setFont('helvetica', 'normal');\r\n        doc.setFontSize(8);\r\n        doc.setTextColor(100, 100, 100);\r\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\r\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\r\n      }\r\n\r\n      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\r\n      doc.save(fileName);\r\n      \r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n                              this.customLayoutUtilsService.showSuccess('Error generation PDF. Please try agaiin', '');\r\n\r\n      //alert('Error generating PDF. Please try again.');\r\n    }\r\n  }\r\n\r\n  public checkIfAdmin(): boolean {\r\n    // Check if the user is an admin based on roleId\r\n    // Assuming roleId 1 is admin - adjust this based on your role system\r\n    return this.loginUser && this.loginUser.roleId === 1;\r\n  }\r\n\r\n  public shouldShowEditResponseButton(correction: any): boolean {\r\n    // Show edit response button if:\r\n    // 1. User is admin (can always edit)\r\n    // 2. User is not admin but lockResponse is false (unlocked by admin)\r\n    if (this.isAdmin) {\r\n      return true;\r\n    }\r\n    \r\n    // For non-admin users, only show if lockResponse is explicitly false\r\n    return correction.lockResponse === false;\r\n  }\r\n  onEdit(template: any){\r\n const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n    this.modalService.open(template,NgbModalOptions)\r\n\r\n    // console.log(\"this.permit\", this.permit)\r\n    this.notesForm.patchValue({\r\n      actionTaken:this.permit.actionTaken,\r\n      attentionReason:this.permit.attentionReason,\r\n      internalNotes:this.permit.internalNotes,\r\n      // actionTaken:'helo',\r\n    })\r\n\r\n  }\r\n\r\n  closModal(){\r\n    this.modalService.dismissAll()\r\n  }\r\n\r\n  \r\n  public editNotesandactions(): void {\r\n    this.isLoading = true;\r\n    const formData = {\r\n     permitId: this.permitId,\r\n      actionTaken: this.notesForm.value.actionTaken,\r\n      internalNotes: this.notesForm.value.internalNotes,\r\n      attentionReason: this.notesForm.value.attentionReason,\r\n      \r\n    };\r\n\r\n    this.permitsService.editNotesAndActions(formData).subscribe({\r\n      next: (res: any) => {\r\n        this.closModal()\r\n        this.isLoading = false;\r\n        if (res?.isFault === false) {\r\n          //alert(res.responseData.message);\r\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n          // Refresh permit details to get updated data from server\r\n          this.fetchPermitDetails();\r\n        } else {\r\n          this.customLayoutUtilsService.showError(res.faultMessage||'❌ Error in update notes and actions', '');\r\n          //alert(res.faultMessage || 'Failed to update review response');\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        this.customLayoutUtilsService.showError('❌ Error in update notes and actions', '');\r\n        //alert('Error updating review response');\r\n        console.error(err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"spinner-border text-primary spinner-md\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<div class=\"permit-view-container\">\n  <!-- Permit Details Card -->\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"permit\">\n    <!-- Permit Details Header -->\n    <div class=\"permit-details-header\">\n      <div class=\"header-content w-100\">\n        <div class=\"title-wrap\">\n          <div class=\"title-line\">\n            <span class=\"permit-title\">Permit #: {{\n              permit.permitNumber || \"\"\n              }}</span>\n            <span class=\"status-text status-under-review\">{{ permit.permitReviewType || \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-number-line\">\n            {{ permit.permitName || \"\" }}\n          </div>\n        </div>\n        <div class=\"button-group\">\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center\" (click)=\"goBack()\">\n            <i class=\"fas fa-arrow-left me-2\"></i>\n            Back\n          </button>\n        </div>\n      </div>\n    </div>\n    <!-- Card Header with Tabs -->\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\">\n      <!-- Tabs -->\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap\">\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'details' }\"\n            (click)=\"showTab('details', $event)\">\n            Permit Details\n          </a>\n        </li>\n        <li class=\"nav-item\" *ngIf=\"isInternalReviewEnabled()\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'internal' }\"\n            (click)=\"showTab('internal', $event)\">\n            Internal Reviews\n          </a>\n        </li>\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'external' }\"\n            (click)=\"showTab('external', $event)\">\n            External Reviews\n          </a>\n        </li>\n      </ul>\n\n      <!-- Right side buttons -->\n      <div class=\"d-flex align-items-center gap-2\" style=\"margin-right: 16px;\">\n        <!-- Edit icon - only show when permit details tab is active -->\n        <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"editPermit()\" *ngIf=\"selectedTab === 'details'\" title=\"Edit Permit\">\n          <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\n        </button>\n      </div>\n\n      <!-- Action Buttons -->\n      <div class=\"button-group\" *ngIf=\"selectedTab === 'internal' && isInternalReviewEnabled()\">\n        <button type=\"button\" class=\"btn btn-link p-0 me-3\" (click)=\"downloadInternalReviewsPdf()\" [disabled]=\"isLoading || auditEntries.length === 0\" title=\"Download Internal Reviews PDF\">\n          <i class=\"fas fa-download download-pdf-icon\" style=\"color:#3699ff\"></i>\n        </button>\n        <button class=\"btn btn-success btn-sm\" (click)=\"addPopUp()\" [disabled]=\"isLoading\">\n          <i class=\"bi bi-plus-lg\"></i>Add Review\n        </button>\n      </div>\n       <div class=\"button-group\" *ngIf=\"selectedTab === 'external'\">\n         <button type=\"button\" class=\"btn btn-secondary btn-sm me-3\" (click)=\"toggleAllSubmittals()\" [disabled]=\"isLoading || externalSubmittals.length === 0\" [title]=\"areAllSubmittalsExpanded() ? 'Collapse all submittals' : 'Expand all submittals'\">\n           <i class=\"fas\" [ngClass]=\"areAllSubmittalsExpanded() ? 'fa-compress-arrows-alt' : 'fa-expand-arrows-alt'\"></i>\n           <span class=\"ms-1\">{{ areAllSubmittalsExpanded() ? 'Collapse All' : 'Expand All' }}</span>\n         </button>\n         <button type=\"button\" class=\"btn btn-primary btn-sm me-3\" (click)=\"syncPermits(true)\" [disabled]=\"isLoading\">\n           <i class=\"fas fa-sync-alt\"></i> Sync\n         </button>\n         <button type=\"button\" class=\"btn btn-secondary btn-sm\" \n                 (click)=\"goToPortal()\" \n                 [disabled]=\"!permit?.permitEntityID\"\n                 [title]=\"permit?.permitEntityID ? 'Open Portal' : 'Portal not available - Permit Entity ID required'\">\n           <i class=\"fas fa-external-link-alt\"></i> Portal\n         </button>\n       </div>\n    </div>\n\n\n    <!-- Card Body with Tab Content -->\n    <div class=\"card-body\">\n      <!-- Permit Details Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'details' && permit\">\n      <div class=\" permit-details-content\">\n        <div class=\"permit-details-grid\">\n          <div class=\"permit-detail-item\">\n            <label>Project Name</label>\n            <span class=\"permit-value\">{{ permit.projectName || \"\" }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Permit Type</label>\n            <span class=\"permit-value\">{{ permit.permitType || \"\" }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Primary Contact</label>\n            <span class=\"permit-value\">{{\n              permit.primaryContact || \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Permit Status</label>\n            <span class=\"status-text\" [ngClass]=\"getStatusClass(permit.permitStatus)\">{{ permit.permitStatus || \"\"\n            }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Location</label>\n            <span class=\"permit-value\">{{ permit.location || \"\" }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Category</label>\n            <span class=\"permit-value\">{{\n              permit.permitCategory || \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Issue Date</label>\n            <span class=\"permit-value\">{{\n              permit.permitIssueDate\n              ? (permit.permitIssueDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n            <span class=\"text-gray-500 fs-7 p-0\"> Applied on {{\n              permit.permitAppliedDate\n              ? (permit.permitAppliedDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Expiration Date</label>\n            <span class=\"permit-value\">{{\n              permit.permitExpirationDate\n              ? (permit.permitExpirationDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Final Date</label>\n            <span class=\"permit-value\">{{\n              permit.permitFinalDate \n              ? (permit.permitFinalDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Complete Date</label>\n            <span class=\"permit-value\">{{\n              permit.permitCompleteDate\n              ? (permit.permitCompleteDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>internal Review Status</label>\n            <span class=\"status-text\" [ngClass]=\"getStatusClass(permit.internalReviewStatus)\">{{ permit.internalReviewStatus || \"\"\n            }}</span>\n          </div>\n        </div>\n      </div>\n        \n        <!-- Notes & Actions fields (inline with other details) -->\n         <div class=\"permit-details-card\"> \n          <div class=\"permit-details-header\">\n            <h4>Notes / Action</h4>\n            <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"onEdit(notesActionsTemplate)\" title=\"Edit Notes/Actions\">\n              <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\n            </button>\n          </div>\n        <div class=\"permit-details-content\">\n          <div class=\"notes-actions-container\">\n            <div class=\"permit-detail-item-full\">\n              <label>Attention Reason</label>\n            <span class=\"permit-value\">{{ permit.attentionReason || \"\" }}</span>\n          </div>\n            <div class=\"permit-detail-item-full\">\n              <label>Internal Notes</label>\n            <span class=\"permit-value\">{{ permit.internalNotes || \"\" }}</span>\n          </div>\n            <div class=\"permit-detail-item-full\">\n            <label>Action Taken</label>\n            <span class=\"permit-value\">{{\n              permit.actionTaken || \"N/A\"\n              }}</span>\n          </div>\n        </div>\n      </div>\n      </div>\n      </ng-container>\n\n      <!-- Internal Reviews Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'internal' && permit && isInternalReviewEnabled()\">\n        <!-- Empty State for Internal Reviews -->\n        <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"auditEntries.length === 0\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-clipboard-list fa-3x mb-3\"></i>\n            <p>No internal reviews found for this permit.</p>\n          </div>\n        </div>\n\n        <!-- Internal Reviews Table -->\n        <div class=\"table-responsive\" *ngIf=\"auditEntries.length > 0\">\n          <table class=\"table table-hover\">\n            <tbody>\n              <tr *ngFor=\"let audit of auditEntries; let i = index\" [class.table-active]=\"selectedAuditIndex === i\"\n                (click)=\"selectAudit(i)\" class=\"audit-table-row\">\n                <td class=\"audit-title-cell px-4\">\n                  <div class=\"d-flex align-items-center\">\n                    <h6 class=\"mb-0 me-3\">{{ audit.title }}</h6>\n                    <span class=\"audit-status-badge\" \n                          [ngClass]=\"getStatusClass(audit.internalVerificationStatus)\"\n                          [ngStyle]=\"getStatusStyle(audit.internalVerificationStatus)\">\n                      {{ audit.internalVerificationStatus || 'Pending' }}\n                    </span>\n                  </div>\n                  <div class=\"mt-1\">\n                    <small class=\"text-muted\">{{ audit.typeCodeDrawing || '' }}</small>\n                  </div>\n                </td>\n                <td class=\"audit-dates-cell px-3\">\n                  <div class=\"d-flex gap-4\">\n                    <div class=\"date-item\">\n                      <small class=\"text-muted d-block\">Reviewed</small>\n                      <span class=\"fw-medium\">{{ audit.reviewedDate ? (audit.reviewedDate | date : \"MM/dd/yyyy\") : '' }}</span>\n                    </div>\n                    <div class=\"date-item\">\n                      <small class=\"text-muted d-block\">Completed</small>\n                      <span class=\"fw-medium\">{{ audit.completedDate ? (audit.completedDate | date : \"MM/dd/yyyy\") : '' }}</span>\n                    </div>\n                    <div class=\"date-item\">\n                      <small class=\"text-muted d-block\">Reviewer</small>\n                      <span class=\"fw-medium\">{{ audit.internalReviewer || '' }}</span>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"audit-actions-cell px-4\">\n                  <i class=\"fas fa-edit action-icon edit-icon\" (click)=\"editInternalReview(i); $event.stopPropagation()\"\n                    title=\"Edit Review\"></i>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </ng-container>\n\n      <!-- External Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'external' && permit\">\n         <div class=\"external-reviews-card\">\n           <div class=\"card shadow-sm rounded-3\">\n        <!-- Error State for External Reviews -->\n           <div class=\"card-body\" *ngIf=\"reviewsError\">\n             <div class=\"d-flex justify-content-center align-items-center py-5 text-danger\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-exclamation-triangle fa-3x mb-3\"></i>\n            <p>{{ reviewsError }}</p>\n            <button class=\"btn btn-outline-primary btn-sm\" (click)=\"fetchExternalReviews()\" [disabled]=\"isLoading\">\n              <i class=\"fas fa-redo\"></i> Retry\n            </button>\n               </div>\n          </div>\n        </div>\n\n        <!-- Empty State for External Reviews -->\n           <div class=\"card-body\" *ngIf=\"!reviewsError && externalSubmittals.length === 0\">\n             <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-external-link-alt fa-3x mb-3\"></i>\n            <p>No external reviews found for this permit.</p>\n               </div>\n          </div>\n        </div>\n\n        <!-- External Reviews Table with Accordion -->\n           <div class=\"card-body p-0\" *ngIf=\"!reviewsError && externalSubmittals.length > 0\">\n             <div class=\"table-responsive external-reviews-table\">\n               <table class=\"table table-hover mb-0\">\n            <tbody>\n              <ng-container *ngFor=\"let sub of externalSubmittals; let i = index\">\n                <!-- Submittal Row -->\n                     <tr class=\"external-submittal-row\" \n                       (click)=\"toggleSubmittalAccordion(i)\"\n                       [class.expanded]=\"isSubmittalExpanded(i)\"\n                       style=\"cursor: pointer;\">\n                  <td class=\"submittal-title-cell px-3\">\n                    <div class=\"d-flex align-items-center\">\n                           <div class=\"accordion-toggle me-2\">\n                        <i class=\"fas\" [ngClass]=\"isSubmittalExpanded(i) ? 'fa-chevron-down' : 'fa-chevron-right'\"></i>\n                           </div>\n                      <h6 class=\"mb-0 me-3\">{{ sub.title }}</h6>\n                      <span class=\"submittal-status-badge\" [ngClass]=\"getStatusClass(sub.submittalStatus)\">\n                        {{ sub.submittalStatus }}\n                      </span>\n                    </div>\n                  </td>\n                  <td class=\"submittal-dates-cell px-3\">\n                    <div class=\"d-flex gap-4\">\n                      <div class=\"date-item\">\n                        <small class=\"text-muted d-block\">Due</small>\n                        <span class=\"fw-medium\">{{ sub.dueDate | date : \"MM/dd/yyyy\" }}</span>\n                      </div>\n                      <div class=\"date-item\">\n                        <small class=\"text-muted d-block\">Completed</small>\n                        <span class=\"fw-medium\">{{ sub.receivedDate | date : \"MM/dd/yyyy\" }}</span>\n                      </div>\n                    </div>\n                  </td>\n                </tr>\n                <!-- Accordion Content for External Reviews - positioned directly below its submittal row -->\n                <tr class=\"accordion-content-row\" [class.d-none]=\"!isSubmittalExpanded(i)\">\n                  <td colspan=\"2\" class=\"p-0\">\n                    <div class=\"accordion-content\">\n                      <div class=\"reviews-container p-3\">\n                        <div class=\"review-item\" *ngFor=\"let review of getExternalReviewsForSubmittal(sub.id)\">\n                                <div class=\"review-single-line\" \n                                  (click)=\"toggleReviewAccordion(review.commentsId)\"\n                                  [class.expanded]=\"isReviewExpanded(review.commentsId)\"\n                                  style=\"cursor: pointer;\">\n                                  <div class=\"review-accordion-toggle me-2\">\n                                    <i class=\"fas\" [ngClass]=\"isReviewExpanded(review.commentsId) ? 'fa-chevron-down' : 'fa-chevron-right'\"></i>\n                                  </div>\n                                  <h6 class=\"review-title\" [style.color]=\"review.FailureFlag ? 'red' : 'green'\">\n                                    {{ review.name }}\n                                  </h6>\n                                  <div class=\"review-status-container\">\n                                    <span class=\"review-status\" [ngClass]=\"getStatusClass(review.status)\">\n                                      {{ review.status || '' }}\n                                    </span>\n                                  </div>\n                                  <div class=\"reviewer-container\">\n                                    <span class=\"reviewer\">{{ review.reviewer || '' }}</span>\n                                  </div>\n                                  <div class=\"due-date-container\">\n                                    <span class=\"due-date\">\n                                      {{ review.dueDate ? ('Due: ' + (review.dueDate | date : \"MM/dd/yyyy\")) : '' }}\n                                    </span>\n                                  </div>\n                                  <div class=\"completed-date-container\">\n                                    <span class=\"completed-date\">\n                                      {{ review.completedDate ? ('Completed: ' + (review.completedDate | date : \"MM/dd/yyyy\")) : '' }}\n                                    </span>\n                                  </div>\n                                  <div class=\"review-actions-container\">\n                                    <i class=\"fas fa-download download-pdf-icon\" \n                                      (click)=\"downloadReviewPDF(review); $event.stopPropagation()\"\n                                      title=\"Download Review PDF\"></i>\n                                  </div>\n                                </div>\n                                \n                                <!-- Review Details Accordion Content -->\n                                <div class=\"review-details-accordion\" [class.d-none]=\"!isReviewExpanded(review.commentsId)\">\n                                  <div class=\"review-details-content\">\n                                    <!-- Corrections Section -->\n                                    <div class=\"corrections-section p-3\" *ngIf=\"review?.corrections && review.corrections.length > 0\" style=\"padding-bottom: 0px !important;\">\n                                      <h6 class=\"section-title\">Corrections ({{ review.corrections.length }})</h6>\n                                      <div class=\"correction-item\" *ngFor=\"let correction of review.corrections; let i = index\">\n                                        <div class=\"correction-header d-flex align-items-center\">\n                                          <div class=\"correction-number\">{{ i + 1 }}</div>\n                                          <div class=\"correction-meta flex-grow-1 ms-3 d-flex align-items-center justify-content-between\">\n                                            <div class=\"meta-fields d-flex align-items-center w-100\">\n                                              <div class=\"meta-field flex-fill\">\n                                                <span class=\"meta-label fw-bold\">Correction Type: </span>\n                                                <span class=\"meta-value\">{{ correction.CorrectionTypeName || '' }}</span>\n                                              </div>\n                                              <div class=\"meta-field flex-fill\">\n                                                <span class=\"meta-label fw-bold\">Category: </span>\n                                                <span class=\"meta-value\">{{ correction.CorrectionCategoryName || '' }}</span>\n                                              </div>\n                                              <div class=\"meta-field flex-fill\">\n                                                <span class=\"meta-label fw-bold\">Resolved: </span>\n                                                <span class=\"meta-value resolved-date\">{{ correction.ResolvedDate ? (correction.ResolvedDate | date:'MM/dd/yyyy') : '' }}</span>\n                                              </div>\n                                            </div>\n                                          </div>\n                                          <div class=\"respond-buttons\">\n                                            <button class=\"btn btn-primary btn-sm me-3\" \n                                              *ngIf=\"!correction.EORAOROwner_Response && !correction.commentResponsedBy && shouldShowEditResponseButton(correction)\"\n                                              (click)=\"openResponseModal(correction, review)\"\n                                              [disabled]=\"isLoading\"\n                                              title=\"Respond to this correction\">\n                                              Respond\n                                            </button>\n                                            <button class=\"btn btn-primary btn-sm me-3\" \n                                              *ngIf=\"(correction.EORAOROwner_Response || correction.commentResponsedBy) && shouldShowEditResponseButton(correction)\"\n                                              (click)=\"openResponseModal(correction, review)\"\n                                              [disabled]=\"isLoading\"\n                                              title=\"Edit response to this correction\">\n                                              Edit Response\n                                            </button>\n                                          </div>\n                                        </div>\n                                        \n                                        <div class=\"correction-content\">\n                                          <div class=\"correction-field\">\n                                            <label class=\"field-label\">\n                                              Corrective Action\n                                            </label>\n                                            <div class=\"field-content corrective-action\">\n                                              {{ correction.CorrectiveAction || 'N/A' }}\n                                            </div>\n                                          </div>\n                                          \n                                          <div class=\"correction-field\">\n                                            <label class=\"field-label\">\n                                              Comment\n                                            </label>\n                                            <div class=\"field-content comment\">\n                                              {{ correction.Comments || 'No comment provided' }}\n                                            </div>\n                                          </div>\n                                          \n                                          <div class=\"correction-field\" *ngIf=\"correction.Response\">\n                                            <label class=\"field-label\">\n                                              Response\n                                            </label>\n                                            <div class=\"field-content response\">\n                                              {{ correction.Response }}\n                                            </div>\n                                          </div>\n                                          \n                                          <div class=\"correction-field\" *ngIf=\"correction.EORAOROwner_Response\">\n                                            <label class=\"field-label\">\n                                              EOR / AOR / Owner Response\n                                            </label>\n                                            <div class=\"field-content eor-response\">\n                                              {{ correction.EORAOROwner_Response }}\n                                            </div>\n                                          </div>\n                                          \n                                          <div class=\"correction-field\" *ngIf=\"correction.commentResponsedBy\">\n                                            <label class=\"field-label\">\n                                              Comment Responded By\n                                            </label>\n                                            <div class=\"field-content responded-by\">\n                                              {{ correction.commentResponsedBy }}\n                                            </div>\n                                          </div>\n                                        </div>\n                                        \n                                        <div class=\"correction-separator\" *ngIf=\"i < review.corrections.length - 1\"></div>\n                                      </div>\n                                    </div>\n\n                                    <!-- Comments Section (fallback when no corrections) -->\n                                    <div class=\"comments-section p-3\"\n                                      *ngIf=\"(!review?.corrections || review.corrections.length === 0) && review?.comments\">\n                                      <h6 class=\"section-title\">Comments</h6>\n                                      <div class=\"comment-content\">\n                                        <div class=\"comment-text\">{{ review.comments }}</div>\n                                      </div>\n                                    </div>\n\n                                    <!-- No Data Message -->\n                                    <div class=\"no-data-section p-3\" \n                                      *ngIf=\"(!review?.corrections || review.corrections.length === 0) && !review?.comments\">\n                                      <div class=\"no-data-message text-center text-muted\">\n                                        <i class=\"fas fa-info-circle\"></i>\n                                        <span> No corrections or comments available for this review.</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                </tr>\n              </ng-container>\n            </tbody>\n          </table>\n             </div>\n           </div>\n         </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n\n</div>\n\n\n<ng-template #notesActionsTemplate let-permit=\"permit\">\n <div class=\"modal-content h-auto\">\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container>\n       \n        <div>Edit Notes/Actions</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i\n        class=\"fa-solid fs-2 fa-xmark text-white\"\n        (click)=\"closModal()\"\n      ></i>\n    </div>\n  </div>\n\n  <div\n    class=\"modal-body medium-modal-body\"\n    \n  >\n\n  <form class=\"form form-label-right\" [formGroup]=\"notesForm\">\n  <!-- Project Name -->\n  <div class=\"row mt-4\">\n    <div class=\"col-xl-12\">\n      <div class=\"form-group\">\n        <label for=\"projectName\" class=\"fw-bold form-label mb-2\">\n          Attention Reason\n        </label>\n        <textarea\n          id=\"attentionReason\"\n          class=\"form-control form-control-sm\"\n          rows=\"2\"\n          formControlName=\"attentionReason\"\n          placeholder=\"Tyed here...\"\n        ></textarea>\n      </div>\n    </div>\n  </div>\n\n  <!-- Description -->\n  <div class=\"row mt-4\">\n    <div class=\"col-xl-12\">\n      <div class=\"form-group\">\n        <label for=\"projectDescription\" class=\"fw-bold form-label mb-2\">\n          Internal Notes\n        </label>\n        <textarea\n          id=\"internalNotes\"\n          class=\"form-control form-control-sm\"\n          rows=\"3\"\n          formControlName=\"internalNotes\"\n          placeholder=\"Type here\"\n        ></textarea>\n      </div>\n    </div>\n  </div>\n\n  <!-- Internal Project Number & Start Date -->\n  <div class=\"row mt-4\">\n    <div class=\"col-xl-12\">\n      <div class=\"form-group\">\n        <label for=\"internalProjectNo\" class=\"fw-bold form-label mb-2\">\n          Action Taken \n        </label>\n          <textarea\n          id=\"internalNotes\"\n          class=\"form-control form-control-sm\"\n          rows=\"3\"\n          formControlName=\"actionTaken\"\n          placeholder=\"Type here\"\n        ></textarea>\n      </div>\n    </div>\n\n    \n  <!-- </div>\n  <div class=\"row mt-4\">\n    <div class=\"col-xl-12\">\n      <div class=\"form-group\">\n        <label for=\"internalProjectNo\" class=\"fw-bold form-label mb-2\">\nStatus        </label>\n        \n <ng-select></ng-select>\n\n      </div>\n    </div> -->\n\n    \n  </div>\n\n\n</form>\n\n  </div>\n\n  <div class=\"modal-footer justify-content-end\">\n    <div>\n      <button\n        type=\"button\"\n        class=\"btn btn-danger btn-sm btn-elevate mr-2\"\n        (click)=\"closModal()\"\n      >\n        Cancel</button\n      >&nbsp;\n      <button\n        \n        type=\"button\"\n        class=\"btn btn-primary btn-sm\"\n        \n       (click)=\"editNotesandactions()\"\n      >Update\n       <!-- (click)=\"save()\" -->\n        <!-- [disabled]=\"projectForm?.invalid\" -->\n        <!-- {{ id ? \"Update\" : \"Save\" }} -->\n      </button>\n     \n    </div>\n  </div>\n</div>\n\n</ng-template>\n\n\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,MAAM;AAEnC,SAASC,2BAA2B,QAAQ,wDAAwD;AACpG,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,8BAA8B,QAAQ,gEAAgE;AAC/G,SAASC,oBAAoB,QAAQ,wCAAwC;AAE7E,SAASC,2BAA2B,QAAQ,wDAAwD;AAEpG,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AACxB,SAASC,SAAS,QAAQ,iBAAiB;;;;;;;;;;;;;;ICTrCC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuC,cAClC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IAuCIH,EADF,CAAAC,cAAA,aAAuD,YAEb;IAAtCD,EAAA,CAAAI,UAAA,mBAAAC,4DAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,UAAU,EAAAN,MAAA,CAAS;IAAA,EAAC;IACrCN,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;;;;IAJyDH,EAAA,CAAAa,SAAA,EAAkD;IAAlDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,iBAAkD;;;;;;IAgBhHjB,EAAA,CAAAC,cAAA,iBAA4H;IAA7ED,EAAA,CAAAI,UAAA,mBAAAc,qEAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAW,UAAA,EAAY;IAAA,EAAC;IACnEpB,EAAA,CAAAqB,SAAA,YAAmE;IACrErB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAKTH,EADF,CAAAC,cAAA,cAA0F,iBAC6F;IAAjID,EAAA,CAAAI,UAAA,mBAAAkB,kEAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,0BAAA,EAA4B;IAAA,EAAC;IACxFxB,EAAA,CAAAqB,SAAA,YAAuE;IACzErB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAmF;IAA5CD,EAAA,CAAAI,UAAA,mBAAAqB,kEAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiB,QAAA,EAAU;IAAA,EAAC;IACzD1B,EAAA,CAAAqB,SAAA,YAA6B;IAAArB,EAAA,CAAAE,MAAA,kBAC/B;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IANuFH,EAAA,CAAAa,SAAA,EAAmD;IAAnDb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,IAAAlB,MAAA,CAAAmB,YAAA,CAAAC,MAAA,OAAmD;IAGlF7B,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;;;;;;IAKjF3B,EADF,CAAAC,cAAA,cAA6D,iBACsL;IAArLD,EAAA,CAAAI,UAAA,mBAAA0B,kEAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,mBAAA,EAAqB;IAAA,EAAC;IACzFhC,EAAA,CAAAqB,SAAA,YAA8G;IAC9GrB,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAgE;IACrFF,EADqF,CAAAG,YAAA,EAAO,EACnF;IACTH,EAAA,CAAAC,cAAA,iBAA6G;IAAnDD,EAAA,CAAAI,UAAA,mBAAA6B,kEAAA;MAAAjC,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyB,WAAA,CAAY,IAAI,CAAC;IAAA,EAAC;IACnFlC,EAAA,CAAAqB,SAAA,YAA+B;IAACrB,EAAA,CAAAE,MAAA,aAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAG8G;IAFtGD,EAAA,CAAAI,UAAA,mBAAA+B,kEAAA;MAAAnC,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC;IAG5BpC,EAAA,CAAAqB,SAAA,YAAwC;IAACrB,EAAA,CAAAE,MAAA,gBAC3C;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAbwFH,EAAA,CAAAa,SAAA,EAAyD;IAACb,EAA1D,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,IAAAlB,MAAA,CAAA4B,kBAAA,CAAAR,MAAA,OAAyD,UAAApB,MAAA,CAAA6B,wBAAA,yDAA2F;IAC/NtC,EAAA,CAAAa,SAAA,EAA0F;IAA1Fb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA6B,wBAAA,uDAA0F;IACtFtC,EAAA,CAAAa,SAAA,GAAgE;IAAhEb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA6B,wBAAA,mCAAgE;IAECtC,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;IAKpG3B,EAAA,CAAAa,SAAA,GAAoC;IACpCb,EADA,CAAAc,UAAA,eAAAL,MAAA,CAAA+B,MAAA,kBAAA/B,MAAA,CAAA+B,MAAA,CAAAC,cAAA,EAAoC,WAAAhC,MAAA,CAAA+B,MAAA,kBAAA/B,MAAA,CAAA+B,MAAA,CAAAC,cAAA,uEACiE;;;;;;IAUhHzC,EAAA,CAAA0C,uBAAA,GAAyD;IAInD1C,EAHN,CAAAC,cAAA,cAAqC,cACF,cACC,YACvB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3BH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;IAEJH,EADF,CAAAC,cAAA,cAAgC,YACvB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,gBAA0E;IAAAD,EAAA,CAAAE,MAAA,IACxE;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAC,cAAA,gBAAqC;IAACD,EAAA,CAAAE,MAAA,IAIlC;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrCH,EAAA,CAAAC,cAAA,gBAAkF;IAAAD,EAAA,CAAAE,MAAA,IAChF;IAGRF,EAHQ,CAAAG,YAAA,EAAO,EACL,EACF,EACF;IAKAH,EAFH,CAAAC,cAAA,eAAiC,eACG,UAC7B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,kBAAiH;IAAlED,EAAA,CAAAI,UAAA,mBAAAuC,4EAAA;MAAA3C,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,MAAAmC,uBAAA,GAAA7C,EAAA,CAAA8C,WAAA;MAAA,OAAA9C,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsC,MAAA,CAAAF,uBAAA,CAA4B;IAAA,EAAC;IACnF7C,EAAA,CAAAqB,SAAA,aAAmE;IAEvErB,EADE,CAAAG,YAAA,EAAS,EACL;IAIFH,EAHN,CAAAC,cAAA,eAAoC,eACG,eACE,aAC5B;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC/DF,EAD+D,CAAAG,YAAA,EAAO,EAChE;IAEFH,EADF,CAAAC,cAAA,eAAqC,aAC5B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IAEJH,EADA,CAAAC,cAAA,eAAqC,aAC9B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IAIVF,EAJU,CAAAG,YAAA,EAAO,EACP,EACF,EACF,EACA;;;;;IAlG2BH,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAQ,WAAA,OAA8B;IAI9BhD,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAS,UAAA,OAA6B;IAI7BjD,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAU,cAAA,OAEvB;IAIsBlD,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAA1C,MAAA,CAAA+B,MAAA,CAAAY,YAAA,EAA+C;IAACpD,EAAA,CAAAa,SAAA,EACxE;IADwEb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAY,YAAA,OACxE;IAIyBpD,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAa,QAAA,OAA2B;IAI3BrD,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAc,cAAA,OAEvB;IAIuBtD,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAe,eAAA,GAAAvD,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAe,eAAA,qBAIvB;IACkCvD,EAAA,CAAAa,SAAA,GAIlC;IAJkCb,EAAA,CAAAyD,kBAAA,iBAAAhD,MAAA,CAAA+B,MAAA,CAAAkB,iBAAA,GAAA1D,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAkB,iBAAA,yBAIlC;IAIuB1D,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAmB,oBAAA,GAAA3D,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAmB,oBAAA,qBAIvB;IAIuB3D,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAoB,eAAA,GAAA5D,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAoB,eAAA,qBAIvB;IAIuB5D,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAqB,kBAAA,GAAA7D,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAqB,kBAAA,qBAIvB;IAIsB7D,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAA1C,MAAA,CAAA+B,MAAA,CAAAsB,oBAAA,EAAuD;IAAC9D,EAAA,CAAAa,SAAA,EAChF;IADgFb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAsB,oBAAA,OAChF;IAiByB9D,EAAA,CAAAa,SAAA,IAAkC;IAAlCb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAuB,eAAA,OAAkC;IAIlC/D,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAwB,aAAA,OAAgC;IAIhChE,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAyB,WAAA,UAEvB;;;;;IAWNjE,EADF,CAAAC,cAAA,cAAgH,cACrF;IACvBD,EAAA,CAAAqB,SAAA,YAAgD;IAChDrB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAEjDF,EAFiD,CAAAG,YAAA,EAAI,EAC7C,EACF;;;;;;IAMAH,EAAA,CAAAC,cAAA,aACmD;IAAjDD,EAAA,CAAAI,UAAA,mBAAA8D,kFAAA;MAAA,MAAAC,KAAA,GAAAnE,EAAA,CAAAO,aAAA,CAAA6D,GAAA,EAAAC,KAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6D,WAAA,CAAAH,KAAA,CAAc;IAAA,EAAC;IAGpBnE,EAFJ,CAAAC,cAAA,aAAkC,cACO,aACf;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,eAEmE;IACjED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,cAAkB,gBACU;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAE/DF,EAF+D,CAAAG,YAAA,EAAQ,EAC/D,EACH;IAICH,EAHN,CAAAC,cAAA,cAAkC,eACN,eACD,iBACa;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA0E;;IACpGF,EADoG,CAAAG,YAAA,EAAO,EACrG;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4E;;IACtGF,EADsG,CAAAG,YAAA,EAAO,EACvG;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAGhEF,EAHgE,CAAAG,YAAA,EAAO,EAC7D,EACF,EACH;IAEHH,EADF,CAAAC,cAAA,cAAoC,aAEZ;IADuBD,EAAA,CAAAI,UAAA,mBAAAmE,kFAAAjE,MAAA;MAAA,MAAA6D,KAAA,GAAAnE,EAAA,CAAAO,aAAA,CAAA6D,GAAA,EAAAC,KAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAA+D,kBAAA,CAAAL,KAAA,CAAqB;MAAA,OAAAnE,EAAA,CAAAW,WAAA,CAAEL,MAAA,CAAAmE,eAAA,EAAwB;IAAA,EAAC;IAG1GzE,EAF0B,CAAAG,YAAA,EAAI,EACvB,EACF;;;;;;IAnCiDH,EAAA,CAAA0E,WAAA,iBAAAjE,MAAA,CAAAkE,kBAAA,KAAAR,KAAA,CAA+C;IAIzEnE,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAC,KAAA,CAAiB;IAEjC7E,EAAA,CAAAa,SAAA,EAA4D;IAC5Db,EADA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAAyB,SAAA,CAAAE,0BAAA,EAA4D,YAAArE,MAAA,CAAAsE,cAAA,CAAAH,SAAA,CAAAE,0BAAA,EACA;IAChE9E,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAmB,SAAA,CAAAE,0BAAA,mBACF;IAG0B9E,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAI,eAAA,OAAiC;IAOjChF,EAAA,CAAAa,SAAA,GAA0E;IAA1Eb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAK,YAAA,GAAAjF,EAAA,CAAAwD,WAAA,SAAAoB,SAAA,CAAAK,YAAA,qBAA0E;IAI1EjF,EAAA,CAAAa,SAAA,GAA4E;IAA5Eb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAM,aAAA,GAAAlF,EAAA,CAAAwD,WAAA,SAAAoB,SAAA,CAAAM,aAAA,qBAA4E;IAI5ElF,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAO,gBAAA,OAAkC;;;;;IA5BpEnF,EAFJ,CAAAC,cAAA,cAA8D,gBAC3B,YACxB;IACLD,EAAA,CAAAoF,UAAA,IAAAC,6DAAA,mBACmD;IAqCzDrF,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAtCsBH,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAmB,YAAA,CAAiB;;;;;IAb/C5B,EAAA,CAAA0C,uBAAA,GAAuF;IAUrF1C,EARA,CAAAoF,UAAA,IAAAE,wDAAA,kBAAgH,IAAAC,wDAAA,kBAQlD;;;;;IARiBvF,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAmB,YAAA,CAAAC,MAAA,OAA+B;IAQ/E7B,EAAA,CAAAa,SAAA,EAA6B;IAA7Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAmB,YAAA,CAAAC,MAAA,KAA6B;;;;;;IAmD1D7B,EAFC,CAAAC,cAAA,cAA4C,cACqC,cACzD;IACvBD,EAAA,CAAAqB,SAAA,YAAsD;IACtDrB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzBH,EAAA,CAAAC,cAAA,iBAAuG;IAAxDD,EAAA,CAAAI,UAAA,mBAAAoF,iFAAA;MAAAxF,EAAA,CAAAO,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiF,oBAAA,EAAsB;IAAA,EAAC;IAC7E1F,EAAA,CAAAqB,SAAA,YAA2B;IAACrB,EAAA,CAAAE,MAAA,cAC9B;IAGJF,EAHI,CAAAG,YAAA,EAAS,EACA,EACL,EACF;;;;IANCH,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAkF,YAAA,CAAkB;IAC2D3F,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;;;;;IAUxG3B,EAFC,CAAAC,cAAA,cAAgF,cACA,cACxD;IACvBD,EAAA,CAAAqB,SAAA,YAAmD;IACnDrB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAGjDF,EAHiD,CAAAG,YAAA,EAAI,EACxC,EACL,EACF;;;;;;IAwG8BH,EAAA,CAAAC,cAAA,kBAIqC;IAFnCD,EAAA,CAAAI,UAAA,mBAAAwF,8HAAA;MAAA5F,EAAA,CAAAO,aAAA,CAAAsF,IAAA;MAAA,MAAAC,cAAA,GAAA9F,EAAA,CAAAU,aAAA,GAAAqF,SAAA;MAAA,MAAAC,UAAA,GAAAhG,EAAA,CAAAU,aAAA,IAAAqF,SAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwF,iBAAA,CAAAH,cAAA,EAAAE,UAAA,CAAqC;IAAA,EAAC;IAG/ChG,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;;;;;;IAIxB3B,EAAA,CAAAC,cAAA,kBAI2C;IAFzCD,EAAA,CAAAI,UAAA,mBAAA8F,8HAAA;MAAAlG,EAAA,CAAAO,aAAA,CAAA4F,IAAA;MAAA,MAAAL,cAAA,GAAA9F,EAAA,CAAAU,aAAA,GAAAqF,SAAA;MAAA,MAAAC,UAAA,GAAAhG,EAAA,CAAAU,aAAA,IAAAqF,SAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwF,iBAAA,CAAAH,cAAA,EAAAE,UAAA,CAAqC;IAAA,EAAC;IAG/ChG,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;;;;;IA2BxB3B,EADF,CAAAC,cAAA,eAA0D,iBAC7B;IACzBD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAoC;IAClCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAM,QAAA,MACF;;;;;IAIApG,EADF,CAAAC,cAAA,eAAsE,iBACzC;IACzBD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAO,oBAAA,MACF;;;;;IAIArG,EADF,CAAAC,cAAA,eAAoE,iBACvC;IACzBD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAQ,kBAAA,MACF;;;;;IAIJtG,EAAA,CAAAqB,SAAA,eAAkF;;;;;IAlFhFrB,EAFJ,CAAAC,cAAA,eAA0F,eAC/B,eACxB;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAI1CH,EAHN,CAAAC,cAAA,eAAgG,eACrC,eACrB,gBACC;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;IAEJH,EADF,CAAAC,cAAA,gBAAkC,iBACC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IACxEF,EADwE,CAAAG,YAAA,EAAO,EACzE;IAEJH,EADF,CAAAC,cAAA,gBAAkC,iBACC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAE,MAAA,IAAkF;;IAG/HF,EAH+H,CAAAG,YAAA,EAAO,EAC5H,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,gBAA6B;IAQ3BD,EAPA,CAAAoF,UAAA,KAAAmB,qGAAA,sBAIqC,KAAAC,qGAAA,sBAOM;IAI/CxG,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,gBAAgC,gBACA,kBACD;IACzBD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAA6C;IAC3CD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,gBAA8B,kBACD;IACzBD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAoBNH,EAlBA,CAAAoF,UAAA,KAAAqB,kGAAA,mBAA0D,KAAAC,kGAAA,mBASY,KAAAC,kGAAA,mBASF;IAQtE3G,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAoF,UAAA,KAAAwB,kGAAA,mBAA4E;IAC9E5G,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAnF6BH,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAuC,iBAAA,CAAAsE,KAAA,KAAW;IAKX7G,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAuC,iBAAA,CAAAuD,cAAA,CAAAgB,kBAAA,OAAyC;IAIzC9G,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAuC,iBAAA,CAAAuD,cAAA,CAAAiB,sBAAA,OAA6C;IAI/B/G,EAAA,CAAAa,SAAA,GAAkF;IAAlFb,EAAA,CAAAuC,iBAAA,CAAAuD,cAAA,CAAAkB,YAAA,GAAAhH,EAAA,CAAAwD,WAAA,SAAAsC,cAAA,CAAAkB,YAAA,qBAAkF;IAM1HhH,EAAA,CAAAa,SAAA,GAAoH;IAApHb,EAAA,CAAAc,UAAA,UAAAgF,cAAA,CAAAO,oBAAA,KAAAP,cAAA,CAAAQ,kBAAA,IAAA7F,MAAA,CAAAwG,4BAAA,CAAAnB,cAAA,EAAoH;IAOpH9F,EAAA,CAAAa,SAAA,EAAoH;IAApHb,EAAA,CAAAc,UAAA,UAAAgF,cAAA,CAAAO,oBAAA,IAAAP,cAAA,CAAAQ,kBAAA,KAAA7F,MAAA,CAAAwG,4BAAA,CAAAnB,cAAA,EAAoH;IAerH9F,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAoB,gBAAA,eACF;IAQElH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAqB,QAAA,+BACF;IAG6BnH,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAc,UAAA,SAAAgF,cAAA,CAAAM,QAAA,CAAyB;IASzBpG,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAc,UAAA,SAAAgF,cAAA,CAAAO,oBAAA,CAAqC;IASrCrG,EAAA,CAAAa,SAAA,EAAmC;IAAnCb,EAAA,CAAAc,UAAA,SAAAgF,cAAA,CAAAQ,kBAAA,CAAmC;IAUjCtG,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAAc,UAAA,SAAA+F,KAAA,GAAAb,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,KAAuC;;;;;IArF5E7B,EADF,CAAAC,cAAA,eAA0I,cAC9G;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5EH,EAAA,CAAAoF,UAAA,IAAAiC,2FAAA,qBAA0F;IAsF5FrH,EAAA,CAAAG,YAAA,EAAM;;;;IAvFsBH,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAyD,kBAAA,kBAAAuC,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,MAA6C;IACnB7B,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAkF,UAAA,CAAAoB,WAAA,CAAuB;;;;;IA2F3EpH,EAFF,CAAAC,cAAA,eACwF,cAC5D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAErCH,EADF,CAAAC,cAAA,eAA6B,eACD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAEnDF,EAFmD,CAAAG,YAAA,EAAM,EACjD,EACF;;;;IAFwBH,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAuC,iBAAA,CAAAyD,UAAA,CAAAsB,QAAA,CAAqB;;;;;IAOjDtH,EAFF,CAAAC,cAAA,eACyF,eACnC;IAClDD,EAAA,CAAAqB,SAAA,aAAkC;IAClCrB,EAAA,CAAAC,cAAA,WAAM;IAACD,EAAA,CAAAE,MAAA,6DAAqD;IAEhEF,EAFgE,CAAAG,YAAA,EAAO,EAC/D,EACF;;;;;;IAjJVH,EADR,CAAAC,cAAA,cAAuF,cAIpD;IAFzBD,EAAA,CAAAI,UAAA,mBAAAmH,oGAAA;MAAA,MAAAvB,UAAA,GAAAhG,EAAA,CAAAO,aAAA,CAAAiH,IAAA,EAAAzB,SAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgH,qBAAA,CAAAzB,UAAA,CAAA0B,UAAA,CAAwC;IAAA,EAAC;IAGlD1H,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAqB,SAAA,YAA4G;IAC9GrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAqC,gBACmC;IACpED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,eAAgC,iBACP;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;IAEJH,EADF,CAAAC,cAAA,gBAAgC,iBACP;IACrBD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,gBAAsC,iBACP;IAC3BD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,gBAAsC,cAGN;IAD5BD,EAAA,CAAAI,UAAA,mBAAAuH,mGAAArH,MAAA;MAAA,MAAA0F,UAAA,GAAAhG,EAAA,CAAAO,aAAA,CAAAiH,IAAA,EAAAzB,SAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAmH,iBAAA,CAAA5B,UAAA,CAAyB;MAAA,OAAAhG,EAAA,CAAAW,WAAA,CAAEL,MAAA,CAAAmE,eAAA,EAAwB;IAAA,EAAC;IAGnEzE,EAFkC,CAAAG,YAAA,EAAI,EAC9B,EACF;IAIJH,EADF,CAAAC,cAAA,gBAA4F,gBACtD;IAsGlCD,EApGA,CAAAoF,UAAA,KAAAyC,qFAAA,mBAA0I,KAAAC,qFAAA,mBA4FlD,KAAAC,qFAAA,mBASC;IAQrG/H,EAFU,CAAAG,YAAA,EAAM,EACR,EACF;;;;;IAlJIH,EAAA,CAAAa,SAAA,EAAsD;IAAtDb,EAAA,CAAA0E,WAAA,aAAAjE,MAAA,CAAAuH,gBAAA,CAAAhC,UAAA,CAAA0B,UAAA,EAAsD;IAGrC1H,EAAA,CAAAa,SAAA,GAAwF;IAAxFb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAuH,gBAAA,CAAAhC,UAAA,CAAA0B,UAAA,2CAAwF;IAEhF1H,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAAiI,WAAA,UAAAjC,UAAA,CAAAkC,WAAA,mBAAoD;IAC3ElI,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAuC,UAAA,CAAAmC,IAAA,MACF;IAE8BnI,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAA6C,UAAA,CAAAoC,MAAA,EAAyC;IACnEpI,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAuC,UAAA,CAAAoC,MAAA,YACF;IAGuBpI,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuC,iBAAA,CAAAyD,UAAA,CAAAqC,QAAA,OAA2B;IAIhDrI,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAuC,UAAA,CAAAsC,OAAA,aAAAtI,EAAA,CAAAwD,WAAA,SAAAwC,UAAA,CAAAsC,OAAA,0BACF;IAIEtI,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAuC,UAAA,CAAAd,aAAA,mBAAAlF,EAAA,CAAAwD,WAAA,SAAAwC,UAAA,CAAAd,aAAA,0BACF;IAUkClF,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAA0E,WAAA,YAAAjE,MAAA,CAAAuH,gBAAA,CAAAhC,UAAA,CAAA0B,UAAA,EAAqD;IAGjD1H,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAc,UAAA,UAAAkF,UAAA,kBAAAA,UAAA,CAAAoB,WAAA,KAAApB,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,KAA0D;IA4F7F7B,EAAA,CAAAa,SAAA,EAAmF;IAAnFb,EAAA,CAAAc,UAAA,YAAAkF,UAAA,kBAAAA,UAAA,CAAAoB,WAAA,KAAApB,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,YAAAmE,UAAA,kBAAAA,UAAA,CAAAsB,QAAA,EAAmF;IASnFtH,EAAA,CAAAa,SAAA,EAAoF;IAApFb,EAAA,CAAAc,UAAA,YAAAkF,UAAA,kBAAAA,UAAA,CAAAoB,WAAA,KAAApB,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,aAAAmE,UAAA,kBAAAA,UAAA,CAAAsB,QAAA,EAAoF;;;;;;IAhL7GtH,EAAA,CAAA0C,uBAAA,GAAoE;IAE7D1C,EAAA,CAAAC,cAAA,aAG2B;IAFzBD,EAAA,CAAAI,UAAA,mBAAAmI,4FAAA;MAAA,MAAAC,KAAA,GAAAxI,EAAA,CAAAO,aAAA,CAAAkI,IAAA,EAAApE,KAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiI,wBAAA,CAAAF,KAAA,CAA2B;IAAA,EAAC;IAKjCxI,EAFT,CAAAC,cAAA,aAAsC,cACG,cACG;IACtCD,EAAA,CAAAqB,SAAA,YAA+F;IAC5FrB,EAAA,CAAAG,YAAA,EAAM;IACXH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,eAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACH;IAICH,EAHN,CAAAC,cAAA,cAAsC,eACV,eACD,iBACa;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7CH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4C;;IAI5EF,EAJ4E,CAAAG,YAAA,EAAO,EACvE,EACF,EACH,EACF;IAKCH,EAHN,CAAAC,cAAA,cAA2E,cAC7C,eACK,eACM;IACjCD,EAAA,CAAAoF,UAAA,KAAAuD,8EAAA,oBAAuF;IAyJ/F3I,EAHM,CAAAG,YAAA,EAAM,EACF,EACH,EACF;;;;;;;IAxLEH,EAAA,CAAAa,SAAA,EAAyC;IAAzCb,EAAA,CAAA0E,WAAA,aAAAjE,MAAA,CAAAmI,mBAAA,CAAAJ,KAAA,EAAyC;IAKzBxI,EAAA,CAAAa,SAAA,GAA2E;IAA3Eb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAmI,mBAAA,CAAAJ,KAAA,2CAA2E;IAEtExI,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAuC,iBAAA,CAAAsG,OAAA,CAAAhE,KAAA,CAAe;IACA7E,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAA0F,OAAA,CAAAC,eAAA,EAA+C;IAClF9I,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAoF,OAAA,CAAAC,eAAA,MACF;IAO0B9I,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAwD,WAAA,SAAAqF,OAAA,CAAAP,OAAA,gBAAuC;IAIvCtI,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAwD,WAAA,SAAAqF,OAAA,CAAAE,YAAA,gBAA4C;IAM1C/I,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAA0E,WAAA,YAAAjE,MAAA,CAAAmI,mBAAA,CAAAJ,KAAA,EAAwC;IAItBxI,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAuI,8BAAA,CAAAH,OAAA,CAAAI,EAAA,EAAyC;;;;;IApCjGjJ,EAHD,CAAAC,cAAA,cAAkF,cAC3B,gBACb,YAClC;IACLD,EAAA,CAAAoF,UAAA,IAAA8D,uEAAA,6BAAoE;IAiMvElJ,EAHC,CAAAG,YAAA,EAAQ,EACF,EACC,EACF;;;;IAjM2BH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA4B,kBAAA,CAAuB;;;;;IA/B7DrC,EAAA,CAAA0C,uBAAA,GAA0D;IAErD1C,EADF,CAAAC,cAAA,cAAmC,aACK;IAyBtCD,EAvBA,CAAAoF,UAAA,IAAA+D,wDAAA,kBAA4C,IAAAC,wDAAA,kBAaoC,IAAAC,wDAAA,kBAUE;IAuMrFrJ,EADC,CAAAG,YAAA,EAAM,EACD;;;;;IA9NqBH,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAkF,YAAA,CAAkB;IAalB3F,EAAA,CAAAa,SAAA,EAAsD;IAAtDb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAkF,YAAA,IAAAlF,MAAA,CAAA4B,kBAAA,CAAAR,MAAA,OAAsD;IAUlD7B,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAkF,YAAA,IAAAlF,MAAA,CAAA4B,kBAAA,CAAAR,MAAA,KAAoD;;;;;;IA7Q/E7B,EANV,CAAAC,cAAA,aAAqD,cAEhB,cACC,cACR,cACE,eACK;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAC,cAAA,eAA8C;IAAAD,EAAA,CAAAE,MAAA,GAC1C;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IACNH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACgF;IAAnBD,EAAA,CAAAI,UAAA,mBAAAkJ,4DAAA;MAAAtJ,EAAA,CAAAO,aAAA,CAAAgJ,GAAA;MAAA,MAAA9I,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+I,MAAA,EAAQ;IAAA,EAAC;IACrGxJ,EAAA,CAAAqB,SAAA,aAAsC;IACtCrB,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAMAH,EAJN,CAAAC,cAAA,eAAyF,cAEgB,cAChF,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAAqJ,uDAAAnJ,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgJ,GAAA;MAAA,MAAA9I,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,SAAS,EAAAN,MAAA,CAAS;IAAA,EAAC;IACpCN,EAAA,CAAAE,MAAA,wBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IACLH,EAAA,CAAAoF,UAAA,KAAAsE,wCAAA,iBAAuD;IAOrD1J,EADF,CAAAC,cAAA,cAAqB,aAEqB;IAAtCD,EAAA,CAAAI,UAAA,mBAAAuJ,uDAAArJ,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgJ,GAAA;MAAA,MAAA9I,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,UAAU,EAAAN,MAAA,CAAS;IAAA,EAAC;IACrCN,EAAA,CAAAE,MAAA,0BACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACD,EACF;IAGLH,EAAA,CAAAC,cAAA,eAAyE;IAEvED,EAAA,CAAAoF,UAAA,KAAAwE,4CAAA,qBAA4H;IAG9H5J,EAAA,CAAAG,YAAA,EAAM;IAWLH,EARD,CAAAoF,UAAA,KAAAyE,yCAAA,kBAA0F,KAAAC,yCAAA,mBAQ5B;IAehE9J,EAAA,CAAAG,YAAA,EAAM;IAINH,EAAA,CAAAC,cAAA,eAAuB;IAoKrBD,EAlKA,CAAAoF,UAAA,KAAA2E,kDAAA,6BAAyD,KAAAC,kDAAA,2BA2G8B,KAAAC,kDAAA,2BAuD7B;IAqO9DjK,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAvd+BH,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAyD,kBAAA,eAAAhD,MAAA,CAAA+B,MAAA,CAAA0H,YAAA,WAEvB;IAC0ClK,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAA2H,gBAAA,OAC1C;IAGJnK,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAhD,MAAA,CAAA+B,MAAA,CAAA4H,UAAA,YACF;IAe4DpK,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,KAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,gBAAiD;IAKzFjB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAA4J,uBAAA,GAA+B;IAOSrK,EAAA,CAAAa,SAAA,GAAkD;IAAlDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,KAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,iBAAkD;IAUzCjB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,eAA+B;IAM7EjB,EAAA,CAAAa,SAAA,EAA6D;IAA7Db,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,mBAAAR,MAAA,CAAA4J,uBAAA,GAA6D;IAQ5DrK,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,gBAAgC;IAqB7CjB,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,iBAAAR,MAAA,CAAA+B,MAAA,CAAwC;IA2GxCxC,EAAA,CAAAa,SAAA,EAAsE;IAAtEb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,kBAAAR,MAAA,CAAA+B,MAAA,IAAA/B,MAAA,CAAA4J,uBAAA,GAAsE;IAuDtErK,EAAA,CAAAa,SAAA,EAAyC;IAAzCb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,kBAAAR,MAAA,CAAA+B,MAAA,CAAyC;;;;;;IA6O1DxC,EAFH,CAAAC,cAAA,eAAkC,eACU,eACR;IAC/BD,EAAA,CAAA0C,uBAAA,GAAc;IAEZ1C,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;IAEjCH,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAItB;IADCD,EAAA,CAAAI,UAAA,mBAAAkK,8DAAA;MAAAtK,EAAA,CAAAO,aAAA,CAAAgK,IAAA;MAAA,MAAA9J,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+J,SAAA,EAAW;IAAA,EAAC;IAG3BxK,EAFK,CAAAG,YAAA,EAAI,EACD,EACF;IAYAH,EAVN,CAAAC,cAAA,eAGC,gBAE2D,gBAEtC,gBACG,gBACG,kBACmC;IACvDD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAqB,SAAA,qBAMY;IAGlBrB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAsB,gBACG,gBACG,kBAC0C;IAC9DD,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAqB,SAAA,qBAMY;IAGlBrB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAsB,gBACG,gBACG,kBACyC;IAC7DD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACNH,EAAA,CAAAqB,SAAA,qBAMU;IAuBlBrB,EAtBI,CAAAG,YAAA,EAAM,EACF,EAgBF,EAGD,EAEC;IAIFH,EAFJ,CAAAC,cAAA,gBAA8C,WACvC,mBAKF;IADCD,EAAA,CAAAI,UAAA,mBAAAqK,oEAAA;MAAAzK,EAAA,CAAAO,aAAA,CAAAgK,IAAA;MAAA,MAAA9J,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+J,SAAA,EAAW;IAAA,EAAC;IAErBxK,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EACP;IAAAH,EAAA,CAAAE,MAAA,eACD;IAAAF,EAAA,CAAAC,cAAA,mBAMC;IADAD,EAAA,CAAAI,UAAA,mBAAAsK,oEAAA;MAAA1K,EAAA,CAAAO,aAAA,CAAAgK,IAAA;MAAA,MAAA9J,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkK,mBAAA,EAAqB;IAAA,EAAC;IAC/B3K,EAAA,CAAAE,MAAA,eACA;IAOPF,EAJM,CAAAG,YAAA,EAAS,EAEL,EACF,EACF;;;;IAlGgCH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,cAAAL,MAAA,CAAAmK,SAAA,CAAuB;;;AD9e7D,OAAM,MAAOC,mBAAmB;EAoCpBC,KAAA;EACAC,MAAA;EACAC,YAAA;EACAC,GAAA;EACAC,EAAA;EAGAC,UAAA;EACIC,wBAAA;EAEJC,cAAA;EA7CRT,SAAS;EAEJU,QAAQ,GAAkB,IAAI;EAC9B9I,MAAM,GAAQ,IAAI;EAClBb,SAAS,GAAY,KAAK,CAAC,CAAC;EAC5BC,YAAY,GAAU,EAAE;EACxB+C,kBAAkB,GAAW,CAAC,CAAC,CAAC;EAChC4G,iBAAiB,GAAQ,EAAE;EAC3BC,mBAAmB,GAAQ,EAAE;EACpCvK,WAAW,GAAQ,SAAS;EAC5BkJ,gBAAgB,GAAO,EAAE;EAClBsB,eAAe,GAAU,EAAE;EAC3B9F,YAAY,GAAW,EAAE;EACzBtD,kBAAkB,GAA6I,EAAE;EACjKqJ,2BAA2B,GAAQ,IAAI;EACvCC,eAAe,GAAU,EAAE;EAC3BC,SAAS,GAAM,EAAE;EACjBC,OAAO,GAAY,KAAK;EAC/BC,YAAY;EACLC,kBAAkB,GAAgB,IAAIC,GAAG,EAAE;EAC3CC,eAAe,GAAgB,IAAID,GAAG,EAAE;EACxCE,kBAAkB,GAA8B,EAAE;EACjDC,iBAAiB,GAAiB,IAAI3M,YAAY,EAAE;EACpD4M,uBAAuB,GAAiB,IAAI5M,YAAY,EAAE;EACpE6M,UAAU,GAAG,CACX;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,EACrC;IAAED,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC7C;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAE,EACzC;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,CACtC;EACC;EACOC,YAAY,GAAW,aAAa,CAAC,CAAC;EACtCC,SAAS,GAAkB,IAAI;EAEtCC,YACU5B,KAAqB,EACrBC,MAAc,EACdC,YAAsB,EACtBC,GAAsB,EACtBC,EAAe;EAC3B;EAEYC,UAAqB,EACjBC,wBAAkD,EAEtDC,cAA8B;IAV9B,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,EAAE,GAAFA,EAAE;IAGF,KAAAC,UAAU,GAAVA,UAAU;IACN,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAE5B,KAAAC,cAAc,GAAdA,cAAc;EACrB;EAEHsB,QAAQA,CAAA;IACN,IAAI,CAACf,SAAS,GAAG,IAAI,CAACT,UAAU,CAACyB,eAAe,EAAE;IAClD,IAAI,CAACf,OAAO,GAAG,IAAI,CAACgB,YAAY,EAAE;IAElC;IACA,IAAI,CAACT,uBAAuB,GAAG,IAAI,CAACtB,KAAK,CAACgC,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACvE,IAAI,CAACR,YAAY,GAAGQ,MAAM,CAAC,MAAM,CAAC,IAAI,aAAa;MACnD,IAAI,CAACP,SAAS,GAAGO,MAAM,CAAC,WAAW,CAAC,GAAGC,MAAM,CAACD,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI;MACzEE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;QAAEX,YAAY,EAAE,IAAI,CAACA,YAAY;QAAEC,SAAS,EAAE,IAAI,CAACA;MAAS,CAAE,CAAC;IAC5G,CAAC,CAAC;IAEF;IACA,IAAI,CAACN,iBAAiB,GAAG,IAAI,CAACrB,KAAK,CAACsC,QAAQ,CAACL,SAAS,CAACC,MAAM,IAAG;MAC9D,MAAMK,OAAO,GAAGL,MAAM,CAACM,GAAG,CAAC,IAAI,CAAC;MAChC,IAAI,CAAChC,QAAQ,GAAG+B,OAAO,GAAGJ,MAAM,CAACI,OAAO,CAAC,GAAG,IAAI;MAEhD,IAAI,IAAI,CAAC/B,QAAQ,EAAE;QACjB,IAAI,CAACiC,kBAAkB,EAAE;QACzB,IAAI,CAAC7H,oBAAoB,EAAE;QAC3B,IAAI,CAAC8H,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,EAAE;EACjB;EACAA,QAAQA,CAAA;IACN,IAAI,CAAC7C,SAAS,GAAG,IAAI,CAACM,EAAE,CAACwC,KAAK,CAAC;MAC9B3J,eAAe,EAAC,CAAC,EAAE,CAAC;MACpBC,aAAa,EAAC,CAAC,EAAE,CAAC;MAClBC,WAAW,EAAC,CAAC,EAAE;KACf,CAAC;IAEF;IACA,IAAI,CAACgH,GAAG,CAAC0C,aAAa,EAAE;EAC1B;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAAC0B,WAAW,EAAE;IACtC;IACA,IAAI,IAAI,CAACzB,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAACyB,WAAW,EAAE;IAC5C;EACF;EAEON,kBAAkBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACjC,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAAC3J,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC0J,cAAc,CAACyC,SAAS,CAAC;MAAExC,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE,CAAC,CAACyB,SAAS,CAAC;MACnEgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrM,SAAS,GAAG,KAAK;QACtBuL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEa,GAAG,CAAC;QACxC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB,IAAI,CAACzL,MAAM,GAAGwL,GAAG,CAACE,YAAY,EAAEC,IAAI,IAAIH,GAAG,CAACE,YAAY,IAAI,IAAI;UAChEhB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC3K,MAAM,CAAC;UACjD0K,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC3K,MAAM,EAAE4H,UAAU,CAAC;UAChE8C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7L,MAAM,IAAI,EAAE,CAAC,CAAC;UACjE,IAAI,CAAC2H,gBAAgB,GAAG,IAAI,CAAC3H,MAAM,EAAE2H,gBAAgB,IAAI,EAAE;UAC3D;UACA,IAAI,CAAClJ,WAAW,GAAG,SAAS;QAC9B,CAAC,MAAM;UACLiM,OAAO,CAACoB,KAAK,CAAC,qBAAqB,EAAEN,GAAG,CAACO,YAAY,CAAC;UACtD,IAAI,CAAC/L,MAAM,GAAG,IAAI;QACpB;QACA,IAAI,CAACyI,GAAG,CAACuD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3M,SAAS,GAAG,KAAK;QACtB,IAAI,CAACsJ,GAAG,CAACuD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEO9I,oBAAoBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAC4F,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAAC3J,SAAS,GAAG,IAAI;IACrB,IAAI,CAACgE,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC0F,cAAc,CAACoD,aAAa,CAAC;MAAEnD,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE,CAAC,CAACyB,SAAS,CAAC;MACvEgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrM,SAAS,GAAG,KAAK;QACtB,IAAIqM,GAAG,EAAEC,OAAO,EAAE;UAChB,IAAI,CAACtI,YAAY,GAAGqI,GAAG,CAACO,YAAY,IAAI,wBAAwB;QAClE,CAAC,MAAM;UACL,MAAMG,OAAO,GAAGV,GAAG,CAACE,YAAY,EAAEQ,OAAO,IAAI,EAAE;UAC/C,IAAI,CAACjD,eAAe,GAAGiD,OAAO,CAACC,GAAG,CAAEC,CAAM,KAAM;YAC9ClH,UAAU,EAACkH,CAAC,CAAClH,UAAU;YACvBS,IAAI,EAAEyG,CAAC,CAACC,QAAQ;YAChBxG,QAAQ,EAAEuG,CAAC,CAACE,UAAU;YACtB1G,MAAM,EAAEwG,CAAC,CAACG,UAAU;YACpB7J,aAAa,EAAE0J,CAAC,CAACI,aAAa,GAAG,IAAIC,IAAI,CAACL,CAAC,CAACI,aAAa,CAAC,GAAG,IAAI;YACjE1G,OAAO,EAAEsG,CAAC,CAACM,OAAO,GAAG,IAAID,IAAI,CAACL,CAAC,CAACM,OAAO,CAAC,GAAG,IAAI;YAC/CnG,YAAY,EAAE6F,CAAC,CAAC7F,YAAY,GAAG,IAAIkG,IAAI,CAACL,CAAC,CAAC7F,YAAY,CAAC,GAAG,IAAI;YAC9DzB,QAAQ,EAAEsH,CAAC,CAACzH,QAAQ;YACpBC,WAAW,EAAEwH,CAAC,CAACO,WAAW,IAAI,EAAE;YAChCC,WAAW,EAAER,CAAC,CAACS,WAAW;YAC1BnH,WAAW,EAAE0G,CAAC,CAAC1G,WAAW;YAC1BoH,cAAc,EAAEV,CAAC,CAACU,cAAc;YAChCjJ,oBAAoB,EAAEuI,CAAC,CAACvI,oBAAoB;YAC5CC,kBAAkB,EAAEsI,CAAC,CAACtI;WACvB,CAAC,CAAC;UAEH;UACA,MAAMiJ,WAAW,GAA6B,EAAE;UAChD,IAAI,CAAC9D,eAAe,CAAC+D,OAAO,CAAEC,EAAO,IAAI;YACvC,MAAMC,GAAG,GAAGC,MAAM,CAACF,EAAE,CAACL,WAAW,IAAI,SAAS,CAAC;YAC/C,IAAI,CAACG,WAAW,CAACG,GAAG,CAAC,EAAE;cAAEH,WAAW,CAACG,GAAG,CAAC,GAAG,EAAE;YAAE;YAChDH,WAAW,CAACG,GAAG,CAAC,CAACE,IAAI,CAACH,EAAE,CAAC;UAC3B,CAAC,CAAC;UAEF,IAAI,CAACpN,kBAAkB,GAAG+L,MAAM,CAACC,IAAI,CAACkB,WAAW,CAAC,CAACZ,GAAG,CAAEe,GAAG,IAAI;YAC7D,MAAMG,KAAK,GAAGN,WAAW,CAACG,GAAG,CAAC;YAC9B;YACA,MAAMI,WAAW,GAAQ;cACvB,oBAAoB,EAAE,CAAC;cACvB,cAAc,EAAE,CAAC;cACjB,wBAAwB,EAAE,CAAC;cAC3B,UAAU,EAAE;aACb;YACD,MAAMhH,eAAe,GAAG+G,KAAK,CAACE,MAAM,CAAC,CAACC,GAAW,EAAEC,EAAO,KAAI;cAC5D,MAAMC,CAAC,GAAGJ,WAAW,CAACE,GAAG,CAAC,IAAI,CAAC;cAAE,MAAMG,CAAC,GAAGL,WAAW,CAACG,EAAE,CAAC7H,MAAM,CAAC,IAAI,CAAC;cAAE,OAAO+H,CAAC,GAAGD,CAAC,GAAGD,EAAE,CAAC7H,MAAM,GAAG4H,GAAG;YACxG,CAAC,EAAE,EAAE,CAAC;YAEN;YACA,MAAM1H,OAAO,GAAGuH,KAAK,CAACE,MAAM,CAAC,CAACC,GAAgB,EAAEC,EAAO,KAAI;cACzD,IAAI,CAACA,EAAE,CAAC3H,OAAO,EAAE;gBAAE,OAAO0H,GAAG;cAAE;cAC/B,IAAI,CAACA,GAAG,EAAE;gBAAE,OAAOC,EAAE,CAAC3H,OAAO;cAAE;cAC/B,OAAO0H,GAAG,GAAGC,EAAE,CAAC3H,OAAO,GAAG2H,EAAE,CAAC3H,OAAO,GAAG0H,GAAG,CAAC,CAAC;YAC9C,CAAC,EAAE,IAAmB,CAAC;YAEvB,MAAM9K,aAAa,GAAG2K,KAAK,CAACE,MAAM,CAAC,CAACC,GAAgB,EAAEC,EAAO,KAAI;cAC/D,IAAI,CAACA,EAAE,CAAC/K,aAAa,EAAE;gBAAE,OAAO8K,GAAG;cAAE;cACrC,IAAI,CAACA,GAAG,EAAE;gBAAE,OAAOC,EAAE,CAAC/K,aAAa;cAAE;cACrC,OAAO8K,GAAG,GAAGC,EAAE,CAAC/K,aAAa,GAAG+K,EAAE,CAAC/K,aAAa,GAAG8K,GAAG,CAAC,CAAC;YAC1D,CAAC,EAAE,IAAmB,CAAC;YAEvB;YACA,MAAMjH,YAAY,GAAG8G,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAAClH,YAAY,CAAC,EAAEA,YAAY,IACvD8G,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAACI,WAAW,CAAC,EAAEA,WAAW,IACpD,IAAI;YAExB;YACA,MAAMf,cAAc,GAAGO,KAAK,CAAC,CAAC,CAAC,EAAEP,cAAc,IAAI,aAAaI,GAAG,EAAE;YAErE,OAAO;cACLzG,EAAE,EAAEyG,GAAG;cACP7K,KAAK,EAAEyK,cAAc;cACrBxG,eAAe,EAAEA,eAAe,IAAK+G,KAAK,CAAC,CAAC,CAAC,EAAEzH,MAAM,IAAI,EAAG;cAC5DW,YAAY,EAAEA,YAAY,GAAG,IAAIkG,IAAI,CAAClG,YAAY,CAAC,GAAG,IAAI;cAC1DT,OAAO,EAAEA,OAAO;cAChBpD,aAAa,EAAEA;aAChB;UACH,CAAC,CAAC,CAACoL,IAAI,CAAC,CAACJ,CAAC,EAAEC,CAAC,KAAI;YACf;YACA,IAAI,CAACD,CAAC,CAACnH,YAAY,IAAI,CAACoH,CAAC,CAACpH,YAAY,EAAE,OAAO,CAAC;YAChD,IAAI,CAACmH,CAAC,CAACnH,YAAY,EAAE,OAAO,CAAC;YAC7B,IAAI,CAACoH,CAAC,CAACpH,YAAY,EAAE,OAAO,CAAC,CAAC;YAC9B,OAAOoH,CAAC,CAACpH,YAAY,CAACwH,OAAO,EAAE,GAAGL,CAAC,CAACnH,YAAY,CAACwH,OAAO,EAAE;UAC5D,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAAClO,kBAAkB,CAACR,MAAM,GAAG,CAAC,EAAE;YACtC,IAAI,CAAC6J,2BAA2B,GAAG,IAAI,CAACrJ,kBAAkB,CAAC,CAAC,CAAC,CAAC4G,EAAE;YAChE,IAAI,CAACsC,iBAAiB,GAAG,IAAI,CAAClJ,kBAAkB,CAAC,CAAC,CAAC,CAACwC,KAAK;YACzD,IAAI,CAAC2G,mBAAmB,GAAG,IAAI,CAACnJ,kBAAkB,CAAC,CAAC,CAAC,CAACyG,eAAe;UACvE;QACF;QACA,IAAI,CAACmC,GAAG,CAACuD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAAC7O,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgE,YAAY,GAAG,wBAAwB;QAC5C,IAAI,CAACsF,GAAG,CAACuD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOhB,oBAAoBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAClC,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAAC3J,SAAS,GAAG,IAAI;IAErB,IAAI,CAAC0J,cAAc,CAACoF,kBAAkB,CAAC;MACrCnF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBoF,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE;KACP,CAAC,CAAC5D,SAAS,CAAC;MACXgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrM,SAAS,GAAG,KAAK;QACtB,IAAIqM,GAAG,EAAEC,OAAO,EAAE;UAChBf,OAAO,CAACoB,KAAK,CAAC,kCAAkC,EAAEN,GAAG,CAACO,YAAY,CAAC;QACrE,CAAC,MAAM;UACL,IAAI,CAAC5C,eAAe,GAAGqC,GAAG,CAACE,YAAY,EAAEC,IAAI,IAAIH,GAAG,CAACG,IAAI,IAAI,EAAE;UAE/D;UACA,IAAI,CAACvM,YAAY,GAAG,IAAI,CAAC+J,eAAe,CAACgD,GAAG,CAAEiC,MAAW,KAAM;YAC7DlJ,UAAU,EAAEkJ,MAAM,CAAClJ,UAAU;YAC7B7C,KAAK,EAAE+L,MAAM,CAACtB,cAAc;YAC5BA,cAAc,EAAEsB,MAAM,CAACtB,cAAc;YAAE;YACvCtK,eAAe,EAAE4L,MAAM,CAAC5L,eAAe;YACvC6L,cAAc,EAAED,MAAM,CAACC,cAAc;YACrCC,kBAAkB,EAAEF,MAAM,CAACE,kBAAkB;YAC7CC,UAAU,EAAEH,MAAM,CAACG,UAAU;YAC7B5L,gBAAgB,EAAEyL,MAAM,CAACzL,gBAAgB;YACzCL,0BAA0B,EAAE8L,MAAM,CAAC9L,0BAA0B;YAC7DG,YAAY,EAAE2L,MAAM,CAAC3L,YAAY,GAAG,IAAIgK,IAAI,CAAC2B,MAAM,CAAC3L,YAAY,CAAC,GAAG,IAAI;YACxEC,aAAa,EAAE0L,MAAM,CAAC1L,aAAa,GAAG,IAAI+J,IAAI,CAAC2B,MAAM,CAAC1L,aAAa,CAAC,GAAG,IAAI;YAC3EwJ,OAAO,EAAE,CAAC;cACRvG,IAAI,EAAEyI,MAAM,CAACtB,cAAc;cAC3BtK,eAAe,EAAE4L,MAAM,CAAC5L,eAAe;cACvC6L,cAAc,EAAED,MAAM,CAACC,cAAc;cACrCC,kBAAkB,EAAEF,MAAM,CAACE,kBAAkB;cAC7CC,UAAU,EAAEH,MAAM,CAACG,UAAU;cAC7B5L,gBAAgB,EAAEyL,MAAM,CAACzL,gBAAgB;cACzCL,0BAA0B,EAAE8L,MAAM,CAAC9L,0BAA0B;cAC7DG,YAAY,EAAE2L,MAAM,CAAC3L,YAAY,GAAG,IAAIgK,IAAI,CAAC2B,MAAM,CAAC3L,YAAY,CAAC,GAAG,IAAI;cACxEC,aAAa,EAAE0L,MAAM,CAAC1L,aAAa,GAAG,IAAI+J,IAAI,CAAC2B,MAAM,CAAC1L,aAAa,CAAC,GAAG;aACxE;WACF,CAAC,CAAC;UAEH;UACA,IAAI,IAAI,CAACtD,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;YAChC,IAAI,CAAC8C,kBAAkB,GAAG,CAAC;YAC3B,IAAI,CAAC4G,iBAAiB,GAAG,IAAI,CAAC3J,YAAY,CAAC,CAAC,CAAC,CAACiD,KAAK;YACnD,IAAI,CAAC2G,mBAAmB,GAAG,IAAI,CAAC5J,YAAY,CAAC,CAAC,CAAC,CAACkD,0BAA0B,IAAI,SAAS;UACzF;QACF;QACA,IAAI,CAACmG,GAAG,CAACuD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAAC7O,SAAS,GAAG,KAAK;QACtBuL,OAAO,CAACoB,KAAK,CAAC,iCAAiC,EAAEkC,GAAG,CAAC;QACrD,IAAI,CAACvF,GAAG,CAACuD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOhN,0BAA0BA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACmK,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC9J,MAAM,KAAK,CAAC,EAAE;MAAE;IAAQ;IAE1E,MAAMmP,OAAO,GAAkC,EAAE;IACjD,IAAI,CAACrF,eAAe,CAAC6D,OAAO,CAAEZ,CAAM,IAAI;MACtC,MAAMc,GAAG,GAAGd,CAAC,CAACU,cAAc,IAAI,eAAe;MAC/C,IAAI,CAAC0B,OAAO,CAACtB,GAAG,CAAC,EAAE;QAAEsB,OAAO,CAACtB,GAAG,CAAC,GAAG,EAAE;MAAE;MACxCsB,OAAO,CAACtB,GAAG,CAAC,CAACE,IAAI,CAAChB,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,MAAMqC,GAAG,GAAG,IAAInR,KAAK,CAAC;MAAEoR,WAAW,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAE,CAAC;IAC5E,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;IAClD;IACA,MAAMC,MAAM,GAAG;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAE,CAAE;IAC/C,IAAIC,CAAC,GAAGJ,MAAM,CAACG,GAAG;IAElB,MAAME,WAAW,GAAGA,CAACC,QAAgB,EAAElC,KAAY,KAAI;MACrD;MACAoB,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCf,GAAG,CAACgB,WAAW,CAAC,EAAE,CAAC;MACnBhB,GAAG,CAAC3E,IAAI,CAAC,GAAGyF,QAAQ,CAACG,WAAW,EAAE,kBAAkB,EAAEb,SAAS,GAAG,CAAC,EAAEQ,CAAC,EAAE;QACtEM,KAAK,EAAE;OACR,CAAC;MACFN,CAAC,IAAI,EAAE;MAEP;MACA,MAAMO,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAItG,GAAG,CAAC6D,KAAK,CACvClB,GAAG,CAAEsB,EAAO,IAAK,CAACA,EAAE,CAAC9K,gBAAgB,IAAI,EAAE,EAAEoN,QAAQ,EAAE,CAACC,IAAI,EAAE,CAAC,CAC/DC,MAAM,CAAEC,CAAS,IAAKA,CAAC,CAAC,CAAC,CAAC;MAC7BzB,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCf,GAAG,CAACgB,WAAW,CAAC,EAAE,CAAC;MACnBhB,GAAG,CAAC3E,IAAI,CAAC,aAAa8F,SAAS,CAACO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,EAAEtB,SAAS,GAAG,CAAC,EAAEQ,CAAC,EAAE;QAAEM,KAAK,EAAE;MAAQ,CAAE,CAAC;MAC7FN,CAAC,IAAI,EAAE;MAEP;MACA,MAAM5M,YAAY,GAAG4K,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAAChL,YAAY,CAAC,EAAEA,YAAY;MAC3E,MAAM2N,aAAa,GAAG/C,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAAC/K,aAAa,CAAC,EAAEA,aAAa;MAC9E+L,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCf,GAAG,CAACgB,WAAW,CAAC,CAAC,CAAC;MAClBhB,GAAG,CAAC3E,IAAI,CACN,kBAAkBrH,YAAY,GAAG,IAAIgK,IAAI,CAAChK,YAAY,CAAC,CAAC4N,kBAAkB,EAAE,GAAG,EAAE,EAAE,EACnFpB,MAAM,CAACC,IAAI,EACXG,CAAC,CACF;MACDZ,GAAG,CAAC3E,IAAI,CACN,mBAAmBsG,aAAa,GAAG,IAAI3D,IAAI,CAAC2D,aAAa,CAAC,CAACC,kBAAkB,EAAE,GAAG,EAAE,EAAE,EACtFxB,SAAS,GAAGI,MAAM,CAACE,KAAK,EACxBE,CAAC,EACD;QAAEM,KAAK,EAAE;MAAO,CAAE,CACnB;MACDN,CAAC,IAAI,CAAC;MAEN,MAAMiB,IAAI,GAAGjD,KAAK,CAAClB,GAAG,CAAC,CAACsB,EAAE,EAAE8C,GAAG,KAAK,CAClC,CAACA,GAAG,GAAG,CAAC,EAAER,QAAQ,EAAE,EACpBtC,EAAE,CAACjL,eAAe,IAAI,EAAE,EACxB,CAACiL,EAAE,CAACY,cAAc,IAAI,EAAE,EAAE0B,QAAQ,EAAE,EACpC,CAACtC,EAAE,CAACc,UAAU,IAAI,EAAE,EAAEwB,QAAQ,EAAE,EAChCtC,EAAE,CAACnL,0BAA0B,IAAI,EAAE,CACpC,CAAC;MAEF/E,SAAS,CAACkR,GAAG,EAAE;QACb+B,MAAM,EAAEnB,CAAC,GAAG,CAAC;QACboB,IAAI,EAAE,CAAC,CACL,GAAG,EACH,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,QAAQ,CACT,CAAC;QACFC,IAAI,EAAEJ,IAAI;QACVrB,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDwB,MAAM,EAAE;UAAEC,IAAI,EAAE,WAAW;UAAEC,QAAQ,EAAE,CAAC;UAAEC,WAAW,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAE;QACzEC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE,GAAG;UAAEC,MAAM,EAAE,QAAQ;UAAEN,QAAQ,EAAE;QAAC,CAAE;QACxF;QACA;QACAO,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEF,MAAM,EAAE;UAAQ,CAAE;UAAI;UAC1C,CAAC,EAAE;YAAEE,SAAS,EAAE;UAAE,CAAE;UAAsB;UAC1C,CAAC,EAAE;YAAEA,SAAS,EAAE;UAAG,CAAE;UAAqB;UAC1C,CAAC,EAAE;YAAEA,SAAS,EAAE;UAAG,CAAE;UAAqB;UAC1C,CAAC,EAAE;YAAEA,SAAS,EAAE;UAAE,CAAE,CAAsB;SAC3C;QACDC,KAAK,EAAE;OACR,CAAC;MAEF;MACA;MACAjC,CAAC,GAAIZ,GAAW,CAAC8C,aAAa,CAACC,MAAM,GAAG,EAAE;MAE1C;MACA,IAAInC,CAAC,GAAGZ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAAC0C,SAAS,EAAE,GAAG,GAAG,EAAE;QAC/ChD,GAAG,CAACiD,OAAO,EAAE;QACbrC,CAAC,GAAGJ,MAAM,CAACG,GAAG;MAChB;IACF,CAAC;IAEDxD,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAACxB,OAAO,CAAC,CAACuC,QAAQ,EAAEgB,GAAG,KAAI;MAC7C,IAAIA,GAAG,GAAG,CAAC,IAAIlB,CAAC,GAAGJ,MAAM,CAACG,GAAG,GAAG,EAAE,EAAE;QAClC;QACAX,GAAG,CAACkD,YAAY,CAAC,GAAG,CAAC;QACrBlD,GAAG,CAACmD,IAAI,CAAC3C,MAAM,CAACC,IAAI,EAAEG,CAAC,GAAG,EAAE,EAAER,SAAS,GAAGI,MAAM,CAACE,KAAK,EAAEE,CAAC,GAAG,EAAE,CAAC;MACjE;MACAC,WAAW,CAACC,QAAQ,EAAEf,OAAO,CAACe,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAMsC,QAAQ,GAAG,oBAAoB,IAAI,CAAC7R,MAAM,EAAE0H,YAAY,IAAI,EAAE,IAAI,IAAI+E,IAAI,EAAE,CAACqF,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IACpHtD,GAAG,CAACuD,IAAI,CAACH,QAAQ,CAAC;EACpB;EAEOI,uBAAuBA,CAACxL,EAAO;IACpC,IAAI,CAACyC,2BAA2B,GAAGzC,EAAE;IACrC,MAAMyL,GAAG,GAAG,IAAI,CAACrS,kBAAkB,CAAC+N,IAAI,CAACuE,CAAC,IAAIhF,MAAM,CAACgF,CAAC,CAAC1L,EAAE,CAAC,KAAK0G,MAAM,CAAC1G,EAAE,CAAC,CAAC;IAC1E,IAAIyL,GAAG,EAAE;MACP,IAAI,CAACnJ,iBAAiB,GAAGmJ,GAAG,CAAC7P,KAAK;MAClC,IAAI,CAAC2G,mBAAmB,GAAGkJ,GAAG,CAAC5L,eAAe;IAChD;EACF;EAEO8L,sCAAsCA,CAAA;IAC3C,IAAI,CAAC,IAAI,CAAClJ,2BAA2B,EAAE;MAAE,OAAO,EAAE;IAAE;IACpD,OAAO,IAAI,CAAC1C,8BAA8B,CAAC,IAAI,CAAC0C,2BAA2B,CAAC;EAC9E;EAEO1C,8BAA8BA,CAACoG,WAAgB;IACpD,MAAMV,OAAO,GAAG,IAAI,CAACjD,eAAe,CAACgH,MAAM,CAAC7D,CAAC,IAAIe,MAAM,CAACf,CAAC,CAACQ,WAAW,CAAC,KAAKO,MAAM,CAACP,WAAW,CAAC,CAAC;IAE/F;IACA,OAAOV,OAAO,CAAC4B,IAAI,CAAC,CAACJ,CAAC,EAAEC,CAAC,KAAI;MAC3B;MACA,IAAID,CAAC,CAAChI,WAAW,KAAKiI,CAAC,CAACjI,WAAW,EAAE;QACnC,OAAO,CAAC;MACV;MAEA;MACA,IAAI,CAACgI,CAAC,CAAChI,WAAW,IAAIiI,CAAC,CAACjI,WAAW,EAAE;QACnC,OAAO,CAAC,CAAC;MACX;MAEA;MACA,IAAIgI,CAAC,CAAChI,WAAW,IAAI,CAACiI,CAAC,CAACjI,WAAW,EAAE;QACnC,OAAO,CAAC;MACV;MAEA,OAAO,CAAC;IACV,CAAC,CAAC,CAAC2M,OAAO,EAAE,CAAC,CAAC;EAChB;EAEOnM,wBAAwBA,CAACrE,KAAa;IAC3C,IAAI,IAAI,CAAC0H,kBAAkB,CAAC+I,GAAG,CAACzQ,KAAK,CAAC,EAAE;MACtC,IAAI,CAAC0H,kBAAkB,CAACgJ,MAAM,CAAC1Q,KAAK,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAAC0H,kBAAkB,CAACiJ,GAAG,CAAC3Q,KAAK,CAAC;IACpC;EACF;EAEOuE,mBAAmBA,CAACvE,KAAa;IACtC,OAAO,IAAI,CAAC0H,kBAAkB,CAAC+I,GAAG,CAACzQ,KAAK,CAAC;EAC3C;EAEO/B,wBAAwBA,CAAA;IAC7B,OAAO,IAAI,CAACD,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACR,MAAM,GAAG,CAAC,IAAI,IAAI,CAACkK,kBAAkB,CAACkJ,IAAI,KAAK,IAAI,CAAC5S,kBAAkB,CAACR,MAAM;EACzI;EAEOG,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACK,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACR,MAAM,KAAK,CAAC,EAAE;MACpE;IACF;IACA,IAAI,IAAI,CAACS,wBAAwB,EAAE,EAAE;MACnC,IAAI,CAACyJ,kBAAkB,CAACmJ,KAAK,EAAE;IACjC,CAAC,MAAM;MACL,IAAI,CAACnJ,kBAAkB,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAC3J,kBAAkB,CAACsM,GAAG,CAAC,CAACwG,CAAC,EAAEpC,GAAG,KAAKA,GAAG,CAAC,CAAC;IACjF;IACA,IAAI,CAAC9H,GAAG,CAACuD,YAAY,EAAE;EACzB;EAEO/G,qBAAqBA,CAAC2N,QAAgB;IAC3C,IAAI,IAAI,CAACnJ,eAAe,CAAC6I,GAAG,CAACM,QAAQ,CAAC,EAAE;MACtC,IAAI,CAACnJ,eAAe,CAAC8I,MAAM,CAACK,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACnJ,eAAe,CAAC+I,GAAG,CAACI,QAAQ,CAAC;MAClC;MACA,IAAI,CAAC,IAAI,CAAClJ,kBAAkB,CAACkJ,QAAQ,CAAC,EAAE;QACtC,IAAI,CAAClJ,kBAAkB,CAACkJ,QAAQ,CAAC,GAAG,aAAa;MACnD;IACF;EACF;EAEOpN,gBAAgBA,CAACoN,QAAgB;IACtC,OAAO,IAAI,CAACnJ,eAAe,CAAC6I,GAAG,CAACM,QAAQ,CAAC;EAC3C;EAEOC,aAAaA,CAACD,QAAgB,EAAEE,GAAW,EAAEhV,MAAW;IAC7DA,MAAM,CAACmE,eAAe,EAAE;IACxB,IAAI,CAACyH,kBAAkB,CAACkJ,QAAQ,CAAC,GAAGE,GAAG;IACvC,IAAI,CAACrK,GAAG,CAACuD,YAAY,EAAE;EACzB;EAEO+G,oBAAoBA,CAAC3E,MAAW;IACrC,IAAI,CAACjP,SAAS,GAAG,IAAI;IACrB,MAAM6T,QAAQ,GAAG;MACfnP,oBAAoB,EAAEuK,MAAM,CAACvK,oBAAoB;MACjDC,kBAAkB,EAAEsK,MAAM,CAACtK,kBAAkB;MAC7CgF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB5D,UAAU,EAAEkJ,MAAM,CAAClJ,UAAU;MAC7B+N,cAAc,EAAE,IAAI,CAAC7J,SAAS,CAAC8J;KAChC;IAED,IAAI,CAACrK,cAAc,CAACsK,oBAAoB,CAACH,QAAQ,CAAC,CAACzI,SAAS,CAAC;MAC3DgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrM,SAAS,GAAG,KAAK;QACtB,IAAIqM,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAI,CAAC7C,wBAAwB,CAACwK,WAAW,CAAC5H,GAAG,CAACE,YAAY,CAAC2H,OAAO,EAAE,EAAE,CAAC;UACvE;UACA,IAAI,CAACnQ,oBAAoB,EAAE;QAC7B,CAAC,MAAM;UACL,IAAI,CAAC0F,wBAAwB,CAAC0K,SAAS,CAAC9H,GAAG,CAACO,YAAY,IAAE,wBAAwB,EAAE,EAAE,CAAC;UACvF;QACF;QACA,IAAI,CAACtD,GAAG,CAACuD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAAC7O,SAAS,GAAG,KAAK;QACtB,IAAI,CAACyJ,wBAAwB,CAAC0K,SAAS,CAAC,kCAAkC,EAAE,EAAE,CAAC;QAC/E;QACA5I,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAACvF,GAAG,CAACuD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOuH,mBAAmBA,CAAC3G,WAAgB;IACzC,OAAOO,MAAM,CAAC,IAAI,CAACjE,2BAA2B,CAAC,KAAKiE,MAAM,CAACP,WAAW,CAAC;EACzE;EAEO9K,WAAWA,CAACD,KAAa;IAC9B,IAAI,CAACM,kBAAkB,GAAGN,KAAK;IAC/B,IAAI,CAACkH,iBAAiB,GAAG,IAAI,CAAC3J,YAAY,CAAC,IAAI,CAAC+C,kBAAkB,CAAC,CAACE,KAAK;IACzE,IAAI,CAAC2G,mBAAmB,GACtB,IAAI,CAAC5J,YAAY,CAAC,IAAI,CAAC+C,kBAAkB,CAAC,CAACmE,eAAe;EAC9D;EAEOkN,0BAA0BA,CAAA;IAC/B,IACE,IAAI,CAACrR,kBAAkB,KAAK,IAAI,IAChC,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAAC/C,YAAY,CAACC,MAAM,EACnD;MACA,OAAO,EAAE;IACX;IAEA,OAAO,IAAI,CAACD,YAAY,CAAC,IAAI,CAAC+C,kBAAkB,CAAC,CAAC+J,OAAO,IAAI,EAAE;EACjE;EAIOlF,MAAMA,CAAA;IACX0D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACX,YAAY,EAAE,YAAY,EAAE,IAAI,CAACC,SAAS,CAAC;IAC7F,IAAI,IAAI,CAACD,YAAY,KAAK,SAAS,IAAI,IAAI,CAACC,SAAS,EAAE;MACrD;MACAS,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,IAAI,CAACpC,MAAM,CAACkL,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAACxJ,SAAS,CAAC,EAAE;QACvDK,WAAW,EAAE;UAAEoJ,SAAS,EAAE;QAAS;OACpC,CAAC;IACJ,CAAC,MAAM;MACL;MACAhJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,IAAI,CAACpC,MAAM,CAACkL,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC;EACF;EAEO7T,UAAUA,CAAA;IACf+T,MAAM,CAACC,IAAI,CACT,GAAG,IAAI,CAAC5T,MAAM,CAAC6T,cAAc,GAAG,IAAI,CAAC7T,MAAM,CAACC,cAAc,EAAE,EAC5D,QAAQ,CACT;EACH;EAEO6T,iBAAiBA,CAAC1F,MAAW;IAClC,MAAM2F,QAAQ,GAAG,IAAI,CAACvL,YAAY,CAACoL,IAAI,CAAC3W,2BAA2B,EAAE;MACnEwV,IAAI,EAAE;KACP,CAAC;IACFsB,QAAQ,CAACC,iBAAiB,CAAC5F,MAAM,GAAGA,MAAM;IAC1C2F,QAAQ,CAACC,iBAAiB,CAAClL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDiL,QAAQ,CAACC,iBAAiB,CAACC,aAAa,GAAG,IAAI,CAACjU,MAAM;IAEtD;IACA+T,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAChR,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAACkR,KAAK,CAAEtI,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEOhK,cAAcA,CAACiF,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,MAAMyO,UAAU,GAAGzO,MAAM,CAAC0O,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAChF7J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE/E,MAAM,EAAE,aAAa,EAAEyO,UAAU,CAAC;IAC5E;IACA,MAAMG,QAAQ,GAA4B;MACxC,WAAW,EAAE,WAAW;MACxB,oBAAoB,EAAE,oBAAoB;MAC1C,uBAAuB,EAAE,uBAAuB;MAChD,aAAa,EAAE,aAAa;MAC5B,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE,UAAU;MACtB,UAAU,EAAE,UAAU;MACtB,cAAc,EAAE,cAAc;MAC9B,mBAAmB,EAAE,mBAAmB;MACxC,uBAAuB,EAAE,uBAAuB;MAChD,cAAc,EAAE,cAAc;MAC9B,cAAc,EAAE,cAAc;MAC9B,mBAAmB,EAAE,mBAAmB;MACxC,mBAAmB,EAAE,mBAAmB;MACxC,iBAAiB,EAAE;KACpB;IACD,MAAMC,QAAQ,GAAGD,QAAQ,CAACH,UAAU,CAAC,IAAIA,UAAU;IACnD,MAAMK,UAAU,GAAG,SAAS,GAAGD,QAAQ;IACvC/J,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+J,UAAU,CAAC;IAC9ChK,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,CAClD,gBAAgB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,iBAAiB,EAC7E,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,0BAA0B,EACvF,8BAA8B,EAAE,qBAAqB,EAAE,qBAAqB,EAC5E,kBAAkB,EAAE,0BAA0B,CAC/C,CAAC;IACF,OAAO+J,UAAU;EACnB;EAEOnS,cAAcA,CAACqD,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,EAAE;IACtB,MAAMyO,UAAU,GAAGzO,MAAM,CAAC0O,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAEhF,MAAMI,QAAQ,GAAyB;MACrC,mBAAmB,EAAE;QAAEC,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAClG,mBAAmB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAClG,iBAAiB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAChG,uBAAuB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACtG,cAAc,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC7F,cAAc,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC7F,WAAW,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC1F,SAAS,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACxF,UAAU,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACzF,WAAW,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC1F,UAAU,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB;KACxF;IAED,OAAOH,QAAQ,CAACN,UAAU,CAAC,IAAI,EAAE;EACnC;EAEAjW,OAAOA,CAAC0U,GAAQ,EAAEhV,MAAW;IAC3B,IAAIgV,GAAG,KAAK,UAAU,IAAI,CAAC,IAAI,CAACjL,uBAAuB,EAAE,EAAE;MACzD;IACF;IACA,IAAI,CAACpJ,WAAW,GAAGqU,GAAG;IACtB,IAAI,CAACrK,GAAG,CAACuD,YAAY,EAAE;EACzB;EAEOnE,uBAAuBA,CAAA;IAC5B,MAAMkN,IAAI,GAAG,CAAC,IAAI,CAACpN,gBAAgB,IAAI,IAAI,CAAC3H,MAAM,EAAE2H,gBAAgB,IAAI,EAAE,EAAEoI,QAAQ,EAAE,CAACuE,WAAW,EAAE;IACpG;IACA,OAAOS,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM;EAC/C;EAEA7V,QAAQA,CAAA;IACN,MAAM8V,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAACvL,YAAY,CAACoL,IAAI,CACrCzW,8BAA8B,EAC9B6X,eAAe,CAChB;IAED;IACAjB,QAAQ,CAACC,iBAAiB,CAAClL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDiL,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAAC7J,SAAS,CAAC8J,MAAM,CAAC,CAAC;IACnEa,QAAQ,CAACC,iBAAiB,CAACtM,YAAY,GAAG,IAAI,CAAC1H,MAAM,EAAE0H,YAAY,IAAI,EAAE;IAEzE;IACAqM,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAClJ,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAACoJ,KAAK,CAAEtI,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEA3I,kBAAkBA,CAACoT,WAAmB;IACpC,IAAIA,WAAW,GAAG,CAAC,IAAIA,WAAW,IAAI,IAAI,CAAChW,YAAY,CAACC,MAAM,EAAE;MAC9D;IACF;IAEA,MAAM2V,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MACVwC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE;KACb;IAED,MAAMpB,QAAQ,GAAG,IAAI,CAACvL,YAAY,CAACoL,IAAI,CACrCzW,8BAA8B,EAC9B6X,eAAe,CAChB;IAED;IACAjB,QAAQ,CAACC,iBAAiB,CAAClL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDiL,QAAQ,CAACC,iBAAiB,CAACqB,UAAU,GAAG,IAAI,CAACjW,YAAY,CAACgW,WAAW,CAAC;IACtErB,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAAC7J,SAAS,CAAC8J,MAAM,CAAC,CAAC;IACnEa,QAAQ,CAACC,iBAAiB,CAACtM,YAAY,GAAG,IAAI,CAAC1H,MAAM,EAAE0H,YAAY,IAAI,EAAE;IAEzE;IACAqM,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxB;QACA,IAAI,CAAClJ,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAACoJ,KAAK,CAAEtI,KAAK,IAAI;MACjBpB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEA2K,SAASA,CAAA;IACP,MAAMN,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAACvL,YAAY,CAACoL,IAAI,CACrCzW,8BAA8B,EAC9B6X,eAAe,CAChB;EACH;EACApW,UAAUA,CAAA;IACR,MAAMoW,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAACvL,YAAY,CAACoL,IAAI,CACrCxW,oBAAoB,EACpB4X,eAAe,CAChB;IACD;IACAjB,QAAQ,CAACC,iBAAiB,CAACvN,EAAE,GAAG,IAAI,CAACqC,QAAQ;IAE7C;IACAiL,QAAQ,CAACC,iBAAiB,CAACuB,SAAS,CAAChL,SAAS,CAAEiL,KAAc,IAAI;MAChE,IAAIA,KAAK,EAAE;QACT,IAAI,CAACzK,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC;EACJ;EAEArL,WAAWA,CAAC+V,CAAM;IAChB,IAAI,CAACtW,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmK,YAAY,GAAGmM,CAAC,IAAI,KAAK;IAC9B,IAAI,CAAC5M,cAAc,CAACnJ,WAAW,CAAC;MAAEoJ,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEQ,YAAY,EAAE,IAAI,CAACA,YAAY;MAAEoM,SAAS,EAAE;IAAI,CAAE,CAAC,CAACnL,SAAS,CAAC;MACvHgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrM,SAAS,GAAG,KAAK;QACtBuL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEa,GAAG,CAAC;QAClCd,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOa,GAAG,CAAC;QACzCd,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiB,MAAM,CAACC,IAAI,CAACL,GAAG,IAAI,EAAE,CAAC,CAAC;QACrDd,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEa,GAAG,EAAEmK,OAAO,CAAC;QAC9CjL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEa,GAAG,EAAE6H,OAAO,CAAC;QAC9C3I,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEa,GAAG,EAAEE,YAAY,CAAC;QAExD;QACA,IAAIA,YAAY,GAAGF,GAAG;QAEtB;QACA,IAAIA,GAAG,EAAEE,YAAY,EAAE;UACrBA,YAAY,GAAGF,GAAG,CAACE,YAAY;QACjC,CAAC,MAAM,IAAIF,GAAG,EAAEkF,IAAI,EAAEhF,YAAY,EAAE;UAClCA,YAAY,GAAGF,GAAG,CAACkF,IAAI,CAAChF,YAAY;QACtC,CAAC,MAAM,IAAIF,GAAG,EAAEkF,IAAI,EAAE;UACpBhF,YAAY,GAAGF,GAAG,CAACkF,IAAI;QACzB;QAEAhG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,YAAY,CAAC;QAChDhB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEe,YAAY,EAAEiK,OAAO,CAAC;QACpDjL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEe,YAAY,EAAE2H,OAAO,CAAC;QAEpD,IAAI3H,YAAY,EAAED,OAAO,EAAE;UACzB;UACA,IAAI,CAAC7C,wBAAwB,CAAC0K,SAAS,CAAC5H,YAAY,CAACK,YAAY,EAAE,EAAE,CAAC;QACxE,CAAC,MAAM,IAAIL,YAAY,EAAEiK,OAAO,KAAK,KAAK,EAAE;UAC1C;UACA,IAAIjK,YAAY,CAAC2H,OAAO,KAAK,oCAAoC,EAAE;YACzC,IAAI,CAACzK,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAI5H,YAAY,CAAC2H,OAAO,KAAK,mCAAmC,EAAE;YAC/C,IAAI,CAACzK,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAAC1K,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAI5H,YAAY,EAAEiK,OAAO,KAAK,IAAI,IAAIjK,YAAY,EAAEC,IAAI,EAAE;UAC/D,IAAI,CAAC/C,wBAAwB,CAACwK,WAAW,CAAC,4BAA4B,EAAE,EAAE,CAAC;UAC3E;UACA,IAAI,CAACrI,kBAAkB,EAAE;UACzB,IAAI,CAAC7H,oBAAoB,EAAE;QAC7B,CAAC,MAAM;UACL;UACAwH,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACA,IAAI,CAAC/B,wBAAwB,CAACwK,WAAW,CAAC,4BAA4B,EAAE,EAAE,CAAC;UAE3E,IAAI,CAACrI,kBAAkB,EAAE;UACzB,IAAI,CAAC7H,oBAAoB,EAAE;QAC7B;QACA,IAAI,CAACuF,GAAG,CAACuD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAAC7O,SAAS,GAAG,KAAK;QACtB;QACAuL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqD,GAAG,CAAC;QACnCtD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,OAAOqD,GAAG,CAAC;QACtCtD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiB,MAAM,CAACC,IAAI,CAACmC,GAAG,IAAI,EAAE,CAAC,CAAC;QAClDtD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqD,GAAG,EAAEpI,MAAM,CAAC;QACzC8E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqD,GAAG,EAAEqF,OAAO,CAAC;QAC3C3I,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEqD,GAAG,EAAElC,KAAK,CAAC;QAEvC;QACA;QACA,IAAIkC,GAAG,EAAE2H,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAI3H,GAAG,CAACqF,OAAO,KAAK,oCAAoC,EAAE;YAChC,IAAI,CAACzK,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAItF,GAAG,CAACqF,OAAO,KAAK,mCAAmC,EAAE;YACtC,IAAI,CAACzK,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAAC1K,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAItF,GAAG,EAAElC,KAAK,EAAEuH,OAAO,EAAE;UAC9B,IAAIrF,GAAG,CAAClC,KAAK,CAACuH,OAAO,KAAK,oCAAoC,EAAE;YACtC,IAAI,CAACzK,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAItF,GAAG,CAAClC,KAAK,CAACuH,OAAO,KAAK,mCAAmC,EAAE;YAC5C,IAAI,CAACzK,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAAC1K,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAItF,GAAG,EAAEpI,MAAM,KAAK,GAAG,EAAE;UACN,IAAI,CAACgD,wBAAwB,CAAC0K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAE3J;UACA;QACF,CAAC,MAAM;UACL;QAAA;QAEF5I,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAACvF,GAAG,CAACuD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEA4J,kBAAkBA,CAACxH,MAAU;IAC3B,MAAM4G,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAACvL,YAAY,CAACoL,IAAI,CACrCvW,2BAA2B,EAC3B2X,eAAe,CAChB;IAEDtK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEyD,MAAM,CAAC;IAClC;IACA2F,QAAQ,CAACC,iBAAiB,CAAClL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDiL,QAAQ,CAACC,iBAAiB,CAACqB,UAAU,GAAGjH,MAAM;IAC9C2F,QAAQ,CAACC,iBAAiB,CAACC,aAAa,GAAG,IAAI,CAACjU,MAAM;IACtD+T,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAAC7J,SAAS,CAAC8J,MAAM,CAAC,CAAC;IAEnE;IACAa,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAChR,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAACkR,KAAK,CAAEtI,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEOlH,iBAAiBA,CAACoS,UAAe,EAAEzH,MAAW;IACnD;IACA,MAAM2F,QAAQ,GAAG,IAAI,CAACvL,YAAY,CAACoL,IAAI,CAAC1W,sBAAsB,EAAE;MAC9DuV,IAAI,EAAE,IAAI;MACVwC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE;KACX,CAAC;IAEF;IACAnB,QAAQ,CAACC,iBAAiB,CAAC6B,UAAU,GAAGA,UAAU;IAClD9B,QAAQ,CAACC,iBAAiB,CAAC5F,MAAM,GAAGA,MAAM;IAC1C2F,QAAQ,CAACC,iBAAiB,CAAClL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDiL,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAAC7J,SAAS,CAAC8J,MAAM;IACjEa,QAAQ,CAACC,iBAAiB,CAAC3K,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACA0K,QAAQ,CAACC,iBAAiB,CAAC8B,iBAAiB,CAACvL,SAAS,CAAEyI,QAAa,IAAI;MACvE,IAAI,CAAC+C,cAAc,CAAC/C,QAAQ,EAAEe,QAAQ,CAAC;IACzC,CAAC,CAAC;IAEF;IACAA,QAAQ,CAACC,iBAAiB,CAACgC,iBAAiB,CAACzL,SAAS,CAAEoL,OAAgB,IAAI;MAC1E,IAAI,CAACA,OAAO,EAAE;QACZ;QACA5B,QAAQ,CAACC,iBAAiB,CAAC7U,SAAS,GAAG,KAAK;MAC9C;IACF,CAAC,CAAC;IAEF4U,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAC,MAAK;MACxB;IAAA,CACD,CAAC,CAACC,KAAK,CAAC,MAAK;MACZ;IAAA,CACD,CAAC;EACJ;EAEO2B,cAAcA,CAAC/C,QAAa,EAAEe,QAAc;IACjD,IAAI,CAACf,QAAQ,EAAE;MACb;IACF;IAEA,IAAI,CAAC7T,SAAS,GAAG,IAAI;IAErB,IAAI,CAAC0J,cAAc,CAACsK,oBAAoB,CAACH,QAAQ,CAAC,CAACzI,SAAS,CAAC;MAC3DgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrM,SAAS,GAAG,KAAK;QACtB,IAAIqM,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UACF,IAAI,CAAC7C,wBAAwB,CAACwK,WAAW,CAAC5H,GAAG,CAACE,YAAY,CAAC2H,OAAO,EAAE,EAAE,CAAC;UAE/F;UACA,IAAI,CAACnQ,oBAAoB,EAAE,CAAC,CAAC;UAE7B;UACA,IAAI6Q,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;YAC1CD,QAAQ,CAACC,iBAAiB,CAACgC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;UACzD;UAEA;UACA,IAAIlC,QAAQ,EAAE;YACZA,QAAQ,CAACmC,KAAK,EAAE;UAClB;QACF,CAAC,MAAM;UACL;UACwB,IAAI,CAACtN,wBAAwB,CAAC0K,SAAS,CAAC9H,GAAG,CAACO,YAAY,IAAE,2BAA2B,EAAE,EAAE,CAAC;UAElH;UACA,IAAIgI,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;YAC1CD,QAAQ,CAACC,iBAAiB,CAACgC,iBAAiB,CAACC,IAAI,CAAC,KAAK,CAAC;UAC1D;QACF;QACA,IAAI,CAACxN,GAAG,CAACuD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAAC7O,SAAS,GAAG,KAAK;QACtB;QACAuL,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QACM,IAAI,CAACpF,wBAAwB,CAACwK,WAAW,CAAC,2BAA2B,EAAE,EAAE,CAAC;QAElG;QACA,IAAIW,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;UAC1CD,QAAQ,CAACC,iBAAiB,CAACgC,iBAAiB,CAACC,IAAI,CAAC,KAAK,CAAC;QAC1D;QAEA,IAAI,CAACxN,GAAG,CAACuD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEO5G,iBAAiBA,CAACgJ,MAAW;IAClC,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA,IAAI;MACF,MAAM1G,YAAY,GAAG,IAAI,CAAC1H,MAAM,EAAE0H,YAAY,IAAI,EAAE;MACpD,MAAM7B,QAAQ,GAAG,CAACuI,MAAM,EAAE9B,UAAU,IAAI8B,MAAM,EAAE+H,oBAAoB,IAAI/H,MAAM,EAAEvI,QAAQ,IAAI,EAAE,EAAEkK,QAAQ,EAAE;MAC1G,MAAMqG,YAAY,GAAG,CAAChI,MAAM,EAAEzJ,QAAQ,KAAKyJ,MAAM,EAAEgI,YAAY,IAAI,EAAE,CAAC,EAAErG,QAAQ,EAAE;MAClF,MAAMsG,aAAa,GAAG,CAACjI,MAAM,EAAEvK,oBAAoB,IAAI,EAAE,EAAEkM,QAAQ,EAAE;MAErE;MACA,MAAMuG,cAAc,GAAG,IAAI,CAACzW,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACR,MAAM,GAAG,CAAC;MACnF,MAAMkX,KAAK,GAAGD,cAAc,GAAG,CAAC,GAAGA,cAAc,CAACvG,QAAQ,EAAE,GAAG,CAAC3B,MAAM,EAAEmI,KAAK,IAAInI,MAAM,EAAEoI,KAAK,IAAI,EAAE,EAAEzG,QAAQ,EAAE;MAEhH,MAAMnK,MAAM,GAAG,CAACwI,MAAM,EAAEqI,UAAU,IAAIrI,MAAM,EAAEsI,aAAa,IAAItI,MAAM,EAAExI,MAAM,IAAI,EAAE,EAAEmK,QAAQ,EAAE;MAC/F,MAAM4G,WAAW,GAAIvI,MAAM,EAAE5B,aAAa,IAAI4B,MAAM,EAAE1L,aAAa,IAAI0L,MAAM,EAAE1B,OAAO,IAAI0B,MAAM,EAAEP,WAAW,GACzG,IAAIpB,IAAI,CAAC2B,MAAM,EAAE5B,aAAa,IAAI4B,MAAM,EAAE1L,aAAa,IAAI0L,MAAM,EAAE1B,OAAO,IAAI0B,MAAM,EAAEP,WAAW,CAAC,CAACwC,kBAAkB,EAAE,GACvH,IAAI5D,IAAI,EAAE,CAAC4D,kBAAkB,EAAE;MAEnC,MAAM5B,GAAG,GAAG,IAAInR,KAAK,CAAC;QAAEoR,WAAW,EAAE,UAAU;QAAEC,IAAI,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAI,CAAE,CAAC;MAE5E,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;MAClD,MAAM4H,UAAU,GAAGnI,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAAC0C,SAAS,EAAE;MACpD,MAAMxC,MAAM,GAAG;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,GAAG,EAAE,EAAE;QAAEyH,MAAM,EAAE;MAAE,CAAE;MAE3D;MACApI,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCf,GAAG,CAACgB,WAAW,CAAC,EAAE,CAAC;MACnBhB,GAAG,CAAC3E,IAAI,CAAC,kBAAkBpC,YAAY,IAAI,KAAK,EAAE,EAAEuH,MAAM,CAACC,IAAI,EAAED,MAAM,CAACG,GAAG,CAAC;MAE5E,MAAMoB,MAAM,GAAGvB,MAAM,CAACG,GAAG,GAAG,EAAE;MAE9B;MACA,MAAM0H,OAAO,GAAG,CACd,aAAa,EACb,eAAe,EACf,gCAAgC,EAChC,OAAO,EACP,QAAQ,EACR,MAAM,CACP;MAED;MACA,MAAMC,cAAc,GAAQ,CAAC3I,MAAM,IAAKA,MAAc,CAACzB,WAAW,KAAMyB,MAAc,CAACxJ,WAAW,IAAI,EAAE;MACxG,MAAMoS,gBAAgB,GAAGnH,KAAK,CAACoH,OAAO,CAACF,cAAc,CAAC,GAAGA,cAAc,GAAIA,cAAc,GAAG,CAACA,cAAc,CAAC,GAAG,EAAG;MAClH,MAAMG,QAAQ,GAAUF,gBAAgB,CAAC3X,MAAM,GAAG,CAAC,GAC/C2X,gBAAgB,CAAC7K,GAAG,CAAEgL,CAAM,IAAK,CAC/BtR,QAAQ,IAAI,KAAK,EACjBsR,CAAC,EAAExS,QAAQ,IAAIyR,YAAY,IAAI,KAAK,EACpCe,CAAC,EAAEvT,QAAQ,IAAIuT,CAAC,EAAEtT,oBAAoB,IAAIwS,aAAa,IAAI,KAAK,EAChEE,KAAK,IAAI,KAAK,EACd3Q,MAAM,IAAI,KAAK,EACf,CAACuR,CAAC,EAAE3S,YAAY,GAAG,IAAIiI,IAAI,CAAC0K,CAAC,CAAC3S,YAAY,CAAC,CAAC6L,kBAAkB,EAAE,GAAGsG,WAAW,KAAK,KAAK,CACzF,CAAC,GACF,CAAC,CACC9Q,QAAQ,IAAI,KAAK,EACjBuQ,YAAY,IAAI,KAAK,EACrBC,aAAa,IAAI,KAAK,EACtBE,KAAK,IAAI,KAAK,EACd3Q,MAAM,IAAI,KAAK,EACf+Q,WAAW,IAAI,KAAK,CACrB,CAAC;MAENpZ,SAAS,CAACkR,GAAG,EAAE;QACb+B,MAAM;QACNC,IAAI,EAAE,CAACqG,OAAO,CAAC;QACfpG,IAAI,EAAEwG,QAAQ;QACdjI,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDwB,MAAM,EAAE;UAAEC,IAAI,EAAE,WAAW;UAAEC,QAAQ,EAAE,CAAC;UAAEC,WAAW,EAAE,CAAC;UAAEsG,QAAQ,EAAE;QAAW,CAAE;QACjFpG,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAEmG,SAAS,EAAE,MAAM;UAAExG,QAAQ,EAAE;QAAC,CAAE;QAChGO,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEC,SAAS,EAAE;UAAE,CAAE;UACpB,CAAC,EAAE;YAAEA,SAAS,EAAE,CAACxC,SAAS,GAAGI,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,IAAI;UAAI,CAAE;UACjE,CAAC,EAAE;YAAEkC,SAAS,EAAE,CAACxC,SAAS,GAAGI,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,IAAI;UAAI,CAAE;UACjE,CAAC,EAAE;YAAEkC,SAAS,EAAE,EAAE;YAAEF,MAAM,EAAE;UAAQ,CAAE;UACtC,CAAC,EAAE;YAAEE,SAAS,EAAE,EAAE;YAAEF,MAAM,EAAE;UAAQ,CAAE;UACtC,CAAC,EAAE;YAAEE,SAAS,EAAE,EAAE;YAAEF,MAAM,EAAE;UAAQ;SACrC;QACDmG,YAAY,EAAG3L,IAAS,IAAI;UAC1B;UACA,IAAIA,IAAI,CAAC4L,OAAO,KAAK,MAAM,IAAI5L,IAAI,CAAC6L,MAAM,CAAC3V,KAAK,KAAK,CAAC,EAAE;YACtD8J,IAAI,CAAC8L,IAAI,CAAC9G,MAAM,CAACO,SAAS,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;UAC1C;QACF,CAAC;QACDwG,WAAW,EAAG/L,IAAS,IAAI;UACzB;UACA,IAAIA,IAAI,CAAC4L,OAAO,KAAK,MAAM,IAAI5L,IAAI,CAAC6L,MAAM,CAAC3V,KAAK,KAAK,CAAC,EAAE;YACtD,MAAMkI,KAAK,GAAGoD,MAAM,CAACxB,IAAI,CAAC8L,IAAI,CAACE,GAAG,IAAI,EAAE,CAAC;YACzC,MAAMC,UAAU,GAAG7N,KAAK,CAACuK,WAAW,EAAE,KAAK,UAAU;YACrD,MAAMuD,EAAE,GAAGD,UAAU,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;YACrD,MAAM1G,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YAEjC;YACAzC,GAAG,CAACqJ,YAAY,CAACD,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC;YACrCpJ,GAAG,CAACsJ,IAAI,CAACpM,IAAI,CAAC8L,IAAI,CAACO,CAAC,GAAG,GAAG,EAAErM,IAAI,CAAC8L,IAAI,CAACpI,CAAC,GAAG,GAAG,EAAE1D,IAAI,CAAC8L,IAAI,CAACQ,KAAK,GAAG,CAAC,EAAEtM,IAAI,CAAC8L,IAAI,CAACS,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC;YAE9F;YACAzJ,GAAG,CAAC0J,YAAY,CAACjH,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1DzC,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;YAChCf,GAAG,CAACgB,WAAW,CAAC,CAAC,CAAC;YAClB,MAAM2I,SAAS,GAAG3J,GAAG,CAAC4J,YAAY,CAACtO,KAAK,CAAC;YACzC,MAAMuO,KAAK,GAAG3M,IAAI,CAAC8L,IAAI,CAACO,CAAC,GAAGrM,IAAI,CAAC8L,IAAI,CAACQ,KAAK,GAAG,CAAC,GAAGG,SAAS,GAAG,CAAC;YAC/D,MAAMG,KAAK,GAAG5M,IAAI,CAAC8L,IAAI,CAACpI,CAAC,GAAG1D,IAAI,CAAC8L,IAAI,CAACS,MAAM,GAAG,CAAC,GAAG,CAAC;YACpDzJ,GAAG,CAAC3E,IAAI,CAACC,KAAK,EAAEuO,KAAK,EAAEC,KAAK,CAAC;YAE7B5M,IAAI,CAAC8L,IAAI,CAAC3N,IAAI,GAAG,EAAE;UACrB;QACF,CAAC;QACDwH,KAAK,EAAE;OACR,CAAC;MAEF;MACA,MAAMkH,SAAS,GAAG/J,GAAG,CAACgK,gBAAgB,EAAE;MACxC,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI+C,SAAS,EAAE/C,CAAC,EAAE,EAAE;QACnChH,GAAG,CAACiK,OAAO,CAACjD,CAAC,CAAC;QACdhH,GAAG,CAACkD,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/BlD,GAAG,CAACmD,IAAI,CAAC3C,MAAM,CAACC,IAAI,EAAE0H,UAAU,GAAG,EAAE,EAAE/H,SAAS,GAAGI,MAAM,CAACE,KAAK,EAAEyH,UAAU,GAAG,EAAE,CAAC;QACjFnI,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCf,GAAG,CAACgB,WAAW,CAAC,CAAC,CAAC;QAClBhB,GAAG,CAAC0J,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/B1J,GAAG,CAAC3E,IAAI,CAAC,iBAAiB,IAAI2C,IAAI,EAAE,CAACkM,cAAc,EAAE,EAAE,EAAE1J,MAAM,CAACC,IAAI,EAAE0H,UAAU,GAAG,EAAE,CAAC;QACtFnI,GAAG,CAAC3E,IAAI,CAAC,QAAQ2L,CAAC,OAAO+C,SAAS,EAAE,EAAE3J,SAAS,GAAGI,MAAM,CAACE,KAAK,GAAG,EAAE,EAAEyH,UAAU,GAAG,EAAE,CAAC;MACvF;MAEA,MAAM/E,QAAQ,GAAG,UAAUnK,YAAY,GAAGA,YAAY,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI+E,IAAI,EAAE,CAACqF,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MAChHtD,GAAG,CAACuD,IAAI,CAACH,QAAQ,CAAC;IAEpB,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACrB,IAAI,CAAClD,wBAAwB,CAACwK,WAAW,CAAC,yCAAyC,EAAE,EAAE,CAAC;MAEhH;IACF;EACF;EAEO/I,YAAYA,CAAA;IACjB;IACA;IACA,OAAO,IAAI,CAACjB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACwP,MAAM,KAAK,CAAC;EACtD;EAEOnU,4BAA4BA,CAACoR,UAAe;IACjD;IACA;IACA;IACA,IAAI,IAAI,CAACxM,OAAO,EAAE;MAChB,OAAO,IAAI;IACb;IAEA;IACA,OAAOwM,UAAU,CAACgD,YAAY,KAAK,KAAK;EAC1C;EACAtY,MAAMA,CAACuY,QAAa;IACrB,MAAM9D,eAAe,GAKd;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IACD,IAAI,CAAC3M,YAAY,CAACoL,IAAI,CAACkF,QAAQ,EAAC9D,eAAe,CAAC;IAEhD;IACA,IAAI,CAAC5M,SAAS,CAAC2Q,UAAU,CAAC;MACxBtX,WAAW,EAAC,IAAI,CAACzB,MAAM,CAACyB,WAAW;MACnCF,eAAe,EAAC,IAAI,CAACvB,MAAM,CAACuB,eAAe;MAC3CC,aAAa,EAAC,IAAI,CAACxB,MAAM,CAACwB;MAC1B;KACD,CAAC;EAEJ;EAEAwG,SAASA,CAAA;IACP,IAAI,CAACQ,YAAY,CAACwQ,UAAU,EAAE;EAChC;EAGO7Q,mBAAmBA,CAAA;IACxB,IAAI,CAAChJ,SAAS,GAAG,IAAI;IACrB,MAAM6T,QAAQ,GAAG;MAChBlK,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACtBrH,WAAW,EAAE,IAAI,CAAC2G,SAAS,CAAC2B,KAAK,CAACtI,WAAW;MAC7CD,aAAa,EAAE,IAAI,CAAC4G,SAAS,CAAC2B,KAAK,CAACvI,aAAa;MACjDD,eAAe,EAAE,IAAI,CAAC6G,SAAS,CAAC2B,KAAK,CAACxI;KAEvC;IAED,IAAI,CAACsH,cAAc,CAACoQ,mBAAmB,CAACjG,QAAQ,CAAC,CAACzI,SAAS,CAAC;MAC1DgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACxD,SAAS,EAAE;QAChB,IAAI,CAAC7I,SAAS,GAAG,KAAK;QACtB,IAAIqM,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAI,CAAC7C,wBAAwB,CAACwK,WAAW,CAAC5H,GAAG,CAACE,YAAY,CAAC2H,OAAO,EAAE,EAAE,CAAC;UACvE;UACA,IAAI,CAACtI,kBAAkB,EAAE;QAC3B,CAAC,MAAM;UACL,IAAI,CAACnC,wBAAwB,CAAC0K,SAAS,CAAC9H,GAAG,CAACO,YAAY,IAAE,qCAAqC,EAAE,EAAE,CAAC;UACpG;QACF;QACA,IAAI,CAACtD,GAAG,CAACuD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAAC7O,SAAS,GAAG,KAAK;QACtB,IAAI,CAACyJ,wBAAwB,CAAC0K,SAAS,CAAC,qCAAqC,EAAE,EAAE,CAAC;QAClF;QACA5I,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAACvF,GAAG,CAACuD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;;qCA/sCW3D,mBAAmB,EAAA7K,EAAA,CAAA0b,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5b,EAAA,CAAA0b,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA7b,EAAA,CAAA0b,iBAAA,CAAAI,EAAA,CAAAC,QAAA,GAAA/b,EAAA,CAAA0b,iBAAA,CAAA1b,EAAA,CAAAgc,iBAAA,GAAAhc,EAAA,CAAA0b,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAlc,EAAA,CAAA0b,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAApc,EAAA,CAAA0b,iBAAA,CAAAW,EAAA,CAAAC,wBAAA,GAAAtc,EAAA,CAAA0b,iBAAA,CAAAa,EAAA,CAAAC,cAAA;EAAA;;UAAnB3R,mBAAmB;IAAA4R,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAtB,QAAA,WAAAuB,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrBhC9c,EAAA,CAAAoF,UAAA,IAAA4X,kCAAA,iBAA0D;QAS1Dhd,EAAA,CAAAC,cAAA,aAAmC;QAEjCD,EAAA,CAAAoF,UAAA,IAAA6X,kCAAA,mBAAqD;QA+dvDjd,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAoF,UAAA,IAAA8X,0CAAA,iCAAAld,EAAA,CAAAmd,sBAAA,CAAuD;;;QA7ejDnd,EAAA,CAAAc,UAAA,SAAAic,GAAA,CAAApb,SAAA,CAAe;QAWoB3B,EAAA,CAAAa,SAAA,GAAY;QAAZb,EAAA,CAAAc,UAAA,SAAAic,GAAA,CAAAva,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}