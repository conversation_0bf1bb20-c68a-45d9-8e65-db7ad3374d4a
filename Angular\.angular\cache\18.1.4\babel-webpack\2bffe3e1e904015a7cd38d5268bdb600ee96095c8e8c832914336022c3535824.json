{"ast": null, "code": "import { Subscription, combineLatest } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 24)(2, \"div\", 25)(3, \"h5\", 26);\n    i0.ɵɵtext(4, \"Project Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_ng_container_22_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(6, \"i\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 29)(8, \"div\", 30)(9, \"label\");\n    i0.ɵɵtext(10, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 31);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 32)(14, \"label\");\n    i0.ɵɵtext(15, \"Project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 31);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 32)(19, \"label\");\n    i0.ɵɵtext(20, \"External project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 31);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 30)(24, \"label\");\n    i0.ɵɵtext(25, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 31);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 32)(29, \"label\");\n    i0.ɵɵtext(30, \"Start date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 31);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 32)(35, \"label\");\n    i0.ɵɵtext(36, \"End date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 31);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectLocation || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.internalProjectManagerName || ctx_r1.project.internalProjectManager || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.externalPMNames || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectDescription || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStartDate ? i0.ɵɵpipeBind2(33, 6, ctx_r1.project.projectStartDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectEndDate ? i0.ɵɵpipeBind2(39, 9, ctx_r1.project.projectEndDate, \"MM/dd/yyyy\") : \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 42);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template_a_click_2_listener() {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r5.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"select\", 43);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template_select_change_8_listener($event) {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r5, $event.target.value));\n    });\n    i0.ɵɵelementStart(9, \"option\", 44);\n    i0.ɵɵtext(10, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"option\", 45);\n    i0.ɵɵtext(12, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 46);\n    i0.ɵɵtext(14, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 47);\n    i0.ɵɵtext(16, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 48);\n    i0.ɵɵtext(18, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 49);\n    i0.ɵɵtext(20, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 50);\n    i0.ɵɵtext(22, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 51);\n    i0.ɵɵtext(24, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"td\")(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"td\", 52)(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const permit_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitName || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.permitNumber || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r5.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(permit_r5.permitAppliedDate ? i0.ɵɵpipeBind2(28, 7, permit_r5.permitAppliedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(permit_r5.attentionReason || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_23_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"table\", 39)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Permit #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Submitted Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 40);\n    i0.ɵɵtext(13, \"Ball in Court\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template, 32, 10, \"tr\", 41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectViewComponent_div_2_ng_container_23_div_1_Template, 5, 0, \"div\", 33)(2, ProjectViewComponent_div_2_ng_container_23_div_2_Template, 16, 1, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nfunction ProjectViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12)(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵtext(12, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 18)(14, \"ul\", 19)(15, \"li\", 20)(16, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_16_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(17, \" Project details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 20)(19, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"permits\", $event));\n    });\n    i0.ɵɵtext(20, \" Permits list \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"div\", 22);\n    i0.ɵɵtemplate(22, ProjectViewComponent_div_2_ng_container_22_Template, 40, 12, \"ng-container\", 23)(23, ProjectViewComponent_div_2_ng_container_23_Template, 3, 2, \"ng-container\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"Project # \", ctx_r1.project.internalProjectNumber || \"\", \" - \", ctx_r1.project.projectName || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"Active\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx_r1.selectedTab === \"permits\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.project);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"permits\");\n  }\n}\nexport let ProjectViewComponent = /*#__PURE__*/(() => {\n  class ProjectViewComponent {\n    route;\n    router;\n    cdr;\n    appService;\n    projectsService;\n    permitsService;\n    modalService;\n    httpUtilService;\n    projectId = null;\n    project = null;\n    isLoading = false;\n    selectedTab = 'details';\n    projectPermits = [];\n    loginUser = {};\n    routeSubscription = new Subscription();\n    loadingSubscription = new Subscription();\n    constructor(route, router, cdr, appService, projectsService, permitsService, modalService, httpUtilService) {\n      this.route = route;\n      this.router = router;\n      this.cdr = cdr;\n      this.appService = appService;\n      this.projectsService = projectsService;\n      this.permitsService = permitsService;\n      this.modalService = modalService;\n      this.httpUtilService = httpUtilService;\n    }\n    ngOnInit() {\n      this.loginUser = this.appService.getLoggedInUser();\n      // Subscribe to global loading state\n      this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n        this.isLoading = loading === true;\n      });\n      // Combine route params and query params to handle both together\n      this.routeSubscription = combineLatest([this.route.paramMap, this.route.queryParams]).subscribe(([paramMap, queryParams]) => {\n        const idParam = paramMap.get('id');\n        this.projectId = idParam ? Number(idParam) : null;\n        console.log('Project view - received params:', {\n          projectId: this.projectId,\n          queryParams\n        });\n        // Handle active tab from query params\n        const activeTab = queryParams['activeTab'];\n        if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n          this.selectedTab = activeTab;\n          console.log('Setting selectedTab from query params:', activeTab);\n        } else {\n          console.log('No valid activeTab found, keeping default:', this.selectedTab);\n        }\n        if (this.projectId) {\n          this.fetchProjectDetails();\n          this.fetchProjectPermits();\n        }\n        this.cdr.markForCheck();\n      });\n    }\n    ngOnDestroy() {\n      if (this.routeSubscription) {\n        this.routeSubscription.unsubscribe();\n      }\n      if (this.loadingSubscription) {\n        this.loadingSubscription.unsubscribe();\n      }\n    }\n    fetchProjectDetails() {\n      if (!this.projectId) {\n        return;\n      }\n      this.httpUtilService.loadingSubject.next(true);\n      this.projectsService.getProject({\n        projectId: this.projectId\n      }).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.log('Project API Response:', res);\n          if (!res?.isFault) {\n            // Try different response structures\n            this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n            console.log('Project data assigned:', this.project);\n            console.log('Project fields available:', Object.keys(this.project || {}));\n            // Don't override selectedTab here - let query params handle it\n          } else {\n            console.error('API returned fault:', res.faultMessage);\n            this.project = null;\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('Error fetching project details:', err);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    fetchProjectPermits() {\n      if (!this.projectId) {\n        return;\n      }\n      this.httpUtilService.loadingSubject.next(true);\n      // Get permits for this specific project\n      this.permitsService.getPermitsForKendoGrid({\n        take: 100,\n        skip: 0,\n        sort: [],\n        filter: {\n          logic: 'and',\n          filters: [{\n            field: 'projectId',\n            operator: 'eq',\n            value: this.projectId\n          }]\n        },\n        search: '',\n        loggedInUserId: this.loginUser.userId\n      }).subscribe({\n        next: res => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.log('Project permits API response:', res);\n          if (res?.isFault) {\n            console.error('Failed to load project permits:', res.faultMessage);\n            this.projectPermits = [];\n          } else {\n            const rawPermits = res.responseData?.data || res.data || [];\n            // Client-side guard: ensure only permits for this project are shown\n            this.projectPermits = (rawPermits || []).filter(p => {\n              const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n              return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n            });\n            console.log('Project permits assigned (filtered):', this.projectPermits);\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('Error loading project permits:', err);\n          this.projectPermits = [];\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    goBack() {\n      this.router.navigate(['/projects/list']);\n    }\n    editProject() {\n      if (!this.projectId) {\n        return;\n      }\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Open the modal and load the ProjectPopup\n      const modalRef = this.modalService.open(ProjectPopupComponent, NgbModalOptions);\n      // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n      modalRef.componentInstance.id = this.projectId;\n      modalRef.componentInstance.project = this.project;\n      // Subscribe to the modal event when it closes\n      modalRef.result.then(result => {\n        // Handle successful edit\n        if (result) {\n          console.log('Project edited successfully:', result);\n          // Refresh project details and permits\n          this.fetchProjectDetails();\n          this.fetchProjectPermits();\n        }\n      }, reason => {\n        // Handle modal dismissal\n        console.log('Modal dismissed:', reason);\n      });\n    }\n    viewPermit(permitId) {\n      this.router.navigate(['/permits/view', permitId], {\n        queryParams: {\n          from: 'project',\n          projectId: this.projectId\n        }\n      });\n    }\n    onStatusChange(permit, newStatus) {\n      if (!permit?.permitId || !newStatus) {\n        return;\n      }\n      const allowed = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n      if (!allowed.includes(newStatus)) {\n        return;\n      }\n      const previous = permit.internalReviewStatus;\n      permit.internalReviewStatus = newStatus;\n      this.httpUtilService.loadingSubject.next(true);\n      this.cdr.markForCheck();\n      this.permitsService.updatePermitInternalReviewStatus({\n        permitId: permit.permitId,\n        internalReviewStatus: newStatus\n      }).subscribe({\n        next: res => {\n          const isFault = res?.isFault || res?.responseData?.isFault;\n          if (isFault) {\n            permit.internalReviewStatus = previous;\n          }\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.markForCheck();\n        },\n        error: () => {\n          permit.internalReviewStatus = previous;\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    getStatusClass(status) {\n      if (!status) return 'status-n-a';\n      return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    }\n    showTab(tab, $event) {\n      this.selectedTab = tab;\n      this.cdr.markForCheck();\n    }\n    static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.HttpUtilsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectViewComponent,\n      selectors: [[\"app-project-view\"]],\n      decls: 3,\n      vars: 2,\n      consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"project-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"project-details-header\"], [1, \"header-content\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"project-title\"], [1, \"status-text\", \"status-active\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"card-body\"], [4, \"ngIf\"], [1, \"project-details-content\"], [1, \"project-details-tab-header\"], [1, \"project-details-title\"], [\"type\", \"button\", \"title\", \"Edit Project\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-light-primary\", 3, \"click\"], [1, \"fas\", \"fa-pencil\"], [1, \"project-details-grid\"], [1, \"project-detail-item\", \"span-2\"], [1, \"project-value\"], [1, \"project-detail-item\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"ball-in-court-header\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"fw-bold\", 3, \"click\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"], [1, \"ball-in-court-cell\"]],\n      template: function ProjectViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵtemplate(2, ProjectViewComponent_div_2_Template, 24, 11, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.project);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i7.DatePipe],\n      styles: [\".project-view-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:0}.project-view-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{margin-bottom:0;padding-bottom:.25rem}.project-view-container[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding-top:.5rem}.project-details-header[_ngcontent-%COMP%]{padding:0 1.5rem;border-bottom:1px solid #e5eaee;background:transparent}.project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding-top:.5rem}.project-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#3f4254;font-weight:600;font-size:1.1rem}.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]{display:flex;gap:.5rem;align-items:center}.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%]{font-size:.875rem!important;padding:.375rem .75rem!important;line-height:1.5!important}.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], .project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.3rem;padding:.15rem .5rem;border-radius:.55rem;background-color:#f3f6f9;color:#3f4254;border:1px solid #e5eaee;box-shadow:0 2px 6px #0000000d;font-weight:600;font-size:.8rem;line-height:1;transition:background-color .2s ease,box-shadow .2s ease,transform .02s ease}.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#5e6e82;font-size:.75rem}.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, .project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover{background-color:#eef2f7;box-shadow:0 3px 10px #00000012;text-decoration:none}.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, .project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active{transform:translateY(1px);box-shadow:0 1px 4px #0000000f}.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}.project-details-content[_ngcontent-%COMP%]{padding:1rem 1.5rem}.project-details-tab-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem;padding-bottom:.75rem;border-bottom:1px solid #e5eaee}.project-details-tab-header[_ngcontent-%COMP%]   .project-details-title[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;font-weight:600;color:#3f4254}.project-details-tab-header[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;width:32px;height:32px;padding:0;border-radius:.25rem;background-color:transparent;color:#3699ff;border:2px solid #3699ff;transition:all .2s ease}.project-details-tab-header[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}.project-details-tab-header[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]:hover{background-color:#3699ff;color:#fff;transform:translateY(-1px);box-shadow:0 2px 8px #3699ff4d}.project-details-tab-header[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]:active{transform:translateY(0);box-shadow:0 1px 4px #3699ff33}.project-details-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(4,minmax(0,1fr));gap:1.5rem}.project-detail-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.4rem}.project-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;line-height:1.2;font-size:.875rem;font-weight:600;color:#323337;text-transform:capitalize;letter-spacing:.1rem;margin:0}.project-detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#3b3c3f}.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%], .project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%]{margin-top:.1rem}.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%]{font-size:1rem;color:#3f4254;font-weight:500;padding:.5rem 0;border-bottom:none}.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%]{display:block;vertical-align:top;padding:.5rem 0;font-size:1rem;font-weight:600;text-align:left;background:transparent;border:none;min-width:0;border-radius:0}.project-detail-item.span-2[_ngcontent-%COMP%]{grid-column:span 2}.status-active[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.status-inactive[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.status-completed[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0;color:#e65100;border:1px solid #ffcc02}.status-cancelled[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828;border:1px solid #ffcdd2}.status-n-a[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.table-responsive[_ngcontent-%COMP%]{overflow-x:auto;padding:0 .5rem;margin-top:0}.table[_ngcontent-%COMP%]{margin-bottom:0;overflow:hidden;table-layout:fixed;width:100%}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]{background-color:#f8f9fa}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:600;color:#3f4254;border-bottom:2px solid #e5eaee;padding:1rem .75rem}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{border-bottom:1px solid #e5eaee}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:.75rem;vertical-align:middle;white-space:nowrap}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-number-col[_ngcontent-%COMP%]{width:25%}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-description-col[_ngcontent-%COMP%]{width:40%}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-type-col[_ngcontent-%COMP%]{width:20%}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-status-col[_ngcontent-%COMP%]{width:15%}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-number-cell[_ngcontent-%COMP%]{width:25%;white-space:nowrap}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-description-cell[_ngcontent-%COMP%]{width:40%}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-type-cell[_ngcontent-%COMP%]{width:20%}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-status-cell[_ngcontent-%COMP%]{width:15%}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child, .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child{width:30%}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child, .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%]{cursor:pointer}.table[_ngcontent-%COMP%]   .ball-in-court-header[_ngcontent-%COMP%]{width:20%}.table[_ngcontent-%COMP%]   .ball-in-court-cell[_ngcontent-%COMP%]{width:20%;white-space:normal;word-wrap:break-word;word-break:break-word;max-width:200px}.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]{color:var(--bs-primary, #0d6efd);text-decoration:none;font-weight:700;cursor:pointer;transition:color .15s ease,-webkit-text-decoration .15s ease;transition:color .15s ease,text-decoration .15s ease;transition:color .15s ease,text-decoration .15s ease,-webkit-text-decoration .15s ease}.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:hover{color:#0b5ed7;text-decoration:underline}.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 2px #187de440;border-radius:.25rem}.permit-status-cell[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem;border-radius:.25rem;font-weight:600;border:1px solid transparent;text-transform:uppercase;letter-spacing:.05rem}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0;color:#e65100;border:1px solid #ffcc02}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%]{background-color:#e8eaf6;color:#3949ab;border:1px solid #c5cae9}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828;border:1px solid #ffcdd2}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1565c0;border:1px solid #bbdefb}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-complete[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved-w-conditions[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-re-submit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-unknown[_ngcontent-%COMP%], .permit-status-cell[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}.permit-description-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .permit-type-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .permit-status-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:inline-block;max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.75rem;padding:.375rem .75rem;border-radius:.375rem;font-weight:600;transition:all .2s ease}.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 8px #00000026}.title-wrap[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.title-line[_ngcontent-%COMP%]{display:flex;align-items:baseline;gap:.75rem}.project-title[_ngcontent-%COMP%]{font-size:1.05rem;font-weight:700;color:#181c32}.status-text[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;color:#3f4254;padding:.25rem .5rem;border-radius:.25rem;border:1px solid transparent;width:-moz-fit-content;width:fit-content}.project-number-line[_ngcontent-%COMP%]{font-size:.85rem;color:#6c7293;padding-bottom:.25rem}.fullscreen-loading-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#0006;display:flex;justify-content:center;align-items:center;z-index:9999}.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]{text-align:center;color:#3699ff;padding:2rem}@media (max-width: 768px){.project-details-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr));gap:1rem}.project-detail-item.span-2[_ngcontent-%COMP%]{grid-column:auto}.table-responsive[_ngcontent-%COMP%]{font-size:.875rem}.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.7rem;padding:.25rem .5rem}}@media (max-width: 576px){.project-details-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:.75rem}.project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;align-items:flex-start}.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]{width:100%;justify-content:flex-end}}\"]\n    });\n  }\n  return ProjectViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}