{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nexport class ConfirmationDialogComponent {\n  modal;\n  showClose = true;\n  description = '';\n  actionButtonText = '';\n  cancelButtonText = '';\n  title = '';\n  selectedTab;\n  passEntry = new EventEmitter();\n  constructor(modal) {\n    this.modal = modal;\n  }\n  ngOnInit() {}\n  onYesClick() {\n    this.passEntry.emit({\n      success: true,\n      selectedTab: this.selectedTab\n    });\n    // this.passEntry.emit(true)\n    this.modal.close();\n  }\n  onCancelClick() {\n    this.passEntry.emit({\n      success: false,\n      selectedTab: this.selectedTab\n    });\n    // this.passEntry.emit(false)\n    this.modal.close();\n  }\n  static ɵfac = function ConfirmationDialogComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfirmationDialogComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ConfirmationDialogComponent,\n    selectors: [[\"app-confirmation-dialog\"]],\n    inputs: {\n      showClose: \"showClose\",\n      description: \"description\",\n      actionButtonText: \"actionButtonText\",\n      cancelButtonText: \"cancelButtonText\",\n      title: \"title\",\n      selectedTab: \"selectedTab\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 20,\n    vars: 4,\n    consts: [[1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"example-modal-sizes-title-lg\", 1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [1, \"btn\", \"btn-icon\", \"btn-sm\", \"pl-08\", 3, \"click\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 2, \"margin-left\", \"50%\"], [1, \"modal-body\", \"medium-modal-body\", 2, \"min-height\", \"50px\"], [1, \"col-lg-12\", \"form-label\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-elevate\", \"btn-sm\", \"mr-2\", 3, \"click\"]],\n    template: function ConfirmationDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3)(4);\n        i0.ɵɵtext(5);\n        i0.ɵɵelementContainerEnd()();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 3)(7, \"a\", 4);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_a_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelement(8, \"i\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelementContainerStart(10);\n        i0.ɵɵelementStart(11, \"div\", 7);\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 8);\n        i0.ɵɵelementContainerStart(14);\n        i0.ɵɵelementStart(15, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_15_listener() {\n          return ctx.onCancelClick();\n        });\n        i0.ɵɵtext(16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtext(17, \"\\u00A0 \");\n        i0.ɵɵelementStart(18, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_18_listener() {\n          return ctx.onYesClick();\n        });\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.title);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\" \", ctx.description, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.cancelButtonText);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.actionButtonText, \"\");\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "ConfirmationDialogComponent", "modal", "showClose", "description", "actionButtonText", "cancelButtonText", "title", "selectedTab", "passEntry", "constructor", "ngOnInit", "onYesClick", "emit", "success", "close", "onCancelClick", "i0", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ConfirmationDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ConfirmationDialogComponent_Template_a_click_7_listener", "dismiss", "ɵɵelement", "ConfirmationDialogComponent_Template_button_click_15_listener", "ConfirmationDialogComponent_Template_button_click_18_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\shared\\confirmation-dialog\\confirmation-dialog.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\shared\\confirmation-dialog\\confirmation-dialog.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\n\n@Component({\n  selector: 'app-confirmation-dialog',\n  templateUrl: './confirmation-dialog.component.html',\n  styleUrls: ['./confirmation-dialog.component.scss']\n})\nexport class ConfirmationDialogComponent implements OnInit {\n\n  @Input() showClose:boolean = true;\n  @Input() description:string ='';\n  @Input() actionButtonText:string ='';\n  @Input() cancelButtonText:string='';\n  @Input() title:string = '';\n  @Input() selectedTab: string;\n\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\n  constructor(public modal: NgbActiveModal,\n  ) { }\n\n  ngOnInit(): void {\n  }\n\n  onYesClick(): void {\n    this.passEntry.emit({ success: true, selectedTab: this.selectedTab });\n    // this.passEntry.emit(true)\n    this.modal.close();\n\t}\n\n  onCancelClick(): void {\n    this.passEntry.emit({ success: false, selectedTab: this.selectedTab });\n    // this.passEntry.emit(false)\n    this.modal.close();\n\t}\n\n\n}\n", "<div class=\"modal-content\">\r\n  <div class=\"modal-header\">\r\n    <div class=\"modal-title h5 fs-3\" id=\"example-modal-sizes-title-lg\">\r\n      <ng-container>\r\n        <ng-container>{{title}}</ng-container>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n     <a class=\"btn btn-icon  btn-sm pl-08\" (click)=\"modal.dismiss()\">\r\n        <i class=\"fa-solid fs-2 fa-xmark text-white\" style=\"    margin-left: 50%;\"></i>\r\n    </a>\r\n    </div>\r\n  </div>\r\n  <div class=\"modal-body medium-modal-body\" style=\"min-height: 50px;\">\r\n    <ng-container>\r\n      <div class=\"col-lg-12 form-label\">\r\n        {{description}}\r\n      </div>\r\n    </ng-container>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <ng-container>\r\n       <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate \" (click)=\"onCancelClick()\">{{cancelButtonText}}</button>\r\n    </ng-container>&nbsp;\r\n    <button type=\"button\" class=\"btn btn-primary btn-elevate btn-sm mr-2\" (click)=\"onYesClick()\">\r\n      {{actionButtonText}}</button>\r\n\r\n\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;;;AAQ9E,OAAM,MAAOC,2BAA2B;EAUnBC,KAAA;EARVC,SAAS,GAAW,IAAI;EACxBC,WAAW,GAAS,EAAE;EACtBC,gBAAgB,GAAS,EAAE;EAC3BC,gBAAgB,GAAQ,EAAE;EAC1BC,KAAK,GAAU,EAAE;EACjBC,WAAW;EAEVC,SAAS,GAAsB,IAAIT,YAAY,EAAE;EAC3DU,YAAmBR,KAAqB;IAArB,KAAAA,KAAK,GAALA,KAAK;EACpB;EAEJS,QAAQA,CAAA,GACR;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACH,SAAS,CAACI,IAAI,CAAC;MAAEC,OAAO,EAAE,IAAI;MAAEN,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC;IACrE;IACA,IAAI,CAACN,KAAK,CAACa,KAAK,EAAE;EACrB;EAECC,aAAaA,CAAA;IACX,IAAI,CAACP,SAAS,CAACI,IAAI,CAAC;MAAEC,OAAO,EAAE,KAAK;MAAEN,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC;IACtE;IACA,IAAI,CAACN,KAAK,CAACa,KAAK,EAAE;EACrB;;qCA1BYd,2BAA2B,EAAAgB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;;UAA3BnB,2BAA2B;IAAAoB,SAAA;IAAAC,MAAA;MAAAnB,SAAA;MAAAC,WAAA;MAAAC,gBAAA;MAAAC,gBAAA;MAAAC,KAAA;MAAAC,WAAA;IAAA;IAAAe,OAAA;MAAAd,SAAA;IAAA;IAAAe,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCNpCZ,EAFJ,CAAAc,cAAA,aAA2B,aACC,aAC2C;QAE/Dd,EADF,CAAAe,uBAAA,GAAc,GACE;QAAAf,EAAA,CAAAgB,MAAA,GAAS;;QAE3BhB,EAAA,CAAAiB,YAAA,EAAM;QAELjB,EADD,CAAAc,cAAA,aAAyB,WACwC;QAA1Bd,EAAA,CAAAkB,UAAA,mBAAAC,wDAAA;UAAA,OAASN,GAAA,CAAA5B,KAAA,CAAAmC,OAAA,EAAe;QAAA,EAAC;QAC5DpB,EAAA,CAAAqB,SAAA,WAA+E;QAGrFrB,EAFE,CAAAiB,YAAA,EAAI,EACE,EACF;QACNjB,EAAA,CAAAc,cAAA,aAAoE;QAClEd,EAAA,CAAAe,uBAAA,IAAc;QACZf,EAAA,CAAAc,cAAA,cAAkC;QAChCd,EAAA,CAAAgB,MAAA,IACF;QAAAhB,EAAA,CAAAiB,YAAA,EAAM;;QAEVjB,EAAA,CAAAiB,YAAA,EAAM;QACNjB,EAAA,CAAAc,cAAA,cAA0B;QACxBd,EAAA,CAAAe,uBAAA,IAAc;QACXf,EAAA,CAAAc,cAAA,iBAA2F;QAA1Bd,EAAA,CAAAkB,UAAA,mBAAAI,8DAAA;UAAA,OAAST,GAAA,CAAAd,aAAA,EAAe;QAAA,EAAC;QAACC,EAAA,CAAAgB,MAAA,IAAoB;QAAAhB,EAAA,CAAAiB,YAAA,EAAS;;QAC5GjB,EAAA,CAAAgB,MAAA,eACf;QAAAhB,EAAA,CAAAc,cAAA,kBAA6F;QAAvBd,EAAA,CAAAkB,UAAA,mBAAAK,8DAAA;UAAA,OAASV,GAAA,CAAAlB,UAAA,EAAY;QAAA,EAAC;QAC1FK,EAAA,CAAAgB,MAAA,IAAoB;QAI1BhB,EAJ0B,CAAAiB,YAAA,EAAS,EAG3B,EACF;;;QAzBgBjB,EAAA,CAAAwB,SAAA,GAAS;QAATxB,EAAA,CAAAyB,iBAAA,CAAAZ,GAAA,CAAAvB,KAAA,CAAS;QAYvBU,EAAA,CAAAwB,SAAA,GACF;QADExB,EAAA,CAAA0B,kBAAA,MAAAb,GAAA,CAAA1B,WAAA,MACF;QAK4Fa,EAAA,CAAAwB,SAAA,GAAoB;QAApBxB,EAAA,CAAAyB,iBAAA,CAAAZ,GAAA,CAAAxB,gBAAA,CAAoB;QAGhHW,EAAA,CAAAwB,SAAA,GAAoB;QAApBxB,EAAA,CAAA0B,kBAAA,MAAAb,GAAA,CAAAzB,gBAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}