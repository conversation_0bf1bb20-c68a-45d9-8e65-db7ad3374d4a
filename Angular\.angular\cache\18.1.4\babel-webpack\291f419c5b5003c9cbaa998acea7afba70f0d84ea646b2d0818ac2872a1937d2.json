{"ast": null, "code": "import { AppSettings } from 'src/app/app.settings';\nimport { CurrencyPipe } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./app-encrypt-decrypt\";\nimport * as i2 from \"@angular/router\";\n// import { DatePipe } from '@angular/common';\n// import validator from 'validator';\nexport class AppService {\n  appEncryptDecryptService;\n  router;\n  constructor(appEncryptDecryptService, router) {\n    this.appEncryptDecryptService = appEncryptDecryptService;\n    this.router = router;\n  }\n  // get the local storage item with decrypted value\n  getLocalStorageItem(key, parsingNeeded) {\n    const itemVal = localStorage.getItem(key);\n    if (itemVal == null) {\n      return null;\n    }\n    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.SECRET_KEY, itemVal);\n    if (!parsingNeeded) {\n      return decryptedValue;\n    } else {\n      return JSON.parse(decryptedValue);\n    }\n  }\n  // set the local storage item with encrypted value\n  setLocalStorageItem(key, value, parsingNeeded) {\n    if (!parsingNeeded) {\n      return localStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, value));\n    } else {\n      return localStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, JSON.stringify(value)));\n    }\n  }\n  // get the session storage item with decrypted value\n  getSessionStorageItem(key, parsingNeeded) {\n    const itemVal = sessionStorage.getItem(key);\n    if (itemVal == null) {\n      return null;\n    }\n    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.SECRET_KEY, itemVal);\n    if (!parsingNeeded) {\n      return decryptedValue;\n    } else {\n      return JSON.parse(decryptedValue);\n    }\n  }\n  // set the local storage item with encrypted value\n  setSessionStorageItem(key, value, parsingNeeded) {\n    if (!parsingNeeded) {\n      return sessionStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, value));\n    } else {\n      return sessionStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, JSON.stringify(value)));\n    }\n  }\n  fromJsonDate(jDate) {\n    const bDate = new Date(jDate);\n    return bDate.toISOString().substring(0, 10); //Ignore time\n  }\n  logout() {\n    localStorage.removeItem('permitUser');\n    localStorage.removeItem('permitToken');\n    localStorage.removeItem('permit_access');\n    localStorage.removeItem('permit_exp');\n    const remember = this.getLocalStorageItem('permitRemember', false);\n    if (remember !== 'true') {\n      localStorage.removeItem('permitUserAuth');\n    }\n    this.router.navigate(['/auth/login']);\n  }\n  removeToken() {\n    localStorage.removeItem('permit_access');\n    localStorage.removeItem('permit_exp');\n  }\n  dollarAmount(data) {\n    const currencyPipe = new CurrencyPipe('en-US');\n    // Value to be formatted\n    const value = data;\n    // Format the currency\n    const formattedValue = currencyPipe.transform(value, 'USD', 'symbol', '1.2-2');\n    return formattedValue;\n  }\n  // get the time in sentence format\n  timeConvert(num) {\n    const hours = Math.floor(num / 60);\n    const minutes = num % 60;\n    switch (hours) {\n      case 0:\n        return minutes + 'min ago';\n      case 1:\n        return hours + 'hr' + minutes + 'min ago';\n      default:\n        return hours + 'hr' + minutes + 'min ago';\n    }\n  }\n  // get the decrypted password\n  getPasswordData(data) {\n    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.API_PASSWORD_SECRET_KEY, data);\n    return JSON.parse(decryptedValue);\n  }\n  // get the date from Unix time\n  unixDate(unixtime) {\n    if (unixtime) {\n      const u = new Date(unixtime);\n      const month = String(u.getMonth() + 1).padStart(2, '0');\n      const day = String(u.getDate()).padStart(2, '0');\n      const dates = `${month}/${day}/${u.getFullYear()}`;\n      return dates;\n    } else {\n      const dates = '';\n      return dates;\n    }\n  }\n  // get the date time from unix time\n  unixTime(unixtime) {\n    if (unixtime) {\n      const u = new Date(unixtime);\n      const amOrPm = u.getHours() < 12 ? 'AM' : 'PM';\n      const hour = u.getHours() < 12 ? u.getHours() : u.getHours() - 12;\n      const month = u.getMonth() + 1;\n      const minutes = u.getMinutes();\n      let min;\n      if (minutes < 10) {\n        min = '0' + minutes.toString();\n      } else {\n        min = minutes.toString();\n      }\n      // const dates = month + '/' + u.getDate() + '/' + u.getFullYear() + ' ' + hour + ':' + min + ' ' + amOrPm;\n      const dates = hour + ':' + min + ' ' + amOrPm;\n      return dates;\n    } else {\n      const dates = '';\n      return dates;\n    }\n  }\n  dateDiffInDays(a, b) {\n    const _MS_PER_DAY = 1000 * 60 * 60 * 24;\n    // Discard the time and time-zone information.\n    const utc1 = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());\n    const utc2 = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());\n    return Math.floor((utc2 - utc1) / _MS_PER_DAY);\n  }\n  // get the format for phone number with /without extension\n  getPhoneFormat(phoneNum) {\n    // Remove all non-digit characters\n    const digitsOnly = phoneNum.replace(/\\D/g, '');\n    const baseLength = 10;\n    if (digitsOnly.length < baseLength) {\n      return phoneNum.trim(); // Not enough digits to format\n    }\n    const areaCode = digitsOnly.substring(0, 3);\n    const mid = digitsOnly.substring(3, 6);\n    const lastFour = digitsOnly.substring(6, 10);\n    const extension = digitsOnly.length > baseLength ? digitsOnly.substring(10) : '';\n    let formatted = `(${areaCode}) ${mid}-${lastFour}`;\n    if (extension) {\n      formatted += ` Ext. ${extension}`;\n    }\n    return formatted.trim();\n  }\n  // get the format for email in href\n  mailto(emailAddress) {\n    return \"mailto:\" + emailAddress;\n  }\n  onImgError(event) {\n    event.target.src = './assets/media/avatars/blank.png';\n  }\n  ImageUrl(name) {\n    return AppSettings.IMAGEPATH + name;\n  }\n  formatDate(date) {\n    if (date === '' || date === null || date === undefined) {\n      return '';\n    }\n    const d = new Date(date);\n    const month = String(d.getMonth() + 1).padStart(2, '0');\n    const day = String(d.getDate()).padStart(2, '0');\n    const year = d.getFullYear();\n    return `${month}/${day}/${year}`;\n  }\n  // Static method for consistent date formatting across components\n  static formatDate(date) {\n    if (date === '' || date === null || date === undefined) {\n      return '';\n    }\n    const d = new Date(date);\n    const month = String(d.getMonth() + 1).padStart(2, '0');\n    const day = String(d.getDate()).padStart(2, '0');\n    const year = d.getFullYear();\n    return `${month}/${day}/${year}`;\n  }\n  getTodayDate() {\n    const today = new Date();\n    let date = today.getDate() > 9 ? today.getDate() : `0${today.getDate()}`;\n    let month = today.getMonth() > 9 ? today.getMonth() + 1 : `0${today.getMonth() + 1}`;\n    let year = today.getFullYear();\n    let todayDate = year + \"-\" + month + \"-\" + date;\n    return todayDate;\n  }\n  customSearchFn(term, item) {\n    term = term.toLocaleLowerCase();\n    return item.email.toLocaleLowerCase().indexOf(term) > -1 || item.UserFullName.toLocaleLowerCase().indexOf(term) > -1 || item.firstname.toLocaleLowerCase().indexOf(term) > -1;\n    // (item.code + \" - \" + item.name).toLocaleLowerCase().indexOf(term) > -1;\n  }\n  formatCurrency(amount) {\n    // Convert the input to a number, handling null, undefined, or empty string\n    const amountFormatted = parseFloat(amount) || 0;\n    // Use Intl.NumberFormat to format as USD currency\n    const formatter = new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"USD\"\n    });\n    return formatter.format(amountFormatted);\n  }\n  formatMonthDate(dateString) {\n    if (dateString) {\n      const u = new Date(dateString);\n      const month = String(u.getUTCMonth() + 1).padStart(2, '0'); // Ensure two digits for the month\n      const day = String(u.getUTCDate()).padStart(2, '0'); // Ensure two digits for the day\n      const year = u.getUTCFullYear();\n      return `${month}/${day}/${year}`;\n    } else {\n      return '';\n    }\n  }\n  formatCommissionPercent(compAgentPercent) {\n    if (!compAgentPercent || compAgentPercent === 0 || compAgentPercent === 0.00) {\n      return '0%';\n    }\n    return `${compAgentPercent}%`;\n  }\n  decodeHtmlEntities(input) {\n    const entities = {\n      '&amp;': '&',\n      '&lt;': '<',\n      '&gt;': '>',\n      '&quot;': '\"',\n      '&#39;': \"'\"\n    };\n    return input.replace(/&amp;|&lt;|&gt;|&quot;|&#39;/g, match => entities[match] || match);\n  }\n  // formatMonthDate(dateString: string): string | null {\n  //   return this.datePipe.transform(dateString, 'MM/dd/yyyy');\n  // }\n  getDateWithoutTimezone(date) {\n    const d = new Date(date);\n    const year = d.getFullYear();\n    const month = String(d.getMonth() + 1).padStart(2, '0'); // Add leading zero if needed\n    const day = String(d.getDate()).padStart(2, '0'); // Add leading zero if needed\n    return `${year}-${month}-${day}`;\n  }\n  // Utility to check if the field is a date field\n  isDateField(columns, field) {\n    const dateColumnNames = columns; // List of date fields\n    return dateColumnNames.includes(field);\n  }\n  convertDateFilters(filters, dateFields) {\n    return filters.map(group => {\n      const updatedFilters = group.filters.map(f => {\n        if (dateFields.includes(f.field) && typeof f.value === 'string') {\n          return {\n            ...f,\n            value: f.value ? new Date(f.value) : null\n          };\n        }\n        return f;\n      });\n      return {\n        ...group,\n        filters: updatedFilters\n      };\n    });\n  }\n  /**\n   * Converts file size from bytes to kilobytes (KB).\n   *\n   * - Takes the file size in bytes as input.\n   * - Divides by 1024 to convert it to KB.\n   * - Rounds the result to the nearest whole number.\n   *\n   * @param size - The file size in bytes.\n   * @returns The file size in kilobytes (rounded).\n   */\n  getFileSize(size) {\n    return Math.round(size / 1024);\n  }\n  updateColumnConfig(hiddenData, columnReorderData, columnJSONFormat) {\n    // Create a deep copy of the original array to avoid modifying the input directly\n    const updatedColumns = columnJSONFormat;\n    for (const column of updatedColumns) {\n      column.hidden = false;\n    }\n    // Step 1: Update hidden property based on hiddenData\n    for (const hiddenItem of hiddenData) {\n      const fieldToUpdate = updatedColumns.find(item => item.field === hiddenItem.field);\n      if (fieldToUpdate) {\n        fieldToUpdate.hidden = hiddenItem.hidden;\n      }\n    }\n    // Step 2: Create a map of fields to their new order\n    const orderMap = {};\n    columnReorderData.forEach(item => {\n      orderMap[item.field] = item.orderIndex;\n    });\n    // Step 3: Update order based on columnReorderData\n    for (let column of updatedColumns) {\n      if (orderMap.hasOwnProperty(column.field)) {\n        column.order = orderMap[column.field];\n      }\n    }\n    // Step 4: Sort the array by order\n    updatedColumns.sort((a, b) => a.order - b.order);\n    return updatedColumns;\n  }\n  // function to check whether the form has any error\n  controlHasError(validation, controlName, name) {\n    const control = name.controls[controlName];\n    if (!control) {\n      return false;\n    }\n    let result = control.hasError(validation) && (control.dirty || control.touched);\n    return result;\n  }\n  getLoggedInUser() {\n    let loggedInUser = this.getLocalStorageItem('permitUser', true);\n    return loggedInUser;\n  }\n  // Derive initials from various possible user shapes\n  getUserInitials(user) {\n    if (!user) {\n      return '?';\n    }\n    const tryString = val => (val || '').toString().trim();\n    // Prefer explicit first/last\n    const first = tryString(user.firstname || user.firstName || user.FirstName);\n    const last = tryString(user.lastname || user.lastName || user.LastName);\n    if (first || last) {\n      const fi = first ? first.charAt(0).toUpperCase() : '';\n      const li = last ? last.charAt(0).toUpperCase() : '';\n      return fi + li || '?';\n    }\n    // Try full name fields\n    const full = tryString(user.UserFullName || user.userFullName || user.fullName || user.FullName || user.name);\n    if (full) {\n      const parts = full.split(/\\s+/).filter(Boolean);\n      if (parts.length === 1) {\n        return parts[0].charAt(0).toUpperCase();\n      }\n      return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();\n    }\n    // Fallback to email/username\n    const email = tryString(user.email || user.Email);\n    const username = tryString(user.userName || user.username || user.UserName);\n    const source = email || username;\n    if (source) {\n      // take letters from before @ or start of username\n      const base = email ? source.split('@')[0] : source;\n      const letters = base.replace(/[^A-Za-z]/g, '');\n      if (letters.length >= 2) {\n        return (letters[0] + letters[1]).toUpperCase();\n      }\n      if (letters.length === 1) {\n        return letters[0].toUpperCase();\n      }\n    }\n    return '?';\n  }\n  static ɵfac = function AppService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppService)(i0.ɵɵinject(i1.AppEncryptDecryptService), i0.ɵɵinject(i2.Router));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AppService,\n    factory: AppService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["AppSettings", "C<PERSON><PERSON>cyPipe", "AppService", "appEncryptDecryptService", "router", "constructor", "getLocalStorageItem", "key", "parsingNeeded", "itemVal", "localStorage", "getItem", "decryptedValue", "get", "SECRET_KEY", "JSON", "parse", "setLocalStorageItem", "value", "setItem", "set", "stringify", "getSessionStorageItem", "sessionStorage", "setSessionStorageItem", "fromJsonDate", "jDate", "bDate", "Date", "toISOString", "substring", "logout", "removeItem", "remember", "navigate", "removeToken", "dollarAmount", "data", "currencyPipe", "formattedValue", "transform", "timeConvert", "num", "hours", "Math", "floor", "minutes", "getPasswordData", "API_PASSWORD_SECRET_KEY", "unixDate", "unixtime", "u", "month", "String", "getMonth", "padStart", "day", "getDate", "dates", "getFullYear", "unixTime", "amOrPm", "getHours", "hour", "getMinutes", "min", "toString", "dateDiffInDays", "a", "b", "_MS_PER_DAY", "utc1", "UTC", "utc2", "getPhoneFormat", "phoneNum", "digitsOnly", "replace", "baseLength", "length", "trim", "areaCode", "mid", "lastFour", "extension", "formatted", "mailto", "emailAddress", "onImgError", "event", "target", "src", "ImageUrl", "name", "IMAGEPATH", "formatDate", "date", "undefined", "d", "year", "getTodayDate", "today", "todayDate", "customSearchFn", "term", "item", "toLocaleLowerCase", "email", "indexOf", "UserFullName", "firstname", "formatCurrency", "amount", "amountFormatted", "parseFloat", "formatter", "Intl", "NumberFormat", "style", "currency", "format", "formatMonthDate", "dateString", "getUTCMonth", "getUTCDate", "getUTCFullYear", "formatCommissionPercent", "compAgentPercent", "decodeHtmlEntities", "input", "entities", "match", "getDateWithoutTimezone", "isDateField", "columns", "field", "dateColumnNames", "includes", "convertDateFilters", "filters", "dateFields", "map", "group", "updatedFilters", "f", "getFileSize", "size", "round", "updateColumnConfig", "hiddenData", "columnReorderData", "columnJSONFormat", "updatedColumns", "column", "hidden", "hiddenItem", "fieldToUpdate", "find", "orderMap", "for<PERSON>ach", "orderIndex", "hasOwnProperty", "order", "sort", "controlHasError", "validation", "controlName", "control", "controls", "result", "<PERSON><PERSON><PERSON><PERSON>", "dirty", "touched", "getLoggedInUser", "loggedInUser", "getUserInitials", "user", "tryString", "val", "first", "firstName", "FirstName", "last", "lastname", "lastName", "LastName", "fi", "char<PERSON>t", "toUpperCase", "li", "full", "userFullName", "fullName", "FullName", "parts", "split", "filter", "Boolean", "Email", "username", "userName", "UserName", "source", "base", "letters", "i0", "ɵɵinject", "i1", "AppEncryptDecryptService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\services\\app.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport * as _ from 'lodash';\r\nimport { AppSettings } from 'src/app/app.settings';\r\n\r\nimport { Router } from '@angular/router';\r\nimport { CurrencyPipe } from '@angular/common';\r\nimport { AppEncryptDecryptService } from './app-encrypt-decrypt';\r\nimport { FormGroup } from '@angular/forms';\r\n// import { DatePipe } from '@angular/common';\r\n// import validator from 'validator';\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AppService {\r\n  constructor(\r\n    private appEncryptDecryptService: AppEncryptDecryptService,\r\n    private router: Router,\r\n    // private datePipe: DatePipe\r\n  ) {\r\n  }\r\n  // get the local storage item with decrypted value\r\n  public getLocalStorageItem(key: string, parsingNeeded: boolean) {\r\n    const itemVal = localStorage.getItem(key);\r\n    if (itemVal == null) {\r\n      return null;\r\n    }\r\n    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.SECRET_KEY, itemVal);\r\n    if (!parsingNeeded) {\r\n      return decryptedValue;\r\n    } else {\r\n      return JSON.parse(decryptedValue);\r\n    }\r\n  }\r\n  // set the local storage item with encrypted value\r\n  public setLocalStorageItem(key: string, value: any, parsingNeeded: boolean) {\r\n    if (!parsingNeeded) {\r\n      return localStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, value));\r\n    } else {\r\n      return localStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, JSON.stringify(value)));\r\n    }\r\n  }\r\n\r\n  // get the session storage item with decrypted value\r\n  public getSessionStorageItem(key: string, parsingNeeded: boolean) {\r\n    const itemVal = sessionStorage.getItem(key);\r\n    if (itemVal == null) {\r\n      return null;\r\n    }\r\n    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.SECRET_KEY, itemVal);\r\n    if (!parsingNeeded) {\r\n      return decryptedValue;\r\n    } else {\r\n      return JSON.parse(decryptedValue);\r\n    }\r\n  }\r\n  // set the local storage item with encrypted value\r\n  public setSessionStorageItem(key: string, value: any, parsingNeeded: boolean) {\r\n    if (!parsingNeeded) {\r\n      return sessionStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, value));\r\n    } else {\r\n      return sessionStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, JSON.stringify(value)));\r\n    }\r\n  }\r\n\r\n  public fromJsonDate(jDate: any): string {\r\n    const bDate: Date = new Date(jDate);\r\n    return bDate.toISOString().substring(0, 10);  //Ignore time\r\n  }\r\n\r\n  logout() {\r\n    localStorage.removeItem('permitUser');\r\n    localStorage.removeItem('permitToken');\r\n    localStorage.removeItem('permit_access');\r\n    localStorage.removeItem('permit_exp');\r\n    const remember = this.getLocalStorageItem('permitRemember', false);\r\n    if (remember !== 'true') {\r\n      localStorage.removeItem('permitUserAuth');\r\n    }\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  removeToken() {\r\n    localStorage.removeItem('permit_access');\r\n    localStorage.removeItem('permit_exp');\r\n  }\r\n  dollarAmount(data: any) {\r\n    const currencyPipe = new CurrencyPipe('en-US');\r\n    // Value to be formatted\r\n    const value = data;\r\n    // Format the currency\r\n    const formattedValue = currencyPipe.transform(value, 'USD', 'symbol', '1.2-2');\r\n    return formattedValue;\r\n  }\r\n\r\n\r\n\r\n\r\n  // get the time in sentence format\r\n  timeConvert(num: number) {\r\n    const hours = Math.floor(num / 60);\r\n    const minutes = num % 60;\r\n    switch (hours) {\r\n      case 0:\r\n        return minutes + 'min ago';\r\n      case 1:\r\n        return hours + 'hr' + minutes + 'min ago';\r\n      default:\r\n        return hours + 'hr' + minutes + 'min ago';\r\n    }\r\n  };\r\n\r\n  // get the decrypted password\r\n  getPasswordData(data: any) {\r\n    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.API_PASSWORD_SECRET_KEY, data);\r\n    return JSON.parse(decryptedValue);\r\n  }\r\n\r\n  // get the date from Unix time\r\n  unixDate(unixtime: any) {\r\n    if (unixtime) {\r\n      const u = new Date(unixtime);\r\n      const month = String(u.getMonth() + 1).padStart(2, '0');\r\n      const day = String(u.getDate()).padStart(2, '0');\r\n      const dates = `${month}/${day}/${u.getFullYear()}`;\r\n      return dates;\r\n    } else {\r\n      const dates = '';\r\n      return dates;\r\n    }\r\n  };\r\n\r\n  // get the date time from unix time\r\n  public unixTime(unixtime: any) {\r\n    if (unixtime) {\r\n      const u = new Date(unixtime);\r\n      const amOrPm = (u.getHours() < 12) ? 'AM' : 'PM';\r\n      const hour = (u.getHours() < 12) ? u.getHours() : u.getHours() - 12;\r\n      const month = u.getMonth() + 1;\r\n      const minutes: number = u.getMinutes();\r\n      let min: string;\r\n      if (minutes < 10) {\r\n        min = '0' + minutes.toString();\r\n      } else {\r\n        min = minutes.toString();\r\n      }\r\n      // const dates = month + '/' + u.getDate() + '/' + u.getFullYear() + ' ' + hour + ':' + min + ' ' + amOrPm;\r\n      const dates = hour + ':' + min + ' ' + amOrPm;\r\n      return dates;\r\n    } else {\r\n      const dates = '';\r\n      return dates;\r\n    }\r\n  }\r\n\r\n  public dateDiffInDays(a: any, b: any) {\r\n    const _MS_PER_DAY = 1000 * 60 * 60 * 24;\r\n    // Discard the time and time-zone information.\r\n    const utc1 = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());\r\n    const utc2 = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());\r\n\r\n    return Math.floor((utc2 - utc1) / _MS_PER_DAY);\r\n  }\r\n\r\n\r\n  // get the format for phone number with /without extension\r\n  getPhoneFormat(phoneNum: string): string {\r\n  // Remove all non-digit characters\r\n  const digitsOnly = phoneNum.replace(/\\D/g, '');\r\n\r\n  const baseLength = 10;\r\n  if (digitsOnly.length < baseLength) {\r\n    return phoneNum.trim(); // Not enough digits to format\r\n  }\r\n\r\n  const areaCode = digitsOnly.substring(0, 3);\r\n  const mid = digitsOnly.substring(3, 6);\r\n  const lastFour = digitsOnly.substring(6, 10);\r\n  const extension = digitsOnly.length > baseLength ? digitsOnly.substring(10) : '';\r\n\r\n  let formatted = `(${areaCode}) ${mid}-${lastFour}`;\r\n  if (extension) {\r\n    formatted += ` Ext. ${extension}`;\r\n  }\r\n\r\n  return formatted.trim();\r\n}\r\n\r\n  // get the format for email in href\r\n  mailto(emailAddress: string) {\r\n    return \"mailto:\" + emailAddress\r\n  }\r\n\r\n  onImgError(event: any) {\r\n    event.target.src = './assets/media/avatars/blank.png';\r\n  }\r\n\r\n  public ImageUrl(name: any) {\r\n    return AppSettings.IMAGEPATH + name;\r\n  }\r\n\r\n  formatDate(date: any): string {\r\n    if (date === '' || date === null || date === undefined) {\r\n      return '';\r\n    }\r\n    const d = new Date(date);\r\n    const month = String(d.getMonth() + 1).padStart(2, '0');\r\n    const day = String(d.getDate()).padStart(2, '0');\r\n    const year = d.getFullYear();\r\n    return `${month}/${day}/${year}`;\r\n  }\r\n\r\n  // Static method for consistent date formatting across components\r\n  static formatDate(date: any): string {\r\n    if (date === '' || date === null || date === undefined) {\r\n      return '';\r\n    }\r\n    const d = new Date(date);\r\n    const month = String(d.getMonth() + 1).padStart(2, '0');\r\n    const day = String(d.getDate()).padStart(2, '0');\r\n    const year = d.getFullYear();\r\n    return `${month}/${day}/${year}`;\r\n  }\r\n\r\n\r\n  getTodayDate() {\r\n    const today = new Date();\r\n    let date = today.getDate() > 9 ? today.getDate() :\r\n      `0${today.getDate()}`;\r\n    let month = today.getMonth() > 9 ? today.getMonth() + 1 :\r\n      `0${today.getMonth() + 1}`;\r\n    let year = today.getFullYear();\r\n    let todayDate = year + \"-\" + month + \"-\" + date;\r\n    return todayDate;\r\n  }\r\n\r\n  customSearchFn(term: string, item: any) {\r\n    term = term.toLocaleLowerCase();\r\n    return item.email.toLocaleLowerCase().indexOf(term) > -1 ||\r\n    item.UserFullName.toLocaleLowerCase().indexOf(term) > -1 ||\r\n    item.firstname.toLocaleLowerCase().indexOf(term) > -1 ;\r\n    // (item.code + \" - \" + item.name).toLocaleLowerCase().indexOf(term) > -1;\r\n  }\r\n\r\n  formatCurrency(amount: any): string {\r\n    // Convert the input to a number, handling null, undefined, or empty string\r\n    const amountFormatted = parseFloat(amount as string) || 0;\r\n\r\n    // Use Intl.NumberFormat to format as USD currency\r\n    const formatter = new Intl.NumberFormat(\"en-US\", {\r\n      style: \"currency\",\r\n      currency: \"USD\",\r\n    });\r\n\r\n    return formatter.format(amountFormatted);\r\n  }\r\n\r\n  formatMonthDate(dateString: any): any {\r\n    if (dateString) {\r\n      const u = new Date(dateString);\r\n      const month = String(u.getUTCMonth() + 1).padStart(2, '0'); // Ensure two digits for the month\r\n    const day = String(u.getUTCDate()).padStart(2, '0'); // Ensure two digits for the day\r\n    const year = u.getUTCFullYear();\r\n      return `${month}/${day}/${year}`;\r\n    } else {\r\n      return '';\r\n    }\r\n  }\r\n\r\n  formatCommissionPercent(compAgentPercent: any): string {\r\n    if (!compAgentPercent || compAgentPercent === 0 || compAgentPercent === 0.00) {\r\n      return '0%';\r\n    }\r\n    return `${compAgentPercent}%`;\r\n  }\r\n\r\n\r\n\r\n  decodeHtmlEntities(input: string): string {\r\n    const entities: { [key: string]: string } = {\r\n      '&amp;': '&',\r\n      '&lt;': '<',\r\n      '&gt;': '>',\r\n      '&quot;': '\"',\r\n      '&#39;': \"'\",\r\n    };\r\n\r\n    return input.replace(/&amp;|&lt;|&gt;|&quot;|&#39;/g, (match) => entities[match] || match);\r\n  }\r\n  // formatMonthDate(dateString: string): string | null {\r\n  //   return this.datePipe.transform(dateString, 'MM/dd/yyyy');\r\n  // }\r\n\r\n  getDateWithoutTimezone(date: Date | string): string {\r\n    const d = new Date(date);\r\n    const year = d.getFullYear();\r\n    const month = String(d.getMonth() + 1).padStart(2, '0'); // Add leading zero if needed\r\n    const day = String(d.getDate()).padStart(2, '0'); // Add leading zero if needed\r\n    return `${year}-${month}-${day}`;\r\n}\r\n// Utility to check if the field is a date field\r\nisDateField(columns:any,field: string): boolean {\r\n  const dateColumnNames =columns; // List of date fields\r\n  return dateColumnNames.includes(field);\r\n}\r\nconvertDateFilters(filters: any, dateFields: string[]) {\r\n  return filters.map((group: any) => {\r\n    const updatedFilters = group.filters.map((f: any) => {\r\n      if (dateFields.includes(f.field) && typeof f.value === 'string') {\r\n        return {\r\n          ...f,\r\n          value: f.value ? new Date(f.value) : null\r\n        };\r\n      }\r\n      return f;\r\n    });\r\n\r\n    return {\r\n      ...group,\r\n      filters: updatedFilters\r\n    };\r\n  });\r\n}\r\n\r\n\r\n/**\r\n * Converts file size from bytes to kilobytes (KB).\r\n *\r\n * - Takes the file size in bytes as input.\r\n * - Divides by 1024 to convert it to KB.\r\n * - Rounds the result to the nearest whole number.\r\n *\r\n * @param size - The file size in bytes.\r\n * @returns The file size in kilobytes (rounded).\r\n */\r\ngetFileSize(size: number) {\r\n  return Math.round(size / 1024);\r\n}\r\n\r\nupdateColumnConfig(hiddenData:any, columnReorderData:any, columnJSONFormat:any) {\r\n    // Create a deep copy of the original array to avoid modifying the input directly\r\n    const updatedColumns = columnJSONFormat;\r\n    for (const column of updatedColumns) {\r\n      column.hidden = false;\r\n    }\r\n    // Step 1: Update hidden property based on hiddenData\r\n    for (const hiddenItem of hiddenData) {\r\n      const fieldToUpdate = updatedColumns.find((item:any) => item.field === hiddenItem.field);\r\n      if (fieldToUpdate) {\r\n        fieldToUpdate.hidden = hiddenItem.hidden;\r\n      }\r\n    }\r\n    // Step 2: Create a map of fields to their new order\r\n    const orderMap :any= {};\r\n    columnReorderData.forEach((item:any) => {\r\n      orderMap[item.field] = item.orderIndex;\r\n    });\r\n    // Step 3: Update order based on columnReorderData\r\n    for (let column of updatedColumns) {\r\n      if (orderMap.hasOwnProperty(column.field)) {\r\n        column.order = orderMap[column.field];\r\n      }\r\n    }\r\n    // Step 4: Sort the array by order\r\n    updatedColumns.sort((a:any, b:any) => a.order - b.order);\r\n    return updatedColumns;\r\n  }\r\n\r\n   // function to check whether the form has any error\r\n  controlHasError(validation: any, controlName: string | number, name: FormGroup): boolean {\r\n    const control = name.controls[controlName];\r\n    if (!control) {\r\n      return false;\r\n    }\r\n    let result = control.hasError(validation) && (control.dirty || control.touched);\r\n    return result;\r\n  }\r\n\r\n  getLoggedInUser(){\r\n    let loggedInUser = this.getLocalStorageItem('permitUser', true);\r\n    return loggedInUser;\r\n  }\r\n\r\n  // Derive initials from various possible user shapes\r\n  getUserInitials(user: any): string {\r\n    if (!user) { return '?'; }\r\n\r\n    const tryString = (val: any) => (val || '').toString().trim();\r\n\r\n    // Prefer explicit first/last\r\n    const first = tryString(user.firstname || user.firstName || user.FirstName);\r\n    const last = tryString(user.lastname || user.lastName || user.LastName);\r\n    if (first || last) {\r\n      const fi = first ? first.charAt(0).toUpperCase() : '';\r\n      const li = last ? last.charAt(0).toUpperCase() : '';\r\n      return (fi + li) || '?';\r\n    }\r\n\r\n    // Try full name fields\r\n    const full = tryString(user.UserFullName || user.userFullName || user.fullName || user.FullName || user.name);\r\n    if (full) {\r\n      const parts = full.split(/\\s+/).filter(Boolean);\r\n      if (parts.length === 1) {\r\n        return parts[0].charAt(0).toUpperCase();\r\n      }\r\n      return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();\r\n    }\r\n\r\n    // Fallback to email/username\r\n    const email = tryString(user.email || user.Email);\r\n    const username = tryString(user.userName || user.username || user.UserName);\r\n    const source = email || username;\r\n    if (source) {\r\n      // take letters from before @ or start of username\r\n      const base = email ? source.split('@')[0] : source;\r\n      const letters = base.replace(/[^A-Za-z]/g, '');\r\n      if (letters.length >= 2) {\r\n        return (letters[0] + letters[1]).toUpperCase();\r\n      }\r\n      if (letters.length === 1) {\r\n        return letters[0].toUpperCase();\r\n      }\r\n    }\r\n\r\n    return '?';\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,sBAAsB;AAGlD,SAASC,YAAY,QAAQ,iBAAiB;;;;AAG9C;AACA;AAIA,OAAM,MAAOC,UAAU;EAEXC,wBAAA;EACAC,MAAA;EAFVC,YACUF,wBAAkD,EAClDC,MAAc;IADd,KAAAD,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,MAAM,GAANA,MAAM;EAGhB;EACA;EACOE,mBAAmBA,CAACC,GAAW,EAAEC,aAAsB;IAC5D,MAAMC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAACJ,GAAG,CAAC;IACzC,IAAIE,OAAO,IAAI,IAAI,EAAE;MACnB,OAAO,IAAI;IACb;IACA,MAAMG,cAAc,GAAG,IAAI,CAACT,wBAAwB,CAACU,GAAG,CAACb,WAAW,CAACc,UAAU,EAAEL,OAAO,CAAC;IACzF,IAAI,CAACD,aAAa,EAAE;MAClB,OAAOI,cAAc;IACvB,CAAC,MAAM;MACL,OAAOG,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC;IACnC;EACF;EACA;EACOK,mBAAmBA,CAACV,GAAW,EAAEW,KAAU,EAAEV,aAAsB;IACxE,IAAI,CAACA,aAAa,EAAE;MAClB,OAAOE,YAAY,CAACS,OAAO,CAACZ,GAAG,EAAE,IAAI,CAACJ,wBAAwB,CAACiB,GAAG,CAACpB,WAAW,CAACc,UAAU,EAAEI,KAAK,CAAC,CAAC;IACpG,CAAC,MAAM;MACL,OAAOR,YAAY,CAACS,OAAO,CAACZ,GAAG,EAAE,IAAI,CAACJ,wBAAwB,CAACiB,GAAG,CAACpB,WAAW,CAACc,UAAU,EAAEC,IAAI,CAACM,SAAS,CAACH,KAAK,CAAC,CAAC,CAAC;IACpH;EACF;EAEA;EACOI,qBAAqBA,CAACf,GAAW,EAAEC,aAAsB;IAC9D,MAAMC,OAAO,GAAGc,cAAc,CAACZ,OAAO,CAACJ,GAAG,CAAC;IAC3C,IAAIE,OAAO,IAAI,IAAI,EAAE;MACnB,OAAO,IAAI;IACb;IACA,MAAMG,cAAc,GAAG,IAAI,CAACT,wBAAwB,CAACU,GAAG,CAACb,WAAW,CAACc,UAAU,EAAEL,OAAO,CAAC;IACzF,IAAI,CAACD,aAAa,EAAE;MAClB,OAAOI,cAAc;IACvB,CAAC,MAAM;MACL,OAAOG,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC;IACnC;EACF;EACA;EACOY,qBAAqBA,CAACjB,GAAW,EAAEW,KAAU,EAAEV,aAAsB;IAC1E,IAAI,CAACA,aAAa,EAAE;MAClB,OAAOe,cAAc,CAACJ,OAAO,CAACZ,GAAG,EAAE,IAAI,CAACJ,wBAAwB,CAACiB,GAAG,CAACpB,WAAW,CAACc,UAAU,EAAEI,KAAK,CAAC,CAAC;IACtG,CAAC,MAAM;MACL,OAAOK,cAAc,CAACJ,OAAO,CAACZ,GAAG,EAAE,IAAI,CAACJ,wBAAwB,CAACiB,GAAG,CAACpB,WAAW,CAACc,UAAU,EAAEC,IAAI,CAACM,SAAS,CAACH,KAAK,CAAC,CAAC,CAAC;IACtH;EACF;EAEOO,YAAYA,CAACC,KAAU;IAC5B,MAAMC,KAAK,GAAS,IAAIC,IAAI,CAACF,KAAK,CAAC;IACnC,OAAOC,KAAK,CAACE,WAAW,EAAE,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAE;EAChD;EAEAC,MAAMA,CAAA;IACJrB,YAAY,CAACsB,UAAU,CAAC,YAAY,CAAC;IACrCtB,YAAY,CAACsB,UAAU,CAAC,aAAa,CAAC;IACtCtB,YAAY,CAACsB,UAAU,CAAC,eAAe,CAAC;IACxCtB,YAAY,CAACsB,UAAU,CAAC,YAAY,CAAC;IACrC,MAAMC,QAAQ,GAAG,IAAI,CAAC3B,mBAAmB,CAAC,gBAAgB,EAAE,KAAK,CAAC;IAClE,IAAI2B,QAAQ,KAAK,MAAM,EAAE;MACvBvB,YAAY,CAACsB,UAAU,CAAC,gBAAgB,CAAC;IAC3C;IACA,IAAI,CAAC5B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAC,WAAWA,CAAA;IACTzB,YAAY,CAACsB,UAAU,CAAC,eAAe,CAAC;IACxCtB,YAAY,CAACsB,UAAU,CAAC,YAAY,CAAC;EACvC;EACAI,YAAYA,CAACC,IAAS;IACpB,MAAMC,YAAY,GAAG,IAAIrC,YAAY,CAAC,OAAO,CAAC;IAC9C;IACA,MAAMiB,KAAK,GAAGmB,IAAI;IAClB;IACA,MAAME,cAAc,GAAGD,YAAY,CAACE,SAAS,CAACtB,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;IAC9E,OAAOqB,cAAc;EACvB;EAKA;EACAE,WAAWA,CAACC,GAAW;IACrB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,GAAG,EAAE,CAAC;IAClC,MAAMI,OAAO,GAAGJ,GAAG,GAAG,EAAE;IACxB,QAAQC,KAAK;MACX,KAAK,CAAC;QACJ,OAAOG,OAAO,GAAG,SAAS;MAC5B,KAAK,CAAC;QACJ,OAAOH,KAAK,GAAG,IAAI,GAAGG,OAAO,GAAG,SAAS;MAC3C;QACE,OAAOH,KAAK,GAAG,IAAI,GAAGG,OAAO,GAAG,SAAS;IAC7C;EACF;EAEA;EACAC,eAAeA,CAACV,IAAS;IACvB,MAAMzB,cAAc,GAAG,IAAI,CAACT,wBAAwB,CAACU,GAAG,CAACb,WAAW,CAACgD,uBAAuB,EAAEX,IAAI,CAAC;IACnG,OAAOtB,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC;EACnC;EAEA;EACAqC,QAAQA,CAACC,QAAa;IACpB,IAAIA,QAAQ,EAAE;MACZ,MAAMC,CAAC,GAAG,IAAIvB,IAAI,CAACsB,QAAQ,CAAC;MAC5B,MAAME,KAAK,GAAGC,MAAM,CAACF,CAAC,CAACG,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACvD,MAAMC,GAAG,GAAGH,MAAM,CAACF,CAAC,CAACM,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAChD,MAAMG,KAAK,GAAG,GAAGN,KAAK,IAAII,GAAG,IAAIL,CAAC,CAACQ,WAAW,EAAE,EAAE;MAClD,OAAOD,KAAK;IACd,CAAC,MAAM;MACL,MAAMA,KAAK,GAAG,EAAE;MAChB,OAAOA,KAAK;IACd;EACF;EAEA;EACOE,QAAQA,CAACV,QAAa;IAC3B,IAAIA,QAAQ,EAAE;MACZ,MAAMC,CAAC,GAAG,IAAIvB,IAAI,CAACsB,QAAQ,CAAC;MAC5B,MAAMW,MAAM,GAAIV,CAAC,CAACW,QAAQ,EAAE,GAAG,EAAE,GAAI,IAAI,GAAG,IAAI;MAChD,MAAMC,IAAI,GAAIZ,CAAC,CAACW,QAAQ,EAAE,GAAG,EAAE,GAAIX,CAAC,CAACW,QAAQ,EAAE,GAAGX,CAAC,CAACW,QAAQ,EAAE,GAAG,EAAE;MACnE,MAAMV,KAAK,GAAGD,CAAC,CAACG,QAAQ,EAAE,GAAG,CAAC;MAC9B,MAAMR,OAAO,GAAWK,CAAC,CAACa,UAAU,EAAE;MACtC,IAAIC,GAAW;MACf,IAAInB,OAAO,GAAG,EAAE,EAAE;QAChBmB,GAAG,GAAG,GAAG,GAAGnB,OAAO,CAACoB,QAAQ,EAAE;MAChC,CAAC,MAAM;QACLD,GAAG,GAAGnB,OAAO,CAACoB,QAAQ,EAAE;MAC1B;MACA;MACA,MAAMR,KAAK,GAAGK,IAAI,GAAG,GAAG,GAAGE,GAAG,GAAG,GAAG,GAAGJ,MAAM;MAC7C,OAAOH,KAAK;IACd,CAAC,MAAM;MACL,MAAMA,KAAK,GAAG,EAAE;MAChB,OAAOA,KAAK;IACd;EACF;EAEOS,cAAcA,CAACC,CAAM,EAAEC,CAAM;IAClC,MAAMC,WAAW,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IACvC;IACA,MAAMC,IAAI,GAAG3C,IAAI,CAAC4C,GAAG,CAACJ,CAAC,CAACT,WAAW,EAAE,EAAES,CAAC,CAACd,QAAQ,EAAE,EAAEc,CAAC,CAACX,OAAO,EAAE,CAAC;IACjE,MAAMgB,IAAI,GAAG7C,IAAI,CAAC4C,GAAG,CAACH,CAAC,CAACV,WAAW,EAAE,EAAEU,CAAC,CAACf,QAAQ,EAAE,EAAEe,CAAC,CAACZ,OAAO,EAAE,CAAC;IAEjE,OAAOb,IAAI,CAACC,KAAK,CAAC,CAAC4B,IAAI,GAAGF,IAAI,IAAID,WAAW,CAAC;EAChD;EAGA;EACAI,cAAcA,CAACC,QAAgB;IAC/B;IACA,MAAMC,UAAU,GAAGD,QAAQ,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAE9C,MAAMC,UAAU,GAAG,EAAE;IACrB,IAAIF,UAAU,CAACG,MAAM,GAAGD,UAAU,EAAE;MAClC,OAAOH,QAAQ,CAACK,IAAI,EAAE,CAAC,CAAC;IAC1B;IAEA,MAAMC,QAAQ,GAAGL,UAAU,CAAC9C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3C,MAAMoD,GAAG,GAAGN,UAAU,CAAC9C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACtC,MAAMqD,QAAQ,GAAGP,UAAU,CAAC9C,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5C,MAAMsD,SAAS,GAAGR,UAAU,CAACG,MAAM,GAAGD,UAAU,GAAGF,UAAU,CAAC9C,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE;IAEhF,IAAIuD,SAAS,GAAG,IAAIJ,QAAQ,KAAKC,GAAG,IAAIC,QAAQ,EAAE;IAClD,IAAIC,SAAS,EAAE;MACbC,SAAS,IAAI,SAASD,SAAS,EAAE;IACnC;IAEA,OAAOC,SAAS,CAACL,IAAI,EAAE;EACzB;EAEE;EACAM,MAAMA,CAACC,YAAoB;IACzB,OAAO,SAAS,GAAGA,YAAY;EACjC;EAEAC,UAAUA,CAACC,KAAU;IACnBA,KAAK,CAACC,MAAM,CAACC,GAAG,GAAG,kCAAkC;EACvD;EAEOC,QAAQA,CAACC,IAAS;IACvB,OAAO7F,WAAW,CAAC8F,SAAS,GAAGD,IAAI;EACrC;EAEAE,UAAUA,CAACC,IAAS;IAClB,IAAIA,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACtD,OAAO,EAAE;IACX;IACA,MAAMC,CAAC,GAAG,IAAItE,IAAI,CAACoE,IAAI,CAAC;IACxB,MAAM5C,KAAK,GAAGC,MAAM,CAAC6C,CAAC,CAAC5C,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,GAAG,GAAGH,MAAM,CAAC6C,CAAC,CAACzC,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChD,MAAM4C,IAAI,GAAGD,CAAC,CAACvC,WAAW,EAAE;IAC5B,OAAO,GAAGP,KAAK,IAAII,GAAG,IAAI2C,IAAI,EAAE;EAClC;EAEA;EACA,OAAOJ,UAAUA,CAACC,IAAS;IACzB,IAAIA,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACtD,OAAO,EAAE;IACX;IACA,MAAMC,CAAC,GAAG,IAAItE,IAAI,CAACoE,IAAI,CAAC;IACxB,MAAM5C,KAAK,GAAGC,MAAM,CAAC6C,CAAC,CAAC5C,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,GAAG,GAAGH,MAAM,CAAC6C,CAAC,CAACzC,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChD,MAAM4C,IAAI,GAAGD,CAAC,CAACvC,WAAW,EAAE;IAC5B,OAAO,GAAGP,KAAK,IAAII,GAAG,IAAI2C,IAAI,EAAE;EAClC;EAGAC,YAAYA,CAAA;IACV,MAAMC,KAAK,GAAG,IAAIzE,IAAI,EAAE;IACxB,IAAIoE,IAAI,GAAGK,KAAK,CAAC5C,OAAO,EAAE,GAAG,CAAC,GAAG4C,KAAK,CAAC5C,OAAO,EAAE,GAC9C,IAAI4C,KAAK,CAAC5C,OAAO,EAAE,EAAE;IACvB,IAAIL,KAAK,GAAGiD,KAAK,CAAC/C,QAAQ,EAAE,GAAG,CAAC,GAAG+C,KAAK,CAAC/C,QAAQ,EAAE,GAAG,CAAC,GACrD,IAAI+C,KAAK,CAAC/C,QAAQ,EAAE,GAAG,CAAC,EAAE;IAC5B,IAAI6C,IAAI,GAAGE,KAAK,CAAC1C,WAAW,EAAE;IAC9B,IAAI2C,SAAS,GAAGH,IAAI,GAAG,GAAG,GAAG/C,KAAK,GAAG,GAAG,GAAG4C,IAAI;IAC/C,OAAOM,SAAS;EAClB;EAEAC,cAAcA,CAACC,IAAY,EAAEC,IAAS;IACpCD,IAAI,GAAGA,IAAI,CAACE,iBAAiB,EAAE;IAC/B,OAAOD,IAAI,CAACE,KAAK,CAACD,iBAAiB,EAAE,CAACE,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC,IACxDC,IAAI,CAACI,YAAY,CAACH,iBAAiB,EAAE,CAACE,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC,IACxDC,IAAI,CAACK,SAAS,CAACJ,iBAAiB,EAAE,CAACE,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC;IACrD;EACF;EAEAO,cAAcA,CAACC,MAAW;IACxB;IACA,MAAMC,eAAe,GAAGC,UAAU,CAACF,MAAgB,CAAC,IAAI,CAAC;IAEzD;IACA,MAAMG,SAAS,GAAG,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAC/CC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC;IAEF,OAAOJ,SAAS,CAACK,MAAM,CAACP,eAAe,CAAC;EAC1C;EAEAQ,eAAeA,CAACC,UAAe;IAC7B,IAAIA,UAAU,EAAE;MACd,MAAMvE,CAAC,GAAG,IAAIvB,IAAI,CAAC8F,UAAU,CAAC;MAC9B,MAAMtE,KAAK,GAAGC,MAAM,CAACF,CAAC,CAACwE,WAAW,EAAE,GAAG,CAAC,CAAC,CAACpE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MAC9D,MAAMC,GAAG,GAAGH,MAAM,CAACF,CAAC,CAACyE,UAAU,EAAE,CAAC,CAACrE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACrD,MAAM4C,IAAI,GAAGhD,CAAC,CAAC0E,cAAc,EAAE;MAC7B,OAAO,GAAGzE,KAAK,IAAII,GAAG,IAAI2C,IAAI,EAAE;IAClC,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF;EAEA2B,uBAAuBA,CAACC,gBAAqB;IAC3C,IAAI,CAACA,gBAAgB,IAAIA,gBAAgB,KAAK,CAAC,IAAIA,gBAAgB,KAAK,IAAI,EAAE;MAC5E,OAAO,IAAI;IACb;IACA,OAAO,GAAGA,gBAAgB,GAAG;EAC/B;EAIAC,kBAAkBA,CAACC,KAAa;IAC9B,MAAMC,QAAQ,GAA8B;MAC1C,OAAO,EAAE,GAAG;MACZ,MAAM,EAAE,GAAG;MACX,MAAM,EAAE,GAAG;MACX,QAAQ,EAAE,GAAG;MACb,OAAO,EAAE;KACV;IAED,OAAOD,KAAK,CAACpD,OAAO,CAAC,+BAA+B,EAAGsD,KAAK,IAAKD,QAAQ,CAACC,KAAK,CAAC,IAAIA,KAAK,CAAC;EAC5F;EACA;EACA;EACA;EAEAC,sBAAsBA,CAACpC,IAAmB;IACxC,MAAME,CAAC,GAAG,IAAItE,IAAI,CAACoE,IAAI,CAAC;IACxB,MAAMG,IAAI,GAAGD,CAAC,CAACvC,WAAW,EAAE;IAC5B,MAAMP,KAAK,GAAGC,MAAM,CAAC6C,CAAC,CAAC5C,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACzD,MAAMC,GAAG,GAAGH,MAAM,CAAC6C,CAAC,CAACzC,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAClD,OAAO,GAAG4C,IAAI,IAAI/C,KAAK,IAAII,GAAG,EAAE;EACpC;EACA;EACA6E,WAAWA,CAACC,OAAW,EAACC,KAAa;IACnC,MAAMC,eAAe,GAAEF,OAAO,CAAC,CAAC;IAChC,OAAOE,eAAe,CAACC,QAAQ,CAACF,KAAK,CAAC;EACxC;EACAG,kBAAkBA,CAACC,OAAY,EAAEC,UAAoB;IACnD,OAAOD,OAAO,CAACE,GAAG,CAAEC,KAAU,IAAI;MAChC,MAAMC,cAAc,GAAGD,KAAK,CAACH,OAAO,CAACE,GAAG,CAAEG,CAAM,IAAI;QAClD,IAAIJ,UAAU,CAACH,QAAQ,CAACO,CAAC,CAACT,KAAK,CAAC,IAAI,OAAOS,CAAC,CAAC9H,KAAK,KAAK,QAAQ,EAAE;UAC/D,OAAO;YACL,GAAG8H,CAAC;YACJ9H,KAAK,EAAE8H,CAAC,CAAC9H,KAAK,GAAG,IAAIU,IAAI,CAACoH,CAAC,CAAC9H,KAAK,CAAC,GAAG;WACtC;QACH;QACA,OAAO8H,CAAC;MACV,CAAC,CAAC;MAEF,OAAO;QACL,GAAGF,KAAK;QACRH,OAAO,EAAEI;OACV;IACH,CAAC,CAAC;EACJ;EAGA;;;;;;;;;;EAUAE,WAAWA,CAACC,IAAY;IACtB,OAAOtG,IAAI,CAACuG,KAAK,CAACD,IAAI,GAAG,IAAI,CAAC;EAChC;EAEAE,kBAAkBA,CAACC,UAAc,EAAEC,iBAAqB,EAAEC,gBAAoB;IAC1E;IACA,MAAMC,cAAc,GAAGD,gBAAgB;IACvC,KAAK,MAAME,MAAM,IAAID,cAAc,EAAE;MACnCC,MAAM,CAACC,MAAM,GAAG,KAAK;IACvB;IACA;IACA,KAAK,MAAMC,UAAU,IAAIN,UAAU,EAAE;MACnC,MAAMO,aAAa,GAAGJ,cAAc,CAACK,IAAI,CAAEpD,IAAQ,IAAKA,IAAI,CAAC8B,KAAK,KAAKoB,UAAU,CAACpB,KAAK,CAAC;MACxF,IAAIqB,aAAa,EAAE;QACjBA,aAAa,CAACF,MAAM,GAAGC,UAAU,CAACD,MAAM;MAC1C;IACF;IACA;IACA,MAAMI,QAAQ,GAAO,EAAE;IACvBR,iBAAiB,CAACS,OAAO,CAAEtD,IAAQ,IAAI;MACrCqD,QAAQ,CAACrD,IAAI,CAAC8B,KAAK,CAAC,GAAG9B,IAAI,CAACuD,UAAU;IACxC,CAAC,CAAC;IACF;IACA,KAAK,IAAIP,MAAM,IAAID,cAAc,EAAE;MACjC,IAAIM,QAAQ,CAACG,cAAc,CAACR,MAAM,CAAClB,KAAK,CAAC,EAAE;QACzCkB,MAAM,CAACS,KAAK,GAAGJ,QAAQ,CAACL,MAAM,CAAClB,KAAK,CAAC;MACvC;IACF;IACA;IACAiB,cAAc,CAACW,IAAI,CAAC,CAAC/F,CAAK,EAAEC,CAAK,KAAKD,CAAC,CAAC8F,KAAK,GAAG7F,CAAC,CAAC6F,KAAK,CAAC;IACxD,OAAOV,cAAc;EACvB;EAEC;EACDY,eAAeA,CAACC,UAAe,EAAEC,WAA4B,EAAEzE,IAAe;IAC5E,MAAM0E,OAAO,GAAG1E,IAAI,CAAC2E,QAAQ,CAACF,WAAW,CAAC;IAC1C,IAAI,CAACC,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;IACA,IAAIE,MAAM,GAAGF,OAAO,CAACG,QAAQ,CAACL,UAAU,CAAC,KAAKE,OAAO,CAACI,KAAK,IAAIJ,OAAO,CAACK,OAAO,CAAC;IAC/E,OAAOH,MAAM;EACf;EAEAI,eAAeA,CAAA;IACb,IAAIC,YAAY,GAAG,IAAI,CAACxK,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC;IAC/D,OAAOwK,YAAY;EACrB;EAEA;EACAC,eAAeA,CAACC,IAAS;IACvB,IAAI,CAACA,IAAI,EAAE;MAAE,OAAO,GAAG;IAAE;IAEzB,MAAMC,SAAS,GAAIC,GAAQ,IAAK,CAACA,GAAG,IAAI,EAAE,EAAEhH,QAAQ,EAAE,CAACc,IAAI,EAAE;IAE7D;IACA,MAAMmG,KAAK,GAAGF,SAAS,CAACD,IAAI,CAAClE,SAAS,IAAIkE,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,SAAS,CAAC;IAC3E,MAAMC,IAAI,GAAGL,SAAS,CAACD,IAAI,CAACO,QAAQ,IAAIP,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACS,QAAQ,CAAC;IACvE,IAAIN,KAAK,IAAIG,IAAI,EAAE;MACjB,MAAMI,EAAE,GAAGP,KAAK,GAAGA,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG,EAAE;MACrD,MAAMC,EAAE,GAAGP,IAAI,GAAGA,IAAI,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG,EAAE;MACnD,OAAQF,EAAE,GAAGG,EAAE,IAAK,GAAG;IACzB;IAEA;IACA,MAAMC,IAAI,GAAGb,SAAS,CAACD,IAAI,CAACnE,YAAY,IAAImE,IAAI,CAACe,YAAY,IAAIf,IAAI,CAACgB,QAAQ,IAAIhB,IAAI,CAACiB,QAAQ,IAAIjB,IAAI,CAACnF,IAAI,CAAC;IAC7G,IAAIiG,IAAI,EAAE;MACR,MAAMI,KAAK,GAAGJ,IAAI,CAACK,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;MAC/C,IAAIH,KAAK,CAACnH,MAAM,KAAK,CAAC,EAAE;QACtB,OAAOmH,KAAK,CAAC,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;MACzC;MACA,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC,GAAGO,KAAK,CAACA,KAAK,CAACnH,MAAM,GAAG,CAAC,CAAC,CAAC4G,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE;IAC/E;IAEA;IACA,MAAMjF,KAAK,GAAGsE,SAAS,CAACD,IAAI,CAACrE,KAAK,IAAIqE,IAAI,CAACsB,KAAK,CAAC;IACjD,MAAMC,QAAQ,GAAGtB,SAAS,CAACD,IAAI,CAACwB,QAAQ,IAAIxB,IAAI,CAACuB,QAAQ,IAAIvB,IAAI,CAACyB,QAAQ,CAAC;IAC3E,MAAMC,MAAM,GAAG/F,KAAK,IAAI4F,QAAQ;IAChC,IAAIG,MAAM,EAAE;MACV;MACA,MAAMC,IAAI,GAAGhG,KAAK,GAAG+F,MAAM,CAACP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGO,MAAM;MAClD,MAAME,OAAO,GAAGD,IAAI,CAAC9H,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MAC9C,IAAI+H,OAAO,CAAC7H,MAAM,IAAI,CAAC,EAAE;QACvB,OAAO,CAAC6H,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,EAAEhB,WAAW,EAAE;MAChD;MACA,IAAIgB,OAAO,CAAC7H,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO6H,OAAO,CAAC,CAAC,CAAC,CAAChB,WAAW,EAAE;MACjC;IACF;IAEA,OAAO,GAAG;EACZ;;qCA3ZW1L,UAAU,EAAA2M,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,wBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;;WAAVhN,UAAU;IAAAiN,OAAA,EAAVjN,UAAU,CAAAkN,IAAA;IAAAC,UAAA,EAFT;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}