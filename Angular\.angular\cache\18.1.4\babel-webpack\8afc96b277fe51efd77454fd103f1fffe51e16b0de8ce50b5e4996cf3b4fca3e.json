{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ProjectRoutingModule } from './projects-routing.module';\nimport { ProjectsComponent } from './projects.component';\nimport { ProjectEditComponent } from './project-edit/project-edit.component';\nimport { ProjectListComponent } from './project-list/project-list.component';\nimport { ProjectViewComponent } from './project-view/project-view.component';\nimport { SharedModule } from '../shared/shared.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonsModule } from '@progress/kendo-angular-buttons';\nimport { DateInputsModule } from '@progress/kendo-angular-dateinputs';\nimport { DropDownsModule } from '@progress/kendo-angular-dropdowns';\nimport { GridModule } from '@progress/kendo-angular-grid';\nimport { InputsModule } from '@progress/kendo-angular-inputs';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ProjectPopupComponent } from './project-popup/project-popup.component';\nlet ProjectsModule = class ProjectsModule {};\nProjectsModule = __decorate([NgModule({\n  declarations: [ProjectsComponent, ProjectListComponent, ProjectEditComponent, ProjectViewComponent, ProjectPopupComponent],\n  imports: [CommonModule, ProjectRoutingModule, SharedModule, FormsModule, GridModule, InputsModule, DropDownsModule, ButtonsModule, DateInputsModule, InlineSVGModule, FormsModule, ReactiveFormsModule, NgbModule, NgSelectModule],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]\n})], ProjectsModule);\nexport { ProjectsModule };", "map": {"version": 3, "names": ["NgModule", "CUSTOM_ELEMENTS_SCHEMA", "NO_ERRORS_SCHEMA", "CommonModule", "ProjectRoutingModule", "ProjectsComponent", "ProjectEditComponent", "ProjectListComponent", "ProjectViewComponent", "SharedModule", "FormsModule", "ReactiveFormsModule", "ButtonsModule", "DateInputsModule", "DropDownsModule", "GridModule", "InputsModule", "InlineSVGModule", "NgbModule", "NgSelectModule", "ProjectPopupComponent", "ProjectsModule", "__decorate", "declarations", "imports", "schemas"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\projects.module.ts"], "sourcesContent": ["import { NgModule,CUSTOM_ELEMENTS_SCHEMA ,NO_ERRORS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ProjectRoutingModule } from './projects-routing.module';\r\nimport { ProjectsComponent } from './projects.component';\r\nimport { ProjectEditComponent } from './project-edit/project-edit.component';\r\nimport { ProjectListComponent } from './project-list/project-list.component';\r\nimport { ProjectViewComponent } from './project-view/project-view.component';\r\nimport { SharedModule } from '../shared/shared.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ButtonsModule } from '@progress/kendo-angular-buttons';\r\nimport { DateInputsModule } from '@progress/kendo-angular-dateinputs';\r\nimport { DropDownsModule } from '@progress/kendo-angular-dropdowns';\r\nimport { GridModule } from '@progress/kendo-angular-grid';\r\nimport { InputsModule } from '@progress/kendo-angular-inputs';\r\nimport { InlineSVGModule } from 'ng-inline-svg-2';\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { ProjectPopupComponent } from './project-popup/project-popup.component';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [ProjectsComponent, ProjectListComponent, ProjectEditComponent, ProjectViewComponent, ProjectPopupComponent],\r\n  imports: [\r\n    CommonModule,\r\n    ProjectRoutingModule,\r\n    SharedModule,\r\n    FormsModule,\r\n    GridModule,\r\n    InputsModule,\r\n    DropDownsModule,\r\n    ButtonsModule,\r\n    DateInputsModule,\r\n    InlineSVGModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    NgbModule,\r\n    NgSelectModule\r\n  ],\r\n   schemas: [ CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA ],\r\n})\r\nexport class ProjectsModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,EAACC,sBAAsB,EAAEC,gBAAgB,QAAQ,eAAe;AACjF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,qBAAqB,QAAQ,yCAAyC;AAwBxE,IAAMC,cAAc,GAApB,MAAMA,cAAc,GAAI;AAAlBA,cAAc,GAAAC,UAAA,EApB1BtB,QAAQ,CAAC;EACRuB,YAAY,EAAE,CAAClB,iBAAiB,EAAEE,oBAAoB,EAAED,oBAAoB,EAAEE,oBAAoB,EAAEY,qBAAqB,CAAC;EAC1HI,OAAO,EAAE,CACPrB,YAAY,EACZC,oBAAoB,EACpBK,YAAY,EACZC,WAAW,EACXK,UAAU,EACVC,YAAY,EACZF,eAAe,EACfF,aAAa,EACbC,gBAAgB,EAChBI,eAAe,EACfP,WAAW,EACXC,mBAAmB,EACnBO,SAAS,EACTC,cAAc,CACf;EACAM,OAAO,EAAE,CAAExB,sBAAsB,EAAEC,gBAAgB;CACrD,CAAC,C,EACWmB,cAAc,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}