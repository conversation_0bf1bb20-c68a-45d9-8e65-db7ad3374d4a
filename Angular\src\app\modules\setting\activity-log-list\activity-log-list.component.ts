import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, ViewChild, OnInit, OnDestroy, AfterViewInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import _, { each } from 'lodash';
import { map, Subject, debounceTime, distinctUntilChanged, Subscription } from 'rxjs';
import { AppSettings } from 'src/app/app.settings';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';
import { ConfirmationDialogComponent } from '../../shared/confirmation-dialog/confirmation-dialog.component';
import { Page } from '../../shared/data/pagination.module';
import { AppService } from '../../services/app.service';
import { ActivityLogService } from '../../services/activity-log.service';
import { ExceljsService } from '../../services/exceljs.service';
import { KendoColumnService } from '../../services/kendo-column.service';
import { SortDescriptor } from '@progress/kendo-data-query';
import { FilterDescriptor, CompositeFilterDescriptor } from '@progress/kendo-data-query';

// Type definitions
interface ActivityLogData {
  activityId: number;
  activityEvent: string;
  eventDescription: string;
  eventDetails: string;
  tableName: string;
  tableId: number;
  activityStatus: string;
  activityUserType: string;
  field1: string;
  field2: string;
  field3: string;
  createdBy: number;
  createdDate: string;
  lastUpdatedBy: number;
  lastUpdatedDate: string;
  createdByUserFullName: string;
}

// Type for page configuration
interface PageConfig {
  size: number;
  pageNumber: number;
  totalElements: number;
  totalPages: number;
  orderBy: string;
  orderDir: string;
}

@Component({
  selector: 'app-activity-log-list',
  templateUrl: './activity-log-list.component.html',
  styleUrl: './activity-log-list.component.scss'
})
export class ActivityLogListComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('normalGrid') grid: any;

  // Data
  public serverSideRowData: ActivityLogData[] = [];
  public gridData: any = [];
  public IsListHasValue: boolean = false;

  public loading: boolean = false;
  public isLoading: boolean = false;

  loginUser: Record<string, any> = {};

  // Search
  public searchData: string = '';
  private searchTerms = new Subject<string>();
  private searchSubscription: Subscription;

  // Enhanced Filters for Kendo UI
  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public activeFilters: Array<{
    field: string;
    operator: string;
    value: any;
  }> = [];

  public filterOptions: Array<{ text: string; value: string | null }> = [
    { text: 'All', value: null },
    { text: 'Success', value: 'SUCCESS' },
    { text: 'Failed', value: 'FAILED' }
  ];

  // Advanced filter options
  public advancedFilterOptions = {
    status: [
      { text: 'All', value: null },
      { text: 'Success', value: 'SUCCESS' },
      { text: 'Failed', value: 'FAILED' }
    ] as Array<{ text: string; value: string | null }>,
    userTypes: [
      { text: 'All Types', value: null },
      { text: 'Admin', value: 'ADMIN' },
      { text: 'User', value: 'USER' }
    ] as Array<{ text: string; value: string | null }>
  };

  // Filter state
  public showAdvancedFilters = false;
  public appliedFilters: {
    status?: string | null;
    userType?: string | null;
  } = {};

  // Kendo Grid properties
  public page: PageConfig = {
    size: 10,
    pageNumber: 0,
    totalElements: 0,
    totalPages: 0,
    orderBy: 'createdDate',
    orderDir: 'desc'
  };
  public skip: number = 0;
  public sort: SortDescriptor[] = [
    { field: 'createdDate', dir: 'desc' }
  ];

  // Column visibility system properties
  public kendoHide: any;
  public hiddenData: any = [];
  public kendoColOrder: any = [];
  public kendoInitColOrder: any = [];
  public hiddenFields: any = [];

  // Column configuration for the new system
  public gridColumns: string[] = [];
  public defaultColumns: string[] = [];
  public fixedColumns: string[] = [];
  public draggableColumns: string[] = [];

  private readonly GRID_STATE_KEY = 'activity-logs-grid-state';

  // Export options
  public exportOptions: Array<{ text: string; value: string }> = [
    { text: 'Export All', value: 'all' },
    { text: 'Export Selected', value: 'selected' },
    { text: 'Export Filtered', value: 'filtered' }
  ];

  // Selection state
  public selectedLogs: ActivityLogData[] = [];
  public isAllSelected: boolean = false;

  // Statistics
  public logStatistics: {
    successLogs: number;
    failedLogs: number;
    totalLogs: number;
  } = {
    successLogs: 0,
    failedLogs: 0,
    totalLogs: 0
  };

  // Legacy properties (keeping for backward compatibility)
  pageSize: number = AppSettings.PAGE_SIZE;
  pageSizeOptions: any = AppSettings.PAGE_SIZE_OPTIONS;
  itemsPerPage = new FormControl(this.pageSize);
  defaultOrder = 'desc';
  defaultOrderBy = 'createdDate';
  statusData: boolean = false;
  selectedTab = 'All';
  innerWidth: any;
  displayMobile: boolean = false;

  constructor(
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal,
    public exceljsService: ExceljsService,
    private httpUtilService: HttpUtilsService,
    public AppService: AppService,
    private layoutUtilService: CustomLayoutUtilsService,
    private activityLogService: ActivityLogService,
    private kendoColumnService: KendoColumnService,
  ) {
    // set the default paging options
    this.page.pageNumber = 0;
    this.page.size = this.pageSize;
    this.page.orderBy = 'createdDate';
    this.page.orderDir = 'desc';
  }

  ngOnInit(): void {
    this.loginUser = this.AppService.getLoggedInUser();
    this.setupSearchSubscription();
    this.loadTable();
    this.loadLogStatistics();
  }

  ngAfterViewInit(): void {
    this.loadGridState();
    this.setupGridEventHandlers();
  }

  // Method to handle when the component becomes visible
  onTabActivated(): void {
    // Set loading state for tab activation
    this.loading = true;
    this.isLoading = true;
    // Refresh the data when the tab is activated
    this.loadTable();
    this.loadLogStatistics();
  }

  // Refresh grid data - only refresh the grid with latest API call
  refreshGrid(): void {
    // Set loading state to show full-screen loader
    this.loading = true;
    this.isLoading = true;
    // Refresh the data
    this.loadTable();
  }

  ngOnDestroy(): void {
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }
    this.searchTerms.complete();
  }

  // Setup search subscription with debouncing
  private setupSearchSubscription(): void {
    this.searchSubscription = this.searchTerms
      .pipe(
        debounceTime(500),
        distinctUntilChanged()
      )
      .subscribe(() => {
        // Set loading state for search
        this.loading = true;
        this.isLoading = true;
        this.loadTable();
      });
  }

  // Setup grid event handlers
  private setupGridEventHandlers(): void {
    if (this.grid) {
      this.grid.pageChange.subscribe((event: any) => {
        this.pageChange(event);
      });
    }
  }

  // Load grid state from localStorage
  private loadGridState(): void {
    try {
      const savedState = localStorage.getItem(this.GRID_STATE_KEY);
      if (savedState) {
        const state = JSON.parse(savedState);
        this.page.size = state.pageSize || this.page.size;
        this.sort = state.sort || this.sort;
        this.filter = state.filter || this.filter;
        this.skip = state.skip || 0;
      }
    } catch (error) {
      console.warn('Error loading grid state:', error);
    }
  }

  // Save grid state to localStorage
  private saveGridState(): void {
    try {
      const state = {
        pageSize: this.page.size,
        sort: this.sort,
        filter: this.filter,
        skip: this.skip
      };
      localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));
    } catch (error) {
      console.warn('Error saving grid state:', error);
    }
  }

  // Load activity logs table
  loadTable(): void {
    this.loadTableWithKendoEndpoint();
  }

  // Load data using Kendo UI specific endpoint
  loadTableWithKendoEndpoint() {
    this.loading = true;
    this.isLoading = true;

    // Enable loader
    this.httpUtilService.loadingSubject.next(true);

    // Prepare state object for Kendo UI endpoint
    const state = {
      take: this.page.size,
      skip: this.skip,
      sort: this.sort,
      filter: this.filter,
      search: this.searchData
    };

    this.activityLogService.getActivityLogsForKendoGrid(state).subscribe({
      next: (data: {
        isFault?: boolean;
        responseData?: {
          data: any[];
          total: number;
          errors?: string[];
        };
        data?: any[];
        total?: number;
        errors?: string[];
      }) => {
        // Handle the new API response structure
        if (data.isFault || (data.responseData && data.responseData.errors && data.responseData.errors.length > 0)) {
          const errors = data.responseData?.errors || data.errors || [];
          console.error('Kendo UI Grid errors:', errors);
          this.handleEmptyResponse();
        } else {
          // Handle both old and new response structures
          const responseData = data.responseData || data;
          const logData = responseData.data || [];
          const total = responseData.total || 0;

          this.IsListHasValue = logData.length !== 0;
          this.serverSideRowData = logData;
          this.page.totalElements = total;
          this.page.totalPages = Math.ceil(total / this.page.size);
          
          // Create a data source with total count for Kendo Grid
          this.gridData = {
            data: logData,
            total: total
          };
        }
        this.httpUtilService.loadingSubject.next(false);
      },
      error: (error: unknown) => {
        console.error('Error loading data with Kendo UI endpoint:', error);
        this.handleEmptyResponse();
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
      },
      complete: () => {
        this.loading = false;
        this.isLoading = false;
      }
    });
  }

  // Handle empty response
  private handleEmptyResponse(): void {
    this.IsListHasValue = false;
    this.serverSideRowData = [];
    this.gridData = [];
    this.page.totalElements = 0;
    this.page.totalPages = 0;
  }

  // Load log statistics
  loadLogStatistics(): void {
    // This would be implemented if there's a statistics endpoint
    // For now, we'll calculate from the current data
    this.updateLogStatistics();
  }

  // Update log statistics
  private updateLogStatistics(): void {
    const logs = this.serverSideRowData;
    this.logStatistics = {
      successLogs: logs.filter(l => l.activityStatus === 'SUCCESS').length,
      failedLogs: logs.filter(l => l.activityStatus === 'FAILED').length,
      totalLogs: logs.length
    };
  }

  // Search functionality
  onSearchKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.loadTable();
    }
  }

  onSearchChange(): void {
    console.log('Search changed:', this.searchData);
    // Trigger search with debounce
    this.searchTerms.next(this.searchData || '');
  }

  clearSearch(): void {
    if (!this.searchData || this.searchData.trim() === '') {
      this.searchTerms.next('');
    }
  }

  // Advanced filters
  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  applyAdvancedFilters(): void {
    this.filter = { logic: 'and', filters: [] };
    
    if (this.appliedFilters.status) {
      this.filter.filters.push({
        field: 'activityStatus',
        operator: 'eq',
        value: this.appliedFilters.status
      });
    }

    if (this.appliedFilters.userType) {
      this.filter.filters.push({
        field: 'activityUserType',
        operator: 'eq',
        value: this.appliedFilters.userType
      });
    }

    this.loadTable();
  }

  clearAdvancedFilters(): void {
    this.appliedFilters = {};
    this.filter = { logic: 'and', filters: [] };
    this.loadTable();
  }

  // Kendo UI Grid event handlers
  onSortChange(event: any): void {
    console.log('Sort change triggered:', event.sort);
    
    // Handle empty sort array (normalize/unsort case)
    const incomingSort = Array.isArray(event.sort) ? event.sort : [];
    
    if (incomingSort.length === 0) {
      // Normalize/unsort case - return to default sorting
      console.log('Normalize triggered - returning to default sort');
      this.sort = [{ field: 'createdDate', dir: 'desc' }];
      
      // Reset the grid's sort state to default
      if (this.grid) {
        this.grid.sort = [{ field: 'createdDate', dir: 'desc' }];
      }
    } else {
      // Normal sorting case
      this.sort = incomingSort;
    }

    console.log('Final sort state:', this.sort);

    this.skip = 0;
    this.page.pageNumber = 0;
    this.saveGridState();
    // Set loading state for sorting
    this.loading = true;
    this.isLoading = true;
    this.loadTable();
  }

  onSelectionChange(event: any): void {
    this.selectedLogs = event.selectedRows || [];
    this.isAllSelected = this.selectedLogs.length === this.serverSideRowData.length;
  }

  onColumnReorder(event: any): void {
    // Handle column reordering
    this.kendoColOrder = event.columns;
  }

  updateColumnVisibility(event: any): void {
    // Handle column visibility changes
    this.kendoHide = event.columns;
  }

  filterChange(event: any): void {
    this.filter = event.filter;
    this.skip = 0;
    this.page.pageNumber = 0;
    this.saveGridState();
    // Set loading state for filtering
    this.loading = true;
    this.isLoading = true;
    this.loadTable();
  }

  pageChange(event: any): void {
    // Use Kendo's provided values as source of truth
    this.skip = event.skip;
    this.page.size = event.take || this.page.size;
    this.page.pageNumber = Math.floor(this.skip / this.page.size);
    this.saveGridState();
    // Set loading state for pagination
    this.loading = true;
    this.isLoading = true;
    this.loadTable();
  }

  /**
   * Reset the current state of column visibility and order in the grid to its original state.
   */
  resetTable(): void {
    // Check if loginUser is available
    if (!this.loginUser || !this.loginUser.userId) {
      console.error('loginUser not available:', this.loginUser);
      this.layoutUtilService.showError('User not logged in. Please refresh the page and try again.', '');
      return;
    }

    // Reset all grid state to default
    this.page.pageNumber = 0;
    this.skip = 0;
    this.sort = [{ field: 'createdDate', dir: 'desc' }];
    this.filter = { logic: 'and', filters: [] };
    this.searchData = '';
    this.appliedFilters = {};
    this.showAdvancedFilters = false;

    // Reset column visibility and order
    if (this.grid && this.grid.columns) {
      this.grid.columns.forEach((column: any) => {
        const index = this.gridColumns.indexOf(column.field);
        if (index !== -1) {
          column.orderIndex = index;
        }
        // Reset column visibility - show all columns
        if (column.field && column.field !== 'action') {
          column.hidden = false;
        }
      });
    }

    // Clear hidden columns
    this.hiddenData = [];
    this.kendoColOrder = [];
    this.hiddenFields = [];

    // Reset the Kendo Grid's internal state
    if (this.grid) {
      // Clear all filters
      this.grid.filter = { logic: 'and', filters: [] };
      
      // Reset sorting
      this.grid.sort = [{ field: 'createdDate', dir: 'desc' }];
      
      // Reset to first page
      this.grid.skip = 0;
      this.grid.pageSize = this.page.size;
    }

    // Prepare reset data
    const userData = {
      pageName: 'ActivityLogs',
      userID: this.loginUser.userId,
      hiddenData: [],
      kendoColOrder: [],
      LoggedId: this.loginUser.userId
    };

    // Show loading state
    this.httpUtilService.loadingSubject.next(true);

    // Save reset state to backend
    this.kendoColumnService.createHideFields(userData).subscribe({
      next: (res) => {
        this.httpUtilService.loadingSubject.next(false);
        if (!res.isFault) {
          // Also clear from localStorage
          this.kendoColumnService.clearFromLocalStorage('ActivityLogs');
          this.layoutUtilService.showSuccess(res.message || 'Column settings reset successfully.', '');
        } else {
          this.layoutUtilService.showError(res.message || 'Failed to reset column settings.', '');
        }
        
        // Trigger change detection and refresh grid
        this.cdr.detectChanges();
        
        // Small delay to ensure the grid is updated
        setTimeout(() => {
          if (this.grid) {
            this.grid.refresh();
          }
        }, 100);
        
        this.loadTable();
      },
      error: (error) => {
        this.httpUtilService.loadingSubject.next(false);
        console.error('Error resetting column settings:', error);

        // Check if it's an authentication error
        if (error.status === 401 || (error.error && error.error.status === 401)) {
          this.layoutUtilService.showError('Authentication failed. Please login again.', '');
          // Optionally redirect to login page
        } else {
          this.layoutUtilService.showError('Error resetting column settings. Please try again.', '');
        }
      }
    });
  }

  // Export functionality
  exportData(exportType: string): void {
    let dataToExport: any[] = [];

    switch (exportType) {
      case 'all':
        dataToExport = this.serverSideRowData;
        break;
      case 'selected':
        dataToExport = this.selectedLogs;
        break;
      case 'filtered':
        dataToExport = this.serverSideRowData;
        break;
    }

    if (dataToExport.length === 0) {
      return;
    }

    const exportData = dataToExport.map(log => ({
      'Event': log.activityEvent,
      'Description': log.eventDescription,
      'Details': log.eventDetails,
      'Table': log.tableName,
      'Status': log.activityStatus,
      'User Type': log.activityUserType,
      'Created Date': this.AppService.formatDate(log.createdDate),
      'User': log.createdByUserFullName
    }));

    const headers = Object.keys(exportData[0]);
    const rows = exportData.map(item => Object.values(item));
    const colSize = headers.map((_, index) => ({ id: index + 1, width: 20 }));
    this.exceljsService.generateExcel('Activity_Logs_Export', headers, rows, colSize);
  }

  // Log actions
  viewLog(log: ActivityLogData): void {
    // Navigate to view log page
    console.log('View log:', log);
  }

  // Utility methods
  getStatusClass(status: string): string {
    return status === 'SUCCESS' ? 'badge-success' : 'badge-danger';
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    const timeString = date.toLocaleTimeString();
    return `${month}/${day}/${year} ${timeString}`;
  }

  truncateText(text: string, maxLength: number = 50): string {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  getEventIcon(event: string): string {
    const eventIcons: { [key: string]: string } = {
      'USER_CREATED': 'fas fa-user-plus',
      'USER_UPDATED': 'fas fa-user-edit',
      'USER_DELETED': 'fas fa-user-minus',
      'ROLE_CREATED': 'fas fa-shield-plus',
      'ROLE_UPDATED': 'fas fa-shield-edit',
      'ROLE_DELETED': 'fas fa-shield-minus',
      'LOGIN': 'fas fa-sign-in-alt',
      'LOGOUT': 'fas fa-sign-out-alt',
      'PASSWORD_CHANGED': 'fas fa-key',
      'default': 'fas fa-info-circle'
    };
    return eventIcons[event] || eventIcons['default'];
  }
}
