import { formatDate } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {  FilterService } from '@progress/kendo-angular-grid';

import { saveAs } from '@progress/kendo-file-saver';
import { add, each } from 'lodash';
import {
  Subject,
  Subscription,
  debounceTime,
  distinctUntilChanged,
  filter,
} from 'rxjs';
import { AppService } from '../../services/app.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';
import { KendoColumnService } from '../../services/kendo-column.service';
import {
  CompositeFilterDescriptor,
  SortDescriptor,
} from '@progress/kendo-data-query';
import { PermitsService } from '../../services/permits.service';
import { PermitPopupComponent } from '../permit-popup/permit-popup.component';
import { ExceljsService } from '../../services/exceljs.service';
import { PageInfoService } from 'src/app/_metronic/layout/core/page-info.service';

@Component({
  selector: 'app-permit-list',
  templateUrl: './permit-list.component.html',
  styleUrl: './permit-list.component.scss',
})
export class PermitListComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('normalGrid') grid: any;

  // Data
  public serverSideRowData: any[] = [];
  public gridData: any = [];
  public IsListHasValue: boolean = false;

  public loading: boolean = false;
  public isLoading: boolean = false;

  loginUser: any = {};

  // Search
  public searchData: string = '';
  private searchTerms = new Subject<string>();
  private searchSubscription: Subscription;
onMenuDropdownChange(value: any, field: string, filterService: FilterService): void {
  const next: CompositeFilterDescriptor =
    value == null
      ? { filters: [], logic: 'and' }
      : { filters: [{ field, operator: 'eq', value }], logic: 'and' };
  filterService.filter(next);
}
  // Enhanced Filters for Kendo UI
  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public activeFilters: Array<{
    field: string;
    operator: string;
    value: any;
  }> = [];

  public filterOptions: Array<{ text: string; value: string | null }> = [
    { text: 'All', value: null },
    { text: 'Active', value: 'Active' },
    { text: 'Inactive', value: 'Inactive' },
  ];

  // Advanced filter options
  public advancedFilterOptions = {
    status: [
       { text: 'All', value: null },
  { text: 'Requires Resubmit', value: 'Requires Resubmit' },
  { text: 'On Hold', value: 'On Hold' },
  { text: 'Approved', value: 'Approved' },
  { text: 'Pending', value: 'Pending' },
  { text: 'Canceled', value: 'Canceled' },
  { text: 'Complete', value: 'Complete' },
  { text: 'Expired', value: 'Expired' },
  { text: 'Fees Due', value: 'Fees Due' },
  { text: 'In Review', value: 'In Review' },
  { text: 'Issued', value: 'Issued' },
  { text: 'Requires Resubmit for Prescreen', value: 'Requires Resubmit for Prescreen' },
  { text: 'Submitted - Online', value: 'Submitted - Online' },
  { text: 'Void', value: 'Void' },
    ] as Array<{ text: string; value: string | null }>,
    categories: [
      { text: 'All', value: null },
      { text: 'Primary', value: 'Primary' },
      { text: 'Sub Permit', value: 'Sub Permit' },
      { text: 'Industrial', value: 'Industrial' },
      { text: 'Municipal', value: 'Municipal' },
      { text: 'Environmental', value: 'Environmental' },
    
    ] as Array<{ text: string; value: string | null }>,
    categoriess: [
      { text: 'All', value: null },
      { text: 'Approved', value: 'Approved' },
      { text: 'Pacifica Verification', value: 'Pacifica Verification' },
      { text: 'Dis-Approved', value: 'Dis-Approved' },
      { text: 'Pending', value: 'Pending' },
      { text: 'Not Required', value: 'Not Required' },
      { text: 'In Review', value: 'In Review' },
      { text: '1 Cycle Completed', value: '1 Cycle Completed' },
      
      // 'Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed'
    
    ] as Array<{ text: string; value: string | null }>,
  };

  // Filter state
  public showAdvancedFilters = false;
  public appliedFilters: {
    status?: string | null;
    category?: string | null;
    internalReviewStatus?: string | null;
  } = {};

  // Column visibility system
  public kendoHide: any;
  public hiddenData: any = [];
  public kendoColOrder: any = [];
  public kendoInitColOrder: any = [];
  public hiddenFields: any = [];

  // Column configuration
  public gridColumns: string[] = [];
  public defaultColumns: string[] = [];
  public fixedColumns: string[] = [];
  public draggableColumns: string[] = [];
  public normalGrid: any;
  public expandedGrid: any;
  public isExpanded = false;

  // Enhanced Columns with Kendo UI features - adapted for permits
  public gridColumnConfig: Array<{
    field: string;
    title: string;
    width: number;
    isFixed: boolean;
    type: string;
    filterable?: boolean;
    order: number;
  }> = [
    {
      field: 'action',
      title: 'Action',
      width: 100,
      isFixed: true,
      type: 'action',
      order: 1,
    },
     {
      field: 'permitName',
      title: 'Permit/Sub Project Name',
      width: 180,
      isFixed: true,
      type: 'text',
      filterable: true,
      order: 2,
    },
    {
      field: 'permitNumber',
      title: 'Permit #',
      width: 180,
      isFixed: true,
      type: 'text',
      filterable: true,
      order: 3,
    },
   
      {
      field: 'projectName',
      title: 'Project Name',
      width: 180,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 4,
    },
       {
      field: 'permitCategory',
      title: 'Category',
      width: 120,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 5,
    },
    {
      field: 'permitType',
      title: 'Permit Type',
      width: 200,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 6,
    },
 
    {
      field: 'permitStatus',
      title: 'Permit Status',
      width: 150,
      isFixed: false,
      type: 'status',
      filterable: true,
      order: 7,
    },
    {
      field: 'internalReviewStatus',
      title: 'Internal Review Status',
      width: 150,
      isFixed: false,
      type: 'status',
      filterable: true,
      order: 8,
    },
    {
      field: 'location',
      title: 'Location',
      width: 200,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 9,
    },
  
    {
      field: 'permitAppliedDate',
      title: 'Applied Date',
      width: 130,
      isFixed: false,
      type: 'date',
      filterable: true,
      order: 10,
    },
    {
      field: 'permitExpirationDate',
      title: 'Expiration Date',
      width: 130,
      isFixed: false,
      type: 'date',
      filterable: true,
      order: 11,
    },
    {
      field: 'permitFinalDate',
      title: 'Final Date',
      width: 130,
      isFixed: false,
      type: 'date',
      filterable: true,
      order: 12,
    },
    {
      field: 'permitCompleteDate',
      title: 'Complete Date',
      width: 130,
      isFixed: false,
      type: 'date',
      filterable: true,
      order: 13,
    },
    {
      field: 'attentionReason',
      title: 'Attention Reason',
      width: 180,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 14,
    },
    {
      field: 'lastUpdatedDate',
      title: 'Updated Date',
      width: 130,
      isFixed: false,
      type: 'date',
      filterable: true,
      order: 15,
    },
  ];
public statusList:any[] = [
  { text: 'Approved', value: 'Approved' },
  { text: 'Pending', value: 'Pending' },
  { text: 'Rejected', value: 'Rejected' }
];
  // State
  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];

  public page: any = {
    size: 15,
    pageNumber: 0,
    totalElements: 0,
    totalPages: 0,
    orderBy: 'lastUpdatedDate',
    orderDir: 'desc',
  };

  public skip: number = 0;

  // Selection
  public selectedRows: any[] = [];
  public isAllSelected: boolean = false;

  // Export options
  public exportOptions = [
    { text: 'All', value: 'all' },
    { text: 'Page Results', value: 'selected' },
  ];
  columnJSONFormat: any;
  permitId: number;
  singlePermit: any;
  private resetToDefaultSettings(): void {
    console.log('Resetting to default settings...');
    
    // Reset column visibility - show all columns
    this.hiddenFields = [];
    this.gridColumns = [...this.defaultColumns];
    this.kendoColOrder = [...this.defaultColumns];

    // Reset sort state to default
    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];
    this.page.orderBy = 'lastUpdatedDate';
    this.page.orderDir = 'desc';
    
    // Reset page state
    this.page.pageNumber = 0;
    this.skip = 0;

    // Reset all filters - clear everything
    this.filter = { logic: 'and', filters: [] };
    this.activeFilters = [];

    // Reset advanced filters
    this.appliedFilters = {};

    // Reset search
    this.searchData = '';

    // Reset advanced filters visibility
    this.showAdvancedFilters = false;

    console.log('Reset completed:', {
      hiddenFields: this.hiddenFields,
      gridColumns: this.gridColumns,
      defaultColumns: this.defaultColumns,
      sort: this.sort,
      filter: this.filter,
      searchData: this.searchData
    });

    // Reset the Kendo Grid's internal state
    if (this.grid) {
      // Clear all filters
      this.grid.filter = { logic: 'and', filters: [] };
      
      // Reset sorting
      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];
      
      // Reset column visibility - show all columns
      this.grid.columns.forEach((column: any) => {
        if (column.field && column.field !== 'action') {
          column.hidden = false;
        }
      });
      
      // Reset to first page
      this.grid.skip = 0;
      this.grid.pageSize = this.page.size;
    }

    // Trigger change detection
    this.cdr.detectChanges();
    
    // Force grid refresh to apply all changes
    if (this.grid) {
      setTimeout(() => {
        this.grid.refresh();
        // Also try to reset the grid state completely
        this.grid.reset();
      }, 100);
    }
    
    // Reload data with clean state
    this.loadTable();
  }
  // private saveColumnState(): void {
  //   try {
  //     const columnState = {
  //       columns: this.gridColumns,
  //       hidden: this.hiddenFields,
  //       order: this.kendoColOrder,
  //     };
  //     localStorage.setItem(
  //       `${this.GRID_STATE_KEY}-columns`,
  //       JSON.stringify(columnState)
  //     );
  //   } catch (error) {
  //     console.warn('Error saving column state:', error);
  //   }
  // }
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private permitsService: PermitsService,
    private httpUtilService: HttpUtilsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private kendoColumnService: KendoColumnService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef,
    public appService: AppService,
    public ExceljsService: ExceljsService,
    private pageInfo: PageInfoService
  ) {
    // Initialize search subscription
    this.searchSubscription = this.searchTerms
      .pipe(debounceTime(500), distinctUntilChanged())
      .subscribe(() => {
        // Set loading state for search
        this.loading = true;
        this.isLoading = true;
        this.httpUtilService.loadingSubject.next(true);
        this.loadTable();
      });
  }

  ngOnInit(): void {
    this.pageInfo.updateTitle('Permits');
    this.initializeComponent();
    this.loadTable();
  }

  ngAfterViewInit(): void {
    this.initializeGrid();
  }

  ngOnDestroy(): void {
    if (this.searchSubscription) {
      this.searchTerms.complete();
    }
  }

  private initializeComponent(): void {
    // Get login user info
    this.loginUser = this.appService.getLoggedInUser();
    // Initialize column visibility system
    this.initializeColumnVisibility();
  }

  private initializeColumnVisibility(): void {
    // Set up column arrays first
    this.setupColumnArrays();
    
    // Try to load from local storage first
    const savedConfig = this.kendoColumnService.getFromLocalStorage('permits', this.loginUser.userId);
    
    if (savedConfig) {
      // Load saved settings from local storage
      this.kendoHide = savedConfig.hiddenData || [];
      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];
      this.kendoInitColOrder = [...this.kendoColOrder];
    } else {
      // Initialize with default values
      this.kendoHide = [];
      this.hiddenData = [];
      this.kendoColOrder = [...this.defaultColumns];
      this.kendoInitColOrder = [...this.defaultColumns];
    }
    
    // Apply settings
    this.applySavedColumnSettings();
  }

  private loadColumnSettingsFromServer(): void {
    const config = {
      pageName: 'permits',
      userID: this.loginUser.userId
    };

    this.kendoColumnService.getHideFields(config).subscribe({
      next: (response) => {
        if (response.isFault === false && response.Data) {
          // Parse the saved settings
          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];
          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];
          this.kendoInitColOrder = [...this.kendoColOrder];
          
          // Apply the settings
          this.applySavedColumnSettings();
          
          console.log('Column settings loaded from server:', {
            kendoHide: this.kendoHide,
            kendoColOrder: this.kendoColOrder
          });
        } else {
          // No saved settings, use defaults
          this.kendoHide = [];
          this.kendoColOrder = [...this.defaultColumns];
          this.kendoInitColOrder = [...this.defaultColumns];
          this.applySavedColumnSettings();
        }
      },
      error: (error) => {
        console.error('Error loading column settings:', error);
        // Use defaults on error
        this.kendoHide = [];
        this.kendoColOrder = [...this.defaultColumns];
        this.kendoInitColOrder = [...this.defaultColumns];
        this.applySavedColumnSettings();
      }
    });
  }

  private setupColumnArrays(): void {
    this.gridColumns = this.gridColumnConfig.map((col) => col.field);
    this.defaultColumns = [...this.gridColumns];
    this.fixedColumns = this.gridColumnConfig
      .filter((col) => col.isFixed)
      .map((col) => col.field);
    this.draggableColumns = this.gridColumnConfig
      .filter((col) => !col.isFixed)
      .map((col) => col.field);
  }

  private initializeGrid(): void {
    if (this.grid) {
      // Apply saved column settings
      this.applySavedColumnSettings();
    }
  }

  private applySavedColumnSettings(): void {
    if (this.kendoHide && this.kendoHide.length > 0) {
      this.hiddenFields = this.kendoHide;
    }

    if (this.kendoColOrder && this.kendoColOrder.length > 0) {
      // Apply column order
      this.gridColumnConfig.sort((a, b) => {
        const aOrder = this.kendoColOrder.indexOf(a.field);
        const bOrder = this.kendoColOrder.indexOf(b.field);
        return aOrder - bOrder;
      });
    }
  }

  // Load table data
  public loadTable(): void {
    this.loadTableWithKendoEndpoint();
  }

  // New method to load data using Kendo UI specific endpoint
  loadTableWithKendoEndpoint() {
    this.loading = true;
    this.isLoading = true;

    // Enable loader
    this.httpUtilService.loadingSubject.next(true);

    // Safety timeout to prevent loader from getting stuck
    const loadingTimeout = setTimeout(() => {
      console.warn('Loading timeout reached, resetting loading states');
      this.resetLoadingStates();
    }, 30000); // 30 seconds timeout

    // Prepare state object for Kendo UI endpoint
    const state = {
      take: this.page.size,
      skip: this.skip,
      sort: this.sort,
      filter: this.filter.filters,
      search: this.searchData,
      loggedInUserId: this.loginUser.userId,
    };
    
    console.log('Loading table with state:', state);

    this.permitsService.getPermitsForKendoGrid(state).subscribe({
      next: (data: {
        isFault?: boolean;
        responseData?: {
          data: any[];
          total: number;
          errors?: string[];
          status?: number;
        };
        data?: any[];
        total?: number;
        errors?: string[];
        status?: number;
      }) => {
        // Clear the safety timeout since we got a response
        clearTimeout(loadingTimeout);

        console.log('API Response:', data);

        // Handle the new API response structure
        if (
          data.isFault ||
          (data.responseData &&
            data.responseData.errors &&
            data.responseData.errors.length > 0)
        ) {
          const errors = data.responseData?.errors || data.errors || [];
          console.error('Kendo UI Grid errors:', errors);

          // Check if this is an authentication error
          if (data.responseData?.status === 401 || data.status === 401) {
            console.warn('Authentication error - token may be expired');
            // Don't handle empty response here, let the interceptor handle auth
            return;
          }

          this.handleEmptyResponse();
          // Always reset loading states regardless of data content
          this.loading = false;
          this.isLoading = false;
          this.httpUtilService.loadingSubject.next(false);
        } else {
          // Handle both old and new response structures
          const responseData = data.responseData || data;
          const permitData = responseData.data || [];
          const total = responseData.total || 0;

          this.IsListHasValue = permitData.length !== 0;
          this.serverSideRowData = permitData;
          this.gridData = this.serverSideRowData;
          this.page.totalElements = total;
          this.page.totalPages = Math.ceil(total / this.page.size);
          
          // Create a data source with total count for Kendo Grid
          this.gridData = {
            data: permitData,
            total: total
          };
          console.log('this.serverSideRowData ', this.serverSideRowData);
          console.log('this.gridData ', this.gridData);
          console.log('this.IsListHasValue ', this.IsListHasValue);
          console.log('this.page ', this.page);
          console.log('Total elements set to:', this.page.totalElements);
          console.log('Total pages calculated:', this.page.totalPages);
          console.log('Current skip:', this.skip, 'Page size:', this.page.size);
          console.log('Expected items range:', (this.skip + 1), '-', Math.min(this.skip + this.page.size, this.page.totalElements));
          
          // Debug grid state after data load
          setTimeout(() => {
            if (this.grid) {
              console.log('Grid total:', this.grid.total);
              console.log('Grid pageSize:', this.grid.pageSize);
              console.log('Grid skip:', this.grid.skip);
              console.log('Grid pageIndex:', this.grid.pageIndex);
              console.log('Grid data length:', this.grid.data ? this.grid.data.length : 'no data');
              
              // Force grid to update its total and maintain pagination state
              this.grid.total = this.page.totalElements;
              this.grid.skip = this.skip;
              this.grid.pageIndex = this.page.pageNumber;
              console.log('Forced grid total to:', this.grid.total, 'skip to:', this.skip, 'pageIndex to:', this.page.pageNumber);
            }
          }, 100);
          
          this.cdr.markForCheck();
          // Always reset loading states regardless of data content
          this.loading = false;
          this.isLoading = false;
          this.httpUtilService.loadingSubject.next(false);
        }
      },
      error: (error: unknown) => {
        // Clear the safety timeout since we got an error
        clearTimeout(loadingTimeout);

        console.error('Error loading data with Kendo UI endpoint:', error);

        // Check if this is an authentication error
        if (error && typeof error === 'object' && 'status' in error) {
          const httpError = error as any;
          if (httpError.status === 401) {
            console.warn('Authentication error - token may be expired');
            // Don't handle empty response here, let the interceptor handle auth
            return;
          }
        }

        this.handleEmptyResponse();
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
      },
      complete: () => {
        // Clear the safety timeout
        clearTimeout(loadingTimeout);

        // Ensure loading states are reset in complete block as well
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
      },
    });
  }

  private handleEmptyResponse(): void {
    this.IsListHasValue = false;
    this.serverSideRowData = [];
    this.gridData = [];
    this.page.totalElements = 0;
    this.page.totalPages = 0;

    // Ensure loading states are reset when handling empty response
    this.loading = false;
    this.isLoading = false;
    this.httpUtilService.loadingSubject.next(false);
  }

  // Method to manually reset loading states if they get stuck
  private resetLoadingStates(): void {
    this.loading = false;
    this.isLoading = false;
    this.httpUtilService.loadingSubject.next(false);
  }

  // Public method to manually refresh the grid and reset any stuck loading states
  public refreshGrid(): void {
    console.log('Manually refreshing grid...');
    this.resetLoadingStates();
    this.loadTable();
  }

  // Search functionality
  public onSearchKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.loadTable();
    }
  }

  public onSearchChange(): void {
    console.log('Search changed:', this.searchData);
    // Trigger search with debounce
    this.searchTerms.next(this.searchData || '');
  }

  private applySearch(): void {
    this.loadTable();
  }

  public clearSearch(): void {
    this.searchTerms.next(this.searchData);
  }

  // Filter functionality
  public filterChange(filter: CompositeFilterDescriptor): void {
    console.log('filter', filter);
    this.filter = filter;
    this.loadTable();
  }

  public applyAdvancedFilters(): void {
    console.log('yes it came here');
    // Apply status filter
    if (this.appliedFilters.status) {
      this.filter.filters = this.filter.filters.filter((f) => {
        if ('field' in f) {
          return f.field !== 'permitStatus';
        }
        return true;
      });
      this.filter.filters.push({
        field: 'permitStatus',
        operator: 'eq',
        value: this.appliedFilters.status,
      });
    }

    // Apply category filter
    if (this.appliedFilters.category) {
      this.filter.filters = this.filter.filters.filter((f) => {
        if ('field' in f) {
          return f.field !== 'permitCategory';
        }
        return true;
      });
      this.filter.filters.push({
        field: 'permitCategory',
        operator: 'eq',
        value: this.appliedFilters.category,
      });
    }

    this.loadTable();
  }

  public clearAdvancedFilters(): void {
    this.appliedFilters = {};
    this.filter.filters = [];
    this.loadTable();
  }

  // Sorting functionality
  public onSortChange(sort: SortDescriptor[]): void {
    console.log('Sort change triggered:', sort);
    
    // Handle empty sort array (normalize/unsort case)
    const incomingSort = Array.isArray(sort) ? sort : [];
    this.sort = incomingSort.length > 0
      ? incomingSort
      : [{ field: 'lastUpdatedDate', dir: 'desc' }];

    // Update page order fields for consistency
    this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';
    this.page.orderDir = this.sort[0].dir || 'desc';

    console.log('Final sort state:', this.sort);
    console.log('Page order:', { orderBy: this.page.orderBy, orderDir: this.page.orderDir });

    this.loadTable();
  }

  // Pagination functionality
  public pageChange(event: any): void {
    console.log('Page change event:', event);
    console.log('Current page size:', this.page.size);
    console.log('Event page size:', event.pageSize);
    console.log('Event page index:', event.pageIndex);
    console.log('Event skip:', event.skip);
    
    // Use Kendo's provided values as source of truth
    this.skip = event.skip;
    this.page.size = event.take || this.page.size;
    this.page.pageNumber = Math.floor(this.skip / this.page.size);
    
    console.log('Updated skip:', this.skip, 'page size:', this.page.size, 'page number:', this.page.pageNumber);
    console.log('Expected items range:', (this.skip + 1), '-', Math.min(this.skip + this.page.size, this.page.totalElements));
    
    this.loadTable();
  }

  // Handle page size change specifically
  public onPageSizeChange(event: any): void {
    console.log('Page size change event:', event);
    console.log('New page size:', event.pageSize);
    
    if (event.pageSize && event.pageSize !== this.page.size) {
      console.log('Page size changing from', this.page.size, 'to', event.pageSize);
      this.page.size = event.pageSize;
      this.page.pageNumber = 0; // Reset to first page when changing page size
      this.skip = 0;
      
      // Recalculate total pages based on new page size
      if (this.page.totalElements > 0) {
        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);
        console.log('Recalculated total pages:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);
      }
      
      // Force grid to update its page size and reset to first page
      if (this.grid) {
        this.grid.pageSize = this.page.size;
        this.grid.skip = 0;
        this.grid.pageIndex = 0;
        this.grid.total = this.page.totalElements;
        console.log('Updated grid total to:', this.grid.total);
      }
      
      console.log('Updated page size:', this.page.size, 'skip:', this.skip);
      this.loadTable();
    }
  }

  // Handle data state changes (includes page size changes)
  public onDataStateChange(event: any): void {
    console.log('Data state change event:', event);
    
    // Check if page size changed
    if (event.take && event.take !== this.page.size) {
      console.log('Page size changing via data state from', this.page.size, 'to', event.take);
      this.page.size = event.take;
      this.page.pageNumber = 0;
      this.skip = 0;
      
      // Recalculate total pages based on new page size
      if (this.page.totalElements > 0) {
        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);
        console.log('Recalculated total pages via data state:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);
      }
      
      // Force grid to update its page size and reset to first page
      if (this.grid) {
        this.grid.pageSize = this.page.size;
        this.grid.skip = 0;
        this.grid.pageIndex = 0;
        this.grid.total = this.page.totalElements;
        console.log('Updated grid total via data state to:', this.grid.total);
      }
      
      console.log('Updated page size via data state:', this.page.size, 'skip:', this.skip);
      this.loadTable();
    }
  }

  // Column management
  public onColumnReorder(event: any): void {
    // Handle column reordering
    const reorderedColumns = event.columns.map((col: any) => col.field);
    this.kendoColOrder = reorderedColumns;
  }

  public updateColumnVisibility(event: any): void {
    // Handle column visibility changes
    const hiddenColumns = event.hiddenColumns || [];
    this.hiddenFields = hiddenColumns;
  }

  // Selection functionality
  public onSelectionChange(event: any): void {
    this.selectedRows = event.selectedRows || [];
    this.isAllSelected =
      this.selectedRows.length === this.serverSideRowData.length;
  }

  public selectAll(): void {
    if (this.isAllSelected) {
      this.selectedRows = [];
      this.isAllSelected = false;
    } else {
      this.selectedRows = [...this.serverSideRowData];
      this.isAllSelected = true;
    }
  }

  // Grid expansion
  public toggleExpand(): void {
    // Find grid container element and toggle fullscreen class
    const gridContainer = document.querySelector(
      '.grid-container'
    ) as HTMLElement;
    if (gridContainer) {
      gridContainer.classList.toggle('fullscreen-grid');
      this.isExpanded = !this.isExpanded;
      // Refresh grid after resize to ensure proper rendering
      if (this.grid) {
        this.grid.refresh();
      }
    }
  }

  // Export functionality
  onExportClick(event: any) {
    const selectedOption = event.value; // Get selected option

    let prdItems: any = [];
    if (selectedOption === 'selected') {
      prdItems = this.serverSideRowData;

      // declare the title and header data for excel
      // get the data for excel in a array format
      this.exportExcel(prdItems);
    } else if (selectedOption === 'all') {
      const queryparamsExcel = {
        pageSize: this.page.totalElements,
        sortOrder: this.page.orderDir,
        sortField: this.page.orderBy,
        pageNumber: this.page.pageNumber,
        // filter: this.filterConfiguration()
      };

      // Enable loading indicator
      this.httpUtilService.loadingSubject.next(true);
      // API call
      this.permitsService
        .getAllPermits(queryparamsExcel)
        // .pipe(map((data: any) => data as any))
        .subscribe((data) => {
          // Disable loading indicator
          this.httpUtilService.loadingSubject.next(false);
          if (data.isFault) {
            this.IsListHasValue = false;
            this.cdr.markForCheck();
            return; // Exit early if the response has a fault
          }

          this.IsListHasValue = true;
          prdItems = data.responseData.data || [];

          this.cdr.detectChanges(); // Manually trigger UI update
          this.exportExcel(prdItems);
        });
    }
  }

  exportExcel(listOfItems: any): void {
    // Define local variables for the items and current date
    let prdItems: any = listOfItems;
    let currentDate: Date = this.appService.formatMonthDate(new Date());

    console.log('prdItems', prdItems);

    // Check if the data exists and is not empty
    if (prdItems !== undefined && prdItems.length > 0) {
      // Define the title for the Excel file
      const tableTitle = 'Events';

      // Filter out hidden columns and sort by order
      // const visibleColumns = this.columnJSONFormat
      //   .filter((col: any) => !col.hidden)
      //   .sort((a: any, b: any) => a.order - b.order);

      // Create header from visible columns

      const headerArray = [
        'Permit Number',
        'Permit Type',
        'Category',
        'Status',
        'Location',
        'Project Name',
        'Applied Date',

        'Expiration Date',
        'Attention Reason',
      ];
      // ...visibleColumns.map((col: any) => col.title),

      // Define which columns should have currency and percentage formatting
      // const currencyColumns: any = [
      //   'Pending',
      //   'ACAT',
      //   'Annuity',
      //   'AUM',
      //   'Total Assets',
      //   'Event Cost',
      //   'Gross Profit',
      // ].filter((col) => headerArray.includes(col));

      const percentageColumns: any = [];

      // Get the data for excel in an array format
      const respResult: any = [];

      // Prepare the data for export based on visible columns
      each(prdItems, (prdItem: any) => {
        // Create an array with the same length as headerArray
        const respData = Array(headerArray.length).fill(null);
        respData[0] = prdItem.eventDescription;
        respData[1] = this.appService.formatMonthDate(prdItem.event_date);
        // Fill in data for each visible column
        headerArray.forEach((col: any, i: number) => {
          const adjustedIndex = i; // +2 for 'Name' and 'Hot'
          switch (col) {
            case 'Permit Number':
              respData[adjustedIndex] = prdItem.permitNumber;
              break;
            case 'Permit Type':
              respData[adjustedIndex] = prdItem.permitType;
              break;
            case 'Category':
              respData[adjustedIndex] = prdItem.permitCategory;
              break;
            case 'Status':
              respData[adjustedIndex] = prdItem.permitStatus;
              break;
            case 'Location':
              respData[adjustedIndex] = prdItem.location;
              break;
            case 'Project Name':
              respData[adjustedIndex] = prdItem.projectName;
              break;
            case 'Applied Date':
              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitAppliedDate);
              break;
            case 'Expiration Date':
              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitExpirationDate);
              break;
            case 'Attention Reason':
              respData[adjustedIndex] = prdItem.attentionReason;
              break;
            // case 'kept_appointments':
            //   respData[adjustedIndex] = prdItem.kept_appointments;
            //   break;
            // case 'kept_appt_ratio':
            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;
            //   break;
            // case 'apptKeptNo':
            //   respData[adjustedIndex] = prdItem.apptKeptNo;
            //   break;
            // case 'has_assets':
            //   respData[adjustedIndex] = prdItem.has_assets;
            //   break;
            // case 'prospects_closed':
            //   respData[adjustedIndex] = prdItem.prospects_closed;
            //   break;
            // case 'closing_ratio':
            //   respData[adjustedIndex] = prdItem.closing_ratio;
            //   break;
            // case 'totalPending':
            //   respData[adjustedIndex] = prdItem.totalPending;
            //   break;
            // case 'acatproduction':
            //   respData[adjustedIndex] = prdItem.acatproduction;
            //   break;
            // case 'annuityproduction':
            //   respData[adjustedIndex] = prdItem.annuityproduction;
            //   break;
            // case 'aumproduction':
            //   respData[adjustedIndex] = prdItem.aumproduction;
            //   break;
            // case 'totalAssets':
            //   respData[adjustedIndex] = prdItem.totalAssets;
            //   break;
            // case 'eventCost':
            //   respData[adjustedIndex] = prdItem.eventCost;
            //   break;
            // case 'grossProfit':
            //   respData[adjustedIndex] = prdItem.grossProfit;
            //   break;
            // case 'status':
            //   respData[adjustedIndex] = prdItem.status;
            //   break;
          }
        });

        respResult.push(respData);
      });

      // Define column sizes for the Excel file
      const colSize = headerArray.map((header, index) => ({
        id: index + 1,
        width: 20,
      }));

      // Generate the Excel file using the exceljsService
      this.ExceljsService.generateExcel(
        tableTitle,
        headerArray,
        respResult,
        colSize
        // currencyColumns,
        // percentageColumns
      );
    } else {
      const message = 'There are no records available to export.';
      // this.layoutUtilService.showError(message, '');
    }
  }

  // public onExportClick(event: any): void {
  //   const exportType = event.item.value;
  //   let selectedIds: number[] = [];

  //   switch (exportType) {
  //     case 'selected':
  //       selectedIds = this.selectedRows.map((row) => row.permitId);
  //       if (selectedIds.length === 0) {
  //         //alert('Please select permits to export');
  //         return;
  //       }
  //       break;
  //     case 'filtered':
  //       // Export filtered data
  //       break;
  //     case 'all':
  //     default:
  //       // Export all data
  //       break;
  //   }

  //   this.exportPermits(exportType, selectedIds);
  // }

  // private exportPermits(exportType: string, selectedIds: number[]): void {
  //   this.permitsService.exportPermits(exportType, selectedIds).subscribe({
  //     next: (response: any) => {
  //       if (response.data) {
  //         const blob = new Blob([response.data], {
  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //         });
  //         saveAs(
  //           blob,
  //           `permits_${exportType}_${
  //             new Date().toISOString().split('T')[0]
  //           }.xlsx`
  //         );
  //       }
  //     },
  //     error: (error: any) => {
  //       console.error('Export error:', error);
  //       //alert('Error exporting permits data');
  //     },
  //   });
  // }

  // Column settings management
  public saveHead(): void {
    const settings = {
      kendoHide: this.hiddenFields,
      kendoColOrder: this.kendoColOrder,
      kendoInitColOrder: this.kendoInitColOrder,
    };

    // Save to local storage only
    this.kendoColumnService.saveToLocalStorage({
      pageName: 'permits',
      userID: this.loginUser.userId,
      hiddenData: settings.kendoHide,
      kendoColOrder: settings.kendoColOrder,
      LoggedId: this.loginUser.userId
    });

    console.log('Column settings saved locally:', settings);
                        this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');

    //alert('Column settings saved locally');
  }

  private saveColumnSettingsToServer(settings: any): void {
    const config = {
      pageName: 'permits',
      userID: this.loginUser.userId,
      hiddenData: settings.kendoHide,
      kendoColOrder: settings.kendoColOrder,
      LoggedId: this.loginUser.userId
    };

    this.kendoColumnService.createHideFields(config).subscribe({
      next: (response) => {
        if (response.isFault === false) {
          console.log('Column settings saved successfully:', response);
                        this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');

          //alert('Column settings saved successfully');
        } else {
          console.error('Failed to save column settings:', response.message);
                        this.customLayoutUtilsService.showError(response.message, '');

          //alert('Failed to save column settings: ' + response.message);
        }
      },
      error: (error) => {
        console.error('Error saving column settings:', error);
                        this.customLayoutUtilsService.showError('Error saving column setting', '');

        //alert('Error saving column settings. Please try again.');
      }
    });
  }

  private saveResetToServer(): void {
    // First delete existing settings
    const deleteConfig = {
      pageName: 'permits',
      userID: this.loginUser.userId
    };

    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({
      next: (response) => {
        console.log('Existing settings deleted:', response);
        // Then save the reset state (all columns visible)
        this.saveColumnSettingsToServer({
          kendoHide: [],
          kendoColOrder: this.defaultColumns,
          kendoInitColOrder: this.defaultColumns
        });
      },
      error: (error) => {
        console.error('Error deleting existing settings:', error);
        // Still try to save the reset state
        this.saveColumnSettingsToServer({
          kendoHide: [],
          kendoColOrder: this.defaultColumns,
          kendoInitColOrder: this.defaultColumns
        });
      }
    });
  }

  public resetTable(): void {
    console.log('Resetting Kendo settings for permits');
    
    // Clear all saved settings first
    this.kendoHide = [];
    this.hiddenData = [];
    this.kendoColOrder = [];
    this.kendoInitColOrder = [];
    
    // Clear local storage
    this.kendoColumnService.clearFromLocalStorage('permits');
    
    // Reset to default settings
    this.resetToDefaultSettings();
    
    // Trigger change detection to update the template
    this.cdr.detectChanges();
    
    // Force grid refresh to show all columns
    if (this.grid) {
      this.grid.refresh();
    }

    // Show success message
    console.log('Table reset to default settings');
    //alert('Table reset to default settings - all columns restored');
  }

  // Navigation
  public add(): void {
    // this.router.navigate(['/permits/add']);
    this.edit(0);
  }

  public view(permitId: number): void {
    this.router.navigate(['/permits/view', permitId], { 
      queryParams: { from: 'permit-list' } 
    });
  }

  public edit(permitId: number): void {
    if (permitId == 0) {
      const permit = this.serverSideRowData.find(
        (p) => p.permitId === permitId
      );
      const NgbModalOptions: {
        size: string;
        backdrop: boolean | 'static';
        keyboard: boolean;
        scrollable: boolean;
      } = {
        size: 'lg', // Large modal size
        backdrop: 'static', // Prevents closing when clicking outside
        keyboard: false, // Disables closing with the Escape key
        scrollable: true, // Allows scrolling inside the modal
      };

      // Open the modal and load the ProjectPopup
      const modalRef = this.modalService.open(
        PermitPopupComponent,
        NgbModalOptions
      );
      // Pass the selected ID to the modal component (0 for new, existing ID for edit)
      modalRef.componentInstance.id = permitId;
      modalRef.componentInstance.permit = permit;
      // Subscribe to the modal event when data is updated
      modalRef.componentInstance.passEntry.subscribe(
        (receivedEntry: boolean) => {
          if (receivedEntry === true) {
            // Reload the table data after a successful update
            this.loadTable();
          }
        }
      );
    } else {
      this.router.navigate(['/permits/view', permitId], { 
        queryParams: { from: 'permit-list' } 
      });
    }
  }
  deletePop(content: any) {
    this.modalService.open(content, { centered: true });
  }

  confirmDelete() {
    console.log('Item deleted ✅');
    // your delete logic here
  }

  public delete(permitId: number): void {
    if (confirm('Are you sure you want to delete this permit?')) {
      this.permitsService.deletePermit({ permitId }).subscribe({
        next: (response: any) => {
          if (response.message) {
            //alert('Permit deleted successfully');
                        this.customLayoutUtilsService.showSuccess('Permit deleted successfully', '');

            this.loadTable();
          }
        },
        error: (error: any) => {
          console.error('Delete error:', error);
                        this.customLayoutUtilsService.showError('Error deleting permit', '');

          //alert('Error deleting permit');
        },
      });
    }
  }

  // Utility methods
  public formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  }

  public getStatusClass(status: string): string {
    switch (status) {
      case 'Approved':
        return 'badge-light-success';
      case 'Requires Resubmit':
        return 'badge-light-warning';
      case 'On Hold':
        return 'badge-light-danger';
      case 'Pending':
        return 'badge-light-info';
      default:
        return 'badge-light-secondary';
    }
  }

  public getCategoryClass(category: string): string {
    return category === 'Primary'
      ? 'badge-light-primary'
      : 'badge-light-secondary';
  }

  syncPermits(i: any) {
    this.isLoading = true;
    this.singlePermit = i || false;
    this.permitsService.syncPermits({ municipalityId: 1, singlePermit: this.singlePermit, autoLogin: true }).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        console.log('Sync response:', res);
        
        // Handle wrapped response structure from interceptor
        const responseData = res?.responseData || res;
        
        if (responseData?.isFault) {
          //alert(responseData.faultMessage || 'Failed to sync permit');
                        this.customLayoutUtilsService.showError(responseData.faultMessage|| 'Failed to sync permit', '');

        } else if (responseData?.success === false) {
          // Handle specific error messages from the API
          if (responseData.message === 'Permit not found in Energov system') {
            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');
                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

          } else if (responseData.message === 'No permits found for any keywords') {
            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');
         
                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');
           } else {
                                    this.customLayoutUtilsService.showError(responseData.message ||'Failed to sync permit', '');

            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);
          }
        } else {
                        this.customLayoutUtilsService.showSuccess('✅ Permit synced successfully', '');

          //alert('✅ Permit synced successfully');
        }
        this.cdr.markForCheck();
      },
      error: (err: any) => {
        this.isLoading = false;
        // Handle HTTP error responses
        console.log('Error response:', err);
        console.log('Error type:', typeof err);
        console.log('Error keys:', Object.keys(err || {}));
        console.log('Error status:', err?.status);
        console.log('Error message:', err?.message);
        console.log('Error error:', err?.error);
        
        // The interceptor passes err.error to the error handler
        // So err might actually be the response data
        if (err?.success === false) {
          // Handle specific error messages from the API
          if (err.message === 'Permit not found in Energov system') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');
          } else if (err.message === 'No permits found for any keywords') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');
          } else {                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');


            //alert(`❌ ${err.message || 'Failed to sync permit'}`);
          }
        } else if (err?.error?.message) {
          if (err.error.message === 'Permit not found in Energov system') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');
          } else if (err.error.message === 'No permits found for any keywords') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');
          } else {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert(`❌ ${err.error.message}`)
            // ;
          }
        } else if (err?.status === 404) {
                                  this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

          // Handle 404 specifically for permit not found
          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');
        } else {
                                  this.customLayoutUtilsService.showError('❌ Error syncing permit', '');

          //alert('❌ Error syncing permit');
        }
        console.error(err);
        this.cdr.markForCheck();
      }
    });
  }

  onTabActivated() {
    // This method is called when the tab is activated
    // You can add any specific logic here if needed
    console.log('Permits tab activated');
  }
}
