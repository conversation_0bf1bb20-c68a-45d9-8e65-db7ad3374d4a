{"ast": null, "code": "import { catchError, from, mergeMap, of } from 'rxjs';\nimport { AppSettings } from 'src/app/app.settings';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PermitsService {\n  http;\n  constructor(http) {\n    this.http = http;\n  }\n  handleError(operation = 'operation', result) {\n    return error => {\n      // TODO: send the error to remote logging infrastructure\n      console.error(error); // log to console instead\n      // Let the app keep running by returning an empty result.\n      return from(result);\n    };\n  }\n  getAllPermits(queryParams) {\n    const requestBody = {\n      pageSize: queryParams.pageSize,\n      pageNumber: queryParams.pageNumber,\n      sortField: queryParams.sortField,\n      sortOrder: queryParams.sortOrder,\n      paginate: true,\n      search: queryParams.filter?.search || '',\n      columnFilter: queryParams.filter?.columnFilter || []\n    };\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllPermits`, requestBody).pipe(mergeMap(res => {\n      return of(res);\n    }), catchError(err => {\n      console.error('Error in getAllPermits:', err);\n      return of({\n        error: {\n          isFault: true\n        },\n        message: 'Error retrieving Permits'\n      });\n    }));\n  }\n  getPermitsForKendoGrid(state) {\n    const requestBody = {\n      take: state.take || 10,\n      skip: state.skip || 0,\n      sort: state.sort || [],\n      filter: state.filter || {\n        logic: 'and',\n        filters: []\n      },\n      search: state.search || '',\n      loggedInUserId: state.loggedInUserId\n    };\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsForKendoGrid`, requestBody).pipe(mergeMap(res => {\n      return of(res);\n    }), catchError(err => {\n      console.error('Error in getPermitsForKendoGrid:', err);\n      return of({\n        data: [],\n        total: 0,\n        errors: ['Error retrieving Permits']\n      });\n    }));\n  }\n  createPermit(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/createPermit', data);\n  }\n  updatePermit(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/editPermit', data);\n  }\n  checkPermitNumberExists(permitNumber, permitId) {\n    const params = {\n      permitNumber: permitNumber,\n      permitId: permitId\n    };\n    return this.http.post(AppSettings.REST_ENDPOINT + '/checkPermitNumberExists', params);\n  }\n  updatePermitInternalReviewStatus(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/updatePermitInternalReviewStatus', data);\n  }\n  getPermit(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/getPermitById', data);\n  }\n  deletePermit(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/deletePermit', data);\n  }\n  searchPermits(searchTerm) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/searchPermits`, {\n      search: searchTerm\n    });\n  }\n  exportPermits(exportType, selectedIds) {\n    const requestBody = {\n      exportType: exportType,\n      selectedIds: selectedIds || []\n    };\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/exportPermits`, requestBody);\n  }\n  getAllMunicipalities(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllMunicipalities`, params);\n  }\n  getAllReviews(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllReviews`, params);\n  }\n  syncPermits(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/syncPermits`, params);\n  }\n  // Internal Reviews Methods\n  addInternalReview(data) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/addInternalReview`, data);\n  }\n  updateInternalReview(data) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateInternalReview`, data);\n  }\n  getInternalReviews(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getInternalReviews`, params);\n  }\n  updateExternalReview(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateExternalReview`, params);\n  }\n  getPermitDetails(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitDetails`, params);\n  }\n  editNotesAndActions(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/editNotesAndActions`, params);\n  }\n  static ɵfac = function PermitsService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitsService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PermitsService,\n    factory: PermitsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["catchError", "from", "mergeMap", "of", "AppSettings", "PermitsService", "http", "constructor", "handleError", "operation", "result", "error", "console", "getAllPermits", "queryParams", "requestBody", "pageSize", "pageNumber", "sortField", "sortOrder", "paginate", "search", "filter", "columnFilter", "post", "REST_ENDPOINT", "pipe", "res", "err", "<PERSON><PERSON><PERSON>", "message", "getPermitsForKendoGrid", "state", "take", "skip", "sort", "logic", "filters", "loggedInUserId", "data", "total", "errors", "createPermit", "updatePermit", "checkPermitNumberExists", "permitNumber", "permitId", "params", "updatePermitInternalReviewStatus", "get<PERSON><PERSON><PERSON>", "deletePermit", "searchPermits", "searchTerm", "exportPermits", "exportType", "selectedIds", "getAllMunicipalities", "getAllReviews", "syncPermits", "addInternalReview", "updateInternalReview", "getInternalReviews", "updateExternalReview", "getPermitDetails", "editNotesAndActions", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\services\\permits.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable, catchError, from, mergeMap, of } from 'rxjs';\r\nimport { AppSettings } from 'src/app/app.settings';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PermitsService {\r\n\r\n  constructor(\r\n    private http: HttpClient\r\n  ) { }\r\n\r\n  private handleError<T>(operation = 'operation', result?: any) {\r\n    return (error: any): Observable<any> => {\r\n      // TODO: send the error to remote logging infrastructure\r\n      console.error(error); // log to console instead\r\n\r\n      // Let the app keep running by returning an empty result.\r\n      return from(result);\r\n    };\r\n  }\r\n\r\n  public getAllPermits(queryParams: any): Observable<any> {\r\n    const requestBody = {\r\n      pageSize: queryParams.pageSize,\r\n      pageNumber: queryParams.pageNumber,\r\n      sortField: queryParams.sortField,\r\n      sortOrder: queryParams.sortOrder,\r\n      paginate: true,\r\n      search: queryParams.filter?.search || '',\r\n      columnFilter: queryParams.filter?.columnFilter || []\r\n    };\r\n\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllPermits`, requestBody)\r\n      .pipe(\r\n        mergeMap((res: any) => {\r\n          return of(res);\r\n        }),\r\n        catchError((err: any) => {\r\n          console.error('Error in getAllPermits:', err);\r\n          return of({\r\n            error: { isFault: true },\r\n            message: 'Error retrieving Permits'\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n  public getPermitsForKendoGrid(state: any): Observable<any> {\r\n    const requestBody = {\r\n      take: state.take || 10,\r\n      skip: state.skip || 0,\r\n      sort: state.sort || [],\r\n      filter: state.filter || { logic: 'and', filters: [] },\r\n      search: state.search || '',\r\n      loggedInUserId: state.loggedInUserId\r\n    };\r\n\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsForKendoGrid`, requestBody)\r\n      .pipe(\r\n        mergeMap((res: any) => {\r\n          return of(res);\r\n        }),\r\n        catchError((err: any) => {\r\n          console.error('Error in getPermitsForKendoGrid:', err);\r\n          return of({\r\n            data: [],\r\n            total: 0,\r\n            errors: ['Error retrieving Permits']\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n  public createPermit(data: any): Observable<any> {\r\n    return this.http.post(AppSettings.REST_ENDPOINT + '/createPermit', data);\r\n  }\r\n\r\n  public updatePermit(data: any): Observable<any> {\r\n    return this.http.post(AppSettings.REST_ENDPOINT + '/editPermit', data);\r\n  }\r\n\r\n  public checkPermitNumberExists(permitNumber: string, permitId?: number): Observable<any> {\r\n    const params = { permitNumber: permitNumber, permitId: permitId };\r\n    return this.http.post(AppSettings.REST_ENDPOINT + '/checkPermitNumberExists', params);\r\n  }\r\n\r\n  public updatePermitInternalReviewStatus(data: { permitId: number; internalReviewStatus: string }): Observable<any> {\r\n    return this.http.post(AppSettings.REST_ENDPOINT + '/updatePermitInternalReviewStatus', data);\r\n  }\r\n\r\n  public getPermit(data: any): Observable<any> {\r\n    return this.http.post(AppSettings.REST_ENDPOINT + '/getPermitById', data);\r\n  }\r\n\r\n  public deletePermit(data: any): Observable<any> {\r\n    return this.http.post(AppSettings.REST_ENDPOINT + '/deletePermit', data);\r\n  }\r\n\r\n  public searchPermits(searchTerm: string): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/searchPermits`, { search: searchTerm });\r\n  }\r\n\r\n  public exportPermits(exportType: string, selectedIds?: number[]): Observable<any> {\r\n    const requestBody = {\r\n      exportType: exportType,\r\n      selectedIds: selectedIds || []\r\n    };\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/exportPermits`, requestBody);\r\n  }\r\n\r\n  public getAllMunicipalities(params: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllMunicipalities`, params);\r\n  }\r\n\r\n  public getAllReviews(params: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllReviews`, params);\r\n  }\r\n\r\n  public syncPermits(params: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/syncPermits`, params);\r\n  }\r\n\r\n  // Internal Reviews Methods\r\n  public addInternalReview(data: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/addInternalReview`, data);\r\n  }\r\n\r\n  public updateInternalReview(data: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateInternalReview`, data);\r\n  }\r\n\r\n  public getInternalReviews(params: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getInternalReviews`, params);\r\n  }\r\n  public updateExternalReview(params: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateExternalReview`, params);\r\n  }\r\n\r\n    public getPermitDetails(params: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitDetails`, params);\r\n  }\r\n\r\n   public editNotesAndActions(params: any): Observable<any> {\r\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/editNotesAndActions`, params);\r\n  }\r\n\r\n}\r\n"], "mappings": "AAEA,SAAqBA,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,EAAE,QAAQ,MAAM;AACjE,SAASC,WAAW,QAAQ,sBAAsB;;;AAKlD,OAAM,MAAOC,cAAc;EAGfC,IAAA;EADVC,YACUD,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EACV;EAEIE,WAAWA,CAAIC,SAAS,GAAG,WAAW,EAAEC,MAAY;IAC1D,OAAQC,KAAU,IAAqB;MACrC;MACAC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC;MAEtB;MACA,OAAOV,IAAI,CAACS,MAAM,CAAC;IACrB,CAAC;EACH;EAEOG,aAAaA,CAACC,WAAgB;IACnC,MAAMC,WAAW,GAAG;MAClBC,QAAQ,EAAEF,WAAW,CAACE,QAAQ;MAC9BC,UAAU,EAAEH,WAAW,CAACG,UAAU;MAClCC,SAAS,EAAEJ,WAAW,CAACI,SAAS;MAChCC,SAAS,EAAEL,WAAW,CAACK,SAAS;MAChCC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAEP,WAAW,CAACQ,MAAM,EAAED,MAAM,IAAI,EAAE;MACxCE,YAAY,EAAET,WAAW,CAACQ,MAAM,EAAEC,YAAY,IAAI;KACnD;IAED,OAAO,IAAI,CAACjB,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,gBAAgB,EAAEV,WAAW,CAAC,CAC7EW,IAAI,CACHxB,QAAQ,CAAEyB,GAAQ,IAAI;MACpB,OAAOxB,EAAE,CAACwB,GAAG,CAAC;IAChB,CAAC,CAAC,EACF3B,UAAU,CAAE4B,GAAQ,IAAI;MACtBhB,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEiB,GAAG,CAAC;MAC7C,OAAOzB,EAAE,CAAC;QACRQ,KAAK,EAAE;UAAEkB,OAAO,EAAE;QAAI,CAAE;QACxBC,OAAO,EAAE;OACV,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEOC,sBAAsBA,CAACC,KAAU;IACtC,MAAMjB,WAAW,GAAG;MAClBkB,IAAI,EAAED,KAAK,CAACC,IAAI,IAAI,EAAE;MACtBC,IAAI,EAAEF,KAAK,CAACE,IAAI,IAAI,CAAC;MACrBC,IAAI,EAAEH,KAAK,CAACG,IAAI,IAAI,EAAE;MACtBb,MAAM,EAAEU,KAAK,CAACV,MAAM,IAAI;QAAEc,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MACrDhB,MAAM,EAAEW,KAAK,CAACX,MAAM,IAAI,EAAE;MAC1BiB,cAAc,EAAEN,KAAK,CAACM;KACvB;IAED,OAAO,IAAI,CAAChC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,yBAAyB,EAAEV,WAAW,CAAC,CACtFW,IAAI,CACHxB,QAAQ,CAAEyB,GAAQ,IAAI;MACpB,OAAOxB,EAAE,CAACwB,GAAG,CAAC;IAChB,CAAC,CAAC,EACF3B,UAAU,CAAE4B,GAAQ,IAAI;MACtBhB,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEiB,GAAG,CAAC;MACtD,OAAOzB,EAAE,CAAC;QACRoC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC,0BAA0B;OACpC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEOC,YAAYA,CAACH,IAAS;IAC3B,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,eAAe,EAAEc,IAAI,CAAC;EAC1E;EAEOI,YAAYA,CAACJ,IAAS;IAC3B,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,aAAa,EAAEc,IAAI,CAAC;EACxE;EAEOK,uBAAuBA,CAACC,YAAoB,EAAEC,QAAiB;IACpE,MAAMC,MAAM,GAAG;MAAEF,YAAY,EAAEA,YAAY;MAAEC,QAAQ,EAAEA;IAAQ,CAAE;IACjE,OAAO,IAAI,CAACxC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,0BAA0B,EAAEsB,MAAM,CAAC;EACvF;EAEOC,gCAAgCA,CAACT,IAAwD;IAC9F,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,mCAAmC,EAAEc,IAAI,CAAC;EAC9F;EAEOU,SAASA,CAACV,IAAS;IACxB,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,gBAAgB,EAAEc,IAAI,CAAC;EAC3E;EAEOW,YAAYA,CAACX,IAAS;IAC3B,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,eAAe,EAAEc,IAAI,CAAC;EAC1E;EAEOY,aAAaA,CAACC,UAAkB;IACrC,OAAO,IAAI,CAAC9C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,gBAAgB,EAAE;MAAEJ,MAAM,EAAE+B;IAAU,CAAE,CAAC;EAC7F;EAEOC,aAAaA,CAACC,UAAkB,EAAEC,WAAsB;IAC7D,MAAMxC,WAAW,GAAG;MAClBuC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW,IAAI;KAC7B;IACD,OAAO,IAAI,CAACjD,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,gBAAgB,EAAEV,WAAW,CAAC;EAClF;EAEOyC,oBAAoBA,CAACT,MAAW;IACrC,OAAO,IAAI,CAACzC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,uBAAuB,EAAEsB,MAAM,CAAC;EACpF;EAEOU,aAAaA,CAACV,MAAW;IAC9B,OAAO,IAAI,CAACzC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,gBAAgB,EAAEsB,MAAM,CAAC;EAC7E;EAEOW,WAAWA,CAACX,MAAW;IAC5B,OAAO,IAAI,CAACzC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,cAAc,EAAEsB,MAAM,CAAC;EAC3E;EAEA;EACOY,iBAAiBA,CAACpB,IAAS;IAChC,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,oBAAoB,EAAEc,IAAI,CAAC;EAC/E;EAEOqB,oBAAoBA,CAACrB,IAAS;IACnC,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,uBAAuB,EAAEc,IAAI,CAAC;EAClF;EAEOsB,kBAAkBA,CAACd,MAAW;IACnC,OAAO,IAAI,CAACzC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,qBAAqB,EAAEsB,MAAM,CAAC;EAClF;EACOe,oBAAoBA,CAACf,MAAW;IACrC,OAAO,IAAI,CAACzC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,uBAAuB,EAAEsB,MAAM,CAAC;EACpF;EAESgB,gBAAgBA,CAAChB,MAAW;IACnC,OAAO,IAAI,CAACzC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,mBAAmB,EAAEsB,MAAM,CAAC;EAChF;EAEQiB,mBAAmBA,CAACjB,MAAW;IACrC,OAAO,IAAI,CAACzC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,sBAAsB,EAAEsB,MAAM,CAAC;EACnF;;qCA3IW1C,cAAc,EAAA4D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;;WAAd/D,cAAc;IAAAgE,OAAA,EAAdhE,cAAc,CAAAiE,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}