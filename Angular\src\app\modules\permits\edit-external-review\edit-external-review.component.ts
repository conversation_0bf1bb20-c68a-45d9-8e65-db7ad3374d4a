import { Component, Input } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { AppService } from '../../services/app.service';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';

@Component({
  selector: 'app-edit-external-review',
  templateUrl: './edit-external-review.component.html',
  styleUrl: './edit-external-review.component.scss'
})
export class EditExternalReviewComponent {
  @Input() permitId: number | null = null;
  @Input() reviewData: any = {}; // For edit mode
  @Input() permitDetails: any = {};
  @Input() loggedInUserId: string = 'user'; // Should be passed from parent

  reviewForm!: FormGroup;
  isLoading: boolean = false;

  constructor(
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private modalService: NgbModal,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private permitsService: PermitsService,
    private appService: AppService
  ) {}

  ngOnInit(): void {

    this.reviewForm = this.fb.group({
      // cityComments: [this.reviewData?.comments || ''],
      EORAOROwner_Response: [this.reviewData?.EORAOROwner_Response || ''],
      commentResponsedBy: [this.reviewData?.commentResponsedBy || '']
    });
  }

  private formatDateForInput(date: string | Date): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]
  }

  onSubmit(): void {
      this.isLoading = true;
      const formData = {
        // cityComments:this.reviewForm.controls.cityComments.value,
        EORAOROwner_Response:this.reviewForm.controls.EORAOROwner_Response.value,
        commentResponsedBy:this.reviewForm.controls.commentResponsedBy.value,
        permitId: this.permitId,
        commentsId:this.reviewData.commentsId,
        loggedInUserId: this.loggedInUserId
      };
        this.permitsService.updateExternalReview(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            if (res?.isFault === false) {
              //alert(res.responseData.message);
                                  this.customLayoutUtilsService.showSuccess(res.responseData.message, '');

              this.modal.close('updated');

            } else {
                                  this.customLayoutUtilsService.showError(res.responseData.message || 'Failed to update external review', '');

              //alert(res.responseData.message || 'Failed to update external review');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
                                this.customLayoutUtilsService.showError('error updating external review', '');

            //alert('Error updating external review');
            console.error(err);
          }
        });


  }
  getPdf(){
    const permitNumber = this.permitDetails?.permitNumber || '';
    const projectName = this.permitDetails?.projectName || '';
    const applicantName = this.permitDetails?.applicantName || '';
    const reviewer = (this.reviewData?.reviewer || this.reviewData?.municipalityReviewer || '').toString();
    const cityComments = (this.reviewData?.comments || (this.reviewData?.cityComments ?? '')).toString();
    const ownerResponse = (this.reviewForm?.value?.EORAOROwner_Response ?? this.reviewData?.EORAOROwner_Response ?? '').toString();
    const respondedBy = (this.reviewForm?.value?.commentResponsedBy ?? this.reviewData?.commentResponsedBy ?? '').toString();
    const cycle = (this.reviewData?.cycle || this.reviewData?.Cycle || '').toString();
    const status = (this.reviewData?.status || this.reviewData?.commentstatus || '').toString();
    const reviewCategory = (this.reviewData?.reviewCategory || '').toString();
    const dueDate = this.reviewData?.dueDate ? this.appService.formatDate(this.reviewData.dueDate) : '';
    const completedDate = this.reviewData?.completedDate ? this.appService.formatDate(this.reviewData.completedDate) : '';
    const createdDate = this.reviewData?.createdDate ? this.appService.formatDate(this.reviewData.createdDate) : '';

    const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });

    // Page metrics and margins
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = { left: 50, right: 50, top: 60, bottom: 60 };

    let currentY = margin.top;

    // Professional Header with Company Branding
    const drawHeader = () => {
      // Company Logo/Title Area
      doc.setFillColor(41, 128, 185); // Professional blue
      doc.rect(0, 0, pageWidth, 50, 'F');
      
      doc.setTextColor(255, 255, 255);
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(18);
      doc.text('PERMIT TRACKER SYSTEM', margin.left, 25);
      
      doc.setFontSize(12);
      doc.text('Review Report', pageWidth - margin.right - 80, 25);
      
      // Reset text color
      doc.setTextColor(0, 0, 0);
      
      // Permit Information Header
      currentY = 70;
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(14);
      doc.text('REVIEW DETAILS', margin.left, currentY);
      
      currentY += 20;
    };

    drawHeader();

    // Permit Information Section
    const drawPermitInfo = () => {
      const infoData = [
        ['Permit #:', permitNumber || 'N/A'],
        ['Project Name:', projectName || 'N/A'],
        ['Applicant Name:', applicantName || 'N/A'],
        ['Review Category:', reviewCategory || 'N/A'],
        ['Reviewer:', reviewer || 'N/A'],
        ['Status:', status || 'N/A'],
        ['Due Date:', dueDate || 'N/A'],
        ['Completed Date:', completedDate || 'N/A'],
        ['Created Date:', createdDate || 'N/A']
      ];

      autoTable(doc, {
        startY: currentY,
        body: infoData,
        margin: { left: margin.left, right: margin.right },
        styles: {
          font: 'helvetica',
          fontSize: 10,
          cellPadding: 8,
          overflow: 'linebreak'
        },
        columnStyles: {
          0: { cellWidth: 120, fontStyle: 'bold', fillColor: [240, 240, 240] },
          1: { cellWidth: 200 }
        },
        theme: 'grid'
      });

      currentY = (doc as any).lastAutoTable.finalY + 20;
    };

    drawPermitInfo();

    // City Comments Section
    if (cityComments) {
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(12);
      doc.setTextColor(192, 0, 0); // Red color for city comments
      doc.text('CITY COMMENTS', margin.left, currentY);
      currentY += 15;

      doc.setTextColor(0, 0, 0);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      
      // Split long text into multiple lines
      const splitText = doc.splitTextToSize(cityComments, pageWidth - margin.left - margin.right);
      doc.text(splitText, margin.left, currentY);
      currentY += splitText.length * 12 + 20;
    }

    // Owner Response Section
    if (ownerResponse) {
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(12);
      doc.text('EOR/AOR/OWNER RESPONSE', margin.left, currentY);
      currentY += 15;

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      
      const splitText = doc.splitTextToSize(ownerResponse, pageWidth - margin.left - margin.right);
      doc.text(splitText, margin.left, currentY);
      currentY += splitText.length * 12 + 20;

      // Response details
      if (respondedBy) {
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(10);
        doc.text(`Responded By: ${respondedBy}`, margin.left, currentY);
        currentY += 15;
      }
    }

    // Footer
    const drawFooter = () => {
      const pageCount = doc.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        
        // Footer line
        doc.setDrawColor(200, 200, 200);
        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);
        
        // Footer text
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);
        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);
      }
    };

    drawFooter();

    // Save the PDF
    const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${reviewCategory ? reviewCategory.replace(/[^a-zA-Z0-9]/g, '_') + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);
  }

  onCancel(): void {
    this.modal.dismiss('cancelled');
  }

  public getStatusClass(status: string): string {
    if (!status) return 'status-n-a';
    return (
      'status-' + status.toLowerCase().replace(/\s+/g, '-').replace(/\//g, '-')
    );
  }
}
