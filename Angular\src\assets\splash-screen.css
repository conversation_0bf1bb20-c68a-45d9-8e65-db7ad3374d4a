body {
  margin: 0;
  padding: 0;
}

.splash-screen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 9999 !important;
  font-family: Helvetica, 'sans-serif';
  color: #ffffff;
  line-height: 1;
  font-size: 14px;
  font-weight: 400;
}

.splash-screen span {
  color: #ffffff;
  transition: none !important;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
}

.splash-screen img {
  margin-left: calc(100vw - 100%);
  margin-bottom: 30px;
  height: 30px !important;
}

[data-bs-theme="dark"] .splash-screen {
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: #ffffff;
}

[data-bs-theme="dark"] .splash-screen span {
  color: #ffffff;
}

/* Custom spinner styles to match component loaders */
.splash-screen .loading-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  color: #ffffff !important;
}

.splash-screen .custom-colored-spinner {
  width: 3rem !important;
  height: 3rem !important;
  border: 0.25rem solid transparent !important;
  border-top: 0.25rem solid #D91F2D !important;
  border-right: 0.25rem solid #058247 !important;
  border-bottom: 0.25rem solid #184786 !important;
  border-radius: 50% !important;
  animation: custom-spin 1s linear infinite !important;
}

.splash-screen .mb-3 {
  margin-bottom: 1rem !important;
}

.splash-screen .text-primary {
  color: #ffffff !important;
}

.splash-screen .fs-5 {
  font-size: 1.25rem !important;
}

@keyframes custom-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

#root {
  opacity: 1;
  transition: opacity 1s ease-in-out;
}
