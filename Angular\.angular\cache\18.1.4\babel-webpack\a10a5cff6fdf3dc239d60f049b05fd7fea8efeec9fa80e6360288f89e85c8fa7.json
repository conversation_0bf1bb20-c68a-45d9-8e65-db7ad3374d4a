{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule, NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { PermitsComponent } from './permits.component';\nimport { PermitEditComponent } from './permit-edit/permit-edit.component';\nimport { PermitListComponent } from './permit-list/permit-list.component';\nimport { PermitViewComponent } from './permit-view/permit-view.component';\nimport { PermitsRoutingModule } from './permits-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonsModule } from '@progress/kendo-angular-buttons';\nimport { DateInputsModule } from '@progress/kendo-angular-dateinputs';\nimport { DropDownsModule } from '@progress/kendo-angular-dropdowns';\nimport { GridModule } from '@progress/kendo-angular-grid';\nimport { InputsModule } from '@progress/kendo-angular-inputs';\nimport { IntlModule } from '@progress/kendo-angular-intl';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { PermitPopupComponent } from './permit-popup/permit-popup.component';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ReviewDetailsModalComponent } from './review-details-modal/review-details-modal.component';\nimport { AddEditInternalReviewComponent } from './add-edit-internal-review/add-edit-internal-review.component';\nimport { EditExternalReviewComponent } from './edit-external-review/edit-external-review.component';\nimport { ResponseModalComponent } from './response-modal/response-modal.component';\nlet PermitsModule = class PermitsModule {};\nPermitsModule = __decorate([NgModule({\n  declarations: [PermitsComponent, PermitListComponent, PermitEditComponent, PermitPopupComponent, PermitViewComponent, ReviewDetailsModalComponent, AddEditInternalReviewComponent, EditExternalReviewComponent, ResponseModalComponent],\n  imports: [CommonModule, PermitsRoutingModule, SharedModule, FormsModule, GridModule, InputsModule, DropDownsModule, ButtonsModule, DateInputsModule, IntlModule,\n  // ✅ Required for Kendo Grid\n  InlineSVGModule, ReactiveFormsModule,\n  // ✅ Needed for [formGroup]\n  NgbModule, NgSelectModule],\n  providers: [DatePipe // ✅ Add DatePipe to providers\n  ],\n  schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]\n})], PermitsModule);\nexport { PermitsModule };", "map": {"version": 3, "names": ["NgModule", "NO_ERRORS_SCHEMA", "CUSTOM_ELEMENTS_SCHEMA", "CommonModule", "DatePipe", "PermitsComponent", "PermitEditComponent", "PermitListComponent", "PermitViewComponent", "PermitsRoutingModule", "SharedModule", "FormsModule", "ReactiveFormsModule", "ButtonsModule", "DateInputsModule", "DropDownsModule", "GridModule", "InputsModule", "IntlModule", "InlineSVGModule", "PermitPopupComponent", "NgbModule", "NgSelectModule", "ReviewDetailsModalComponent", "AddEditInternalReviewComponent", "EditExternalReviewComponent", "ResponseModalComponent", "PermitsModule", "__decorate", "declarations", "imports", "providers", "schemas"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permits.module.ts"], "sourcesContent": ["import {\r\n  NgModule,\r\n  NO_ERRORS_SCHEMA,\r\n  CUSTOM_ELEMENTS_SCHEMA,\r\n} from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { PermitsComponent } from './permits.component';\r\nimport { PermitEditComponent } from './permit-edit/permit-edit.component';\r\nimport { PermitListComponent } from './permit-list/permit-list.component';\r\nimport { PermitViewComponent } from './permit-view/permit-view.component';\r\nimport { PermitsRoutingModule } from './permits-routing.module';\r\nimport { SharedModule } from '../shared/shared.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ButtonsModule } from '@progress/kendo-angular-buttons';\r\nimport { DateInputsModule } from '@progress/kendo-angular-dateinputs';\r\nimport { DropDownsModule } from '@progress/kendo-angular-dropdowns';\r\nimport { GridModule } from '@progress/kendo-angular-grid';\r\nimport { InputsModule } from '@progress/kendo-angular-inputs';\r\nimport { IntlModule } from '@progress/kendo-angular-intl';\r\nimport { InlineSVGModule } from 'ng-inline-svg-2';\r\nimport { PermitPopupComponent } from './permit-popup/permit-popup.component';\r\nimport { NgbModal, NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\n\r\nimport { ReviewDetailsModalComponent } from './review-details-modal/review-details-modal.component';\r\nimport { AddEditInternalReviewComponent } from './add-edit-internal-review/add-edit-internal-review.component';\r\nimport { EditExternalReviewComponent } from './edit-external-review/edit-external-review.component';\r\nimport { ResponseModalComponent } from './response-modal/response-modal.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    PermitsComponent,\r\n    PermitListComponent,\r\n    PermitEditComponent,\r\n    PermitPopupComponent,\r\n    PermitViewComponent,\r\n    ReviewDetailsModalComponent,\r\n    AddEditInternalReviewComponent,\r\n    EditExternalReviewComponent,\r\n    ResponseModalComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    PermitsRoutingModule,\r\n    SharedModule,\r\n    FormsModule,\r\n    GridModule,\r\n    InputsModule,\r\n    DropDownsModule,\r\n    ButtonsModule,\r\n    DateInputsModule,\r\n    IntlModule, // ✅ Required for Kendo Grid\r\n    InlineSVGModule,\r\n    ReactiveFormsModule, // ✅ Needed for [formGroup]\r\n    NgbModule,\r\n    NgSelectModule,\r\n  ],\r\n  providers: [\r\n    DatePipe, // ✅ Add DatePipe to providers\r\n  ],\r\n  schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],\r\n})\r\nexport class PermitsModule {}\r\n"], "mappings": ";AAAA,SACEA,QAAQ,EACRC,gBAAgB,EAChBC,sBAAsB,QACjB,eAAe;AACtB,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAAmBC,SAAS,QAAQ,4BAA4B;AAChE,SAASC,cAAc,QAAQ,sBAAsB;AAErD,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,8BAA8B,QAAQ,+DAA+D;AAC9G,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,sBAAsB,QAAQ,2CAA2C;AAmC3E,IAAMC,aAAa,GAAnB,MAAMA,aAAa,GAAG;AAAhBA,aAAa,GAAAC,UAAA,EAjCzB5B,QAAQ,CAAC;EACR6B,YAAY,EAAE,CACZxB,gBAAgB,EAChBE,mBAAmB,EACnBD,mBAAmB,EACnBc,oBAAoB,EACpBZ,mBAAmB,EACnBe,2BAA2B,EAC3BC,8BAA8B,EAC9BC,2BAA2B,EAC3BC,sBAAsB,CACvB;EACDI,OAAO,EAAE,CACP3B,YAAY,EACZM,oBAAoB,EACpBC,YAAY,EACZC,WAAW,EACXK,UAAU,EACVC,YAAY,EACZF,eAAe,EACfF,aAAa,EACbC,gBAAgB,EAChBI,UAAU;EAAE;EACZC,eAAe,EACfP,mBAAmB;EAAE;EACrBS,SAAS,EACTC,cAAc,CACf;EACDS,SAAS,EAAE,CACT3B,QAAQ,CAAE;EAAA,CACX;EACD4B,OAAO,EAAE,CAAC/B,gBAAgB,EAAEC,sBAAsB;CACnD,CAAC,C,EACWyB,aAAa,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}