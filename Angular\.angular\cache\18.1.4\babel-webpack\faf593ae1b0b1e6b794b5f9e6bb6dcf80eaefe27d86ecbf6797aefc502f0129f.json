{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = [\"root\", \"\"];\nfunction AuthComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 8);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n// const BODY_CLASSES = ['bgi-size-cover', 'bgi-position-center', 'bgi-no-repeat'];\nexport class AuthComponent {\n  titleService;\n  today = new Date();\n  constructor(titleService) {\n    this.titleService = titleService;\n  }\n  ngOnInit() {\n    // BODY_CLASSES.forEach((c) => document.body.classList.add(c));\n    // Default title for auth wrapper; individual pages can override\n    this.titleService.setTitle('Permit Tracker');\n  }\n  ngOnDestroy() {\n    // BODY_CLASSES.forEach((c) => document.body.classList.remove(c));\n  }\n  static ɵfac = function AuthComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthComponent)(i0.ɵɵdirectiveInject(i1.Title));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AuthComponent,\n    selectors: [[\"body\", \"root\", \"\"]],\n    attrs: _c0,\n    decls: 5,\n    vars: 1,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"flex-column-fluid\", \"bgi-position-y-bottom\", \"position-x-center\", \"bgi-no-repeat\", \"bgi-size-contain\", \"bgi-attachment-fixed\", 2, \"background-color\", \"#d4d5d5\"], [1, \"d-flex\", \"flex-center\", \"flex-column\", \"flex-column-fluid\", \"p-10\", \"pb-lg-20\"], [1, \"w-lg-450px\", \"bg-body\", \"rounded\", \"shadow-sm\", \"p-10\", \"p-lg-15\", \"mx-auto\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"]],\n    template: function AuthComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AuthComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"router-outlet\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      }\n    },\n    dependencies: [i2.NgIf, i3.RouterOutlet],\n    styles: [\"[_nghost-%COMP%] {\\n  height: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9hdXRoL2F1dGguY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XG4gIGhlaWdodDogMTAwJTtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AuthComponent", "titleService", "today", "Date", "constructor", "ngOnInit", "setTitle", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "Title", "selectors", "attrs", "_c0", "decls", "vars", "consts", "template", "AuthComponent_Template", "rf", "ctx", "ɵɵtemplate", "AuthComponent_div_0_Template", "ɵɵelement", "ɵɵproperty", "isLoading"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\auth\\auth.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\auth\\auth.component.html"], "sourcesContent": ["import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';\nimport { Title } from '@angular/platform-browser';\n\n// const BODY_CLASSES = ['bgi-size-cover', 'bgi-position-center', 'bgi-no-repeat'];\n\n@Component({\n  // eslint-disable-next-line @angular-eslint/component-selector\n  selector: '<body[root]>',\n  templateUrl: './auth.component.html',\n  styleUrls: ['./auth.component.scss'],\n})\nexport class AuthComponent implements OnInit, OnDestroy {\n  today: Date = new Date();\n\n  constructor(private titleService: Title) {}\n\n  ngOnInit(): void {\n    // BODY_CLASSES.forEach((c) => document.body.classList.add(c));\n    // Default title for auth wrapper; individual pages can override\n    this.titleService.setTitle('Permit Tracker');\n  }\n\n  ngOnDestroy() {\n    // BODY_CLASSES.forEach((c) => document.body.classList.remove(c));\n  }\n}\n", "<!-- Global Loading Overlay for Auth Pages -->\r\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div\r\n  class=\"\r\n    d-flex\r\n    flex-column flex-column-fluid\r\n    bgi-position-y-bottom\r\n    position-x-center\r\n    bgi-no-repeat bgi-size-contain bgi-attachment-fixed\r\n  \"\r\n  style=\"background-color: #d4d5d5;\"\r\n>\r\n  <!--begin::Content-->\r\n  <div class=\"d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20\">\r\n\r\n    <!--begin::Content body-->\r\n    <div class=\"w-lg-450px bg-body rounded shadow-sm p-10 p-lg-15 mx-auto\">\r\n      <router-outlet></router-outlet>\r\n    </div>\r\n    <!--end::Content body-->\r\n  </div>\r\n  <!--end::Content-->\r\n\r\n</div>\r\n"], "mappings": ";;;;;;;ICIMA,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;ADLN;AAQA,OAAM,MAAOC,aAAa;EAGJC,YAAA;EAFpBC,KAAK,GAAS,IAAIC,IAAI,EAAE;EAExBC,YAAoBH,YAAmB;IAAnB,KAAAA,YAAY,GAAZA,YAAY;EAAU;EAE1CI,QAAQA,CAAA;IACN;IACA;IACA,IAAI,CAACJ,YAAY,CAACK,QAAQ,CAAC,gBAAgB,CAAC;EAC9C;EAEAC,WAAWA,CAAA;IACT;EAAA;;qCAZSP,aAAa,EAAAJ,EAAA,CAAAY,iBAAA,CAAAC,EAAA,CAAAC,KAAA;EAAA;;UAAbV,aAAa;IAAAW,SAAA;IAAAC,KAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCV1BvB,EAAA,CAAAyB,UAAA,IAAAC,4BAAA,iBAA0D;QAuBtD1B,EAdJ,CAAAC,cAAA,aASC,aAE6E,aAGH;QACrED,EAAA,CAAA2B,SAAA,oBAA+B;QAMrC3B,EALI,CAAAG,YAAA,EAAM,EAEF,EAGF;;;QA9BAH,EAAA,CAAA4B,UAAA,SAAAJ,GAAA,CAAAK,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}