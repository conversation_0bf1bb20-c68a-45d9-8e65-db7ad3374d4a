{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/permits.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction AddEditInternalReviewComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Internal Review - \", ctx_r0.permitNumber || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Add Review - \", ctx_r0.permitNumber || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"span\", 22);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Review category is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Type/Code/Drawing # is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Internal reviewer is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Internal verification status is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Reviewed date is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Completed date is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23)(2, \"div\", 24)(3, \"label\", 25);\n    i0.ɵɵtext(4, \"Review Category \");\n    i0.ɵɵelementStart(5, \"span\", 26);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"select\", 27)(8, \"option\", 28);\n    i0.ɵɵtext(9, \"Select Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 29);\n    i0.ɵɵtext(11, \"Building\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 30);\n    i0.ɵɵtext(13, \"Electrical\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 31);\n    i0.ɵɵtext(15, \"Mechanical\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 32);\n    i0.ɵɵtext(17, \"Plumbing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 33);\n    i0.ɵɵtext(19, \"Structural\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 34);\n    i0.ɵɵtext(21, \"Other\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, AddEditInternalReviewComponent_div_21_div_22_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 24)(24, \"label\", 25);\n    i0.ɵɵtext(25, \"Type/Code/Drawing # \");\n    i0.ɵɵelementStart(26, \"span\", 26);\n    i0.ɵɵtext(27, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(28, \"input\", 36);\n    i0.ɵɵtemplate(29, AddEditInternalReviewComponent_div_21_div_29_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 37)(31, \"div\", 24)(32, \"label\", 25);\n    i0.ɵɵtext(33, \"Internal Reviewer \");\n    i0.ɵɵelementStart(34, \"span\", 26);\n    i0.ɵɵtext(35, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(36, \"input\", 38);\n    i0.ɵɵtemplate(37, AddEditInternalReviewComponent_div_21_div_37_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 24)(39, \"label\", 25);\n    i0.ɵɵtext(40, \"Internal Verification Status \");\n    i0.ɵɵelementStart(41, \"span\", 26);\n    i0.ɵɵtext(42, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"select\", 39)(44, \"option\", 28);\n    i0.ɵɵtext(45, \"Select Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"option\");\n    i0.ɵɵtext(47, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"option\");\n    i0.ɵɵtext(49, \"In Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"option\");\n    i0.ɵɵtext(51, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"option\");\n    i0.ɵɵtext(53, \"Verified\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"option\");\n    i0.ɵɵtext(55, \"Rejected\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(56, AddEditInternalReviewComponent_div_21_div_56_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 37)(58, \"div\", 24)(59, \"label\", 25);\n    i0.ɵɵtext(60, \"Reviewed Date \");\n    i0.ɵɵelementStart(61, \"span\", 26);\n    i0.ɵɵtext(62, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(63, \"input\", 40);\n    i0.ɵɵtemplate(64, AddEditInternalReviewComponent_div_21_div_64_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 24)(66, \"label\", 25);\n    i0.ɵɵtext(67, \"Completed Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"input\", 41);\n    i0.ɵɵtemplate(69, AddEditInternalReviewComponent_div_21_div_69_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_12_0;\n    let tmp_13_0;\n    let tmp_15_0;\n    let tmp_16_0;\n    let tmp_18_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx_r0.reviewForm.get(\"reviewCategory\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.reviewForm.get(\"reviewCategory\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.reviewForm.get(\"reviewCategory\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.reviewForm.get(\"reviewCategory\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx_r0.reviewForm.get(\"typeCodeDrawing\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.reviewForm.get(\"typeCodeDrawing\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.reviewForm.get(\"typeCodeDrawing\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.reviewForm.get(\"typeCodeDrawing\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx_r0.reviewForm.get(\"internalReviewer\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r0.reviewForm.get(\"internalReviewer\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.reviewForm.get(\"internalReviewer\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r0.reviewForm.get(\"internalReviewer\")) == null ? null : tmp_9_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_10_0 = ctx_r0.reviewForm.get(\"internalVerificationStatus\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r0.reviewForm.get(\"internalVerificationStatus\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r0.reviewForm.get(\"internalVerificationStatus\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r0.reviewForm.get(\"internalVerificationStatus\")) == null ? null : tmp_12_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_13_0 = ctx_r0.reviewForm.get(\"reviewedDate\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx_r0.reviewForm.get(\"reviewedDate\")) == null ? null : tmp_13_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx_r0.reviewForm.get(\"reviewedDate\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r0.reviewForm.get(\"reviewedDate\")) == null ? null : tmp_15_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_16_0 = ctx_r0.reviewForm.get(\"completedDate\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx_r0.reviewForm.get(\"completedDate\")) == null ? null : tmp_16_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx_r0.reviewForm.get(\"completedDate\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx_r0.reviewForm.get(\"completedDate\")) == null ? null : tmp_18_0.touched));\n  }\n}\nfunction AddEditInternalReviewComponent_div_22_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Review comments is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23)(2, \"div\", 9)(3, \"label\", 25);\n    i0.ɵɵtext(4, \"Review Comments \");\n    i0.ɵɵelementStart(5, \"span\", 26);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"textarea\", 43);\n    i0.ɵɵtemplate(8, AddEditInternalReviewComponent_div_22_div_8_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 37)(10, \"div\", 9)(11, \"label\", 25);\n    i0.ɵɵtext(12, \"Non Compliance Items\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"textarea\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 37)(15, \"div\", 9)(16, \"label\", 25);\n    i0.ɵɵtext(17, \"A/E Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"textarea\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx_r0.reviewForm.get(\"reviewComments\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.reviewForm.get(\"reviewComments\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.reviewForm.get(\"reviewComments\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.reviewForm.get(\"reviewComments\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n  }\n}\nfunction AddEditInternalReviewComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.setActiveTab(\"comments\"));\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading || !ctx_r0.isDetailsValid);\n  }\n}\nfunction AddEditInternalReviewComponent_button_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 49);\n  }\n}\nfunction AddEditInternalReviewComponent_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵtemplate(1, AddEditInternalReviewComponent_button_31_span_1_Template, 1, 0, \"span\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading || !ctx_r0.isFormValid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isEdit ? \"Update\" : \"Create\", \" \");\n  }\n}\nexport let AddEditInternalReviewComponent = /*#__PURE__*/(() => {\n  class AddEditInternalReviewComponent {\n    fb;\n    modal;\n    modalService;\n    permitsService;\n    customLayoutUtilsService;\n    permitId = null;\n    reviewData = null; // For edit mode\n    loggedInUserId = 'user'; // Should be passed from parent\n    permitNumber = '';\n    reviewForm;\n    isEdit = false;\n    isLoading = false;\n    activeTab = 'details';\n    constructor(fb, modal, modalService, permitsService, customLayoutUtilsService) {\n      this.fb = fb;\n      this.modal = modal;\n      this.modalService = modalService;\n      this.permitsService = permitsService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n    }\n    ngOnInit() {\n      this.isEdit = !!this.reviewData;\n      this.reviewForm = this.fb.group({\n        reviewCategory: [this.reviewData?.reviewCategory || '', Validators.required],\n        typeCodeDrawing: [this.reviewData?.typeCodeDrawing || '', Validators.required],\n        reviewComments: [this.reviewData?.reviewComments || '', Validators.required],\n        nonComplianceItems: [this.reviewData?.nonComplianceItems || ''],\n        aeResponse: [this.reviewData?.aeResponse || ''],\n        internalReviewer: [this.reviewData?.internalReviewer || '', Validators.required],\n        internalVerificationStatus: [this.reviewData?.internalVerificationStatus || '', Validators.required],\n        reviewedDate: [this.reviewData?.reviewedDate ? this.formatDateForInput(this.reviewData.reviewedDate) : '', Validators.required],\n        completedDate: [this.reviewData?.completedDate ? this.formatDateForInput(this.reviewData.completedDate) : '']\n      });\n    }\n    setActiveTab(tab) {\n      this.activeTab = tab;\n    }\n    goToPrevious() {\n      if (this.activeTab === 'comments') {\n        this.activeTab = 'details';\n      }\n    }\n    formatDateForInput(date) {\n      if (!date) return '';\n      const d = new Date(date);\n      return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type=\"date\"]\n    }\n    onSubmit() {\n      if (this.reviewForm.valid && this.permitId) {\n        this.isLoading = true;\n        const formData = {\n          ...this.reviewForm.value,\n          permitId: this.permitId,\n          loggedInUserId: this.loggedInUserId\n        };\n        if (this.isEdit && this.reviewData?.commentsId) {\n          // Update existing review\n          formData.commentsId = this.reviewData.commentsId;\n          this.permitsService.updateInternalReview(formData).subscribe({\n            next: res => {\n              this.isLoading = false;\n              if (res?.isFault) {\n                //alert(res.faultMessage || 'Failed to update internal review');\n              } else {\n                this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n                //alert('Internal review updated successfully!');\n                this.modal.close('updated');\n              }\n            },\n            error: err => {\n              this.isLoading = false;\n              this.customLayoutUtilsService.showError('error updating internal review', '');\n              //alert('Error updating internal review');\n              console.error(err);\n            }\n          });\n        } else {\n          // Create new review\n          this.permitsService.addInternalReview(formData).subscribe({\n            next: res => {\n              this.isLoading = false;\n              if (res?.isFault) {\n                //alert(res.faultMessage || 'Failed to create internal review');\n                this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create interval review', '');\n              } else {\n                //alert('Internal review created successfully!');\n                this.customLayoutUtilsService.showSuccess('Internal review created successfully!', '');\n                this.modal.close('created');\n              }\n            },\n            error: err => {\n              this.isLoading = false;\n              this.customLayoutUtilsService.showError('error creating internal review', '');\n              //alert('Error creating internal review');\n              console.error(err);\n            }\n          });\n        }\n      } else {\n        this.reviewForm.markAllAsTouched();\n        if (!this.permitId) {\n          this.customLayoutUtilsService.showError('Permit Id is required', '');\n          //alert('Permit ID is required');\n        }\n      }\n    }\n    onCancel() {\n      this.modal.dismiss('cancelled');\n    }\n    get isFormValid() {\n      return this.reviewForm.valid;\n    }\n    get isDetailsValid() {\n      if (!this.reviewForm) {\n        return false;\n      }\n      const controls = this.reviewForm.controls;\n      return !!controls.reviewCategory?.valid && !!controls.typeCodeDrawing?.valid && !!controls.internalReviewer?.valid && !!controls.internalVerificationStatus?.valid && !!controls.reviewedDate?.valid;\n    }\n    static ɵfac = function AddEditInternalReviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddEditInternalReviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.PermitsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddEditInternalReviewComponent,\n      selectors: [[\"app-add-edit-internal-review\"]],\n      inputs: {\n        permitId: \"permitId\",\n        reviewData: \"reviewData\",\n        loggedInUserId: \"loggedInUserId\",\n        permitNumber: \"permitNumber\"\n      },\n      decls: 32,\n      vars: 16,\n      consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\", 2, \"max-height\", \"calc(100vh - 250px)\", \"overflow-y\", \"auto\", \"position\", \"relative\"], [\"class\", \"loading-overlay-inside\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"novalidate\", \"\", 3, \"ngSubmit\", \"formGroup\"], [1, \"modal-footer\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"submit\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"loading-overlay-inside\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"row\", \"mt-3\"], [1, \"col-xl-6\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"formControlName\", \"reviewCategory\", 1, \"form-select\", \"form-select-sm\", 3, \"disabled\"], [\"value\", \"\"], [\"value\", \"Building\"], [\"value\", \"Electrical\"], [\"value\", \"Mechanical\"], [\"value\", \"Plumbing\"], [\"value\", \"Structural\"], [\"value\", \"Other\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"typeCodeDrawing\", \"placeholder\", \"Enter type/code/drawing\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"row\", \"mt-4\"], [\"type\", \"text\", \"formControlName\", \"internalReviewer\", \"placeholder\", \"Enter internal reviewer\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"internalVerificationStatus\", 1, \"form-select\", \"form-select-sm\", 3, \"disabled\"], [\"type\", \"date\", \"formControlName\", \"reviewedDate\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"date\", \"formControlName\", \"completedDate\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"invalid-feedback\"], [\"formControlName\", \"reviewComments\", \"rows\", \"3\", \"placeholder\", \"Enter review comments\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"nonComplianceItems\", \"rows\", \"3\", \"placeholder\", \"Enter non compliance items\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"aeResponse\", \"rows\", \"3\", \"placeholder\", \"Enter A/E response\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function AddEditInternalReviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelementContainerStart(3);\n          i0.ɵɵtemplate(4, AddEditInternalReviewComponent_div_4_Template, 2, 1, \"div\", 3)(5, AddEditInternalReviewComponent_div_5_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n          i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_i_click_7_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 6);\n          i0.ɵɵtemplate(9, AddEditInternalReviewComponent_div_9_Template, 4, 0, \"div\", 7);\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"ul\", 11)(14, \"li\", 12)(15, \"a\", 13);\n          i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_a_click_15_listener() {\n            return ctx.setActiveTab(\"details\");\n          });\n          i0.ɵɵtext(16, \" Review Details \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"li\", 12)(18, \"a\", 13);\n          i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_a_click_18_listener() {\n            return ctx.setActiveTab(\"comments\");\n          });\n          i0.ɵɵtext(19, \" Review Comments \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(20, \"form\", 14);\n          i0.ɵɵlistener(\"ngSubmit\", function AddEditInternalReviewComponent_Template_form_ngSubmit_20_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(21, AddEditInternalReviewComponent_div_21_Template, 70, 24, \"div\", 3)(22, AddEditInternalReviewComponent_div_22_Template, 19, 6, \"div\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\")(25, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_button_click_25_listener() {\n            return ctx.goToPrevious();\n          });\n          i0.ɵɵtext(26, \" Previous \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\")(28, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function AddEditInternalReviewComponent_Template_button_click_28_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(29, \" Cancel \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(30, AddEditInternalReviewComponent_button_30_Template, 2, 1, \"button\", 18)(31, AddEditInternalReviewComponent_button_31_Template, 3, 3, \"button\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEdit);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEdit);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.activeTab === \"details\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ctx.activeTab === \"comments\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.reviewForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"details\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"comments\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.activeTab === \"details\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"details\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"comments\");\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName]\n    });\n  }\n  return AddEditInternalReviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}