{"ast": null, "code": "import { each } from 'lodash';\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/exceljs.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/http-utils.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/kendo-column.service\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"../../services/app.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@progress/kendo-angular-grid\";\nimport * as i12 from \"@progress/kendo-angular-inputs\";\nimport * as i13 from \"@progress/kendo-angular-dropdowns\";\nimport * as i14 from \"@progress/kendo-angular-buttons\";\nimport * as i15 from \"ng-inline-svg-2\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c4 = () => ({\n  filter: true\n});\nconst _c5 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c6 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction ProjectListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"span\", 11);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 12);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"kendo-textbox\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"span\", 16);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 17);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 19);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"kendo-dropdownbutton\", 22);\n    i0.ɵɵlistener(\"itemClick\", function ProjectListComponent_ng_template_4_Template_kendo_dropdownbutton_itemClick_13_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onExportClick($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(15, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(17, \"i\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"data\", ctx_r2.exportOptions);\n  }\n}\nfunction ProjectListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"label\", 31);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_5_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(8, \"i\", 35);\n    i0.ɵɵtext(9, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_5_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(11, \"i\", 37);\n    i0.ɵɵtext(12, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n  }\n}\nfunction ProjectListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProjectListComponent_ng_template_5_div_0_Template, 13, 2, \"div\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"a\", 50);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.projectId));\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 52);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_3_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      const deleteModal_r7 = i0.ɵɵreference(9);\n      return i0.ɵɵresetView(dataItem_r6.isDeletable && ctx_r2.deletePop(deleteModal_r7, dataItem_r6.projectId, dataItem_r6.projectName));\n    });\n    i0.ɵɵelement(4, \"span\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r6 = ctx.dataItem;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"invisible\", !dataItem_r6.isDeletable)(\"disabled\", !dataItem_r6.isDeletable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen027.svg\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 47);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template, 5, 5, \"ng-template\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c5));\n    i0.ɵɵproperty(\"width\", 90)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(10, _c6))(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2191\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2193\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onColumnSort(\"projectName\"));\n    });\n    i0.ɵɵtext(1, \" Project Name \");\n    i0.ɵɵtemplate(2, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_2_Template, 2, 0, \"span\", 58)(3, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_span_3_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"projectName\"] === \"asc\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"projectName\"] === \"desc\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"strong\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r9.projectName);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r10 = ctx.$implicit;\n    const column_r11 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r11)(\"filter\", filter_r10)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 54);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template, 4, 2, \"ng-template\", 55)(2, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template, 2, 1, \"ng-template\", 48)(3, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_3_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 200)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"projectName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c6))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"projectName\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2191\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2193\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onColumnSort(\"internalProjectNumber\"));\n    });\n    i0.ɵɵtext(1, \" Internal Project # \");\n    i0.ɵɵtemplate(2, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_2_Template, 2, 0, \"span\", 58)(3, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_span_3_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"internalProjectNumber\"] === \"asc\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"internalProjectNumber\"] === \"desc\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r13 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r13.internalProjectNumber, \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r14 = ctx.$implicit;\n    const column_r15 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r15)(\"filter\", filter_r14)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 61);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template, 4, 2, \"ng-template\", 55)(2, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template, 1, 1, \"ng-template\", 48)(3, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_3_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"internalProjectNumber\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"internalProjectNumber\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r16 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r16.projectStartDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r17 = ctx.$implicit;\n    const column_r18 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r18)(\"filter\", filter_r17)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 62);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template, 5, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 110)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"projectStartDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectStartDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r19 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r19.projectEndDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r20 = ctx.$implicit;\n    const column_r21 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r21)(\"filter\", filter_r20)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 63);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template, 5, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 110)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"projectEndDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectEndDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r22 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r22.projectLocation, \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r23 = ctx.$implicit;\n    const column_r24 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r24)(\"filter\", filter_r23)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 64);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"projectLocation\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectLocation\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r25 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r25.internalProjectManagerName, \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r27 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r27)(\"filter\", filter_r26)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 65);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"internalProjectManagerName\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"internalProjectManagerName\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r28 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r28.externalPMNames, \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r29 = ctx.$implicit;\n    const column_r30 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r30)(\"filter\", filter_r29)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 66);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template, 1, 1, \"ng-template\", 48)(2, ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template, 2, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 220)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"externalPMNames\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"externalPMNames\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2191\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1, \"\\u2193\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onColumnSort(\"lastUpdatedDate\"));\n    });\n    i0.ɵɵtext(1, \" Updated date \");\n    i0.ɵɵtemplate(2, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_2_Template, 2, 0, \"span\", 58)(3, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_span_3_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"lastUpdatedDate\"] === \"asc\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.columnSortStates[\"lastUpdatedDate\"] === \"desc\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r32 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r32.lastUpdatedDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 60);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r33 = ctx.$implicit;\n    const column_r34 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r34)(\"filter\", filter_r33)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 67);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template, 4, 2, \"ng-template\", 55)(2, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_2_Template, 1, 1, \"ng-template\", 48)(3, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_3_Template, 5, 3, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 110)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c6))(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_1_Template, 2, 11, \"kendo-grid-column\", 38)(2, ProjectListComponent_ng_container_6_kendo_grid_column_2_Template, 4, 8, \"kendo-grid-column\", 39)(3, ProjectListComponent_ng_container_6_kendo_grid_column_3_Template, 4, 6, \"kendo-grid-column\", 40)(4, ProjectListComponent_ng_container_6_kendo_grid_column_4_Template, 3, 6, \"kendo-grid-column\", 41)(5, ProjectListComponent_ng_container_6_kendo_grid_column_5_Template, 3, 6, \"kendo-grid-column\", 42)(6, ProjectListComponent_ng_container_6_kendo_grid_column_6_Template, 3, 6, \"kendo-grid-column\", 43)(7, ProjectListComponent_ng_container_6_kendo_grid_column_7_Template, 3, 6, \"kendo-grid-column\", 44)(8, ProjectListComponent_ng_container_6_kendo_grid_column_8_Template, 3, 6, \"kendo-grid-column\", 45)(9, ProjectListComponent_ng_container_6_kendo_grid_column_9_Template, 4, 6, \"kendo-grid-column\", 46);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r35 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"internalProjectNumber\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectStartDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectEndDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectLocation\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"internalProjectManagerName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"externalPMNames\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"lastUpdatedDate\");\n  }\n}\nfunction ProjectListComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70);\n    i0.ɵɵelement(2, \"i\", 71);\n    i0.ɵɵelementStart(3, \"p\", 16);\n    i0.ɵɵtext(4, \"No projects found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_7_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 73);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectListComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProjectListComponent_ng_template_7_div_0_Template, 8, 0, \"div\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading === false && ctx_r2.serverSideRowData.length === 0);\n  }\n}\nfunction ProjectListComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"h5\", 75);\n    i0.ɵɵtext(2, \"Confirm Delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_3_listener() {\n      const modal_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      return i0.ɵɵresetView(modal_r38.dismiss());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 77)(5, \"p\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 79)(8, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_8_listener() {\n      const modal_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      return i0.ɵɵresetView(modal_r38.dismiss());\n    });\n    i0.ɵɵtext(9, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_10_listener() {\n      const modal_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.confirmDelete();\n      return i0.ɵɵresetView(modal_r38.close());\n    });\n    i0.ɵɵtext(11, \" Delete \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Are you sure you want to delete this project? - \", ctx_r2.projectName, \" \");\n  }\n}\nexport let ProjectListComponent = /*#__PURE__*/(() => {\n  class ProjectListComponent {\n    router;\n    execeljsservice;\n    route;\n    projectsService;\n    httpUtilService;\n    customLayoutUtilsService;\n    kendoColumnService;\n    modalService;\n    cdr;\n    appService;\n    grid;\n    // Data\n    serverSideRowData = [];\n    gridData = [];\n    IsListHasValue = false;\n    loading = false;\n    isLoading = false;\n    loginUser = {};\n    // Search\n    searchData = '';\n    searchTerms = new Subject();\n    searchSubscription;\n    // Enhanced Filters for Kendo UI\n    filter = {\n      logic: 'and',\n      filters: []\n    };\n    gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    activeFilters = [];\n    filterOptions = [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }];\n    // Advanced filter options\n    advancedFilterOptions = {\n      status: [{\n        text: 'All',\n        value: null\n      }, {\n        text: 'Active',\n        value: 'Active'\n      }, {\n        text: 'Inactive',\n        value: 'Inactive'\n      }],\n      centers: []\n    };\n    // Filter state\n    showAdvancedFilters = false;\n    appliedFilters = {};\n    // Column visibility system\n    kendoHide;\n    hiddenData = [];\n    kendoColOrder = [];\n    kendoInitColOrder = [];\n    hiddenFields = [];\n    // Column configuration\n    gridColumns = [];\n    defaultColumns = [];\n    fixedColumns = [];\n    draggableColumns = [];\n    normalGrid;\n    expandedGrid;\n    isExpanded = false;\n    // Enhanced Columns with Kendo UI features\n    gridColumnConfig = [{\n      field: 'action',\n      title: 'Action',\n      width: 100,\n      isFixed: true,\n      type: 'action',\n      order: 1\n    }, {\n      field: 'projectName',\n      title: 'Project name',\n      width: 200,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2\n    }, {\n      field: 'internalProjectNumber',\n      title: 'Internal project #',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 3\n    }, {\n      field: 'projectStartDate',\n      title: 'Start date',\n      width: 110,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 4\n    }, {\n      field: 'projectEndDate',\n      title: 'End date',\n      width: 110,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5\n    }, {\n      field: 'projectLocation',\n      title: 'Location',\n      width: 150,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6\n    }, {\n      field: 'internalProjectManagerName',\n      title: 'Internal manager',\n      width: 150,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 7\n    }, {\n      field: 'externalPMNames',\n      title: 'External PM',\n      width: 220,\n      type: 'status',\n      isFixed: false,\n      filterable: true,\n      order: 8\n    }, {\n      field: 'lastUpdatedDate',\n      title: 'Updated date',\n      width: 110,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 9\n    }];\n    // State\n    sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    // Custom sort state tracking for three-state cycle\n    columnSortStates = {\n      'lastUpdatedDate': 'desc' // Default sort state\n    };\n    page = {\n      size: 15,\n      pageNumber: 0,\n      totalElements: 0,\n      totalPages: 0,\n      orderBy: 'lastUpdatedDate',\n      orderDir: 'desc'\n    };\n    skip = 0;\n    // Selection\n    selectedRows = [];\n    isAllSelected = false;\n    // Export options\n    exportOptions = [{\n      text: 'All',\n      value: 'all'\n    }, {\n      text: 'Page Results',\n      value: 'selected'\n    }\n    // { text: 'Export Filtered', value: 'filtered' },\n    ];\n    projectName;\n    projectId;\n    constructor(router, execeljsservice, route, projectsService, httpUtilService, customLayoutUtilsService, kendoColumnService, modalService, cdr, appService) {\n      this.router = router;\n      this.execeljsservice = execeljsservice;\n      this.route = route;\n      this.projectsService = projectsService;\n      this.httpUtilService = httpUtilService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.kendoColumnService = kendoColumnService;\n      this.modalService = modalService;\n      this.cdr = cdr;\n      this.appService = appService;\n      // Initialize search subscription with debounced search\n      this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n        // Set loading state immediately for search\n        this.loading = true;\n        this.isLoading = true;\n        this.httpUtilService.loadingSubject.next(true);\n        this.loadTable();\n      });\n    }\n    ngOnInit() {\n      this.initializeComponent();\n      this.loadTable();\n    }\n    ngAfterViewInit() {\n      this.initializeGrid();\n    }\n    ngOnDestroy() {\n      if (this.searchSubscription) {\n        this.searchTerms.complete();\n      }\n    }\n    initializeComponent() {\n      // Get login user info\n      // this.loginUser = this.customLayoutUtils.getLoginUser();\n      this.loginUser = this.appService.getLoggedInUser();\n      // Initialize column visibility system\n      this.initializeColumnVisibility();\n    }\n    initializeColumnVisibility() {\n      // Set up column arrays first\n      this.setupColumnArrays();\n      // Try to load from local storage first\n      const savedConfig = this.kendoColumnService.getFromLocalStorage('projects', this.loginUser.userId);\n      if (savedConfig) {\n        // Load saved settings from local storage\n        this.kendoHide = savedConfig.hiddenData || [];\n        this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.kendoColOrder];\n      } else {\n        // Initialize with default values\n        this.kendoHide = [];\n        this.hiddenData = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n      }\n      // Apply settings\n      this.applySavedColumnSettings();\n    }\n    loadColumnSettingsFromServer() {\n      const config = {\n        pageName: 'projects',\n        userID: this.loginUser.userId\n      };\n      this.kendoColumnService.getHideFields(config).subscribe({\n        next: response => {\n          if (response.isFault === false && response.Data) {\n            // Parse the saved settings\n            this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n            this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n            this.kendoInitColOrder = [...this.kendoColOrder];\n            // Apply the settings\n            this.applySavedColumnSettings();\n            console.log('Column settings loaded from server:', {\n              kendoHide: this.kendoHide,\n              kendoColOrder: this.kendoColOrder\n            });\n          } else {\n            // No saved settings, use defaults\n            this.kendoHide = [];\n            this.kendoColOrder = [...this.defaultColumns];\n            this.kendoInitColOrder = [...this.defaultColumns];\n            this.applySavedColumnSettings();\n          }\n        },\n        error: error => {\n          console.error('Error loading column settings:', error);\n          // Use defaults on error\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      });\n    }\n    setupColumnArrays() {\n      this.gridColumns = this.gridColumnConfig.map(col => col.field);\n      this.defaultColumns = [...this.gridColumns];\n      this.fixedColumns = this.gridColumnConfig.filter(col => col.isFixed).map(col => col.field);\n      this.draggableColumns = this.gridColumnConfig.filter(col => !col.isFixed).map(col => col.field);\n      console.log('Column arrays initialized:', {\n        gridColumns: this.gridColumns,\n        defaultColumns: this.defaultColumns,\n        fixedColumns: this.fixedColumns,\n        draggableColumns: this.draggableColumns\n      });\n    }\n    initializeGrid() {\n      if (this.grid) {\n        // Apply saved column settings\n        this.applySavedColumnSettings();\n      }\n    }\n    applySavedColumnSettings() {\n      if (this.kendoHide && this.kendoHide.length > 0) {\n        this.hiddenFields = this.kendoHide;\n      }\n      if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n        // Apply column order\n        this.gridColumnConfig.sort((a, b) => {\n          const aOrder = this.kendoColOrder.indexOf(a.field);\n          const bOrder = this.kendoColOrder.indexOf(b.field);\n          return aOrder - bOrder;\n        });\n      }\n    }\n    // Load table data\n    loadTable() {\n      this.loadTableWithKendoEndpoint();\n    }\n    // New method to load data using Kendo UI specific endpoint\n    loadTableWithKendoEndpoint() {\n      this.loading = true;\n      this.isLoading = true;\n      // Enable loader\n      this.httpUtilService.loadingSubject.next(true);\n      // Safety timeout to prevent loader from getting stuck\n      const loadingTimeout = setTimeout(() => {\n        console.warn('Loading timeout reached, resetting loading states');\n        this.resetLoadingStates();\n      }, 15000); // 15 seconds timeout - reduced from 30 seconds\n      // Prepare state object for Kendo UI endpoint\n      const state = {\n        take: this.page.size,\n        skip: this.skip,\n        sort: this.sort,\n        filter: this.filter.filters,\n        search: this.searchData,\n        loggedInUserId: this.loginUser.userId\n      };\n      console.log('Search request state:', {\n        searchTerm: this.searchData,\n        state: state\n      });\n      this.projectsService.getProjectsForKendoGrid(state).subscribe({\n        next: data => {\n          // Clear the safety timeout since we got a response\n          clearTimeout(loadingTimeout);\n          console.log('API Response:', data);\n          // Handle the new API response structure\n          if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n            const errors = data.responseData?.errors || data.errors || [];\n            console.error('Kendo UI Grid errors:', errors);\n            // Check if this is an authentication error\n            if (data.responseData?.status === 401 || data.status === 401) {\n              console.warn('Authentication error - token may be expired');\n              // Don't handle empty response here, let the interceptor handle auth\n              return;\n            }\n            this.handleEmptyResponse();\n            // Always reset loading states regardless of data content\n            this.loading = false;\n            this.isLoading = false;\n            this.httpUtilService.loadingSubject.next(false);\n          } else {\n            // Handle both old and new response structures\n            const responseData = data.responseData || data;\n            const projectData = responseData.data || [];\n            const total = responseData.total || 0;\n            this.IsListHasValue = projectData.length !== 0;\n            this.serverSideRowData = projectData;\n            this.page.totalElements = total;\n            this.page.totalPages = Math.ceil(total / this.page.size);\n            // Create a data source with total count for Kendo Grid\n            this.gridData = {\n              data: projectData,\n              total: total\n            };\n            console.log('this.serverSideRowData ', this.serverSideRowData);\n            console.log('this.gridData ', this.gridData);\n            console.log('this.IsListHasValue ', this.IsListHasValue);\n            console.log('this.page ', this.page);\n            this.cdr.markForCheck();\n            // Always reset loading states regardless of data content\n            this.loading = false;\n            this.isLoading = false;\n            this.httpUtilService.loadingSubject.next(false);\n          }\n        },\n        error: error => {\n          // Clear the safety timeout since we got an error\n          clearTimeout(loadingTimeout);\n          console.error('Error loading data with Kendo UI endpoint:', error);\n          // Check if this is an authentication error\n          if (error && typeof error === 'object' && 'status' in error) {\n            const httpError = error;\n            if (httpError.status === 401) {\n              console.warn('Authentication error - token may be expired');\n              // Don't handle empty response here, let the interceptor handle auth\n              return;\n            }\n          }\n          this.handleEmptyResponse();\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        },\n        complete: () => {\n          // Clear the safety timeout\n          clearTimeout(loadingTimeout);\n          // Ensure loading states are reset in complete block as well\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      });\n    }\n    handleEmptyResponse() {\n      this.IsListHasValue = false;\n      this.serverSideRowData = [];\n      this.gridData = [];\n      this.page.totalElements = 0;\n      this.page.totalPages = 0;\n      // Ensure loading states are reset when handling empty response\n      this.loading = false;\n      this.isLoading = false;\n      this.httpUtilService.loadingSubject.next(false);\n    }\n    // Method to manually reset loading states if they get stuck\n    resetLoadingStates() {\n      this.loading = false;\n      this.isLoading = false;\n      this.httpUtilService.loadingSubject.next(false);\n    }\n    // Public method to manually refresh the grid and reset any stuck loading states\n    refreshGrid() {\n      console.log('Manually refreshing grid...');\n      this.resetLoadingStates();\n      this.loadTable();\n    }\n    // Search functionality\n    onSearchKeyDown(event) {\n      if (event.key === 'Enter') {\n        console.log('Search triggered by Enter key:', this.searchData);\n        // Set loading state immediately for search\n        this.loading = true;\n        this.isLoading = true;\n        this.httpUtilService.loadingSubject.next(true);\n        this.loadTable();\n      }\n    }\n    onSearchChange() {\n      console.log('Search changed:', this.searchData);\n      // Trigger search with debounce\n      this.searchTerms.next(this.searchData);\n    }\n    applySearch() {\n      // Set loading state immediately for search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    }\n    clearSearch() {\n      // Clear search data and reset table\n      this.searchData = '';\n      // Set loading state for clearing search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      // Reload table without search filter\n      this.loadTable();\n    }\n    // Test method for External PM search\n    testExternalPMSearch() {\n      console.log('Testing External PM search...');\n      this.searchData = 'External PM'; // Test search term\n      // Set loading state immediately for test search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    }\n    // Filter functionality\n    filterChange(filter) {\n      this.filter = filter;\n      // Set loading state immediately for filtering\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    }\n    applyAdvancedFilters() {\n      // Apply status filter\n      if (this.appliedFilters.status) {\n        this.filter.filters = this.filter.filters.filter(f => {\n          if ('field' in f) {\n            return f.field !== 'projectStatus';\n          }\n          return true;\n        });\n        this.filter.filters.push({\n          field: 'projectStatus',\n          operator: 'eq',\n          value: this.appliedFilters.status\n        });\n      }\n      // Apply center filter\n      if (this.appliedFilters.center) {\n        this.filter.filters = this.filter.filters.filter(f => {\n          if ('field' in f) {\n            return f.field !== 'centerId';\n          }\n          return true;\n        });\n        this.filter.filters.push({\n          field: 'centerId',\n          operator: 'eq',\n          value: this.appliedFilters.center\n        });\n      }\n      this.loadTable();\n    }\n    clearAdvancedFilters() {\n      this.appliedFilters = {};\n      this.filter.filters = [];\n      // Set loading state immediately for clearing filters\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    }\n    clearAllFilters() {\n      this.appliedFilters = {};\n      this.filter.filters = [];\n      // Set loading state immediately for clearing filters\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    }\n    // Custom sort handler for three-state cycle\n    onColumnSort(field) {\n      console.log('Custom sort triggered for field:', field);\n      // Get current sort state for this column\n      const currentState = this.columnSortStates[field] || 'none';\n      // Determine next state in cycle: none -> asc -> desc -> none\n      let nextState;\n      let nextDir;\n      switch (currentState) {\n        case 'none':\n          nextState = 'asc';\n          nextDir = 'asc';\n          break;\n        case 'asc':\n          nextState = 'desc';\n          nextDir = 'desc';\n          break;\n        case 'desc':\n          nextState = 'none';\n          nextDir = 'desc'; // Will be overridden to default\n          break;\n      }\n      // Update column sort state\n      this.columnSortStates[field] = nextState;\n      // Clear other columns' sort states (single column sorting)\n      Object.keys(this.columnSortStates).forEach(key => {\n        if (key !== field) {\n          this.columnSortStates[key] = 'none';\n        }\n      });\n      // Set sort based on state\n      if (nextState === 'none') {\n        // Return to default sorting and clear the column sort state\n        this.sort = [{\n          field: 'lastUpdatedDate',\n          dir: 'desc'\n        }];\n        this.columnSortStates[field] = 'none'; // Ensure it's set to none\n        console.log('Returning to default sort');\n      } else {\n        // Apply the new sort\n        this.sort = [{\n          field: field,\n          dir: nextDir\n        }];\n        console.log('Applying sort:', this.sort);\n      }\n      // Update page order fields\n      this.page.orderBy = this.sort[0].field;\n      this.page.orderDir = this.sort[0].dir;\n      // Reset to first page\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      console.log('Final sort state:', this.sort);\n      console.log('Column sort states:', this.columnSortStates);\n      this.loadTable();\n    }\n    // Data state change handler (includes sorting and pagination)\n    onDataStateChange(event) {\n      console.log('Data state change:', event);\n      // Handle pagination only (sorting handled by custom method)\n      if (event.skip !== undefined) {\n        this.skip = event.skip;\n        this.page.pageNumber = Math.floor(event.skip / this.page.size);\n      }\n      if (event.take !== undefined) {\n        this.page.size = event.take;\n      }\n      this.loadTable();\n    }\n    // Sorting functionality (kept for compatibility)\n    onSortChange(sort) {\n      console.log('Sort change triggered:', sort);\n      this.sort = sort;\n      this.loadTable();\n    }\n    // Pagination functionality\n    pageChange(event) {\n      // Use Kendo's provided values as source of truth\n      this.skip = event.skip;\n      this.page.size = event.take || this.page.size;\n      this.page.pageNumber = Math.floor(this.skip / this.page.size);\n      // Set loading state immediately for pagination\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    }\n    // Column management\n    onColumnReorder(event) {\n      // Handle column reordering\n      const reorderedColumns = event.columns.map(col => col.field);\n      this.kendoColOrder = reorderedColumns;\n    }\n    updateColumnVisibility(event) {\n      // Handle column visibility changes\n      const hiddenColumns = event.hiddenColumns || [];\n      this.hiddenFields = hiddenColumns;\n      // Update gridColumns to reflect visible columns only\n      this.gridColumns = this.defaultColumns.filter(column => !hiddenColumns.includes(column));\n      console.log('Column visibility updated:', {\n        hiddenColumns: hiddenColumns,\n        visibleColumns: this.gridColumns,\n        allColumns: this.defaultColumns\n      });\n    }\n    getHiddenField(fieldName) {\n      return this.hiddenFields.includes(fieldName);\n    }\n    // Selection functionality\n    onSelectionChange(event) {\n      this.selectedRows = event.selectedRows || [];\n      this.isAllSelected = this.selectedRows.length === this.serverSideRowData.length;\n    }\n    selectAll() {\n      if (this.isAllSelected) {\n        this.selectedRows = [];\n        this.isAllSelected = false;\n      } else {\n        this.selectedRows = [...this.serverSideRowData];\n        this.isAllSelected = true;\n      }\n    }\n    // Grid expansion\n    toggleExpand() {\n      // Find grid container element and toggle fullscreen class\n      const gridContainer = document.querySelector('.grid-container');\n      if (gridContainer) {\n        gridContainer.classList.toggle('fullscreen-grid');\n        this.isExpanded = !this.isExpanded;\n        // Refresh grid after resize to ensure proper rendering\n        if (this.grid) {\n          this.grid.refresh();\n        }\n      }\n    }\n    // Export functionality\n    // public onExportClick(event: any): void {\n    //   const exportType = event.item.value;\n    //   let selectedIds: number[] = [];\n    //   switch (exportType) {\n    //     case 'selected':\n    //       selectedIds = this.selectedRows.map((row) => row.projectId);\n    //       if (selectedIds.length === 0) {\n    //         //alert('Please select projects to export');\n    //         return;\n    //       }\n    //       break;\n    //     case 'filtered':\n    //       // Export filtered data\n    //       break;\n    //     case 'all':\n    //     default:\n    //       // Export all data\n    //       break;\n    //   }\n    //   this.exportProjects(exportType, selectedIds);\n    // }\n    // private exportProjects(exportType: string, selectedIds: number[]): void {\n    //   this.projectsService.exportProjects(exportType, selectedIds).subscribe({\n    //     next: (response: any) => {\n    //       if (response.data) {\n    //         const blob = new Blob([response.data], {\n    //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    //         });\n    //         saveAs(\n    //           blob,\n    //           `projects_${exportType}_${\n    //             new Date().toISOString().split('T')[0]\n    //           }.xlsx`\n    //         );\n    //       }\n    //     },\n    //     error: (error: any) => {\n    //       console.error('Export error:', error);\n    //       //alert('Error exporting projects data');\n    //     },\n    //   });\n    // }\n    onExportClick(event) {\n      const selectedOption = event.value; // Get selected option\n      let prdItems = [];\n      if (selectedOption === 'selected') {\n        prdItems = this.serverSideRowData;\n        // declare the title and header data for excel\n        // get the data for excel in a array format\n        this.exportExcel(prdItems);\n      } else if (selectedOption === 'all') {\n        const queryparamsExcel = {\n          pageSize: this.page.totalElements,\n          sortOrder: this.page.orderDir,\n          sortField: this.page.orderBy,\n          pageNumber: this.page.pageNumber\n          // filter: this.filterConfiguration()\n        };\n        // Enable loading indicator\n        this.httpUtilService.loadingSubject.next(true);\n        // API call\n        this.projectsService.getAllProjects(queryparamsExcel)\n        // .pipe(map((data: any) => data as any))\n        .subscribe(data => {\n          // Disable loading indicator\n          this.httpUtilService.loadingSubject.next(false);\n          if (data.isFault) {\n            this.IsListHasValue = false;\n            this.cdr.markForCheck();\n            return; // Exit early if the response has a fault\n          }\n          this.IsListHasValue = true;\n          prdItems = data.responseData.data || [];\n          this.cdr.detectChanges(); // Manually trigger UI update\n          this.exportExcel(prdItems);\n        });\n      }\n    }\n    exportExcel(listOfItems) {\n      // Define local variables for the items and current date\n      let prdItems = listOfItems;\n      let currentDate = this.appService.formatMonthDate(new Date());\n      console.log('prdItems', prdItems);\n      // Check if the data exists and is not empty\n      if (prdItems !== undefined && prdItems.length > 0) {\n        // Define the title for the Excel file\n        const tableTitle = 'Events';\n        // Filter out hidden columns and sort by order\n        // const visibleColumns = this.columnJSONFormat\n        //   .filter((col: any) => !col.hidden)\n        //   .sort((a: any, b: any) => a.order - b.order);\n        // Create header from visible columns\n        const headerArray = ['Project Name', 'Internal Projet #', 'Start Date', 'End Date', 'Location', 'Manager', 'External PM'];\n        // ...visibleColumns.map((col: any) => col.title),\n        // Define which columns should have currency and percentage formatting\n        // const currencyColumns: any = [\n        //   'Pending',\n        //   'ACAT',\n        //   'Annuity',\n        //   'AUM',\n        //   'Total Assets',\n        //   'Event Cost',\n        //   'Gross Profit',\n        // ].filter((col) => headerArray.includes(col));\n        const percentageColumns = [];\n        // Get the data for excel in an array format\n        const respResult = [];\n        // Prepare the data for export based on visible columns\n        each(prdItems, prdItem => {\n          // Create an array with the same length as headerArray\n          const respData = Array(headerArray.length).fill(null);\n          respData[0] = prdItem.eventDescription;\n          respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n          // Fill in data for each visible column\n          headerArray.forEach((col, i) => {\n            const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n            switch (col) {\n              case 'Project Name':\n                respData[adjustedIndex] = prdItem.projectName;\n                break;\n              case 'Internal Projet #':\n                respData[adjustedIndex] = prdItem.internalProjectNumber;\n                break;\n              case 'Start Date':\n                respData[adjustedIndex] = this.appService.formatDate(prdItem.projectStartDate);\n                break;\n              case 'End Date':\n                respData[adjustedIndex] = this.appService.formatDate(prdItem.projectEndDate);\n                break;\n              case 'Location':\n                respData[adjustedIndex] = prdItem.projectLocation;\n                break;\n              case 'Manager':\n                respData[adjustedIndex] = prdItem.internalProjectManagerName;\n                break;\n              case 'External PM':\n                respData[adjustedIndex] = prdItem.externalPMNames;\n                break;\n              // case 'kept_appointments':\n              //   respData[adjustedIndex] = prdItem.kept_appointments;\n              //   break;\n              // case 'kept_appt_ratio':\n              //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n              //   break;\n              // case 'apptKeptNo':\n              //   respData[adjustedIndex] = prdItem.apptKeptNo;\n              //   break;\n              // case 'has_assets':\n              //   respData[adjustedIndex] = prdItem.has_assets;\n              //   break;\n              // case 'prospects_closed':\n              //   respData[adjustedIndex] = prdItem.prospects_closed;\n              //   break;\n              // case 'closing_ratio':\n              //   respData[adjustedIndex] = prdItem.closing_ratio;\n              //   break;\n              // case 'totalPending':\n              //   respData[adjustedIndex] = prdItem.totalPending;\n              //   break;\n              // case 'acatproduction':\n              //   respData[adjustedIndex] = prdItem.acatproduction;\n              //   break;\n              // case 'annuityproduction':\n              //   respData[adjustedIndex] = prdItem.annuityproduction;\n              //   break;\n              // case 'aumproduction':\n              //   respData[adjustedIndex] = prdItem.aumproduction;\n              //   break;\n              // case 'totalAssets':\n              //   respData[adjustedIndex] = prdItem.totalAssets;\n              //   break;\n              // case 'eventCost':\n              //   respData[adjustedIndex] = prdItem.eventCost;\n              //   break;\n              // case 'grossProfit':\n              //   respData[adjustedIndex] = prdItem.grossProfit;\n              //   break;\n              // case 'status':\n              //   respData[adjustedIndex] = prdItem.status;\n              //   break;\n            }\n          });\n          respResult.push(respData);\n        });\n        // Define column sizes for the Excel file\n        const colSize = headerArray.map((header, index) => ({\n          id: index + 1,\n          width: 20\n        }));\n        // Generate the Excel file using the exceljsService\n        this.execeljsservice.generateExcel(tableTitle, headerArray, respResult, colSize\n        // currencyColumns,\n        // percentageColumns\n        );\n      } else {\n        const message = 'There are no records available to export.';\n        // this.layoutUtilService.showError(message, '');\n      }\n    }\n    // Column settings management\n    saveHead() {\n      const settings = {\n        kendoHide: this.hiddenFields,\n        kendoColOrder: this.kendoColOrder,\n        kendoInitColOrder: this.kendoInitColOrder\n      };\n      // Save to local storage only\n      this.kendoColumnService.saveToLocalStorage({\n        pageName: 'projects',\n        userID: this.loginUser.userId,\n        hiddenData: settings.kendoHide,\n        kendoColOrder: settings.kendoColOrder,\n        LoggedId: this.loginUser.userId\n      });\n      console.log('Column settings saved locally:', settings);\n      this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n      //alert('Column settings saved locally');\n    }\n    saveColumnSettingsToServer(settings) {\n      const config = {\n        pageName: 'projects',\n        userID: this.loginUser.userId,\n        hiddenData: settings.kendoHide,\n        kendoColOrder: settings.kendoColOrder,\n        LoggedId: this.loginUser.userId\n      };\n      this.kendoColumnService.createHideFields(config).subscribe({\n        next: response => {\n          if (response.isFault === false) {\n            console.log('Column settings saved successfully:', response);\n            this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n            //alert('Column settings saved successfully');\n          } else {\n            console.error('Failed to save column settings:', response.message);\n            this.customLayoutUtilsService.showError('Column settings failed to save', '');\n            //alert('Failed to save column settings: ' + response.message);\n          }\n        },\n        error: error => {\n          console.error('Error saving column settings:', error);\n          this.customLayoutUtilsService.showError('Error saving column settings', '');\n          //alert('Error saving column settings. Please try again.');\n        }\n      });\n    }\n    saveResetToServer() {\n      // First delete existing settings\n      const deleteConfig = {\n        pageName: 'projects',\n        userID: this.loginUser.userId\n      };\n      this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n        next: response => {\n          console.log('Existing settings deleted:', response);\n          // Then save the reset state (all columns visible)\n          this.saveColumnSettingsToServer({\n            kendoHide: [],\n            kendoColOrder: this.defaultColumns,\n            kendoInitColOrder: this.defaultColumns\n          });\n        },\n        error: error => {\n          console.error('Error deleting existing settings:', error);\n          // Still try to save the reset state\n          this.saveColumnSettingsToServer({\n            kendoHide: [],\n            kendoColOrder: this.defaultColumns,\n            kendoInitColOrder: this.defaultColumns\n          });\n        }\n      });\n    }\n    resetTable() {\n      console.log('Resetting Kendo settings for projects');\n      // Clear all saved settings first\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [];\n      this.kendoInitColOrder = [];\n      // Clear local storage\n      this.kendoColumnService.clearFromLocalStorage('projects');\n      // Reset to default settings\n      this.resetToDefaultSettings();\n      // Trigger change detection to update the template\n      this.cdr.detectChanges();\n      // Force grid refresh to show all columns\n      if (this.grid) {\n        this.grid.refresh();\n      }\n      // Show success message\n      console.log('Table reset to default settings');\n      //alert('Table reset to default settings - all columns restored');\n    }\n    resetToDefaultSettings() {\n      console.log('Resetting to default settings...');\n      // Reset column visibility - show all columns\n      this.hiddenFields = [];\n      this.gridColumns = [...this.defaultColumns];\n      this.kendoColOrder = [...this.defaultColumns];\n      // Reset sort state to default\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n      // Reset page state\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Reset all filters - clear everything\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.activeFilters = [];\n      // Reset advanced filters\n      this.appliedFilters = {};\n      // Reset search\n      this.searchData = '';\n      // Reset advanced filters visibility\n      this.showAdvancedFilters = false;\n      console.log('Reset completed:', {\n        hiddenFields: this.hiddenFields,\n        gridColumns: this.gridColumns,\n        defaultColumns: this.defaultColumns,\n        sort: this.sort,\n        filter: this.filter,\n        searchData: this.searchData\n      });\n      // Reset the Kendo Grid's internal state\n      if (this.grid) {\n        // Clear all filters\n        this.grid.filter = {\n          logic: 'and',\n          filters: []\n        };\n        // Reset sorting\n        this.grid.sort = [{\n          field: 'lastUpdatedDate',\n          dir: 'desc'\n        }];\n        // Reset column visibility - show all columns\n        this.grid.columns.forEach(column => {\n          if (column.field && column.field !== 'action') {\n            column.hidden = false;\n          }\n        });\n        // Reset to first page\n        this.grid.skip = 0;\n        this.grid.pageSize = this.page.size;\n      }\n      // Trigger change detection\n      this.cdr.detectChanges();\n      // Force grid refresh to apply all changes\n      if (this.grid) {\n        setTimeout(() => {\n          this.grid.refresh();\n          // Also try to reset the grid state completely\n          this.grid.reset();\n        }, 100);\n      }\n      // Reload data with clean state\n      this.loadTable();\n    }\n    // Navigation\n    add() {\n      this.edit(0);\n    }\n    view(projectId) {\n      this.router.navigate(['/projects/view', projectId]);\n    }\n    edit(projectId) {\n      if (projectId == 0) {\n        // Open modal for new project\n        const modalRef = this.modalService.open(ProjectPopupComponent, {\n          size: 'lg',\n          centered: true,\n          backdrop: 'static'\n        });\n        modalRef.componentInstance.id = projectId;\n        modalRef.componentInstance.project = null;\n        modalRef.componentInstance.passEntry.subscribe(result => {\n          if (result) {\n            // Refresh the grid after successful add\n            this.loadTable();\n          }\n        });\n      } else {\n        // Navigate to project view for existing projects\n        this.router.navigate(['/projects/view', projectId]);\n      }\n    }\n    delete(projectId) {\n      // if (confirm('Are you sure you want to delete this project?')) {\n      // }\n      this.projectsService.deleteProject({\n        projectId\n      }).subscribe({\n        next: response => {\n          console.log(\"response\", response);\n          if (!response.isFault) {\n            this.customLayoutUtilsService.showSuccess('Project deleted successfully', '');\n            //alert('Project deleted successfully');\n            this.loadTable();\n          }\n        },\n        error: error => {\n          console.error('Delete error:', error);\n          this.customLayoutUtilsService.showError('error deleting project', '');\n          //alert('Error deleting project');\n        }\n      });\n    }\n    // Utility methods\n    getProjectFullName(project) {\n      return `${project.projectFirstName || ''} ${project.projectLastName || ''}`.trim();\n    }\n    getCenterName(project) {\n      return project.medicalCenter?.centerName || '';\n    }\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const day = date.getDate().toString().padStart(2, '0');\n      const year = date.getFullYear();\n      return `${month}/${day}/${year}`;\n    }\n    getStatusClass(status) {\n      return status === 'Active' ? 'badge-light-success' : 'badge-light-danger';\n    }\n    deletePop(content, projectId, projectName) {\n      this.projectName = projectName;\n      this.projectId = projectId;\n      this.modalService.open(content, {\n        centered: true\n      });\n    }\n    confirmDelete() {\n      // console.log('Item deleted ✅');\n      this.delete(this.projectId);\n      // your delete logic here\n    }\n    onTabActivated() {\n      // This method is called when the tab is activated\n      // You can add any specific logic here if needed\n      console.log('Projects tab activated');\n    }\n    static ɵfac = function ProjectListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ExceljsService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.HttpUtilsService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.KendoColumnService), i0.ɵɵdirectiveInject(i7.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i8.AppService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectListComponent,\n      selectors: [[\"app-project-list\"]],\n      viewQuery: function ProjectListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      decls: 10,\n      vars: 22,\n      consts: [[\"normalGrid\", \"\"], [\"deleteModal\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"dataStateChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"total\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\", \"loading\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"text\", \"Export Excel\", \"iconClass\", \"fas fa-file-excel\", \"title\", \"Export\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"itemClick\", \"data\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"projectName\", \"title\", \"Project name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"internalProjectNumber\", \"title\", \"Internal Project #\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectStartDate\", \"title\", \"Start Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectEndDate\", \"title\", \"End Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectLocation\", \"title\", \"Location\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"internalProjectManagerName\", \"title\", \"Internal Manager\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"externalPMNames\", \"title\", \"External PM\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", 2, \"min-height\", \"32px\"], [\"title\", \"View\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"32px\", \"height\", \"32px\", 3, \"click\"], [1, \"fas\", \"fa-pencil\", 2, \"color\", \"var(--bs-primary, #0d6efd)\", \"font-size\", \"1rem\"], [\"title\", \"Delete\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"32px\", \"height\", \"32px\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", 3, \"inlineSVG\"], [\"field\", \"projectName\", \"title\", \"Project name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridHeaderTemplate\", \"\"], [\"kendoGridFilterMenuTemplate\", \"\"], [2, \"cursor\", \"pointer\", \"user-select\", \"none\", 3, \"click\"], [\"style\", \"color: red; font-weight: bold;\", 4, \"ngIf\"], [2, \"color\", \"red\", \"font-weight\", \"bold\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"internalProjectNumber\", \"title\", \"Internal Project #\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectStartDate\", \"title\", \"Start Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectEndDate\", \"title\", \"End Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectLocation\", \"title\", \"Location\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"internalProjectManagerName\", \"title\", \"Internal Manager\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"externalPMNames\", \"title\", \"External PM\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"fas\", \"fa-folder-open\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"], [1, \"modal-header\", \"bg-danger\", \"text-white\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"delete-modal-body\", \"mt-4\", \"text-center\"], [1, \"fs-5\"], [1, \"modal-footer\", \"delete-modal-footer\", \"ms-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"]],\n      template: function ProjectListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, ProjectListComponent_div_0_Template, 7, 0, \"div\", 2);\n          i0.ɵɵelementStart(1, \"div\", 3)(2, \"kendo-grid\", 4, 0);\n          i0.ɵɵlistener(\"columnReorder\", function ProjectListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          })(\"selectionChange\", function ProjectListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChange($event));\n          })(\"filterChange\", function ProjectListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterChange($event));\n          })(\"pageChange\", function ProjectListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChange($event));\n          })(\"dataStateChange\", function ProjectListComponent_Template_kendo_grid_dataStateChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDataStateChange($event));\n          })(\"columnVisibilityChange\", function ProjectListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n          });\n          i0.ɵɵtemplate(4, ProjectListComponent_ng_template_4_Template, 18, 11, \"ng-template\", 5)(5, ProjectListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 5)(6, ProjectListComponent_ng_container_6_Template, 10, 9, \"ng-container\", 6)(7, ProjectListComponent_ng_template_7_Template, 1, 1, \"ng-template\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, ProjectListComponent_ng_template_8_Template, 12, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.gridData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(18, _c2, i0.ɵɵpureFunction0(17, _c1)))(\"total\", ctx.page.totalElements)(\"sortable\", false)(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(20, _c3))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(21, _c4))(\"loading\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n        }\n      },\n      dependencies: [i9.NgForOf, i9.NgIf, i10.NgControlStatus, i10.NgModel, i11.GridComponent, i11.ToolbarTemplateDirective, i11.GridSpacerComponent, i11.ColumnComponent, i11.CellTemplateDirective, i11.NoRecordsTemplateDirective, i11.HeaderTemplateDirective, i11.ContainsFilterOperatorComponent, i11.EqualFilterOperatorComponent, i11.NotEqualFilterOperatorComponent, i11.GreaterOrEqualToFilterOperatorComponent, i11.LessOrEqualToFilterOperatorComponent, i11.StringFilterMenuComponent, i11.FilterMenuTemplateDirective, i11.DateFilterMenuComponent, i12.TextBoxComponent, i13.DropDownListComponent, i14.ButtonComponent, i14.DropDownButtonComponent, i15.InlineSVGDirective],\n      styles: [\".grid-container[_ngcontent-%COMP%]{padding:20px;display:flex;flex-direction:column;height:100%;position:relative}.grid-container.fullscreen-grid[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;background:#fff;padding:20px;overflow:auto}.modal-body[_ngcontent-%COMP%]{min-height:250px}.delete-modal-footer[_ngcontent-%COMP%]{min-height:10px}.delete-modal-body[_ngcontent-%COMP%]{min-height:52px}.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{border-radius:.375rem;padding:.5rem .75rem;width:80%;border:2px solid #646367;box-shadow:0 0 6px #393a3a80}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{border-radius:.375rem;padding:.75rem 1.25rem;min-width:120px;background-color:#4c4e4f;color:#fff;font-weight:500;transition:background .3s,transform .2s}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{background-color:#4c4e4f;transform:scale(1.05)}.advanced-filters-panel[_ngcontent-%COMP%]{border-radius:8px;margin-bottom:1rem}.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-weight:600;color:#495057;margin-bottom:.5rem}.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]{width:100%;border-radius:6px}[_nghost-%COMP%]     .k-grid{border-radius:8px;box-shadow:0 2px 10px #0000001a}[_nghost-%COMP%]     .k-grid .k-grid-header{background:#f8f9fa;border-bottom:2px solid #dee2e6}[_nghost-%COMP%]     .k-grid .k-grid-header .k-header{background:#f8f9fa;border-color:#dee2e6;font-weight:600;color:#495057}[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover{background:#e9ecef}[_nghost-%COMP%]     .k-grid .k-grid-toolbar{background:#f8f9fa;border-bottom:1px solid #dee2e6;padding:1rem}[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button{border-radius:6px;font-weight:500}[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary{background:#007bff;border-color:#007bff}[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary:hover{background:#0056b3;border-color:#0056b3}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover{background:#f8f9fa}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt{background:#f8f9fa}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt:hover{background:#e9ecef}[_nghost-%COMP%]     .k-grid .k-pager{background:#f8f9fa;border-top:1px solid #dee2e6}[_nghost-%COMP%]     .k-grid .k-pager .k-pager-info{color:#6c757d}[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link{border-radius:4px}[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link:hover{background:#e9ecef}[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected{background:#007bff;color:#fff}[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button{border-radius:.375rem!important;font-weight:500!important;font-size:.75rem!important;padding:.25rem .5rem!important;background-color:#6c757d!important;border-color:#6c757d!important;color:#fff!important;margin-right:.5rem!important;transition:all .2s ease!important;height:auto!important;min-height:31px!important}[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button:hover{background-color:#5a6268!important;border-color:#545b62!important;transform:translateY(-1px)!important;box-shadow:0 2px 8px #00000026!important}[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button:focus{box-shadow:0 0 0 .2rem #6c757d80!important}[_nghost-%COMP%]     .k-textbox{border-radius:6px}[_nghost-%COMP%]     .k-textbox:focus{box-shadow:0 0 0 .2rem #007bff40}[_nghost-%COMP%]     .k-dropdownlist{border-radius:6px}[_nghost-%COMP%]     .k-dropdownlist:focus{box-shadow:0 0 0 .2rem #007bff40}.badge[_ngcontent-%COMP%]{padding:.5em .75em;font-size:.75em;font-weight:600;border-radius:6px}.badge.badge-light-success[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.badge.badge-light-warning[_ngcontent-%COMP%]{background:#fff3cd;color:#856404}.badge.badge-light-danger[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.badge.badge-light-info[_ngcontent-%COMP%]{background:#d1ecf1;color:#0c5460}.badge.badge-light-secondary[_ngcontent-%COMP%]{background:#e2e3e5;color:#383d41}.badge.badge-light-primary[_ngcontent-%COMP%]{background:#cce7ff;color:#004085}.btn[_ngcontent-%COMP%]{border-radius:6px;font-weight:500;transition:all .2s ease}.btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 8px #00000026}.btn.btn-success[_ngcontent-%COMP%]{background:#28a745;border-color:#28a745}.btn.btn-success[_ngcontent-%COMP%]:hover{background:#1e7e34;border-color:#1e7e34}.btn.btn-warning[_ngcontent-%COMP%]{background:#ffc107;border-color:#ffc107;color:#212529}.btn.btn-warning[_ngcontent-%COMP%]:hover{background:#e0a800;border-color:#d39e00}.btn.btn-info[_ngcontent-%COMP%]{background:#17a2b8;border-color:#17a2b8}.btn.btn-info[_ngcontent-%COMP%]:hover{background:#138496;border-color:#138496}.btn.btn-outline-secondary[_ngcontent-%COMP%]{color:#6c757d;border-color:#6c757d}.btn.btn-outline-secondary[_ngcontent-%COMP%]:hover{background:#6c757d;color:#fff}.btn-icon[_ngcontent-%COMP%]{width:32px;height:32px;padding:0;display:inline-flex;align-items:center;justify-content:center;border-radius:6px;transition:all .2s ease}.btn-icon[_ngcontent-%COMP%]:hover{transform:scale(1.1)}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action]{padding:4px 8px;vertical-align:middle}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex{min-height:32px;align-items:center;justify-content:center}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon{flex-shrink:0;margin:0 2px}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon.invisible{visibility:hidden;pointer-events:none}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon.disabled{opacity:.5;cursor:not-allowed;pointer-events:none}.text-muted[_ngcontent-%COMP%]   .fas[_ngcontent-%COMP%]{color:#6c757d}.text-muted[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#495057;margin-top:1rem}.text-muted[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:1.5rem}@media (max-width: 768px){.grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]{width:100%}.grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{width:100%!important}.grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{justify-content:center}.advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%]{margin-bottom:1rem}}.grid-container[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.grid-container.fullscreen-grid[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_expandGrid .3s ease-out}@keyframes _ngcontent-%COMP%_expandGrid{0%{opacity:.8;transform:scale(.95)}to{opacity:1;transform:scale(1)}}\"]\n    });\n  }\n  return ProjectListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}