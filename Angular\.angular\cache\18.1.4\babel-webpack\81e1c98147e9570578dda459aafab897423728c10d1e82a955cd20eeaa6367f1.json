{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport { ReviewDetailsModalComponent } from '../review-details-modal/review-details-modal.component';\nimport { ResponseModalComponent } from '../response-modal/response-modal.component';\nimport { AddEditInternalReviewComponent } from '../add-edit-internal-review/add-edit-internal-review.component';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport { EditExternalReviewComponent } from '../edit-external-review/edit-external-review.component';\nimport { AppService } from '../../services/app.service';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport { autoTable } from 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../../services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/permits.service\";\nimport * as i7 from \"../../services/http-utils.service\";\nimport * as i8 from \"@angular/common\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 8);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_li_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_li_20_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showTab(\"internal\", $event));\n    });\n    i0.ɵɵtext(2, \" Internal Reviews \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r1.selectedTab === \"internal\"));\n  }\n}\nfunction PermitViewComponent_div_2_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editPermit());\n    });\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitViewComponent_div_2_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_26_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadInternalReviewsPdf());\n    });\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_26_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addPopUp());\n    });\n    i0.ɵɵelement(4, \"i\", 35);\n    i0.ɵɵtext(5, \"Add Review \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.auditEntries.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_27_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleAllSubmittals());\n    });\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"span\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_27_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.syncPermits(true));\n    });\n    i0.ɵɵelement(6, \"i\", 40);\n    i0.ɵɵtext(7, \" Sync \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_27_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPortal());\n    });\n    i0.ɵɵelement(9, \"i\", 42);\n    i0.ɵɵtext(10, \" Portal \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.externalSubmittals.length === 0)(\"title\", ctx_r1.areAllSubmittalsExpanded() ? \"Collapse all submittals\" : \"Expand all submittals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.areAllSubmittalsExpanded() ? \"fa-compress-arrows-alt\" : \"fa-expand-arrows-alt\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.areAllSubmittalsExpanded() ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r1.permit == null ? null : ctx_r1.permit.permitEntityID))(\"title\", (ctx_r1.permit == null ? null : ctx_r1.permit.permitEntityID) ? \"Open Portal\" : \"Portal not available - Permit Entity ID required\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 43)(2, \"div\", 44)(3, \"div\", 45)(4, \"label\");\n    i0.ɵɵtext(5, \"Project Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 45)(9, \"label\");\n    i0.ɵɵtext(10, \"Permit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 46);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 45)(14, \"label\");\n    i0.ɵɵtext(15, \"Primary Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 46);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 45)(19, \"label\");\n    i0.ɵɵtext(20, \"Permit Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 47);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 45)(24, \"label\");\n    i0.ɵɵtext(25, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 46);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 45)(29, \"label\");\n    i0.ɵɵtext(30, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 46);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 45)(34, \"label\");\n    i0.ɵɵtext(35, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 46);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\", 48);\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 45)(43, \"label\");\n    i0.ɵɵtext(44, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"span\", 46);\n    i0.ɵɵtext(46);\n    i0.ɵɵpipe(47, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 45)(49, \"label\");\n    i0.ɵɵtext(50, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 46);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 45)(55, \"label\");\n    i0.ɵɵtext(56, \"Complete Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 46);\n    i0.ɵɵtext(58);\n    i0.ɵɵpipe(59, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 45)(61, \"label\");\n    i0.ɵɵtext(62, \"internal Review Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"span\", 47);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(65, \"div\", 49)(66, \"div\", 10)(67, \"h4\");\n    i0.ɵɵtext(68, \"Notes / Action\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_Template_button_click_69_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      const notesActionsTemplate_r8 = i0.ɵɵreference(4);\n      return i0.ɵɵresetView(ctx_r1.onEdit(notesActionsTemplate_r8));\n    });\n    i0.ɵɵelement(70, \"i\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 43)(72, \"div\", 51)(73, \"div\", 52)(74, \"label\");\n    i0.ɵɵtext(75, \"Attention Reason\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"span\", 46);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 52)(79, \"label\");\n    i0.ɵɵtext(80, \"Internal Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"span\", 46);\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 52)(84, \"label\");\n    i0.ɵɵtext(85, \"Action Taken\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"span\", 46);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.projectName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitType || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.primaryContact || \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.permit.permitStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitStatus || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.location || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitCategory || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitIssueDate ? i0.ɵɵpipeBind2(38, 17, ctx_r1.permit.permitIssueDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Applied on \", ctx_r1.permit.permitAppliedDate ? i0.ɵɵpipeBind2(41, 20, ctx_r1.permit.permitAppliedDate, \"MM/dd/yyyy\") : \"\", \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitExpirationDate ? i0.ɵɵpipeBind2(47, 23, ctx_r1.permit.permitExpirationDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitFinalDate ? i0.ɵɵpipeBind2(53, 26, ctx_r1.permit.permitFinalDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitCompleteDate ? i0.ɵɵpipeBind2(59, 29, ctx_r1.permit.permitCompleteDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.permit.internalReviewStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.permit.internalReviewStatus || \"\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.attentionReason || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.internalNotes || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.actionTaken || \"\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56);\n    i0.ɵɵelement(2, \"i\", 57);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No internal reviews found for this permit.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 61);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template_tr_click_0_listener() {\n      const i_r10 = i0.ɵɵrestoreView(_r9).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectAudit(i_r10));\n    });\n    i0.ɵɵelementStart(1, \"td\", 62)(2, \"div\", 63)(3, \"h6\", 64);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 65);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 66)(8, \"small\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"td\", 68)(11, \"div\", 69)(12, \"div\", 70)(13, \"small\", 71);\n    i0.ɵɵtext(14, \"Reviewed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 72);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 70)(19, \"small\", 71);\n    i0.ɵɵtext(20, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 72);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 70)(25, \"small\", 71);\n    i0.ɵɵtext(26, \"Reviewer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 72);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"td\", 73)(30, \"i\", 74);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template_i_click_30_listener($event) {\n      const i_r10 = i0.ɵɵrestoreView(_r9).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      ctx_r1.editInternalReview(i_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const audit_r11 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"table-active\", ctx_r1.selectedAuditIndex === i_r10);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(audit_r11.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(audit_r11.internalVerificationStatus))(\"ngStyle\", ctx_r1.getStatusStyle(audit_r11.internalVerificationStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", audit_r11.internalVerificationStatus || \"Pending\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(audit_r11.typeCodeDrawing || \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(audit_r11.reviewedDate ? i0.ɵɵpipeBind2(17, 10, audit_r11.reviewedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(audit_r11.completedDate ? i0.ɵɵpipeBind2(23, 13, audit_r11.completedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(audit_r11.internalReviewer || \"\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_30_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"table\", 59)(2, \"tbody\");\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template, 31, 16, \"tr\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.auditEntries);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitViewComponent_div_2_ng_container_30_div_1_Template, 5, 0, \"div\", 53)(2, PermitViewComponent_div_2_ng_container_30_div_2_Template, 4, 1, \"div\", 54);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.auditEntries.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.auditEntries.length > 0);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 78)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\", 79);\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.fetchExternalReviews());\n    });\n    i0.ɵɵelement(7, \"i\", 81);\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.reviewsError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 55)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\", 82);\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"No external reviews found for this permit.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const correction_r18 = i0.ɵɵnextContext().$implicit;\n      const review_r16 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.openResponseModal(correction_r18, review_r16));\n    });\n    i0.ɵɵtext(1, \" Respond \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const correction_r18 = i0.ɵɵnextContext().$implicit;\n      const review_r16 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.openResponseModal(correction_r18, review_r16));\n    });\n    i0.ɵɵtext(1, \" Edit Response \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 140);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.Response, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" EOR / AOR / Owner Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 141);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.EORAOROwner_Response, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"label\", 133);\n    i0.ɵɵtext(2, \" Comment Responded By \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.commentResponsedBy, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 143);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 120)(2, \"div\", 121);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 122)(5, \"div\", 123)(6, \"div\", 124)(7, \"span\", 125);\n    i0.ɵɵtext(8, \"Correction Type: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 126);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 124)(12, \"span\", 125);\n    i0.ɵɵtext(13, \"Category: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 126);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 124)(17, \"span\", 125);\n    i0.ɵɵtext(18, \"Resolved: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 127);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 128);\n    i0.ɵɵtemplate(23, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template, 2, 1, \"button\", 129)(24, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template, 2, 1, \"button\", 130);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 131)(26, \"div\", 132)(27, \"label\", 133);\n    i0.ɵɵtext(28, \" Corrective Action \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 134);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 132)(32, \"label\", 133);\n    i0.ɵɵtext(33, \" Comment \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 135);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_36_Template, 5, 1, \"div\", 136)(37, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_37_Template, 5, 1, \"div\", 136)(38, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_38_Template, 5, 1, \"div\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_39_Template, 1, 0, \"div\", 137);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const correction_r18 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    const review_r16 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r20 + 1);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(correction_r18.CorrectionTypeName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r18.CorrectionCategoryName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r18.ResolvedDate ? i0.ɵɵpipeBind2(21, 12, correction_r18.ResolvedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !correction_r18.EORAOROwner_Response && !correction_r18.commentResponsedBy && ctx_r1.shouldShowEditResponseButton(correction_r18));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (correction_r18.EORAOROwner_Response || correction_r18.commentResponsedBy) && ctx_r1.shouldShowEditResponseButton(correction_r18));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.CorrectiveAction || \"\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", correction_r18.Comments || \"No comment provided\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r18.Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r18.EORAOROwner_Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r18.commentResponsedBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r20 < review_r16.corrections.length - 1);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"h6\", 117);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_Template, 40, 15, \"div\", 118);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const review_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Corrections (\", review_r16.corrections.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", review_r16.corrections);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"h6\", 117);\n    i0.ɵɵtext(2, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 145)(4, \"div\", 146);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const review_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(review_r16.comments);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"div\", 148);\n    i0.ɵɵelement(2, \"i\", 149);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \" No corrections or comments available for this review.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template_div_click_1_listener() {\n      const review_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.toggleReviewAccordion(review_r16.commentsId));\n    });\n    i0.ɵɵelementStart(2, \"div\", 99);\n    i0.ɵɵelement(3, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h6\", 100);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 101)(7, \"span\", 102);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 103)(10, \"span\", 104);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 105)(13, \"span\", 106);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 107)(17, \"span\", 108);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 109)(21, \"i\", 110);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template_i_click_21_listener($event) {\n      const review_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      ctx_r1.downloadReviewPDF(review_r16);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 111)(23, \"div\", 112);\n    i0.ɵɵtemplate(24, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_Template, 4, 2, \"div\", 113)(25, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_25_Template, 6, 1, \"div\", 114)(26, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_26_Template, 5, 0, \"div\", 115);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const review_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isReviewExpanded(review_r16.commentsId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isReviewExpanded(review_r16.commentsId) ? \"fa-chevron-down\" : \"fa-chevron-right\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", review_r16.FailureFlag ? \"red\" : \"green\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", review_r16.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(review_r16.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", review_r16.status || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(review_r16.reviewer || \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", review_r16.dueDate ? \"Due: \" + i0.ɵɵpipeBind2(15, 16, review_r16.dueDate, \"MM/dd/yyyy\") : \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", review_r16.completedDate ? \"Completed: \" + i0.ɵɵpipeBind2(19, 19, review_r16.completedDate, \"MM/dd/yyyy\") : \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"d-none\", !ctx_r1.isReviewExpanded(review_r16.commentsId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (review_r16 == null ? null : review_r16.corrections) && review_r16.corrections.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(review_r16 == null ? null : review_r16.corrections) || review_r16.corrections.length === 0) && (review_r16 == null ? null : review_r16.comments));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(review_r16 == null ? null : review_r16.corrections) || review_r16.corrections.length === 0) && !(review_r16 == null ? null : review_r16.comments));\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\", 87);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template_tr_click_1_listener() {\n      const i_r14 = i0.ɵɵrestoreView(_r13).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.toggleSubmittalAccordion(i_r14));\n    });\n    i0.ɵɵelementStart(2, \"td\", 88)(3, \"div\", 63)(4, \"div\", 89);\n    i0.ɵɵelement(5, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h6\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 90);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"td\", 91)(11, \"div\", 69)(12, \"div\", 70)(13, \"small\", 71);\n    i0.ɵɵtext(14, \"Due\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 72);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 70)(19, \"small\", 71);\n    i0.ɵɵtext(20, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 72);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"tr\", 92)(25, \"td\", 93)(26, \"div\", 94)(27, \"div\", 95);\n    i0.ɵɵtemplate(28, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template, 27, 22, \"div\", 96);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const sub_r21 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isSubmittalExpanded(i_r14));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isSubmittalExpanded(i_r14) ? \"fa-chevron-down\" : \"fa-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sub_r21.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(sub_r21.submittalStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", sub_r21.submittalStatus, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 11, sub_r21.dueDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 14, sub_r21.receivedDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"d-none\", !ctx_r1.isSubmittalExpanded(i_r14));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getExternalReviewsForSubmittal(sub_r21.id));\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84)(2, \"table\", 85)(3, \"tbody\");\n    i0.ɵɵtemplate(4, PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template, 29, 17, \"ng-container\", 86);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.externalSubmittals);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 75)(2, \"div\", 9);\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_31_div_3_Template, 9, 2, \"div\", 76)(4, PermitViewComponent_div_2_ng_container_31_div_4_Template, 6, 0, \"div\", 76)(5, PermitViewComponent_div_2_ng_container_31_div_5_Template, 5, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.reviewsError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reviewsError && ctx_r1.externalSubmittals.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reviewsError && ctx_r1.externalSubmittals.length > 0);\n  }\n}\nfunction PermitViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"div\", 12)(4, \"div\", 13)(5, \"span\", 14);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 17)(12, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(13, \"i\", 19);\n    i0.ɵɵtext(14, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"ul\", 21)(17, \"li\", 22)(18, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_a_click_18_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(19, \" Permit Details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, PermitViewComponent_div_2_li_20_Template, 3, 3, \"li\", 24);\n    i0.ɵɵelementStart(21, \"li\", 22)(22, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_a_click_22_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"external\", $event));\n    });\n    i0.ɵɵtext(23, \" External Reviews \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 25);\n    i0.ɵɵtemplate(25, PermitViewComponent_div_2_button_25_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, PermitViewComponent_div_2_div_26_Template, 6, 2, \"div\", 27)(27, PermitViewComponent_div_2_div_27_Template, 11, 7, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 28);\n    i0.ɵɵtemplate(29, PermitViewComponent_div_2_ng_container_29_Template, 88, 32, \"ng-container\", 29)(30, PermitViewComponent_div_2_ng_container_30_Template, 3, 2, \"ng-container\", 29)(31, PermitViewComponent_div_2_ng_container_31_Template, 6, 3, \"ng-container\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Permit # \", ctx_r1.permit.permitNumber || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitReviewType || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.permit.permitName || \"\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ctx_r1.selectedTab === \"external\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"details\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"internal\" && ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"external\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.permit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"internal\" && ctx_r1.permit && ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"external\" && ctx_r1.permit);\n  }\n}\nfunction PermitViewComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 150)(1, \"div\", 151)(2, \"div\", 152);\n    i0.ɵɵelementContainerStart(3);\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5, \"Edit Notes/Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 153)(7, \"i\", 154);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_i_click_7_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closModal());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 155)(9, \"form\", 156)(10, \"div\", 157)(11, \"div\", 158)(12, \"div\", 159)(13, \"label\", 160);\n    i0.ɵɵtext(14, \" Attention Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"textarea\", 161);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 157)(17, \"div\", 158)(18, \"div\", 159)(19, \"label\", 162);\n    i0.ɵɵtext(20, \" Internal Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 163);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 157)(23, \"div\", 158)(24, \"div\", 159)(25, \"label\", 164);\n    i0.ɵɵtext(26, \" Action Taken \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"textarea\", 165);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(28, \"div\", 166)(29, \"div\")(30, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closModal());\n    });\n    i0.ɵɵtext(31, \" Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \"\\u00A0 \");\n    i0.ɵɵelementStart(33, \"button\", 168);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editNotesandactions());\n    });\n    i0.ɵɵtext(34, \"Update \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.notesForm);\n  }\n}\nexport class PermitViewComponent {\n  route;\n  router;\n  modalService;\n  cdr;\n  fb;\n  appService;\n  customLayoutUtilsService;\n  permitsService;\n  httpUtilService;\n  notesForm;\n  permitId = null;\n  permit = null;\n  isLoading = false; // Main page loader\n  auditEntries = [];\n  selectedAuditIndex = 0; // Set to 0 to make first item initially active\n  selectedAuditName = '';\n  selectedAuditStatus = '';\n  selectedTab = 'details';\n  permitReviewType = '';\n  externalReviews = [];\n  reviewsError = '';\n  externalSubmittals = [];\n  selectedExternalSubmittalId = null;\n  internalReviews = [];\n  loginUser = {};\n  isAdmin = false;\n  singlePermit;\n  expandedSubmittals = new Set();\n  expandedReviews = new Set();\n  reviewSelectedTabs = {};\n  routeSubscription = new Subscription();\n  queryParamsSubscription = new Subscription();\n  loadingSubscription = new Subscription();\n  statusList = [{\n    text: 'Pending',\n    value: 'Pending'\n  }, {\n    text: 'In Progress',\n    value: 'In Progress'\n  }, {\n    text: 'Completed',\n    value: 'Completed'\n  }, {\n    text: 'On Hold',\n    value: 'On Hold'\n  }];\n  // Navigation tracking\n  previousPage = 'permit-list'; // Default fallback\n  projectId = null;\n  constructor(route, router, modalService, cdr, fb,\n  // private modal: NgbActiveModal,\n  appService, customLayoutUtilsService, permitsService, httpUtilService) {\n    this.route = route;\n    this.router = router;\n    this.modalService = modalService;\n    this.cdr = cdr;\n    this.fb = fb;\n    this.appService = appService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.permitsService = permitsService;\n    this.httpUtilService = httpUtilService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.isAdmin = this.checkIfAdmin();\n    // Subscribe to global loading state\n    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading === true;\n    });\n    // Read query parameters for navigation tracking\n    this.queryParamsSubscription = this.route.queryParams.subscribe(params => {\n      this.previousPage = params['from'] || 'permit-list';\n      this.projectId = params['projectId'] ? Number(params['projectId']) : null;\n      console.log('Permit view - query params:', {\n        previousPage: this.previousPage,\n        projectId: this.projectId\n      });\n    });\n    // Listen for route parameter changes\n    this.routeSubscription = this.route.paramMap.subscribe(params => {\n      const idParam = params.get('id');\n      this.permitId = idParam ? Number(idParam) : null;\n      if (this.permitId) {\n        this.fetchPermitDetails();\n        this.fetchExternalReviews();\n        this.fetchInternalReviews();\n      }\n    });\n    this.loadForm();\n  }\n  loadForm() {\n    this.notesForm = this.fb.group({\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: ['']\n    });\n    // Trigger change detection to update the view\n    this.cdr.detectChanges();\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n    if (this.queryParamsSubscription) {\n      this.queryParamsSubscription.unsubscribe();\n    }\n    if (this.loadingSubscription) {\n      this.loadingSubscription.unsubscribe();\n    }\n  }\n  fetchPermitDetails() {\n    if (!this.permitId) {\n      return;\n    }\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.getPermit({\n      permitId: this.permitId\n    }).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.log('Permit API Response:', res);\n        if (!res?.isFault) {\n          this.permit = res.responseData?.data || res.responseData || null;\n          console.log('Permit data assigned:', this.permit);\n          console.log('Permit permitName field:', this.permit?.permitName);\n          console.log('All permit fields:', Object.keys(this.permit || {}));\n          this.permitReviewType = this.permit?.permitReviewType || '';\n          // Default to details tab, user can navigate to reviews as needed\n          this.selectedTab = 'details';\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.permit = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchExternalReviews() {\n    if (!this.permitId) {\n      return;\n    }\n    this.httpUtilService.loadingSubject.next(true);\n    this.reviewsError = '';\n    this.permitsService.getAllReviews({\n      permitId: this.permitId\n    }).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (res?.isFault) {\n          this.reviewsError = res.faultMessage || 'Failed to load reviews';\n        } else {\n          const reviews = res.responseData?.reviews || [];\n          this.externalReviews = reviews.map(r => ({\n            commentsId: r.commentsId,\n            name: r.TypeName,\n            reviewer: r.AssignedTo,\n            status: r.StatusText,\n            completedDate: r.CompletedDate ? new Date(r.CompletedDate) : null,\n            dueDate: r.DueDate ? new Date(r.DueDate) : null,\n            receivedDate: r.receivedDate ? new Date(r.receivedDate) : null,\n            comments: r.Comments,\n            corrections: r.Corrections || [],\n            submittalId: r.SubmittalId,\n            FailureFlag: r.FailureFlag,\n            reviewCategory: r.reviewCategory,\n            EORAOROwner_Response: r.EORAOROwner_Response,\n            commentResponsedBy: r.commentResponsedBy\n          }));\n          // Build submittal list grouped from reviews\n          const idToReviews = {};\n          this.externalReviews.forEach(rv => {\n            const key = String(rv.submittalId ?? 'unknown');\n            if (!idToReviews[key]) {\n              idToReviews[key] = [];\n            }\n            idToReviews[key].push(rv);\n          });\n          this.externalSubmittals = Object.keys(idToReviews).map(key => {\n            const items = idToReviews[key];\n            // Determine status priority: Requires Re-submit > Under Review > Approved w/ Conditions > Approved\n            const statusOrder = {\n              'Requires Re-submit': 4,\n              'Under Review': 3,\n              'Approved w/ Conditions': 2,\n              'Approved': 1\n            };\n            const submittalStatus = items.reduce((acc, it) => {\n              const a = statusOrder[acc] || 0;\n              const b = statusOrder[it.status] || 0;\n              return b > a ? it.status : acc;\n            }, '');\n            // Aggregate dates\n            const dueDate = items.reduce((acc, it) => {\n              if (!it.dueDate) {\n                return acc;\n              }\n              if (!acc) {\n                return it.dueDate;\n              }\n              return acc > it.dueDate ? it.dueDate : acc; // earliest due date\n            }, null);\n            const completedDate = items.reduce((acc, it) => {\n              if (!it.completedDate) {\n                return acc;\n              }\n              if (!acc) {\n                return it.completedDate;\n              }\n              return acc < it.completedDate ? it.completedDate : acc; // latest completed date\n            }, null);\n            // Get received date from the first item that has it\n            const receivedDate = items.find(it => it.receivedDate)?.receivedDate || items.find(it => it.createdDate)?.createdDate || null;\n            // Get submittal name from the first item (all items in this group have same submittalId)\n            const reviewCategory = items[0]?.reviewCategory || `Submittal ${key}`;\n            return {\n              id: key,\n              title: reviewCategory,\n              submittalStatus: submittalStatus || items[0]?.status || '',\n              receivedDate: receivedDate ? new Date(receivedDate) : null,\n              dueDate: dueDate,\n              completedDate: completedDate\n            };\n          }).sort((a, b) => {\n            // Sort by received date in descending order (latest first)\n            if (!a.receivedDate && !b.receivedDate) return 0;\n            if (!a.receivedDate) return 1;\n            if (!b.receivedDate) return -1;\n            return b.receivedDate.getTime() - a.receivedDate.getTime();\n          });\n          // Select first submittal by default\n          if (this.externalSubmittals.length > 0) {\n            this.selectedExternalSubmittalId = this.externalSubmittals[0].id;\n            this.selectedAuditName = this.externalSubmittals[0].title;\n            this.selectedAuditStatus = this.externalSubmittals[0].submittalStatus;\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        this.reviewsError = 'Failed to load reviews';\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchInternalReviews() {\n    if (!this.permitId) {\n      return;\n    }\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.getInternalReviews({\n      permitId: this.permitId,\n      take: 50,\n      skip: 0\n    }).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (res?.isFault) {\n          console.error('Failed to load internal reviews:', res.faultMessage);\n        } else {\n          this.internalReviews = res.responseData?.data || res.data || [];\n          // Transform internal reviews to match the auditEntries format\n          this.auditEntries = this.internalReviews.map(review => ({\n            commentsId: review.commentsId,\n            title: review.reviewCategory,\n            reviewCategory: review.reviewCategory,\n            // Preserve reviewCategory for edit modal\n            typeCodeDrawing: review.typeCodeDrawing,\n            reviewComments: review.reviewComments,\n            nonComplianceItems: review.nonComplianceItems,\n            aeResponse: review.aeResponse,\n            internalReviewer: review.internalReviewer,\n            internalVerificationStatus: review.internalVerificationStatus,\n            reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n            completedDate: review.completedDate ? new Date(review.completedDate) : null,\n            reviews: [{\n              name: review.reviewCategory,\n              typeCodeDrawing: review.typeCodeDrawing,\n              reviewComments: review.reviewComments,\n              nonComplianceItems: review.nonComplianceItems,\n              aeResponse: review.aeResponse,\n              internalReviewer: review.internalReviewer,\n              internalVerificationStatus: review.internalVerificationStatus,\n              reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n              completedDate: review.completedDate ? new Date(review.completedDate) : null\n            }]\n          }));\n          // Select first internal review by default\n          if (this.auditEntries.length > 0) {\n            this.selectedAuditIndex = 0;\n            this.selectedAuditName = this.auditEntries[0].title;\n            this.selectedAuditStatus = this.auditEntries[0].internalVerificationStatus || 'Pending';\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading internal reviews:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  downloadInternalReviewsPdf() {\n    if (!this.internalReviews || this.internalReviews.length === 0) {\n      return;\n    }\n    const grouped = {};\n    this.internalReviews.forEach(r => {\n      const key = r.reviewCategory || 'Uncategorized';\n      if (!grouped[key]) {\n        grouped[key] = [];\n      }\n      grouped[key].push(r);\n    });\n    const doc = new jsPDF({\n      orientation: 'portrait',\n      unit: 'pt',\n      format: 'a4'\n    });\n    const pageWidth = doc.internal.pageSize.getWidth();\n    // Use more horizontal space: reduce side margins to ~0.5 inch (36pt)\n    const margin = {\n      left: 36,\n      right: 36,\n      top: 40\n    };\n    let y = margin.top;\n    const addCategory = (category, items) => {\n      // Category block title (centered, uppercase)\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`${category.toUpperCase()} REVIEW COMMENTS`, pageWidth / 2, y, {\n        align: 'center'\n      });\n      y += 12;\n      // Reviewer line (take distinct non-empty names, join with comma)\n      const reviewers = Array.from(new Set(items.map(it => (it.internalReviewer || '').toString().trim()).filter(v => v)));\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Reviewer: ${reviewers.join(', ') || ''}`, pageWidth / 2, y, {\n        align: 'center'\n      });\n      y += 12;\n      // Dates line (Reviewed Date / Responses Date)\n      const reviewedDate = items.find(it => it.reviewedDate)?.reviewedDate;\n      const responsesDate = items.find(it => it.completedDate)?.completedDate;\n      doc.setFont('helvetica', 'italic');\n      doc.setFontSize(9);\n      doc.text(`Reviewed Date: ${reviewedDate ? AppService.formatDate(reviewedDate) : ''}`, margin.left, y);\n      doc.text(`Response Date: ${responsesDate ? AppService.formatDate(responsesDate) : ''}`, pageWidth - margin.right, y, {\n        align: 'right'\n      });\n      y += 6;\n      const rows = items.map((it, idx) => [(idx + 1).toString(), it.typeCodeDrawing || '', (it.reviewComments || '').toString(), (it.aeResponse || '').toString(), it.internalVerificationStatus || '']);\n      autoTable(doc, {\n        startY: y + 5,\n        head: [['#', 'Drawing #', 'Review Comments', 'A/E Response', 'Status']],\n        body: rows,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 8,\n          cellPadding: 5,\n          valign: 'top'\n        },\n        headStyles: {\n          fillColor: [33, 150, 243],\n          textColor: 255,\n          halign: 'center',\n          fontSize: 9\n        },\n        // Fit exactly into available width (pageWidth - margins)\n        // A4 width ~595pt; with 36pt margins each side → 523pt content width\n        columnStyles: {\n          0: {\n            cellWidth: 24,\n            halign: 'center'\n          },\n          // #\n          1: {\n            cellWidth: 55\n          },\n          // Drawing # (even narrower)\n          2: {\n            cellWidth: 198\n          },\n          // Review Comments (half of remaining)\n          3: {\n            cellWidth: 197\n          },\n          // A/E Response (other half)\n          4: {\n            cellWidth: 49\n          } // Verification Status (fits)\n        },\n        theme: 'grid'\n      });\n      // update y for next section\n      // @ts-ignore\n      y = doc.lastAutoTable.finalY + 20;\n      // add page if needed\n      if (y > doc.internal.pageSize.getHeight() - 100) {\n        doc.addPage();\n        y = margin.top;\n      }\n    };\n    Object.keys(grouped).forEach((category, idx) => {\n      // Removed divider line between categories\n      addCategory(category, grouped[category]);\n    });\n    const fileName = `Internal_Reviews_${this.permit?.permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  }\n  selectExternalSubmittal(id) {\n    this.selectedExternalSubmittalId = id;\n    const sel = this.externalSubmittals.find(s => String(s.id) === String(id));\n    if (sel) {\n      this.selectedAuditName = sel.title;\n      this.selectedAuditStatus = sel.submittalStatus;\n    }\n  }\n  getExternalReviewsForSelectedSubmittal() {\n    if (!this.selectedExternalSubmittalId) {\n      return [];\n    }\n    return this.getExternalReviewsForSubmittal(this.selectedExternalSubmittalId);\n  }\n  getExternalReviewsForSubmittal(submittalId) {\n    const reviews = this.externalReviews.filter(r => String(r.submittalId) === String(submittalId));\n    // Sort by FailureFlag: false reviews first (desc), then true reviews (desc)\n    return reviews.sort((a, b) => {\n      // If both have the same FailureFlag value, maintain original order (reverse for desc)\n      if (a.FailureFlag === b.FailureFlag) {\n        return 0;\n      }\n      // False reviews (FailureFlag = false) come first\n      if (!a.FailureFlag && b.FailureFlag) {\n        return -1;\n      }\n      // True reviews (FailureFlag = true) come after false reviews\n      if (a.FailureFlag && !b.FailureFlag) {\n        return 1;\n      }\n      return 0;\n    }).reverse(); // Reverse to get descending order within each group\n  }\n  toggleSubmittalAccordion(index) {\n    if (this.expandedSubmittals.has(index)) {\n      this.expandedSubmittals.delete(index);\n    } else {\n      this.expandedSubmittals.add(index);\n    }\n  }\n  isSubmittalExpanded(index) {\n    return this.expandedSubmittals.has(index);\n  }\n  areAllSubmittalsExpanded() {\n    return this.externalSubmittals && this.externalSubmittals.length > 0 && this.expandedSubmittals.size === this.externalSubmittals.length;\n  }\n  toggleAllSubmittals() {\n    if (!this.externalSubmittals || this.externalSubmittals.length === 0) {\n      return;\n    }\n    if (this.areAllSubmittalsExpanded()) {\n      this.expandedSubmittals.clear();\n    } else {\n      this.expandedSubmittals = new Set(this.externalSubmittals.map((_, idx) => idx));\n    }\n    this.cdr.markForCheck();\n  }\n  toggleReviewAccordion(reviewId) {\n    if (this.expandedReviews.has(reviewId)) {\n      this.expandedReviews.delete(reviewId);\n    } else {\n      this.expandedReviews.add(reviewId);\n      // Set default tab for this review if not already set\n      if (!this.reviewSelectedTabs[reviewId]) {\n        this.reviewSelectedTabs[reviewId] = 'corrections';\n      }\n    }\n  }\n  isReviewExpanded(reviewId) {\n    return this.expandedReviews.has(reviewId);\n  }\n  showReviewTab(reviewId, tab, $event) {\n    $event.stopPropagation();\n    this.reviewSelectedTabs[reviewId] = tab;\n    this.cdr.markForCheck();\n  }\n  updateReviewResponse(review) {\n    this.httpUtilService.loadingSubject.next(true);\n    const formData = {\n      EORAOROwner_Response: review.EORAOROwner_Response,\n      commentResponsedBy: review.commentResponsedBy,\n      permitId: this.permitId,\n      commentsId: review.commentsId,\n      loggedInUserId: this.loginUser.userId\n    };\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          // Refresh external reviews to get updated data\n          this.fetchExternalReviews();\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage || '❌ Error syncing permit', '');\n          //alert(res.faultMessage || 'Failed to update review response');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        this.customLayoutUtilsService.showError('❌ Error updating review response', '');\n        //alert('Error updating review response');\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  isSelectedSubmittal(submittalId) {\n    return String(this.selectedExternalSubmittalId) === String(submittalId);\n  }\n  selectAudit(index) {\n    this.selectedAuditIndex = index;\n    this.selectedAuditName = this.auditEntries[this.selectedAuditIndex].title;\n    this.selectedAuditStatus = this.auditEntries[this.selectedAuditIndex].submittalStatus;\n  }\n  getReviewsForSelectedAudit() {\n    if (this.selectedAuditIndex === null || this.selectedAuditIndex >= this.auditEntries.length) {\n      return [];\n    }\n    return this.auditEntries[this.selectedAuditIndex].reviews || [];\n  }\n  goBack() {\n    console.log('goBack called - previousPage:', this.previousPage, 'projectId:', this.projectId);\n    if (this.previousPage === 'project' && this.projectId) {\n      // Navigate back to the specific project view with permits tab active\n      console.log('Navigating to project view with permits tab active');\n      this.router.navigate(['/projects/view', this.projectId], {\n        queryParams: {\n          activeTab: 'permits'\n        }\n      });\n    } else {\n      // Default to permit list\n      console.log('Navigating to permit list');\n      this.router.navigate(['/permits/list']);\n    }\n  }\n  goToPortal() {\n    window.open(`${this.permit.cityReviewLink + this.permit.permitEntityID}`, '_blank');\n  }\n  openReviewDetails(review) {\n    const modalRef = this.modalService.open(ReviewDetailsModalComponent, {\n      size: 'lg'\n    });\n    modalRef.componentInstance.review = review;\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.permitDetails = this.permit;\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchExternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    console.log('Status mapping - Original:', status, 'Normalized:', normalized);\n    // Map common synonyms from API to a single class we style\n    const aliasMap = {\n      'in-review': 'in-review',\n      'requires-re-submit': 'requires-re-submit',\n      'approved-w-conditions': 'approved-w-conditions',\n      'in-progress': 'in-progress',\n      'completed': 'completed',\n      'verified': 'verified',\n      'pending': 'pending',\n      'rejected': 'rejected',\n      'approved': 'approved',\n      'under-review': 'under-review',\n      'requires-resubmit': 'requires-resubmit',\n      'pacifica-verification': 'pacifica-verification',\n      'dis-approved': 'dis-approved',\n      'not-required': 'not-required',\n      '1-cycle-completed': '1-cycle-completed',\n      '1 cycle completed': '1-cycle-completed',\n      'cycle completed': '1-cycle-completed'\n    };\n    const resolved = aliasMap[normalized] || normalized;\n    const finalClass = 'status-' + resolved;\n    console.log('Final status class:', finalClass);\n    console.log('Available CSS classes for debugging:', ['status-pending', 'status-in-progress', 'status-completed', 'status-verified', 'status-rejected', 'status-approved', 'status-under-review', 'status-requires-resubmit', 'status-pacifica-verification', 'status-dis-approved', 'status-not-required', 'status-in-review', 'status-1-cycle-completed']);\n    return finalClass;\n  }\n  getStatusStyle(status) {\n    if (!status) return {};\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    const styleMap = {\n      '1-cycle-completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      '1 cycle completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      'cycle completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      'pacifica-verification': {\n        backgroundColor: '#e1f5fe',\n        color: '#0277bd',\n        border: '1px solid #81d4fa'\n      },\n      'dis-approved': {\n        backgroundColor: '#ffebee',\n        color: '#c62828',\n        border: '1px solid #ffcdd2'\n      },\n      'not-required': {\n        backgroundColor: '#f5f5f5',\n        color: '#757575',\n        border: '1px solid #e0e0e0'\n      },\n      'in-review': {\n        backgroundColor: '#e8eaf6',\n        color: '#3949ab',\n        border: '1px solid #c5cae9'\n      },\n      'pending': {\n        backgroundColor: '#fff3e0',\n        color: '#e65100',\n        border: '1px solid #ffcc02'\n      },\n      'approved': {\n        backgroundColor: '#e8f5e8',\n        color: '#1b5e20',\n        border: '1px solid #c8e6c9'\n      },\n      'completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#1b5e20',\n        border: '1px solid #c8e6c9'\n      },\n      'rejected': {\n        backgroundColor: '#ffebee',\n        color: '#c62828',\n        border: '1px solid #ffcdd2'\n      }\n    };\n    return styleMap[normalized] || {};\n  }\n  showTab(tab, $event) {\n    if (tab === 'internal' && !this.isInternalReviewEnabled()) {\n      return;\n    }\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  isInternalReviewEnabled() {\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n    // show only when type is 'internal' or 'both'\n    return type === 'internal' || type === 'both';\n  }\n  isExternalReviewEnabled() {\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n    // show only when type is 'external' or 'both'\n    return type === 'external' || type === 'both';\n  }\n  addPopUp() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddEditInternalReviewComponent\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n    // Pass data to the modal\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchInternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  editInternalReview(reviewIndex) {\n    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false,\n      scrollable: true\n    };\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n    // Pass data to the modal for editing\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.reviewData = this.auditEntries[reviewIndex];\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'updated') {\n        // Refresh internal reviews\n        this.fetchInternalReviews();\n      }\n    }).catch(error => {\n      console.log('Modal dismissed');\n    });\n  }\n  editPopUp() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n  }\n  editPermit() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(PermitPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.permitId;\n    // Listen for passEntry event to refresh permit details when permit is saved\n    modalRef.componentInstance.passEntry.subscribe(saved => {\n      if (saved) {\n        this.fetchPermitDetails();\n      }\n    });\n  }\n  syncPermits(i) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.singlePermit = i || false;\n    this.permitsService.syncPermits({\n      permitId: this.permitId,\n      singlePermit: this.singlePermit,\n      autoLogin: true\n    }).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.log('Sync response:', res);\n        console.log('Response type:', typeof res);\n        console.log('Response keys:', Object.keys(res || {}));\n        console.log('Response success:', res?.success);\n        console.log('Response message:', res?.message);\n        console.log('Response responseData:', res?.responseData);\n        // Handle various response structures\n        let responseData = res;\n        // Check different possible response structures\n        if (res?.responseData) {\n          responseData = res.responseData;\n        } else if (res?.body?.responseData) {\n          responseData = res.body.responseData;\n        } else if (res?.body) {\n          responseData = res.body;\n        }\n        console.log('Final responseData:', responseData);\n        console.log('Final success:', responseData?.success);\n        console.log('Final message:', responseData?.message);\n        if (responseData?.isFault) {\n          //alert(responseData.faultMessage || 'Failed to sync permit');\n          this.customLayoutUtilsService.showError(responseData.faultMessage, '');\n        } else if (responseData?.success === false) {\n          // Handle specific error messages from the API\n          if (responseData.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (responseData.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n          }\n        } else if (responseData?.success === true || responseData?.data) {\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n          //alert('✅ Permit synced successfully');\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n        } else {\n          // Fallback for unknown response structure\n          console.log('Unknown response structure, showing generic success');\n          //alert('✅ Permit synced successfully');\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        // Handle HTTP error responses\n        console.log('Error response:', err);\n        console.log('Error type:', typeof err);\n        console.log('Error keys:', Object.keys(err || {}));\n        console.log('Error status:', err?.status);\n        console.log('Error message:', err?.message);\n        console.log('Error error:', err?.error);\n        // The interceptor passes err.error to the error handler\n        // So err might actually be the response data\n        if (err?.success === false) {\n          // Handle specific error messages from the API\n          if (err.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n          }\n        } else if (err?.error?.message) {\n          if (err.error.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.error.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.error.message}`);\n          }\n        } else if (err?.status === 404) {\n          this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          // Handle 404 specifically for permit not found\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n        } else {\n          //alert('❌ Error syncing permit');\n        }\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  editExternalReview(review) {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddEditInternalReviewComponent\n    const modalRef = this.modalService.open(EditExternalReviewComponent, NgbModalOptions);\n    console.log('reviewData ', review);\n    // Pass data to the modal\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.reviewData = review;\n    modalRef.componentInstance.permitDetails = this.permit;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchExternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  openResponseModal(correction, review) {\n    // Open the modal using NgbModal\n    const modalRef = this.modalService.open(ResponseModalComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false\n    });\n    // Pass data to the modal\n    modalRef.componentInstance.correction = correction;\n    modalRef.componentInstance.review = review;\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;\n    modalRef.componentInstance.isAdmin = this.isAdmin;\n    // Handle modal result\n    modalRef.componentInstance.responseSubmitted.subscribe(formData => {\n      this.submitResponse(formData, modalRef);\n    });\n    // Handle response completion to reset loading state\n    modalRef.componentInstance.responseCompleted.subscribe(success => {\n      if (!success) {\n        // Reset loading state if submission failed\n        modalRef.componentInstance.isLoading = false;\n      }\n    });\n    modalRef.result.then(() => {\n      // Modal was closed\n    }).catch(() => {\n      // Modal was dismissed\n    });\n  }\n  submitResponse(formData, modalRef) {\n    if (!formData) {\n      return;\n    }\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (res?.isFault === false) {\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          //alert(res.responseData.message || 'Response submitted successfully');\n          this.fetchExternalReviews(); // Refresh external reviews to get updated data\n          // Emit success completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(true);\n          }\n          // Close the modal if it was passed\n          if (modalRef) {\n            modalRef.close();\n          }\n        } else {\n          //alert(res.faultMessage || 'Failed to submit response');\n          this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to submit response', '');\n          // Emit failure completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(false);\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        //alert('Error submitting response');\n        console.error(err);\n        this.customLayoutUtilsService.showSuccess('Error submitting response', '');\n        // Emit failure completion event\n        if (modalRef && modalRef.componentInstance) {\n          modalRef.componentInstance.responseCompleted.emit(false);\n        }\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  downloadReviewPDF(review) {\n    if (!review) {\n      return;\n    }\n    try {\n      const permitNumber = this.permit?.permitNumber || '';\n      const reviewer = (review?.AssignedTo || review?.municipalityReviewer || review?.reviewer || '').toString();\n      const cityComments = (review?.Comments || (review?.cityComments ?? '')).toString();\n      const ownerResponse = (review?.EORAOROwner_Response ?? '').toString();\n      // Calculate submittal count for this review\n      const submittalCount = this.externalSubmittals ? this.externalSubmittals.length : 0;\n      const cycle = submittalCount > 0 ? submittalCount.toString() : (review?.cycle || review?.Cycle || '').toString();\n      const status = (review?.StatusName || review?.commentstatus || review?.status || '').toString();\n      const displayDate = review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate ? AppService.formatDate(review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate) : AppService.formatDate(new Date());\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'pt',\n        format: 'a4'\n      });\n      const pageWidth = doc.internal.pageSize.getWidth();\n      const pageHeight = doc.internal.pageSize.getHeight();\n      const margin = {\n        left: 40,\n        right: 40,\n        top: 40,\n        bottom: 40\n      };\n      // Header: Permit Number\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Permit #: ${permitNumber || ''}`, margin.left, margin.top);\n      const startY = margin.top + 20;\n      // Table with one row matching the screenshot\n      const headers = ['REVIEWED BY', 'CITY COMMENTS', 'EOR/AOR/OWNER COMMENT RESPONSE', 'CYCLE', 'STATUS', 'DATE'];\n      // Build body rows. If there are corrections, show one row per correction; otherwise single row\n      const rawCorrections = (review && review.Corrections) ?? review.corrections ?? [];\n      const correctionsArray = Array.isArray(rawCorrections) ? rawCorrections : rawCorrections ? [rawCorrections] : [];\n      const bodyRows = correctionsArray.length > 0 ? correctionsArray.map(c => [reviewer || '', c?.Comments || cityComments || '', c?.Response || c?.EORAOROwner_Response || ownerResponse || '', cycle || '', status || '', (c?.ResolvedDate ? AppService.formatDate(c.ResolvedDate) : displayDate) || '']) : [[reviewer || '', cityComments || '', ownerResponse || '', cycle || '', status || '', displayDate || '']];\n      autoTable(doc, {\n        startY,\n        head: [headers],\n        body: bodyRows,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 8,\n          cellPadding: 5,\n          overflow: 'linebreak'\n        },\n        headStyles: {\n          fillColor: [240, 240, 240],\n          textColor: [0, 0, 0],\n          fontStyle: 'bold',\n          fontSize: 8\n        },\n        columnStyles: {\n          0: {\n            cellWidth: 90\n          },\n          1: {\n            cellWidth: (pageWidth - margin.left - margin.right) * 0.26\n          },\n          2: {\n            cellWidth: (pageWidth - margin.left - margin.right) * 0.24\n          },\n          3: {\n            cellWidth: 45,\n            halign: 'center'\n          },\n          4: {\n            cellWidth: 60,\n            halign: 'center'\n          },\n          5: {\n            cellWidth: 60,\n            halign: 'center'\n          }\n        },\n        didParseCell: data => {\n          // City comments text in red\n          if (data.section === 'body' && data.column.index === 1) {\n            data.cell.styles.textColor = [192, 0, 0];\n          }\n        },\n        didDrawCell: data => {\n          // Fully colored background for Status cell\n          if (data.section === 'body' && data.column.index === 4) {\n            const value = String(data.cell.raw || '');\n            const isApproved = value.toLowerCase() === 'approved';\n            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\n            const textColor = [255, 255, 255];\n            // fill whole cell\n            doc.setFillColor(bg[0], bg[1], bg[2]);\n            doc.rect(data.cell.x + 0.5, data.cell.y + 0.5, data.cell.width - 1, data.cell.height - 1, 'F');\n            // write centered white text\n            doc.setTextColor(textColor[0], textColor[1], textColor[2]);\n            doc.setFont('helvetica', 'bold');\n            doc.setFontSize(8);\n            const textWidth = doc.getTextWidth(value);\n            const textX = data.cell.x + data.cell.width / 2 - textWidth / 2;\n            const textY = data.cell.y + data.cell.height / 2 + 3;\n            doc.text(value, textX, textY);\n            data.cell.text = [];\n          }\n        },\n        theme: 'grid'\n      });\n      // Footer\n      const pageCount = doc.getNumberOfPages();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        doc.setDrawColor(200, 200, 200);\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(8);\n        doc.setTextColor(100, 100, 100);\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n      }\n      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      this.customLayoutUtilsService.showSuccess('Error generation PDF. Please try agaiin', '');\n      //alert('Error generating PDF. Please try again.');\n    }\n  }\n  checkIfAdmin() {\n    // Check if the user is an admin based on roleId\n    // Assuming roleId 1 is admin - adjust this based on your role system\n    return this.loginUser && this.loginUser.roleId === 1;\n  }\n  shouldShowEditResponseButton(correction) {\n    // Show edit response button if:\n    // 1. User is admin (can always edit)\n    // 2. User is not admin but lockResponse is false (unlocked by admin)\n    if (this.isAdmin) {\n      return true;\n    }\n    // For non-admin users, only show if lockResponse is explicitly false\n    return correction.lockResponse === false;\n  }\n  onEdit(template) {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    this.modalService.open(template, NgbModalOptions);\n    // console.log(\"this.permit\", this.permit)\n    this.notesForm.patchValue({\n      actionTaken: this.permit.actionTaken,\n      attentionReason: this.permit.attentionReason,\n      internalNotes: this.permit.internalNotes\n      // actionTaken:'helo',\n    });\n  }\n  closModal() {\n    this.modalService.dismissAll();\n  }\n  editNotesandactions() {\n    this.httpUtilService.loadingSubject.next(true);\n    const formData = {\n      permitId: this.permitId,\n      actionTaken: this.notesForm.value.actionTaken,\n      internalNotes: this.notesForm.value.internalNotes,\n      attentionReason: this.notesForm.value.attentionReason\n    };\n    this.permitsService.editNotesAndActions(formData).subscribe({\n      next: res => {\n        this.closModal();\n        this.httpUtilService.loadingSubject.next(false);\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          // Refresh permit details to get updated data from server\n          this.fetchPermitDetails();\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage || '❌ Error in update notes and actions', '');\n          //alert(res.faultMessage || 'Failed to update review response');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        this.customLayoutUtilsService.showError('❌ Error in update notes and actions', '');\n        //alert('Error updating review response');\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  static ɵfac = function PermitViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.PermitsService), i0.ɵɵdirectiveInject(i7.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitViewComponent,\n    selectors: [[\"app-permit-view\"]],\n    decls: 5,\n    vars: 2,\n    consts: [[\"notesActionsTemplate\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"permit-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"permit-details-header\"], [1, \"header-content\", \"w-100\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"permit-title\"], [1, \"status-text\", \"status-under-review\"], [1, \"permit-number-line\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\", 2, \"margin-right\", \"16px\"], [\"type\", \"button\", \"class\", \"btn btn-link p-0\", \"title\", \"Edit Permit\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"button-group\", 4, \"ngIf\"], [1, \"card-body\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Edit Permit\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\", 2, \"font-size\", \"1.1rem\"], [\"type\", \"button\", \"title\", \"Download Internal Reviews PDF\", 1, \"btn\", \"btn-link\", \"p-0\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"download-pdf-icon\", 2, \"color\", \"#3699ff\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"bi\", \"bi-plus-lg\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", 3, \"ngClass\"], [1, \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-sync-alt\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"permit-details-content\"], [1, \"permit-details-grid\"], [1, \"permit-detail-item\"], [1, \"permit-value\"], [1, \"status-text\", 3, \"ngClass\"], [1, \"text-gray-500\", \"fs-7\", \"p-0\"], [1, \"permit-details-card\"], [\"type\", \"button\", \"title\", \"Edit Notes/Actions\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"notes-actions-container\"], [1, \"permit-detail-item-full\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-clipboard-list\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [\"class\", \"audit-table-row\", 3, \"table-active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"audit-table-row\", 3, \"click\"], [1, \"audit-title-cell\", \"px-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"mb-0\", \"me-3\"], [1, \"audit-status-badge\", 3, \"ngClass\", \"ngStyle\"], [1, \"mt-1\"], [1, \"text-muted\"], [1, \"audit-dates-cell\", \"px-3\"], [1, \"d-flex\", \"gap-4\"], [1, \"date-item\"], [1, \"text-muted\", \"d-block\"], [1, \"fw-medium\"], [1, \"audit-actions-cell\", \"px-4\"], [\"title\", \"Edit Review\", 1, \"fas\", \"fa-edit\", \"action-icon\", \"edit-icon\", 3, \"click\"], [1, \"external-reviews-card\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"class\", \"card-body p-0\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"fa-3x\", \"mb-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-redo\"], [1, \"fas\", \"fa-external-link-alt\", \"fa-3x\", \"mb-3\"], [1, \"card-body\", \"p-0\"], [1, \"table-responsive\", \"external-reviews-table\"], [1, \"table\", \"table-hover\", \"mb-0\"], [4, \"ngFor\", \"ngForOf\"], [1, \"external-submittal-row\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"submittal-title-cell\", \"px-3\"], [1, \"accordion-toggle\", \"me-2\"], [1, \"submittal-status-badge\", 3, \"ngClass\"], [1, \"submittal-dates-cell\", \"px-3\"], [1, \"accordion-content-row\"], [\"colspan\", \"2\", 1, \"p-0\"], [1, \"accordion-content\"], [1, \"reviews-container\", \"p-3\"], [\"class\", \"review-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"review-item\"], [1, \"review-single-line\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"review-accordion-toggle\", \"me-2\"], [1, \"review-title\"], [1, \"review-status-container\"], [1, \"review-status\", 3, \"ngClass\"], [1, \"reviewer-container\"], [1, \"reviewer\"], [1, \"due-date-container\"], [1, \"due-date\"], [1, \"completed-date-container\"], [1, \"completed-date\"], [1, \"review-actions-container\"], [\"title\", \"Download Review PDF\", 1, \"fas\", \"fa-download\", \"download-pdf-icon\", 3, \"click\"], [1, \"review-details-accordion\"], [1, \"review-details-content\"], [\"class\", \"corrections-section p-3\", \"style\", \"padding-bottom: 0px !important;\", 4, \"ngIf\"], [\"class\", \"comments-section p-3\", 4, \"ngIf\"], [\"class\", \"no-data-section p-3\", 4, \"ngIf\"], [1, \"corrections-section\", \"p-3\", 2, \"padding-bottom\", \"0px !important\"], [1, \"section-title\"], [\"class\", \"correction-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"correction-item\"], [1, \"correction-header\", \"d-flex\", \"align-items-center\"], [1, \"correction-number\"], [1, \"correction-meta\", \"flex-grow-1\", \"ms-3\", \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"meta-fields\", \"d-flex\", \"align-items-center\", \"w-100\"], [1, \"meta-field\", \"flex-fill\"], [1, \"meta-label\", \"fw-bold\"], [1, \"meta-value\"], [1, \"meta-value\", \"resolved-date\"], [1, \"respond-buttons\"], [\"class\", \"btn btn-primary btn-sm me-3\", \"title\", \"Respond to this correction\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary btn-sm me-3\", \"title\", \"Edit response to this correction\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"correction-content\"], [1, \"correction-field\"], [1, \"field-label\"], [1, \"field-content\", \"corrective-action\"], [1, \"field-content\", \"comment\"], [\"class\", \"correction-field\", 4, \"ngIf\"], [\"class\", \"correction-separator\", 4, \"ngIf\"], [\"title\", \"Respond to this correction\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [\"title\", \"Edit response to this correction\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"field-content\", \"response\"], [1, \"field-content\", \"eor-response\"], [1, \"field-content\", \"responded-by\"], [1, \"correction-separator\"], [1, \"comments-section\", \"p-3\"], [1, \"comment-content\"], [1, \"comment-text\"], [1, \"no-data-section\", \"p-3\"], [1, \"no-data-message\", \"text-center\", \"text-muted\"], [1, \"fas\", \"fa-info-circle\"], [1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"medium-modal-body\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [1, \"form-group\"], [\"for\", \"projectName\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"attentionReason\", \"rows\", \"2\", \"formControlName\", \"attentionReason\", \"placeholder\", \"Type here...\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"projectDescription\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"internalNotes\", \"rows\", \"3\", \"formControlName\", \"internalNotes\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"internalProjectNo\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"internalNotes\", \"rows\", \"3\", \"formControlName\", \"actionTaken\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [1, \"modal-footer\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n    template: function PermitViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, PermitViewComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2);\n        i0.ɵɵtemplate(2, PermitViewComponent_div_2_Template, 32, 16, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, PermitViewComponent_ng_template_3_Template, 35, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.permit);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgStyle, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i8.DatePipe],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.permit-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: transparent;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n  margin-left: auto; \\n\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  padding: 0.375rem 0.75rem !important;\\n  line-height: 1.5 !important;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  padding: 0.15rem 0.5rem; \\n\\n  border-radius: 0.55rem;\\n  background-color: #f3f6f9;\\n  color: #3f4254;\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1; \\n\\n  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.02s ease;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #5e6e82;\\n  font-size: 0.75rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover {\\n  background-color: #eef2f7;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);\\n  text-decoration: none;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(1px);\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 2rem; \\n\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover {\\n  background-color: transparent;\\n  color: #3699ff !important;\\n  text-decoration: none;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: color 0.2s ease;\\n  line-height: 1;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #3699ff !important;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  background-color: #f5f5f5 !important;\\n  color: #999 !important;\\n  border-color: #ddd !important;\\n}\\n.btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: #f5f5f5 !important;\\n  color: #999 !important;\\n  border-color: #ddd !important;\\n  box-shadow: none !important;\\n}\\n\\n.permit-details-card[_ngcontent-%COMP%] {\\n  background: #fff;\\n  margin: 1rem 0;\\n  overflow: hidden;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%] {\\n  background: #f5f6f8;\\n  padding: 0.75rem 1.25rem;\\n  border-bottom: 1px solid #e3e6ea;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 2rem; \\n\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover {\\n  background-color: transparent;\\n  color: #3699ff !important;\\n  text-decoration: none;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: color 0.2s ease;\\n  line-height: 1;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #3699ff !important;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%] {\\n  background: #1b7e6c;\\n  color: #fff;\\n  border: none;\\n  border-radius: 6px;\\n  padding: 0.35rem 0.75rem;\\n  font-size: 0.85rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  transition: background 0.2s ease;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%]:hover {\\n  background: #166354;\\n}\\n\\n.permit-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);\\n  margin: 1rem 0;\\n  overflow: hidden;\\n}\\n\\n.permit-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr; \\n\\n  gap: 1.5rem;\\n}\\n\\n.notes-actions-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.4rem;\\n  width: 100%;\\n}\\n.permit-detail-item-full[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  line-height: 1.2;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #6c7293;\\n  text-transform: capitalize;\\n  letter-spacing: 0.1rem;\\n  margin: 0;\\n}\\n.permit-detail-item-full[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  padding: 0.5rem 0;\\n  border-bottom: none;\\n  word-wrap: break-word;\\n  white-space: pre-wrap;\\n}\\n\\n.permit-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.4rem;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  line-height: 1.2;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #111111;\\n  text-transform: capitalize;\\n  letter-spacing: 0.1rem;\\n  margin: 0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%], \\n.permit-detail-item[_ngcontent-%COMP%]   .permit-status[_ngcontent-%COMP%] {\\n  margin-top: 0.1rem;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  padding: 0.5rem 0;\\n  border-bottom: none; \\n\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-status[_ngcontent-%COMP%] {\\n  display: block;\\n  vertical-align: top;\\n  padding: 0.5rem 0; \\n\\n  font-size: 1rem;\\n  font-weight: 600;\\n  text-align: left; \\n\\n  background: transparent; \\n\\n  border: none; \\n\\n  min-width: 0; \\n\\n  border-radius: 0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n\\n.loading-section[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 0.475rem;\\n  padding: 2rem;\\n  text-align: center;\\n  border: 1px solid #e5eaee;\\n}\\n.loading-section[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n.loading-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n  margin: 0;\\n}\\n.loading-section[_ngcontent-%COMP%]   .loading-tips[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #a1a5b7 !important;\\n  font-size: 0.875rem;\\n}\\n.loading-section[_ngcontent-%COMP%]   .loading-tips[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #3699ff;\\n}\\n\\n.no-permit-data[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  border-radius: 0.475rem;\\n  border: 1px solid #e5eaee;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #6c7293;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  padding-left: 1.5rem;\\n  color: #6c7293;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  padding: 0 0.5rem 0 0.5rem;\\n  margin-top: 0;\\n}\\n\\n.external-reviews-table[_ngcontent-%COMP%] {\\n  padding-top: 0 !important;\\n  margin-top: 0 !important;\\n}\\n\\n.card-body[_ngcontent-%COMP%] {\\n  padding-top: 0 !important;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  overflow: hidden;\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border-bottom: 1px solid #e5eaee;\\n  background: #ffffff;\\n  border-radius: 0.5rem;\\n  margin-bottom: 0.5rem;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row[_ngcontent-%COMP%]:hover, \\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row.expanded[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row.expanded[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border-color: #2196f3;\\n  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row.table-active[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row.table-active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%] {\\n  width: 75%;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n  display: inline-block;\\n  min-width: 60px;\\n  text-align: center;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-progress[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #90caf9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-verified[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n  border: 1px solid #a5d6a7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-on-hold[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%] {\\n  width: 25%;\\n  vertical-align: middle;\\n  text-align: right;\\n  padding-right: 0;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  gap: 1rem;\\n  justify-content: flex-end;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 120px; \\n\\n  min-width: 120px;\\n  max-width: 120px;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n  display: block; \\n\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   .fw-medium[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   .fw-medium[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  display: block; \\n\\n  min-height: 1.25rem; \\n\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%] {\\n  width: 15%;\\n  vertical-align: middle;\\n  text-align: center;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3699ff;\\n  cursor: pointer;\\n  transition: color 0.2s ease, transform 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  transition: transform 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\\n  border-left: 4px solid #3699ff;\\n  box-shadow: inset 0 2px 4px rgba(54, 153, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]   .accordion-content[_ngcontent-%COMP%] {\\n  border-top: 1px solid #d1e7ff;\\n  background: transparent;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]   .accordion-content[_ngcontent-%COMP%]   .reviews-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 0.375rem;\\n  margin: 0.5rem;\\n  padding: 1rem;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.15);\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:has( + .accordion-content-row[_ngcontent-%COMP%]:not(.d-none)) {\\n  background: linear-gradient(135deg, #f0f8ff 0%, #e8f2ff 100%);\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.2);\\n  transition: all 0.3s ease;\\n  margin-right: 0;\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:has( + .accordion-content-row[_ngcontent-%COMP%]:not(.d-none))   .submittal-title-cell[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: #f8f9fa;\\n  border-radius: 0.475rem 0.475rem 0 0;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1rem;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.reviews-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #1b5e20 !important;\\n  border: 1px solid #c8e6c9 !important;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0 !important;\\n  color: #e65100 !important;\\n  border: 1px solid #ffcc02 !important;\\n}\\n\\n.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n\\n.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd !important;\\n  color: #1565c0 !important;\\n  border: 1px solid #bbdefb !important;\\n}\\n\\n.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #1b5e20 !important;\\n  border: 1px solid #c8e6c9 !important;\\n}\\n\\n.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n\\n.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n\\n.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n\\n.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n\\n.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n\\n.reviews-container[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n  overflow-y: auto;\\n  padding: 0.75rem;\\n}\\n\\n.review-item[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5eaee;\\n  padding: 0.5rem 0;\\n  background: transparent;\\n  transition: background-color 0.2s ease;\\n}\\n.review-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.review-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.review-single-line[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 20px 1fr 120px 120px 140px 140px 30px;\\n  align-items: center;\\n  gap: 0.75rem;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  background: transparent;\\n  border-radius: 0.375rem;\\n  transition: all 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.review-single-line.expanded[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 1px solid #2196f3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .reviewer-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .due-date-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .completed-date-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .review-actions-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  min-height: 20px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-actions-container[_ngcontent-%COMP%] {\\n  gap: 0.5rem;\\n  justify-content: flex-end;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n  white-space: nowrap;\\n  display: inline-block;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3699ff;\\n  cursor: pointer;\\n  transition: color 0.2s ease, transform 0.2s ease;\\n  margin-left: 0.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.view-icon[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.view-icon[_ngcontent-%COMP%]:hover {\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-accordion-toggle[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  transition: transform 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-accordion-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 0.25rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]:hover {\\n  color: #1e7e34;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 0.25rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  border: none;\\n  background: none;\\n  box-shadow: none;\\n  text-decoration: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover, .review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus, .review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:active {\\n  background: none;\\n  box-shadow: none;\\n  text-decoration: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled   .download-pdf-icon[_ngcontent-%COMP%] {\\n  cursor: not-allowed;\\n  color: #6c757d;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled   .download-pdf-icon[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n  color: #6c757d;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  border-top: 1px solid #e5eaee;\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\\n  border-radius: 0.375rem;\\n  box-shadow: inset 0 2px 4px rgba(54, 153, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 0.375rem;\\n  margin: 0.5rem;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-radius: 0.75rem !important;\\n  padding: 2rem !important;\\n  border: 1px solid #e5eaee !important;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;\\n  margin: 0 !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem !important;\\n  font-weight: 800 !important;\\n  color: #1a202c !important;\\n  margin-bottom: 2rem !important;\\n  padding-bottom: 1rem !important;\\n  border-bottom: 3px solid #3699ff !important;\\n  position: relative !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 1rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: 1.8rem;\\n  filter: drop-shadow(0 2px 4px rgba(54, 153, 255, 0.3));\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 2px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.4);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%] {\\n  background: #fff !important;\\n  border: 1px solid #e5eaee !important;\\n  border-radius: 0.75rem !important;\\n  padding: 1.5rem !important;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\\n  position: relative !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\\n  overflow: hidden !important;\\n  margin-bottom: 2rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #3699ff, #1bc5bd);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before {\\n  background: linear-gradient(180deg, #1bc5bd, #3699ff);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1.25rem;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 2px solid #f1f3f4;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  width: 50px;\\n  height: 2px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 1px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-number, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd) !important;\\n  color: white !important;\\n  width: 32px !important;\\n  height: 32px !important;\\n  border-radius: 50% !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  font-size: 0.9rem !important;\\n  font-weight: 800 !important;\\n  box-shadow: 0 4px 12px rgba(54, 153, 255, 0.4) !important;\\n  flex-shrink: 0 !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-number::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd);\\n  border-radius: 50%;\\n  z-index: -1;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_pulse 2s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 0.75rem !important;\\n  padding: 0.5rem 0 !important;\\n  background: transparent !important;\\n  border-radius: 0 !important;\\n  border: none !important;\\n  transition: all 0.2s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover {\\n  background: transparent;\\n  transform: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 700;\\n  color: #4a5568;\\n  text-transform: capitalize;\\n  letter-spacing: 0.05rem;\\n  min-width: 80px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-value, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .resolved-date, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%] {\\n  color: inherit;\\n  background: transparent;\\n  padding: 0.25rem 0;\\n  border-radius: 0;\\n  font-weight: 700;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 800;\\n  color: #2d3748;\\n  text-transform: capitalize;\\n  letter-spacing: 0.15rem;\\n  margin-bottom: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label i, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.9rem;\\n  width: 16px;\\n  text-align: center;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u25B6\\\";\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  animation: _ngcontent-%COMP%_slideRight 1.5s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_slideRight {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  50% {\\n    transform: translateX(3px);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  color: #2d3748 !important;\\n  line-height: 1.7 !important;\\n  padding: 1.25rem !important;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;\\n  border-radius: 0.75rem !important;\\n  border: 1px solid #e2e8f0 !important;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04) !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #e2e8f0, #cbd5e0);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);\\n  border-color: #fed7d7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #feb2b2, #fc8181);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.comment, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);\\n  border-color: #bee3f8;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.comment::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #90cdf4, #63b3ed);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%);\\n  border-color: #c6f6d5;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  color: #38a169;\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  animation: _ngcontent-%COMP%_checkmark 0.6s ease-in-out;\\n}\\n@keyframes _ngcontent-%COMP%_checkmark {\\n  0% {\\n    transform: scale(0) rotate(0deg);\\n  }\\n  50% {\\n    transform: scale(1.2) rotate(180deg);\\n  }\\n  100% {\\n    transform: scale(1) rotate(360deg);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.eor-response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%) !important;\\n  border-color: #c6f6d5 !important;\\n  box-shadow: inset 0 2px 8px rgba(56, 161, 105, 0.1) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.eor-response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.responded-by, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%) !important;\\n  border-color: #c6f6d5 !important;\\n  box-shadow: inset 0 2px 8px rgba(56, 161, 105, 0.1) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.responded-by::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-separator, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent, #e5eaee, transparent);\\n  margin: 2rem 0;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-separator::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 6px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 3px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-radius: 0.75rem !important;\\n  padding: 2rem !important;\\n  border: 1px solid #e5eaee !important;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem !important;\\n  font-weight: 800 !important;\\n  color: #1a202c !important;\\n  margin-bottom: 2rem !important;\\n  padding-bottom: 1rem !important;\\n  border-bottom: 3px solid #3699ff !important;\\n  position: relative !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 1rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: 1.8rem;\\n  filter: drop-shadow(0 2px 4px rgba(54, 153, 255, 0.3));\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 2px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.4);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%] {\\n  background: #fff !important;\\n  border: 1px solid #e5eaee !important;\\n  border-radius: 0.75rem !important;\\n  padding: 1.5rem !important;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\\n  position: relative !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\\n  overflow: hidden !important;\\n  margin-bottom: 2rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #3699ff, #1bc5bd);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before {\\n  background: linear-gradient(180deg, #1bc5bd, #3699ff);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1.25rem;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 2px solid #f1f3f4;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  width: 50px;\\n  height: 2px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 1px;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-number, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd) !important;\\n  color: white !important;\\n  width: 32px !important;\\n  height: 32px !important;\\n  border-radius: 50% !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  font-size: 0.9rem !important;\\n  font-weight: 800 !important;\\n  box-shadow: 0 4px 12px rgba(54, 153, 255, 0.4) !important;\\n  flex-shrink: 0 !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-number::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd);\\n  border-radius: 50%;\\n  z-index: -1;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_pulse 2s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.5rem 0;\\n  background: transparent;\\n  border-radius: 0;\\n  border: none;\\n  transition: all 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover {\\n  background: transparent;\\n  transform: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 700;\\n  color: #4a5568;\\n  text-transform: capitalize;\\n  letter-spacing: 0.05rem;\\n  min-width: 80px;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-value, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .resolved-date, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%] {\\n  color: inherit;\\n  background: transparent;\\n  padding: 0.25rem 0;\\n  border-radius: 0;\\n  font-weight: 700;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .respond-btn, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .respond-btn[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .respond-btn[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 800;\\n  color: #2d3748;\\n  text-transform: capitalize;\\n  letter-spacing: 0.15rem;\\n  margin-bottom: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label i, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.9rem;\\n  width: 16px;\\n  text-align: center;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u25B6\\\";\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  animation: _ngcontent-%COMP%_slideRight 1.5s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_slideRight {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  50% {\\n    transform: translateX(3px);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #2d3748;\\n  line-height: 1.7;\\n  padding: 1.25rem;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\\n  border-radius: 0.75rem;\\n  border: 1px solid #e2e8f0;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04);\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #e2e8f0, #cbd5e0);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);\\n  border-color: #fed7d7;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #feb2b2, #fc8181);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.comment, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);\\n  border-color: #bee3f8;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.comment::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #90cdf4, #63b3ed);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%);\\n  border-color: #c6f6d5;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  color: #38a169;\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  animation: _ngcontent-%COMP%_checkmark 0.6s ease-in-out;\\n}\\n@keyframes _ngcontent-%COMP%_checkmark {\\n  0% {\\n    transform: scale(0) rotate(0deg);\\n  }\\n  50% {\\n    transform: scale(1.2) rotate(180deg);\\n  }\\n  100% {\\n    transform: scale(1) rotate(360deg);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-separator, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent, #e5eaee, transparent);\\n  margin: 2rem 0;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-separator::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 6px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 3px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  margin-bottom: 1rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 2px solid #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #3f4254;\\n  line-height: 1.5;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  text-align: center;\\n  color: #6c7293;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  display: block;\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  margin-bottom: 0.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.875rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #3699ff;\\n  box-shadow: 0 0 0 0.2rem rgba(54, 153, 255, 0.25);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background-color: #1bc5bd;\\n  border-color: #1bc5bd;\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  background-color: #17a2b8;\\n  border-color: #17a2b8;\\n}\\n\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-bottom: 0.75rem;\\n  font-size: 0.75rem;\\n  color: #6c7293;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%]::before, \\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  width: 4px;\\n  height: 4px;\\n  background-color: #6c7293;\\n  border-radius: 50%;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .view-btn[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-size: 0.65rem;\\n  padding: 6px 10px;\\n  line-height: 1;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n}\\n\\n.no-selection[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  text-align: center;\\n  color: #6c7293;\\n}\\n\\n@media (max-width: 768px) {\\n  .table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%], \\n   .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%] {\\n    grid-template-columns: 20px 1fr 100px 100px 120px 120px 30px;\\n    gap: 0.5rem;\\n    font-size: 0.8rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.2rem 0.4rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .review-single-line[_ngcontent-%COMP%] {\\n    grid-template-columns: 20px 1fr 80px 80px 100px 100px 25px;\\n    gap: 0.25rem;\\n    padding: 0.25rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n    padding: 0.15rem 0.3rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n    font-size: 0.6rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.permit-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.4);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #3699ff;\\n  padding: 2rem;\\n}\\n\\n.correction-content[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%], \\n.correction-content[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;\\n  border-color: #e2e8f0 !important;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04) !important;\\n}\\n.correction-content[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]::before, \\n.correction-content[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n\\n\\n\\n.notes-actions-card[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  padding: 1.5rem;\\n  max-width: 700px;\\n  margin: auto;\\n}\\n\\n\\n\\n.notes-actions-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1.2rem;\\n  border-bottom: 1px solid #e5e5e5;\\n  padding-bottom: 0.8rem;\\n}\\n\\n.notes-actions-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n  border: none;\\n  padding: 6px 14px;\\n  border-radius: 6px;\\n  font-size: 0.9rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n\\n\\n.notes-actions-body[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 0.4rem;\\n  color: #333;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 60px;\\n  padding: 0.6rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  resize: vertical;\\n  font-size: 0.95rem;\\n}\\n\\n\\n\\n.kendo-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9wZXJtaXRzL3Blcm1pdC12aWV3L3Blcm1pdC12aWV3LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGdCQUFnQjtBQUFoQjtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLE1BQUE7QUFFRjs7QUFFQTtFQUNFLGlCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx1QkFBQTtBQUNGO0FBQ0U7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBQ0o7QUFFRTtFQUNFLFNBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtBQUFKO0FBR0U7RUFDRSxhQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUEsRUFBQSxxQ0FBQTtBQURKO0FBR0k7RUFDRSw4QkFBQTtFQUNBLG9DQUFBO0VBQ0EsMkJBQUE7QUFETjtBQUtFOztFQUVFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsdUJBQUEsRUFBQSw2Q0FBQTtFQUNBLHNCQUFBO0VBQ0EseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7RUFDQSx5Q0FBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBLEVBQUEsaUJBQUE7RUFDQSxrRkFBQTtBQUhKO0FBS0k7O0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0FBRk47QUFLSTs7RUFDRSx5QkFBQTtFQUNBLDBDQUFBO0VBQ0EscUJBQUE7QUFGTjtBQUtJOztFQUNFLDBCQUFBO0VBQ0EseUNBQUE7QUFGTjtBQU9JO0VBQ0UscUJBQUE7QUFMTjs7QUFhSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQSxFQUFBLDBDQUFBO0FBVk47QUFZTTtFQUNFLDZCQUFBO0VBQ0EseUJBQUE7RUFDQSxxQkFBQTtBQVZSO0FBYU07RUFDRSxnQkFBQTtFQUNBLGFBQUE7QUFYUjtBQWNNO0VBQ0UsMkJBQUE7RUFDQSxjQUFBO0FBWlI7QUFlTTtFQUNFLHlCQUFBO0FBYlI7O0FBb0JBO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0VBQ0Esb0NBQUE7RUFDQSxzQkFBQTtFQUNBLDZCQUFBO0FBakJGO0FBbUJFO0VBQ0Usb0NBQUE7RUFDQSxzQkFBQTtFQUNBLDZCQUFBO0VBQ0EsMkJBQUE7QUFqQko7O0FBb0JBO0VBQ0UsZ0JBQUE7RUFHQSxjQUFBO0VBQ0EsZ0JBQUE7QUFuQkY7O0FBc0JBO0VBQ0UsbUJBQUE7RUFDQSx3QkFBQTtFQUNBLGdDQUFBO0VBQ0EsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7QUFuQkY7O0FBc0JBO0VBQ0UsU0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7QUFuQkY7O0FBd0JFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBLEVBQUEscUNBQUE7QUFyQko7QUF1Qkk7RUFDRSw2QkFBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7QUFyQk47QUF3Qkk7RUFDRSxnQkFBQTtFQUNBLGFBQUE7QUF0Qk47QUF5Qkk7RUFDRSwyQkFBQTtFQUNBLGNBQUE7QUF2Qk47QUEwQkk7RUFDRSx5QkFBQTtBQXhCTjs7QUE0QkE7RUFDRSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSx3QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxnQ0FBQTtBQXpCRjs7QUE0QkE7RUFDRSxtQkFBQTtBQXpCRjs7QUEyQkE7RUFDRSxvQkFBQTtFQUNFLHNCQUFBO0VBQ0YsMENBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUF4QkY7O0FBMkJBO0VBQ0UsYUFBQTtFQUNBLHNDQUFBLEVBQUEsNEJBQUE7RUFDQSxXQUFBO0FBeEJGOztBQTRCQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFdBQUE7QUF6QkY7O0FBNEJBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7QUF6QkY7QUEyQkU7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDBCQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBekJKO0FBNEJFO0VBQ0UsZUFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxxQkFBQTtFQUNBLHFCQUFBO0FBMUJKOztBQThCQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFdBQUE7QUEzQkY7QUE2QkU7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDBCQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBM0JKO0FBOEJFOztFQUVFLGtCQUFBO0FBNUJKO0FBK0JFO0VBQ0UsZUFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUEsRUFBQSx3QkFBQTtBQTdCSjtBQWdDRTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBLEVBQUEsb0NBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQSxFQUFBLG9CQUFBO0VBQ0EsdUJBQUEsRUFBQSx3QkFBQTtFQUNBLFlBQUEsRUFBQSxjQUFBO0VBQ0EsWUFBQSxFQUFBLHdCQUFBO0VBQ0EsZ0JBQUE7QUE5Qko7QUFrQ0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWhDSjtBQW1DRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBakNKO0FBb0NFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFsQ0o7QUFxQ0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQW5DSjtBQXNDRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBcENKO0FBdUNFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFyQ0o7QUF3Q0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXRDSjtBQXlDRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBdkNKO0FBMENFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF4Q0o7O0FBNkNBO0VBQ0UsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBMUNGO0FBNENFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUExQ0o7QUE2Q0U7RUFDRSxjQUFBO0VBQ0EsU0FBQTtBQTNDSjtBQStDSTtFQUNFLHlCQUFBO0VBQ0EsbUJBQUE7QUE3Q047QUErQ007RUFDRSxvQkFBQTtFQUNBLGNBQUE7QUE3Q1I7O0FBc0RBO0VBQ0UsbUJBQUE7QUFuREY7QUFxREU7RUFDRSx1QkFBQTtFQUNBLHlCQUFBO0FBbkRKO0FBcURJO0VBQ0Usb0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUFuRE47QUFzREk7RUFDRSxvQkFBQTtFQUNBLGNBQUE7QUFwRE47QUF1REk7RUFDRSxrQkFBQTtFQUNBLG9CQUFBO0VBQ0EsY0FBQTtBQXJETjtBQXVETTtFQUNFLHNCQUFBO0FBckRSO0FBeURJO0VBQ0UsbUJBQUE7RUFDQSxvQkFBQTtBQXZETjtBQXlETTtFQUNFLG9CQUFBO0FBdkRSOztBQThEQTtFQUNFLGdCQUFBO0VBQ0EsMEJBQUE7RUFDQSxhQUFBO0FBM0RGOztBQStEQTtFQUNFLHlCQUFBO0VBQ0Esd0JBQUE7QUE1REY7O0FBZ0VBO0VBQ0UseUJBQUE7QUE3REY7O0FBZ0VBO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtBQTdERjtBQStERTs7RUFFRSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxnQ0FBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7RUFDQSxxQkFBQTtFQUNBLHdDQUFBO0FBN0RKO0FBK0RJOztFQUNFLHlCQUFBO0VBQ0EsMkJBQUE7RUFDQSx5Q0FBQTtBQTVETjtBQStESTs7RUFDRSxtQkFBQTtFQUNBLHFCQUFBO0VBQ0EsNkNBQUE7QUE1RE47QUErREk7O0VBQ0UseUJBQUE7QUE1RE47QUFnRUU7O0VBRUUsVUFBQTtFQUNBLHNCQUFBO0FBOURKO0FBZ0VJOzs7O0VBRUUsaUJBQUE7RUFDQSxzQkFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7RUFDQSw2QkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxxQkFBQTtFQUNBLGVBQUE7RUFnQ0Esa0JBQUE7QUEzRk47QUE4RE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUF6RFI7QUE0RE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUF2RFI7QUEwRE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFyRFI7QUF3RE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFuRFI7QUFzRE07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFqRFI7QUFzRE07Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWpEUjtBQW9ETTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBL0NSO0FBa0RNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUE3Q1I7QUFnRE07Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTNDUjtBQThDTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBekNSO0FBNENNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF2Q1I7QUEwQ007Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXJDUjtBQXdDTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBbkNSO0FBdUNNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFsQ1I7QUFxQ007Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWhDUjtBQW1DTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBOUJSO0FBaUNNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUE1QlI7QUErQk07Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFCUjtBQTZCTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBeEJSO0FBMkJNOzs7O0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF0QlI7QUF5Qk07Ozs7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXBCUjtBQXVCTTs7OztFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBbEJSO0FBc0JNOzs7O0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBakJSO0FBb0JNOzs7O0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBZlI7QUFrQk07Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFiUjtBQWdCTTs7OztFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQVhSO0FBY007Ozs7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFUUjtBQWNFOztFQUVFLFVBQUE7RUFDQSxzQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFaSjtBQWNJOztFQUNFLFNBQUE7RUFDQSx5QkFBQTtBQVhOO0FBY0k7O0VBQ0Usa0JBQUE7RUFDQSxZQUFBLEVBQUEsaUNBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBWE47QUFhTTs7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQSxFQUFBLG9DQUFBO0FBVlI7QUFhTTs7RUFDRSxrQkFBQTtFQUNBLGNBQUEsRUFBQSx3Q0FBQTtFQUNBLG1CQUFBLEVBQUEsNkJBQUE7QUFWUjtBQWdCRTtFQUNFLFVBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0FBZEo7QUFnQkk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxnREFBQTtBQWROO0FBZ0JNO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBZFI7QUFpQk07RUFDRSxjQUFBO0FBZlI7QUFpQlE7RUFDRSxjQUFBO0FBZlY7QUFxQkU7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFuQko7QUFxQkk7RUFDRSwrQkFBQTtBQW5CTjtBQXVCRTtFQUNFLDZEQUFBO0VBQ0EsOEJBQUE7RUFDQSxtREFBQTtFQUNBLHlCQUFBO0FBckJKO0FBdUJJO0VBQ0UsNkJBQUE7RUFDQSx1QkFBQTtBQXJCTjtBQXVCTTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQ0FBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtFQUNBLGFBQUE7RUFDQSw4Q0FBQTtBQXJCUjtBQTJCRTtFQUNFLDZEQUFBO0VBQ0EsNkNBQUE7RUFDQSx5QkFBQTtFQUNBLGVBQUE7QUF6Qko7QUE0Qk07RUFDRSxjQUFBO0VBQ0EscUJBQUE7QUExQlI7QUFnQ0U7RUFDRSx5QkFBQTtBQTlCSjtBQWdDSTtFQUNFLHlCQUFBO0FBOUJOOztBQW1DQTtFQUNFLG9CQUFBO0VBQ0EsZ0NBQUE7RUFDQSxtQkFBQTtFQUNBLG9DQUFBO0VBQ0EsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7QUFoQ0Y7QUFrQ0U7RUFDRSxTQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtBQWhDSjtBQW9DSTtFQUNFLGtCQUFBO0VBQ0EseUJBQUE7QUFsQ047QUFzQ0U7RUFDRSxrQkFBQTtFQUNBLHVCQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtFQUNBLDZCQUFBO0FBcENKO0FBdUNJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFyQ047QUF3Q0k7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXRDTjtBQXlDSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBdkNOO0FBMkNJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF6Q047QUE0Q0k7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFDTjtBQTZDSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBM0NOO0FBOENJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUE1Q047QUErQ0k7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTdDTjtBQWdESTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBOUNOOztBQW9EQTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7QUFqREY7O0FBc0RBO0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBbkRGOztBQXNEQTtFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQW5ERjs7QUFzREE7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFuREY7O0FBc0RBO0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBbkRGOztBQXNEQTtFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQW5ERjs7QUF1REE7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFwREY7O0FBdURBO0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBcERGOztBQXVEQTtFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQXBERjs7QUF1REE7RUFDRSxvQ0FBQTtFQUNBLHlCQUFBO0VBQ0Esb0NBQUE7QUFwREY7O0FBdURBO0VBQ0Usb0NBQUE7RUFDQSx5QkFBQTtFQUNBLG9DQUFBO0FBcERGOztBQXdEQTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQXJERjs7QUF3REE7RUFDRSxnQ0FBQTtFQUNBLGlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQ0FBQTtBQXJERjtBQXVERTtFQUNFLHlCQUFBO0FBckRKO0FBd0RFO0VBQ0UsbUJBQUE7QUF0REo7O0FBMERBO0VBQ0UsYUFBQTtFQUNBLDREQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSx1QkFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7QUF2REY7QUF5REU7RUFDRSx5QkFBQTtFQUNBLDJCQUFBO0VBQ0Esd0NBQUE7QUF2REo7QUEwREU7RUFDRSxtQkFBQTtFQUNBLHlCQUFBO0FBeERKO0FBMkRFO0VBQ0UsU0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0FBekRKO0FBNERFOzs7OztFQUtFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0FBMURKO0FBNkRFO0VBQ0UsV0FBQTtFQUNBLHlCQUFBO0FBM0RKO0FBOERFO0VBQ0Usa0JBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7QUE1REo7QUErREk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTdETjtBQWdFSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBOUROO0FBaUVJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUEvRE47QUFrRUk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWhFTjtBQW1FSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBakVOO0FBb0VJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFsRU47QUFxRUk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQW5FTjtBQXNFSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBcEVOO0FBd0VJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF0RU47QUF5RUk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXZFTjtBQTBFSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBeEVOO0FBMkVJO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF6RU47QUE0RUk7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFFTjtBQThFRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTVFSjtBQStFRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTdFSjtBQWdGRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTlFSjtBQWlGRTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdEQUFBO0VBQ0EsbUJBQUE7QUEvRUo7QUFpRkk7RUFDRSxjQUFBO0VBQ0EscUJBQUE7QUEvRU47QUFrRkk7RUFDRSxjQUFBO0FBaEZOO0FBa0ZNO0VBQ0UsY0FBQTtBQWhGUjtBQW9GSTtFQUNFLGNBQUE7QUFsRk47QUFvRk07RUFDRSxjQUFBO0FBbEZSO0FBd0ZFO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsK0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBdEZKO0FBd0ZJO0VBQ0UsK0JBQUE7QUF0Rk47QUEyRkU7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGdCQUFBO0FBekZKO0FBMkZJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBekZOO0FBNEZJO0VBQ0Usc0JBQUE7QUExRk47QUErRkU7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGdCQUFBO0FBN0ZKO0FBK0ZJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBN0ZOO0FBZ0dJO0VBQ0Usc0JBQUE7QUE5Rk47QUFtR0U7RUFDRSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0FBakdKO0FBbUdJO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0FBakdOO0FBb0dJO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0FBbEdOO0FBb0dNO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBbEdSO0FBb0dRO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUFsR1Y7QUEwR0k7RUFDRSx5QkFBQTtFQUNBLHdDQUFBO0FBeEdOO0FBMkdJO0VBQ0UsZUFBQTtBQXpHTjtBQTRHSTtFQUNFLGdCQUFBO0FBMUdOO0FBK0dFO0VBQ0UsZ0JBQUE7RUFDQSw2QkFBQTtFQUNBLDZEQUFBO0VBQ0EsdUJBQUE7RUFDQSxtREFBQTtFQUNBLHlCQUFBO0FBN0dKO0FBK0dJO0VBQ0UsYUFBQTtFQUNBLG9DQUFBO0VBQ0EsdUJBQUE7RUFDQSxjQUFBO0VBQ0EsOENBQUE7QUE3R047QUFnSE07O0VBRUUsOEJBQUE7RUFDQSxpQ0FBQTtFQUNBLHdCQUFBO0VBQ0Esb0NBQUE7RUFDQSxxREFBQTtFQUNBLG9CQUFBO0FBOUdSO0FBZ0hROztFQUNFLDRCQUFBO0VBQ0EsMkJBQUE7RUFDQSx5QkFBQTtFQUNBLDhCQUFBO0VBQ0EsK0JBQUE7RUFDQSwyQ0FBQTtFQUNBLDZCQUFBO0VBQ0Esd0JBQUE7RUFDQSw4QkFBQTtFQUNBLG9CQUFBO0FBN0dWO0FBK0dVOztFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLHNEQUFBO0FBNUdaO0FBK0dVOztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxvREFBQTtFQUNBLGtCQUFBO0VBQ0EsNkNBQUE7QUE1R1o7QUFnSFE7O0VBQ0UsMkJBQUE7RUFDQSxvQ0FBQTtFQUNBLGlDQUFBO0VBQ0EsMEJBQUE7RUFDQSxxREFBQTtFQUNBLDZCQUFBO0VBQ0EsNERBQUE7RUFDQSwyQkFBQTtFQUNBLDhCQUFBO0FBN0dWO0FBK0dVOztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7RUFDQSxxREFBQTtFQUNBLGtDQUFBO0FBNUdaO0FBK0dVOztFQUNFLDJCQUFBO0VBQ0EsK0NBQUE7QUE1R1o7QUE4R1k7O0VBQ0UscURBQUE7QUEzR2Q7QUErR1U7O0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQ0FBQTtFQUNBLGtCQUFBO0FBNUdaO0FBOEdZOztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxvREFBQTtFQUNBLGtCQUFBO0FBM0dkO0FBOEdZOztFQUNFLGdFQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0VBQ0EsNkJBQUE7RUFDQSx3QkFBQTtFQUNBLDhCQUFBO0VBQ0Esa0NBQUE7RUFDQSw0QkFBQTtFQUNBLDJCQUFBO0VBQ0EseURBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0Esb0NBQUE7QUEzR2Q7QUE2R2M7O0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHFEQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHdDQUFBO0FBMUdoQjtBQTZHYztFQUNFO0lBQVcsbUJBQUE7SUFBcUIsWUFBQTtFQXpHOUM7RUEwR2M7SUFBTSxxQkFBQTtJQUF1QixZQUFBO0VBdEczQztBQUNGO0FBeUdZOztFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0FBdEdkO0FBd0djOztFQUNFLHdCQUFBO0VBQ0EsOEJBQUE7RUFDQSx1QkFBQTtFQUNBLDRCQUFBO0VBQ0Esa0NBQUE7RUFDQSwyQkFBQTtFQUNBLHVCQUFBO0VBQ0Esb0NBQUE7QUFyR2hCO0FBdUdnQjs7RUFDRSx1QkFBQTtFQUNBLGVBQUE7QUFwR2xCO0FBdUdnQjs7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDBCQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0FBcEdsQjtBQXVHZ0I7O0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxPQUFBO0FBcEdsQjtBQXVHZ0I7O0VBQ0UsY0FBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBcEdsQjtBQTJHWTs7RUFDRSxxQkFBQTtFQUNBLGtCQUFBO0FBeEdkO0FBMEdjOztFQUNFLGdCQUFBO0FBdkdoQjtBQTBHYzs7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDBCQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtBQXZHaEI7QUF5R2dCOztFQUNFLGNBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtBQXRHbEI7QUF5R2dCOztFQUNFLFlBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQ0FBQTtBQXRHbEI7QUF5R2dCO0VBQ0U7SUFBVyx3QkFBQTtFQXRHM0I7RUF1R2dCO0lBQU0sMEJBQUE7RUFwR3RCO0FBQ0Y7QUF1R2M7O0VBQ0UsMEJBQUE7RUFDQSx5QkFBQTtFQUNBLDJCQUFBO0VBQ0EsMkJBQUE7RUFDQSx3RUFBQTtFQUNBLGlDQUFBO0VBQ0Esb0NBQUE7RUFDQSwwREFBQTtFQUNBLDZCQUFBO0VBQ0Esb0NBQUE7QUFwR2hCO0FBc0dnQjs7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFVBQUE7RUFDQSxZQUFBO0VBQ0EscURBQUE7RUFDQSxrQ0FBQTtBQW5HbEI7QUFzR2dCOztFQUNFLGdEQUFBO0VBQ0EsMkJBQUE7QUFuR2xCO0FBc0dnQjs7RUFDRSw2REFBQTtFQUNBLHFCQUFBO0FBbkdsQjtBQXFHa0I7O0VBQ0UscURBQUE7QUFsR3BCO0FBc0dnQjs7RUFDRSw2REFBQTtFQUNBLHFCQUFBO0FBbkdsQjtBQXFHa0I7O0VBQ0UscURBQUE7QUFsR3BCO0FBc0dnQjs7RUFDRSw2REFBQTtFQUNBLHFCQUFBO0FBbkdsQjtBQXFHa0I7O0VBQ0UscURBQUE7QUFsR3BCO0FBcUdrQjs7RUFDRSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0VBQ0EscUNBQUE7QUFsR3BCO0FBcUdrQjtFQUNFO0lBQUssZ0NBQUE7RUFsR3ZCO0VBbUdrQjtJQUFNLG9DQUFBO0VBaEd4QjtFQWlHa0I7SUFBTyxrQ0FBQTtFQTlGekI7QUFDRjtBQWlHZ0I7O0VBQ0Usd0VBQUE7RUFDQSxnQ0FBQTtFQUNBLDhEQUFBO0FBOUZsQjtBQWdHa0I7O0VBQ0UsZ0VBQUE7RUFDQSxxQkFBQTtFQUNBLHVCQUFBO0VBQ0EsNkNBQUE7QUE3RnBCO0FBaUdnQjs7RUFDRSx3RUFBQTtFQUNBLGdDQUFBO0VBQ0EsOERBQUE7QUE5RmxCO0FBZ0drQjs7RUFDRSxnRUFBQTtFQUNBLHFCQUFBO0VBQ0EsdUJBQUE7RUFDQSw2Q0FBQTtBQTdGcEI7QUFvR1U7O0VBQ0UsV0FBQTtFQUNBLHFFQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBakdaO0FBbUdZOztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLG9EQUFBO0VBQ0Esa0JBQUE7RUFDQSw2Q0FBQTtBQWhHZDtBQTBHRTs7O0VBR0UsOEJBQUE7RUFDQSxpQ0FBQTtFQUNBLHdCQUFBO0VBQ0Esb0NBQUE7RUFDQSxxREFBQTtBQXhHSjtBQTBHSTs7O0VBQ0UsNEJBQUE7RUFDQSwyQkFBQTtFQUNBLHlCQUFBO0VBQ0EsOEJBQUE7RUFDQSwrQkFBQTtFQUNBLDJDQUFBO0VBQ0EsNkJBQUE7RUFDQSx3QkFBQTtFQUNBLDhCQUFBO0VBQ0Esb0JBQUE7QUF0R047QUF3R007OztFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLHNEQUFBO0FBcEdSO0FBdUdNOzs7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxXQUFBO0VBQ0Esb0RBQUE7RUFDQSxrQkFBQTtFQUNBLDZDQUFBO0FBbkdSO0FBdUdJOzs7RUFDRSwyQkFBQTtFQUNBLG9DQUFBO0VBQ0EsaUNBQUE7RUFDQSwwQkFBQTtFQUNBLHFEQUFBO0VBQ0EsNkJBQUE7RUFDQSw0REFBQTtFQUNBLDJCQUFBO0VBQ0EsOEJBQUE7QUFuR047QUFxR007OztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7RUFDQSxxREFBQTtFQUNBLGtDQUFBO0FBakdSO0FBb0dNOzs7RUFDRSwyQkFBQTtFQUNBLCtDQUFBO0FBaEdSO0FBa0dROzs7RUFDRSxxREFBQTtBQTlGVjtBQWtHTTs7O0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQ0FBQTtFQUNBLGtCQUFBO0FBOUZSO0FBZ0dROzs7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxXQUFBO0VBQ0Esb0RBQUE7RUFDQSxrQkFBQTtBQTVGVjtBQStGUTs7O0VBQ0UsZ0VBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSw2QkFBQTtFQUNBLHdCQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQ0FBQTtFQUNBLDRCQUFBO0VBQ0EsMkJBQUE7RUFDQSx5REFBQTtFQUNBLHlCQUFBO0VBQ0EsNkJBQUE7RUFDQSxvQ0FBQTtBQTNGVjtBQTZGVTs7O0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHFEQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHdDQUFBO0FBekZaO0FBNEZVO0VBQ0U7SUFBVyxtQkFBQTtJQUFxQixZQUFBO0VBeEYxQztFQXlGVTtJQUFNLHFCQUFBO0lBQXVCLFlBQUE7RUFyRnZDO0FBQ0Y7QUF3RlE7OztFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0FBcEZWO0FBc0ZVOzs7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLHlCQUFBO0FBbEZaO0FBb0ZZOzs7RUFDRSx1QkFBQTtFQUNBLGVBQUE7QUFoRmQ7QUFtRlk7OztFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsMEJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7QUEvRWQ7QUFrRlk7OztFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsT0FBQTtBQTlFZDtBQWlGWTs7O0VBQ0UsY0FBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBN0VkO0FBZ0ZZOzs7RUFFRSw4QkFBQTtBQTdFZDtBQW9GUTs7O0VBQ0UscUJBQUE7RUFDQSxrQkFBQTtBQWhGVjtBQWtGVTs7O0VBQ0UsZ0JBQUE7QUE5RVo7QUFpRlU7OztFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsMEJBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0FBN0VaO0FBK0VZOzs7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxXQUFBO0VBQ0Esa0JBQUE7QUEzRWQ7QUE4RVk7OztFQUNFLFlBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQ0FBQTtBQTFFZDtBQTZFWTtFQUNFO0lBQVcsd0JBQUE7RUExRXZCO0VBMkVZO0lBQU0sMEJBQUE7RUF4RWxCO0FBQ0Y7QUEyRVU7OztFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLDZEQUFBO0VBQ0Esc0JBQUE7RUFDQSx5QkFBQTtFQUNBLCtDQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQXZFWjtBQXlFWTs7O0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxVQUFBO0VBQ0EsWUFBQTtFQUNBLHFEQUFBO0VBQ0Esa0NBQUE7QUFyRWQ7QUF3RVk7OztFQUNFLGdEQUFBO0VBQ0EsMkJBQUE7QUFwRWQ7QUF1RVk7OztFQUNFLDZEQUFBO0VBQ0EscUJBQUE7QUFuRWQ7QUFxRWM7OztFQUNFLHFEQUFBO0FBakVoQjtBQXFFWTs7O0VBQ0UsNkRBQUE7RUFDQSxxQkFBQTtBQWpFZDtBQW1FYzs7O0VBQ0UscURBQUE7QUEvRGhCO0FBbUVZOzs7RUFDRSw2REFBQTtFQUNBLHFCQUFBO0FBL0RkO0FBaUVjOzs7RUFDRSxxREFBQTtBQTdEaEI7QUFnRWM7OztFQUNFLFlBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQ0FBQTtBQTVEaEI7QUErRGM7RUFDRTtJQUFLLGdDQUFBO0VBNURuQjtFQTZEYztJQUFNLG9DQUFBO0VBMURwQjtFQTJEYztJQUFPLGtDQUFBO0VBeERyQjtBQUNGO0FBOERNOzs7RUFDRSxXQUFBO0VBQ0EscUVBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUExRFI7QUE0RFE7OztFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLG9EQUFBO0VBQ0Esa0JBQUE7RUFDQSw2Q0FBQTtBQXhEVjtBQWdFSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0NBQUE7QUE5RE47QUFpRUk7RUFDRSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxhQUFBO0FBL0ROO0FBaUVNO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUEvRFI7QUFzRUk7RUFDRSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBcEVOO0FBc0VNO0VBQ0UsZUFBQTtFQUNBLHFCQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7QUFwRVI7QUF1RU07RUFDRSxpQkFBQTtBQXJFUjtBQTJFRTtFQUNFLGdCQUFBO0VBQ0EseUJBQUE7RUFDQSx1QkFBQTtBQXpFSjtBQTJFSTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EscUJBQUE7QUF6RU47QUE0RUk7RUFDRSx5QkFBQTtFQUNBLHVCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTFFTjtBQTRFTTtFQUNFLHFCQUFBO0VBQ0EsaURBQUE7QUExRVI7QUE4RUk7RUFDRSx5QkFBQTtFQUNBLHFCQUFBO0VBQ0EsbUJBQUE7RUFDQSxvQkFBQTtBQTVFTjtBQThFTTtFQUNFLHlCQUFBO0VBQ0EscUJBQUE7QUE1RVI7O0FBbUZFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLHNCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBaEZKO0FBa0ZJOztFQUVFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUFoRk47QUFrRk07O0VBQ0UsV0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtBQS9FUjtBQW1GSTtFQUNFLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxzQkFBQTtFQUNBLGdCQUFBO0FBakZOOztBQXNGQTtFQUNFLGFBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7QUFuRkY7O0FBdUZBO0VBSU07O0lBQ0Usc0JBQUE7SUFDQSxXQUFBO0VBdEZOO0VBMkZBO0lBQ0UsNERBQUE7SUFDQSxXQUFBO0lBQ0EsaUJBQUE7RUF6RkY7RUEyRkU7SUFDRSxpQkFBQTtJQUNBLHNCQUFBO0VBekZKO0VBNEZFOzs7SUFHRSxrQkFBQTtFQTFGSjtFQTZGRTtJQUNFLGlCQUFBO0VBM0ZKO0FBQ0Y7QUErRkE7RUFDRTtJQUNFLDBEQUFBO0lBQ0EsWUFBQTtJQUNBLGdCQUFBO0VBN0ZGO0VBK0ZFO0lBQ0Usa0JBQUE7SUFDQSx1QkFBQTtFQTdGSjtFQWdHRTs7O0lBR0UsaUJBQUE7RUE5Rko7RUFpR0U7SUFDRSxpQkFBQTtFQS9GSjtBQUNGO0FBbUdBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtBQWpHRjs7QUFvR0E7RUFDRSxhQUFBO0VBQ0EscUJBQUE7RUFDQSxZQUFBO0FBakdGOztBQW9HQTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBakdGOztBQW9HQTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQkFBQTtFQUNBLDZCQUFBO0VBQ0EsdUJBQUE7RUFBQSxrQkFBQTtBQWpHRjtBQW9HRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBbEdKO0FBcUdFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFuR0o7QUFzR0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXBHSjtBQXVHRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBckdKO0FBd0dFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF0R0o7QUF5R0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQXZHSjtBQTBHRTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBeEdKO0FBMkdFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF6R0o7QUE0R0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFHSjs7QUE4R0E7RUFDRSxrQkFBQTtFQUNBLGNBQUE7RUFDQSx1QkFBQTtBQTNHRjs7QUErR0E7RUFDRSxlQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0FBNUdGO0FBOEdFO0VBQ0Usa0JBQUE7RUFDQSxjQUFBO0VBQ0EsYUFBQTtBQTVHSjs7QUFpSEE7O0VBRUUsd0VBQUE7RUFDQSxnQ0FBQTtFQUNBLDBEQUFBO0FBOUdGO0FBZ0hFOztFQUNFLGdFQUFBO0VBQ0EscUJBQUE7RUFDQSx1QkFBQTtFQUNBLDZDQUFBO0FBN0dKOztBQWtIQSxtQkFBQTtBQUNBO0VBQ0UsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLDBDQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtBQS9HRjs7QUFrSEEsV0FBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxxQkFBQTtFQUNBLGdDQUFBO0VBQ0Esc0JBQUE7QUEvR0Y7O0FBa0hBO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7QUEvR0Y7O0FBa0hBO0VBQ0UsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxlQUFBO0VBQ0EsZ0NBQUE7QUEvR0Y7O0FBaUhBO0VBQ0UsbUJBQUE7QUE5R0Y7O0FBaUhBLFNBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7QUE5R0Y7O0FBaUhBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0FBOUdGOztBQWlIQTtFQUNFLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0FBOUdGOztBQWlIQTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQTlHRjs7QUFpSEEsMkJBQUE7QUFDQTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtBQTlHRiIsInNvdXJjZXNDb250ZW50IjpbIi5wZXJtaXQtdmlldy1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDA7XG59XG5cbi8vIFBlcm1pdCBEZXRhaWxzIEhlYWRlclxuLnBlcm1pdC1kZXRhaWxzLWhlYWRlciB7XG4gIHBhZGRpbmc6IDAgMS41cmVtO1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZWFlZTtcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG5cbiAgLmhlYWRlci1jb250ZW50IHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIHBhZGRpbmctdG9wOiAwLjVyZW07XG4gIH1cblxuICBoNCB7XG4gICAgbWFyZ2luOiAwO1xuICAgIGNvbG9yOiAjM2Y0MjU0O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgZm9udC1zaXplOiAxLjFyZW07XG4gIH1cblxuICAuYnV0dG9uLWdyb3VwIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGdhcDogMC41cmVtO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWxlZnQ6IGF1dG87IC8qIEVuc3VyZSBpdCBzdGF5cyBhdCB0aGUgcmlnaHQgZW5kICovXG5cbiAgICAuYnRuLXNtIHtcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW0gIWltcG9ydGFudDtcbiAgICAgIHBhZGRpbmc6IDAuMzc1cmVtIDAuNzVyZW0gIWltcG9ydGFudDtcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjUgIWltcG9ydGFudDtcbiAgICB9XG4gIH1cblxuICAuYmFjay1idXR0b24sXG4gIC5wb3J0YWwtYnV0dG9uIHtcbiAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogLjNyZW07XG4gICAgcGFkZGluZzogLjE1cmVtIC41cmVtOyAvKiBzbWFsbGVyIHdpdGggZXhwbGljaXQgdG9wL2JvdHRvbSBwYWRkaW5nICovXG4gICAgYm9yZGVyLXJhZGl1czogLjU1cmVtO1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNmM2Y2Zjk7XG4gICAgY29sb3I6ICMzZjQyNTQ7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZTtcbiAgICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgwLDAsMCwwLjA1KTtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGZvbnQtc2l6ZTogLjhyZW07XG4gICAgbGluZS1oZWlnaHQ6IDE7IC8qIGtlZXAgY29tcGFjdCAqL1xuICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgLjJzIGVhc2UsIGJveC1zaGFkb3cgLjJzIGVhc2UsIHRyYW5zZm9ybSAuMDJzIGVhc2U7XG5cbiAgICBpIHtcbiAgICAgIGNvbG9yOiAjNWU2ZTgyO1xuICAgICAgZm9udC1zaXplOiAuNzVyZW07XG4gICAgfVxuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWVmMmY3O1xuICAgICAgYm94LXNoYWRvdzogMCAzcHggMTBweCByZ2JhKDAsMCwwLDAuMDcpO1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgIH1cblxuICAgICY6YWN0aXZlIHtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxcHgpO1xuICAgICAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwwLDAsMC4wNik7XG4gICAgfVxuICB9XG5cbiAgLnBvcnRhbC1idXR0b24ge1xuICAgIGkge1xuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xuICAgIH1cbiAgfVxufVxuXG4vLyBUYWIgcm93IHJpZ2h0IHNpZGUgYnV0dG9ucyBzdHlsaW5nXG4uY2FyZC1oZWFkZXIge1xuICAuZC1mbGV4LmFsaWduLWl0ZW1zLWNlbnRlci5nYXAtMiB7XG4gICAgLmJ0bi1saW5rIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBoZWlnaHQ6IDJyZW07IC8qIEZpeGVkIGhlaWdodCBmb3IgY29uc2lzdGVudCBhbGlnbm1lbnQgKi9cbiAgICAgIFxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xuICAgICAgICBjb2xvcjogIzM2OTlmZiAhaW1wb3J0YW50O1xuICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgICB9XG4gICAgICBcbiAgICAgICY6Zm9jdXMge1xuICAgICAgICBib3gtc2hhZG93OiBub25lO1xuICAgICAgICBvdXRsaW5lOiBub25lO1xuICAgICAgfVxuICAgICAgXG4gICAgICBpIHtcbiAgICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4ycyBlYXNlO1xuICAgICAgICBsaW5lLWhlaWdodDogMTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgJjpob3ZlciBpIHtcbiAgICAgICAgY29sb3I6ICMzNjk5ZmYgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gRGlzYWJsZWQgYnV0dG9uIHN0eWxpbmdcbi5idG46ZGlzYWJsZWQge1xuICBvcGFjaXR5OiAwLjY7XG4gIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjUgIWltcG9ydGFudDtcbiAgY29sb3I6ICM5OTkgIWltcG9ydGFudDtcbiAgYm9yZGVyLWNvbG9yOiAjZGRkICFpbXBvcnRhbnQ7XG4gIFxuICAmOmhvdmVyIHtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1ICFpbXBvcnRhbnQ7XG4gICAgY29sb3I6ICM5OTkgIWltcG9ydGFudDtcbiAgICBib3JkZXItY29sb3I6ICNkZGQgIWltcG9ydGFudDtcbiAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XG4gIH1cbn1cbi5wZXJtaXQtZGV0YWlscy1jYXJkIHtcbiAgYmFja2dyb3VuZDogI2ZmZjtcbiAgLy8gYm9yZGVyLXJhZGl1czogMC43NXJlbTtcbiAgLy8gYm94LXNoYWRvdzogMCA0cHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xuICBtYXJnaW46IDFyZW0gMDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLnBlcm1pdC1kZXRhaWxzLWhlYWRlciB7XG4gIGJhY2tncm91bmQ6ICNmNWY2Zjg7XG4gIHBhZGRpbmc6IDAuNzVyZW0gMS4yNXJlbTtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlM2U2ZWE7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbn1cblxuLnBlcm1pdC1kZXRhaWxzLWhlYWRlciBoNCB7XG4gIG1hcmdpbjogMDtcbiAgZm9udC1zaXplOiAxcmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzMzMztcbn1cblxuLy8gTm90ZXMvQWN0aW9ucyBlZGl0IGJ1dHRvbiBzdHlsaW5nIHRvIG1hdGNoIHRhYiByb3cgZWRpdCBpY29uXG4ucGVybWl0LWRldGFpbHMtY2FyZCAucGVybWl0LWRldGFpbHMtaGVhZGVyIHtcbiAgLmJ0bi1saW5rIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgaGVpZ2h0OiAycmVtOyAvKiBTYW1lIGhlaWdodCBhcyB0YWIgcm93IGVkaXQgaWNvbiAqL1xuICAgIFxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gICAgICBjb2xvcjogIzM2OTlmZiAhaW1wb3J0YW50O1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgIH1cbiAgICBcbiAgICAmOmZvY3VzIHtcbiAgICAgIGJveC1zaGFkb3c6IG5vbmU7XG4gICAgICBvdXRsaW5lOiBub25lO1xuICAgIH1cbiAgICBcbiAgICBpIHtcbiAgICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZTtcbiAgICAgIGxpbmUtaGVpZ2h0OiAxO1xuICAgIH1cbiAgICBcbiAgICAmOmhvdmVyIGkge1xuICAgICAgY29sb3I6ICMzNjk5ZmYgIWltcG9ydGFudDtcbiAgICB9XG4gIH1cbn1cbi5lZGl0LWJ0biB7XG4gIGJhY2tncm91bmQ6ICMxYjdlNmM7XG4gIGNvbG9yOiAjZmZmO1xuICBib3JkZXI6IG5vbmU7XG4gIGJvcmRlci1yYWRpdXM6IDZweDtcbiAgcGFkZGluZzogMC4zNXJlbSAwLjc1cmVtO1xuICBmb250LXNpemU6IDAuODVyZW07XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiA0cHg7XG4gIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4ycyBlYXNlO1xufVxuXG4uZWRpdC1idG46aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjMTY2MzU0O1xufVxuLnBlcm1pdC1kZXRhaWxzLWNvbnRlbnQge1xuICBwYWRkaW5nOiAxcmVtIDEuNXJlbTtcbiAgICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xuICBib3gtc2hhZG93OiAwIDRweCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gIG1hcmdpbjogMXJlbSAwO1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuXG4ucGVybWl0LWRldGFpbHMtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMmZyIDFmciAxZnIgMWZyOyAvKiBNYWtlIFByb2plY3QgTmFtZSB3aWRlciAqL1xuICBnYXA6IDEuNXJlbTtcbn1cblxuLy8gTm90ZXMgJiBBY3Rpb25zIGZ1bGwtd2lkdGggbGF5b3V0XG4ubm90ZXMtYWN0aW9ucy1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDEuNXJlbTtcbn1cblxuLnBlcm1pdC1kZXRhaWwtaXRlbS1mdWxsIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAwLjRyZW07XG4gIHdpZHRoOiAxMDAlO1xuXG4gIGxhYmVsIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjb2xvcjogIzZjNzI5MztcbiAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcbiAgICBsZXR0ZXItc3BhY2luZzogMC4xcmVtO1xuICAgIG1hcmdpbjogMDtcbiAgfVxuXG4gIC5wZXJtaXQtdmFsdWUge1xuICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICBjb2xvcjogIzNmNDI1NDtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIHBhZGRpbmc6IDAuNXJlbSAwO1xuICAgIGJvcmRlci1ib3R0b206IG5vbmU7XG4gICAgd29yZC13cmFwOiBicmVhay13b3JkO1xuICAgIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDtcbiAgfVxufVxuXG4ucGVybWl0LWRldGFpbC1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAwLjRyZW07XG5cbiAgbGFiZWwge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGNvbG9yOiAjMTExMTExO1xuICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xuICAgIGxldHRlci1zcGFjaW5nOiAwLjFyZW07XG4gICAgbWFyZ2luOiAwO1xuICB9XG5cbiAgLnBlcm1pdC12YWx1ZSxcbiAgLnBlcm1pdC1zdGF0dXMge1xuICAgIG1hcmdpbi10b3A6IDAuMXJlbTtcbiAgfVxuXG4gIC5wZXJtaXQtdmFsdWUge1xuICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICBjb2xvcjogIzNmNDI1NDtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIHBhZGRpbmc6IDAuNXJlbSAwO1xuICAgIGJvcmRlci1ib3R0b206IG5vbmU7IC8qIHJlbW92ZSBkaXZpZGVyIGxpbmUgKi9cbiAgfVxuXG4gIC5wZXJtaXQtc3RhdHVzIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgICB2ZXJ0aWNhbC1hbGlnbjogdG9wO1xuICAgIHBhZGRpbmc6IDAuNXJlbSAwOyAvKiBtYXRjaCBzcGFjaW5nIHdpdGggb3RoZXIgZmllbGRzICovXG4gICAgZm9udC1zaXplOiAxcmVtO1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgdGV4dC1hbGlnbjogbGVmdDsgLyogYWxpZ24gZnJvbSBsZWZ0ICovXG4gICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7IC8qIG5vIGJhZGdlIGJhY2tncm91bmQgKi9cbiAgICBib3JkZXI6IG5vbmU7IC8qIG5vIGJvcmRlciAqL1xuICAgIG1pbi13aWR0aDogMDsgLyogYWxsb3cgbmF0dXJhbCB3aWR0aCAqL1xuICAgIGJvcmRlci1yYWRpdXM6IDA7XG4gIH1cblxuICAvLyBTdGF0dXMgY29sb3IgdmFyaWF0aW9uc1xuICAuc3RhdHVzLWFwcHJvdmVkIHtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xuICAgIGNvbG9yOiAjMWI1ZTIwO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XG4gIH1cblxuICAuc3RhdHVzLXBlbmRpbmcge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzZTA7XG4gICAgY29sb3I6ICNlNjUxMDA7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2ZmY2MwMjtcbiAgfVxuXG4gIC5zdGF0dXMtcmVqZWN0ZWQge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmViZWU7XG4gICAgY29sb3I6ICNjNjI4Mjg7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2ZmY2RkMjtcbiAgfVxuXG4gIC5zdGF0dXMtc3VibWl0dGVkIHtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTNmMmZkO1xuICAgIGNvbG9yOiAjMTU2NWMwO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNiYmRlZmI7XG4gIH1cblxuICAuc3RhdHVzLXZvaWQge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gICAgY29sb3I6ICM3NTc1NzU7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcbiAgfVxuXG4gIC5zdGF0dXMtdW5rbm93biB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgICBjb2xvcjogIzc1NzU3NTtcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xuICB9XG5cbiAgLnN0YXR1cy1yZXF1aXJlcy1yZXN1Ym1pdCB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjhlMTtcbiAgICBjb2xvcjogI2Y1N2YxNztcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZlY2IzO1xuICB9XG5cbiAgLnN0YXR1cy1jb25kaXRpb25hbC1hcHByb3ZhbCB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2YzZTVmNTtcbiAgICBjb2xvcjogIzdiMWZhMjtcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTFiZWU3O1xuICB9XG5cbiAgLnN0YXR1cy11bmRlci1yZXZpZXcge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjY7XG4gICAgY29sb3I6ICMzOTQ5YWI7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2M1Y2FlOTtcbiAgfVxufVxuXG4vLyBMb2FkaW5nIFNlY3Rpb25cbi5sb2FkaW5nLXNlY3Rpb24ge1xuICBiYWNrZ3JvdW5kOiAjZmZmO1xuICBib3JkZXItcmFkaXVzOiAuNDc1cmVtO1xuICBwYWRkaW5nOiAycmVtO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNlNWVhZWU7XG5cbiAgLnNwaW5uZXItYm9yZGVyIHtcbiAgICB3aWR0aDogM3JlbTtcbiAgICBoZWlnaHQ6IDNyZW07XG4gIH1cblxuICBwIHtcbiAgICBjb2xvcjogIzZjNzI5MztcbiAgICBtYXJnaW46IDA7XG4gIH1cblxuICAubG9hZGluZy10aXBzIHtcbiAgICAudGV4dC1tdXRlZCB7XG4gICAgICBjb2xvcjogI2ExYTViNyAhaW1wb3J0YW50O1xuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcblxuICAgICAgaSB7XG4gICAgICAgIG1hcmdpbi1yaWdodDogMC41cmVtO1xuICAgICAgICBjb2xvcjogIzM2OTlmZjtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuXG5cbi8vIE5vIFBlcm1pdCBEYXRhIE1lc3NhZ2Vcbi5uby1wZXJtaXQtZGF0YSB7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG5cbiAgLmFsZXJ0IHtcbiAgICBib3JkZXItcmFkaXVzOiAuNDc1cmVtO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWVhZWU7XG5cbiAgICBoNSB7XG4gICAgICBtYXJnaW46IDAgMCAwLjVyZW0gMDtcbiAgICAgIGNvbG9yOiAjM2Y0MjU0O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB9XG5cbiAgICBwIHtcbiAgICAgIG1hcmdpbjogMCAwIDAuNXJlbSAwO1xuICAgICAgY29sb3I6ICM2YzcyOTM7XG4gICAgfVxuXG4gICAgdWwge1xuICAgICAgbWFyZ2luOiAwIDAgMXJlbSAwO1xuICAgICAgcGFkZGluZy1sZWZ0OiAxLjVyZW07XG4gICAgICBjb2xvcjogIzZjNzI5MztcblxuICAgICAgbGkge1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5idG4ge1xuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xuXG4gICAgICBpIHtcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiAwLjVyZW07XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIFRhYmxlIFN0eWxpbmcgZm9yIFJldmlld3Ncbi50YWJsZS1yZXNwb25zaXZlIHtcbiAgb3ZlcmZsb3cteDogYXV0bztcbiAgcGFkZGluZzogMCAwLjVyZW0gMCAwLjVyZW07XG4gIG1hcmdpbi10b3A6IDA7XG59XG5cbi8vIFJlbW92ZSB0b3AgcGFkZGluZyBmcm9tIGV4dGVybmFsIHJldmlld3MgdGFibGVcbi5leHRlcm5hbC1yZXZpZXdzLXRhYmxlIHtcbiAgcGFkZGluZy10b3A6IDAgIWltcG9ydGFudDtcbiAgbWFyZ2luLXRvcDogMCAhaW1wb3J0YW50O1xufVxuXG4vLyBSZW1vdmUgdG9wIHBhZGRpbmcgZnJvbSB0aGUgY2FyZCBib2R5IGNvbnRhaW5pbmcgdGhlIGV4dGVybmFsIHJldmlld3Ncbi5jYXJkLWJvZHkge1xuICBwYWRkaW5nLXRvcDogMCAhaW1wb3J0YW50O1xufVxuXG4udGFibGUge1xuICBtYXJnaW4tYm90dG9tOiAwO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuXG4gIC5hdWRpdC10YWJsZS1yb3csXG4gIC5leHRlcm5hbC1zdWJtaXR0YWwtcm93IHtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZWFlZTtcbiAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmO1xuICAgIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG4gICAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xuICAgIH1cblxuICAgICYuZXhwYW5kZWQge1xuICAgICAgYmFja2dyb3VuZDogI2UzZjJmZDtcbiAgICAgIGJvcmRlci1jb2xvcjogIzIxOTZmMztcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDMzLCAxNTAsIDI0MywgMC4yKTtcbiAgICB9XG5cbiAgICAmLnRhYmxlLWFjdGl2ZSB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTNmMmZkO1xuICAgIH1cbiAgfVxuXG4gIC5hdWRpdC10aXRsZS1jZWxsLFxuICAuc3VibWl0dGFsLXRpdGxlLWNlbGwge1xuICAgIHdpZHRoOiA3NSU7XG4gICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTtcblxuICAgIC5hdWRpdC1zdGF0dXMtYmFkZ2UsXG4gICAgLnN1Ym1pdHRhbC1zdGF0dXMtYmFkZ2Uge1xuICAgICAgZm9udC1zaXplOiAwLjdyZW07XG4gICAgICBwYWRkaW5nOiAwLjJyZW0gMC40cmVtO1xuICAgICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDtcbiAgICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gICAgICBsZXR0ZXItc3BhY2luZzogMC4wNXJlbTtcbiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgICAgIG1pbi13aWR0aDogNjBweDtcbiAgICAgIFxuICAgICAgLy8gRW5zdXJlIHN0YXR1cyBjbGFzc2VzIGFyZSBhcHBsaWVkIHdpdGggaGlnaGVyIHNwZWNpZmljaXR5XG4gICAgICAmLnN0YXR1cy0xLWN5Y2xlLWNvbXBsZXRlZCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTggIWltcG9ydGFudDtcbiAgICAgICAgY29sb3I6ICMyZTdkMzIgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2E1ZDZhNyAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgICAgXG4gICAgICAmLnN0YXR1cy1wYWNpZmljYS12ZXJpZmljYXRpb24ge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTFmNWZlICFpbXBvcnRhbnQ7XG4gICAgICAgIGNvbG9yOiAjMDI3N2JkICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICM4MWQ0ZmEgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgJi5zdGF0dXMtZGlzLWFwcHJvdmVkIHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZWJlZSAhaW1wb3J0YW50O1xuICAgICAgICBjb2xvcjogI2M2MjgyOCAhaW1wb3J0YW50O1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjZGQyICFpbXBvcnRhbnQ7XG4gICAgICB9XG4gICAgICBcbiAgICAgICYuc3RhdHVzLW5vdC1yZXF1aXJlZCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjUgIWltcG9ydGFudDtcbiAgICAgICAgY29sb3I6ICM3NTc1NzUgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMCAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgICAgXG4gICAgICAmLnN0YXR1cy1pbi1yZXZpZXcge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThlYWY2ICFpbXBvcnRhbnQ7XG4gICAgICAgIGNvbG9yOiAjMzk0OWFiICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjNWNhZTkgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcblxuICAgICAgLy8gU3RhdHVzIGNvbG9yIHZhcmlhdGlvbnNcbiAgICAgICYuc3RhdHVzLWFwcHJvdmVkIHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjVlODtcbiAgICAgICAgY29sb3I6ICMxYjVlMjA7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XG4gICAgICB9XG5cbiAgICAgICYuc3RhdHVzLXBlbmRpbmcge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmM2UwO1xuICAgICAgICBjb2xvcjogI2U2NTEwMDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2ZmY2MwMjtcbiAgICAgIH1cblxuICAgICAgJi5zdGF0dXMtdW5kZXItcmV2aWV3IHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZWFmNjtcbiAgICAgICAgY29sb3I6ICMzOTQ5YWI7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjNWNhZTk7XG4gICAgICB9XG5cbiAgICAgICYuc3RhdHVzLXJlamVjdGVkIHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZWJlZTtcbiAgICAgICAgY29sb3I6ICNjNjI4Mjg7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDI7XG4gICAgICB9XG5cbiAgICAgICYuc3RhdHVzLXJlcXVpcmVzLXJlLXN1Ym1pdCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY4ZTE7XG4gICAgICAgIGNvbG9yOiAjZjU3ZjE3O1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZlY2IzO1xuICAgICAgfVxuXG4gICAgICAmLnN0YXR1cy1hcHByb3ZlZC13LWNvbmRpdGlvbnMge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNlNWY1O1xuICAgICAgICBjb2xvcjogIzdiMWZhMjtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UxYmVlNztcbiAgICAgIH1cblxuICAgICAgJi5zdGF0dXMtY29tcGxldGUge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xuICAgICAgICBjb2xvcjogIzFiNWUyMDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M4ZTZjOTtcbiAgICAgIH1cblxuICAgICAgJi5zdGF0dXMtbi1hIHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgICAgICAgY29sb3I6ICM3NTc1NzU7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XG4gICAgICB9XG5cbiAgICAgIC8vIEludGVybmFsIFZlcmlmaWNhdGlvbiBTdGF0dXMgc3R5bGVzXG4gICAgICAmLnN0YXR1cy1wZW5kaW5nIHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjNlMDtcbiAgICAgICAgY29sb3I6ICNlNjUxMDA7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNjMDI7XG4gICAgICB9XG5cbiAgICAgICYuc3RhdHVzLWluLXByb2dyZXNzIHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcbiAgICAgICAgY29sb3I6ICMxNTY1YzA7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICM5MGNhZjk7XG4gICAgICB9XG5cbiAgICAgICYuc3RhdHVzLWNvbXBsZXRlZCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTg7XG4gICAgICAgIGNvbG9yOiAjMWI1ZTIwO1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjYzhlNmM5O1xuICAgICAgfVxuXG4gICAgICAmLnN0YXR1cy12ZXJpZmllZCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTg7XG4gICAgICAgIGNvbG9yOiAjMmU3ZDMyO1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjYTVkNmE3O1xuICAgICAgfVxuXG4gICAgICAmLnN0YXR1cy1yZWplY3RlZCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmViZWU7XG4gICAgICAgIGNvbG9yOiAjYzYyODI4O1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjZGQyO1xuICAgICAgfVxuXG4gICAgICAmLnN0YXR1cy1hcHByb3ZlZCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTg7XG4gICAgICAgIGNvbG9yOiAjMWI1ZTIwO1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjYzhlNmM5O1xuICAgICAgfVxuXG4gICAgICAmLnN0YXR1cy11bmRlci1yZXZpZXcge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThlYWY2O1xuICAgICAgICBjb2xvcjogIzM5NDlhYjtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M1Y2FlOTtcbiAgICAgIH1cblxuICAgICAgJi5zdGF0dXMtcmVxdWlyZXMtcmVzdWJtaXQge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOGUxO1xuICAgICAgICBjb2xvcjogI2Y1N2YxNztcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2ZmZWNiMztcbiAgICAgIH1cblxuICAgICAgJi5zdGF0dXMtb24taG9sZCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmM2U1ZjU7XG4gICAgICAgIGNvbG9yOiAjN2IxZmEyO1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTFiZWU3O1xuICAgICAgfVxuXG4gICAgICAvLyBBZGRpdGlvbmFsIHNwZWNpZmljIHN0YXR1cyBzdHlsZXNcbiAgICAgICYuc3RhdHVzLXBhY2lmaWNhLXZlcmlmaWNhdGlvbiB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlMWY1ZmUgIWltcG9ydGFudDtcbiAgICAgICAgY29sb3I6ICMwMjc3YmQgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzgxZDRmYSAhaW1wb3J0YW50O1xuICAgICAgfVxuXG4gICAgICAmLnN0YXR1cy1kaXMtYXBwcm92ZWQge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlICFpbXBvcnRhbnQ7XG4gICAgICAgIGNvbG9yOiAjYzYyODI4ICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDIgIWltcG9ydGFudDtcbiAgICAgIH1cblxuICAgICAgJi5zdGF0dXMtbm90LXJlcXVpcmVkIHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNSAhaW1wb3J0YW50O1xuICAgICAgICBjb2xvcjogIzc1NzU3NSAhaW1wb3J0YW50O1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwICFpbXBvcnRhbnQ7XG4gICAgICB9XG5cbiAgICAgICYuc3RhdHVzLWluLXJldmlldyB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjYgIWltcG9ydGFudDtcbiAgICAgICAgY29sb3I6ICMzOTQ5YWIgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M1Y2FlOSAhaW1wb3J0YW50O1xuICAgICAgfVxuXG4gICAgICAmLnN0YXR1cy0xLWN5Y2xlLWNvbXBsZXRlZCB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTggIWltcG9ydGFudDtcbiAgICAgICAgY29sb3I6ICMyZTdkMzIgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2E1ZDZhNyAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5hdWRpdC1kYXRlcy1jZWxsLFxuICAuc3VibWl0dGFsLWRhdGVzLWNlbGwge1xuICAgIHdpZHRoOiAyNSU7XG4gICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTtcbiAgICB0ZXh0LWFsaWduOiByaWdodDtcbiAgICBwYWRkaW5nLXJpZ2h0OiAwO1xuXG4gICAgLmQtZmxleCB7XG4gICAgICBnYXA6IDFyZW07XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xuICAgIH1cblxuICAgIC5kYXRlLWl0ZW0ge1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgd2lkdGg6IDEyMHB4OyAvKiBmaXhlZCB3aWR0aCB0byBhbGlnbiBjb2x1bW5zICovXG4gICAgICBtaW4td2lkdGg6IDEyMHB4O1xuICAgICAgbWF4LXdpZHRoOiAxMjBweDtcblxuICAgICAgc21hbGwge1xuICAgICAgICBmb250LXNpemU6IDAuN3JlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgZGlzcGxheTogYmxvY2s7IC8qIGVuc3VyZSBsYWJlbCB0YWtlcyBpdHMgb3duIGxpbmUgKi9cbiAgICAgIH1cblxuICAgICAgLmZ3LW1lZGl1bSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcbiAgICAgICAgZGlzcGxheTogYmxvY2s7IC8qIGZvcmNlIGRhdGUgdGV4dCB0byBhbGlnbiB2ZXJ0aWNhbGx5ICovXG4gICAgICAgIG1pbi1oZWlnaHQ6IDEuMjVyZW07IC8qIHJlc2VydmUgc3BhY2Ugd2hlbiBlbXB0eSAqL1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG5cbiAgLmF1ZGl0LWFjdGlvbnMtY2VsbCB7XG4gICAgd2lkdGg6IDE1JTtcbiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcblxuICAgIC5hY3Rpb24taWNvbiB7XG4gICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICBjb2xvcjogIzM2OTlmZjtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZSwgdHJhbnNmb3JtIDAuMnMgZWFzZTtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGNvbG9yOiAjMmI3Y2UwO1xuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XG4gICAgICB9XG5cbiAgICAgICYuZWRpdC1pY29uIHtcbiAgICAgICAgY29sb3I6ICMzNjk5ZmY7XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgY29sb3I6ICMyYjdjZTA7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuYWNjb3JkaW9uLXRvZ2dsZSB7XG4gICAgY29sb3I6ICMzNjk5ZmY7XG4gICAgZm9udC1zaXplOiAwLjhyZW07XG4gICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgd2lkdGg6IDIwcHg7XG4gICAgaGVpZ2h0OiAyMHB4O1xuXG4gICAgaSB7XG4gICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xuICAgIH1cbiAgfVxuXG4gIC5hY2NvcmRpb24tY29udGVudC1yb3cge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmYgMCUsICNlOGYyZmYgMTAwJSk7XG4gICAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMzY5OWZmO1xuICAgIGJveC1zaGFkb3c6IGluc2V0IDAgMnB4IDRweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4xKTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICAgLmFjY29yZGlvbi1jb250ZW50IHtcbiAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZDFlN2ZmO1xuICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG5cbiAgICAgIC5yZXZpZXdzLWNvbnRhaW5lciB7XG4gICAgICAgIG1heC1oZWlnaHQ6IDQwMHB4O1xuICAgICAgICBvdmVyZmxvdy15OiBhdXRvO1xuICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyk7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xuICAgICAgICBtYXJnaW46IDAuNXJlbTtcbiAgICAgICAgcGFkZGluZzogMXJlbTtcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjE1KTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBIaWdobGlnaHQgdGhlIHBhcmVudCBzdWJtaXR0YWwgcm93IHdoZW4gZXhwYW5kZWRcbiAgLmV4dGVybmFsLXN1Ym1pdHRhbC1yb3c6aGFzKCsgLmFjY29yZGlvbi1jb250ZW50LXJvdzpub3QoLmQtbm9uZSkpIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjBmOGZmIDAlLCAjZThmMmZmIDEwMCUpO1xuICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4yKTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgIG1hcmdpbi1yaWdodDogMDtcblxuICAgIC5zdWJtaXR0YWwtdGl0bGUtY2VsbCB7XG4gICAgICAuYWNjb3JkaW9uLXRvZ2dsZSB7XG4gICAgICAgIGNvbG9yOiAjMmI3Y2UwO1xuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gQWRkIHN1YnRsZSBhbmltYXRpb24gZm9yIGV4cGFuc2lvblxuICAuZXh0ZXJuYWwtc3VibWl0dGFsLXJvdyB7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcbiAgICB9XG4gIH1cbn1cblxuLnNlY3Rpb24taGVhZGVyIHtcbiAgcGFkZGluZzogMXJlbSAxLjVyZW07XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlYWVlO1xuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICBib3JkZXItcmFkaXVzOiAuNDc1cmVtIC40NzVyZW0gMCAwO1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cbiAgaDUge1xuICAgIG1hcmdpbjogMDtcbiAgICBjb2xvcjogIzNmNDI1NDtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgfVxuXG4gIC5zZWN0aW9uLWFjdGlvbnMge1xuICAgIC5idG4ge1xuICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xuICAgICAgcGFkZGluZzogMC4zNzVyZW0gMC43NXJlbTtcbiAgICB9XG4gIH1cblxuICAuc3VibWl0dGFsLXN0YXR1cyB7XG4gICAgZm9udC1zaXplOiAwLjc1cmVtO1xuICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xuICAgIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDtcblxuICAgIC8vIFN0YXR1cyBjb2xvciB2YXJpYXRpb25zIGZvciBzdWJtaXR0YWwgc3RhdHVzXG4gICAgJi5zdGF0dXMtYXBwcm92ZWQge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjVlODtcbiAgICAgIGNvbG9yOiAjMWI1ZTIwO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M4ZTZjOTtcbiAgICB9XG5cbiAgICAmLnN0YXR1cy1wZW5kaW5nIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzZTA7XG4gICAgICBjb2xvcjogI2U2NTEwMDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNjMDI7XG4gICAgfVxuXG4gICAgJi5zdGF0dXMtdW5kZXItcmV2aWV3IHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjY7XG4gICAgICBjb2xvcjogIzM5NDlhYjtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjNWNhZTk7XG4gICAgfVxuXG4gICAgLy8gTWFwIEFQSSBzdGF0dXMgXCJJbiBSZXZpZXdcIiDDosKGwpIgY2xhc3MgXCJzdGF0dXMtaW4tcmV2aWV3XCJcbiAgICAmLnN0YXR1cy1pbi1yZXZpZXcge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZWFmNjtcbiAgICAgIGNvbG9yOiAjMzk0OWFiO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M1Y2FlOTtcbiAgICB9XG5cbiAgICAmLnN0YXR1cy1yZWplY3RlZCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlO1xuICAgICAgY29sb3I6ICNjNjI4Mjg7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjZGQyO1xuICAgIH1cblxuICAgICYuc3RhdHVzLXJlcXVpcmVzLXJlLXN1Ym1pdCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOGUxO1xuICAgICAgY29sb3I6ICNmNTdmMTc7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZlY2IzO1xuICAgIH1cblxuICAgICYuc3RhdHVzLWFwcHJvdmVkLXctY29uZGl0aW9ucyB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNlNWY1O1xuICAgICAgY29sb3I6ICM3YjFmYTI7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTFiZWU3O1xuICAgIH1cblxuICAgICYuc3RhdHVzLWNvbXBsZXRlIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTg7XG4gICAgICBjb2xvcjogIzFiNWUyMDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XG4gICAgfVxuXG4gICAgJi5zdGF0dXMtbi1hIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gICAgICBjb2xvcjogIzc1NzU3NTtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XG4gICAgfVxuICB9XG59XG5cbi8vIFJldmlld3MgQ29udGFpbmVyIFN0eWxpbmcgKGZvciBhY2NvcmRpb24gY29udGVudClcbi5yZXZpZXdzLWNvbnRhaW5lciB7XG4gIG1heC1oZWlnaHQ6IDQwMHB4O1xuICBvdmVyZmxvdy15OiBhdXRvO1xufVxuXG5cbi8vIFN0YXR1cyBDb2xvcnMgZm9yIEF1ZGl0IEVudHJpZXMgLSBHbG9iYWwgc3R5bGVzXG4uc3RhdHVzLWFwcHJvdmVkIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjVlOCAhaW1wb3J0YW50O1xuICBjb2xvcjogIzFiNWUyMCAhaW1wb3J0YW50O1xuICBib3JkZXI6IDFweCBzb2xpZCAjYzhlNmM5ICFpbXBvcnRhbnQ7XG59XG5cbi5zdGF0dXMtcGVuZGluZyB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmYzZTAgIWltcG9ydGFudDtcbiAgY29sb3I6ICNlNjUxMDAgIWltcG9ydGFudDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2ZmY2MwMiAhaW1wb3J0YW50O1xufVxuXG4uc3RhdHVzLXJlcXVpcmVzLXJlc3VibWl0IHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZWJlZSAhaW1wb3J0YW50O1xuICBjb2xvcjogI2M2MjgyOCAhaW1wb3J0YW50O1xuICBib3JkZXI6IDFweCBzb2xpZCAjZmZjZGQyICFpbXBvcnRhbnQ7XG59XG5cbi5zdGF0dXMtdW5kZXItcmV2aWV3IHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZCAhaW1wb3J0YW50O1xuICBjb2xvcjogIzE1NjVjMCAhaW1wb3J0YW50O1xuICBib3JkZXI6IDFweCBzb2xpZCAjYmJkZWZiICFpbXBvcnRhbnQ7XG59XG5cbi5zdGF0dXMtY29tcGxldGUge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4ICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiAjMWI1ZTIwICFpbXBvcnRhbnQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2YzkgIWltcG9ydGFudDtcbn1cblxuLy8gU3BlY2lmaWMgc3RhdHVzIGNsYXNzZXMgZm9yIGludGVybmFsIHJldmlld3Ncbi5zdGF0dXMtMS1jeWNsZS1jb21wbGV0ZWQge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4ICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiAjMmU3ZDMyICFpbXBvcnRhbnQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNhNWQ2YTcgIWltcG9ydGFudDtcbn1cblxuLnN0YXR1cy1wYWNpZmljYS12ZXJpZmljYXRpb24ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTFmNWZlICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiAjMDI3N2JkICFpbXBvcnRhbnQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkICM4MWQ0ZmEgIWltcG9ydGFudDtcbn1cblxuLnN0YXR1cy1kaXMtYXBwcm92ZWQge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiAjYzYyODI4ICFpbXBvcnRhbnQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDIgIWltcG9ydGFudDtcbn1cblxuLnN0YXR1cy1ub3QtcmVxdWlyZWQge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1ICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiAjNzU3NTc1ICFpbXBvcnRhbnQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTAgIWltcG9ydGFudDtcbn1cblxuLnN0YXR1cy1pbi1yZXZpZXcge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThlYWY2ICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiAjMzk0OWFiICFpbXBvcnRhbnQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNjNWNhZTkgIWltcG9ydGFudDtcbn1cblxuLy8gUmV2aWV3cyBDb250YWluZXIgU3R5bGluZ1xuLnJldmlld3MtY29udGFpbmVyIHtcbiAgbWF4LWhlaWdodDogNTAwcHg7XG4gIG92ZXJmbG93LXk6IGF1dG87XG4gIHBhZGRpbmc6IDAuNzVyZW07XG59XG5cbi5yZXZpZXctaXRlbSB7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlYWVlO1xuICBwYWRkaW5nOiAwLjVyZW0gMDtcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4ycyBlYXNlO1xuXG4gICY6aG92ZXIge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XG4gIH1cblxuICAmOmxhc3QtY2hpbGQge1xuICAgIGJvcmRlci1ib3R0b206IG5vbmU7XG4gIH1cbn1cblxuLnJldmlldy1zaW5nbGUtbGluZSB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMjBweCAxZnIgMTIwcHggMTIwcHggMTQwcHggMTQwcHggMzBweDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAwLjc1cmVtO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHBhZGRpbmc6IDAuNXJlbTtcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuXG4gICY6aG92ZXIge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIH1cblxuICAmLmV4cGFuZGVkIHtcbiAgICBiYWNrZ3JvdW5kOiAjZTNmMmZkO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMTk2ZjM7XG4gIH1cblxuICAucmV2aWV3LXRpdGxlIHtcbiAgICBtYXJnaW46IDA7XG4gICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjb2xvcjogIzNmNDI1NDtcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gIH1cblxuICAucmV2aWV3LXN0YXR1cy1jb250YWluZXIsXG4gIC5yZXZpZXdlci1jb250YWluZXIsXG4gIC5kdWUtZGF0ZS1jb250YWluZXIsXG4gIC5jb21wbGV0ZWQtZGF0ZS1jb250YWluZXIsXG4gIC5yZXZpZXctYWN0aW9ucy1jb250YWluZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBtaW4taGVpZ2h0OiAyMHB4O1xuICB9XG5cbiAgLnJldmlldy1hY3Rpb25zLWNvbnRhaW5lciB7XG4gICAgZ2FwOiAwLjVyZW07XG4gICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDtcbiAgfVxuXG4gIC5yZXZpZXctc3RhdHVzIHtcbiAgICBmb250LXNpemU6IDAuNzVyZW07XG4gICAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW07XG4gICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkIHRyYW5zcGFyZW50O1xuICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuXG4gICAgLy8gU3RhdHVzIGNvbG9yIHZhcmlhdGlvbnMgZm9yIHJldmlldyBzdGF0dXNcbiAgICAmLnN0YXR1cy1hcHByb3ZlZCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xuICAgICAgY29sb3I6ICMxYjVlMjA7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjYzhlNmM5O1xuICAgIH1cblxuICAgICYuc3RhdHVzLXBlbmRpbmcge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjNlMDtcbiAgICAgIGNvbG9yOiAjZTY1MTAwO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2ZmY2MwMjtcbiAgICB9XG5cbiAgICAmLnN0YXR1cy11bmRlci1yZXZpZXcge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZWFmNjtcbiAgICAgIGNvbG9yOiAjMzk0OWFiO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2M1Y2FlOTtcbiAgICB9XG5cbiAgICAmLnN0YXR1cy1yZWplY3RlZCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlO1xuICAgICAgY29sb3I6ICNjNjI4Mjg7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjZGQyO1xuICAgIH1cblxuICAgICYuc3RhdHVzLXJlcXVpcmVzLXJlLXN1Ym1pdCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOGUxO1xuICAgICAgY29sb3I6ICNmNTdmMTc7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZlY2IzO1xuICAgIH1cblxuICAgICYuc3RhdHVzLWFwcHJvdmVkLXctY29uZGl0aW9ucyB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNlNWY1O1xuICAgICAgY29sb3I6ICM3YjFmYTI7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTFiZWU3O1xuICAgIH1cblxuICAgICYuc3RhdHVzLWNvbXBsZXRlIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY1ZTg7XG4gICAgICBjb2xvcjogIzFiNWUyMDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XG4gICAgfVxuXG4gICAgJi5zdGF0dXMtbi1hIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gICAgICBjb2xvcjogIzc1NzU3NTtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XG4gICAgfVxuXG4gICAgLy8gQWRkaXRpb25hbCBzdGF0dXMgdmFyaWF0aW9ucyBmb3IgY29tcHJlaGVuc2l2ZSBjb3ZlcmFnZVxuICAgICYuc3RhdHVzLXN1Ym1pdHRlZCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTNmMmZkO1xuICAgICAgY29sb3I6ICMxNTY1YzA7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjYmJkZWZiO1xuICAgIH1cblxuICAgICYuc3RhdHVzLXZvaWQge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgICAgIGNvbG9yOiAjNzU3NTc1O1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcbiAgICB9XG5cbiAgICAmLnN0YXR1cy1jb25kaXRpb25hbC1hcHByb3ZhbCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNlNWY1O1xuICAgICAgY29sb3I6ICM3YjFmYTI7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTFiZWU3O1xuICAgIH1cblxuICAgICYuc3RhdHVzLXJlcXVpcmVzLXJlc3VibWl0IHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY4ZTE7XG4gICAgICBjb2xvcjogI2Y1N2YxNztcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmVjYjM7XG4gICAgfVxuXG4gICAgJi5zdGF0dXMtdW5rbm93biB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xuICAgICAgY29sb3I6ICM3NTc1NzU7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xuICAgIH1cbiAgfVxuXG4gIC5yZXZpZXdlciB7XG4gICAgZm9udC1zaXplOiAwLjhyZW07XG4gICAgY29sb3I6ICM2YzcyOTM7XG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcbiAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICB9XG5cbiAgLmR1ZS1kYXRlIHtcbiAgICBmb250LXNpemU6IDAuN3JlbTtcbiAgICBjb2xvcjogIzZjNzI5MztcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gIH1cblxuICAuY29tcGxldGVkLWRhdGUge1xuICAgIGZvbnQtc2l6ZTogMC43cmVtO1xuICAgIGNvbG9yOiAjNmM3MjkzO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XG4gICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgfVxuXG4gIC5hY3Rpb24taWNvbiB7XG4gICAgZm9udC1zaXplOiAxcmVtO1xuICAgIGNvbG9yOiAjMzY5OWZmO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICB0cmFuc2l0aW9uOiBjb2xvciAwLjJzIGVhc2UsIHRyYW5zZm9ybSAwLjJzIGVhc2U7XG4gICAgbWFyZ2luLWxlZnQ6IDAuNXJlbTtcblxuICAgICY6aG92ZXIge1xuICAgICAgY29sb3I6ICMyYjdjZTA7XG4gICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XG4gICAgfVxuXG4gICAgJi5lZGl0LWljb24ge1xuICAgICAgY29sb3I6ICMzNjk5ZmY7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICBjb2xvcjogIzJiN2NlMDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAmLnZpZXctaWNvbiB7XG4gICAgICBjb2xvcjogIzZjNzI5MztcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGNvbG9yOiAjMzY5OWZmO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIFJldmlldyBhY2NvcmRpb24gdG9nZ2xlIGljb25cbiAgLnJldmlldy1hY2NvcmRpb24tdG9nZ2xlIHtcbiAgICBjb2xvcjogIzM2OTlmZjtcbiAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICB3aWR0aDogMjBweDtcbiAgICBoZWlnaHQ6IDIwcHg7XG5cbiAgICBpIHtcbiAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2U7XG4gICAgfVxuICB9XG5cbiAgLy8gRWRpdCBSZXZpZXcgaWNvblxuICAuZWRpdC1yZXZpZXctaWNvbiB7XG4gICAgY29sb3I6ICMyOGE3NDU7XG4gICAgZm9udC1zaXplOiAxcmVtO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuICAgIHBhZGRpbmc6IDAuMjVyZW07XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGNvbG9yOiAjMWU3ZTM0O1xuICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xuICAgIH1cblxuICAgICY6YWN0aXZlIHtcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMC45NSk7XG4gICAgfVxuICB9XG5cbiAgLy8gRG93bmxvYWQgUERGIGljb25cbiAgLmRvd25sb2FkLXBkZi1pY29uIHtcbiAgICBjb2xvcjogIzM2OTlmZjtcbiAgICBmb250LXNpemU6IDFyZW07XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG4gICAgcGFkZGluZzogMC4yNXJlbTtcblxuICAgICY6aG92ZXIge1xuICAgICAgY29sb3I6ICMyYjdjZTA7XG4gICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XG4gICAgfVxuXG4gICAgJjphY3RpdmUge1xuICAgICAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTtcbiAgICB9XG4gIH1cblxuICAvLyBJbnRlcm5hbCByZXZpZXdzIGRvd25sb2FkIGJ1dHRvbiBzdHlsaW5nXG4gIC5idXR0b24tZ3JvdXAgLmJ0bi1saW5rIHtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3gtc2hhZG93OiBub25lO1xuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICBcbiAgICAmOmhvdmVyLCAmOmZvY3VzLCAmOmFjdGl2ZSB7XG4gICAgICBiYWNrZ3JvdW5kOiBub25lO1xuICAgICAgYm94LXNoYWRvdzogbm9uZTtcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICB9XG4gICAgXG4gICAgJjpkaXNhYmxlZCB7XG4gICAgICBvcGFjaXR5OiAwLjU7XG4gICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICAgICAgXG4gICAgICAuZG93bmxvYWQtcGRmLWljb24ge1xuICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICAgICAgICBjb2xvcjogIzZjNzU3ZDtcbiAgICAgICAgXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIHRyYW5zZm9ybTogbm9uZTtcbiAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIEV4dGVybmFsIFJldmlld3MgQ2FyZCBTdHlsaW5nXG4gIC5leHRlcm5hbC1yZXZpZXdzLWNhcmQge1xuICAgIC5jYXJkIHtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWVhZWU7XG4gICAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICAgIH1cblxuICAgIC5jYXJkLWJvZHkge1xuICAgICAgcGFkZGluZzogMS41cmVtO1xuICAgIH1cblxuICAgIC50YWJsZSB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgIH1cbiAgfVxuXG4gIC8vIFJldmlldyBkZXRhaWxzIGFjY29yZGlvblxuICAucmV2aWV3LWRldGFpbHMtYWNjb3JkaW9uIHtcbiAgICBtYXJnaW4tdG9wOiAxcmVtO1xuICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTVlYWVlO1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmYgMCUsICNlOGYyZmYgMTAwJSk7XG4gICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XG4gICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggNHB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjEpO1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAucmV2aWV3LWRldGFpbHMtY29udGVudCB7XG4gICAgICBwYWRkaW5nOiAxcmVtO1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XG4gICAgICBtYXJnaW46IDAuNXJlbTtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4xNSk7XG5cbiAgICAgIC8vIEVuc3VyZSBjb3JyZWN0aW9ucyBzZWN0aW9uIHN0eWxlcyBhcmUgYXBwbGllZCB3aXRoaW4gcmV2aWV3LWRldGFpbHMtY29udGVudFxuICAgICAgOjpuZy1kZWVwIC5jb3JyZWN0aW9ucy1zZWN0aW9uLFxuICAgICAgLmNvcnJlY3Rpb25zLXNlY3Rpb24ge1xuICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gIWltcG9ydGFudDtcbiAgICAgICAgcGFkZGluZzogMnJlbSAhaW1wb3J0YW50O1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlYWVlICFpbXBvcnRhbnQ7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KSAhaW1wb3J0YW50O1xuICAgICAgICBtYXJnaW46IDAgIWltcG9ydGFudDtcblxuICAgICAgICAuc2VjdGlvbi10aXRsZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAxLjRyZW0gIWltcG9ydGFudDtcbiAgICAgICAgICBmb250LXdlaWdodDogODAwICFpbXBvcnRhbnQ7XG4gICAgICAgICAgY29sb3I6ICMxYTIwMmMgIWltcG9ydGFudDtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAycmVtICFpbXBvcnRhbnQ7XG4gICAgICAgICAgcGFkZGluZy1ib3R0b206IDFyZW0gIWltcG9ydGFudDtcbiAgICAgICAgICBib3JkZXItYm90dG9tOiAzcHggc29saWQgIzM2OTlmZiAhaW1wb3J0YW50O1xuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICAgICAgZ2FwOiAxcmVtICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgICAgY29udGVudDogJ8Owwp/Ck8KLJztcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMS44cmVtO1xuICAgICAgICAgICAgZmlsdGVyOiBkcm9wLXNoYWRvdygwIDJweCA0cHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuMykpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgICY6OmFmdGVyIHtcbiAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgYm90dG9tOiAtM3B4O1xuICAgICAgICAgICAgbGVmdDogMDtcbiAgICAgICAgICAgIHdpZHRoOiA2MHB4O1xuICAgICAgICAgICAgaGVpZ2h0OiAzcHg7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNjk5ZmYsICMxYmM1YmQpO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMnB4O1xuICAgICAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5jb3JyZWN0aW9uLWl0ZW0ge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmYgIWltcG9ydGFudDtcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlYWVlICFpbXBvcnRhbnQ7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAhaW1wb3J0YW50O1xuICAgICAgICAgIHBhZGRpbmc6IDEuNXJlbSAhaW1wb3J0YW50O1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA4KSAhaW1wb3J0YW50O1xuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSkgIWltcG9ydGFudDtcbiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuICFpbXBvcnRhbnQ7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMnJlbSAhaW1wb3J0YW50O1xuXG4gICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgdG9wOiAwO1xuICAgICAgICAgICAgbGVmdDogMDtcbiAgICAgICAgICAgIHdpZHRoOiA0cHg7XG4gICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjMzY5OWZmLCAjMWJjNWJkKTtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW07XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCk7XG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjE1KTtcblxuICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzFiYzViZCwgIzM2OTlmZik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLmNvcnJlY3Rpb24taGVhZGVyIHtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICAgICAgICAgIGdhcDogMS4yNXJlbTtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbiAgICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAxcmVtO1xuICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNmMWYzZjQ7XG4gICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG5cbiAgICAgICAgICAgICY6OmFmdGVyIHtcbiAgICAgICAgICAgICAgY29udGVudDogJyc7XG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgICAgICAgYm90dG9tOiAtMnB4O1xuICAgICAgICAgICAgICBsZWZ0OiAwO1xuICAgICAgICAgICAgICB3aWR0aDogNTBweDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiAycHg7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzM2OTlmZiwgIzFiYzViZCk7XG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDFweDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLmNvcnJlY3Rpb24tbnVtYmVyIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM2OTlmZiwgIzFiYzViZCkgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIHdpZHRoOiAzMnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGhlaWdodDogMzJweCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCUgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA4MDAgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC40KSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBmbGV4LXNocmluazogMCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmUgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZSAhaW1wb3J0YW50O1xuXG4gICAgICAgICAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgICAgICAgICAgY29udGVudDogJyc7XG4gICAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgICAgIHRvcDogLTJweDtcbiAgICAgICAgICAgICAgICBsZWZ0OiAtMnB4O1xuICAgICAgICAgICAgICAgIHJpZ2h0OiAtMnB4O1xuICAgICAgICAgICAgICAgIGJvdHRvbTogLTJweDtcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzY5OWZmLCAjMWJjNWJkKTtcbiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICAgICAgICAgICAgei1pbmRleDogLTE7XG4gICAgICAgICAgICAgICAgb3BhY2l0eTogMC4zO1xuICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogcHVsc2UgMnMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICBAa2V5ZnJhbWVzIHB1bHNlIHtcbiAgICAgICAgICAgICAgICAwJSwgMTAwJSB7IHRyYW5zZm9ybTogc2NhbGUoMSk7IG9wYWNpdHk6IDAuMzsgfVxuICAgICAgICAgICAgICAgIDUwJSB7IHRyYW5zZm9ybTogc2NhbGUoMS4xKTsgb3BhY2l0eTogMC42OyB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLmNvcnJlY3Rpb24tbWV0YSB7XG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgICAgIGdhcDogMC43NXJlbTtcbiAgICAgICAgICAgICAgZmxleDogMTtcblxuICAgICAgICAgICAgICAubWV0YS1yb3cge1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgZ2FwOiAwLjc1cmVtICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgcGFkZGluZzogMC41cmVtIDAgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBub25lO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC5tZXRhLWxhYmVsIHtcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcbiAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICAgICAgICAgICAgICBjb2xvcjogIzRhNTU2ODtcbiAgICAgICAgICAgICAgICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xuICAgICAgICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuMDVyZW07XG4gICAgICAgICAgICAgICAgICBtaW4td2lkdGg6IDgwcHg7XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLm1ldGEtdmFsdWUge1xuICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICMyZDM3NDg7XG4gICAgICAgICAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC5yZXNvbHZlZC1kYXRlIHtcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBpbmhlcml0O1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDA7XG4gICAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwO1xuICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuY29ycmVjdGlvbi1jb250ZW50IHtcbiAgICAgICAgICAgIC5jb3JyZWN0aW9uLWZpZWxkIHtcbiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICAgICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG5cbiAgICAgICAgICAgICAgJjpsYXN0LWNoaWxkIHtcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgLmZpZWxkLWxhYmVsIHtcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODVyZW07XG4gICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDgwMDtcbiAgICAgICAgICAgICAgICBjb2xvcjogIzJkMzc0ODtcbiAgICAgICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcbiAgICAgICAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC4xNXJlbTtcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjc1cmVtO1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgICAgICBnYXA6IDAuNzVyZW07XG4gICAgICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICAgICAgICAgICAgICAgaSB7XG4gICAgICAgICAgICAgICAgICBjb2xvcjogIzM2OTlmZjtcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICAgICAgICAgICAgd2lkdGg6IDE2cHg7XG4gICAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICfDosKWwrYnO1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICMzNjk5ZmY7XG4gICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogc2xpZGVSaWdodCAxLjVzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIEBrZXlmcmFtZXMgc2xpZGVSaWdodCB7XG4gICAgICAgICAgICAgICAgICAwJSwgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTsgfVxuICAgICAgICAgICAgICAgICAgNTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDNweCk7IH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAuZmllbGQtY29udGVudCB7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxcmVtICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgY29sb3I6ICMyZDM3NDggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS43ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgcGFkZGluZzogMS4yNXJlbSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmEgMCUsICNmZmZmZmYgMTAwJSkgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjc1cmVtICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UyZThmMCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICAgIGJveC1zaGFkb3c6IGluc2V0IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDQpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZSAhaW1wb3J0YW50O1xuXG4gICAgICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgICAgICAgdG9wOiAwO1xuICAgICAgICAgICAgICAgICAgbGVmdDogMDtcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiA0cHg7XG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjZTJlOGYwLCAjY2JkNWUwKTtcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW07XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgICAgICBib3gtc2hhZG93OiBpbnNldCAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgJi5jb3JyZWN0aXZlLWFjdGlvbiB7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmZmNWY1IDAlLCAjZmZmZmZmIDEwMCUpO1xuICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjZmVkN2Q3O1xuXG4gICAgICAgICAgICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjZmViMmIyLCAjZmM4MTgxKTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAmLmNvbW1lbnQge1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwZjlmZiAwJSwgI2ZmZmZmZiAxMDAlKTtcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2JlZTNmODtcblxuICAgICAgICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzkwY2RmNCwgIzYzYjNlZCk7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgJi5yZXNwb25zZSB7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZThmNWU4IDAlLCAjZjBmZmY0IDEwMCUpO1xuICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYzZmNmQ1O1xuXG4gICAgICAgICAgICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjMzhhMTY5LCAjMmY4NTVhKTtcbiAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgJjo6YWZ0ZXIge1xuICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAnw6LCnMKTJztcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgICAgICAgICB0b3A6IDFyZW07XG4gICAgICAgICAgICAgICAgICAgIHJpZ2h0OiAxcmVtO1xuICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzM4YTE2OTtcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xuICAgICAgICAgICAgICAgICAgICBhbmltYXRpb246IGNoZWNrbWFyayAwLjZzIGVhc2UtaW4tb3V0O1xuICAgICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgICBAa2V5ZnJhbWVzIGNoZWNrbWFyayB7XG4gICAgICAgICAgICAgICAgICAgIDAlIHsgdHJhbnNmb3JtOiBzY2FsZSgwKSByb3RhdGUoMGRlZyk7IH1cbiAgICAgICAgICAgICAgICAgICAgNTAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxLjIpIHJvdGF0ZSgxODBkZWcpOyB9XG4gICAgICAgICAgICAgICAgICAgIDEwMCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEpIHJvdGF0ZSgzNjBkZWcpOyB9XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgJi5lb3ItcmVzcG9uc2Uge1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2U4ZjVlOCAwJSwgI2YwZmZmNCAxMDAlKSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYzZmNmQ1ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgICBib3gtc2hhZG93OiBpbnNldCAwIDJweCA4cHggcmdiYSg1NiwgMTYxLCAxMDUsIDAuMSkgIWltcG9ydGFudDtcblxuICAgICAgICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzM4YTE2OSwgIzJmODU1YSkgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDRweCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDEwMCUgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAwIDAgMC43NXJlbSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICYucmVzcG9uZGVkLWJ5IHtcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlOGY1ZTggMCUsICNmMGZmZjQgMTAwJSkgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2M2ZjZkNSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICAgICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggOHB4IHJnYmEoNTYsIDE2MSwgMTA1LCAwLjEpICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgICAgICAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICMzOGExNjksICMyZjg1NWEpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiA0cHggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW0gIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuY29ycmVjdGlvbi1zZXBhcmF0b3Ige1xuICAgICAgICAgICAgaGVpZ2h0OiAycHg7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50LCAjZTVlYWVlLCB0cmFuc3BhcmVudCk7XG4gICAgICAgICAgICBtYXJnaW46IDJyZW0gMDtcbiAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgY29udGVudDogJyc7XG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgICAgICAgdG9wOiAtMnB4O1xuICAgICAgICAgICAgICBsZWZ0OiA1MCU7XG4gICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcbiAgICAgICAgICAgICAgd2lkdGg6IDYwcHg7XG4gICAgICAgICAgICAgIGhlaWdodDogNnB4O1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNjk5ZmYsICMxYmM1YmQpO1xuICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7XG4gICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4zKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuXG4gIC8vIENvcnJlY3Rpb25zIHNlY3Rpb24gc3R5bGluZyAtIHNjb3BlZCBmb3IgcmV2aWV3LWRldGFpbHMtYWNjb3JkaW9uXG4gIDo6bmctZGVlcCAucmV2aWV3LWRldGFpbHMtYWNjb3JkaW9uIC5jb3JyZWN0aW9ucy1zZWN0aW9uLFxuICAucmV2aWV3LWRldGFpbHMtYWNjb3JkaW9uIC5jb3JyZWN0aW9ucy1zZWN0aW9uLFxuICAuY29ycmVjdGlvbnMtc2VjdGlvbiB7XG4gICAgYmFja2dyb3VuZDogI2ZmZmZmZiAhaW1wb3J0YW50O1xuICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gIWltcG9ydGFudDtcbiAgICBwYWRkaW5nOiAycmVtICFpbXBvcnRhbnQ7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZSAhaW1wb3J0YW50O1xuICAgIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KSAhaW1wb3J0YW50O1xuXG4gICAgLnNlY3Rpb24tdGl0bGUge1xuICAgICAgZm9udC1zaXplOiAxLjRyZW0gIWltcG9ydGFudDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA4MDAgIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiAjMWEyMDJjICFpbXBvcnRhbnQ7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycmVtICFpbXBvcnRhbnQ7XG4gICAgICBwYWRkaW5nLWJvdHRvbTogMXJlbSAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyLWJvdHRvbTogM3B4IHNvbGlkICMzNjk5ZmYgIWltcG9ydGFudDtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xuICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xuICAgICAgZ2FwOiAxcmVtICFpbXBvcnRhbnQ7XG5cbiAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgIGNvbnRlbnQ6ICfDsMKfwpPCiyc7XG4gICAgICAgIGZvbnQtc2l6ZTogMS44cmVtO1xuICAgICAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMnB4IDRweCByZ2JhKDU0LCAxNTMsIDI1NSwgMC4zKSk7XG4gICAgICB9XG5cbiAgICAgICY6OmFmdGVyIHtcbiAgICAgICAgY29udGVudDogJyc7XG4gICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgYm90dG9tOiAtM3B4O1xuICAgICAgICBsZWZ0OiAwO1xuICAgICAgICB3aWR0aDogNjBweDtcbiAgICAgICAgaGVpZ2h0OiAzcHg7XG4gICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzM2OTlmZiwgIzFiYzViZCk7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDJweDtcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNTQsIDE1MywgMjU1LCAwLjQpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5jb3JyZWN0aW9uLWl0ZW0ge1xuICAgICAgYmFja2dyb3VuZDogI2ZmZiAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZSAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyLXJhZGl1czogMC43NXJlbSAhaW1wb3J0YW50O1xuICAgICAgcGFkZGluZzogMS41cmVtICFpbXBvcnRhbnQ7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCkgIWltcG9ydGFudDtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKSAhaW1wb3J0YW50O1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMnJlbSAhaW1wb3J0YW50O1xuXG4gICAgICAmOjpiZWZvcmUge1xuICAgICAgICBjb250ZW50OiAnJztcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICB0b3A6IDA7XG4gICAgICAgIGxlZnQ6IDA7XG4gICAgICAgIHdpZHRoOiA0cHg7XG4gICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzM2OTlmZiwgIzFiYzViZCk7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW07XG4gICAgICB9XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCk7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuMTUpO1xuXG4gICAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzFiYzViZCwgIzM2OTlmZik7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmNvcnJlY3Rpb24taGVhZGVyIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgICAgIGdhcDogMS4yNXJlbTtcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICAgICAgICBwYWRkaW5nLWJvdHRvbTogMXJlbTtcbiAgICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNmMWYzZjQ7XG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAmOjphZnRlciB7XG4gICAgICAgICAgY29udGVudDogJyc7XG4gICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgIGJvdHRvbTogLTJweDtcbiAgICAgICAgICBsZWZ0OiAwO1xuICAgICAgICAgIHdpZHRoOiA1MHB4O1xuICAgICAgICAgIGhlaWdodDogMnB4O1xuICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzM2OTlmZiwgIzFiYzViZCk7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMXB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLmNvcnJlY3Rpb24tbnVtYmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzY5OWZmLCAjMWJjNWJkKSAhaW1wb3J0YW50O1xuICAgICAgICAgIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xuICAgICAgICAgIHdpZHRoOiAzMnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgaGVpZ2h0OiAzMnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlICFpbXBvcnRhbnQ7XG4gICAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlciAhaW1wb3J0YW50O1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtICFpbXBvcnRhbnQ7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDgwMCAhaW1wb3J0YW50O1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuNCkgIWltcG9ydGFudDtcbiAgICAgICAgICBmbGV4LXNocmluazogMCAhaW1wb3J0YW50O1xuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UgIWltcG9ydGFudDtcblxuICAgICAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgICAgICBjb250ZW50OiAnJztcbiAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgICAgIHRvcDogLTJweDtcbiAgICAgICAgICAgIGxlZnQ6IC0ycHg7XG4gICAgICAgICAgICByaWdodDogLTJweDtcbiAgICAgICAgICAgIGJvdHRvbTogLTJweDtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNjk5ZmYsICMxYmM1YmQpO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgICAgICAgei1pbmRleDogLTE7XG4gICAgICAgICAgICBvcGFjaXR5OiAwLjM7XG4gICAgICAgICAgICBhbmltYXRpb246IHB1bHNlIDJzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIEBrZXlmcmFtZXMgcHVsc2Uge1xuICAgICAgICAgICAgMCUsIDEwMCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEpOyBvcGFjaXR5OiAwLjM7IH1cbiAgICAgICAgICAgIDUwJSB7IHRyYW5zZm9ybTogc2NhbGUoMS4xKTsgb3BhY2l0eTogMC42OyB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLmNvcnJlY3Rpb24tbWV0YSB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICAgIGdhcDogMC43NXJlbTtcbiAgICAgICAgICBmbGV4OiAxO1xuXG4gICAgICAgICAgLm1ldGEtcm93IHtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgZ2FwOiAwLjc1cmVtO1xuICAgICAgICAgICAgcGFkZGluZzogMC41cmVtIDA7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDA7XG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuXG4gICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgICAgIHRyYW5zZm9ybTogbm9uZTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLm1ldGEtbGFiZWwge1xuICAgICAgICAgICAgICBmb250LXNpemU6IDAuODVyZW07XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICAgICAgICAgIGNvbG9yOiAjNGE1NTY4O1xuICAgICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcbiAgICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuMDVyZW07XG4gICAgICAgICAgICAgIG1pbi13aWR0aDogODBweDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLm1ldGEtdmFsdWUge1xuICAgICAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgICAgY29sb3I6ICMyZDM3NDg7XG4gICAgICAgICAgICAgIGZsZXg6IDE7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5yZXNvbHZlZC1kYXRlIHtcbiAgICAgICAgICAgICAgY29sb3I6IGluaGVyaXQ7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgICAgICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDA7XG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDA7XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5yZXNwb25kLWJ0biB7XG4gICAgICAgICAgICAgIC8vIFJlbW92ZSBjdXN0b20gc3R5bGluZyB0byB1c2UgQm9vdHN0cmFwIGJ0bi1zZWNvbmRhcnkgc3R5bGluZ1xuICAgICAgICAgICAgICBtYXJnaW4tbGVmdDogMC41cmVtICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC5jb3JyZWN0aW9uLWNvbnRlbnQge1xuICAgICAgICAuY29ycmVjdGlvbi1maWVsZCB7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5maWVsZC1sYWJlbCB7XG4gICAgICAgICAgICBmb250LXNpemU6IDAuODVyZW07XG4gICAgICAgICAgICBmb250LXdlaWdodDogODAwO1xuICAgICAgICAgICAgY29sb3I6ICMyZDM3NDg7XG4gICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcbiAgICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAwLjE1cmVtO1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC43NXJlbTtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgZ2FwOiAwLjc1cmVtO1xuICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICAgICAgICAgICBpIHtcbiAgICAgICAgICAgICAgY29sb3I6ICMzNjk5ZmY7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICAgICAgICB3aWR0aDogMTZweDtcbiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgICAgICBjb250ZW50OiAnw6LClsK2JztcbiAgICAgICAgICAgICAgY29sb3I6ICMzNjk5ZmY7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgICAgICAgICAgICBhbmltYXRpb246IHNsaWRlUmlnaHQgMS41cyBlYXNlLWluLW91dCBpbmZpbml0ZTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgQGtleWZyYW1lcyBzbGlkZVJpZ2h0IHtcbiAgICAgICAgICAgICAgMCUsIDEwMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7IH1cbiAgICAgICAgICAgICAgNTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDNweCk7IH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuZmllbGQtY29udGVudCB7XG4gICAgICAgICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICAgICAgICBjb2xvcjogIzJkMzc0ODtcbiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjc7XG4gICAgICAgICAgICBwYWRkaW5nOiAxLjI1cmVtO1xuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2ZmZmZmZiAxMDAlKTtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW07XG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTJlOGYwO1xuICAgICAgICAgICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNCk7XG4gICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICAgICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgICAgICBjb250ZW50OiAnJztcbiAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgICB0b3A6IDA7XG4gICAgICAgICAgICAgIGxlZnQ6IDA7XG4gICAgICAgICAgICAgIHdpZHRoOiA0cHg7XG4gICAgICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgI2UyZThmMCwgI2NiZDVlMCk7XG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW07XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgICBib3gtc2hhZG93OiBpbnNldCAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgJi5jb3JyZWN0aXZlLWFjdGlvbiB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmY1ZjUgMCUsICNmZmZmZmYgMTAwJSk7XG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2ZlZDdkNztcblxuICAgICAgICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICNmZWIyYjIsICNmYzgxODEpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICYuY29tbWVudCB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmMGY5ZmYgMCUsICNmZmZmZmYgMTAwJSk7XG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2JlZTNmODtcblxuICAgICAgICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICM5MGNkZjQsICM2M2IzZWQpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICYucmVzcG9uc2Uge1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZThmNWU4IDAlLCAjZjBmZmY0IDEwMCUpO1xuICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNjNmY2ZDU7XG5cbiAgICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjMzhhMTY5LCAjMmY4NTVhKTtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICY6OmFmdGVyIHtcbiAgICAgICAgICAgICAgICBjb250ZW50OiAnw6LCnMKTJztcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICAgICAgICAgICAgdG9wOiAxcmVtO1xuICAgICAgICAgICAgICAgIHJpZ2h0OiAxcmVtO1xuICAgICAgICAgICAgICAgIGNvbG9yOiAjMzhhMTY5O1xuICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xuICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogY2hlY2ttYXJrIDAuNnMgZWFzZS1pbi1vdXQ7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICBAa2V5ZnJhbWVzIGNoZWNrbWFyayB7XG4gICAgICAgICAgICAgICAgMCUgeyB0cmFuc2Zvcm06IHNjYWxlKDApIHJvdGF0ZSgwZGVnKTsgfVxuICAgICAgICAgICAgICAgIDUwJSB7IHRyYW5zZm9ybTogc2NhbGUoMS4yKSByb3RhdGUoMTgwZGVnKTsgfVxuICAgICAgICAgICAgICAgIDEwMCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEpIHJvdGF0ZSgzNjBkZWcpOyB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmNvcnJlY3Rpb24tc2VwYXJhdG9yIHtcbiAgICAgICAgaGVpZ2h0OiAycHg7XG4gICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQsICNlNWVhZWUsIHRyYW5zcGFyZW50KTtcbiAgICAgICAgbWFyZ2luOiAycmVtIDA7XG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgICB0b3A6IC0ycHg7XG4gICAgICAgICAgbGVmdDogNTAlO1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcbiAgICAgICAgICB3aWR0aDogNjBweDtcbiAgICAgICAgICBoZWlnaHQ6IDZweDtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNjk5ZmYsICMxYmM1YmQpO1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSg1NCwgMTUzLCAyNTUsIDAuMyk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBDb21tZW50cyBzZWN0aW9uIHN0eWxpbmdcbiAgLmNvbW1lbnRzLXNlY3Rpb24ge1xuICAgIC5zZWN0aW9uLXRpdGxlIHtcbiAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjb2xvcjogIzNmNDI1NDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG4gICAgICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xuICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMzNjk5ZmY7XG4gICAgfVxuXG4gICAgLmNvbW1lbnQtY29udGVudCB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZmZmO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xuICAgICAgcGFkZGluZzogMXJlbTtcblxuICAgICAgLmNvbW1lbnQtdGV4dCB7XG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICBjb2xvcjogIzNmNDI1NDtcbiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBObyBkYXRhIHNlY3Rpb24gc3R5bGluZ1xuICAubm8tZGF0YS1zZWN0aW9uIHtcbiAgICAubm8tZGF0YS1tZXNzYWdlIHtcbiAgICAgIHBhZGRpbmc6IDJyZW07XG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICBjb2xvcjogIzZjNzI5MztcblxuICAgICAgaSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgY29sb3I6ICMzNjk5ZmY7XG4gICAgICB9XG5cbiAgICAgIHNwYW4ge1xuICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBSZXZpZXcgZm9ybSBzZWN0aW9uIHN0eWxpbmdcbiAgLnJldmlldy1mb3JtLXNlY3Rpb24ge1xuICAgIGJhY2tncm91bmQ6ICNmZmY7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZWFlZTtcbiAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcblxuICAgIC5mb3JtLWxhYmVsIHtcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY29sb3I6ICMzZjQyNTQ7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG4gICAgfVxuXG4gICAgLmZvcm0tY29udHJvbCB7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlYWVlO1xuICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XG4gICAgICBwYWRkaW5nOiAwLjVyZW0gMC43NXJlbTtcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG5cbiAgICAgICY6Zm9jdXMge1xuICAgICAgICBib3JkZXItY29sb3I6ICMzNjk5ZmY7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDAuMnJlbSByZ2JhKDU0LCAxNTMsIDI1NSwgMC4yNSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLmJ0bi1zdWNjZXNzIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICMxYmM1YmQ7XG4gICAgICBib3JkZXItY29sb3I6ICMxYmM1YmQ7XG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xuICAgICAgcGFkZGluZzogMC41cmVtIDFyZW07XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTdhMmI4O1xuICAgICAgICBib3JkZXItY29sb3I6ICMxN2EyYjg7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi5yZXZpZXctYm9keSB7XG4gIC5yZXZpZXctbWV0YSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogMXJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiAwLjc1cmVtO1xuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICBjb2xvcjogIzZjNzI5MztcblxuICAgIC5yZXZpZXdlcixcbiAgICAuY29tcGxldGVkLWRhdGUge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDAuMjVyZW07XG5cbiAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgICB3aWR0aDogNHB4O1xuICAgICAgICBoZWlnaHQ6IDRweDtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzZjNzI5MztcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgfVxuICAgIH1cblxuICAgIC52aWV3LWJ0biB7XG4gICAgICBtYXJnaW4tbGVmdDogYXV0bztcbiAgICAgIGZvbnQtc2l6ZTogMC42NXJlbTtcbiAgICAgIHBhZGRpbmc6IDZweCAxMHB4O1xuICAgICAgbGluZS1oZWlnaHQ6IDE7XG4gICAgICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICB9XG4gIH1cbn1cblxuLm5vLXNlbGVjdGlvbiB7XG4gIHBhZGRpbmc6IDJyZW07XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgY29sb3I6ICM2YzcyOTM7XG59XG5cbi8vIFJlc3BvbnNpdmUgRGVzaWduXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLnRhYmxlIHtcbiAgICAuYXVkaXQtZGF0ZXMtY2VsbCxcbiAgICAuc3VibWl0dGFsLWRhdGVzLWNlbGwge1xuICAgICAgLmQtZmxleCB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogMC41cmVtO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5yZXZpZXctc2luZ2xlLWxpbmUge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMjBweCAxZnIgMTAwcHggMTAwcHggMTIwcHggMTIwcHggMzBweDtcbiAgICBnYXA6IDAuNXJlbTtcbiAgICBmb250LXNpemU6IDAuOHJlbTtcblxuICAgIC5yZXZpZXctc3RhdHVzIHtcbiAgICAgIGZvbnQtc2l6ZTogMC43cmVtO1xuICAgICAgcGFkZGluZzogMC4ycmVtIDAuNHJlbTtcbiAgICB9XG5cbiAgICAucmV2aWV3ZXIsXG4gICAgLmR1ZS1kYXRlLFxuICAgIC5jb21wbGV0ZWQtZGF0ZSB7XG4gICAgICBmb250LXNpemU6IDAuNjVyZW07XG4gICAgfVxuXG4gICAgLmRvd25sb2FkLXBkZi1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgIH1cbiAgfVxufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcbiAgLnJldmlldy1zaW5nbGUtbGluZSB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAyMHB4IDFmciA4MHB4IDgwcHggMTAwcHggMTAwcHggMjVweDtcbiAgICBnYXA6IDAuMjVyZW07XG4gICAgcGFkZGluZzogMC4yNXJlbTtcblxuICAgIC5yZXZpZXctc3RhdHVzIHtcbiAgICAgIGZvbnQtc2l6ZTogMC42NXJlbTtcbiAgICAgIHBhZGRpbmc6IDAuMTVyZW0gMC4zcmVtO1xuICAgIH1cblxuICAgIC5yZXZpZXdlcixcbiAgICAuZHVlLWRhdGUsXG4gICAgLmNvbXBsZXRlZC1kYXRlIHtcbiAgICAgIGZvbnQtc2l6ZTogMC42cmVtO1xuICAgIH1cblxuICAgIC5kb3dubG9hZC1wZGYtaWNvbiB7XG4gICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICB9XG4gIH1cbn1cblxuLnRpdGxlLXdyYXAge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IC4yNXJlbTtcbn1cblxuLnRpdGxlLWxpbmUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogYmFzZWxpbmU7XG4gIGdhcDogLjc1cmVtO1xufVxuXG4ucGVybWl0LXRpdGxlIHtcbiAgZm9udC1zaXplOiAxLjA1cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogIzE4MWMzMjtcbn1cblxuLnN0YXR1cy10ZXh0IHtcbiAgZm9udC1zaXplOiAuOXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6ICMzZjQyNTQ7XG4gIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xuICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xuICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDtcbiAgd2lkdGg6IGZpdC1jb250ZW50O1xuXG4gIC8vIFN0YXR1cyBjb2xvciB2YXJpYXRpb25zIGZvciBwZXJtaXQgc3RhdHVzXG4gICYuc3RhdHVzLWFwcHJvdmVkIHtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmNWU4O1xuICAgIGNvbG9yOiAjMWI1ZTIwO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNjOGU2Yzk7XG4gIH1cblxuICAmLnN0YXR1cy1wZW5kaW5nIHtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmM2UwO1xuICAgIGNvbG9yOiAjZTY1MTAwO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNjMDI7XG4gIH1cblxuICAmLnN0YXR1cy11bmRlci1yZXZpZXcge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjY7XG4gICAgY29sb3I6ICMzOTQ5YWI7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2M1Y2FlOTtcbiAgfVxuXG4gICYuc3RhdHVzLXJlamVjdGVkIHtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYmVlO1xuICAgIGNvbG9yOiAjYzYyODI4O1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmNkZDI7XG4gIH1cblxuICAmLnN0YXR1cy1zdWJtaXR0ZWQge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNlM2YyZmQ7XG4gICAgY29sb3I6ICMxNTY1YzA7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2JiZGVmYjtcbiAgfVxuXG4gICYuc3RhdHVzLXJlcXVpcmVzLXJlc3VibWl0IHtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOGUxO1xuICAgIGNvbG9yOiAjZjU3ZjE3O1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmVjYjM7XG4gIH1cblxuICAmLnN0YXR1cy1jb25kaXRpb25hbC1hcHByb3ZhbCB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2YzZTVmNTtcbiAgICBjb2xvcjogIzdiMWZhMjtcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTFiZWU3O1xuICB9XG5cbiAgJi5zdGF0dXMtdm9pZCB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgICBjb2xvcjogIzc1NzU3NTtcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xuICB9XG5cbiAgJi5zdGF0dXMtbi1hIHtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xuICAgIGNvbG9yOiAjNzU3NTc1O1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XG4gIH1cbn1cblxuLnBlcm1pdC1udW1iZXItbGluZSB7XG4gIGZvbnQtc2l6ZTogLjg1cmVtO1xuICBjb2xvcjogIzZjNzI5MztcbiAgcGFkZGluZy1ib3R0b206IC4yNXJlbTtcbn1cblxuLy8gRnVsbCBTY3JlZW4gTG9hZGluZyBPdmVybGF5XG4uZnVsbHNjcmVlbi1sb2FkaW5nLW92ZXJsYXkge1xuICBwb3NpdGlvbjogZml4ZWQ7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgd2lkdGg6IDEwMHZ3O1xuICBoZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNCk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICB6LWluZGV4OiA5OTk5O1xuXG4gIC5sb2FkaW5nLWNvbnRlbnQge1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICBjb2xvcjogIzM2OTlmZjtcbiAgICBwYWRkaW5nOiAycmVtO1xuICB9XG59XG5cbi8vIFNwZWNpZmljIHN0eWxpbmcgZm9yIEVPUi9BT1IvT3duZXIgUmVzcG9uc2UgYW5kIENvbW1lbnQgUmVzcG9uZGVkIEJ5IHNlY3Rpb25zXG4uY29ycmVjdGlvbi1jb250ZW50IC5maWVsZC1jb250ZW50LmVvci1yZXNwb25zZSxcbi5jb3JyZWN0aW9uLWNvbnRlbnQgLmZpZWxkLWNvbnRlbnQucmVzcG9uZGVkLWJ5IHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2ZmZmZmZiAxMDAlKSAhaW1wb3J0YW50O1xuICBib3JkZXItY29sb3I6ICNlMmU4ZjAgIWltcG9ydGFudDtcbiAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNCkgIWltcG9ydGFudDtcblxuICAmOjpiZWZvcmUge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICMzOGExNjksICMyZjg1NWEpICFpbXBvcnRhbnQ7XG4gICAgd2lkdGg6IDRweCAhaW1wb3J0YW50O1xuICAgIGhlaWdodDogMTAwJSAhaW1wb3J0YW50O1xuICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW0gMCAwIDAuNzVyZW0gIWltcG9ydGFudDtcbiAgfVxufVxuXG5cbi8qIENhcmQgQ29udGFpbmVyICovXG4ubm90ZXMtYWN0aW9ucy1jYXJkIHtcbiAgYmFja2dyb3VuZDogI2ZmZjtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xuICBwYWRkaW5nOiAxLjVyZW07XG4gIG1heC13aWR0aDogNzAwcHg7XG4gIG1hcmdpbjogYXV0bztcbn1cblxuLyogSGVhZGVyICovXG4ubm90ZXMtYWN0aW9ucy1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDEuMnJlbTtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU1ZTU7XG4gIHBhZGRpbmctYm90dG9tOiAwLjhyZW07XG59XG5cbi5ub3Rlcy1hY3Rpb25zLWhlYWRlciAuY2FyZC10aXRsZSB7XG4gIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgbWFyZ2luOiAwO1xufVxuXG4uZWRpdC1idG4ge1xuICBiYWNrZ3JvdW5kOiAjMDA3YmZmO1xuICBjb2xvcjogI2ZmZjtcbiAgYm9yZGVyOiBub25lO1xuICBwYWRkaW5nOiA2cHggMTRweDtcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xuICBmb250LXNpemU6IDAuOXJlbTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiA2cHg7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjNzIGVhc2U7XG59XG4uZWRpdC1idG46aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjMDA1NmIzO1xufVxuXG4vKiBCb2R5ICovXG4ubm90ZXMtYWN0aW9ucy1ib2R5IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAxcmVtO1xufVxuXG4ucGVybWl0LWRldGFpbC1pdGVtLWZ1bGwge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xufVxuXG4ucGVybWl0LWRldGFpbC1pdGVtLWZ1bGwgbGFiZWwge1xuICBmb250LXdlaWdodDogNjAwO1xuICBtYXJnaW4tYm90dG9tOiAwLjRyZW07XG4gIGNvbG9yOiAjMzMzO1xufVxuXG4ucGVybWl0LWRldGFpbC1pdGVtLWZ1bGwgdGV4dGFyZWEge1xuICB3aWR0aDogMTAwJTtcbiAgbWluLWhlaWdodDogNjBweDtcbiAgcGFkZGluZzogMC42cmVtO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZDFkNWRiO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIHJlc2l6ZTogdmVydGljYWw7XG4gIGZvbnQtc2l6ZTogMC45NXJlbTtcbn1cblxuLyogS2VuZG8gRHJvcGRvd24gU3R5bGluZyAqL1xuLmtlbmRvLWRyb3Bkb3duIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbn1cblxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "ReviewDetailsModalComponent", "ResponseModalComponent", "AddEditInternalReviewComponent", "PermitPopupComponent", "EditExternalReviewComponent", "AppService", "jsPDF", "autoTable", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PermitViewComponent_div_2_li_20_Template_a_click_1_listener", "$event", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showTab", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "selectedTab", "PermitViewComponent_div_2_button_25_Template_button_click_0_listener", "_r4", "editPermit", "ɵɵelement", "PermitViewComponent_div_2_div_26_Template_button_click_1_listener", "_r5", "downloadInternalReviewsPdf", "PermitViewComponent_div_2_div_26_Template_button_click_3_listener", "addPopUp", "isLoading", "auditEntries", "length", "PermitViewComponent_div_2_div_27_Template_button_click_1_listener", "_r6", "toggleAllSubmittals", "PermitViewComponent_div_2_div_27_Template_button_click_5_listener", "syncPermits", "PermitViewComponent_div_2_div_27_Template_button_click_8_listener", "goToPortal", "externalSubmittals", "areAllSubmittalsExpanded", "ɵɵtextInterpolate", "permit", "permitEntityID", "ɵɵelementContainerStart", "PermitViewComponent_div_2_ng_container_29_Template_button_click_69_listener", "_r7", "notesActionsTemplate_r8", "ɵɵreference", "onEdit", "projectName", "permitType", "primaryContact", "getStatusClass", "permitStatus", "location", "permitCategory", "permitIssueDate", "ɵɵpipeBind2", "ɵɵtextInterpolate1", "permitAppliedDate", "permitExpirationDate", "permitFinalDate", "permitCompleteDate", "internalReviewStatus", "attentionReason", "internalNotes", "actionTaken", "PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template_tr_click_0_listener", "i_r10", "_r9", "index", "selectAudit", "PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template_i_click_30_listener", "editInternalReview", "stopPropagation", "ɵɵclassProp", "selectedAuditIndex", "audit_r11", "title", "internalVerificationStatus", "getStatusStyle", "typeCodeDrawing", "reviewedDate", "completedDate", "internalReviewer", "ɵɵtemplate", "PermitViewComponent_div_2_ng_container_30_div_2_tr_3_Template", "PermitViewComponent_div_2_ng_container_30_div_1_Template", "PermitViewComponent_div_2_ng_container_30_div_2_Template", "PermitViewComponent_div_2_ng_container_31_div_3_Template_button_click_6_listener", "_r12", "fetchExternalReviews", "reviewsError", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template_button_click_0_listener", "_r17", "correction_r18", "$implicit", "review_r16", "openResponseModal", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template_button_click_0_listener", "_r19", "Response", "EORAOROwner_Response", "commentResponsedBy", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_23_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_button_24_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_36_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_37_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_38_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_div_39_Template", "i_r20", "CorrectionTypeName", "CorrectionCategoryName", "ResolvedDate", "shouldShowEditResponseButton", "CorrectiveAction", "Comments", "corrections", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_div_3_Template", "comments", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template_div_click_1_listener", "_r15", "toggleReviewAccordion", "commentsId", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template_i_click_21_listener", "downloadReviewPDF", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_24_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_25_Template", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_div_26_Template", "isReviewExpanded", "ɵɵstyleProp", "FailureFlag", "name", "status", "reviewer", "dueDate", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template_tr_click_1_listener", "i_r14", "_r13", "toggleSubmittalA<PERSON>rdion", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_div_28_Template", "isSubmittalExpanded", "sub_r21", "submittalStatus", "receivedDate", "getExternalReviewsForSubmittal", "id", "PermitViewComponent_div_2_ng_container_31_div_5_ng_container_4_Template", "PermitViewComponent_div_2_ng_container_31_div_3_Template", "PermitViewComponent_div_2_ng_container_31_div_4_Template", "PermitViewComponent_div_2_ng_container_31_div_5_Template", "PermitViewComponent_div_2_Template_button_click_12_listener", "_r1", "goBack", "PermitViewComponent_div_2_Template_a_click_18_listener", "PermitViewComponent_div_2_li_20_Template", "PermitViewComponent_div_2_Template_a_click_22_listener", "PermitViewComponent_div_2_button_25_Template", "PermitViewComponent_div_2_div_26_Template", "PermitViewComponent_div_2_div_27_Template", "PermitViewComponent_div_2_ng_container_29_Template", "PermitViewComponent_div_2_ng_container_30_Template", "PermitViewComponent_div_2_ng_container_31_Template", "permitNumber", "permitReviewType", "permitName", "isInternalReviewEnabled", "PermitViewComponent_ng_template_3_Template_i_click_7_listener", "_r22", "closModal", "PermitViewComponent_ng_template_3_Template_button_click_30_listener", "PermitViewComponent_ng_template_3_Template_button_click_33_listener", "editNotesandactions", "notesForm", "PermitViewComponent", "route", "router", "modalService", "cdr", "fb", "appService", "customLayoutUtilsService", "permitsService", "httpUtilService", "permitId", "selectedAuditName", "selectedAuditStatus", "externalReviews", "selectedExternalSubmittalId", "internalReviews", "loginUser", "isAdmin", "singlePermit", "expandedSubmittals", "Set", "expandedReviews", "reviewSelectedTabs", "routeSubscription", "queryParamsSubscription", "loadingSubscription", "statusList", "text", "value", "previousPage", "projectId", "constructor", "ngOnInit", "getLoggedInUser", "checkIfAdmin", "loadingSubject", "subscribe", "loading", "queryParams", "params", "Number", "console", "log", "paramMap", "idParam", "get", "fetchPermitDetails", "fetchInternalReviews", "loadForm", "group", "detectChanges", "ngOnDestroy", "unsubscribe", "next", "get<PERSON><PERSON><PERSON>", "res", "<PERSON><PERSON><PERSON>", "responseData", "data", "Object", "keys", "error", "faultMessage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAllReviews", "reviews", "map", "r", "TypeName", "AssignedTo", "StatusText", "CompletedDate", "Date", "DueDate", "Corrections", "submittalId", "SubmittalId", "reviewCategory", "idToReviews", "for<PERSON>ach", "rv", "key", "String", "push", "items", "statusOrder", "reduce", "acc", "it", "a", "b", "find", "createdDate", "sort", "getTime", "err", "getInternalReviews", "take", "skip", "review", "reviewComments", "nonComplianceItems", "aeResponse", "grouped", "doc", "orientation", "unit", "format", "pageWidth", "internal", "pageSize", "getWidth", "margin", "left", "right", "top", "y", "addCategory", "category", "setFont", "setFontSize", "toUpperCase", "align", "reviewers", "Array", "from", "toString", "trim", "filter", "v", "join", "responsesDate", "formatDate", "rows", "idx", "startY", "head", "body", "styles", "font", "fontSize", "cellPadding", "valign", "headStyles", "fillColor", "textColor", "halign", "columnStyles", "cellWidth", "theme", "lastAutoTable", "finalY", "getHeight", "addPage", "fileName", "toISOString", "split", "save", "selectExternalSubmittal", "sel", "s", "getExternalReviewsForSelectedSubmittal", "reverse", "has", "delete", "add", "size", "clear", "_", "reviewId", "showReviewTab", "tab", "updateReviewResponse", "formData", "loggedInUserId", "userId", "updateExternalReview", "showSuccess", "message", "showError", "isSelectedSubmittal", "getReviewsForSelectedAudit", "navigate", "activeTab", "window", "open", "cityReviewLink", "openReviewDetails", "modalRef", "componentInstance", "permitDetails", "result", "then", "catch", "normalized", "toLowerCase", "replace", "aliasMap", "resolved", "finalClass", "styleMap", "backgroundColor", "color", "border", "type", "isExternalReviewEnabled", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "reviewIndex", "reviewData", "editPopUp", "passEntry", "saved", "i", "autoLogin", "success", "editEx<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "correction", "responseSubmitted", "submitResponse", "responseCompleted", "emit", "close", "municipalityReviewer", "cityComments", "ownerResponse", "submittalCount", "cycle", "Cycle", "StatusName", "commentstatus", "displayDate", "pageHeight", "bottom", "headers", "rawCorrections", "correctionsArray", "isArray", "bodyRows", "c", "overflow", "fontStyle", "didParseCell", "section", "column", "cell", "didDrawCell", "raw", "isApproved", "bg", "setFillColor", "rect", "x", "width", "height", "setTextColor", "textWidth", "getTextWidth", "textX", "textY", "pageCount", "getNumberOfPages", "setPage", "setDrawColor", "line", "toLocaleString", "roleId", "lockResponse", "template", "patchValue", "dismissAll", "editNotesAndActions", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "NgbModal", "ChangeDetectorRef", "i3", "FormBuilder", "i4", "i5", "CustomLayoutUtilsService", "i6", "PermitsService", "i7", "HttpUtilsService", "selectors", "decls", "vars", "consts", "PermitViewComponent_Template", "rf", "ctx", "PermitViewComponent_div_0_Template", "PermitViewComponent_div_2_Template", "PermitViewComponent_ng_template_3_Template", "ɵɵtemplateRefExtractor"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-view\\permit-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-view\\permit-view.component.html"], "sourcesContent": ["import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { ActivatedRoute, ActivationEnd, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ReviewDetailsModalComponent } from '../review-details-modal/review-details-modal.component';\r\nimport { ResponseModalComponent } from '../response-modal/response-modal.component';\r\nimport { AddEditInternalReviewComponent } from '../add-edit-internal-review/add-edit-internal-review.component';\r\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { EditExternalReviewComponent } from '../edit-external-review/edit-external-review.component';\r\nimport { AppService } from '../../services/app.service';\r\nimport jsPDF from 'jspdf';\r\nimport 'jspdf-autotable';\r\nimport { autoTable } from 'jspdf-autotable';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\n\r\n@Component({\r\n  selector: 'app-permit-view',\r\n  templateUrl: './permit-view.component.html',\r\n  styleUrls: ['./permit-view.component.scss'],\r\n})\r\nexport class PermitViewComponent implements OnInit, OnDestroy {\r\n    notesForm: FormGroup;\r\n\r\n  public permitId: number | null = null;\r\n  public permit: any = null;\r\n  public isLoading: boolean = false; // Main page loader\r\n  public auditEntries: any[] = [];\r\n  public selectedAuditIndex: number = 0; // Set to 0 to make first item initially active\r\n  public selectedAuditName: any = '';\r\n  public selectedAuditStatus: any = '';\r\n  selectedTab: any = 'details';\r\n  permitReviewType:any = '';\r\n  public externalReviews: any[] = [];\r\n  public reviewsError: string = '';\r\n  public externalSubmittals: Array<{ id: any; title: string; submittalStatus: string; receivedDate: Date | null; dueDate: Date | null; completedDate: Date | null; }> = [];\r\n  public selectedExternalSubmittalId: any = null;\r\n  public internalReviews: any[] = [];\r\n  public loginUser:any ={};\r\n  public isAdmin: boolean = false;\r\n  singlePermit: boolean;\r\n  public expandedSubmittals: Set<number> = new Set();\r\n  public expandedReviews: Set<string> = new Set();\r\n  public reviewSelectedTabs: { [key: string]: string } = {};\r\n  private routeSubscription: Subscription = new Subscription();\r\n  private queryParamsSubscription: Subscription = new Subscription();\r\n  private loadingSubscription: Subscription = new Subscription();\r\nstatusList = [\r\n  { text: 'Pending', value: 'Pending' },\r\n  { text: 'In Progress', value: 'In Progress' },\r\n  { text: 'Completed', value: 'Completed' },\r\n  { text: 'On Hold', value: 'On Hold' }\r\n];\r\n  // Navigation tracking\r\n  public previousPage: string = 'permit-list'; // Default fallback\r\n  public projectId: number | null = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private modalService: NgbModal,\r\n    private cdr: ChangeDetectorRef,\r\n    private fb: FormBuilder,\r\n// private modal: NgbActiveModal,\r\n\r\n    private appService:AppService,\r\n        private customLayoutUtilsService: CustomLayoutUtilsService,\r\n\r\n    private permitsService: PermitsService,\r\n    private httpUtilService: HttpUtilsService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n    this.isAdmin = this.checkIfAdmin();\r\n\r\n    // Subscribe to global loading state\r\n    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(\r\n      (loading: any) => {\r\n        this.isLoading = loading === true;\r\n      }\r\n    );\r\n\r\n    // Read query parameters for navigation tracking\r\n    this.queryParamsSubscription = this.route.queryParams.subscribe(params => {\r\n      this.previousPage = params['from'] || 'permit-list';\r\n      this.projectId = params['projectId'] ? Number(params['projectId']) : null;\r\n      console.log('Permit view - query params:', { previousPage: this.previousPage, projectId: this.projectId });\r\n    });\r\n\r\n    // Listen for route parameter changes\r\n    this.routeSubscription = this.route.paramMap.subscribe(params => {\r\n      const idParam = params.get('id');\r\n      this.permitId = idParam ? Number(idParam) : null;\r\n\r\n      if (this.permitId) {\r\n        this.fetchPermitDetails();\r\n        this.fetchExternalReviews();\r\n        this.fetchInternalReviews();\r\n      }\r\n    });\r\n    this.loadForm()\r\n  }\r\n  loadForm() {\r\n    this.notesForm = this.fb.group({\r\n     attentionReason:[''],\r\n     internalNotes:[''],\r\n     actionTaken:[''],\r\n    });\r\n\r\n    // Trigger change detection to update the view\r\n    this.cdr.detectChanges();\r\n  }\r\n  ngOnDestroy(): void {\r\n    if (this.routeSubscription) {\r\n      this.routeSubscription.unsubscribe();\r\n    }\r\n    if (this.queryParamsSubscription) {\r\n      this.queryParamsSubscription.unsubscribe();\r\n    }\r\n    if (this.loadingSubscription) {\r\n      this.loadingSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  public fetchPermitDetails(): void {\r\n    if (!this.permitId) { return; }\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.permitsService.getPermit({ permitId: this.permitId }).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.log('Permit API Response:', res);\r\n        if (!res?.isFault) {\r\n          this.permit = res.responseData?.data || res.responseData || null;\r\n          console.log('Permit data assigned:', this.permit);\r\n          console.log('Permit permitName field:', this.permit?.permitName);\r\n          console.log('All permit fields:', Object.keys(this.permit || {}));\r\n          this.permitReviewType = this.permit?.permitReviewType || '';\r\n          // Default to details tab, user can navigate to reviews as needed\r\n          this.selectedTab = 'details'\r\n        } else {\r\n          console.error('API returned fault:', res.faultMessage);\r\n          this.permit = null;\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: () => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public fetchExternalReviews(): void {\r\n    if (!this.permitId) { return; }\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.reviewsError = '';\r\n    this.permitsService.getAllReviews({ permitId: this.permitId }).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (res?.isFault) {\r\n          this.reviewsError = res.faultMessage || 'Failed to load reviews';\r\n        } else {\r\n          const reviews = res.responseData?.reviews || [];\r\n          this.externalReviews = reviews.map((r: any) => ({\r\n            commentsId:r.commentsId,\r\n            name: r.TypeName,\r\n            reviewer: r.AssignedTo,\r\n            status: r.StatusText,\r\n            completedDate: r.CompletedDate ? new Date(r.CompletedDate) : null,\r\n            dueDate: r.DueDate ? new Date(r.DueDate) : null,\r\n            receivedDate: r.receivedDate ? new Date(r.receivedDate) : null,\r\n            comments: r.Comments,\r\n            corrections: r.Corrections || [],\r\n            submittalId: r.SubmittalId,\r\n            FailureFlag: r.FailureFlag,\r\n            reviewCategory: r.reviewCategory,\r\n            EORAOROwner_Response: r.EORAOROwner_Response,\r\n            commentResponsedBy: r.commentResponsedBy\r\n          }));\r\n\r\n          // Build submittal list grouped from reviews\r\n          const idToReviews: { [key: string]: any[] } = {};\r\n          this.externalReviews.forEach((rv: any) => {\r\n            const key = String(rv.submittalId ?? 'unknown');\r\n            if (!idToReviews[key]) { idToReviews[key] = []; }\r\n            idToReviews[key].push(rv);\r\n          });\r\n\r\n          this.externalSubmittals = Object.keys(idToReviews).map((key) => {\r\n            const items = idToReviews[key];\r\n            // Determine status priority: Requires Re-submit > Under Review > Approved w/ Conditions > Approved\r\n            const statusOrder: any = {\r\n              'Requires Re-submit': 4,\r\n              'Under Review': 3,\r\n              'Approved w/ Conditions': 2,\r\n              'Approved': 1\r\n            };\r\n            const submittalStatus = items.reduce((acc: string, it: any) => {\r\n              const a = statusOrder[acc] || 0; const b = statusOrder[it.status] || 0; return b > a ? it.status : acc;\r\n            }, '');\r\n\r\n            // Aggregate dates\r\n            const dueDate = items.reduce((acc: Date | null, it: any) => {\r\n              if (!it.dueDate) { return acc; }\r\n              if (!acc) { return it.dueDate; }\r\n              return acc > it.dueDate ? it.dueDate : acc; // earliest due date\r\n            }, null as Date | null);\r\n\r\n            const completedDate = items.reduce((acc: Date | null, it: any) => {\r\n              if (!it.completedDate) { return acc; }\r\n              if (!acc) { return it.completedDate; }\r\n              return acc < it.completedDate ? it.completedDate : acc; // latest completed date\r\n            }, null as Date | null);\r\n\r\n            // Get received date from the first item that has it\r\n            const receivedDate = items.find((it: any) => it.receivedDate)?.receivedDate || \r\n                                items.find((it: any) => it.createdDate)?.createdDate || \r\n                                null;\r\n\r\n            // Get submittal name from the first item (all items in this group have same submittalId)\r\n            const reviewCategory = items[0]?.reviewCategory || `Submittal ${key}`;\r\n\r\n            return {\r\n              id: key,\r\n              title: reviewCategory,\r\n              submittalStatus: submittalStatus || (items[0]?.status || ''),\r\n              receivedDate: receivedDate ? new Date(receivedDate) : null,\r\n              dueDate: dueDate,\r\n              completedDate: completedDate\r\n            };\r\n          }).sort((a, b) => {\r\n            // Sort by received date in descending order (latest first)\r\n            if (!a.receivedDate && !b.receivedDate) return 0;\r\n            if (!a.receivedDate) return 1;\r\n            if (!b.receivedDate) return -1;\r\n            return b.receivedDate.getTime() - a.receivedDate.getTime();\r\n          });\r\n\r\n          // Select first submittal by default\r\n          if (this.externalSubmittals.length > 0) {\r\n            this.selectedExternalSubmittalId = this.externalSubmittals[0].id;\r\n            this.selectedAuditName = this.externalSubmittals[0].title;\r\n            this.selectedAuditStatus = this.externalSubmittals[0].submittalStatus;\r\n          }\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.reviewsError = 'Failed to load reviews';\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public fetchInternalReviews(): void {\r\n    if (!this.permitId) { return; }\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    this.permitsService.getInternalReviews({\r\n      permitId: this.permitId,\r\n      take: 50,\r\n      skip: 0\r\n    }).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (res?.isFault) {\r\n          console.error('Failed to load internal reviews:', res.faultMessage);\r\n        } else {\r\n          this.internalReviews = res.responseData?.data || res.data || [];\r\n\r\n          // Transform internal reviews to match the auditEntries format\r\n          this.auditEntries = this.internalReviews.map((review: any) => ({\r\n            commentsId: review.commentsId,\r\n            title: review.reviewCategory,\r\n            reviewCategory: review.reviewCategory, // Preserve reviewCategory for edit modal\r\n            typeCodeDrawing: review.typeCodeDrawing,\r\n            reviewComments: review.reviewComments,\r\n            nonComplianceItems: review.nonComplianceItems,\r\n            aeResponse: review.aeResponse,\r\n            internalReviewer: review.internalReviewer,\r\n            internalVerificationStatus: review.internalVerificationStatus,\r\n            reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\r\n            completedDate: review.completedDate ? new Date(review.completedDate) : null,\r\n            reviews: [{\r\n              name: review.reviewCategory,\r\n              typeCodeDrawing: review.typeCodeDrawing,\r\n              reviewComments: review.reviewComments,\r\n              nonComplianceItems: review.nonComplianceItems,\r\n              aeResponse: review.aeResponse,\r\n              internalReviewer: review.internalReviewer,\r\n              internalVerificationStatus: review.internalVerificationStatus,\r\n              reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\r\n              completedDate: review.completedDate ? new Date(review.completedDate) : null\r\n            }]\r\n          }));\r\n\r\n          // Select first internal review by default\r\n          if (this.auditEntries.length > 0) {\r\n            this.selectedAuditIndex = 0;\r\n            this.selectedAuditName = this.auditEntries[0].title;\r\n            this.selectedAuditStatus = this.auditEntries[0].internalVerificationStatus || 'Pending';\r\n          }\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error loading internal reviews:', err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public downloadInternalReviewsPdf(): void {\r\n    if (!this.internalReviews || this.internalReviews.length === 0) { return; }\r\n\r\n    const grouped: { [category: string]: any[] } = {};\r\n    this.internalReviews.forEach((r: any) => {\r\n      const key = r.reviewCategory || 'Uncategorized';\r\n      if (!grouped[key]) { grouped[key] = []; }\r\n      grouped[key].push(r);\r\n    });\r\n\r\n    const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\r\n    const pageWidth = doc.internal.pageSize.getWidth();\r\n    // Use more horizontal space: reduce side margins to ~0.5 inch (36pt)\r\n    const margin = { left: 36, right: 36, top: 40 };\r\n    let y = margin.top;\r\n\r\n    const addCategory = (category: string, items: any[]) => {\r\n      // Category block title (centered, uppercase)\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(10);\r\n      doc.text(`${category.toUpperCase()} REVIEW COMMENTS`, pageWidth / 2, y, {\r\n        align: 'center'\r\n      });\r\n      y += 12;\r\n\r\n      // Reviewer line (take distinct non-empty names, join with comma)\r\n      const reviewers = Array.from(new Set(items\r\n        .map((it: any) => (it.internalReviewer || '').toString().trim())\r\n        .filter((v: string) => v)));\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(10);\r\n      doc.text(`Reviewer: ${reviewers.join(', ') || ''}`, pageWidth / 2, y, { align: 'center' });\r\n      y += 12;\r\n\r\n      // Dates line (Reviewed Date / Responses Date)\r\n      const reviewedDate = items.find((it: any) => it.reviewedDate)?.reviewedDate;\r\n      const responsesDate = items.find((it: any) => it.completedDate)?.completedDate;\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.setFontSize(9);\r\n      doc.text(\r\n        `Reviewed Date: ${reviewedDate ? AppService.formatDate(reviewedDate) : ''}`,\r\n        margin.left,\r\n        y\r\n      );\r\n      doc.text(\r\n        `Response Date: ${responsesDate ? AppService.formatDate(responsesDate) : ''}`,\r\n        pageWidth - margin.right,\r\n        y,\r\n        { align: 'right' }\r\n      );\r\n      y += 6;\r\n\r\n      const rows = items.map((it, idx) => [\r\n        (idx + 1).toString(),\r\n        it.typeCodeDrawing || '',\r\n        (it.reviewComments || '').toString(),\r\n        (it.aeResponse || '').toString(),\r\n        it.internalVerificationStatus || ''\r\n      ]);\r\n\r\n      autoTable(doc, {\r\n        startY: y + 5,\r\n        head: [[\r\n          '#',\r\n          'Drawing #',\r\n          'Review Comments',\r\n          'A/E Response',\r\n          'Status'\r\n        ]],\r\n        body: rows,\r\n        margin: { left: margin.left, right: margin.right },\r\n        styles: { font: 'helvetica', fontSize: 8, cellPadding: 5, valign: 'top' },\r\n        headStyles: { fillColor: [33, 150, 243], textColor: 255, halign: 'center', fontSize: 9 },\r\n        // Fit exactly into available width (pageWidth - margins)\r\n        // A4 width ~595pt; with 36pt margins each side → 523pt content width\r\n        columnStyles: {\r\n          0: { cellWidth: 24, halign: 'center' },   // #\r\n          1: { cellWidth: 55 },                     // Drawing # (even narrower)\r\n          2: { cellWidth: 198 },                    // Review Comments (half of remaining)\r\n          3: { cellWidth: 197 },                    // A/E Response (other half)\r\n          4: { cellWidth: 49 }                      // Verification Status (fits)\r\n        },\r\n        theme: 'grid'\r\n      });\r\n\r\n      // update y for next section\r\n      // @ts-ignore\r\n      y = (doc as any).lastAutoTable.finalY + 20;\r\n\r\n      // add page if needed\r\n      if (y > doc.internal.pageSize.getHeight() - 100) {\r\n        doc.addPage();\r\n        y = margin.top;\r\n      }\r\n    };\r\n\r\n    Object.keys(grouped).forEach((category, idx) => {\r\n      // Removed divider line between categories\r\n      addCategory(category, grouped[category]);\r\n    });\r\n\r\n    const fileName = `Internal_Reviews_${this.permit?.permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\r\n    doc.save(fileName);\r\n  }\r\n\r\n  public selectExternalSubmittal(id: any): void {\r\n    this.selectedExternalSubmittalId = id;\r\n    const sel = this.externalSubmittals.find(s => String(s.id) === String(id));\r\n    if (sel) {\r\n      this.selectedAuditName = sel.title;\r\n      this.selectedAuditStatus = sel.submittalStatus;\r\n    }\r\n  }\r\n\r\n  public getExternalReviewsForSelectedSubmittal(): any[] {\r\n    if (!this.selectedExternalSubmittalId) { return []; }\r\n    return this.getExternalReviewsForSubmittal(this.selectedExternalSubmittalId);\r\n  }\r\n\r\n  public getExternalReviewsForSubmittal(submittalId: any): any[] {\r\n    const reviews = this.externalReviews.filter(r => String(r.submittalId) === String(submittalId));\r\n    \r\n    // Sort by FailureFlag: false reviews first (desc), then true reviews (desc)\r\n    return reviews.sort((a, b) => {\r\n      // If both have the same FailureFlag value, maintain original order (reverse for desc)\r\n      if (a.FailureFlag === b.FailureFlag) {\r\n        return 0;\r\n      }\r\n      \r\n      // False reviews (FailureFlag = false) come first\r\n      if (!a.FailureFlag && b.FailureFlag) {\r\n        return -1;\r\n      }\r\n      \r\n      // True reviews (FailureFlag = true) come after false reviews\r\n      if (a.FailureFlag && !b.FailureFlag) {\r\n        return 1;\r\n      }\r\n      \r\n      return 0;\r\n    }).reverse(); // Reverse to get descending order within each group\r\n  }\r\n\r\n  public toggleSubmittalAccordion(index: number): void {\r\n    if (this.expandedSubmittals.has(index)) {\r\n      this.expandedSubmittals.delete(index);\r\n    } else {\r\n      this.expandedSubmittals.add(index);\r\n    }\r\n  }\r\n\r\n  public isSubmittalExpanded(index: number): boolean {\r\n    return this.expandedSubmittals.has(index);\r\n  }\r\n\r\n  public areAllSubmittalsExpanded(): boolean {\r\n    return this.externalSubmittals && this.externalSubmittals.length > 0 && this.expandedSubmittals.size === this.externalSubmittals.length;\r\n  }\r\n\r\n  public toggleAllSubmittals(): void {\r\n    if (!this.externalSubmittals || this.externalSubmittals.length === 0) {\r\n      return;\r\n    }\r\n    if (this.areAllSubmittalsExpanded()) {\r\n      this.expandedSubmittals.clear();\r\n    } else {\r\n      this.expandedSubmittals = new Set(this.externalSubmittals.map((_, idx) => idx));\r\n    }\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  public toggleReviewAccordion(reviewId: string): void {\r\n    if (this.expandedReviews.has(reviewId)) {\r\n      this.expandedReviews.delete(reviewId);\r\n    } else {\r\n      this.expandedReviews.add(reviewId);\r\n      // Set default tab for this review if not already set\r\n      if (!this.reviewSelectedTabs[reviewId]) {\r\n        this.reviewSelectedTabs[reviewId] = 'corrections';\r\n      }\r\n    }\r\n  }\r\n\r\n  public isReviewExpanded(reviewId: string): boolean {\r\n    return this.expandedReviews.has(reviewId);\r\n  }\r\n\r\n  public showReviewTab(reviewId: string, tab: string, $event: any): void {\r\n    $event.stopPropagation();\r\n    this.reviewSelectedTabs[reviewId] = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  public updateReviewResponse(review: any): void {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    const formData = {\r\n      EORAOROwner_Response: review.EORAOROwner_Response,\r\n      commentResponsedBy: review.commentResponsedBy,\r\n      permitId: this.permitId,\r\n      commentsId: review.commentsId,\r\n      loggedInUserId: this.loginUser.userId\r\n    };\r\n\r\n    this.permitsService.updateExternalReview(formData).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (res?.isFault === false) {\r\n          //alert(res.responseData.message);\r\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n          // Refresh external reviews to get updated data\r\n          this.fetchExternalReviews();\r\n        } else {\r\n          this.customLayoutUtilsService.showError(res.faultMessage||'❌ Error syncing permit', '');\r\n          //alert(res.faultMessage || 'Failed to update review response');\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.customLayoutUtilsService.showError('❌ Error updating review response', '');\r\n        //alert('Error updating review response');\r\n        console.error(err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public isSelectedSubmittal(submittalId: any): boolean {\r\n    return String(this.selectedExternalSubmittalId) === String(submittalId);\r\n  }\r\n\r\n  public selectAudit(index: number): void {\r\n    this.selectedAuditIndex = index;\r\n    this.selectedAuditName = this.auditEntries[this.selectedAuditIndex].title;\r\n    this.selectedAuditStatus =\r\n      this.auditEntries[this.selectedAuditIndex].submittalStatus;\r\n  }\r\n\r\n  public getReviewsForSelectedAudit(): any[] {\r\n    if (\r\n      this.selectedAuditIndex === null ||\r\n      this.selectedAuditIndex >= this.auditEntries.length\r\n    ) {\r\n      return [];\r\n    }\r\n\r\n    return this.auditEntries[this.selectedAuditIndex].reviews || [];\r\n  }\r\n\r\n\r\n\r\n  public goBack(): void {\r\n    console.log('goBack called - previousPage:', this.previousPage, 'projectId:', this.projectId);\r\n    if (this.previousPage === 'project' && this.projectId) {\r\n      // Navigate back to the specific project view with permits tab active\r\n      console.log('Navigating to project view with permits tab active');\r\n      this.router.navigate(['/projects/view', this.projectId], { \r\n        queryParams: { activeTab: 'permits' } \r\n      });\r\n    } else {\r\n      // Default to permit list\r\n      console.log('Navigating to permit list');\r\n      this.router.navigate(['/permits/list']);\r\n    }\r\n  }\r\n\r\n  public goToPortal(): void {\r\n    window.open(\r\n      `${this.permit.cityReviewLink + this.permit.permitEntityID}`,\r\n      '_blank'\r\n    );\r\n  }\r\n\r\n  public openReviewDetails(review: any): void {\r\n    const modalRef = this.modalService.open(ReviewDetailsModalComponent, {\r\n      size: 'lg',\r\n    });\r\n    modalRef.componentInstance.review = review;\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.permitDetails = this.permit;\r\n\r\n    // Handle modal result\r\n    modalRef.result.then((result) => {\r\n      if (result === 'created' || result === 'updated') {\r\n        // Refresh internal reviews\r\n        this.fetchExternalReviews();\r\n      }\r\n    }).catch((error) => {\r\n      // Modal was dismissed\r\n      console.log('Modal dismissed');\r\n    });\r\n  }\r\n\r\n  public getStatusClass(status: string): string {\r\n    if (!status) return 'status-n-a';\r\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\r\n    console.log('Status mapping - Original:', status, 'Normalized:', normalized);\r\n    // Map common synonyms from API to a single class we style\r\n    const aliasMap: { [k: string]: string } = {\r\n      'in-review': 'in-review',\r\n      'requires-re-submit': 'requires-re-submit',\r\n      'approved-w-conditions': 'approved-w-conditions',\r\n      'in-progress': 'in-progress',\r\n      'completed': 'completed',\r\n      'verified': 'verified',\r\n      'pending': 'pending',\r\n      'rejected': 'rejected',\r\n      'approved': 'approved',\r\n      'under-review': 'under-review',\r\n      'requires-resubmit': 'requires-resubmit',\r\n      'pacifica-verification': 'pacifica-verification',\r\n      'dis-approved': 'dis-approved',\r\n      'not-required': 'not-required',\r\n      '1-cycle-completed': '1-cycle-completed',\r\n      '1 cycle completed': '1-cycle-completed',\r\n      'cycle completed': '1-cycle-completed'\r\n    };\r\n    const resolved = aliasMap[normalized] || normalized;\r\n    const finalClass = 'status-' + resolved;\r\n    console.log('Final status class:', finalClass);\r\n    console.log('Available CSS classes for debugging:', [\r\n      'status-pending', 'status-in-progress', 'status-completed', 'status-verified',\r\n      'status-rejected', 'status-approved', 'status-under-review', 'status-requires-resubmit',\r\n      'status-pacifica-verification', 'status-dis-approved', 'status-not-required',\r\n      'status-in-review', 'status-1-cycle-completed'\r\n    ]);\r\n    return finalClass;\r\n  }\r\n\r\n  public getStatusStyle(status: string): any {\r\n    if (!status) return {};\r\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\r\n    \r\n    const styleMap: { [k: string]: any } = {\r\n      '1-cycle-completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\r\n      '1 cycle completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\r\n      'cycle completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\r\n      'pacifica-verification': { backgroundColor: '#e1f5fe', color: '#0277bd', border: '1px solid #81d4fa' },\r\n      'dis-approved': { backgroundColor: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' },\r\n      'not-required': { backgroundColor: '#f5f5f5', color: '#757575', border: '1px solid #e0e0e0' },\r\n      'in-review': { backgroundColor: '#e8eaf6', color: '#3949ab', border: '1px solid #c5cae9' },\r\n      'pending': { backgroundColor: '#fff3e0', color: '#e65100', border: '1px solid #ffcc02' },\r\n      'approved': { backgroundColor: '#e8f5e8', color: '#1b5e20', border: '1px solid #c8e6c9' },\r\n      'completed': { backgroundColor: '#e8f5e8', color: '#1b5e20', border: '1px solid #c8e6c9' },\r\n      'rejected': { backgroundColor: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' }\r\n    };\r\n    \r\n    return styleMap[normalized] || {};\r\n  }\r\n\r\n  showTab(tab: any, $event: any) {\r\n    if (tab === 'internal' && !this.isInternalReviewEnabled()) {\r\n      return;\r\n    }\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  public isInternalReviewEnabled(): boolean {\r\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\r\n    // show only when type is 'internal' or 'both'\r\n    return type === 'internal' || type === 'both';\r\n  }\r\n\r\n  public isExternalReviewEnabled(): boolean {\r\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\r\n    // show only when type is 'external' or 'both'\r\n    return type === 'external' || type === 'both';\r\n  }\r\n\r\n  addPopUp() {\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the AddEditInternalReviewComponent\r\n    const modalRef = this.modalService.open(\r\n      AddEditInternalReviewComponent,\r\n      NgbModalOptions\r\n    );\r\n\r\n    // Pass data to the modal\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\r\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\r\n\r\n    // Handle modal result\r\n    modalRef.result.then((result) => {\r\n      if (result === 'created' || result === 'updated') {\r\n        // Refresh internal reviews\r\n        this.fetchInternalReviews();\r\n      }\r\n    }).catch((error) => {\r\n      // Modal was dismissed\r\n      console.log('Modal dismissed');\r\n    });\r\n  }\r\n\r\n  editInternalReview(reviewIndex: number) {\r\n    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {\r\n      return;\r\n    }\r\n\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg',\r\n      backdrop: 'static',\r\n      keyboard: false,\r\n      scrollable: true,\r\n    };\r\n\r\n    const modalRef = this.modalService.open(\r\n      AddEditInternalReviewComponent,\r\n      NgbModalOptions\r\n    );\r\n\r\n    // Pass data to the modal for editing\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.reviewData = this.auditEntries[reviewIndex];\r\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\r\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\r\n\r\n    // Handle modal result\r\n    modalRef.result.then((result) => {\r\n      if (result === 'updated') {\r\n        // Refresh internal reviews\r\n        this.fetchInternalReviews();\r\n      }\r\n    }).catch((error) => {\r\n      console.log('Modal dismissed');\r\n    });\r\n  }\r\n\r\n  editPopUp() {\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the ProjectPopup\r\n    const modalRef = this.modalService.open(\r\n      AddEditInternalReviewComponent,\r\n      NgbModalOptions\r\n    );\r\n  }\r\n  editPermit() {\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the ProjectPopup\r\n    const modalRef = this.modalService.open(\r\n      PermitPopupComponent,\r\n      NgbModalOptions\r\n    );\r\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\r\n    modalRef.componentInstance.id = this.permitId;\r\n    \r\n    // Listen for passEntry event to refresh permit details when permit is saved\r\n    modalRef.componentInstance.passEntry.subscribe((saved: boolean) => {\r\n      if (saved) {\r\n        this.fetchPermitDetails();\r\n      }\r\n    });\r\n  }\r\n\r\n  syncPermits(i: any) {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.singlePermit = i || false;\r\n    this.permitsService.syncPermits({ permitId: this.permitId, singlePermit: this.singlePermit, autoLogin: true }).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.log('Sync response:', res);\r\n        console.log('Response type:', typeof res);\r\n        console.log('Response keys:', Object.keys(res || {}));\r\n        console.log('Response success:', res?.success);\r\n        console.log('Response message:', res?.message);\r\n        console.log('Response responseData:', res?.responseData);\r\n        \r\n        // Handle various response structures\r\n        let responseData = res;\r\n        \r\n        // Check different possible response structures\r\n        if (res?.responseData) {\r\n          responseData = res.responseData;\r\n        } else if (res?.body?.responseData) {\r\n          responseData = res.body.responseData;\r\n        } else if (res?.body) {\r\n          responseData = res.body;\r\n        }\r\n        \r\n        console.log('Final responseData:', responseData);\r\n        console.log('Final success:', responseData?.success);\r\n        console.log('Final message:', responseData?.message);\r\n        \r\n        if (responseData?.isFault) {\r\n          //alert(responseData.faultMessage || 'Failed to sync permit');\r\n          this.customLayoutUtilsService.showError(responseData.faultMessage, '');\r\n        } else if (responseData?.success === false) {\r\n          // Handle specific error messages from the API\r\n          if (responseData.message === 'Permit not found in Energov system') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\r\n          } else if (responseData.message === 'No permits found for any keywords') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\r\n          } else {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\r\n          }\r\n        } else if (responseData?.success === true || responseData?.data) {\r\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\r\n          //alert('✅ Permit synced successfully');\r\n          this.fetchPermitDetails();\r\n          this.fetchExternalReviews();\r\n        } else {\r\n          // Fallback for unknown response structure\r\n          console.log('Unknown response structure, showing generic success');\r\n          //alert('✅ Permit synced successfully');\r\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\r\n\r\n          this.fetchPermitDetails();\r\n          this.fetchExternalReviews();\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        // Handle HTTP error responses\r\n        console.log('Error response:', err);\r\n        console.log('Error type:', typeof err);\r\n        console.log('Error keys:', Object.keys(err || {}));\r\n        console.log('Error status:', err?.status);\r\n        console.log('Error message:', err?.message);\r\n        console.log('Error error:', err?.error);\r\n        \r\n        // The interceptor passes err.error to the error handler\r\n        // So err might actually be the response data\r\n        if (err?.success === false) {\r\n          // Handle specific error messages from the API\r\n          if (err.message === 'Permit not found in Energov system') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\r\n          } else if (err.message === 'No permits found for any keywords') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\r\n          } else {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\r\n          }\r\n        } else if (err?.error?.message) {\r\n          if (err.error.message === 'Permit not found in Energov system') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\r\n          } else if (err.error.message === 'No permits found for any keywords') {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\r\n          } else {\r\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n            //alert(`❌ ${err.error.message}`);\r\n          }\r\n        } else if (err?.status === 404) {\r\n                                  this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\r\n\r\n          // Handle 404 specifically for permit not found\r\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\r\n        } else {\r\n          //alert('❌ Error syncing permit');\r\n        }\r\n        console.error(err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  editExternalReview(review:any) {\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the AddEditInternalReviewComponent\r\n    const modalRef = this.modalService.open(\r\n      EditExternalReviewComponent,\r\n      NgbModalOptions\r\n    );\r\n\r\n    console.log('reviewData ', review)\r\n    // Pass data to the modal\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.reviewData = review;\r\n    modalRef.componentInstance.permitDetails = this.permit;\r\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\r\n\r\n    // Handle modal result\r\n    modalRef.result.then((result) => {\r\n      if (result === 'created' || result === 'updated') {\r\n        // Refresh internal reviews\r\n        this.fetchExternalReviews();\r\n      }\r\n    }).catch((error) => {\r\n      // Modal was dismissed\r\n      console.log('Modal dismissed');\r\n    });\r\n  }\r\n\r\n  public openResponseModal(correction: any, review: any): void {\r\n    // Open the modal using NgbModal\r\n    const modalRef = this.modalService.open(ResponseModalComponent, {\r\n      size: 'lg',\r\n      backdrop: 'static',\r\n      keyboard: false\r\n    });\r\n\r\n    // Pass data to the modal\r\n    modalRef.componentInstance.correction = correction;\r\n    modalRef.componentInstance.review = review;\r\n    modalRef.componentInstance.permitId = this.permitId;\r\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;\r\n    modalRef.componentInstance.isAdmin = this.isAdmin;\r\n\r\n    // Handle modal result\r\n    modalRef.componentInstance.responseSubmitted.subscribe((formData: any) => {\r\n      this.submitResponse(formData, modalRef);\r\n    });\r\n\r\n    // Handle response completion to reset loading state\r\n    modalRef.componentInstance.responseCompleted.subscribe((success: boolean) => {\r\n      if (!success) {\r\n        // Reset loading state if submission failed\r\n        modalRef.componentInstance.isLoading = false;\r\n      }\r\n    });\r\n\r\n    modalRef.result.then(() => {\r\n      // Modal was closed\r\n    }).catch(() => {\r\n      // Modal was dismissed\r\n    });\r\n  }\r\n\r\n  public submitResponse(formData: any, modalRef?: any): void {\r\n    if (!formData) {\r\n      return;\r\n    }\r\n\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    this.permitsService.updateExternalReview(formData).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (res?.isFault === false) {\r\n                                  this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n\r\n          //alert(res.responseData.message || 'Response submitted successfully');\r\n          this.fetchExternalReviews(); // Refresh external reviews to get updated data\r\n          \r\n          // Emit success completion event\r\n          if (modalRef && modalRef.componentInstance) {\r\n            modalRef.componentInstance.responseCompleted.emit(true);\r\n          }\r\n          \r\n          // Close the modal if it was passed\r\n          if (modalRef) {\r\n            modalRef.close();\r\n          }\r\n        } else {\r\n          //alert(res.faultMessage || 'Failed to submit response');\r\n                                  this.customLayoutUtilsService.showError(res.faultMessage||'Failed to submit response', '');\r\n\r\n          // Emit failure completion event\r\n          if (modalRef && modalRef.componentInstance) {\r\n            modalRef.componentInstance.responseCompleted.emit(false);\r\n          }\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        //alert('Error submitting response');\r\n        console.error(err);\r\n                                this.customLayoutUtilsService.showSuccess('Error submitting response', '');\r\n\r\n        // Emit failure completion event\r\n        if (modalRef && modalRef.componentInstance) {\r\n          modalRef.componentInstance.responseCompleted.emit(false);\r\n        }\r\n        \r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public downloadReviewPDF(review: any): void {\r\n    if (!review) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const permitNumber = this.permit?.permitNumber || '';\r\n      const reviewer = (review?.AssignedTo || review?.municipalityReviewer || review?.reviewer || '').toString();\r\n      const cityComments = (review?.Comments || (review?.cityComments ?? '')).toString();\r\n      const ownerResponse = (review?.EORAOROwner_Response ?? '').toString();\r\n      \r\n      // Calculate submittal count for this review\r\n      const submittalCount = this.externalSubmittals ? this.externalSubmittals.length : 0;\r\n      const cycle = submittalCount > 0 ? submittalCount.toString() : (review?.cycle || review?.Cycle || '').toString();\r\n      \r\n      const status = (review?.StatusName || review?.commentstatus || review?.status || '').toString();\r\n      const displayDate = (review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate)\r\n        ? AppService.formatDate(review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate)\r\n        : AppService.formatDate(new Date());\r\n\r\n      const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\r\n\r\n      const pageWidth = doc.internal.pageSize.getWidth();\r\n      const pageHeight = doc.internal.pageSize.getHeight();\r\n      const margin = { left: 40, right: 40, top: 40, bottom: 40 };\r\n\r\n      // Header: Permit Number\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.setFontSize(10);\r\n      doc.text(`Permit #: ${permitNumber || ''}`, margin.left, margin.top);\r\n\r\n      const startY = margin.top + 20;\r\n\r\n      // Table with one row matching the screenshot\r\n      const headers = [\r\n        'REVIEWED BY',\r\n        'CITY COMMENTS',\r\n        'EOR/AOR/OWNER COMMENT RESPONSE',\r\n        'CYCLE',\r\n        'STATUS',\r\n        'DATE'\r\n      ];\r\n\r\n      // Build body rows. If there are corrections, show one row per correction; otherwise single row\r\n      const rawCorrections: any = (review && (review as any).Corrections) ?? (review as any).corrections ?? [];\r\n      const correctionsArray = Array.isArray(rawCorrections) ? rawCorrections : (rawCorrections ? [rawCorrections] : []);\r\n      const bodyRows: any[] = correctionsArray.length > 0\r\n        ? correctionsArray.map((c: any) => [\r\n            reviewer || '',\r\n            c?.Comments || cityComments || '',\r\n            c?.Response || c?.EORAOROwner_Response || ownerResponse || '',\r\n            cycle || '',\r\n            status || '',\r\n            (c?.ResolvedDate ? AppService.formatDate(c.ResolvedDate) : displayDate) || ''\r\n          ])\r\n        : [[\r\n            reviewer || '',\r\n            cityComments || '',\r\n            ownerResponse || '',\r\n            cycle || '',\r\n            status || '',\r\n            displayDate || ''\r\n          ]];\r\n\r\n      autoTable(doc, {\r\n        startY,\r\n        head: [headers],\r\n        body: bodyRows,\r\n        margin: { left: margin.left, right: margin.right },\r\n        styles: { font: 'helvetica', fontSize: 8, cellPadding: 5, overflow: 'linebreak' },\r\n        headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold', fontSize: 8 },\r\n        columnStyles: {\r\n          0: { cellWidth: 90 },\r\n          1: { cellWidth: (pageWidth - margin.left - margin.right) * 0.26 },\r\n          2: { cellWidth: (pageWidth - margin.left - margin.right) * 0.24 },\r\n          3: { cellWidth: 45, halign: 'center' },\r\n          4: { cellWidth: 60, halign: 'center' },\r\n          5: { cellWidth: 60, halign: 'center' }\r\n        },\r\n        didParseCell: (data: any) => {\r\n          // City comments text in red\r\n          if (data.section === 'body' && data.column.index === 1) {\r\n            data.cell.styles.textColor = [192, 0, 0];\r\n          }\r\n        },\r\n        didDrawCell: (data: any) => {\r\n          // Fully colored background for Status cell\r\n          if (data.section === 'body' && data.column.index === 4) {\r\n            const value = String(data.cell.raw || '');\r\n            const isApproved = value.toLowerCase() === 'approved';\r\n            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\r\n            const textColor = [255, 255, 255];\r\n\r\n            // fill whole cell\r\n            doc.setFillColor(bg[0], bg[1], bg[2]);\r\n            doc.rect(data.cell.x + 0.5, data.cell.y + 0.5, data.cell.width - 1, data.cell.height - 1, 'F');\r\n\r\n            // write centered white text\r\n            doc.setTextColor(textColor[0], textColor[1], textColor[2]);\r\n            doc.setFont('helvetica', 'bold');\r\n            doc.setFontSize(8);\r\n            const textWidth = doc.getTextWidth(value);\r\n            const textX = data.cell.x + data.cell.width / 2 - textWidth / 2;\r\n            const textY = data.cell.y + data.cell.height / 2 + 3;\r\n            doc.text(value, textX, textY);\r\n\r\n            data.cell.text = [];\r\n          }\r\n        },\r\n        theme: 'grid'\r\n      });\r\n\r\n      // Footer\r\n      const pageCount = doc.getNumberOfPages();\r\n      for (let i = 1; i <= pageCount; i++) {\r\n        doc.setPage(i);\r\n        doc.setDrawColor(200, 200, 200);\r\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\r\n        doc.setFont('helvetica', 'normal');\r\n        doc.setFontSize(8);\r\n        doc.setTextColor(100, 100, 100);\r\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\r\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\r\n      }\r\n\r\n      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\r\n      doc.save(fileName);\r\n      \r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n                              this.customLayoutUtilsService.showSuccess('Error generation PDF. Please try agaiin', '');\r\n\r\n      //alert('Error generating PDF. Please try again.');\r\n    }\r\n  }\r\n\r\n  public checkIfAdmin(): boolean {\r\n    // Check if the user is an admin based on roleId\r\n    // Assuming roleId 1 is admin - adjust this based on your role system\r\n    return this.loginUser && this.loginUser.roleId === 1;\r\n  }\r\n\r\n  public shouldShowEditResponseButton(correction: any): boolean {\r\n    // Show edit response button if:\r\n    // 1. User is admin (can always edit)\r\n    // 2. User is not admin but lockResponse is false (unlocked by admin)\r\n    if (this.isAdmin) {\r\n      return true;\r\n    }\r\n    \r\n    // For non-admin users, only show if lockResponse is explicitly false\r\n    return correction.lockResponse === false;\r\n  }\r\n  onEdit(template: any){\r\n const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n    this.modalService.open(template,NgbModalOptions)\r\n\r\n    // console.log(\"this.permit\", this.permit)\r\n    this.notesForm.patchValue({\r\n      actionTaken:this.permit.actionTaken,\r\n      attentionReason:this.permit.attentionReason,\r\n      internalNotes:this.permit.internalNotes,\r\n      // actionTaken:'helo',\r\n    })\r\n\r\n  }\r\n\r\n  closModal(){\r\n    this.modalService.dismissAll()\r\n  }\r\n\r\n  \r\n  public editNotesandactions(): void {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    const formData = {\r\n     permitId: this.permitId,\r\n      actionTaken: this.notesForm.value.actionTaken,\r\n      internalNotes: this.notesForm.value.internalNotes,\r\n      attentionReason: this.notesForm.value.attentionReason,\r\n      \r\n    };\r\n\r\n    this.permitsService.editNotesAndActions(formData).subscribe({\r\n      next: (res: any) => {\r\n        this.closModal()\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (res?.isFault === false) {\r\n          //alert(res.responseData.message);\r\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n          // Refresh permit details to get updated data from server\r\n          this.fetchPermitDetails();\r\n        } else {\r\n          this.customLayoutUtilsService.showError(res.faultMessage||'❌ Error in update notes and actions', '');\r\n          //alert(res.faultMessage || 'Failed to update review response');\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.customLayoutUtilsService.showError('❌ Error in update notes and actions', '');\r\n        //alert('Error updating review response');\r\n        console.error(err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"custom-colored-spinner\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<div class=\"permit-view-container\">\n  <!-- Permit Details Card -->\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"permit\">\n    <!-- Permit Details Header -->\n    <div class=\"permit-details-header\">\n      <div class=\"header-content w-100\">\n        <div class=\"title-wrap\">\n          <div class=\"title-line\">\n            <span class=\"permit-title\">Permit # {{\n              permit.permitNumber || \"\"\n              }}</span>\n            <span class=\"status-text status-under-review\">{{ permit.permitReviewType || \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-number-line\">\n            {{ permit.permitName || \"\" }}\n          </div>\n        </div>\n        <div class=\"button-group\">\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center\" (click)=\"goBack()\">\n            <i class=\"fas fa-arrow-left me-2\"></i>\n            Back\n          </button>\n        </div>\n      </div>\n    </div>\n    <!-- Card Header with Tabs -->\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\">\n      <!-- Tabs -->\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap\">\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'details' }\"\n            (click)=\"showTab('details', $event)\">\n            Permit Details\n          </a>\n        </li>\n        <li class=\"nav-item\" *ngIf=\"isInternalReviewEnabled()\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'internal' }\"\n            (click)=\"showTab('internal', $event)\">\n            Internal Reviews\n          </a>\n        </li>\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'external' }\"\n            (click)=\"showTab('external', $event)\">\n            External Reviews\n          </a>\n        </li>\n      </ul>\n\n      <!-- Right side buttons -->\n      <div class=\"d-flex align-items-center gap-2\" style=\"margin-right: 16px;\">\n        <!-- Edit icon - only show when permit details tab is active -->\n        <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"editPermit()\" *ngIf=\"selectedTab === 'details'\" title=\"Edit Permit\">\n          <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\n        </button>\n      </div>\n\n      <!-- Action Buttons -->\n      <div class=\"button-group\" *ngIf=\"selectedTab === 'internal' && isInternalReviewEnabled()\">\n        <button type=\"button\" class=\"btn btn-link p-0 me-3\" (click)=\"downloadInternalReviewsPdf()\" [disabled]=\"isLoading || auditEntries.length === 0\" title=\"Download Internal Reviews PDF\">\n          <i class=\"fas fa-download download-pdf-icon\" style=\"color:#3699ff\"></i>\n        </button>\n        <button class=\"btn btn-success btn-sm\" (click)=\"addPopUp()\" [disabled]=\"isLoading\">\n          <i class=\"bi bi-plus-lg\"></i>Add Review\n        </button>\n      </div>\n       <div class=\"button-group\" *ngIf=\"selectedTab === 'external'\">\n         <button type=\"button\" class=\"btn btn-secondary btn-sm me-3\" (click)=\"toggleAllSubmittals()\" [disabled]=\"isLoading || externalSubmittals.length === 0\" [title]=\"areAllSubmittalsExpanded() ? 'Collapse all submittals' : 'Expand all submittals'\">\n           <i class=\"fas\" [ngClass]=\"areAllSubmittalsExpanded() ? 'fa-compress-arrows-alt' : 'fa-expand-arrows-alt'\"></i>\n           <span class=\"ms-1\">{{ areAllSubmittalsExpanded() ? 'Collapse All' : 'Expand All' }}</span>\n         </button>\n         <button type=\"button\" class=\"btn btn-primary btn-sm me-3\" (click)=\"syncPermits(true)\" [disabled]=\"isLoading\">\n           <i class=\"fas fa-sync-alt\"></i> Sync\n         </button>\n         <button type=\"button\" class=\"btn btn-secondary btn-sm\" \n                 (click)=\"goToPortal()\" \n                 [disabled]=\"!permit?.permitEntityID\"\n                 [title]=\"permit?.permitEntityID ? 'Open Portal' : 'Portal not available - Permit Entity ID required'\">\n           <i class=\"fas fa-external-link-alt\"></i> Portal\n         </button>\n       </div>\n    </div>\n\n\n    <!-- Card Body with Tab Content -->\n    <div class=\"card-body\">\n      <!-- Permit Details Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'details' && permit\">\n      <div class=\" permit-details-content\">\n        <div class=\"permit-details-grid\">\n          <div class=\"permit-detail-item\">\n            <label>Project Name</label>\n            <span class=\"permit-value\">{{ permit.projectName || \"\" }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Permit Type</label>\n            <span class=\"permit-value\">{{ permit.permitType || \"\" }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Primary Contact</label>\n            <span class=\"permit-value\">{{\n              permit.primaryContact || \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Permit Status</label>\n            <span class=\"status-text\" [ngClass]=\"getStatusClass(permit.permitStatus)\">{{ permit.permitStatus || \"\"\n            }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Location</label>\n            <span class=\"permit-value\">{{ permit.location || \"\" }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Category</label>\n            <span class=\"permit-value\">{{\n              permit.permitCategory || \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Issue Date</label>\n            <span class=\"permit-value\">{{\n              permit.permitIssueDate\n              ? (permit.permitIssueDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n            <span class=\"text-gray-500 fs-7 p-0\"> Applied on {{\n              permit.permitAppliedDate\n              ? (permit.permitAppliedDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Expiration Date</label>\n            <span class=\"permit-value\">{{\n              permit.permitExpirationDate\n              ? (permit.permitExpirationDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Final Date</label>\n            <span class=\"permit-value\">{{\n              permit.permitFinalDate \n              ? (permit.permitFinalDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>Complete Date</label>\n            <span class=\"permit-value\">{{\n              permit.permitCompleteDate\n              ? (permit.permitCompleteDate | date : \"MM/dd/yyyy\")\n              : \"\"\n              }}</span>\n          </div>\n          <div class=\"permit-detail-item\">\n            <label>internal Review Status</label>\n            <span class=\"status-text\" [ngClass]=\"getStatusClass(permit.internalReviewStatus)\">{{ permit.internalReviewStatus || \"\"\n            }}</span>\n          </div>\n        </div>\n      </div>\n        \n        <!-- Notes & Actions fields (inline with other details) -->\n         <div class=\"permit-details-card\"> \n          <div class=\"permit-details-header\">\n            <h4>Notes / Action</h4>\n            <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"onEdit(notesActionsTemplate)\" title=\"Edit Notes/Actions\">\n              <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\n            </button>\n          </div>\n        <div class=\"permit-details-content\">\n          <div class=\"notes-actions-container\">\n            <div class=\"permit-detail-item-full\">\n              <label>Attention Reason</label>\n            <span class=\"permit-value\">{{ permit.attentionReason || \"\" }}</span>\n          </div>\n            <div class=\"permit-detail-item-full\">\n              <label>Internal Notes</label>\n            <span class=\"permit-value\">{{ permit.internalNotes || \"\" }}</span>\n          </div>\n            <div class=\"permit-detail-item-full\">\n            <label>Action Taken</label>\n            <span class=\"permit-value\">{{\n              permit.actionTaken || \"\"\n              }}</span>\n          </div>\n        </div>\n      </div>\n      </div>\n      </ng-container>\n\n      <!-- Internal Reviews Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'internal' && permit && isInternalReviewEnabled()\">\n        <!-- Empty State for Internal Reviews -->\n        <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"auditEntries.length === 0\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-clipboard-list fa-3x mb-3\"></i>\n            <p>No internal reviews found for this permit.</p>\n          </div>\n        </div>\n\n        <!-- Internal Reviews Table -->\n        <div class=\"table-responsive\" *ngIf=\"auditEntries.length > 0\">\n          <table class=\"table table-hover\">\n            <tbody>\n              <tr *ngFor=\"let audit of auditEntries; let i = index\" [class.table-active]=\"selectedAuditIndex === i\"\n                (click)=\"selectAudit(i)\" class=\"audit-table-row\">\n                <td class=\"audit-title-cell px-4\">\n                  <div class=\"d-flex align-items-center\">\n                    <h6 class=\"mb-0 me-3\">{{ audit.title }}</h6>\n                    <span class=\"audit-status-badge\" \n                          [ngClass]=\"getStatusClass(audit.internalVerificationStatus)\"\n                          [ngStyle]=\"getStatusStyle(audit.internalVerificationStatus)\">\n                      {{ audit.internalVerificationStatus || 'Pending' }}\n                    </span>\n                  </div>\n                  <div class=\"mt-1\">\n                    <small class=\"text-muted\">{{ audit.typeCodeDrawing || '' }}</small>\n                  </div>\n                </td>\n                <td class=\"audit-dates-cell px-3\">\n                  <div class=\"d-flex gap-4\">\n                    <div class=\"date-item\">\n                      <small class=\"text-muted d-block\">Reviewed</small>\n                      <span class=\"fw-medium\">{{ audit.reviewedDate ? (audit.reviewedDate | date : \"MM/dd/yyyy\") : '' }}</span>\n                    </div>\n                    <div class=\"date-item\">\n                      <small class=\"text-muted d-block\">Completed</small>\n                      <span class=\"fw-medium\">{{ audit.completedDate ? (audit.completedDate | date : \"MM/dd/yyyy\") : '' }}</span>\n                    </div>\n                    <div class=\"date-item\">\n                      <small class=\"text-muted d-block\">Reviewer</small>\n                      <span class=\"fw-medium\">{{ audit.internalReviewer || '' }}</span>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"audit-actions-cell px-4\">\n                  <i class=\"fas fa-edit action-icon edit-icon\" (click)=\"editInternalReview(i); $event.stopPropagation()\"\n                    title=\"Edit Review\"></i>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </ng-container>\n\n      <!-- External Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'external' && permit\">\n         <div class=\"external-reviews-card\">\n           <div class=\"card shadow-sm rounded-3\">\n        <!-- Error State for External Reviews -->\n           <div class=\"card-body\" *ngIf=\"reviewsError\">\n             <div class=\"d-flex justify-content-center align-items-center py-5 text-danger\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-exclamation-triangle fa-3x mb-3\"></i>\n            <p>{{ reviewsError }}</p>\n            <button class=\"btn btn-outline-primary btn-sm\" (click)=\"fetchExternalReviews()\" [disabled]=\"isLoading\">\n              <i class=\"fas fa-redo\"></i> Retry\n            </button>\n               </div>\n          </div>\n        </div>\n\n        <!-- Empty State for External Reviews -->\n           <div class=\"card-body\" *ngIf=\"!reviewsError && externalSubmittals.length === 0\">\n             <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-external-link-alt fa-3x mb-3\"></i>\n            <p>No external reviews found for this permit.</p>\n               </div>\n          </div>\n        </div>\n\n        <!-- External Reviews Table with Accordion -->\n           <div class=\"card-body p-0\" *ngIf=\"!reviewsError && externalSubmittals.length > 0\">\n             <div class=\"table-responsive external-reviews-table\">\n               <table class=\"table table-hover mb-0\">\n            <tbody>\n              <ng-container *ngFor=\"let sub of externalSubmittals; let i = index\">\n                <!-- Submittal Row -->\n                     <tr class=\"external-submittal-row\" \n                       (click)=\"toggleSubmittalAccordion(i)\"\n                       [class.expanded]=\"isSubmittalExpanded(i)\"\n                       style=\"cursor: pointer;\">\n                  <td class=\"submittal-title-cell px-3\">\n                    <div class=\"d-flex align-items-center\">\n                           <div class=\"accordion-toggle me-2\">\n                        <i class=\"fas\" [ngClass]=\"isSubmittalExpanded(i) ? 'fa-chevron-down' : 'fa-chevron-right'\"></i>\n                           </div>\n                      <h6 class=\"mb-0 me-3\">{{ sub.title }}</h6>\n                      <span class=\"submittal-status-badge\" [ngClass]=\"getStatusClass(sub.submittalStatus)\">\n                        {{ sub.submittalStatus }}\n                      </span>\n                    </div>\n                  </td>\n                  <td class=\"submittal-dates-cell px-3\">\n                    <div class=\"d-flex gap-4\">\n                      <div class=\"date-item\">\n                        <small class=\"text-muted d-block\">Due</small>\n                        <span class=\"fw-medium\">{{ sub.dueDate | date : \"MM/dd/yyyy\" }}</span>\n                      </div>\n                      <div class=\"date-item\">\n                        <small class=\"text-muted d-block\">Completed</small>\n                        <span class=\"fw-medium\">{{ sub.receivedDate | date : \"MM/dd/yyyy\" }}</span>\n                      </div>\n                    </div>\n                  </td>\n                </tr>\n                <!-- Accordion Content for External Reviews - positioned directly below its submittal row -->\n                <tr class=\"accordion-content-row\" [class.d-none]=\"!isSubmittalExpanded(i)\">\n                  <td colspan=\"2\" class=\"p-0\">\n                    <div class=\"accordion-content\">\n                      <div class=\"reviews-container p-3\">\n                        <div class=\"review-item\" *ngFor=\"let review of getExternalReviewsForSubmittal(sub.id)\">\n                                <div class=\"review-single-line\" \n                                  (click)=\"toggleReviewAccordion(review.commentsId)\"\n                                  [class.expanded]=\"isReviewExpanded(review.commentsId)\"\n                                  style=\"cursor: pointer;\">\n                                  <div class=\"review-accordion-toggle me-2\">\n                                    <i class=\"fas\" [ngClass]=\"isReviewExpanded(review.commentsId) ? 'fa-chevron-down' : 'fa-chevron-right'\"></i>\n                                  </div>\n                                  <h6 class=\"review-title\" [style.color]=\"review.FailureFlag ? 'red' : 'green'\">\n                                    {{ review.name }}\n                                  </h6>\n                                  <div class=\"review-status-container\">\n                                    <span class=\"review-status\" [ngClass]=\"getStatusClass(review.status)\">\n                                      {{ review.status || '' }}\n                                    </span>\n                                  </div>\n                                  <div class=\"reviewer-container\">\n                                    <span class=\"reviewer\">{{ review.reviewer || '' }}</span>\n                                  </div>\n                                  <div class=\"due-date-container\">\n                                    <span class=\"due-date\">\n                                      {{ review.dueDate ? ('Due: ' + (review.dueDate | date : \"MM/dd/yyyy\")) : '' }}\n                                    </span>\n                                  </div>\n                                  <div class=\"completed-date-container\">\n                                    <span class=\"completed-date\">\n                                      {{ review.completedDate ? ('Completed: ' + (review.completedDate | date : \"MM/dd/yyyy\")) : '' }}\n                                    </span>\n                                  </div>\n                                  <div class=\"review-actions-container\">\n                                    <i class=\"fas fa-download download-pdf-icon\" \n                                      (click)=\"downloadReviewPDF(review); $event.stopPropagation()\"\n                                      title=\"Download Review PDF\"></i>\n                                  </div>\n                                </div>\n                                \n                                <!-- Review Details Accordion Content -->\n                                <div class=\"review-details-accordion\" [class.d-none]=\"!isReviewExpanded(review.commentsId)\">\n                                  <div class=\"review-details-content\">\n                                    <!-- Corrections Section -->\n                                    <div class=\"corrections-section p-3\" *ngIf=\"review?.corrections && review.corrections.length > 0\" style=\"padding-bottom: 0px !important;\">\n                                      <h6 class=\"section-title\">Corrections ({{ review.corrections.length }})</h6>\n                                      <div class=\"correction-item\" *ngFor=\"let correction of review.corrections; let i = index\">\n                                        <div class=\"correction-header d-flex align-items-center\">\n                                          <div class=\"correction-number\">{{ i + 1 }}</div>\n                                          <div class=\"correction-meta flex-grow-1 ms-3 d-flex align-items-center justify-content-between\">\n                                            <div class=\"meta-fields d-flex align-items-center w-100\">\n                                              <div class=\"meta-field flex-fill\">\n                                                <span class=\"meta-label fw-bold\">Correction Type: </span>\n                                                <span class=\"meta-value\">{{ correction.CorrectionTypeName || '' }}</span>\n                                              </div>\n                                              <div class=\"meta-field flex-fill\">\n                                                <span class=\"meta-label fw-bold\">Category: </span>\n                                                <span class=\"meta-value\">{{ correction.CorrectionCategoryName || '' }}</span>\n                                              </div>\n                                              <div class=\"meta-field flex-fill\">\n                                                <span class=\"meta-label fw-bold\">Resolved: </span>\n                                                <span class=\"meta-value resolved-date\">{{ correction.ResolvedDate ? (correction.ResolvedDate | date:'MM/dd/yyyy') : '' }}</span>\n                                              </div>\n                                            </div>\n                                          </div>\n                                          <div class=\"respond-buttons\">\n                                            <button class=\"btn btn-primary btn-sm me-3\" \n                                              *ngIf=\"!correction.EORAOROwner_Response && !correction.commentResponsedBy && shouldShowEditResponseButton(correction)\"\n                                              (click)=\"openResponseModal(correction, review)\"\n                                              [disabled]=\"isLoading\"\n                                              title=\"Respond to this correction\">\n                                              Respond\n                                            </button>\n                                            <button class=\"btn btn-primary btn-sm me-3\" \n                                              *ngIf=\"(correction.EORAOROwner_Response || correction.commentResponsedBy) && shouldShowEditResponseButton(correction)\"\n                                              (click)=\"openResponseModal(correction, review)\"\n                                              [disabled]=\"isLoading\"\n                                              title=\"Edit response to this correction\">\n                                              Edit Response\n                                            </button>\n                                          </div>\n                                        </div>\n                                        \n                                        <div class=\"correction-content\">\n                                          <div class=\"correction-field\">\n                                            <label class=\"field-label\">\n                                              Corrective Action\n                                            </label>\n                                            <div class=\"field-content corrective-action\">\n                                              {{ correction.CorrectiveAction || '' }}\n                                            </div>\n                                          </div>\n                                          \n                                          <div class=\"correction-field\">\n                                            <label class=\"field-label\">\n                                              Comment\n                                            </label>\n                                            <div class=\"field-content comment\">\n                                              {{ correction.Comments || 'No comment provided' }}\n                                            </div>\n                                          </div>\n                                          \n                                          <div class=\"correction-field\" *ngIf=\"correction.Response\">\n                                            <label class=\"field-label\">\n                                              Response\n                                            </label>\n                                            <div class=\"field-content response\">\n                                              {{ correction.Response }}\n                                            </div>\n                                          </div>\n                                          \n                                          <div class=\"correction-field\" *ngIf=\"correction.EORAOROwner_Response\">\n                                            <label class=\"field-label\">\n                                              EOR / AOR / Owner Response\n                                            </label>\n                                            <div class=\"field-content eor-response\">\n                                              {{ correction.EORAOROwner_Response }}\n                                            </div>\n                                          </div>\n                                          \n                                          <div class=\"correction-field\" *ngIf=\"correction.commentResponsedBy\">\n                                            <label class=\"field-label\">\n                                              Comment Responded By\n                                            </label>\n                                            <div class=\"field-content responded-by\">\n                                              {{ correction.commentResponsedBy }}\n                                            </div>\n                                          </div>\n                                        </div>\n                                        \n                                        <div class=\"correction-separator\" *ngIf=\"i < review.corrections.length - 1\"></div>\n                                      </div>\n                                    </div>\n\n                                    <!-- Comments Section (fallback when no corrections) -->\n                                    <div class=\"comments-section p-3\"\n                                      *ngIf=\"(!review?.corrections || review.corrections.length === 0) && review?.comments\">\n                                      <h6 class=\"section-title\">Comments</h6>\n                                      <div class=\"comment-content\">\n                                        <div class=\"comment-text\">{{ review.comments }}</div>\n                                      </div>\n                                    </div>\n\n                                    <!-- No Data Message -->\n                                    <div class=\"no-data-section p-3\" \n                                      *ngIf=\"(!review?.corrections || review.corrections.length === 0) && !review?.comments\">\n                                      <div class=\"no-data-message text-center text-muted\">\n                                        <i class=\"fas fa-info-circle\"></i>\n                                        <span> No corrections or comments available for this review.</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                </tr>\n              </ng-container>\n            </tbody>\n          </table>\n             </div>\n           </div>\n         </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n\n</div>\n\n\n<ng-template #notesActionsTemplate let-permit=\"permit\">\n <div class=\"modal-content h-auto\">\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container>\n       \n        <div>Edit Notes/Actions</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i\n        class=\"fa-solid fs-2 fa-xmark text-white\"\n        (click)=\"closModal()\"\n      ></i>\n    </div>\n  </div>\n\n  <div\n    class=\"modal-body medium-modal-body\"\n    \n  >\n\n  <form class=\"form form-label-right\" [formGroup]=\"notesForm\">\n  <!-- Project Name -->\n  <div class=\"row mt-4\">\n    <div class=\"col-xl-12\">\n      <div class=\"form-group\">\n        <label for=\"projectName\" class=\"fw-bold form-label mb-2\">\n          Attention Reason\n        </label>\n        <textarea\n          id=\"attentionReason\"\n          class=\"form-control form-control-sm\"\n          rows=\"2\"\n          formControlName=\"attentionReason\"\n          placeholder=\"Type here...\"\n        ></textarea>\n      </div>\n    </div>\n  </div>\n\n  <!-- Description -->\n  <div class=\"row mt-4\">\n    <div class=\"col-xl-12\">\n      <div class=\"form-group\">\n        <label for=\"projectDescription\" class=\"fw-bold form-label mb-2\">\n          Internal Notes\n        </label>\n        <textarea\n          id=\"internalNotes\"\n          class=\"form-control form-control-sm\"\n          rows=\"3\"\n          formControlName=\"internalNotes\"\n          placeholder=\"Type here\"\n        ></textarea>\n      </div>\n    </div>\n  </div>\n\n  <!-- Internal Project Number & Start Date -->\n  <div class=\"row mt-4\">\n    <div class=\"col-xl-12\">\n      <div class=\"form-group\">\n        <label for=\"internalProjectNo\" class=\"fw-bold form-label mb-2\">\n          Action Taken \n        </label>\n          <textarea\n          id=\"internalNotes\"\n          class=\"form-control form-control-sm\"\n          rows=\"3\"\n          formControlName=\"actionTaken\"\n          placeholder=\"Type here\"\n        ></textarea>\n      </div>\n    </div>\n\n    \n  <!-- </div>\n  <div class=\"row mt-4\">\n    <div class=\"col-xl-12\">\n      <div class=\"form-group\">\n        <label for=\"internalProjectNo\" class=\"fw-bold form-label mb-2\">\nStatus        </label>\n        \n <ng-select></ng-select>\n\n      </div>\n    </div> -->\n\n    \n  </div>\n\n\n</form>\n\n  </div>\n\n  <div class=\"modal-footer justify-content-end\">\n    <div>\n      <button\n        type=\"button\"\n        class=\"btn btn-danger btn-sm btn-elevate mr-2\"\n        (click)=\"closModal()\"\n      >\n        Cancel</button\n      >&nbsp;\n      <button\n        \n        type=\"button\"\n        class=\"btn btn-primary btn-sm\"\n        \n       (click)=\"editNotesandactions()\"\n      >Update\n       <!-- (click)=\"save()\" -->\n        <!-- [disabled]=\"projectForm?.invalid\" -->\n        <!-- {{ id ? \"Update\" : \"Save\" }} -->\n      </button>\n     \n    </div>\n  </div>\n</div>\n\n</ng-template>\n\n\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,MAAM;AAEnC,SAASC,2BAA2B,QAAQ,wDAAwD;AACpG,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,8BAA8B,QAAQ,gEAAgE;AAC/G,SAASC,oBAAoB,QAAQ,wCAAwC;AAE7E,SAASC,2BAA2B,QAAQ,wDAAwD;AACpG,SAASC,UAAU,QAAQ,4BAA4B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AACxB,SAASC,SAAS,QAAQ,iBAAiB;;;;;;;;;;;;;;;ICTrCC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IAuCIH,EADF,CAAAC,cAAA,aAAuD,YAEb;IAAtCD,EAAA,CAAAI,UAAA,mBAAAC,4DAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,UAAU,EAAAN,MAAA,CAAS;IAAA,EAAC;IACrCN,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;;;;IAJyDH,EAAA,CAAAa,SAAA,EAAkD;IAAlDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,iBAAkD;;;;;;IAgBhHjB,EAAA,CAAAC,cAAA,iBAA4H;IAA7ED,EAAA,CAAAI,UAAA,mBAAAc,qEAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAW,UAAA,EAAY;IAAA,EAAC;IACnEpB,EAAA,CAAAqB,SAAA,YAAmE;IACrErB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAKTH,EADF,CAAAC,cAAA,cAA0F,iBAC6F;IAAjID,EAAA,CAAAI,UAAA,mBAAAkB,kEAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,0BAAA,EAA4B;IAAA,EAAC;IACxFxB,EAAA,CAAAqB,SAAA,YAAuE;IACzErB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAmF;IAA5CD,EAAA,CAAAI,UAAA,mBAAAqB,kEAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiB,QAAA,EAAU;IAAA,EAAC;IACzD1B,EAAA,CAAAqB,SAAA,YAA6B;IAAArB,EAAA,CAAAE,MAAA,kBAC/B;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IANuFH,EAAA,CAAAa,SAAA,EAAmD;IAAnDb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,IAAAlB,MAAA,CAAAmB,YAAA,CAAAC,MAAA,OAAmD;IAGlF7B,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;;;;;;IAKjF3B,EADF,CAAAC,cAAA,cAA6D,iBACsL;IAArLD,EAAA,CAAAI,UAAA,mBAAA0B,kEAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,mBAAA,EAAqB;IAAA,EAAC;IACzFhC,EAAA,CAAAqB,SAAA,YAA8G;IAC9GrB,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAgE;IACrFF,EADqF,CAAAG,YAAA,EAAO,EACnF;IACTH,EAAA,CAAAC,cAAA,iBAA6G;IAAnDD,EAAA,CAAAI,UAAA,mBAAA6B,kEAAA;MAAAjC,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyB,WAAA,CAAY,IAAI,CAAC;IAAA,EAAC;IACnFlC,EAAA,CAAAqB,SAAA,YAA+B;IAACrB,EAAA,CAAAE,MAAA,aAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAG8G;IAFtGD,EAAA,CAAAI,UAAA,mBAAA+B,kEAAA;MAAAnC,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC;IAG5BpC,EAAA,CAAAqB,SAAA,YAAwC;IAACrB,EAAA,CAAAE,MAAA,gBAC3C;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAbwFH,EAAA,CAAAa,SAAA,EAAyD;IAACb,EAA1D,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,IAAAlB,MAAA,CAAA4B,kBAAA,CAAAR,MAAA,OAAyD,UAAApB,MAAA,CAAA6B,wBAAA,yDAA2F;IAC/NtC,EAAA,CAAAa,SAAA,EAA0F;IAA1Fb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA6B,wBAAA,uDAA0F;IACtFtC,EAAA,CAAAa,SAAA,GAAgE;IAAhEb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA6B,wBAAA,mCAAgE;IAECtC,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;IAKpG3B,EAAA,CAAAa,SAAA,GAAoC;IACpCb,EADA,CAAAc,UAAA,eAAAL,MAAA,CAAA+B,MAAA,kBAAA/B,MAAA,CAAA+B,MAAA,CAAAC,cAAA,EAAoC,WAAAhC,MAAA,CAAA+B,MAAA,kBAAA/B,MAAA,CAAA+B,MAAA,CAAAC,cAAA,uEACiE;;;;;;IAUhHzC,EAAA,CAAA0C,uBAAA,GAAyD;IAInD1C,EAHN,CAAAC,cAAA,cAAqC,cACF,cACC,YACvB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3BH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;IAEJH,EADF,CAAAC,cAAA,cAAgC,YACvB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,gBAA0E;IAAAD,EAAA,CAAAE,MAAA,IACxE;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAC,cAAA,gBAAqC;IAACD,EAAA,CAAAE,MAAA,IAIlC;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrCH,EAAA,CAAAC,cAAA,gBAAkF;IAAAD,EAAA,CAAAE,MAAA,IAChF;IAGRF,EAHQ,CAAAG,YAAA,EAAO,EACL,EACF,EACF;IAKAH,EAFH,CAAAC,cAAA,eAAiC,eACG,UAC7B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,kBAAiH;IAAlED,EAAA,CAAAI,UAAA,mBAAAuC,4EAAA;MAAA3C,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,MAAAmC,uBAAA,GAAA7C,EAAA,CAAA8C,WAAA;MAAA,OAAA9C,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsC,MAAA,CAAAF,uBAAA,CAA4B;IAAA,EAAC;IACnF7C,EAAA,CAAAqB,SAAA,aAAmE;IAEvErB,EADE,CAAAG,YAAA,EAAS,EACL;IAIFH,EAHN,CAAAC,cAAA,eAAoC,eACG,eACE,aAC5B;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC/DF,EAD+D,CAAAG,YAAA,EAAO,EAChE;IAEFH,EADF,CAAAC,cAAA,eAAqC,aAC5B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IAEJH,EADA,CAAAC,cAAA,eAAqC,aAC9B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IAIVF,EAJU,CAAAG,YAAA,EAAO,EACP,EACF,EACF,EACA;;;;;IAlG2BH,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAQ,WAAA,OAA8B;IAI9BhD,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAS,UAAA,OAA6B;IAI7BjD,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAU,cAAA,OAEvB;IAIsBlD,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAA1C,MAAA,CAAA+B,MAAA,CAAAY,YAAA,EAA+C;IAACpD,EAAA,CAAAa,SAAA,EACxE;IADwEb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAY,YAAA,OACxE;IAIyBpD,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAa,QAAA,OAA2B;IAI3BrD,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAc,cAAA,OAEvB;IAIuBtD,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAe,eAAA,GAAAvD,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAe,eAAA,qBAIvB;IACkCvD,EAAA,CAAAa,SAAA,GAIlC;IAJkCb,EAAA,CAAAyD,kBAAA,iBAAAhD,MAAA,CAAA+B,MAAA,CAAAkB,iBAAA,GAAA1D,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAkB,iBAAA,yBAIlC;IAIuB1D,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAmB,oBAAA,GAAA3D,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAmB,oBAAA,qBAIvB;IAIuB3D,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAoB,eAAA,GAAA5D,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAoB,eAAA,qBAIvB;IAIuB5D,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAqB,kBAAA,GAAA7D,EAAA,CAAAwD,WAAA,SAAA/C,MAAA,CAAA+B,MAAA,CAAAqB,kBAAA,qBAIvB;IAIsB7D,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAA1C,MAAA,CAAA+B,MAAA,CAAAsB,oBAAA,EAAuD;IAAC9D,EAAA,CAAAa,SAAA,EAChF;IADgFb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAsB,oBAAA,OAChF;IAiByB9D,EAAA,CAAAa,SAAA,IAAkC;IAAlCb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAuB,eAAA,OAAkC;IAIlC/D,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAwB,aAAA,OAAgC;IAIhChE,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAAyB,WAAA,OAEvB;;;;;IAWNjE,EADF,CAAAC,cAAA,cAAgH,cACrF;IACvBD,EAAA,CAAAqB,SAAA,YAAgD;IAChDrB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAEjDF,EAFiD,CAAAG,YAAA,EAAI,EAC7C,EACF;;;;;;IAMAH,EAAA,CAAAC,cAAA,aACmD;IAAjDD,EAAA,CAAAI,UAAA,mBAAA8D,kFAAA;MAAA,MAAAC,KAAA,GAAAnE,EAAA,CAAAO,aAAA,CAAA6D,GAAA,EAAAC,KAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6D,WAAA,CAAAH,KAAA,CAAc;IAAA,EAAC;IAGpBnE,EAFJ,CAAAC,cAAA,aAAkC,cACO,aACf;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,eAEmE;IACjED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,cAAkB,gBACU;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAE/DF,EAF+D,CAAAG,YAAA,EAAQ,EAC/D,EACH;IAICH,EAHN,CAAAC,cAAA,cAAkC,eACN,eACD,iBACa;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA0E;;IACpGF,EADoG,CAAAG,YAAA,EAAO,EACrG;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4E;;IACtGF,EADsG,CAAAG,YAAA,EAAO,EACvG;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAGhEF,EAHgE,CAAAG,YAAA,EAAO,EAC7D,EACF,EACH;IAEHH,EADF,CAAAC,cAAA,cAAoC,aAEZ;IADuBD,EAAA,CAAAI,UAAA,mBAAAmE,kFAAAjE,MAAA;MAAA,MAAA6D,KAAA,GAAAnE,EAAA,CAAAO,aAAA,CAAA6D,GAAA,EAAAC,KAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAA+D,kBAAA,CAAAL,KAAA,CAAqB;MAAA,OAAAnE,EAAA,CAAAW,WAAA,CAAEL,MAAA,CAAAmE,eAAA,EAAwB;IAAA,EAAC;IAG1GzE,EAF0B,CAAAG,YAAA,EAAI,EACvB,EACF;;;;;;IAnCiDH,EAAA,CAAA0E,WAAA,iBAAAjE,MAAA,CAAAkE,kBAAA,KAAAR,KAAA,CAA+C;IAIzEnE,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAC,KAAA,CAAiB;IAEjC7E,EAAA,CAAAa,SAAA,EAA4D;IAC5Db,EADA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAAyB,SAAA,CAAAE,0BAAA,EAA4D,YAAArE,MAAA,CAAAsE,cAAA,CAAAH,SAAA,CAAAE,0BAAA,EACA;IAChE9E,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAmB,SAAA,CAAAE,0BAAA,mBACF;IAG0B9E,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAI,eAAA,OAAiC;IAOjChF,EAAA,CAAAa,SAAA,GAA0E;IAA1Eb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAK,YAAA,GAAAjF,EAAA,CAAAwD,WAAA,SAAAoB,SAAA,CAAAK,YAAA,qBAA0E;IAI1EjF,EAAA,CAAAa,SAAA,GAA4E;IAA5Eb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAM,aAAA,GAAAlF,EAAA,CAAAwD,WAAA,SAAAoB,SAAA,CAAAM,aAAA,qBAA4E;IAI5ElF,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAuC,iBAAA,CAAAqC,SAAA,CAAAO,gBAAA,OAAkC;;;;;IA5BpEnF,EAFJ,CAAAC,cAAA,cAA8D,gBAC3B,YACxB;IACLD,EAAA,CAAAoF,UAAA,IAAAC,6DAAA,mBACmD;IAqCzDrF,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAtCsBH,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAmB,YAAA,CAAiB;;;;;IAb/C5B,EAAA,CAAA0C,uBAAA,GAAuF;IAUrF1C,EARA,CAAAoF,UAAA,IAAAE,wDAAA,kBAAgH,IAAAC,wDAAA,kBAQlD;;;;;IARiBvF,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAmB,YAAA,CAAAC,MAAA,OAA+B;IAQ/E7B,EAAA,CAAAa,SAAA,EAA6B;IAA7Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAmB,YAAA,CAAAC,MAAA,KAA6B;;;;;;IAmD1D7B,EAFC,CAAAC,cAAA,cAA4C,cACqC,cACzD;IACvBD,EAAA,CAAAqB,SAAA,YAAsD;IACtDrB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzBH,EAAA,CAAAC,cAAA,iBAAuG;IAAxDD,EAAA,CAAAI,UAAA,mBAAAoF,iFAAA;MAAAxF,EAAA,CAAAO,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiF,oBAAA,EAAsB;IAAA,EAAC;IAC7E1F,EAAA,CAAAqB,SAAA,YAA2B;IAACrB,EAAA,CAAAE,MAAA,cAC9B;IAGJF,EAHI,CAAAG,YAAA,EAAS,EACA,EACL,EACF;;;;IANCH,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAkF,YAAA,CAAkB;IAC2D3F,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;;;;;IAUxG3B,EAFC,CAAAC,cAAA,cAAgF,cACA,cACxD;IACvBD,EAAA,CAAAqB,SAAA,YAAmD;IACnDrB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAGjDF,EAHiD,CAAAG,YAAA,EAAI,EACxC,EACL,EACF;;;;;;IAwG8BH,EAAA,CAAAC,cAAA,kBAIqC;IAFnCD,EAAA,CAAAI,UAAA,mBAAAwF,8HAAA;MAAA5F,EAAA,CAAAO,aAAA,CAAAsF,IAAA;MAAA,MAAAC,cAAA,GAAA9F,EAAA,CAAAU,aAAA,GAAAqF,SAAA;MAAA,MAAAC,UAAA,GAAAhG,EAAA,CAAAU,aAAA,IAAAqF,SAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwF,iBAAA,CAAAH,cAAA,EAAAE,UAAA,CAAqC;IAAA,EAAC;IAG/ChG,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;;;;;;IAIxB3B,EAAA,CAAAC,cAAA,kBAI2C;IAFzCD,EAAA,CAAAI,UAAA,mBAAA8F,8HAAA;MAAAlG,EAAA,CAAAO,aAAA,CAAA4F,IAAA;MAAA,MAAAL,cAAA,GAAA9F,EAAA,CAAAU,aAAA,GAAAqF,SAAA;MAAA,MAAAC,UAAA,GAAAhG,EAAA,CAAAU,aAAA,IAAAqF,SAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwF,iBAAA,CAAAH,cAAA,EAAAE,UAAA,CAAqC;IAAA,EAAC;IAG/ChG,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAkB,SAAA,CAAsB;;;;;IA2BxB3B,EADF,CAAAC,cAAA,eAA0D,iBAC7B;IACzBD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAoC;IAClCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAM,QAAA,MACF;;;;;IAIApG,EADF,CAAAC,cAAA,eAAsE,iBACzC;IACzBD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAO,oBAAA,MACF;;;;;IAIArG,EADF,CAAAC,cAAA,eAAoE,iBACvC;IACzBD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAQ,kBAAA,MACF;;;;;IAIJtG,EAAA,CAAAqB,SAAA,eAAkF;;;;;IAlFhFrB,EAFJ,CAAAC,cAAA,eAA0F,eAC/B,eACxB;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAI1CH,EAHN,CAAAC,cAAA,eAAgG,eACrC,eACrB,gBACC;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;IAEJH,EADF,CAAAC,cAAA,gBAAkC,iBACC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IACxEF,EADwE,CAAAG,YAAA,EAAO,EACzE;IAEJH,EADF,CAAAC,cAAA,gBAAkC,iBACC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAE,MAAA,IAAkF;;IAG/HF,EAH+H,CAAAG,YAAA,EAAO,EAC5H,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,gBAA6B;IAQ3BD,EAPA,CAAAoF,UAAA,KAAAmB,qGAAA,sBAIqC,KAAAC,qGAAA,sBAOM;IAI/CxG,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,gBAAgC,gBACA,kBACD;IACzBD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAA6C;IAC3CD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,gBAA8B,kBACD;IACzBD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAoBNH,EAlBA,CAAAoF,UAAA,KAAAqB,kGAAA,mBAA0D,KAAAC,kGAAA,mBASY,KAAAC,kGAAA,mBASF;IAQtE3G,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAoF,UAAA,KAAAwB,kGAAA,mBAA4E;IAC9E5G,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAnF6BH,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAuC,iBAAA,CAAAsE,KAAA,KAAW;IAKX7G,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAuC,iBAAA,CAAAuD,cAAA,CAAAgB,kBAAA,OAAyC;IAIzC9G,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAuC,iBAAA,CAAAuD,cAAA,CAAAiB,sBAAA,OAA6C;IAI/B/G,EAAA,CAAAa,SAAA,GAAkF;IAAlFb,EAAA,CAAAuC,iBAAA,CAAAuD,cAAA,CAAAkB,YAAA,GAAAhH,EAAA,CAAAwD,WAAA,SAAAsC,cAAA,CAAAkB,YAAA,qBAAkF;IAM1HhH,EAAA,CAAAa,SAAA,GAAoH;IAApHb,EAAA,CAAAc,UAAA,UAAAgF,cAAA,CAAAO,oBAAA,KAAAP,cAAA,CAAAQ,kBAAA,IAAA7F,MAAA,CAAAwG,4BAAA,CAAAnB,cAAA,EAAoH;IAOpH9F,EAAA,CAAAa,SAAA,EAAoH;IAApHb,EAAA,CAAAc,UAAA,UAAAgF,cAAA,CAAAO,oBAAA,IAAAP,cAAA,CAAAQ,kBAAA,KAAA7F,MAAA,CAAAwG,4BAAA,CAAAnB,cAAA,EAAoH;IAerH9F,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAoB,gBAAA,YACF;IAQElH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAqC,cAAA,CAAAqB,QAAA,+BACF;IAG6BnH,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAc,UAAA,SAAAgF,cAAA,CAAAM,QAAA,CAAyB;IASzBpG,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAc,UAAA,SAAAgF,cAAA,CAAAO,oBAAA,CAAqC;IASrCrG,EAAA,CAAAa,SAAA,EAAmC;IAAnCb,EAAA,CAAAc,UAAA,SAAAgF,cAAA,CAAAQ,kBAAA,CAAmC;IAUjCtG,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAAc,UAAA,SAAA+F,KAAA,GAAAb,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,KAAuC;;;;;IArF5E7B,EADF,CAAAC,cAAA,eAA0I,cAC9G;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5EH,EAAA,CAAAoF,UAAA,IAAAiC,2FAAA,qBAA0F;IAsF5FrH,EAAA,CAAAG,YAAA,EAAM;;;;IAvFsBH,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAyD,kBAAA,kBAAAuC,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,MAA6C;IACnB7B,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAkF,UAAA,CAAAoB,WAAA,CAAuB;;;;;IA2F3EpH,EAFF,CAAAC,cAAA,eACwF,cAC5D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAErCH,EADF,CAAAC,cAAA,eAA6B,eACD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAEnDF,EAFmD,CAAAG,YAAA,EAAM,EACjD,EACF;;;;IAFwBH,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAuC,iBAAA,CAAAyD,UAAA,CAAAsB,QAAA,CAAqB;;;;;IAOjDtH,EAFF,CAAAC,cAAA,eACyF,eACnC;IAClDD,EAAA,CAAAqB,SAAA,aAAkC;IAClCrB,EAAA,CAAAC,cAAA,WAAM;IAACD,EAAA,CAAAE,MAAA,6DAAqD;IAEhEF,EAFgE,CAAAG,YAAA,EAAO,EAC/D,EACF;;;;;;IAjJVH,EADR,CAAAC,cAAA,cAAuF,cAIpD;IAFzBD,EAAA,CAAAI,UAAA,mBAAAmH,oGAAA;MAAA,MAAAvB,UAAA,GAAAhG,EAAA,CAAAO,aAAA,CAAAiH,IAAA,EAAAzB,SAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgH,qBAAA,CAAAzB,UAAA,CAAA0B,UAAA,CAAwC;IAAA,EAAC;IAGlD1H,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAqB,SAAA,YAA4G;IAC9GrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAqC,gBACmC;IACpED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,eAAgC,iBACP;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;IAEJH,EADF,CAAAC,cAAA,gBAAgC,iBACP;IACrBD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,gBAAsC,iBACP;IAC3BD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,gBAAsC,cAGN;IAD5BD,EAAA,CAAAI,UAAA,mBAAAuH,mGAAArH,MAAA;MAAA,MAAA0F,UAAA,GAAAhG,EAAA,CAAAO,aAAA,CAAAiH,IAAA,EAAAzB,SAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAmH,iBAAA,CAAA5B,UAAA,CAAyB;MAAA,OAAAhG,EAAA,CAAAW,WAAA,CAAEL,MAAA,CAAAmE,eAAA,EAAwB;IAAA,EAAC;IAGnEzE,EAFkC,CAAAG,YAAA,EAAI,EAC9B,EACF;IAIJH,EADF,CAAAC,cAAA,gBAA4F,gBACtD;IAsGlCD,EApGA,CAAAoF,UAAA,KAAAyC,qFAAA,mBAA0I,KAAAC,qFAAA,mBA4FlD,KAAAC,qFAAA,mBASC;IAQrG/H,EAFU,CAAAG,YAAA,EAAM,EACR,EACF;;;;;IAlJIH,EAAA,CAAAa,SAAA,EAAsD;IAAtDb,EAAA,CAAA0E,WAAA,aAAAjE,MAAA,CAAAuH,gBAAA,CAAAhC,UAAA,CAAA0B,UAAA,EAAsD;IAGrC1H,EAAA,CAAAa,SAAA,GAAwF;IAAxFb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAuH,gBAAA,CAAAhC,UAAA,CAAA0B,UAAA,2CAAwF;IAEhF1H,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAAiI,WAAA,UAAAjC,UAAA,CAAAkC,WAAA,mBAAoD;IAC3ElI,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAuC,UAAA,CAAAmC,IAAA,MACF;IAE8BnI,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAA6C,UAAA,CAAAoC,MAAA,EAAyC;IACnEpI,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAuC,UAAA,CAAAoC,MAAA,YACF;IAGuBpI,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuC,iBAAA,CAAAyD,UAAA,CAAAqC,QAAA,OAA2B;IAIhDrI,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAuC,UAAA,CAAAsC,OAAA,aAAAtI,EAAA,CAAAwD,WAAA,SAAAwC,UAAA,CAAAsC,OAAA,0BACF;IAIEtI,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAuC,UAAA,CAAAd,aAAA,mBAAAlF,EAAA,CAAAwD,WAAA,SAAAwC,UAAA,CAAAd,aAAA,0BACF;IAUkClF,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAA0E,WAAA,YAAAjE,MAAA,CAAAuH,gBAAA,CAAAhC,UAAA,CAAA0B,UAAA,EAAqD;IAGjD1H,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAc,UAAA,UAAAkF,UAAA,kBAAAA,UAAA,CAAAoB,WAAA,KAAApB,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,KAA0D;IA4F7F7B,EAAA,CAAAa,SAAA,EAAmF;IAAnFb,EAAA,CAAAc,UAAA,YAAAkF,UAAA,kBAAAA,UAAA,CAAAoB,WAAA,KAAApB,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,YAAAmE,UAAA,kBAAAA,UAAA,CAAAsB,QAAA,EAAmF;IASnFtH,EAAA,CAAAa,SAAA,EAAoF;IAApFb,EAAA,CAAAc,UAAA,YAAAkF,UAAA,kBAAAA,UAAA,CAAAoB,WAAA,KAAApB,UAAA,CAAAoB,WAAA,CAAAvF,MAAA,aAAAmE,UAAA,kBAAAA,UAAA,CAAAsB,QAAA,EAAoF;;;;;;IAhL7GtH,EAAA,CAAA0C,uBAAA,GAAoE;IAE7D1C,EAAA,CAAAC,cAAA,aAG2B;IAFzBD,EAAA,CAAAI,UAAA,mBAAAmI,4FAAA;MAAA,MAAAC,KAAA,GAAAxI,EAAA,CAAAO,aAAA,CAAAkI,IAAA,EAAApE,KAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiI,wBAAA,CAAAF,KAAA,CAA2B;IAAA,EAAC;IAKjCxI,EAFT,CAAAC,cAAA,aAAsC,cACG,cACG;IACtCD,EAAA,CAAAqB,SAAA,YAA+F;IAC5FrB,EAAA,CAAAG,YAAA,EAAM;IACXH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,eAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACH;IAICH,EAHN,CAAAC,cAAA,cAAsC,eACV,eACD,iBACa;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7CH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4C;;IAI5EF,EAJ4E,CAAAG,YAAA,EAAO,EACvE,EACF,EACH,EACF;IAKCH,EAHN,CAAAC,cAAA,cAA2E,cAC7C,eACK,eACM;IACjCD,EAAA,CAAAoF,UAAA,KAAAuD,8EAAA,oBAAuF;IAyJ/F3I,EAHM,CAAAG,YAAA,EAAM,EACF,EACH,EACF;;;;;;;IAxLEH,EAAA,CAAAa,SAAA,EAAyC;IAAzCb,EAAA,CAAA0E,WAAA,aAAAjE,MAAA,CAAAmI,mBAAA,CAAAJ,KAAA,EAAyC;IAKzBxI,EAAA,CAAAa,SAAA,GAA2E;IAA3Eb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAmI,mBAAA,CAAAJ,KAAA,2CAA2E;IAEtExI,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAuC,iBAAA,CAAAsG,OAAA,CAAAhE,KAAA,CAAe;IACA7E,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA0C,cAAA,CAAA0F,OAAA,CAAAC,eAAA,EAA+C;IAClF9I,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAoF,OAAA,CAAAC,eAAA,MACF;IAO0B9I,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAwD,WAAA,SAAAqF,OAAA,CAAAP,OAAA,gBAAuC;IAIvCtI,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAwD,WAAA,SAAAqF,OAAA,CAAAE,YAAA,gBAA4C;IAM1C/I,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAA0E,WAAA,YAAAjE,MAAA,CAAAmI,mBAAA,CAAAJ,KAAA,EAAwC;IAItBxI,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAuI,8BAAA,CAAAH,OAAA,CAAAI,EAAA,EAAyC;;;;;IApCjGjJ,EAHD,CAAAC,cAAA,cAAkF,cAC3B,gBACb,YAClC;IACLD,EAAA,CAAAoF,UAAA,IAAA8D,uEAAA,6BAAoE;IAiMvElJ,EAHC,CAAAG,YAAA,EAAQ,EACF,EACC,EACF;;;;IAjM2BH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA4B,kBAAA,CAAuB;;;;;IA/B7DrC,EAAA,CAAA0C,uBAAA,GAA0D;IAErD1C,EADF,CAAAC,cAAA,cAAmC,aACK;IAyBtCD,EAvBA,CAAAoF,UAAA,IAAA+D,wDAAA,kBAA4C,IAAAC,wDAAA,kBAaoC,IAAAC,wDAAA,kBAUE;IAuMrFrJ,EADC,CAAAG,YAAA,EAAM,EACD;;;;;IA9NqBH,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAkF,YAAA,CAAkB;IAalB3F,EAAA,CAAAa,SAAA,EAAsD;IAAtDb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAkF,YAAA,IAAAlF,MAAA,CAAA4B,kBAAA,CAAAR,MAAA,OAAsD;IAUlD7B,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAkF,YAAA,IAAAlF,MAAA,CAAA4B,kBAAA,CAAAR,MAAA,KAAoD;;;;;;IA7Q/E7B,EANV,CAAAC,cAAA,aAAqD,cAEhB,cACC,cACR,cACE,eACK;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAC,cAAA,eAA8C;IAAAD,EAAA,CAAAE,MAAA,GAC1C;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IACNH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACgF;IAAnBD,EAAA,CAAAI,UAAA,mBAAAkJ,4DAAA;MAAAtJ,EAAA,CAAAO,aAAA,CAAAgJ,GAAA;MAAA,MAAA9I,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+I,MAAA,EAAQ;IAAA,EAAC;IACrGxJ,EAAA,CAAAqB,SAAA,aAAsC;IACtCrB,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAMAH,EAJN,CAAAC,cAAA,eAAyF,cAEgB,cAChF,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAAqJ,uDAAAnJ,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgJ,GAAA;MAAA,MAAA9I,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,SAAS,EAAAN,MAAA,CAAS;IAAA,EAAC;IACpCN,EAAA,CAAAE,MAAA,wBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IACLH,EAAA,CAAAoF,UAAA,KAAAsE,wCAAA,iBAAuD;IAOrD1J,EADF,CAAAC,cAAA,cAAqB,aAEqB;IAAtCD,EAAA,CAAAI,UAAA,mBAAAuJ,uDAAArJ,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgJ,GAAA;MAAA,MAAA9I,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,UAAU,EAAAN,MAAA,CAAS;IAAA,EAAC;IACrCN,EAAA,CAAAE,MAAA,0BACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACD,EACF;IAGLH,EAAA,CAAAC,cAAA,eAAyE;IAEvED,EAAA,CAAAoF,UAAA,KAAAwE,4CAAA,qBAA4H;IAG9H5J,EAAA,CAAAG,YAAA,EAAM;IAWLH,EARD,CAAAoF,UAAA,KAAAyE,yCAAA,kBAA0F,KAAAC,yCAAA,mBAQ5B;IAehE9J,EAAA,CAAAG,YAAA,EAAM;IAINH,EAAA,CAAAC,cAAA,eAAuB;IAoKrBD,EAlKA,CAAAoF,UAAA,KAAA2E,kDAAA,6BAAyD,KAAAC,kDAAA,2BA2G8B,KAAAC,kDAAA,2BAuD7B;IAqO9DjK,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAvd+BH,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAyD,kBAAA,cAAAhD,MAAA,CAAA+B,MAAA,CAAA0H,YAAA,WAEvB;IAC0ClK,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA+B,MAAA,CAAA2H,gBAAA,OAC1C;IAGJnK,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyD,kBAAA,MAAAhD,MAAA,CAAA+B,MAAA,CAAA4H,UAAA,YACF;IAe4DpK,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,KAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,gBAAiD;IAKzFjB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAA4J,uBAAA,GAA+B;IAOSrK,EAAA,CAAAa,SAAA,GAAkD;IAAlDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,KAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,iBAAkD;IAUzCjB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,eAA+B;IAM7EjB,EAAA,CAAAa,SAAA,EAA6D;IAA7Db,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,mBAAAR,MAAA,CAAA4J,uBAAA,GAA6D;IAQ5DrK,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,gBAAgC;IAqB7CjB,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,iBAAAR,MAAA,CAAA+B,MAAA,CAAwC;IA2GxCxC,EAAA,CAAAa,SAAA,EAAsE;IAAtEb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,kBAAAR,MAAA,CAAA+B,MAAA,IAAA/B,MAAA,CAAA4J,uBAAA,GAAsE;IAuDtErK,EAAA,CAAAa,SAAA,EAAyC;IAAzCb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,kBAAAR,MAAA,CAAA+B,MAAA,CAAyC;;;;;;IA6O1DxC,EAFH,CAAAC,cAAA,eAAkC,eACU,eACR;IAC/BD,EAAA,CAAA0C,uBAAA,GAAc;IAEZ1C,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;IAEjCH,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAItB;IADCD,EAAA,CAAAI,UAAA,mBAAAkK,8DAAA;MAAAtK,EAAA,CAAAO,aAAA,CAAAgK,IAAA;MAAA,MAAA9J,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+J,SAAA,EAAW;IAAA,EAAC;IAG3BxK,EAFK,CAAAG,YAAA,EAAI,EACD,EACF;IAYAH,EAVN,CAAAC,cAAA,eAGC,gBAE2D,gBAEtC,gBACG,gBACG,kBACmC;IACvDD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAqB,SAAA,qBAMY;IAGlBrB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAsB,gBACG,gBACG,kBAC0C;IAC9DD,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAqB,SAAA,qBAMY;IAGlBrB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAsB,gBACG,gBACG,kBACyC;IAC7DD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACNH,EAAA,CAAAqB,SAAA,qBAMU;IAuBlBrB,EAtBI,CAAAG,YAAA,EAAM,EACF,EAgBF,EAGD,EAEC;IAIFH,EAFJ,CAAAC,cAAA,gBAA8C,WACvC,mBAKF;IADCD,EAAA,CAAAI,UAAA,mBAAAqK,oEAAA;MAAAzK,EAAA,CAAAO,aAAA,CAAAgK,IAAA;MAAA,MAAA9J,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+J,SAAA,EAAW;IAAA,EAAC;IAErBxK,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EACP;IAAAH,EAAA,CAAAE,MAAA,eACD;IAAAF,EAAA,CAAAC,cAAA,mBAMC;IADAD,EAAA,CAAAI,UAAA,mBAAAsK,oEAAA;MAAA1K,EAAA,CAAAO,aAAA,CAAAgK,IAAA;MAAA,MAAA9J,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkK,mBAAA,EAAqB;IAAA,EAAC;IAC/B3K,EAAA,CAAAE,MAAA,eACA;IAOPF,EAJM,CAAAG,YAAA,EAAS,EAEL,EACF,EACF;;;;IAlGgCH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,cAAAL,MAAA,CAAAmK,SAAA,CAAuB;;;AD7e7D,OAAM,MAAOC,mBAAmB;EAqCpBC,KAAA;EACAC,MAAA;EACAC,YAAA;EACAC,GAAA;EACAC,EAAA;EAGAC,UAAA;EACIC,wBAAA;EAEJC,cAAA;EACAC,eAAA;EA/CRV,SAAS;EAEJW,QAAQ,GAAkB,IAAI;EAC9B/I,MAAM,GAAQ,IAAI;EAClBb,SAAS,GAAY,KAAK,CAAC,CAAC;EAC5BC,YAAY,GAAU,EAAE;EACxB+C,kBAAkB,GAAW,CAAC,CAAC,CAAC;EAChC6G,iBAAiB,GAAQ,EAAE;EAC3BC,mBAAmB,GAAQ,EAAE;EACpCxK,WAAW,GAAQ,SAAS;EAC5BkJ,gBAAgB,GAAO,EAAE;EAClBuB,eAAe,GAAU,EAAE;EAC3B/F,YAAY,GAAW,EAAE;EACzBtD,kBAAkB,GAA6I,EAAE;EACjKsJ,2BAA2B,GAAQ,IAAI;EACvCC,eAAe,GAAU,EAAE;EAC3BC,SAAS,GAAM,EAAE;EACjBC,OAAO,GAAY,KAAK;EAC/BC,YAAY;EACLC,kBAAkB,GAAgB,IAAIC,GAAG,EAAE;EAC3CC,eAAe,GAAgB,IAAID,GAAG,EAAE;EACxCE,kBAAkB,GAA8B,EAAE;EACjDC,iBAAiB,GAAiB,IAAI7M,YAAY,EAAE;EACpD8M,uBAAuB,GAAiB,IAAI9M,YAAY,EAAE;EAC1D+M,mBAAmB,GAAiB,IAAI/M,YAAY,EAAE;EAChEgN,UAAU,GAAG,CACX;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,EACrC;IAAED,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC7C;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAE,EACzC;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,CACtC;EACC;EACOC,YAAY,GAAW,aAAa,CAAC,CAAC;EACtCC,SAAS,GAAkB,IAAI;EAEtCC,YACU9B,KAAqB,EACrBC,MAAc,EACdC,YAAsB,EACtBC,GAAsB,EACtBC,EAAe;EAC3B;EAEYC,UAAqB,EACjBC,wBAAkD,EAEtDC,cAA8B,EAC9BC,eAAiC;IAXjC,KAAAR,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,EAAE,GAAFA,EAAE;IAGF,KAAAC,UAAU,GAAVA,UAAU;IACN,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAE5B,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;EACtB;EAEHuB,QAAQA,CAAA;IACN,IAAI,CAAChB,SAAS,GAAG,IAAI,CAACV,UAAU,CAAC2B,eAAe,EAAE;IAClD,IAAI,CAAChB,OAAO,GAAG,IAAI,CAACiB,YAAY,EAAE;IAElC;IACA,IAAI,CAACT,mBAAmB,GAAG,IAAI,CAAChB,eAAe,CAAC0B,cAAc,CAACC,SAAS,CACrEC,OAAY,IAAI;MACf,IAAI,CAACvL,SAAS,GAAGuL,OAAO,KAAK,IAAI;IACnC,CAAC,CACF;IAED;IACA,IAAI,CAACb,uBAAuB,GAAG,IAAI,CAACvB,KAAK,CAACqC,WAAW,CAACF,SAAS,CAACG,MAAM,IAAG;MACvE,IAAI,CAACV,YAAY,GAAGU,MAAM,CAAC,MAAM,CAAC,IAAI,aAAa;MACnD,IAAI,CAACT,SAAS,GAAGS,MAAM,CAAC,WAAW,CAAC,GAAGC,MAAM,CAACD,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI;MACzEE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;QAAEb,YAAY,EAAE,IAAI,CAACA,YAAY;QAAEC,SAAS,EAAE,IAAI,CAACA;MAAS,CAAE,CAAC;IAC5G,CAAC,CAAC;IAEF;IACA,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAACtB,KAAK,CAAC0C,QAAQ,CAACP,SAAS,CAACG,MAAM,IAAG;MAC9D,MAAMK,OAAO,GAAGL,MAAM,CAACM,GAAG,CAAC,IAAI,CAAC;MAChC,IAAI,CAACnC,QAAQ,GAAGkC,OAAO,GAAGJ,MAAM,CAACI,OAAO,CAAC,GAAG,IAAI;MAEhD,IAAI,IAAI,CAAClC,QAAQ,EAAE;QACjB,IAAI,CAACoC,kBAAkB,EAAE;QACzB,IAAI,CAACjI,oBAAoB,EAAE;QAC3B,IAAI,CAACkI,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,EAAE;EACjB;EACAA,QAAQA,CAAA;IACN,IAAI,CAACjD,SAAS,GAAG,IAAI,CAACM,EAAE,CAAC4C,KAAK,CAAC;MAC9B/J,eAAe,EAAC,CAAC,EAAE,CAAC;MACpBC,aAAa,EAAC,CAAC,EAAE,CAAC;MAClBC,WAAW,EAAC,CAAC,EAAE;KACf,CAAC;IAEF;IACA,IAAI,CAACgH,GAAG,CAAC8C,aAAa,EAAE;EAC1B;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC5B,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAAC6B,WAAW,EAAE;IACtC;IACA,IAAI,IAAI,CAAC5B,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAAC4B,WAAW,EAAE;IAC5C;IACA,IAAI,IAAI,CAAC3B,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAAC2B,WAAW,EAAE;IACxC;EACF;EAEON,kBAAkBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACpC,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAACD,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC7C,cAAc,CAAC8C,SAAS,CAAC;MAAE5C,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE,CAAC,CAAC0B,SAAS,CAAC;MACnEiB,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC9C,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/CZ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEa,GAAG,CAAC;QACxC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB,IAAI,CAAC7L,MAAM,GAAG4L,GAAG,CAACE,YAAY,EAAEC,IAAI,IAAIH,GAAG,CAACE,YAAY,IAAI,IAAI;UAChEhB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC/K,MAAM,CAAC;UACjD8K,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC/K,MAAM,EAAE4H,UAAU,CAAC;UAChEkD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjM,MAAM,IAAI,EAAE,CAAC,CAAC;UACjE,IAAI,CAAC2H,gBAAgB,GAAG,IAAI,CAAC3H,MAAM,EAAE2H,gBAAgB,IAAI,EAAE;UAC3D;UACA,IAAI,CAAClJ,WAAW,GAAG,SAAS;QAC9B,CAAC,MAAM;UACLqM,OAAO,CAACoB,KAAK,CAAC,qBAAqB,EAAEN,GAAG,CAACO,YAAY,CAAC;UACtD,IAAI,CAACnM,MAAM,GAAG,IAAI;QACpB;QACA,IAAI,CAACyI,GAAG,CAAC2D,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACpD,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACjD,GAAG,CAAC2D,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOlJ,oBAAoBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAC6F,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAACD,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACvI,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC0F,cAAc,CAACwD,aAAa,CAAC;MAAEtD,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE,CAAC,CAAC0B,SAAS,CAAC;MACvEiB,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC9C,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIE,GAAG,EAAEC,OAAO,EAAE;UAChB,IAAI,CAAC1I,YAAY,GAAGyI,GAAG,CAACO,YAAY,IAAI,wBAAwB;QAClE,CAAC,MAAM;UACL,MAAMG,OAAO,GAAGV,GAAG,CAACE,YAAY,EAAEQ,OAAO,IAAI,EAAE;UAC/C,IAAI,CAACpD,eAAe,GAAGoD,OAAO,CAACC,GAAG,CAAEC,CAAM,KAAM;YAC9CtH,UAAU,EAACsH,CAAC,CAACtH,UAAU;YACvBS,IAAI,EAAE6G,CAAC,CAACC,QAAQ;YAChB5G,QAAQ,EAAE2G,CAAC,CAACE,UAAU;YACtB9G,MAAM,EAAE4G,CAAC,CAACG,UAAU;YACpBjK,aAAa,EAAE8J,CAAC,CAACI,aAAa,GAAG,IAAIC,IAAI,CAACL,CAAC,CAACI,aAAa,CAAC,GAAG,IAAI;YACjE9G,OAAO,EAAE0G,CAAC,CAACM,OAAO,GAAG,IAAID,IAAI,CAACL,CAAC,CAACM,OAAO,CAAC,GAAG,IAAI;YAC/CvG,YAAY,EAAEiG,CAAC,CAACjG,YAAY,GAAG,IAAIsG,IAAI,CAACL,CAAC,CAACjG,YAAY,CAAC,GAAG,IAAI;YAC9DzB,QAAQ,EAAE0H,CAAC,CAAC7H,QAAQ;YACpBC,WAAW,EAAE4H,CAAC,CAACO,WAAW,IAAI,EAAE;YAChCC,WAAW,EAAER,CAAC,CAACS,WAAW;YAC1BvH,WAAW,EAAE8G,CAAC,CAAC9G,WAAW;YAC1BwH,cAAc,EAAEV,CAAC,CAACU,cAAc;YAChCrJ,oBAAoB,EAAE2I,CAAC,CAAC3I,oBAAoB;YAC5CC,kBAAkB,EAAE0I,CAAC,CAAC1I;WACvB,CAAC,CAAC;UAEH;UACA,MAAMqJ,WAAW,GAA6B,EAAE;UAChD,IAAI,CAACjE,eAAe,CAACkE,OAAO,CAAEC,EAAO,IAAI;YACvC,MAAMC,GAAG,GAAGC,MAAM,CAACF,EAAE,CAACL,WAAW,IAAI,SAAS,CAAC;YAC/C,IAAI,CAACG,WAAW,CAACG,GAAG,CAAC,EAAE;cAAEH,WAAW,CAACG,GAAG,CAAC,GAAG,EAAE;YAAE;YAChDH,WAAW,CAACG,GAAG,CAAC,CAACE,IAAI,CAACH,EAAE,CAAC;UAC3B,CAAC,CAAC;UAEF,IAAI,CAACxN,kBAAkB,GAAGmM,MAAM,CAACC,IAAI,CAACkB,WAAW,CAAC,CAACZ,GAAG,CAAEe,GAAG,IAAI;YAC7D,MAAMG,KAAK,GAAGN,WAAW,CAACG,GAAG,CAAC;YAC9B;YACA,MAAMI,WAAW,GAAQ;cACvB,oBAAoB,EAAE,CAAC;cACvB,cAAc,EAAE,CAAC;cACjB,wBAAwB,EAAE,CAAC;cAC3B,UAAU,EAAE;aACb;YACD,MAAMpH,eAAe,GAAGmH,KAAK,CAACE,MAAM,CAAC,CAACC,GAAW,EAAEC,EAAO,KAAI;cAC5D,MAAMC,CAAC,GAAGJ,WAAW,CAACE,GAAG,CAAC,IAAI,CAAC;cAAE,MAAMG,CAAC,GAAGL,WAAW,CAACG,EAAE,CAACjI,MAAM,CAAC,IAAI,CAAC;cAAE,OAAOmI,CAAC,GAAGD,CAAC,GAAGD,EAAE,CAACjI,MAAM,GAAGgI,GAAG;YACxG,CAAC,EAAE,EAAE,CAAC;YAEN;YACA,MAAM9H,OAAO,GAAG2H,KAAK,CAACE,MAAM,CAAC,CAACC,GAAgB,EAAEC,EAAO,KAAI;cACzD,IAAI,CAACA,EAAE,CAAC/H,OAAO,EAAE;gBAAE,OAAO8H,GAAG;cAAE;cAC/B,IAAI,CAACA,GAAG,EAAE;gBAAE,OAAOC,EAAE,CAAC/H,OAAO;cAAE;cAC/B,OAAO8H,GAAG,GAAGC,EAAE,CAAC/H,OAAO,GAAG+H,EAAE,CAAC/H,OAAO,GAAG8H,GAAG,CAAC,CAAC;YAC9C,CAAC,EAAE,IAAmB,CAAC;YAEvB,MAAMlL,aAAa,GAAG+K,KAAK,CAACE,MAAM,CAAC,CAACC,GAAgB,EAAEC,EAAO,KAAI;cAC/D,IAAI,CAACA,EAAE,CAACnL,aAAa,EAAE;gBAAE,OAAOkL,GAAG;cAAE;cACrC,IAAI,CAACA,GAAG,EAAE;gBAAE,OAAOC,EAAE,CAACnL,aAAa;cAAE;cACrC,OAAOkL,GAAG,GAAGC,EAAE,CAACnL,aAAa,GAAGmL,EAAE,CAACnL,aAAa,GAAGkL,GAAG,CAAC,CAAC;YAC1D,CAAC,EAAE,IAAmB,CAAC;YAEvB;YACA,MAAMrH,YAAY,GAAGkH,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAACtH,YAAY,CAAC,EAAEA,YAAY,IACvDkH,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAACI,WAAW,CAAC,EAAEA,WAAW,IACpD,IAAI;YAExB;YACA,MAAMf,cAAc,GAAGO,KAAK,CAAC,CAAC,CAAC,EAAEP,cAAc,IAAI,aAAaI,GAAG,EAAE;YAErE,OAAO;cACL7G,EAAE,EAAE6G,GAAG;cACPjL,KAAK,EAAE6K,cAAc;cACrB5G,eAAe,EAAEA,eAAe,IAAKmH,KAAK,CAAC,CAAC,CAAC,EAAE7H,MAAM,IAAI,EAAG;cAC5DW,YAAY,EAAEA,YAAY,GAAG,IAAIsG,IAAI,CAACtG,YAAY,CAAC,GAAG,IAAI;cAC1DT,OAAO,EAAEA,OAAO;cAChBpD,aAAa,EAAEA;aAChB;UACH,CAAC,CAAC,CAACwL,IAAI,CAAC,CAACJ,CAAC,EAAEC,CAAC,KAAI;YACf;YACA,IAAI,CAACD,CAAC,CAACvH,YAAY,IAAI,CAACwH,CAAC,CAACxH,YAAY,EAAE,OAAO,CAAC;YAChD,IAAI,CAACuH,CAAC,CAACvH,YAAY,EAAE,OAAO,CAAC;YAC7B,IAAI,CAACwH,CAAC,CAACxH,YAAY,EAAE,OAAO,CAAC,CAAC;YAC9B,OAAOwH,CAAC,CAACxH,YAAY,CAAC4H,OAAO,EAAE,GAAGL,CAAC,CAACvH,YAAY,CAAC4H,OAAO,EAAE;UAC5D,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAACtO,kBAAkB,CAACR,MAAM,GAAG,CAAC,EAAE;YACtC,IAAI,CAAC8J,2BAA2B,GAAG,IAAI,CAACtJ,kBAAkB,CAAC,CAAC,CAAC,CAAC4G,EAAE;YAChE,IAAI,CAACuC,iBAAiB,GAAG,IAAI,CAACnJ,kBAAkB,CAAC,CAAC,CAAC,CAACwC,KAAK;YACzD,IAAI,CAAC4G,mBAAmB,GAAG,IAAI,CAACpJ,kBAAkB,CAAC,CAAC,CAAC,CAACyG,eAAe;UACvE;QACF;QACA,IAAI,CAACmC,GAAG,CAAC2D,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACtF,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACvI,YAAY,GAAG,wBAAwB;QAC5C,IAAI,CAACsF,GAAG,CAAC2D,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOhB,oBAAoBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACrC,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAACD,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAAC7C,cAAc,CAACwF,kBAAkB,CAAC;MACrCtF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBuF,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE;KACP,CAAC,CAAC9D,SAAS,CAAC;MACXiB,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC9C,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIE,GAAG,EAAEC,OAAO,EAAE;UAChBf,OAAO,CAACoB,KAAK,CAAC,kCAAkC,EAAEN,GAAG,CAACO,YAAY,CAAC;QACrE,CAAC,MAAM;UACL,IAAI,CAAC/C,eAAe,GAAGwC,GAAG,CAACE,YAAY,EAAEC,IAAI,IAAIH,GAAG,CAACG,IAAI,IAAI,EAAE;UAE/D;UACA,IAAI,CAAC3M,YAAY,GAAG,IAAI,CAACgK,eAAe,CAACmD,GAAG,CAAEiC,MAAW,KAAM;YAC7DtJ,UAAU,EAAEsJ,MAAM,CAACtJ,UAAU;YAC7B7C,KAAK,EAAEmM,MAAM,CAACtB,cAAc;YAC5BA,cAAc,EAAEsB,MAAM,CAACtB,cAAc;YAAE;YACvC1K,eAAe,EAAEgM,MAAM,CAAChM,eAAe;YACvCiM,cAAc,EAAED,MAAM,CAACC,cAAc;YACrCC,kBAAkB,EAAEF,MAAM,CAACE,kBAAkB;YAC7CC,UAAU,EAAEH,MAAM,CAACG,UAAU;YAC7BhM,gBAAgB,EAAE6L,MAAM,CAAC7L,gBAAgB;YACzCL,0BAA0B,EAAEkM,MAAM,CAAClM,0BAA0B;YAC7DG,YAAY,EAAE+L,MAAM,CAAC/L,YAAY,GAAG,IAAIoK,IAAI,CAAC2B,MAAM,CAAC/L,YAAY,CAAC,GAAG,IAAI;YACxEC,aAAa,EAAE8L,MAAM,CAAC9L,aAAa,GAAG,IAAImK,IAAI,CAAC2B,MAAM,CAAC9L,aAAa,CAAC,GAAG,IAAI;YAC3E4J,OAAO,EAAE,CAAC;cACR3G,IAAI,EAAE6I,MAAM,CAACtB,cAAc;cAC3B1K,eAAe,EAAEgM,MAAM,CAAChM,eAAe;cACvCiM,cAAc,EAAED,MAAM,CAACC,cAAc;cACrCC,kBAAkB,EAAEF,MAAM,CAACE,kBAAkB;cAC7CC,UAAU,EAAEH,MAAM,CAACG,UAAU;cAC7BhM,gBAAgB,EAAE6L,MAAM,CAAC7L,gBAAgB;cACzCL,0BAA0B,EAAEkM,MAAM,CAAClM,0BAA0B;cAC7DG,YAAY,EAAE+L,MAAM,CAAC/L,YAAY,GAAG,IAAIoK,IAAI,CAAC2B,MAAM,CAAC/L,YAAY,CAAC,GAAG,IAAI;cACxEC,aAAa,EAAE8L,MAAM,CAAC9L,aAAa,GAAG,IAAImK,IAAI,CAAC2B,MAAM,CAAC9L,aAAa,CAAC,GAAG;aACxE;WACF,CAAC,CAAC;UAEH;UACA,IAAI,IAAI,CAACtD,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;YAChC,IAAI,CAAC8C,kBAAkB,GAAG,CAAC;YAC3B,IAAI,CAAC6G,iBAAiB,GAAG,IAAI,CAAC5J,YAAY,CAAC,CAAC,CAAC,CAACiD,KAAK;YACnD,IAAI,CAAC4G,mBAAmB,GAAG,IAAI,CAAC7J,YAAY,CAAC,CAAC,CAAC,CAACkD,0BAA0B,IAAI,SAAS;UACzF;QACF;QACA,IAAI,CAACmG,GAAG,CAAC2D,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACtF,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/CZ,OAAO,CAACoB,KAAK,CAAC,iCAAiC,EAAEkC,GAAG,CAAC;QACrD,IAAI,CAAC3F,GAAG,CAAC2D,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOpN,0BAA0BA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACoK,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC/J,MAAM,KAAK,CAAC,EAAE;MAAE;IAAQ;IAE1E,MAAMuP,OAAO,GAAkC,EAAE;IACjD,IAAI,CAACxF,eAAe,CAACgE,OAAO,CAAEZ,CAAM,IAAI;MACtC,MAAMc,GAAG,GAAGd,CAAC,CAACU,cAAc,IAAI,eAAe;MAC/C,IAAI,CAAC0B,OAAO,CAACtB,GAAG,CAAC,EAAE;QAAEsB,OAAO,CAACtB,GAAG,CAAC,GAAG,EAAE;MAAE;MACxCsB,OAAO,CAACtB,GAAG,CAAC,CAACE,IAAI,CAAChB,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,MAAMqC,GAAG,GAAG,IAAIvR,KAAK,CAAC;MAAEwR,WAAW,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAE,CAAC;IAC5E,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;IAClD;IACA,MAAMC,MAAM,GAAG;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAE,CAAE;IAC/C,IAAIC,CAAC,GAAGJ,MAAM,CAACG,GAAG;IAElB,MAAME,WAAW,GAAGA,CAACC,QAAgB,EAAElC,KAAY,KAAI;MACrD;MACAoB,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCf,GAAG,CAACgB,WAAW,CAAC,EAAE,CAAC;MACnBhB,GAAG,CAAC7E,IAAI,CAAC,GAAG2F,QAAQ,CAACG,WAAW,EAAE,kBAAkB,EAAEb,SAAS,GAAG,CAAC,EAAEQ,CAAC,EAAE;QACtEM,KAAK,EAAE;OACR,CAAC;MACFN,CAAC,IAAI,EAAE;MAEP;MACA,MAAMO,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIzG,GAAG,CAACgE,KAAK,CACvClB,GAAG,CAAEsB,EAAO,IAAK,CAACA,EAAE,CAAClL,gBAAgB,IAAI,EAAE,EAAEwN,QAAQ,EAAE,CAACC,IAAI,EAAE,CAAC,CAC/DC,MAAM,CAAEC,CAAS,IAAKA,CAAC,CAAC,CAAC,CAAC;MAC7BzB,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCf,GAAG,CAACgB,WAAW,CAAC,EAAE,CAAC;MACnBhB,GAAG,CAAC7E,IAAI,CAAC,aAAagG,SAAS,CAACO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAEtB,SAAS,GAAG,CAAC,EAAEQ,CAAC,EAAE;QAAEM,KAAK,EAAE;MAAQ,CAAE,CAAC;MAC1FN,CAAC,IAAI,EAAE;MAEP;MACA,MAAMhN,YAAY,GAAGgL,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAACpL,YAAY,CAAC,EAAEA,YAAY;MAC3E,MAAM+N,aAAa,GAAG/C,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAACnL,aAAa,CAAC,EAAEA,aAAa;MAC9EmM,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCf,GAAG,CAACgB,WAAW,CAAC,CAAC,CAAC;MAClBhB,GAAG,CAAC7E,IAAI,CACN,kBAAkBvH,YAAY,GAAGpF,UAAU,CAACoT,UAAU,CAAChO,YAAY,CAAC,GAAG,EAAE,EAAE,EAC3E4M,MAAM,CAACC,IAAI,EACXG,CAAC,CACF;MACDZ,GAAG,CAAC7E,IAAI,CACN,kBAAkBwG,aAAa,GAAGnT,UAAU,CAACoT,UAAU,CAACD,aAAa,CAAC,GAAG,EAAE,EAAE,EAC7EvB,SAAS,GAAGI,MAAM,CAACE,KAAK,EACxBE,CAAC,EACD;QAAEM,KAAK,EAAE;MAAO,CAAE,CACnB;MACDN,CAAC,IAAI,CAAC;MAEN,MAAMiB,IAAI,GAAGjD,KAAK,CAAClB,GAAG,CAAC,CAACsB,EAAE,EAAE8C,GAAG,KAAK,CAClC,CAACA,GAAG,GAAG,CAAC,EAAER,QAAQ,EAAE,EACpBtC,EAAE,CAACrL,eAAe,IAAI,EAAE,EACxB,CAACqL,EAAE,CAACY,cAAc,IAAI,EAAE,EAAE0B,QAAQ,EAAE,EACpC,CAACtC,EAAE,CAACc,UAAU,IAAI,EAAE,EAAEwB,QAAQ,EAAE,EAChCtC,EAAE,CAACvL,0BAA0B,IAAI,EAAE,CACpC,CAAC;MAEF/E,SAAS,CAACsR,GAAG,EAAE;QACb+B,MAAM,EAAEnB,CAAC,GAAG,CAAC;QACboB,IAAI,EAAE,CAAC,CACL,GAAG,EACH,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,QAAQ,CACT,CAAC;QACFC,IAAI,EAAEJ,IAAI;QACVrB,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDwB,MAAM,EAAE;UAAEC,IAAI,EAAE,WAAW;UAAEC,QAAQ,EAAE,CAAC;UAAEC,WAAW,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAE;QACzEC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE,GAAG;UAAEC,MAAM,EAAE,QAAQ;UAAEN,QAAQ,EAAE;QAAC,CAAE;QACxF;QACA;QACAO,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEF,MAAM,EAAE;UAAQ,CAAE;UAAI;UAC1C,CAAC,EAAE;YAAEE,SAAS,EAAE;UAAE,CAAE;UAAsB;UAC1C,CAAC,EAAE;YAAEA,SAAS,EAAE;UAAG,CAAE;UAAqB;UAC1C,CAAC,EAAE;YAAEA,SAAS,EAAE;UAAG,CAAE;UAAqB;UAC1C,CAAC,EAAE;YAAEA,SAAS,EAAE;UAAE,CAAE,CAAsB;SAC3C;QACDC,KAAK,EAAE;OACR,CAAC;MAEF;MACA;MACAjC,CAAC,GAAIZ,GAAW,CAAC8C,aAAa,CAACC,MAAM,GAAG,EAAE;MAE1C;MACA,IAAInC,CAAC,GAAGZ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAAC0C,SAAS,EAAE,GAAG,GAAG,EAAE;QAC/ChD,GAAG,CAACiD,OAAO,EAAE;QACbrC,CAAC,GAAGJ,MAAM,CAACG,GAAG;MAChB;IACF,CAAC;IAEDxD,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAACxB,OAAO,CAAC,CAACuC,QAAQ,EAAEgB,GAAG,KAAI;MAC7C;MACAjB,WAAW,CAACC,QAAQ,EAAEf,OAAO,CAACe,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAMoC,QAAQ,GAAG,oBAAoB,IAAI,CAAC/R,MAAM,EAAE0H,YAAY,IAAI,EAAE,IAAI,IAAImF,IAAI,EAAE,CAACmF,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IACpHpD,GAAG,CAACqD,IAAI,CAACH,QAAQ,CAAC;EACpB;EAEOI,uBAAuBA,CAAC1L,EAAO;IACpC,IAAI,CAAC0C,2BAA2B,GAAG1C,EAAE;IACrC,MAAM2L,GAAG,GAAG,IAAI,CAACvS,kBAAkB,CAACmO,IAAI,CAACqE,CAAC,IAAI9E,MAAM,CAAC8E,CAAC,CAAC5L,EAAE,CAAC,KAAK8G,MAAM,CAAC9G,EAAE,CAAC,CAAC;IAC1E,IAAI2L,GAAG,EAAE;MACP,IAAI,CAACpJ,iBAAiB,GAAGoJ,GAAG,CAAC/P,KAAK;MAClC,IAAI,CAAC4G,mBAAmB,GAAGmJ,GAAG,CAAC9L,eAAe;IAChD;EACF;EAEOgM,sCAAsCA,CAAA;IAC3C,IAAI,CAAC,IAAI,CAACnJ,2BAA2B,EAAE;MAAE,OAAO,EAAE;IAAE;IACpD,OAAO,IAAI,CAAC3C,8BAA8B,CAAC,IAAI,CAAC2C,2BAA2B,CAAC;EAC9E;EAEO3C,8BAA8BA,CAACwG,WAAgB;IACpD,MAAMV,OAAO,GAAG,IAAI,CAACpD,eAAe,CAACmH,MAAM,CAAC7D,CAAC,IAAIe,MAAM,CAACf,CAAC,CAACQ,WAAW,CAAC,KAAKO,MAAM,CAACP,WAAW,CAAC,CAAC;IAE/F;IACA,OAAOV,OAAO,CAAC4B,IAAI,CAAC,CAACJ,CAAC,EAAEC,CAAC,KAAI;MAC3B;MACA,IAAID,CAAC,CAACpI,WAAW,KAAKqI,CAAC,CAACrI,WAAW,EAAE;QACnC,OAAO,CAAC;MACV;MAEA;MACA,IAAI,CAACoI,CAAC,CAACpI,WAAW,IAAIqI,CAAC,CAACrI,WAAW,EAAE;QACnC,OAAO,CAAC,CAAC;MACX;MAEA;MACA,IAAIoI,CAAC,CAACpI,WAAW,IAAI,CAACqI,CAAC,CAACrI,WAAW,EAAE;QACnC,OAAO,CAAC;MACV;MAEA,OAAO,CAAC;IACV,CAAC,CAAC,CAAC6M,OAAO,EAAE,CAAC,CAAC;EAChB;EAEOrM,wBAAwBA,CAACrE,KAAa;IAC3C,IAAI,IAAI,CAAC2H,kBAAkB,CAACgJ,GAAG,CAAC3Q,KAAK,CAAC,EAAE;MACtC,IAAI,CAAC2H,kBAAkB,CAACiJ,MAAM,CAAC5Q,KAAK,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAAC2H,kBAAkB,CAACkJ,GAAG,CAAC7Q,KAAK,CAAC;IACpC;EACF;EAEOuE,mBAAmBA,CAACvE,KAAa;IACtC,OAAO,IAAI,CAAC2H,kBAAkB,CAACgJ,GAAG,CAAC3Q,KAAK,CAAC;EAC3C;EAEO/B,wBAAwBA,CAAA;IAC7B,OAAO,IAAI,CAACD,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACR,MAAM,GAAG,CAAC,IAAI,IAAI,CAACmK,kBAAkB,CAACmJ,IAAI,KAAK,IAAI,CAAC9S,kBAAkB,CAACR,MAAM;EACzI;EAEOG,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACK,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACR,MAAM,KAAK,CAAC,EAAE;MACpE;IACF;IACA,IAAI,IAAI,CAACS,wBAAwB,EAAE,EAAE;MACnC,IAAI,CAAC0J,kBAAkB,CAACoJ,KAAK,EAAE;IACjC,CAAC,MAAM;MACL,IAAI,CAACpJ,kBAAkB,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAC5J,kBAAkB,CAAC0M,GAAG,CAAC,CAACsG,CAAC,EAAElC,GAAG,KAAKA,GAAG,CAAC,CAAC;IACjF;IACA,IAAI,CAAClI,GAAG,CAAC2D,YAAY,EAAE;EACzB;EAEOnH,qBAAqBA,CAAC6N,QAAgB;IAC3C,IAAI,IAAI,CAACpJ,eAAe,CAAC8I,GAAG,CAACM,QAAQ,CAAC,EAAE;MACtC,IAAI,CAACpJ,eAAe,CAAC+I,MAAM,CAACK,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACpJ,eAAe,CAACgJ,GAAG,CAACI,QAAQ,CAAC;MAClC;MACA,IAAI,CAAC,IAAI,CAACnJ,kBAAkB,CAACmJ,QAAQ,CAAC,EAAE;QACtC,IAAI,CAACnJ,kBAAkB,CAACmJ,QAAQ,CAAC,GAAG,aAAa;MACnD;IACF;EACF;EAEOtN,gBAAgBA,CAACsN,QAAgB;IACtC,OAAO,IAAI,CAACpJ,eAAe,CAAC8I,GAAG,CAACM,QAAQ,CAAC;EAC3C;EAEOC,aAAaA,CAACD,QAAgB,EAAEE,GAAW,EAAElV,MAAW;IAC7DA,MAAM,CAACmE,eAAe,EAAE;IACxB,IAAI,CAAC0H,kBAAkB,CAACmJ,QAAQ,CAAC,GAAGE,GAAG;IACvC,IAAI,CAACvK,GAAG,CAAC2D,YAAY,EAAE;EACzB;EAEO6G,oBAAoBA,CAACzE,MAAW;IACrC,IAAI,CAAC1F,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAMwH,QAAQ,GAAG;MACfrP,oBAAoB,EAAE2K,MAAM,CAAC3K,oBAAoB;MACjDC,kBAAkB,EAAE0K,MAAM,CAAC1K,kBAAkB;MAC7CiF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB7D,UAAU,EAAEsJ,MAAM,CAACtJ,UAAU;MAC7BiO,cAAc,EAAE,IAAI,CAAC9J,SAAS,CAAC+J;KAChC;IAED,IAAI,CAACvK,cAAc,CAACwK,oBAAoB,CAACH,QAAQ,CAAC,CAACzI,SAAS,CAAC;MAC3DiB,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC9C,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIE,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAI,CAACjD,wBAAwB,CAAC0K,WAAW,CAAC1H,GAAG,CAACE,YAAY,CAACyH,OAAO,EAAE,EAAE,CAAC;UACvE;UACA,IAAI,CAACrQ,oBAAoB,EAAE;QAC7B,CAAC,MAAM;UACL,IAAI,CAAC0F,wBAAwB,CAAC4K,SAAS,CAAC5H,GAAG,CAACO,YAAY,IAAE,wBAAwB,EAAE,EAAE,CAAC;UACvF;QACF;QACA,IAAI,CAAC1D,GAAG,CAAC2D,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACtF,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC9C,wBAAwB,CAAC4K,SAAS,CAAC,kCAAkC,EAAE,EAAE,CAAC;QAC/E;QACA1I,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAAC3F,GAAG,CAAC2D,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOqH,mBAAmBA,CAACzG,WAAgB;IACzC,OAAOO,MAAM,CAAC,IAAI,CAACpE,2BAA2B,CAAC,KAAKoE,MAAM,CAACP,WAAW,CAAC;EACzE;EAEOlL,WAAWA,CAACD,KAAa;IAC9B,IAAI,CAACM,kBAAkB,GAAGN,KAAK;IAC/B,IAAI,CAACmH,iBAAiB,GAAG,IAAI,CAAC5J,YAAY,CAAC,IAAI,CAAC+C,kBAAkB,CAAC,CAACE,KAAK;IACzE,IAAI,CAAC4G,mBAAmB,GACtB,IAAI,CAAC7J,YAAY,CAAC,IAAI,CAAC+C,kBAAkB,CAAC,CAACmE,eAAe;EAC9D;EAEOoN,0BAA0BA,CAAA;IAC/B,IACE,IAAI,CAACvR,kBAAkB,KAAK,IAAI,IAChC,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAAC/C,YAAY,CAACC,MAAM,EACnD;MACA,OAAO,EAAE;IACX;IAEA,OAAO,IAAI,CAACD,YAAY,CAAC,IAAI,CAAC+C,kBAAkB,CAAC,CAACmK,OAAO,IAAI,EAAE;EACjE;EAIOtF,MAAMA,CAAA;IACX8D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACb,YAAY,EAAE,YAAY,EAAE,IAAI,CAACC,SAAS,CAAC;IAC7F,IAAI,IAAI,CAACD,YAAY,KAAK,SAAS,IAAI,IAAI,CAACC,SAAS,EAAE;MACrD;MACAW,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,IAAI,CAACxC,MAAM,CAACoL,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAACxJ,SAAS,CAAC,EAAE;QACvDQ,WAAW,EAAE;UAAEiJ,SAAS,EAAE;QAAS;OACpC,CAAC;IACJ,CAAC,MAAM;MACL;MACA9I,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,IAAI,CAACxC,MAAM,CAACoL,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC;EACF;EAEO/T,UAAUA,CAAA;IACfiU,MAAM,CAACC,IAAI,CACT,GAAG,IAAI,CAAC9T,MAAM,CAAC+T,cAAc,GAAG,IAAI,CAAC/T,MAAM,CAACC,cAAc,EAAE,EAC5D,QAAQ,CACT;EACH;EAEO+T,iBAAiBA,CAACxF,MAAW;IAClC,MAAMyF,QAAQ,GAAG,IAAI,CAACzL,YAAY,CAACsL,IAAI,CAAC9W,2BAA2B,EAAE;MACnE2V,IAAI,EAAE;KACP,CAAC;IACFsB,QAAQ,CAACC,iBAAiB,CAAC1F,MAAM,GAAGA,MAAM;IAC1CyF,QAAQ,CAACC,iBAAiB,CAACnL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDkL,QAAQ,CAACC,iBAAiB,CAACC,aAAa,GAAG,IAAI,CAACnU,MAAM;IAEtD;IACAiU,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAClR,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAACoR,KAAK,CAAEpI,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEOpK,cAAcA,CAACiF,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,MAAM2O,UAAU,GAAG3O,MAAM,CAAC4O,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAChF3J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEnF,MAAM,EAAE,aAAa,EAAE2O,UAAU,CAAC;IAC5E;IACA,MAAMG,QAAQ,GAA4B;MACxC,WAAW,EAAE,WAAW;MACxB,oBAAoB,EAAE,oBAAoB;MAC1C,uBAAuB,EAAE,uBAAuB;MAChD,aAAa,EAAE,aAAa;MAC5B,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE,UAAU;MACtB,UAAU,EAAE,UAAU;MACtB,cAAc,EAAE,cAAc;MAC9B,mBAAmB,EAAE,mBAAmB;MACxC,uBAAuB,EAAE,uBAAuB;MAChD,cAAc,EAAE,cAAc;MAC9B,cAAc,EAAE,cAAc;MAC9B,mBAAmB,EAAE,mBAAmB;MACxC,mBAAmB,EAAE,mBAAmB;MACxC,iBAAiB,EAAE;KACpB;IACD,MAAMC,QAAQ,GAAGD,QAAQ,CAACH,UAAU,CAAC,IAAIA,UAAU;IACnD,MAAMK,UAAU,GAAG,SAAS,GAAGD,QAAQ;IACvC7J,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6J,UAAU,CAAC;IAC9C9J,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,CAClD,gBAAgB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,iBAAiB,EAC7E,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,0BAA0B,EACvF,8BAA8B,EAAE,qBAAqB,EAAE,qBAAqB,EAC5E,kBAAkB,EAAE,0BAA0B,CAC/C,CAAC;IACF,OAAO6J,UAAU;EACnB;EAEOrS,cAAcA,CAACqD,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,EAAE;IACtB,MAAM2O,UAAU,GAAG3O,MAAM,CAAC4O,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAEhF,MAAMI,QAAQ,GAAyB;MACrC,mBAAmB,EAAE;QAAEC,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAClG,mBAAmB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAClG,iBAAiB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAChG,uBAAuB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACtG,cAAc,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC7F,cAAc,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC7F,WAAW,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC1F,SAAS,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACxF,UAAU,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACzF,WAAW,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC1F,UAAU,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB;KACxF;IAED,OAAOH,QAAQ,CAACN,UAAU,CAAC,IAAI,EAAE;EACnC;EAEAnW,OAAOA,CAAC4U,GAAQ,EAAElV,MAAW;IAC3B,IAAIkV,GAAG,KAAK,UAAU,IAAI,CAAC,IAAI,CAACnL,uBAAuB,EAAE,EAAE;MACzD;IACF;IACA,IAAI,CAACpJ,WAAW,GAAGuU,GAAG;IACtB,IAAI,CAACvK,GAAG,CAAC2D,YAAY,EAAE;EACzB;EAEOvE,uBAAuBA,CAAA;IAC5B,MAAMoN,IAAI,GAAG,CAAC,IAAI,CAACtN,gBAAgB,IAAI,IAAI,CAAC3H,MAAM,EAAE2H,gBAAgB,IAAI,EAAE,EAAEwI,QAAQ,EAAE,CAACqE,WAAW,EAAE;IACpG;IACA,OAAOS,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM;EAC/C;EAEOC,uBAAuBA,CAAA;IAC5B,MAAMD,IAAI,GAAG,CAAC,IAAI,CAACtN,gBAAgB,IAAI,IAAI,CAAC3H,MAAM,EAAE2H,gBAAgB,IAAI,EAAE,EAAEwI,QAAQ,EAAE,CAACqE,WAAW,EAAE;IACpG;IACA,OAAOS,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM;EAC/C;EAEA/V,QAAQA,CAAA;IACN,MAAMiW,eAAe,GAKjB;MACFxC,IAAI,EAAE,IAAI;MAAE;MACZyC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMrB,QAAQ,GAAG,IAAI,CAACzL,YAAY,CAACsL,IAAI,CACrC5W,8BAA8B,EAC9BiY,eAAe,CAChB;IAED;IACAlB,QAAQ,CAACC,iBAAiB,CAACnL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDkL,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAAC9J,SAAS,CAAC+J,MAAM,CAAC,CAAC;IACnEa,QAAQ,CAACC,iBAAiB,CAACxM,YAAY,GAAG,IAAI,CAAC1H,MAAM,EAAE0H,YAAY,IAAI,EAAE;IAEzE;IACAuM,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAChJ,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAACkJ,KAAK,CAAEpI,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEA/I,kBAAkBA,CAACuT,WAAmB;IACpC,IAAIA,WAAW,GAAG,CAAC,IAAIA,WAAW,IAAI,IAAI,CAACnW,YAAY,CAACC,MAAM,EAAE;MAC9D;IACF;IAEA,MAAM8V,eAAe,GAKjB;MACFxC,IAAI,EAAE,IAAI;MACVyC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE;KACb;IAED,MAAMrB,QAAQ,GAAG,IAAI,CAACzL,YAAY,CAACsL,IAAI,CACrC5W,8BAA8B,EAC9BiY,eAAe,CAChB;IAED;IACAlB,QAAQ,CAACC,iBAAiB,CAACnL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDkL,QAAQ,CAACC,iBAAiB,CAACsB,UAAU,GAAG,IAAI,CAACpW,YAAY,CAACmW,WAAW,CAAC;IACtEtB,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAAC9J,SAAS,CAAC+J,MAAM,CAAC,CAAC;IACnEa,QAAQ,CAACC,iBAAiB,CAACxM,YAAY,GAAG,IAAI,CAAC1H,MAAM,EAAE0H,YAAY,IAAI,EAAE;IAEzE;IACAuM,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxB;QACA,IAAI,CAAChJ,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAACkJ,KAAK,CAAEpI,KAAK,IAAI;MACjBpB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEA0K,SAASA,CAAA;IACP,MAAMN,eAAe,GAKjB;MACFxC,IAAI,EAAE,IAAI;MAAE;MACZyC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMrB,QAAQ,GAAG,IAAI,CAACzL,YAAY,CAACsL,IAAI,CACrC5W,8BAA8B,EAC9BiY,eAAe,CAChB;EACH;EACAvW,UAAUA,CAAA;IACR,MAAMuW,eAAe,GAKjB;MACFxC,IAAI,EAAE,IAAI;MAAE;MACZyC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMrB,QAAQ,GAAG,IAAI,CAACzL,YAAY,CAACsL,IAAI,CACrC3W,oBAAoB,EACpBgY,eAAe,CAChB;IACD;IACAlB,QAAQ,CAACC,iBAAiB,CAACzN,EAAE,GAAG,IAAI,CAACsC,QAAQ;IAE7C;IACAkL,QAAQ,CAACC,iBAAiB,CAACwB,SAAS,CAACjL,SAAS,CAAEkL,KAAc,IAAI;MAChE,IAAIA,KAAK,EAAE;QACT,IAAI,CAACxK,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC;EACJ;EAEAzL,WAAWA,CAACkW,CAAM;IAChB,IAAI,CAAC9M,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACnC,YAAY,GAAGqM,CAAC,IAAI,KAAK;IAC9B,IAAI,CAAC/M,cAAc,CAACnJ,WAAW,CAAC;MAAEqJ,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEQ,YAAY,EAAE,IAAI,CAACA,YAAY;MAAEsM,SAAS,EAAE;IAAI,CAAE,CAAC,CAACpL,SAAS,CAAC;MACvHiB,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC9C,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/CZ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEa,GAAG,CAAC;QAClCd,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOa,GAAG,CAAC;QACzCd,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiB,MAAM,CAACC,IAAI,CAACL,GAAG,IAAI,EAAE,CAAC,CAAC;QACrDd,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEa,GAAG,EAAEkK,OAAO,CAAC;QAC9ChL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEa,GAAG,EAAE2H,OAAO,CAAC;QAC9CzI,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEa,GAAG,EAAEE,YAAY,CAAC;QAExD;QACA,IAAIA,YAAY,GAAGF,GAAG;QAEtB;QACA,IAAIA,GAAG,EAAEE,YAAY,EAAE;UACrBA,YAAY,GAAGF,GAAG,CAACE,YAAY;QACjC,CAAC,MAAM,IAAIF,GAAG,EAAEkF,IAAI,EAAEhF,YAAY,EAAE;UAClCA,YAAY,GAAGF,GAAG,CAACkF,IAAI,CAAChF,YAAY;QACtC,CAAC,MAAM,IAAIF,GAAG,EAAEkF,IAAI,EAAE;UACpBhF,YAAY,GAAGF,GAAG,CAACkF,IAAI;QACzB;QAEAhG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,YAAY,CAAC;QAChDhB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEe,YAAY,EAAEgK,OAAO,CAAC;QACpDhL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEe,YAAY,EAAEyH,OAAO,CAAC;QAEpD,IAAIzH,YAAY,EAAED,OAAO,EAAE;UACzB;UACA,IAAI,CAACjD,wBAAwB,CAAC4K,SAAS,CAAC1H,YAAY,CAACK,YAAY,EAAE,EAAE,CAAC;QACxE,CAAC,MAAM,IAAIL,YAAY,EAAEgK,OAAO,KAAK,KAAK,EAAE;UAC1C;UACA,IAAIhK,YAAY,CAACyH,OAAO,KAAK,oCAAoC,EAAE;YACzC,IAAI,CAAC3K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAI1H,YAAY,CAACyH,OAAO,KAAK,mCAAmC,EAAE;YAC/C,IAAI,CAAC3K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAAC5K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAI1H,YAAY,EAAEgK,OAAO,KAAK,IAAI,IAAIhK,YAAY,EAAEC,IAAI,EAAE;UAC/D,IAAI,CAACnD,wBAAwB,CAAC0K,WAAW,CAAC,4BAA4B,EAAE,EAAE,CAAC;UAC3E;UACA,IAAI,CAACnI,kBAAkB,EAAE;UACzB,IAAI,CAACjI,oBAAoB,EAAE;QAC7B,CAAC,MAAM;UACL;UACA4H,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACA,IAAI,CAACnC,wBAAwB,CAAC0K,WAAW,CAAC,4BAA4B,EAAE,EAAE,CAAC;UAE3E,IAAI,CAACnI,kBAAkB,EAAE;UACzB,IAAI,CAACjI,oBAAoB,EAAE;QAC7B;QACA,IAAI,CAACuF,GAAG,CAAC2D,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACtF,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C;QACAZ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqD,GAAG,CAAC;QACnCtD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,OAAOqD,GAAG,CAAC;QACtCtD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiB,MAAM,CAACC,IAAI,CAACmC,GAAG,IAAI,EAAE,CAAC,CAAC;QAClDtD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqD,GAAG,EAAExI,MAAM,CAAC;QACzCkF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqD,GAAG,EAAEmF,OAAO,CAAC;QAC3CzI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEqD,GAAG,EAAElC,KAAK,CAAC;QAEvC;QACA;QACA,IAAIkC,GAAG,EAAE0H,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAI1H,GAAG,CAACmF,OAAO,KAAK,oCAAoC,EAAE;YAChC,IAAI,CAAC3K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAIpF,GAAG,CAACmF,OAAO,KAAK,mCAAmC,EAAE;YACtC,IAAI,CAAC3K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAAC5K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAIpF,GAAG,EAAElC,KAAK,EAAEqH,OAAO,EAAE;UAC9B,IAAInF,GAAG,CAAClC,KAAK,CAACqH,OAAO,KAAK,oCAAoC,EAAE;YACtC,IAAI,CAAC3K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAIpF,GAAG,CAAClC,KAAK,CAACqH,OAAO,KAAK,mCAAmC,EAAE;YAC5C,IAAI,CAAC3K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAAC5K,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAIpF,GAAG,EAAExI,MAAM,KAAK,GAAG,EAAE;UACN,IAAI,CAACgD,wBAAwB,CAAC4K,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAE3J;UACA;QACF,CAAC,MAAM;UACL;QAAA;QAEF1I,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAAC3F,GAAG,CAAC2D,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEA2J,kBAAkBA,CAACvH,MAAU;IAC3B,MAAM2G,eAAe,GAKjB;MACFxC,IAAI,EAAE,IAAI;MAAE;MACZyC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMrB,QAAQ,GAAG,IAAI,CAACzL,YAAY,CAACsL,IAAI,CACrC1W,2BAA2B,EAC3B+X,eAAe,CAChB;IAEDrK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEyD,MAAM,CAAC;IAClC;IACAyF,QAAQ,CAACC,iBAAiB,CAACnL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDkL,QAAQ,CAACC,iBAAiB,CAACsB,UAAU,GAAGhH,MAAM;IAC9CyF,QAAQ,CAACC,iBAAiB,CAACC,aAAa,GAAG,IAAI,CAACnU,MAAM;IACtDiU,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAAC9J,SAAS,CAAC+J,MAAM,CAAC,CAAC;IAEnE;IACAa,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAClR,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAACoR,KAAK,CAAEpI,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEOtH,iBAAiBA,CAACuS,UAAe,EAAExH,MAAW;IACnD;IACA,MAAMyF,QAAQ,GAAG,IAAI,CAACzL,YAAY,CAACsL,IAAI,CAAC7W,sBAAsB,EAAE;MAC9D0V,IAAI,EAAE,IAAI;MACVyC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE;KACX,CAAC;IAEF;IACApB,QAAQ,CAACC,iBAAiB,CAAC8B,UAAU,GAAGA,UAAU;IAClD/B,QAAQ,CAACC,iBAAiB,CAAC1F,MAAM,GAAGA,MAAM;IAC1CyF,QAAQ,CAACC,iBAAiB,CAACnL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnDkL,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAAC9J,SAAS,CAAC+J,MAAM;IACjEa,QAAQ,CAACC,iBAAiB,CAAC5K,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACA2K,QAAQ,CAACC,iBAAiB,CAAC+B,iBAAiB,CAACxL,SAAS,CAAEyI,QAAa,IAAI;MACvE,IAAI,CAACgD,cAAc,CAAChD,QAAQ,EAAEe,QAAQ,CAAC;IACzC,CAAC,CAAC;IAEF;IACAA,QAAQ,CAACC,iBAAiB,CAACiC,iBAAiB,CAAC1L,SAAS,CAAEqL,OAAgB,IAAI;MAC1E,IAAI,CAACA,OAAO,EAAE;QACZ;QACA7B,QAAQ,CAACC,iBAAiB,CAAC/U,SAAS,GAAG,KAAK;MAC9C;IACF,CAAC,CAAC;IAEF8U,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAC,MAAK;MACxB;IAAA,CACD,CAAC,CAACC,KAAK,CAAC,MAAK;MACZ;IAAA,CACD,CAAC;EACJ;EAEO4B,cAAcA,CAAChD,QAAa,EAAEe,QAAc;IACjD,IAAI,CAACf,QAAQ,EAAE;MACb;IACF;IAEA,IAAI,CAACpK,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAAC7C,cAAc,CAACwK,oBAAoB,CAACH,QAAQ,CAAC,CAACzI,SAAS,CAAC;MAC3DiB,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC9C,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIE,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UACF,IAAI,CAACjD,wBAAwB,CAAC0K,WAAW,CAAC1H,GAAG,CAACE,YAAY,CAACyH,OAAO,EAAE,EAAE,CAAC;UAE/F;UACA,IAAI,CAACrQ,oBAAoB,EAAE,CAAC,CAAC;UAE7B;UACA,IAAI+Q,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;YAC1CD,QAAQ,CAACC,iBAAiB,CAACiC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;UACzD;UAEA;UACA,IAAInC,QAAQ,EAAE;YACZA,QAAQ,CAACoC,KAAK,EAAE;UAClB;QACF,CAAC,MAAM;UACL;UACwB,IAAI,CAACzN,wBAAwB,CAAC4K,SAAS,CAAC5H,GAAG,CAACO,YAAY,IAAE,2BAA2B,EAAE,EAAE,CAAC;UAElH;UACA,IAAI8H,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;YAC1CD,QAAQ,CAACC,iBAAiB,CAACiC,iBAAiB,CAACC,IAAI,CAAC,KAAK,CAAC;UAC1D;QACF;QACA,IAAI,CAAC3N,GAAG,CAAC2D,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACtF,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C;QACAZ,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QACM,IAAI,CAACxF,wBAAwB,CAAC0K,WAAW,CAAC,2BAA2B,EAAE,EAAE,CAAC;QAElG;QACA,IAAIW,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;UAC1CD,QAAQ,CAACC,iBAAiB,CAACiC,iBAAiB,CAACC,IAAI,CAAC,KAAK,CAAC;QAC1D;QAEA,IAAI,CAAC3N,GAAG,CAAC2D,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOhH,iBAAiBA,CAACoJ,MAAW;IAClC,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA,IAAI;MACF,MAAM9G,YAAY,GAAG,IAAI,CAAC1H,MAAM,EAAE0H,YAAY,IAAI,EAAE;MACpD,MAAM7B,QAAQ,GAAG,CAAC2I,MAAM,EAAE9B,UAAU,IAAI8B,MAAM,EAAE8H,oBAAoB,IAAI9H,MAAM,EAAE3I,QAAQ,IAAI,EAAE,EAAEsK,QAAQ,EAAE;MAC1G,MAAMoG,YAAY,GAAG,CAAC/H,MAAM,EAAE7J,QAAQ,KAAK6J,MAAM,EAAE+H,YAAY,IAAI,EAAE,CAAC,EAAEpG,QAAQ,EAAE;MAClF,MAAMqG,aAAa,GAAG,CAAChI,MAAM,EAAE3K,oBAAoB,IAAI,EAAE,EAAEsM,QAAQ,EAAE;MAErE;MACA,MAAMsG,cAAc,GAAG,IAAI,CAAC5W,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACR,MAAM,GAAG,CAAC;MACnF,MAAMqX,KAAK,GAAGD,cAAc,GAAG,CAAC,GAAGA,cAAc,CAACtG,QAAQ,EAAE,GAAG,CAAC3B,MAAM,EAAEkI,KAAK,IAAIlI,MAAM,EAAEmI,KAAK,IAAI,EAAE,EAAExG,QAAQ,EAAE;MAEhH,MAAMvK,MAAM,GAAG,CAAC4I,MAAM,EAAEoI,UAAU,IAAIpI,MAAM,EAAEqI,aAAa,IAAIrI,MAAM,EAAE5I,MAAM,IAAI,EAAE,EAAEuK,QAAQ,EAAE;MAC/F,MAAM2G,WAAW,GAAItI,MAAM,EAAE5B,aAAa,IAAI4B,MAAM,EAAE9L,aAAa,IAAI8L,MAAM,EAAE1B,OAAO,IAAI0B,MAAM,EAAEP,WAAW,GACzG5Q,UAAU,CAACoT,UAAU,CAACjC,MAAM,EAAE5B,aAAa,IAAI4B,MAAM,EAAE9L,aAAa,IAAI8L,MAAM,EAAE1B,OAAO,IAAI0B,MAAM,EAAEP,WAAW,CAAC,GAC/G5Q,UAAU,CAACoT,UAAU,CAAC,IAAI5D,IAAI,EAAE,CAAC;MAErC,MAAMgC,GAAG,GAAG,IAAIvR,KAAK,CAAC;QAAEwR,WAAW,EAAE,UAAU;QAAEC,IAAI,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAI,CAAE,CAAC;MAE5E,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;MAClD,MAAM2H,UAAU,GAAGlI,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAAC0C,SAAS,EAAE;MACpD,MAAMxC,MAAM,GAAG;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,GAAG,EAAE,EAAE;QAAEwH,MAAM,EAAE;MAAE,CAAE;MAE3D;MACAnI,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCf,GAAG,CAACgB,WAAW,CAAC,EAAE,CAAC;MACnBhB,GAAG,CAAC7E,IAAI,CAAC,aAAatC,YAAY,IAAI,EAAE,EAAE,EAAE2H,MAAM,CAACC,IAAI,EAAED,MAAM,CAACG,GAAG,CAAC;MAEpE,MAAMoB,MAAM,GAAGvB,MAAM,CAACG,GAAG,GAAG,EAAE;MAE9B;MACA,MAAMyH,OAAO,GAAG,CACd,aAAa,EACb,eAAe,EACf,gCAAgC,EAChC,OAAO,EACP,QAAQ,EACR,MAAM,CACP;MAED;MACA,MAAMC,cAAc,GAAQ,CAAC1I,MAAM,IAAKA,MAAc,CAACzB,WAAW,KAAMyB,MAAc,CAAC5J,WAAW,IAAI,EAAE;MACxG,MAAMuS,gBAAgB,GAAGlH,KAAK,CAACmH,OAAO,CAACF,cAAc,CAAC,GAAGA,cAAc,GAAIA,cAAc,GAAG,CAACA,cAAc,CAAC,GAAG,EAAG;MAClH,MAAMG,QAAQ,GAAUF,gBAAgB,CAAC9X,MAAM,GAAG,CAAC,GAC/C8X,gBAAgB,CAAC5K,GAAG,CAAE+K,CAAM,IAAK,CAC/BzR,QAAQ,IAAI,EAAE,EACdyR,CAAC,EAAE3S,QAAQ,IAAI4R,YAAY,IAAI,EAAE,EACjCe,CAAC,EAAE1T,QAAQ,IAAI0T,CAAC,EAAEzT,oBAAoB,IAAI2S,aAAa,IAAI,EAAE,EAC7DE,KAAK,IAAI,EAAE,EACX9Q,MAAM,IAAI,EAAE,EACZ,CAAC0R,CAAC,EAAE9S,YAAY,GAAGnH,UAAU,CAACoT,UAAU,CAAC6G,CAAC,CAAC9S,YAAY,CAAC,GAAGsS,WAAW,KAAK,EAAE,CAC9E,CAAC,GACF,CAAC,CACCjR,QAAQ,IAAI,EAAE,EACd0Q,YAAY,IAAI,EAAE,EAClBC,aAAa,IAAI,EAAE,EACnBE,KAAK,IAAI,EAAE,EACX9Q,MAAM,IAAI,EAAE,EACZkR,WAAW,IAAI,EAAE,CAClB,CAAC;MAENvZ,SAAS,CAACsR,GAAG,EAAE;QACb+B,MAAM;QACNC,IAAI,EAAE,CAACoG,OAAO,CAAC;QACfnG,IAAI,EAAEuG,QAAQ;QACdhI,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDwB,MAAM,EAAE;UAAEC,IAAI,EAAE,WAAW;UAAEC,QAAQ,EAAE,CAAC;UAAEC,WAAW,EAAE,CAAC;UAAEqG,QAAQ,EAAE;QAAW,CAAE;QACjFnG,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAEkG,SAAS,EAAE,MAAM;UAAEvG,QAAQ,EAAE;QAAC,CAAE;QAChGO,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEC,SAAS,EAAE;UAAE,CAAE;UACpB,CAAC,EAAE;YAAEA,SAAS,EAAE,CAACxC,SAAS,GAAGI,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,IAAI;UAAI,CAAE;UACjE,CAAC,EAAE;YAAEkC,SAAS,EAAE,CAACxC,SAAS,GAAGI,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,IAAI;UAAI,CAAE;UACjE,CAAC,EAAE;YAAEkC,SAAS,EAAE,EAAE;YAAEF,MAAM,EAAE;UAAQ,CAAE;UACtC,CAAC,EAAE;YAAEE,SAAS,EAAE,EAAE;YAAEF,MAAM,EAAE;UAAQ,CAAE;UACtC,CAAC,EAAE;YAAEE,SAAS,EAAE,EAAE;YAAEF,MAAM,EAAE;UAAQ;SACrC;QACDkG,YAAY,EAAG1L,IAAS,IAAI;UAC1B;UACA,IAAIA,IAAI,CAAC2L,OAAO,KAAK,MAAM,IAAI3L,IAAI,CAAC4L,MAAM,CAAC9V,KAAK,KAAK,CAAC,EAAE;YACtDkK,IAAI,CAAC6L,IAAI,CAAC7G,MAAM,CAACO,SAAS,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;UAC1C;QACF,CAAC;QACDuG,WAAW,EAAG9L,IAAS,IAAI;UACzB;UACA,IAAIA,IAAI,CAAC2L,OAAO,KAAK,MAAM,IAAI3L,IAAI,CAAC4L,MAAM,CAAC9V,KAAK,KAAK,CAAC,EAAE;YACtD,MAAMoI,KAAK,GAAGsD,MAAM,CAACxB,IAAI,CAAC6L,IAAI,CAACE,GAAG,IAAI,EAAE,CAAC;YACzC,MAAMC,UAAU,GAAG9N,KAAK,CAACuK,WAAW,EAAE,KAAK,UAAU;YACrD,MAAMwD,EAAE,GAAGD,UAAU,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;YACrD,MAAMzG,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YAEjC;YACAzC,GAAG,CAACoJ,YAAY,CAACD,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC;YACrCnJ,GAAG,CAACqJ,IAAI,CAACnM,IAAI,CAAC6L,IAAI,CAACO,CAAC,GAAG,GAAG,EAAEpM,IAAI,CAAC6L,IAAI,CAACnI,CAAC,GAAG,GAAG,EAAE1D,IAAI,CAAC6L,IAAI,CAACQ,KAAK,GAAG,CAAC,EAAErM,IAAI,CAAC6L,IAAI,CAACS,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC;YAE9F;YACAxJ,GAAG,CAACyJ,YAAY,CAAChH,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1DzC,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;YAChCf,GAAG,CAACgB,WAAW,CAAC,CAAC,CAAC;YAClB,MAAM0I,SAAS,GAAG1J,GAAG,CAAC2J,YAAY,CAACvO,KAAK,CAAC;YACzC,MAAMwO,KAAK,GAAG1M,IAAI,CAAC6L,IAAI,CAACO,CAAC,GAAGpM,IAAI,CAAC6L,IAAI,CAACQ,KAAK,GAAG,CAAC,GAAGG,SAAS,GAAG,CAAC;YAC/D,MAAMG,KAAK,GAAG3M,IAAI,CAAC6L,IAAI,CAACnI,CAAC,GAAG1D,IAAI,CAAC6L,IAAI,CAACS,MAAM,GAAG,CAAC,GAAG,CAAC;YACpDxJ,GAAG,CAAC7E,IAAI,CAACC,KAAK,EAAEwO,KAAK,EAAEC,KAAK,CAAC;YAE7B3M,IAAI,CAAC6L,IAAI,CAAC5N,IAAI,GAAG,EAAE;UACrB;QACF,CAAC;QACD0H,KAAK,EAAE;OACR,CAAC;MAEF;MACA,MAAMiH,SAAS,GAAG9J,GAAG,CAAC+J,gBAAgB,EAAE;MACxC,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI+C,SAAS,EAAE/C,CAAC,EAAE,EAAE;QACnC/G,GAAG,CAACgK,OAAO,CAACjD,CAAC,CAAC;QACd/G,GAAG,CAACiK,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/BjK,GAAG,CAACkK,IAAI,CAAC1J,MAAM,CAACC,IAAI,EAAEyH,UAAU,GAAG,EAAE,EAAE9H,SAAS,GAAGI,MAAM,CAACE,KAAK,EAAEwH,UAAU,GAAG,EAAE,CAAC;QACjFlI,GAAG,CAACe,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCf,GAAG,CAACgB,WAAW,CAAC,CAAC,CAAC;QAClBhB,GAAG,CAACyJ,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/BzJ,GAAG,CAAC7E,IAAI,CAAC,iBAAiB,IAAI6C,IAAI,EAAE,CAACmM,cAAc,EAAE,EAAE,EAAE3J,MAAM,CAACC,IAAI,EAAEyH,UAAU,GAAG,EAAE,CAAC;QACtFlI,GAAG,CAAC7E,IAAI,CAAC,QAAQ4L,CAAC,OAAO+C,SAAS,EAAE,EAAE1J,SAAS,GAAGI,MAAM,CAACE,KAAK,GAAG,EAAE,EAAEwH,UAAU,GAAG,EAAE,CAAC;MACvF;MAEA,MAAMhF,QAAQ,GAAG,UAAUrK,YAAY,GAAGA,YAAY,GAAG,GAAG,GAAG,EAAE,GAAG,IAAImF,IAAI,EAAE,CAACmF,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MAChHpD,GAAG,CAACqD,IAAI,CAACH,QAAQ,CAAC;IAEpB,CAAC,CAAC,OAAO7F,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACrB,IAAI,CAACtD,wBAAwB,CAAC0K,WAAW,CAAC,yCAAyC,EAAE,EAAE,CAAC;MAEhH;IACF;EACF;EAEO/I,YAAYA,CAAA;IACjB;IACA;IACA,OAAO,IAAI,CAAClB,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC4P,MAAM,KAAK,CAAC;EACtD;EAEOxU,4BAA4BA,CAACuR,UAAe;IACjD;IACA;IACA;IACA,IAAI,IAAI,CAAC1M,OAAO,EAAE;MAChB,OAAO,IAAI;IACb;IAEA;IACA,OAAO0M,UAAU,CAACkD,YAAY,KAAK,KAAK;EAC1C;EACA3Y,MAAMA,CAAC4Y,QAAa;IACrB,MAAMhE,eAAe,GAKd;MACFxC,IAAI,EAAE,IAAI;MAAE;MACZyC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IACD,IAAI,CAAC9M,YAAY,CAACsL,IAAI,CAACqF,QAAQ,EAAChE,eAAe,CAAC;IAEhD;IACA,IAAI,CAAC/M,SAAS,CAACgR,UAAU,CAAC;MACxB3X,WAAW,EAAC,IAAI,CAACzB,MAAM,CAACyB,WAAW;MACnCF,eAAe,EAAC,IAAI,CAACvB,MAAM,CAACuB,eAAe;MAC3CC,aAAa,EAAC,IAAI,CAACxB,MAAM,CAACwB;MAC1B;KACD,CAAC;EAEJ;EAEAwG,SAASA,CAAA;IACP,IAAI,CAACQ,YAAY,CAAC6Q,UAAU,EAAE;EAChC;EAGOlR,mBAAmBA,CAAA;IACxB,IAAI,CAACW,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAMwH,QAAQ,GAAG;MAChBnK,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACtBtH,WAAW,EAAE,IAAI,CAAC2G,SAAS,CAAC6B,KAAK,CAACxI,WAAW;MAC7CD,aAAa,EAAE,IAAI,CAAC4G,SAAS,CAAC6B,KAAK,CAACzI,aAAa;MACjDD,eAAe,EAAE,IAAI,CAAC6G,SAAS,CAAC6B,KAAK,CAAC1I;KAEvC;IAED,IAAI,CAACsH,cAAc,CAACyQ,mBAAmB,CAACpG,QAAQ,CAAC,CAACzI,SAAS,CAAC;MAC1DiB,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC5D,SAAS,EAAE;QAChB,IAAI,CAACc,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIE,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAI,CAACjD,wBAAwB,CAAC0K,WAAW,CAAC1H,GAAG,CAACE,YAAY,CAACyH,OAAO,EAAE,EAAE,CAAC;UACvE;UACA,IAAI,CAACpI,kBAAkB,EAAE;QAC3B,CAAC,MAAM;UACL,IAAI,CAACvC,wBAAwB,CAAC4K,SAAS,CAAC5H,GAAG,CAACO,YAAY,IAAE,qCAAqC,EAAE,EAAE,CAAC;UACpG;QACF;QACA,IAAI,CAAC1D,GAAG,CAAC2D,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACtF,eAAe,CAAC0B,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC9C,wBAAwB,CAAC4K,SAAS,CAAC,qCAAqC,EAAE,EAAE,CAAC;QAClF;QACA1I,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAAC3F,GAAG,CAAC2D,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;;qCA7tCW/D,mBAAmB,EAAA7K,EAAA,CAAA+b,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjc,EAAA,CAAA+b,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAlc,EAAA,CAAA+b,iBAAA,CAAAI,EAAA,CAAAC,QAAA,GAAApc,EAAA,CAAA+b,iBAAA,CAAA/b,EAAA,CAAAqc,iBAAA,GAAArc,EAAA,CAAA+b,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAvc,EAAA,CAAA+b,iBAAA,CAAAS,EAAA,CAAA3c,UAAA,GAAAG,EAAA,CAAA+b,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAA1c,EAAA,CAAA+b,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA5c,EAAA,CAAA+b,iBAAA,CAAAc,EAAA,CAAAC,gBAAA;EAAA;;UAAnBjS,mBAAmB;IAAAkS,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAvB,QAAA,WAAAwB,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCtBhCpd,EAAA,CAAAoF,UAAA,IAAAkY,kCAAA,iBAA0D;QAS1Dtd,EAAA,CAAAC,cAAA,aAAmC;QAEjCD,EAAA,CAAAoF,UAAA,IAAAmY,kCAAA,mBAAqD;QA+dvDvd,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAoF,UAAA,IAAAoY,0CAAA,iCAAAxd,EAAA,CAAAyd,sBAAA,CAAuD;;;QA7ejDzd,EAAA,CAAAc,UAAA,SAAAuc,GAAA,CAAA1b,SAAA,CAAe;QAWoB3B,EAAA,CAAAa,SAAA,GAAY;QAAZb,EAAA,CAAAc,UAAA,SAAAuc,GAAA,CAAA7a,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}