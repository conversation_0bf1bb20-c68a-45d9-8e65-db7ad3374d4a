{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\n// language list\nimport { locale as enLang } from './modules/i18n/vocabs/en';\nimport { locale as chLang } from './modules/i18n/vocabs/ch';\nimport { locale as esLang } from './modules/i18n/vocabs/es';\nimport { locale as jpLang } from './modules/i18n/vocabs/jp';\nimport { locale as deLang } from './modules/i18n/vocabs/de';\nimport { locale as frLang } from './modules/i18n/vocabs/fr';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./modules/i18n\";\nimport * as i2 from \"./_metronic/partials/layout/theme-mode-switcher/theme-mode.service\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = [\"root\", \"\"];\nexport class AppComponent {\n  translationService;\n  modeService;\n  router;\n  isInitialNavigation = true;\n  splashScreenHidden = false;\n  constructor(translationService, modeService, router) {\n    this.translationService = translationService;\n    this.modeService = modeService;\n    this.router = router;\n    // register translations\n    this.translationService.loadTranslations(enLang, chLang, esLang, jpLang, deLang, frLang);\n  }\n  ngOnInit() {\n    this.modeService.init();\n    // Listen to router navigation events to hide splash screen at the right time\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      if (this.isInitialNavigation) {\n        this.isInitialNavigation = false;\n        // Hide splash screen after initial navigation is complete\n        this.hideSplashScreenAfterNavigation();\n      }\n    });\n    // Fallback: hide splash screen after a maximum delay\n    setTimeout(() => {\n      if (!this.splashScreenHidden) {\n        this.hideSplashScreen();\n      }\n    }, 8000); // 8 second maximum delay\n    // Test Kendo UI setup\n    console.log('Kendo UI setup check:', {\n      kendoAvailable: typeof window !== 'undefined' && window.kendo,\n      angularVersion: '18.1.4'\n    });\n  }\n  hideSplashScreenAfterNavigation() {\n    // Wait a bit more to ensure the target page is fully rendered\n    setTimeout(() => {\n      this.hideSplashScreen();\n    }, 1500); // Increased delay to ensure login page is fully loaded\n  }\n  hideSplashScreen() {\n    if (this.splashScreenHidden) {\n      return; // Already hidden\n    }\n    this.splashScreenHidden = true;\n    // Show the body content\n    document.body.style.visibility = 'visible';\n    document.body.classList.add('app-loaded');\n    // Hide the static splash screen from index.html\n    const splashScreen = document.getElementById('splash-screen');\n    if (splashScreen) {\n      splashScreen.style.opacity = '0';\n      splashScreen.style.transition = 'opacity 0.8s ease-out';\n      setTimeout(() => {\n        splashScreen.style.display = 'none';\n      }, 800);\n    }\n  }\n  static ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.TranslationService), i0.ɵɵdirectiveInject(i2.ThemeModeService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"body\", \"root\", \"\"]],\n    attrs: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    dependencies: [i3.RouterOutlet],\n    styles: [\"[_nghost-%COMP%] {\\n  height: 100%;\\n  margin: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsWUFBQTtFQUNBLFNBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcbiAgaGVpZ2h0OiAxMDAlO1xuICBtYXJnaW46IDA7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "filter", "locale", "enLang", "chLang", "esLang", "jpLang", "deLang", "frLang", "AppComponent", "translationService", "modeService", "router", "isInitialNavigation", "splashScreenHidden", "constructor", "loadTranslations", "ngOnInit", "init", "events", "pipe", "event", "subscribe", "hideSplashScreenAfterNavigation", "setTimeout", "hideSplashScreen", "console", "log", "kendoAvailable", "window", "kendo", "angularVersion", "document", "body", "style", "visibility", "classList", "add", "splashScreen", "getElementById", "opacity", "transition", "display", "i0", "ɵɵdirectiveInject", "i1", "TranslationService", "i2", "ThemeModeService", "i3", "Router", "selectors", "attrs", "_c0", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\permittracker\\Angular\\src\\app\\app.component.ts", "D:\\permittracker\\Angular\\src\\app\\app.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';\nimport { Router, NavigationEnd, NavigationStart } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { TranslationService } from './modules/i18n';\n// language list\nimport { locale as enLang } from './modules/i18n/vocabs/en';\nimport { locale as chLang } from './modules/i18n/vocabs/ch';\nimport { locale as esLang } from './modules/i18n/vocabs/es';\nimport { locale as jpLang } from './modules/i18n/vocabs/jp';\nimport { locale as deLang } from './modules/i18n/vocabs/de';\nimport { locale as frLang } from './modules/i18n/vocabs/fr';\nimport { ThemeModeService } from './_metronic/partials/layout/theme-mode-switcher/theme-mode.service';\n\n@Component({\n  // tslint:disable-next-line:component-selector\n  // eslint-disable-next-line @angular-eslint/component-selector\n  selector: 'body[root]',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class AppComponent implements OnInit {\n  private isInitialNavigation = true;\n  private splashScreenHidden = false;\n\n  constructor(\n    private translationService: TranslationService,\n    private modeService: ThemeModeService,\n    private router: Router\n  ) {\n    // register translations\n    this.translationService.loadTranslations(\n      enLang,\n      chLang,\n      esLang,\n      jpLang,\n      deLang,\n      frLang\n    );\n  }\n\n  ngOnInit() {\n    this.modeService.init();\n    \n    // Listen to router navigation events to hide splash screen at the right time\n    this.router.events\n      .pipe(filter(event => event instanceof NavigationEnd))\n      .subscribe((event: NavigationEnd) => {\n        if (this.isInitialNavigation) {\n          this.isInitialNavigation = false;\n          // Hide splash screen after initial navigation is complete\n          this.hideSplashScreenAfterNavigation();\n        }\n      });\n\n    // Fallback: hide splash screen after a maximum delay\n    setTimeout(() => {\n      if (!this.splashScreenHidden) {\n        this.hideSplashScreen();\n      }\n    }, 8000); // 8 second maximum delay\n    \n    // Test Kendo UI setup\n    console.log('Kendo UI setup check:', {\n      kendoAvailable: typeof window !== 'undefined' && (window as any).kendo,\n      angularVersion: '18.1.4'\n    });\n  }\n\n  private hideSplashScreenAfterNavigation() {\n    // Wait a bit more to ensure the target page is fully rendered\n    setTimeout(() => {\n      this.hideSplashScreen();\n    }, 1500); // Increased delay to ensure login page is fully loaded\n  }\n\n  private hideSplashScreen() {\n    if (this.splashScreenHidden) {\n      return; // Already hidden\n    }\n    \n    this.splashScreenHidden = true;\n    \n    // Show the body content\n    document.body.style.visibility = 'visible';\n    document.body.classList.add('app-loaded');\n    \n    // Hide the static splash screen from index.html\n    const splashScreen = document.getElementById('splash-screen');\n    if (splashScreen) {\n      splashScreen.style.opacity = '0';\n      splashScreen.style.transition = 'opacity 0.8s ease-out';\n      setTimeout(() => {\n        splashScreen.style.display = 'none';\n      }, 800);\n    }\n  }\n}\n", "<router-outlet></router-outlet>\n"], "mappings": "AACA,SAAiBA,aAAa,QAAyB,iBAAiB;AACxE,SAASC,MAAM,QAAQ,gBAAgB;AAEvC;AACA,SAASC,MAAM,IAAIC,MAAM,QAAQ,0BAA0B;AAC3D,SAASD,MAAM,IAAIE,MAAM,QAAQ,0BAA0B;AAC3D,SAASF,MAAM,IAAIG,MAAM,QAAQ,0BAA0B;AAC3D,SAASH,MAAM,IAAII,MAAM,QAAQ,0BAA0B;AAC3D,SAASJ,MAAM,IAAIK,MAAM,QAAQ,0BAA0B;AAC3D,SAASL,MAAM,IAAIM,MAAM,QAAQ,0BAA0B;;;;;;AAW3D,OAAM,MAAOC,YAAY;EAKbC,kBAAA;EACAC,WAAA;EACAC,MAAA;EANFC,mBAAmB,GAAG,IAAI;EAC1BC,kBAAkB,GAAG,KAAK;EAElCC,YACUL,kBAAsC,EACtCC,WAA6B,EAC7BC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAEd;IACA,IAAI,CAACF,kBAAkB,CAACM,gBAAgB,CACtCb,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,CACP;EACH;EAEAS,QAAQA,CAAA;IACN,IAAI,CAACN,WAAW,CAACO,IAAI,EAAE;IAEvB;IACA,IAAI,CAACN,MAAM,CAACO,MAAM,CACfC,IAAI,CAACnB,MAAM,CAACoB,KAAK,IAAIA,KAAK,YAAYrB,aAAa,CAAC,CAAC,CACrDsB,SAAS,CAAED,KAAoB,IAAI;MAClC,IAAI,IAAI,CAACR,mBAAmB,EAAE;QAC5B,IAAI,CAACA,mBAAmB,GAAG,KAAK;QAChC;QACA,IAAI,CAACU,+BAA+B,EAAE;MACxC;IACF,CAAC,CAAC;IAEJ;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACV,kBAAkB,EAAE;QAC5B,IAAI,CAACW,gBAAgB,EAAE;MACzB;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAEV;IACAC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnCC,cAAc,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAKA,MAAc,CAACC,KAAK;MACtEC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEQR,+BAA+BA,CAAA;IACrC;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACX,kBAAkB,EAAE;MAC3B,OAAO,CAAC;IACV;IAEA,IAAI,CAACA,kBAAkB,GAAG,IAAI;IAE9B;IACAkB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,UAAU,GAAG,SAAS;IAC1CH,QAAQ,CAACC,IAAI,CAACG,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IAEzC;IACA,MAAMC,YAAY,GAAGN,QAAQ,CAACO,cAAc,CAAC,eAAe,CAAC;IAC7D,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACJ,KAAK,CAACM,OAAO,GAAG,GAAG;MAChCF,YAAY,CAACJ,KAAK,CAACO,UAAU,GAAG,uBAAuB;MACvDjB,UAAU,CAAC,MAAK;QACdc,YAAY,CAACJ,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrC,CAAC,EAAE,GAAG,CAAC;IACT;EACF;;qCA3EWjC,YAAY,EAAAkC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;;UAAZzC,YAAY;IAAA0C,SAAA;IAAAC,KAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrBzBf,EAAA,CAAAiB,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}