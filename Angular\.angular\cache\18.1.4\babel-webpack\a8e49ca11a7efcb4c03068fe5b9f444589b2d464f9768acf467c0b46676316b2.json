{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { RouterModule } from '@angular/router';\nimport { NgbDropdownModule, NgbProgressbarModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TranslationModule } from '../../modules/i18n';\nimport { LayoutComponent } from './layout.component';\nimport { ExtrasModule } from '../partials/layout/extras/extras.module';\nimport { Routing } from '../../pages/routing';\nimport { TopbarComponent } from './components/topbar/topbar.component';\nimport { DrawersModule, DropdownMenusModule, ModalsModule, EngagesModule } from '../partials';\nimport { ThemeModeModule } from '../partials/layout/theme-mode-switcher/theme-mode.module';\nimport { SharedModule } from \"../shared/shared.module\";\nimport { HttpUtilsService } from '../../modules/services/http-utils.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../partials/layout/extras/dropdown-inner/notifications-inner/notifications-inner.component\";\nimport * as i4 from \"../partials/layout/extras/dropdown-inner/quick-links-inner/quick-links-inner.component\";\nimport * as i5 from \"../partials/layout/extras/dropdown-inner/search-result-inner/search-result-inner.component\";\nimport * as i6 from \"../partials/layout/extras/dropdown-inner/user-inner/user-inner.component\";\nimport * as i7 from \"../partials/layout/theme-mode-switcher/theme-mode-switcher.component\";\nimport * as i8 from \"../shared/keenicon/keenicon.component\";\nconst routes = [{\n  path: '',\n  component: LayoutComponent,\n  children: Routing\n}];\nexport let LayoutModule = /*#__PURE__*/(() => {\n  class LayoutModule {\n    static ɵfac = function LayoutModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LayoutModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LayoutModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [HttpUtilsService],\n      imports: [CommonModule, RouterModule.forChild(routes), TranslationModule, InlineSVGModule, NgbDropdownModule, NgbProgressbarModule, ExtrasModule, ModalsModule, DrawersModule, EngagesModule, DropdownMenusModule, NgbTooltipModule, TranslateModule, ThemeModeModule, SharedModule, RouterModule]\n    });\n  }\n  return LayoutModule;\n})();\ni0.ɵɵsetComponentScope(TopbarComponent, function () {\n  return [i2.NgClass, i2.NgIf, i3.NotificationsInnerComponent, i4.QuickLinksInnerComponent, i5.SearchResultInnerComponent, i6.UserInnerComponent, i7.ThemeModeSwitcherComponent, i8.KeeniconComponent];\n}, []);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}