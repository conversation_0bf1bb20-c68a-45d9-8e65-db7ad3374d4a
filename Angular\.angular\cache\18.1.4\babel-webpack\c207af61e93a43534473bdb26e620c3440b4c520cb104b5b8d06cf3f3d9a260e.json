{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport * as _ from 'lodash';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class HttpUtilsService {\n  loadingSubject = new BehaviorSubject(null);\n  isDrawerOpenSubject = new BehaviorSubject(false);\n  isDrawerOpen$ = this.isDrawerOpenSubject.asObservable();\n  // Override the next method to add debugging\n  setLoadingState(loading) {\n    console.log('HttpUtilsService - setting loading state to:', loading);\n    this.loadingSubject.next(loading);\n  }\n  toggleDrawerState() {\n    this.isDrawerOpenSubject.next(!this.isDrawerOpenSubject.value);\n  }\n  getFindHTTPParams(queryParams) {\n    const params = new HttpParams().set('lastNamefilter', queryParams.filter).set('sortOrder', queryParams.sortOrder).set('sortField', queryParams.sortField).set('pageNumber', queryParams.pageNumber.toString()).set('pageSize', queryParams.pageSize.toString());\n    return params;\n  }\n  getHTTPHeaders() {\n    const result = new HttpHeaders();\n    result.set('Content-Type', 'application/json');\n    return result;\n  }\n  getHttpErrorMessages(error) {\n    let errorMessages = [];\n    if (typeof error.field !== 'undefined' && error.field.length > 0) {\n      _.forEach(error.field, function (fieldError) {\n        if (fieldError.code === 'NotEmpty') {\n          errorMessages.push(fieldError.field + ' ' + fieldError.defaultMessage);\n        } else {\n          errorMessages.push(fieldError.defaultMessage);\n        }\n      });\n      return errorMessages.toString();\n    } else {\n      if (typeof error.status !== 'undefined' && typeof error.message !== 'undefined') {\n        return error.message;\n      }\n      return error;\n    }\n  }\n  static ɵfac = function HttpUtilsService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpUtilsService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: HttpUtilsService,\n    factory: HttpUtilsService.ɵfac,\n    providedIn: 'root' // ✅ This makes it globally available\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "_", "BehaviorSubject", "HttpUtilsService", "loadingSubject", "isDrawerOpenSubject", "isDrawerOpen$", "asObservable", "setLoadingState", "loading", "console", "log", "next", "toggleDrawerState", "value", "getFindHTTPParams", "queryParams", "params", "set", "filter", "sortOrder", "sortField", "pageNumber", "toString", "pageSize", "getHTTPHeaders", "result", "getHttpErrorMessages", "error", "errorMessages", "field", "length", "for<PERSON>ach", "fieldError", "code", "push", "defaultMessage", "status", "message", "factory", "ɵfac", "providedIn"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\services\\http-utils.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\nimport {HttpHeaders, HttpParams} from '@angular/common/http';\nimport * as _ from 'lodash';\nimport {BehaviorSubject} from 'rxjs';\n\n@Injectable({\n  providedIn: 'root' // ✅ This makes it globally available\n})\nexport class HttpUtilsService {\n\n  loadingSubject: BehaviorSubject<any> = new BehaviorSubject(null);\n  isDrawerOpenSubject = new BehaviorSubject<boolean>(false);\n  isDrawerOpen$ = this.isDrawerOpenSubject.asObservable();\n\n  // Override the next method to add debugging\n  setLoadingState(loading: boolean) {\n    console.log('HttpUtilsService - setting loading state to:', loading);\n    this.loadingSubject.next(loading);\n  }\n\n  toggleDrawerState() {\n    this.isDrawerOpenSubject.next(!this.isDrawerOpenSubject.value);\n  }\n  getFindHTTPParams(queryParams:any): HttpParams {\n    const params = new HttpParams()\n      .set('lastNamefilter', queryParams.filter)\n      .set('sortOrder', queryParams.sortOrder)\n      .set('sortField', queryParams.sortField)\n      .set('pageNumber', queryParams.pageNumber.toString())\n      .set('pageSize', queryParams.pageSize.toString());\n\n    return params;\n  }\n\n  getHTTPHeaders(): HttpHeaders {\n    const result = new HttpHeaders();\n    result.set('Content-Type', 'application/json');\n    return result;\n  }\n\n  getHttpErrorMessages(error: any): any {\n    let errorMessages:any = [];\n    if (typeof error.field !== 'undefined' && error.field.length > 0) {\n      _.forEach(error.field, function(fieldError:any) {\n        if (fieldError.code === 'NotEmpty') {\n          errorMessages.push(fieldError.field + ' ' + fieldError.defaultMessage);\n        } else {\n          errorMessages.push(fieldError.defaultMessage);\n        }\n      });\n      return errorMessages.toString();\n    } else {\n      if (typeof error.status !== 'undefined' && typeof error.message !== 'undefined') {\n        return error.message;\n      }\n      return error;\n    }\n  }\n\n}\n"], "mappings": "AACA,SAAQA,WAAW,EAAEC,UAAU,QAAO,sBAAsB;AAC5D,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAC3B,SAAQC,eAAe,QAAO,MAAM;;AAKpC,OAAM,MAAOC,gBAAgB;EAE3BC,cAAc,GAAyB,IAAIF,eAAe,CAAC,IAAI,CAAC;EAChEG,mBAAmB,GAAG,IAAIH,eAAe,CAAU,KAAK,CAAC;EACzDI,aAAa,GAAG,IAAI,CAACD,mBAAmB,CAACE,YAAY,EAAE;EAEvD;EACAC,eAAeA,CAACC,OAAgB;IAC9BC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEF,OAAO,CAAC;IACpE,IAAI,CAACL,cAAc,CAACQ,IAAI,CAACH,OAAO,CAAC;EACnC;EAEAI,iBAAiBA,CAAA;IACf,IAAI,CAACR,mBAAmB,CAACO,IAAI,CAAC,CAAC,IAAI,CAACP,mBAAmB,CAACS,KAAK,CAAC;EAChE;EACAC,iBAAiBA,CAACC,WAAe;IAC/B,MAAMC,MAAM,GAAG,IAAIjB,UAAU,EAAE,CAC5BkB,GAAG,CAAC,gBAAgB,EAAEF,WAAW,CAACG,MAAM,CAAC,CACzCD,GAAG,CAAC,WAAW,EAAEF,WAAW,CAACI,SAAS,CAAC,CACvCF,GAAG,CAAC,WAAW,EAAEF,WAAW,CAACK,SAAS,CAAC,CACvCH,GAAG,CAAC,YAAY,EAAEF,WAAW,CAACM,UAAU,CAACC,QAAQ,EAAE,CAAC,CACpDL,GAAG,CAAC,UAAU,EAAEF,WAAW,CAACQ,QAAQ,CAACD,QAAQ,EAAE,CAAC;IAEnD,OAAON,MAAM;EACf;EAEAQ,cAAcA,CAAA;IACZ,MAAMC,MAAM,GAAG,IAAI3B,WAAW,EAAE;IAChC2B,MAAM,CAACR,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAC9C,OAAOQ,MAAM;EACf;EAEAC,oBAAoBA,CAACC,KAAU;IAC7B,IAAIC,aAAa,GAAO,EAAE;IAC1B,IAAI,OAAOD,KAAK,CAACE,KAAK,KAAK,WAAW,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAChE9B,CAAC,CAAC+B,OAAO,CAACJ,KAAK,CAACE,KAAK,EAAE,UAASG,UAAc;QAC5C,IAAIA,UAAU,CAACC,IAAI,KAAK,UAAU,EAAE;UAClCL,aAAa,CAACM,IAAI,CAACF,UAAU,CAACH,KAAK,GAAG,GAAG,GAAGG,UAAU,CAACG,cAAc,CAAC;QACxE,CAAC,MAAM;UACLP,aAAa,CAACM,IAAI,CAACF,UAAU,CAACG,cAAc,CAAC;QAC/C;MACF,CAAC,CAAC;MACF,OAAOP,aAAa,CAACN,QAAQ,EAAE;IACjC,CAAC,MAAM;MACL,IAAI,OAAOK,KAAK,CAACS,MAAM,KAAK,WAAW,IAAI,OAAOT,KAAK,CAACU,OAAO,KAAK,WAAW,EAAE;QAC/E,OAAOV,KAAK,CAACU,OAAO;MACtB;MACA,OAAOV,KAAK;IACd;EACF;;qCAjDWzB,gBAAgB;EAAA;;WAAhBA,gBAAgB;IAAAoC,OAAA,EAAhBpC,gBAAgB,CAAAqC,IAAA;IAAAC,UAAA,EAFf,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}