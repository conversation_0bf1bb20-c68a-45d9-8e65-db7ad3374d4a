import { Component, OnDestroy, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { HttpUtilsService } from '../services/http-utils.service';
import { Subscription } from 'rxjs';

// const BODY_CLASSES = ['bgi-size-cover', 'bgi-position-center', 'bgi-no-repeat'];

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: '<body[root]>',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss'],
})
export class AuthComponent implements OnInit, OnDestroy {
  today: Date = new Date();
  isLoading: boolean = false;
  private loadingSubscription: Subscription = new Subscription();

  constructor(
    private titleService: Title,
    private httpUtilService: HttpUtilsService
  ) {}

  ngOnInit(): void {
    // BODY_CLASSES.forEach((c) => document.body.classList.add(c));
    // Default title for auth wrapper; individual pages can override
    this.titleService.setTitle('Permit Tracker');
    
    // Subscribe to loading state changes
    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(
      (loading) => {
        this.isLoading = loading === true;
      }
    );
  }

  ngOnDestroy() {
    // BODY_CLASSES.forEach((c) => document.body.classList.remove(c));
    this.loadingSubscription.unsubscribe();
  }
}
