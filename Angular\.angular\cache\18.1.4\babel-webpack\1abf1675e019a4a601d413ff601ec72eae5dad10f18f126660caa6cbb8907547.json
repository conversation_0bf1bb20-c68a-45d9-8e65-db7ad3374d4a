{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nlet LayoutComponent = class LayoutComponent {\n  initService;\n  layout;\n  router;\n  activatedRoute;\n  httpUtilService;\n  unsubscribe = [];\n  // Loading state\n  isLoading = false;\n  // Public variables\n  // page\n  pageContainerCSSClasses;\n  // header\n  appHeaderDefaultClass = '';\n  appHeaderDisplay;\n  appHeaderDefaultStickyEnabled;\n  appHeaderDefaultStickyAttributes = {};\n  appHeaderDefaultMinimizeEnabled;\n  appHeaderDefaultMinimizeAttributes = {};\n  // toolbar\n  appToolbarDisplay;\n  appToolbarLayout;\n  appToolbarCSSClass = '';\n  appToolbarSwapEnabled;\n  appToolbarSwapAttributes = {};\n  appToolbarStickyEnabled;\n  appToolbarStickyAttributes = {};\n  appToolbarMinimizeEnabled;\n  appToolbarMinimizeAttributes = {};\n  // content\n  appContentContiner;\n  appContentContainerClass;\n  contentCSSClasses;\n  contentContainerCSSClass;\n  // sidebar\n  appSidebarDefaultClass;\n  appSidebarDefaultDrawerEnabled;\n  appSidebarDefaultDrawerAttributes = {};\n  appSidebarDisplay;\n  appSidebarDefaultStickyEnabled;\n  appSidebarDefaultStickyAttributes = {};\n  ktSidebar;\n  /// sidebar panel\n  appSidebarPanelDisplay;\n  // footer\n  appFooterDisplay;\n  appFooterCSSClass = '';\n  appFooterContainer = '';\n  appFooterContainerCSSClass = '';\n  appFooterFixedDesktop;\n  appFooterFixedMobile;\n  // scrolltop\n  scrolltopDisplay;\n  ktAside;\n  ktHeaderMobile;\n  ktHeader;\n  constructor(initService, layout, router, activatedRoute, httpUtilService) {\n    this.initService = initService;\n    this.layout = layout;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.httpUtilService = httpUtilService;\n    // define layout type and load layout\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        const currentLayoutType = this.layout.currentLayoutTypeSubject.value;\n        const nextLayoutType = this.activatedRoute?.firstChild?.snapshot.data.layout || this.layout.getBaseLayoutTypeFromLocalStorage();\n        if (currentLayoutType !== nextLayoutType || !currentLayoutType) {\n          this.layout.currentLayoutTypeSubject.next(nextLayoutType);\n          this.initService.reInitProps(nextLayoutType);\n        }\n      }\n    });\n  }\n  ngOnInit() {\n    const subscr = this.layout.layoutConfigSubject.asObservable().subscribe(config => {\n      this.updateProps(config);\n    });\n    this.unsubscribe.push(subscr);\n    // Subscribe to loading state changes\n    const loadingSubscr = this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading === true;\n    });\n    this.unsubscribe.push(loadingSubscr);\n  }\n  updateProps(config) {\n    this.scrolltopDisplay = this.layout.getProp('scrolltop.display', config);\n    this.pageContainerCSSClasses = this.layout.getStringCSSClasses('pageContainer');\n    this.appHeaderDefaultClass = this.layout.getProp('app.header.default.class', config);\n    this.appHeaderDisplay = this.layout.getProp('app.header.display', config);\n    this.appFooterDisplay = this.layout.getProp('app.footer.display', config);\n    this.appSidebarDisplay = this.layout.getProp('app.sidebar.display', config);\n    this.appSidebarPanelDisplay = this.layout.getProp('app.sidebar-panel.display', config);\n    this.appToolbarDisplay = this.layout.getProp('app.toolbar.display', config);\n    this.contentCSSClasses = this.layout.getStringCSSClasses('content');\n    this.contentContainerCSSClass = this.layout.getStringCSSClasses('contentContainer');\n    this.appContentContiner = this.layout.getProp('app.content.container', config);\n    this.appContentContainerClass = this.layout.getProp('app.content.containerClass', config);\n    // footer\n    if (this.appFooterDisplay) {\n      this.updateFooter(config);\n    }\n    // sidebar\n    if (this.appSidebarDisplay) {\n      this.updateSidebar(config);\n    }\n    // header\n    if (this.appHeaderDisplay) {\n      this.updateHeader(config);\n    }\n    // toolbar\n    if (this.appToolbarDisplay) {\n      this.updateToolbar(config);\n    }\n  }\n  updateSidebar(config) {\n    this.appSidebarDefaultClass = this.layout.getProp('app.sidebar.default.class', config);\n    this.appSidebarDefaultDrawerEnabled = this.layout.getProp('app.sidebar.default.drawer.enabled', config);\n    if (this.appSidebarDefaultDrawerEnabled) {\n      this.appSidebarDefaultDrawerAttributes = this.layout.getProp('app.sidebar.default.drawer.attributes', config);\n    }\n    this.appSidebarDefaultStickyEnabled = this.layout.getProp('app.sidebar.default.sticky.enabled', config);\n    if (this.appSidebarDefaultStickyEnabled) {\n      this.appSidebarDefaultStickyAttributes = this.layout.getProp('app.sidebar.default.sticky.attributes', config);\n    }\n    setTimeout(() => {\n      const sidebarElement = document.getElementById('kt_app_sidebar');\n      // sidebar\n      if (this.appSidebarDisplay && sidebarElement) {\n        const sidebarAttributes = sidebarElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        sidebarAttributes.forEach(attr => sidebarElement.removeAttribute(attr));\n        if (this.appSidebarDefaultDrawerEnabled) {\n          for (const key in this.appSidebarDefaultDrawerAttributes) {\n            if (this.appSidebarDefaultDrawerAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(key, this.appSidebarDefaultDrawerAttributes[key]);\n            }\n          }\n        }\n        if (this.appSidebarDefaultStickyEnabled) {\n          for (const key in this.appSidebarDefaultStickyAttributes) {\n            if (this.appSidebarDefaultStickyAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(key, this.appSidebarDefaultStickyAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  updateHeader(config) {\n    this.appHeaderDefaultStickyEnabled = this.layout.getProp('app.header.default.sticky.enabled', config);\n    if (this.appHeaderDefaultStickyEnabled) {\n      this.appHeaderDefaultStickyAttributes = this.layout.getProp('app.header.default.sticky.attributes', config);\n    }\n    this.appHeaderDefaultMinimizeEnabled = this.layout.getProp('app.header.default.minimize.enabled', config);\n    if (this.appHeaderDefaultMinimizeEnabled) {\n      this.appHeaderDefaultMinimizeAttributes = this.layout.getProp('app.header.default.minimize.attributes', config);\n    }\n    setTimeout(() => {\n      const headerElement = document.getElementById('kt_app_header');\n      // header\n      if (this.appHeaderDisplay && headerElement) {\n        const headerAttributes = headerElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        headerAttributes.forEach(attr => headerElement.removeAttribute(attr));\n        if (this.appHeaderDefaultStickyEnabled) {\n          for (const key in this.appHeaderDefaultStickyAttributes) {\n            if (this.appHeaderDefaultStickyAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(key, this.appHeaderDefaultStickyAttributes[key]);\n            }\n          }\n        }\n        if (this.appHeaderDefaultMinimizeEnabled) {\n          for (const key in this.appHeaderDefaultMinimizeAttributes) {\n            if (this.appHeaderDefaultMinimizeAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(key, this.appHeaderDefaultMinimizeAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  updateFooter(config) {\n    this.appFooterCSSClass = this.layout.getProp('app.footer.class', config);\n    this.appFooterContainer = this.layout.getProp('app.footer.container', config);\n    this.appFooterContainerCSSClass = this.layout.getProp('app.footer.containerClass', config);\n    if (this.appFooterContainer === 'fixed') {\n      this.appFooterContainerCSSClass += ' container-xxl';\n    } else {\n      if (this.appFooterContainer === 'fluid') {\n        this.appFooterContainerCSSClass += ' container-fluid';\n      }\n    }\n    this.appFooterFixedDesktop = this.layout.getProp('app.footer.fixed.desktop', config);\n    if (this.appFooterFixedDesktop) {\n      document.body.setAttribute('data-kt-app-footer-fixed', 'true');\n    }\n    this.appFooterFixedMobile = this.layout.getProp('app.footer.fixed.mobile');\n    if (this.appFooterFixedMobile) {\n      document.body.setAttribute('data-kt-app-footer-fixed-mobile', 'true');\n    }\n  }\n  updateToolbar(config) {\n    this.appToolbarLayout = this.layout.getProp('app.toolbar.layout', config);\n    this.appToolbarSwapEnabled = this.layout.getProp('app.toolbar.swap.enabled', config);\n    if (this.appToolbarSwapEnabled) {\n      this.appToolbarSwapAttributes = this.layout.getProp('app.toolbar.swap.attributes', config);\n    }\n    this.appToolbarStickyEnabled = this.layout.getProp('app.toolbar.sticky.enabled', config);\n    if (this.appToolbarStickyEnabled) {\n      this.appToolbarStickyAttributes = this.layout.getProp('app.toolbar.sticky.attributes', config);\n    }\n    this.appToolbarCSSClass = this.layout.getProp('app.toolbar.class', config) || '';\n    this.appToolbarMinimizeEnabled = this.layout.getProp('app.toolbar.minimize.enabled', config);\n    if (this.appToolbarMinimizeEnabled) {\n      this.appToolbarMinimizeAttributes = this.layout.getProp('app.toolbar.minimize.attributes', config);\n      this.appToolbarCSSClass += ' app-toolbar-minimize';\n    }\n    setTimeout(() => {\n      const toolbarElement = document.getElementById('kt_app_toolbar');\n      // toolbar\n      if (this.appToolbarDisplay && toolbarElement) {\n        const toolbarAttributes = toolbarElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        toolbarAttributes.forEach(attr => toolbarElement.removeAttribute(attr));\n        if (this.appToolbarSwapEnabled) {\n          for (const key in this.appToolbarSwapAttributes) {\n            if (this.appToolbarSwapAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarSwapAttributes[key]);\n            }\n          }\n        }\n        if (this.appToolbarStickyEnabled) {\n          for (const key in this.appToolbarStickyAttributes) {\n            if (this.appToolbarStickyAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarStickyAttributes[key]);\n            }\n          }\n        }\n        if (this.appToolbarMinimizeEnabled) {\n          for (const key in this.appToolbarMinimizeAttributes) {\n            if (this.appToolbarMinimizeAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarMinimizeAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n};\n__decorate([ViewChild('ktSidebar', {\n  static: true\n})], LayoutComponent.prototype, \"ktSidebar\", void 0);\n__decorate([ViewChild('ktAside', {\n  static: true\n})], LayoutComponent.prototype, \"ktAside\", void 0);\n__decorate([ViewChild('ktHeaderMobile', {\n  static: true\n})], LayoutComponent.prototype, \"ktHeaderMobile\", void 0);\n__decorate([ViewChild('ktHeader', {\n  static: true\n})], LayoutComponent.prototype, \"ktHeader\", void 0);\nLayoutComponent = __decorate([Component({\n  selector: 'app-layout',\n  templateUrl: './layout.component.html',\n  styleUrls: ['./layout.component.scss']\n})], LayoutComponent);\nexport { LayoutComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "NavigationEnd", "LayoutComponent", "initService", "layout", "router", "activatedRoute", "httpUtilService", "unsubscribe", "isLoading", "pageContainerCSSClasses", "appHeaderDefaultClass", "appHeaderDisplay", "appHeaderDefaultStickyEnabled", "appHeaderDefaultStickyAttributes", "appHeaderDefaultMinimizeEnabled", "appHeaderDefaultMinimizeAttributes", "appToolbarDisplay", "appToolbarLayout", "appToolbarCSSClass", "appToolbarSwapEnabled", "appToolbarSwapAttributes", "appToolbarStickyEnabled", "appToolbarStickyAttributes", "appToolbarMinimizeEnabled", "appToolbarMinimizeAttributes", "appContentContiner", "appContentContainerClass", "contentCSSClasses", "contentContainerCSSClass", "appSidebarDefaultClass", "appSidebarDefaultDrawerEnabled", "appSidebarDefaultDrawerAttributes", "appSidebarDisplay", "appSidebarDefaultStickyEnabled", "appSidebarDefaultStickyAttributes", "ktSidebar", "appSidebarPanelDisplay", "appFooterDisplay", "appFooterCSSClass", "appFooter<PERSON><PERSON><PERSON>", "appFooterContainerCSSClass", "appFooterFixedDesktop", "appFooterFixedMobile", "scrolltopDisplay", "ktAside", "ktHeaderMobile", "ktHeader", "constructor", "events", "subscribe", "event", "currentLayoutType", "currentLayoutTypeSubject", "value", "nextLayoutType", "<PERSON><PERSON><PERSON><PERSON>", "snapshot", "data", "getBaseLayoutTypeFromLocalStorage", "next", "reInitProps", "ngOnInit", "subscr", "layoutConfigSubject", "asObservable", "config", "updateProps", "push", "loadingSubscr", "loadingSubject", "loading", "getProp", "getStringCSSClasses", "updateFooter", "updateSidebar", "updateHeader", "updateToolbar", "setTimeout", "sidebarElement", "document", "getElementById", "sidebarAttributes", "getAttributeNames", "filter", "t", "indexOf", "for<PERSON>ach", "attr", "removeAttribute", "key", "hasOwnProperty", "setAttribute", "headerElement", "headerAttributes", "body", "toolbarElement", "toolbarAttributes", "ngOnDestroy", "sb", "__decorate", "static", "selector", "templateUrl", "styleUrls"], "sources": ["D:\\permittracker\\Angular\\src\\app\\_metronic\\layout\\layout.component.ts"], "sourcesContent": ["import {\n  Component,\n  OnInit,\n  ViewChild,\n  Element<PERSON>ef,\n  On<PERSON><PERSON>roy,\n} from '@angular/core';\nimport { ActivatedRoute, NavigationEnd, Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { LayoutService } from './core/layout.service';\nimport { LayoutInitService } from './core/layout-init.service';\nimport { ILayout, LayoutType } from './core/configs/config';\nimport { HttpUtilsService } from '../modules/services/http-utils.service';\n\n@Component({\n  selector: 'app-layout',\n  templateUrl: './layout.component.html',\n  styleUrls: ['./layout.component.scss'],\n})\nexport class LayoutComponent implements OnInit, OnDestroy {\n  private unsubscribe: Subscription[] = [];\n\n  // Loading state\n  isLoading: boolean = false;\n\n  // Public variables\n  // page\n  pageContainerCSSClasses: string;\n  // header\n  appHeaderDefaultClass: string = '';\n  appHeaderDisplay: boolean;\n  appHeaderDefaultStickyEnabled: boolean;\n  appHeaderDefaultStickyAttributes: { [attrName: string]: string } = {};\n  appHeaderDefaultMinimizeEnabled: boolean;\n  appHeaderDefaultMinimizeAttributes: { [attrName: string]: string } = {};\n  // toolbar\n  appToolbarDisplay: boolean;\n  appToolbarLayout: 'classic' | 'accounting' | 'extended' | 'reports' | 'saas';\n  appToolbarCSSClass: string = '';\n  appToolbarSwapEnabled: boolean;\n  appToolbarSwapAttributes: { [attrName: string]: string } = {};\n  appToolbarStickyEnabled: boolean;\n  appToolbarStickyAttributes: { [attrName: string]: string } = {};\n  appToolbarMinimizeEnabled: boolean;\n  appToolbarMinimizeAttributes: { [attrName: string]: string } = {};\n\n  // content\n  appContentContiner?: 'fixed' | 'fluid';\n  appContentContainerClass: string;\n  contentCSSClasses: string;\n  contentContainerCSSClass: string;\n  // sidebar\n  appSidebarDefaultClass: string;\n  appSidebarDefaultDrawerEnabled: boolean;\n  appSidebarDefaultDrawerAttributes: { [attrName: string]: string } = {};\n  appSidebarDisplay: boolean;\n  appSidebarDefaultStickyEnabled: boolean;\n  appSidebarDefaultStickyAttributes: { [attrName: string]: string } = {};\n  @ViewChild('ktSidebar', { static: true }) ktSidebar: ElementRef;\n  /// sidebar panel\n  appSidebarPanelDisplay: boolean;\n  // footer\n  appFooterDisplay: boolean;\n  appFooterCSSClass: string = '';\n  appFooterContainer: string = '';\n  appFooterContainerCSSClass: string = '';\n  appFooterFixedDesktop: boolean;\n  appFooterFixedMobile: boolean;\n\n  // scrolltop\n  scrolltopDisplay: boolean;\n\n  @ViewChild('ktAside', { static: true }) ktAside: ElementRef;\n  @ViewChild('ktHeaderMobile', { static: true }) ktHeaderMobile: ElementRef;\n  @ViewChild('ktHeader', { static: true }) ktHeader: ElementRef;\n\n  constructor(\n    private initService: LayoutInitService,\n    private layout: LayoutService,\n    private router: Router,\n    private activatedRoute: ActivatedRoute,\n    private httpUtilService: HttpUtilsService\n  ) {\n    // define layout type and load layout\n    this.router.events.subscribe((event) => {\n      if (event instanceof NavigationEnd) {\n        const currentLayoutType = this.layout.currentLayoutTypeSubject.value;\n\n        const nextLayoutType: LayoutType =\n          this.activatedRoute?.firstChild?.snapshot.data.layout ||\n          this.layout.getBaseLayoutTypeFromLocalStorage();\n\n        if (currentLayoutType !== nextLayoutType || !currentLayoutType) {\n          this.layout.currentLayoutTypeSubject.next(nextLayoutType);\n          this.initService.reInitProps(nextLayoutType);\n        }\n      }\n    });\n  }\n\n  ngOnInit() {\n    const subscr = this.layout.layoutConfigSubject\n      .asObservable()\n      .subscribe((config) => {\n        this.updateProps(config);\n      });\n    this.unsubscribe.push(subscr);\n\n    // Subscribe to loading state changes\n    const loadingSubscr = this.httpUtilService.loadingSubject.subscribe(\n      (loading) => {\n        this.isLoading = loading === true;\n      }\n    );\n    this.unsubscribe.push(loadingSubscr);\n  }\n\n  updateProps(config: ILayout) {\n    this.scrolltopDisplay = this.layout.getProp(\n      'scrolltop.display',\n      config\n    ) as boolean;\n    this.pageContainerCSSClasses =\n      this.layout.getStringCSSClasses('pageContainer');\n    this.appHeaderDefaultClass = this.layout.getProp(\n      'app.header.default.class',\n      config\n    ) as string;\n    this.appHeaderDisplay = this.layout.getProp(\n      'app.header.display',\n      config\n    ) as boolean;\n    this.appFooterDisplay = this.layout.getProp(\n      'app.footer.display',\n      config\n    ) as boolean;\n    this.appSidebarDisplay = this.layout.getProp(\n      'app.sidebar.display',\n      config\n    ) as boolean;\n    this.appSidebarPanelDisplay = this.layout.getProp(\n      'app.sidebar-panel.display',\n      config\n    ) as boolean;\n    this.appToolbarDisplay = this.layout.getProp(\n      'app.toolbar.display',\n      config\n    ) as boolean;\n    this.contentCSSClasses = this.layout.getStringCSSClasses('content');\n    this.contentContainerCSSClass =\n      this.layout.getStringCSSClasses('contentContainer');\n    this.appContentContiner = this.layout.getProp(\n      'app.content.container',\n      config\n    ) as 'fixed' | 'fluid';\n    this.appContentContainerClass = this.layout.getProp(\n      'app.content.containerClass',\n      config\n    ) as string;\n    // footer\n    if (this.appFooterDisplay) {\n      this.updateFooter(config);\n    }\n    // sidebar\n    if (this.appSidebarDisplay) {\n      this.updateSidebar(config);\n    }\n    // header\n    if (this.appHeaderDisplay) {\n      this.updateHeader(config);\n    }\n    // toolbar\n    if (this.appToolbarDisplay) {\n      this.updateToolbar(config);\n    }\n  }\n\n  updateSidebar(config: ILayout) {\n    this.appSidebarDefaultClass = this.layout.getProp(\n      'app.sidebar.default.class',\n      config\n    ) as string;\n\n    this.appSidebarDefaultDrawerEnabled = this.layout.getProp(\n      'app.sidebar.default.drawer.enabled',\n      config\n    ) as boolean;\n    if (this.appSidebarDefaultDrawerEnabled) {\n      this.appSidebarDefaultDrawerAttributes = this.layout.getProp(\n        'app.sidebar.default.drawer.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    this.appSidebarDefaultStickyEnabled = this.layout.getProp(\n      'app.sidebar.default.sticky.enabled',\n      config\n    ) as boolean;\n    if (this.appSidebarDefaultStickyEnabled) {\n      this.appSidebarDefaultStickyAttributes = this.layout.getProp(\n        'app.sidebar.default.sticky.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    setTimeout(() => {\n      const sidebarElement = document.getElementById('kt_app_sidebar');\n      // sidebar\n      if (this.appSidebarDisplay && sidebarElement) {\n        const sidebarAttributes = sidebarElement\n          .getAttributeNames()\n          .filter((t) => t.indexOf('data-') > -1);\n        sidebarAttributes.forEach((attr) =>\n          sidebarElement.removeAttribute(attr)\n        );\n\n        if (this.appSidebarDefaultDrawerEnabled) {\n          for (const key in this.appSidebarDefaultDrawerAttributes) {\n            if (this.appSidebarDefaultDrawerAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(\n                key,\n                this.appSidebarDefaultDrawerAttributes[key]\n              );\n            }\n          }\n        }\n\n        if (this.appSidebarDefaultStickyEnabled) {\n          for (const key in this.appSidebarDefaultStickyAttributes) {\n            if (this.appSidebarDefaultStickyAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(\n                key,\n                this.appSidebarDefaultStickyAttributes[key]\n              );\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n\n  updateHeader(config: ILayout) {\n    this.appHeaderDefaultStickyEnabled = this.layout.getProp(\n      'app.header.default.sticky.enabled',\n      config\n    ) as boolean;\n    if (this.appHeaderDefaultStickyEnabled) {\n      this.appHeaderDefaultStickyAttributes = this.layout.getProp(\n        'app.header.default.sticky.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    this.appHeaderDefaultMinimizeEnabled = this.layout.getProp(\n      'app.header.default.minimize.enabled',\n      config\n    ) as boolean;\n    if (this.appHeaderDefaultMinimizeEnabled) {\n      this.appHeaderDefaultMinimizeAttributes = this.layout.getProp(\n        'app.header.default.minimize.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    setTimeout(() => {\n      const headerElement = document.getElementById('kt_app_header');\n      // header\n      if (this.appHeaderDisplay && headerElement) {\n        const headerAttributes = headerElement\n          .getAttributeNames()\n          .filter((t) => t.indexOf('data-') > -1);\n        headerAttributes.forEach((attr) => headerElement.removeAttribute(attr));\n\n        if (this.appHeaderDefaultStickyEnabled) {\n          for (const key in this.appHeaderDefaultStickyAttributes) {\n            if (this.appHeaderDefaultStickyAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(\n                key,\n                this.appHeaderDefaultStickyAttributes[key]\n              );\n            }\n          }\n        }\n\n        if (this.appHeaderDefaultMinimizeEnabled) {\n          for (const key in this.appHeaderDefaultMinimizeAttributes) {\n            if (this.appHeaderDefaultMinimizeAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(\n                key,\n                this.appHeaderDefaultMinimizeAttributes[key]\n              );\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n\n  updateFooter(config: ILayout) {\n    this.appFooterCSSClass = this.layout.getProp('app.footer.class', config) as string;\n    this.appFooterContainer = this.layout.getProp('app.footer.container', config) as string;\n    this.appFooterContainerCSSClass = this.layout.getProp('app.footer.containerClass', config) as string;\n    if (this.appFooterContainer === 'fixed') {\n      this.appFooterContainerCSSClass += ' container-xxl';\n    } else {\n      if (this.appFooterContainer === 'fluid') {\n        this.appFooterContainerCSSClass += ' container-fluid';\n      }\n    }\n\n    this.appFooterFixedDesktop = this.layout.getProp('app.footer.fixed.desktop', config) as boolean;\n    if (this.appFooterFixedDesktop) {\n      document.body.setAttribute('data-kt-app-footer-fixed', 'true')\n    }\n\n    this.appFooterFixedMobile = this.layout.getProp('app.footer.fixed.mobile') as boolean;\n    if (this.appFooterFixedMobile) {\n      document.body.setAttribute('data-kt-app-footer-fixed-mobile', 'true')\n    }\n  }\n\n  updateToolbar(config: ILayout) {\n    this.appToolbarLayout = this.layout.getProp(\n      'app.toolbar.layout',\n      config\n    ) as 'classic' | 'accounting' | 'extended' | 'reports' | 'saas';\n    this.appToolbarSwapEnabled = this.layout.getProp(\n      'app.toolbar.swap.enabled',\n      config\n    ) as boolean;\n    if (this.appToolbarSwapEnabled) {\n      this.appToolbarSwapAttributes = this.layout.getProp(\n        'app.toolbar.swap.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    this.appToolbarStickyEnabled = this.layout.getProp(\n      'app.toolbar.sticky.enabled',\n      config\n    ) as boolean;\n    if (this.appToolbarStickyEnabled) {\n      this.appToolbarStickyAttributes = this.layout.getProp(\n        'app.toolbar.sticky.attributes',\n        config\n      ) as { [attrName: string]: string };\n    }\n\n    this.appToolbarCSSClass =\n      (this.layout.getProp('app.toolbar.class', config) as string) || '';\n    this.appToolbarMinimizeEnabled = this.layout.getProp(\n      'app.toolbar.minimize.enabled',\n      config\n    ) as boolean;\n    if (this.appToolbarMinimizeEnabled) {\n      this.appToolbarMinimizeAttributes = this.layout.getProp(\n        'app.toolbar.minimize.attributes',\n        config\n      ) as { [attrName: string]: string };\n      this.appToolbarCSSClass += ' app-toolbar-minimize';\n    }\n\n    setTimeout(() => {\n      const toolbarElement = document.getElementById('kt_app_toolbar');\n      // toolbar\n      if (this.appToolbarDisplay && toolbarElement) {\n        const toolbarAttributes = toolbarElement\n          .getAttributeNames()\n          .filter((t) => t.indexOf('data-') > -1);\n        toolbarAttributes.forEach((attr) =>\n          toolbarElement.removeAttribute(attr)\n        );\n\n        if (this.appToolbarSwapEnabled) {\n          for (const key in this.appToolbarSwapAttributes) {\n            if (this.appToolbarSwapAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(\n                key,\n                this.appToolbarSwapAttributes[key]\n              );\n            }\n          }\n        }\n\n        if (this.appToolbarStickyEnabled) {\n          for (const key in this.appToolbarStickyAttributes) {\n            if (this.appToolbarStickyAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(\n                key,\n                this.appToolbarStickyAttributes[key]\n              );\n            }\n          }\n        }\n\n        if (this.appToolbarMinimizeEnabled) {\n          for (const key in this.appToolbarMinimizeAttributes) {\n            if (this.appToolbarMinimizeAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(\n                key,\n                this.appToolbarMinimizeAttributes[key]\n              );\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe());\n  }\n}\n"], "mappings": ";AAAA,SACEA,SAAS,EAETC,SAAS,QAGJ,eAAe;AACtB,SAAyBC,aAAa,QAAgB,iBAAiB;AAYhE,IAAMC,eAAe,GAArB,MAAMA,eAAe;EA0DhBC,WAAA;EACAC,MAAA;EACAC,MAAA;EACAC,cAAA;EACAC,eAAA;EA7DFC,WAAW,GAAmB,EAAE;EAExC;EACAC,SAAS,GAAY,KAAK;EAE1B;EACA;EACAC,uBAAuB;EACvB;EACAC,qBAAqB,GAAW,EAAE;EAClCC,gBAAgB;EAChBC,6BAA6B;EAC7BC,gCAAgC,GAAmC,EAAE;EACrEC,+BAA+B;EAC/BC,kCAAkC,GAAmC,EAAE;EACvE;EACAC,iBAAiB;EACjBC,gBAAgB;EAChBC,kBAAkB,GAAW,EAAE;EAC/BC,qBAAqB;EACrBC,wBAAwB,GAAmC,EAAE;EAC7DC,uBAAuB;EACvBC,0BAA0B,GAAmC,EAAE;EAC/DC,yBAAyB;EACzBC,4BAA4B,GAAmC,EAAE;EAEjE;EACAC,kBAAkB;EAClBC,wBAAwB;EACxBC,iBAAiB;EACjBC,wBAAwB;EACxB;EACAC,sBAAsB;EACtBC,8BAA8B;EAC9BC,iCAAiC,GAAmC,EAAE;EACtEC,iBAAiB;EACjBC,8BAA8B;EAC9BC,iCAAiC,GAAmC,EAAE;EAC5BC,SAAS;EACnD;EACAC,sBAAsB;EACtB;EACAC,gBAAgB;EAChBC,iBAAiB,GAAW,EAAE;EAC9BC,kBAAkB,GAAW,EAAE;EAC/BC,0BAA0B,GAAW,EAAE;EACvCC,qBAAqB;EACrBC,oBAAoB;EAEpB;EACAC,gBAAgB;EAEwBC,OAAO;EACAC,cAAc;EACpBC,QAAQ;EAEjDC,YACU7C,WAA8B,EAC9BC,MAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,eAAiC;IAJjC,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAEvB;IACA,IAAI,CAACF,MAAM,CAAC4C,MAAM,CAACC,SAAS,CAAEC,KAAK,IAAI;MACrC,IAAIA,KAAK,YAAYlD,aAAa,EAAE;QAClC,MAAMmD,iBAAiB,GAAG,IAAI,CAAChD,MAAM,CAACiD,wBAAwB,CAACC,KAAK;QAEpE,MAAMC,cAAc,GAClB,IAAI,CAACjD,cAAc,EAAEkD,UAAU,EAAEC,QAAQ,CAACC,IAAI,CAACtD,MAAM,IACrD,IAAI,CAACA,MAAM,CAACuD,iCAAiC,EAAE;QAEjD,IAAIP,iBAAiB,KAAKG,cAAc,IAAI,CAACH,iBAAiB,EAAE;UAC9D,IAAI,CAAChD,MAAM,CAACiD,wBAAwB,CAACO,IAAI,CAACL,cAAc,CAAC;UACzD,IAAI,CAACpD,WAAW,CAAC0D,WAAW,CAACN,cAAc,CAAC;QAC9C;MACF;IACF,CAAC,CAAC;EACJ;EAEAO,QAAQA,CAAA;IACN,MAAMC,MAAM,GAAG,IAAI,CAAC3D,MAAM,CAAC4D,mBAAmB,CAC3CC,YAAY,EAAE,CACdf,SAAS,CAAEgB,MAAM,IAAI;MACpB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IAC1B,CAAC,CAAC;IACJ,IAAI,CAAC1D,WAAW,CAAC4D,IAAI,CAACL,MAAM,CAAC;IAE7B;IACA,MAAMM,aAAa,GAAG,IAAI,CAAC9D,eAAe,CAAC+D,cAAc,CAACpB,SAAS,CAChEqB,OAAO,IAAI;MACV,IAAI,CAAC9D,SAAS,GAAG8D,OAAO,KAAK,IAAI;IACnC,CAAC,CACF;IACD,IAAI,CAAC/D,WAAW,CAAC4D,IAAI,CAACC,aAAa,CAAC;EACtC;EAEAF,WAAWA,CAACD,MAAe;IACzB,IAAI,CAACtB,gBAAgB,GAAG,IAAI,CAACxC,MAAM,CAACoE,OAAO,CACzC,mBAAmB,EACnBN,MAAM,CACI;IACZ,IAAI,CAACxD,uBAAuB,GAC1B,IAAI,CAACN,MAAM,CAACqE,mBAAmB,CAAC,eAAe,CAAC;IAClD,IAAI,CAAC9D,qBAAqB,GAAG,IAAI,CAACP,MAAM,CAACoE,OAAO,CAC9C,0BAA0B,EAC1BN,MAAM,CACG;IACX,IAAI,CAACtD,gBAAgB,GAAG,IAAI,CAACR,MAAM,CAACoE,OAAO,CACzC,oBAAoB,EACpBN,MAAM,CACI;IACZ,IAAI,CAAC5B,gBAAgB,GAAG,IAAI,CAAClC,MAAM,CAACoE,OAAO,CACzC,oBAAoB,EACpBN,MAAM,CACI;IACZ,IAAI,CAACjC,iBAAiB,GAAG,IAAI,CAAC7B,MAAM,CAACoE,OAAO,CAC1C,qBAAqB,EACrBN,MAAM,CACI;IACZ,IAAI,CAAC7B,sBAAsB,GAAG,IAAI,CAACjC,MAAM,CAACoE,OAAO,CAC/C,2BAA2B,EAC3BN,MAAM,CACI;IACZ,IAAI,CAACjD,iBAAiB,GAAG,IAAI,CAACb,MAAM,CAACoE,OAAO,CAC1C,qBAAqB,EACrBN,MAAM,CACI;IACZ,IAAI,CAACtC,iBAAiB,GAAG,IAAI,CAACxB,MAAM,CAACqE,mBAAmB,CAAC,SAAS,CAAC;IACnE,IAAI,CAAC5C,wBAAwB,GAC3B,IAAI,CAACzB,MAAM,CAACqE,mBAAmB,CAAC,kBAAkB,CAAC;IACrD,IAAI,CAAC/C,kBAAkB,GAAG,IAAI,CAACtB,MAAM,CAACoE,OAAO,CAC3C,uBAAuB,EACvBN,MAAM,CACc;IACtB,IAAI,CAACvC,wBAAwB,GAAG,IAAI,CAACvB,MAAM,CAACoE,OAAO,CACjD,4BAA4B,EAC5BN,MAAM,CACG;IACX;IACA,IAAI,IAAI,CAAC5B,gBAAgB,EAAE;MACzB,IAAI,CAACoC,YAAY,CAACR,MAAM,CAAC;IAC3B;IACA;IACA,IAAI,IAAI,CAACjC,iBAAiB,EAAE;MAC1B,IAAI,CAAC0C,aAAa,CAACT,MAAM,CAAC;IAC5B;IACA;IACA,IAAI,IAAI,CAACtD,gBAAgB,EAAE;MACzB,IAAI,CAACgE,YAAY,CAACV,MAAM,CAAC;IAC3B;IACA;IACA,IAAI,IAAI,CAACjD,iBAAiB,EAAE;MAC1B,IAAI,CAAC4D,aAAa,CAACX,MAAM,CAAC;IAC5B;EACF;EAEAS,aAAaA,CAACT,MAAe;IAC3B,IAAI,CAACpC,sBAAsB,GAAG,IAAI,CAAC1B,MAAM,CAACoE,OAAO,CAC/C,2BAA2B,EAC3BN,MAAM,CACG;IAEX,IAAI,CAACnC,8BAA8B,GAAG,IAAI,CAAC3B,MAAM,CAACoE,OAAO,CACvD,oCAAoC,EACpCN,MAAM,CACI;IACZ,IAAI,IAAI,CAACnC,8BAA8B,EAAE;MACvC,IAAI,CAACC,iCAAiC,GAAG,IAAI,CAAC5B,MAAM,CAACoE,OAAO,CAC1D,uCAAuC,EACvCN,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAChC,8BAA8B,GAAG,IAAI,CAAC9B,MAAM,CAACoE,OAAO,CACvD,oCAAoC,EACpCN,MAAM,CACI;IACZ,IAAI,IAAI,CAAChC,8BAA8B,EAAE;MACvC,IAAI,CAACC,iCAAiC,GAAG,IAAI,CAAC/B,MAAM,CAACoE,OAAO,CAC1D,uCAAuC,EACvCN,MAAM,CAC2B;IACrC;IAEAY,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;MAChE;MACA,IAAI,IAAI,CAAChD,iBAAiB,IAAI8C,cAAc,EAAE;QAC5C,MAAMG,iBAAiB,GAAGH,cAAc,CACrCI,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCJ,iBAAiB,CAACK,OAAO,CAAEC,IAAI,IAC7BT,cAAc,CAACU,eAAe,CAACD,IAAI,CAAC,CACrC;QAED,IAAI,IAAI,CAACzD,8BAA8B,EAAE;UACvC,KAAK,MAAM2D,GAAG,IAAI,IAAI,CAAC1D,iCAAiC,EAAE;YACxD,IAAI,IAAI,CAACA,iCAAiC,CAAC2D,cAAc,CAACD,GAAG,CAAC,EAAE;cAC9DX,cAAc,CAACa,YAAY,CACzBF,GAAG,EACH,IAAI,CAAC1D,iCAAiC,CAAC0D,GAAG,CAAC,CAC5C;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAACxD,8BAA8B,EAAE;UACvC,KAAK,MAAMwD,GAAG,IAAI,IAAI,CAACvD,iCAAiC,EAAE;YACxD,IAAI,IAAI,CAACA,iCAAiC,CAACwD,cAAc,CAACD,GAAG,CAAC,EAAE;cAC9DX,cAAc,CAACa,YAAY,CACzBF,GAAG,EACH,IAAI,CAACvD,iCAAiC,CAACuD,GAAG,CAAC,CAC5C;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAd,YAAYA,CAACV,MAAe;IAC1B,IAAI,CAACrD,6BAA6B,GAAG,IAAI,CAACT,MAAM,CAACoE,OAAO,CACtD,mCAAmC,EACnCN,MAAM,CACI;IACZ,IAAI,IAAI,CAACrD,6BAA6B,EAAE;MACtC,IAAI,CAACC,gCAAgC,GAAG,IAAI,CAACV,MAAM,CAACoE,OAAO,CACzD,sCAAsC,EACtCN,MAAM,CAC2B;IACrC;IAEA,IAAI,CAACnD,+BAA+B,GAAG,IAAI,CAACX,MAAM,CAACoE,OAAO,CACxD,qCAAqC,EACrCN,MAAM,CACI;IACZ,IAAI,IAAI,CAACnD,+BAA+B,EAAE;MACxC,IAAI,CAACC,kCAAkC,GAAG,IAAI,CAACZ,MAAM,CAACoE,OAAO,CAC3D,wCAAwC,EACxCN,MAAM,CAC2B;IACrC;IAEAY,UAAU,CAAC,MAAK;MACd,MAAMe,aAAa,GAAGb,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;MAC9D;MACA,IAAI,IAAI,CAACrE,gBAAgB,IAAIiF,aAAa,EAAE;QAC1C,MAAMC,gBAAgB,GAAGD,aAAa,CACnCV,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCQ,gBAAgB,CAACP,OAAO,CAAEC,IAAI,IAAKK,aAAa,CAACJ,eAAe,CAACD,IAAI,CAAC,CAAC;QAEvE,IAAI,IAAI,CAAC3E,6BAA6B,EAAE;UACtC,KAAK,MAAM6E,GAAG,IAAI,IAAI,CAAC5E,gCAAgC,EAAE;YACvD,IAAI,IAAI,CAACA,gCAAgC,CAAC6E,cAAc,CAACD,GAAG,CAAC,EAAE;cAC7DG,aAAa,CAACD,YAAY,CACxBF,GAAG,EACH,IAAI,CAAC5E,gCAAgC,CAAC4E,GAAG,CAAC,CAC3C;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAAC3E,+BAA+B,EAAE;UACxC,KAAK,MAAM2E,GAAG,IAAI,IAAI,CAAC1E,kCAAkC,EAAE;YACzD,IAAI,IAAI,CAACA,kCAAkC,CAAC2E,cAAc,CAACD,GAAG,CAAC,EAAE;cAC/DG,aAAa,CAACD,YAAY,CACxBF,GAAG,EACH,IAAI,CAAC1E,kCAAkC,CAAC0E,GAAG,CAAC,CAC7C;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAhB,YAAYA,CAACR,MAAe;IAC1B,IAAI,CAAC3B,iBAAiB,GAAG,IAAI,CAACnC,MAAM,CAACoE,OAAO,CAAC,kBAAkB,EAAEN,MAAM,CAAW;IAClF,IAAI,CAAC1B,kBAAkB,GAAG,IAAI,CAACpC,MAAM,CAACoE,OAAO,CAAC,sBAAsB,EAAEN,MAAM,CAAW;IACvF,IAAI,CAACzB,0BAA0B,GAAG,IAAI,CAACrC,MAAM,CAACoE,OAAO,CAAC,2BAA2B,EAAEN,MAAM,CAAW;IACpG,IAAI,IAAI,CAAC1B,kBAAkB,KAAK,OAAO,EAAE;MACvC,IAAI,CAACC,0BAA0B,IAAI,gBAAgB;IACrD,CAAC,MAAM;MACL,IAAI,IAAI,CAACD,kBAAkB,KAAK,OAAO,EAAE;QACvC,IAAI,CAACC,0BAA0B,IAAI,kBAAkB;MACvD;IACF;IAEA,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACtC,MAAM,CAACoE,OAAO,CAAC,0BAA0B,EAAEN,MAAM,CAAY;IAC/F,IAAI,IAAI,CAACxB,qBAAqB,EAAE;MAC9BsC,QAAQ,CAACe,IAAI,CAACH,YAAY,CAAC,0BAA0B,EAAE,MAAM,CAAC;IAChE;IAEA,IAAI,CAACjD,oBAAoB,GAAG,IAAI,CAACvC,MAAM,CAACoE,OAAO,CAAC,yBAAyB,CAAY;IACrF,IAAI,IAAI,CAAC7B,oBAAoB,EAAE;MAC7BqC,QAAQ,CAACe,IAAI,CAACH,YAAY,CAAC,iCAAiC,EAAE,MAAM,CAAC;IACvE;EACF;EAEAf,aAAaA,CAACX,MAAe;IAC3B,IAAI,CAAChD,gBAAgB,GAAG,IAAI,CAACd,MAAM,CAACoE,OAAO,CACzC,oBAAoB,EACpBN,MAAM,CACuD;IAC/D,IAAI,CAAC9C,qBAAqB,GAAG,IAAI,CAAChB,MAAM,CAACoE,OAAO,CAC9C,0BAA0B,EAC1BN,MAAM,CACI;IACZ,IAAI,IAAI,CAAC9C,qBAAqB,EAAE;MAC9B,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACjB,MAAM,CAACoE,OAAO,CACjD,6BAA6B,EAC7BN,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAC5C,uBAAuB,GAAG,IAAI,CAAClB,MAAM,CAACoE,OAAO,CAChD,4BAA4B,EAC5BN,MAAM,CACI;IACZ,IAAI,IAAI,CAAC5C,uBAAuB,EAAE;MAChC,IAAI,CAACC,0BAA0B,GAAG,IAAI,CAACnB,MAAM,CAACoE,OAAO,CACnD,+BAA+B,EAC/BN,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAC/C,kBAAkB,GACpB,IAAI,CAACf,MAAM,CAACoE,OAAO,CAAC,mBAAmB,EAAEN,MAAM,CAAY,IAAI,EAAE;IACpE,IAAI,CAAC1C,yBAAyB,GAAG,IAAI,CAACpB,MAAM,CAACoE,OAAO,CAClD,8BAA8B,EAC9BN,MAAM,CACI;IACZ,IAAI,IAAI,CAAC1C,yBAAyB,EAAE;MAClC,IAAI,CAACC,4BAA4B,GAAG,IAAI,CAACrB,MAAM,CAACoE,OAAO,CACrD,iCAAiC,EACjCN,MAAM,CAC2B;MACnC,IAAI,CAAC/C,kBAAkB,IAAI,uBAAuB;IACpD;IAEA2D,UAAU,CAAC,MAAK;MACd,MAAMkB,cAAc,GAAGhB,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;MAChE;MACA,IAAI,IAAI,CAAChE,iBAAiB,IAAI+E,cAAc,EAAE;QAC5C,MAAMC,iBAAiB,GAAGD,cAAc,CACrCb,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCW,iBAAiB,CAACV,OAAO,CAAEC,IAAI,IAC7BQ,cAAc,CAACP,eAAe,CAACD,IAAI,CAAC,CACrC;QAED,IAAI,IAAI,CAACpE,qBAAqB,EAAE;UAC9B,KAAK,MAAMsE,GAAG,IAAI,IAAI,CAACrE,wBAAwB,EAAE;YAC/C,IAAI,IAAI,CAACA,wBAAwB,CAACsE,cAAc,CAACD,GAAG,CAAC,EAAE;cACrDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAACrE,wBAAwB,CAACqE,GAAG,CAAC,CACnC;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAACpE,uBAAuB,EAAE;UAChC,KAAK,MAAMoE,GAAG,IAAI,IAAI,CAACnE,0BAA0B,EAAE;YACjD,IAAI,IAAI,CAACA,0BAA0B,CAACoE,cAAc,CAACD,GAAG,CAAC,EAAE;cACvDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAACnE,0BAA0B,CAACmE,GAAG,CAAC,CACrC;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAAClE,yBAAyB,EAAE;UAClC,KAAK,MAAMkE,GAAG,IAAI,IAAI,CAACjE,4BAA4B,EAAE;YACnD,IAAI,IAAI,CAACA,4BAA4B,CAACkE,cAAc,CAACD,GAAG,CAAC,EAAE;cACzDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAACjE,4BAA4B,CAACiE,GAAG,CAAC,CACvC;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAAC1F,WAAW,CAAC+E,OAAO,CAAEY,EAAE,IAAKA,EAAE,CAAC3F,WAAW,EAAE,CAAC;EACpD;CACD;AAlW2C4F,UAAA,EAAzCpG,SAAS,CAAC,WAAW,EAAE;EAAEqG,MAAM,EAAE;AAAI,CAAE,CAAC,C,iDAAuB;AAcxBD,UAAA,EAAvCpG,SAAS,CAAC,SAAS,EAAE;EAAEqG,MAAM,EAAE;AAAI,CAAE,CAAC,C,+CAAqB;AACbD,UAAA,EAA9CpG,SAAS,CAAC,gBAAgB,EAAE;EAAEqG,MAAM,EAAE;AAAI,CAAE,CAAC,C,sDAA4B;AACjCD,UAAA,EAAxCpG,SAAS,CAAC,UAAU,EAAE;EAAEqG,MAAM,EAAE;AAAI,CAAE,CAAC,C,gDAAsB;AAvDnDnG,eAAe,GAAAkG,UAAA,EAL3BrG,SAAS,CAAC;EACTuG,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,yBAAyB;EACtCC,SAAS,EAAE,CAAC,yBAAyB;CACtC,CAAC,C,EACWtG,eAAe,CAyY3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}