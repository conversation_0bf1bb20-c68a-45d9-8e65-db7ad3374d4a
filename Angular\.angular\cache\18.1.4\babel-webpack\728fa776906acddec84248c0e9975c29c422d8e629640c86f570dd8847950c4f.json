{"ast": null, "code": "import { Subscription, combineLatest } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 24)(2, \"div\", 25)(3, \"h5\", 26);\n    i0.ɵɵtext(4, \"Project Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_ng_container_22_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(6, \"i\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 29)(8, \"div\", 30)(9, \"label\");\n    i0.ɵɵtext(10, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 31);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 32)(14, \"label\");\n    i0.ɵɵtext(15, \"Project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 31);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 32)(19, \"label\");\n    i0.ɵɵtext(20, \"External project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 31);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 30)(24, \"label\");\n    i0.ɵɵtext(25, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 31);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 32)(29, \"label\");\n    i0.ɵɵtext(30, \"Start date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 31);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 32)(35, \"label\");\n    i0.ɵɵtext(36, \"End date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 31);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectLocation || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.internalProjectManagerName || ctx_r1.project.internalProjectManager || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.externalPMNames || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectDescription || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStartDate ? i0.ɵɵpipeBind2(33, 6, ctx_r1.project.projectStartDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectEndDate ? i0.ɵɵpipeBind2(39, 9, ctx_r1.project.projectEndDate, \"MM/dd/yyyy\") : \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 42);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template_a_click_2_listener() {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r5.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"select\", 43);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template_select_change_8_listener($event) {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r5, $event.target.value));\n    });\n    i0.ɵɵelementStart(9, \"option\", 44);\n    i0.ɵɵtext(10, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"option\", 45);\n    i0.ɵɵtext(12, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 46);\n    i0.ɵɵtext(14, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 47);\n    i0.ɵɵtext(16, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 48);\n    i0.ɵɵtext(18, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 49);\n    i0.ɵɵtext(20, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 50);\n    i0.ɵɵtext(22, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 51);\n    i0.ɵɵtext(24, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"td\")(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"td\", 52)(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const permit_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitName || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.permitNumber || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r5.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(permit_r5.permitAppliedDate ? i0.ɵɵpipeBind2(28, 7, permit_r5.permitAppliedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(permit_r5.attentionReason || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_23_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"table\", 39)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Permit #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Submitted Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 40);\n    i0.ɵɵtext(13, \"Ball in Court\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template, 32, 10, \"tr\", 41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectViewComponent_div_2_ng_container_23_div_1_Template, 5, 0, \"div\", 33)(2, ProjectViewComponent_div_2_ng_container_23_div_2_Template, 16, 1, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nfunction ProjectViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12)(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵtext(12, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 18)(14, \"ul\", 19)(15, \"li\", 20)(16, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_16_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(17, \" Project details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 20)(19, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"permits\", $event));\n    });\n    i0.ɵɵtext(20, \" Permits list \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"div\", 22);\n    i0.ɵɵtemplate(22, ProjectViewComponent_div_2_ng_container_22_Template, 40, 12, \"ng-container\", 23)(23, ProjectViewComponent_div_2_ng_container_23_Template, 3, 2, \"ng-container\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"Project # \", ctx_r1.project.internalProjectNumber || \"\", \" - \", ctx_r1.project.projectName || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"Active\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx_r1.selectedTab === \"permits\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.project);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"permits\");\n  }\n}\nexport class ProjectViewComponent {\n  route;\n  router;\n  cdr;\n  appService;\n  projectsService;\n  permitsService;\n  modalService;\n  httpUtilService;\n  projectId = null;\n  project = null;\n  isLoading = false;\n  selectedTab = 'details';\n  projectPermits = [];\n  loginUser = {};\n  routeSubscription = new Subscription();\n  loadingSubscription = new Subscription();\n  constructor(route, router, cdr, appService, projectsService, permitsService, modalService, httpUtilService) {\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.modalService = modalService;\n    this.httpUtilService = httpUtilService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    // Subscribe to global loading state\n    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading === true;\n    });\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([this.route.paramMap, this.route.queryParams]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n      console.log('Project view - received params:', {\n        projectId: this.projectId,\n        queryParams\n      });\n      // Handle active tab from query params\n      const activeTab = queryParams['activeTab'];\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n        this.selectedTab = activeTab;\n        console.log('Setting selectedTab from query params:', activeTab);\n      } else {\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\n      }\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n    if (this.loadingSubscription) {\n      this.loadingSubscription.unsubscribe();\n    }\n  }\n  fetchProjectDetails() {\n    if (!this.projectId) {\n      return;\n    }\n    this.httpUtilService.loadingSubject.next(true);\n    this.projectsService.getProject({\n      projectId: this.projectId\n    }).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchProjectPermits() {\n    if (!this.projectId) {\n      return;\n    }\n    this.httpUtilService.loadingSubject.next(true);\n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [{\n          field: 'projectId',\n          operator: 'eq',\n          value: this.projectId\n        }]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter(p => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/projects/list']);\n  }\n  editProject() {\n    if (!this.projectId) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(ProjectPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    // Subscribe to the modal event when it closes\n    modalRef.result.then(result => {\n      // Handle successful edit\n      if (result) {\n        console.log('Project edited successfully:', result);\n        // Refresh project details and permits\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n    }, reason => {\n      // Handle modal dismissal\n      console.log('Modal dismissed:', reason);\n    });\n  }\n  viewPermit(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'project',\n        projectId: this.projectId\n      }\n    });\n  }\n  onStatusChange(permit, newStatus) {\n    if (!permit?.permitId || !newStatus) {\n      return;\n    }\n    const allowed = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n    if (!allowed.includes(newStatus)) {\n      return;\n    }\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.httpUtilService.loadingSubject.next(true);\n    this.cdr.markForCheck();\n    this.permitsService.updatePermitInternalReviewStatus({\n      permitId: permit.permitId,\n      internalReviewStatus: newStatus\n    }).subscribe({\n      next: res => {\n        const isFault = res?.isFault || res?.responseData?.isFault;\n        if (isFault) {\n          permit.internalReviewStatus = previous;\n        }\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        permit.internalReviewStatus = previous;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectViewComponent,\n    selectors: [[\"app-project-view\"]],\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"project-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"project-details-header\"], [1, \"header-content\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"project-title\"], [1, \"status-text\", \"status-active\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"card-body\"], [4, \"ngIf\"], [1, \"project-details-content\"], [1, \"project-details-tab-header\"], [1, \"project-details-title\"], [\"type\", \"button\", \"title\", \"Edit Project\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-light-primary\", 3, \"click\"], [1, \"fas\", \"fa-pencil\"], [1, \"project-details-grid\"], [1, \"project-detail-item\", \"span-2\"], [1, \"project-value\"], [1, \"project-detail-item\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"ball-in-court-header\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"fw-bold\", 3, \"click\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"], [1, \"ball-in-court-cell\"]],\n    template: function ProjectViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, ProjectViewComponent_div_2_Template, 24, 11, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.project);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i7.DatePipe],\n    styles: [\".project-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n\\n.project-view-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  padding-bottom: 0.25rem;\\n}\\n.project-view-container[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding-top: 0.5rem;\\n}\\n\\n.project-details-header[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: transparent;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  padding: 0.375rem 0.75rem !important;\\n  line-height: 1.5 !important;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  padding: 0.15rem 0.5rem;\\n  border-radius: 0.55rem;\\n  background-color: #f3f6f9;\\n  color: #3f4254;\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1;\\n  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.02s ease;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #5e6e82;\\n  font-size: 0.75rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover {\\n  background-color: #eef2f7;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);\\n  text-decoration: none;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(1px);\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n}\\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.project-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n}\\n\\n.project-details-tab-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 0.75rem;\\n  border-bottom: 1px solid #e5eaee;\\n}\\n.project-details-tab-header[_ngcontent-%COMP%]   .project-details-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n}\\n.project-details-tab-header[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  padding: 0;\\n  border-radius: 0.25rem;\\n  background-color: transparent;\\n  color: #3699ff;\\n  border: 2px solid #3699ff;\\n  transition: all 0.2s ease;\\n}\\n.project-details-tab-header[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.project-details-tab-header[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]:hover {\\n  background-color: #3699ff;\\n  color: white;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);\\n}\\n.project-details-tab-header[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n  box-shadow: 0 1px 4px rgba(54, 153, 255, 0.2);\\n}\\n\\n.project-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.project-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.4rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  line-height: 1.2;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #323337;\\n  text-transform: capitalize;\\n  letter-spacing: 0.1rem;\\n  margin: 0;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #3b3c3f;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%], \\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  margin-top: 0.1rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  padding: 0.5rem 0;\\n  border-bottom: none;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  display: block;\\n  vertical-align: top;\\n  padding: 0.5rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  text-align: left;\\n  background: transparent;\\n  border: none;\\n  min-width: 0;\\n  border-radius: 0;\\n}\\n\\n.project-detail-item.span-2[_ngcontent-%COMP%] {\\n  grid-column: span 2;\\n}\\n\\n.status-active[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n\\n.status-cancelled[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n\\n.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  padding: 0 0.5rem 0 0.5rem;\\n  margin-top: 0;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  overflow: hidden;\\n  table-layout: fixed;\\n  width: 100%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #3f4254;\\n  border-bottom: 2px solid #e5eaee;\\n  padding: 1rem 0.75rem;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5eaee;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-number-col[_ngcontent-%COMP%] {\\n  width: 25%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-description-col[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-type-col[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-status-col[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-number-cell[_ngcontent-%COMP%] {\\n  width: 25%;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-description-cell[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-type-cell[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-status-cell[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child, \\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.table[_ngcontent-%COMP%]   .ball-in-court-header[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   .ball-in-court-cell[_ngcontent-%COMP%] {\\n  width: 20%;\\n  white-space: normal;\\n  word-wrap: break-word;\\n  word-break: break-word;\\n  max-width: 200px;\\n}\\n\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%] {\\n  color: var(--bs-primary, #0d6efd);\\n  text-decoration: none;\\n  font-weight: 700;\\n  cursor: pointer;\\n  transition: color 0.15s ease, -webkit-text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease, -webkit-text-decoration 0.15s ease;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:hover {\\n  color: #0b5ed7;\\n  text-decoration: underline;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(24, 125, 228, 0.25);\\n  border-radius: 0.25rem;\\n}\\n\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-description-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-type-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-status-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n}\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.project-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.project-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.4);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #3699ff;\\n  padding: 2rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n    gap: 1rem;\\n  }\\n  .project-detail-item.span-2[_ngcontent-%COMP%] {\\n    grid-column: auto;\\n  }\\n  .table-responsive[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.25rem 0.5rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 0.75rem;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: flex-start;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: flex-end;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "combineLatest", "ProjectPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵlistener", "ProjectViewComponent_div_2_ng_container_22_Template_button_click_5_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "editProject", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "project", "projectLocation", "internalProjectManagerName", "internalProjectManager", "externalPMNames", "projectDescription", "projectStartDate", "ɵɵpipeBind2", "projectEndDate", "ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template_a_click_2_listener", "permit_r5", "_r4", "$implicit", "viewPermit", "permitId", "ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template_select_change_8_listener", "$event", "onStatusChange", "target", "value", "ɵɵtextInterpolate1", "permitName", "permitNumber", "ɵɵproperty", "internalReviewStatus", "isLoading", "permitAppliedDate", "attentionReason", "ɵɵtemplate", "ProjectViewComponent_div_2_ng_container_23_div_2_tr_15_Template", "projectPermits", "ProjectViewComponent_div_2_ng_container_23_div_1_Template", "ProjectViewComponent_div_2_ng_container_23_div_2_Template", "length", "ProjectViewComponent_div_2_Template_button_click_10_listener", "_r1", "goBack", "ProjectViewComponent_div_2_Template_a_click_16_listener", "showTab", "ProjectViewComponent_div_2_Template_a_click_19_listener", "ProjectViewComponent_div_2_ng_container_22_Template", "ProjectViewComponent_div_2_ng_container_23_Template", "ɵɵtextInterpolate2", "internalProjectNumber", "projectName", "projectStatus", "ɵɵpureFunction1", "_c0", "selectedTab", "ProjectViewComponent", "route", "router", "cdr", "appService", "projectsService", "permitsService", "modalService", "httpUtilService", "projectId", "loginUser", "routeSubscription", "loadingSubscription", "constructor", "ngOnInit", "getLoggedInUser", "loadingSubject", "subscribe", "loading", "paramMap", "queryParams", "idParam", "get", "Number", "console", "log", "activeTab", "fetchProjectDetails", "fetchProjectPermits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "next", "getProject", "res", "<PERSON><PERSON><PERSON>", "responseData", "Project", "data", "Object", "keys", "error", "faultMessage", "err", "getPermitsForKendoGrid", "take", "skip", "sort", "filter", "logic", "filters", "field", "operator", "search", "loggedInUserId", "userId", "rawPermits", "p", "permitProjectId", "projectID", "project_id", "ProjectId", "ProjectID", "navigate", "NgbModalOptions", "size", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "id", "result", "then", "reason", "from", "permit", "newStatus", "allowed", "includes", "previous", "updatePermitInternalReviewStatus", "getStatusClass", "status", "toLowerCase", "replace", "tab", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "ChangeDetectorRef", "i2", "AppService", "i3", "ProjectsService", "i4", "PermitsService", "i5", "NgbModal", "i6", "HttpUtilsService", "selectors", "decls", "vars", "consts", "template", "ProjectViewComponent_Template", "rf", "ctx", "ProjectViewComponent_div_0_Template", "ProjectViewComponent_div_2_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subscription, combineLatest } from 'rxjs';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ProjectsService } from '../../services/projects.service';\nimport { PermitsService } from '../../services/permits.service';\nimport { AppService } from '../../services/app.service';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport { HttpUtilsService } from '../../services/http-utils.service';\n\n@Component({\n  selector: 'app-project-view',\n  templateUrl: './project-view.component.html',\n  styleUrls: ['./project-view.component.scss']\n})\nexport class ProjectViewComponent implements OnInit, OnDestroy {\n  public projectId: number | null = null;\n  public project: any = null;\n  public isLoading: boolean = false;\n  public selectedTab: string = 'details';\n  public projectPermits: any[] = [];\n  public loginUser: any = {};\n  private routeSubscription: Subscription = new Subscription();\n  private loadingSubscription: Subscription = new Subscription();\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef,\n    private appService: AppService,\n    private projectsService: ProjectsService,\n    private permitsService: PermitsService,\n    private modalService: NgbModal,\n    private httpUtilService: HttpUtilsService\n  ) {}\n\n  ngOnInit(): void {\n    this.loginUser = this.appService.getLoggedInUser();\n\n    // Subscribe to global loading state\n    this.loadingSubscription = this.httpUtilService.loadingSubject.subscribe(\n      (loading: any) => {\n        this.isLoading = loading === true;\n      }\n    );\n\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([\n      this.route.paramMap,\n      this.route.queryParams\n    ]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n\n      console.log('Project view - received params:', { projectId: this.projectId, queryParams });\n      \n      // Handle active tab from query params\n      const activeTab = queryParams['activeTab'];\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n        this.selectedTab = activeTab;\n        console.log('Setting selectedTab from query params:', activeTab);\n      } else {\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\n      }\n\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      \n      this.cdr.markForCheck();\n    });\n  }\n\n  ngOnDestroy(): void {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n    if (this.loadingSubscription) {\n      this.loadingSubscription.unsubscribe();\n    }\n  }\n\n  public fetchProjectDetails(): void {\n    if (!this.projectId) { return; }\n    this.httpUtilService.loadingSubject.next(true);\n    this.projectsService.getProject({ projectId: this.projectId }).subscribe({\n      next: (res: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public fetchProjectPermits(): void {\n    if (!this.projectId) { return; }\n    this.httpUtilService.loadingSubject.next(true);\n    \n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [\n          {\n            field: 'projectId',\n            operator: 'eq',\n            value: this.projectId\n          }\n        ]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: (res: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter((p: any) => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public goBack(): void {\n    this.router.navigate(['/projects/list']);\n  }\n\n  public editProject(): void {\n    if (!this.projectId) { return; }\n    \n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(\n      ProjectPopupComponent,\n      NgbModalOptions\n    );\n    \n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    \n    // Subscribe to the modal event when it closes\n    modalRef.result.then(\n      (result) => {\n        // Handle successful edit\n        if (result) {\n          console.log('Project edited successfully:', result);\n          // Refresh project details and permits\n          this.fetchProjectDetails();\n          this.fetchProjectPermits();\n        }\n      },\n      (reason) => {\n        // Handle modal dismissal\n        console.log('Modal dismissed:', reason);\n      }\n    );\n  }\n\n  public viewPermit(permitId: number): void {\n    this.router.navigate(['/permits/view', permitId], { \n      queryParams: { from: 'project', projectId: this.projectId } \n    });\n  }\n\n  public onStatusChange(permit: any, newStatus: string): void {\n    if (!permit?.permitId || !newStatus) { return; }\n    const allowed = [\n      'Approved',\n      'Pacifica Verification',\n      'Dis-Approved',\n      'Pending',\n      'Not Required',\n      'In Review',\n      '1 Cycle Completed'\n    ];\n    if (!allowed.includes(newStatus)) { return; }\n\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.httpUtilService.loadingSubject.next(true);\n    this.cdr.markForCheck();\n\n    this.permitsService\n      .updatePermitInternalReviewStatus({ permitId: permit.permitId, internalReviewStatus: newStatus })\n      .subscribe({\n        next: (res: any) => {\n          const isFault = res?.isFault || res?.responseData?.isFault;\n          if (isFault) {\n            permit.internalReviewStatus = previous;\n          }\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.markForCheck();\n        },\n        error: () => {\n          permit.internalReviewStatus = previous;\n          this.httpUtilService.loadingSubject.next(false);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  public getStatusClass(status: string): string {\n    if (!status) return 'status-n-a';\n    return (\n      'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-')\n    );\n  }\n\n  showTab(tab: string, $event: any) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n}\n", "<!-- Full Screen Loading Overlay -->\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"custom-colored-spinner\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<div class=\"project-view-container\">\n  <!-- Project Details Card -->\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"project\">\n    <!-- Project Details Header -->\n    <div class=\"project-details-header\">\n      <div class=\"header-content\">\n        <div class=\"title-wrap\">\n          <div class=\"title-line\">\n            <span class=\"project-title\">Project # {{ project.internalProjectNumber || \"\" }} - {{ project.projectName || \"\" }}</span>\n            <span class=\"status-text status-active\">{{ project.projectStatus || \"Active\" }}</span>\n          </div>\n        </div>\n        <div class=\"button-group\">\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center\" (click)=\"goBack()\">\n            <i class=\"fas fa-arrow-left me-2\"></i>\n            Back\n          </button>\n        </div>\n      </div>\n    </div>\n    <!-- Card Header with Tabs -->\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\">\n      <!-- Tabs -->\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap\">\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'details' }\"\n            (click)=\"showTab('details', $event)\">\n            Project details\n          </a>\n        </li>\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'permits' }\"\n            (click)=\"showTab('permits', $event)\">\n            Permits list\n          </a>\n        </li>\n      </ul>\n    </div>\n\n    <!-- Card Body with Tab Content -->\n    <div class=\"card-body\">\n      <!-- Project Details Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'details' && project\">\n        <div class=\"project-details-content\">\n          <!-- Project Details Header with Edit Icon -->\n          <div class=\"project-details-tab-header\">\n            <h5 class=\"project-details-title\">Project Information</h5>\n            <button type=\"button\" class=\"btn btn-icon btn-sm btn-light-primary\" (click)=\"editProject()\" title=\"Edit Project\">\n              <i class=\"fas fa-pencil\"></i>\n            </button>\n          </div>\n          <div class=\"project-details-grid\">\n            <div class=\"project-detail-item span-2\">\n              <label>Location</label>\n              <span class=\"project-value\">{{ project.projectLocation || \"\" }}</span>\n            </div>\n             <div class=\"project-detail-item\">\n               <label>Project manager</label>\n               <span class=\"project-value\">{{ project.internalProjectManagerName || project.internalProjectManager || \"\" }}</span>\n             </div>\n            <div class=\"project-detail-item\">\n              <label>External project manager</label>\n              <span class=\"project-value\">{{ project.externalPMNames || \"\" }}</span>\n            </div>\n            <div class=\"project-detail-item span-2\">\n              <label>Description</label>\n              <span class=\"project-value\">{{ project.projectDescription || \"\" }}</span>\n            </div>\n            <div class=\"project-detail-item\">\n              <label>Start date</label>\n              <span class=\"project-value\">{{\n                project.projectStartDate\n                ? (project.projectStartDate | date : \"MM/dd/yyyy\")\n                : \"\"\n              }}</span>\n            </div>\n            <div class=\"project-detail-item\">\n              <label>End date</label>\n              <span class=\"project-value\">{{\n                project.projectEndDate\n                ? (project.projectEndDate | date : \"MM/dd/yyyy\")\n                : \"\"\n              }}</span>\n            </div>\n           </div>\n         </div>\n       </ng-container>\n\n      <!-- Permits List Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'permits'\">\n        <!-- Empty State for Permits -->\n        <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"projectPermits.length === 0\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-file-alt fa-3x mb-3\"></i>\n            <p>No permits found for this project.</p>\n          </div>\n        </div>\n\n        <!-- Permits Table -->\n        <div class=\"table-responsive\" *ngIf=\"projectPermits.length > 0\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th>Permit Name</th>\n                <th>Permit #</th>\n                <th>Status</th>\n                <th>Submitted Date</th>\n                <th class=\"ball-in-court-header\">Ball in Court</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let permit of projectPermits\">\n                <td>\n                  <a \n                    class=\"fw-bold\" \n                    (click)=\"viewPermit(permit.permitId)\" \n                    title=\"View Permit\"\n                    aria-label=\"View Permit\"\n                  >\n                    {{ permit.permitName || \"\" }}\n                  </a>\n                </td>\n                <td>\n                  <span>{{ permit.permitNumber || \"\" }}</span>\n                </td>\n                <td>\n                  <select class=\"form-select form-select-sm w-auto\"\n                          [value]=\"permit.internalReviewStatus || ''\"\n                          (change)=\"onStatusChange(permit, $any($event.target).value)\"\n                          [disabled]=\"isLoading\">\n                    <option [value]=\"''\" disabled>Select status</option>\n                    <option value=\"Approved\">Approved</option>\n                    <option value=\"Pacifica Verification\">Pacifica Verification</option>\n                    <option value=\"Dis-Approved\">Dis-Approved</option>\n                    <option value=\"Pending\">Pending</option>\n                    <option value=\"Not Required\">Not Required</option>\n                    <option value=\"In Review\">In Review</option>\n                    <option value=\"1 Cycle Completed\">1 Cycle Completed</option>\n                  </select>\n                </td>\n                <td>\n                  <span>{{ permit.permitAppliedDate ? (permit.permitAppliedDate | date : 'MM/dd/yyyy') : '' }}</span>\n                </td>\n                <td class=\"ball-in-court-cell\">\n                  <span>{{ permit.attentionReason || '' }}</span>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,aAAa,QAAQ,MAAM;AAKlD,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;;;;;;;;;;ICH1EC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IA4CAH,EAAA,CAAAI,uBAAA,GAA0D;IAIpDJ,EAHJ,CAAAC,cAAA,cAAqC,cAEK,aACJ;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,iBAAiH;IAA7CD,EAAA,CAAAK,UAAA,mBAAAC,4EAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACzFZ,EAAA,CAAAa,SAAA,YAA6B;IAEjCb,EADE,CAAAG,YAAA,EAAS,EACL;IAGFH,EAFJ,CAAAC,cAAA,cAAkC,cACQ,YAC/B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEHH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgF;IAC9GF,EAD8G,CAAAG,YAAA,EAAO,EAC/G;IAELH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAwC,aAC/B;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IAGPF,EAHO,CAAAG,YAAA,EAAO,EACL,EACD,EACF;;;;;IA/B2BH,EAAA,CAAAc,SAAA,IAAmC;IAAnCd,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAC,eAAA,OAAmC;IAIlCjB,EAAA,CAAAc,SAAA,GAAgF;IAAhFd,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAE,0BAAA,IAAAT,MAAA,CAAAO,OAAA,CAAAG,sBAAA,OAAgF;IAIjFnB,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAI,eAAA,OAAmC;IAInCpB,EAAA,CAAAc,SAAA,GAAsC;IAAtCd,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAK,kBAAA,OAAsC;IAItCrB,EAAA,CAAAc,SAAA,GAI1B;IAJ0Bd,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAM,gBAAA,GAAAtB,EAAA,CAAAuB,WAAA,QAAAd,MAAA,CAAAO,OAAA,CAAAM,gBAAA,qBAI1B;IAI0BtB,EAAA,CAAAc,SAAA,GAI1B;IAJ0Bd,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAQ,cAAA,GAAAxB,EAAA,CAAAuB,WAAA,QAAAd,MAAA,CAAAO,OAAA,CAAAQ,cAAA,qBAI1B;;;;;IAUNxB,EADF,CAAAC,cAAA,cAAkH,cACvF;IACvBD,EAAA,CAAAa,SAAA,YAA0C;IAC1Cb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAEzCF,EAFyC,CAAAG,YAAA,EAAI,EACrC,EACF;;;;;;IAiBIH,EAFJ,CAAAC,cAAA,SAA0C,SACpC,YAMD;IAHCD,EAAA,CAAAK,UAAA,mBAAAoB,mFAAA;MAAA,MAAAC,SAAA,GAAA1B,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoB,UAAA,CAAAH,SAAA,CAAAI,QAAA,CAA2B;IAAA,EAAC;IAIrC9B,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAEHH,EADF,CAAAC,cAAA,SAAI,WACI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACzC;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAI6B;IADvBD,EAAA,CAAAK,UAAA,oBAAA0B,yFAAAC,MAAA;MAAA,MAAAN,SAAA,GAAA1B,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAwB,cAAA,CAAAP,SAAA,EAAAM,MAAA,CAAAE,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAElEnC,EAAA,CAAAC,cAAA,iBAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAAsC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAAkC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACrD,EACN;IAEHH,EADF,CAAAC,cAAA,UAAI,YACI;IAAAD,EAAA,CAAAE,MAAA,IAAsF;;IAC9FF,EAD8F,CAAAG,YAAA,EAAO,EAChG;IAEHH,EADF,CAAAC,cAAA,cAA+B,YACvB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAE5CF,EAF4C,CAAAG,YAAA,EAAO,EAC5C,EACF;;;;;IA3BCH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAoC,kBAAA,MAAAV,SAAA,CAAAW,UAAA,YACF;IAGMrC,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAY,YAAA,OAA+B;IAI7BtC,EAAA,CAAAc,SAAA,GAA2C;IAE3Cd,EAFA,CAAAuC,UAAA,UAAAb,SAAA,CAAAc,oBAAA,OAA2C,aAAA/B,MAAA,CAAAgC,SAAA,CAErB;IACpBzC,EAAA,CAAAc,SAAA,EAAY;IAAZd,EAAA,CAAAuC,UAAA,aAAY;IAWhBvC,EAAA,CAAAc,SAAA,IAAsF;IAAtFd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAgB,iBAAA,GAAA1C,EAAA,CAAAuB,WAAA,QAAAG,SAAA,CAAAgB,iBAAA,qBAAsF;IAGtF1C,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAiB,eAAA,OAAkC;;;;;IAzC1C3C,EAJR,CAAAC,cAAA,cAAgE,gBAC7B,YACxB,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAElDF,EAFkD,CAAAG,YAAA,EAAK,EAChD,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA4C,UAAA,KAAAC,+DAAA,mBAA0C;IAsChD7C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAtCuBH,EAAA,CAAAc,SAAA,IAAiB;IAAjBd,EAAA,CAAAuC,UAAA,YAAA9B,MAAA,CAAAqC,cAAA,CAAiB;;;;;IAtBhD9C,EAAA,CAAAI,uBAAA,GAA+C;IAU7CJ,EARA,CAAA4C,UAAA,IAAAG,yDAAA,kBAAkH,IAAAC,yDAAA,mBAQlD;;;;;IARehD,EAAA,CAAAc,SAAA,EAAiC;IAAjCd,EAAA,CAAAuC,UAAA,SAAA9B,MAAA,CAAAqC,cAAA,CAAAG,MAAA,OAAiC;IAQjFjD,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAAuC,UAAA,SAAA9B,MAAA,CAAAqC,cAAA,CAAAG,MAAA,KAA+B;;;;;;IA3F1DjD,EANV,CAAAC,cAAA,aAAsD,aAEhB,cACN,cACF,cACE,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxHH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAEnFF,EAFmF,CAAAG,YAAA,EAAO,EAClF,EACF;IAEJH,EADF,CAAAC,cAAA,cAA0B,kBACgF;IAAnBD,EAAA,CAAAK,UAAA,mBAAA6C,6DAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,MAAA,EAAQ;IAAA,EAAC;IACrGpD,EAAA,CAAAa,SAAA,aAAsC;IACtCb,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAMAH,EAJN,CAAAC,cAAA,eAAyF,cAEgB,cAChF,aAEoB;IAArCD,EAAA,CAAAK,UAAA,mBAAAgD,wDAAArB,MAAA;MAAAhC,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6C,OAAA,CAAQ,SAAS,EAAAtB,MAAA,CAAS;IAAA,EAAC;IACpChC,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAEHH,EADF,CAAAC,cAAA,cAAqB,aAEoB;IAArCD,EAAA,CAAAK,UAAA,mBAAAkD,wDAAAvB,MAAA;MAAAhC,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6C,OAAA,CAAQ,SAAS,EAAAtB,MAAA,CAAS;IAAA,EAAC;IACpChC,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAI,EACD,EACF,EACD;IAGNH,EAAA,CAAAC,cAAA,eAAuB;IAiDrBD,EA/CA,CAAA4C,UAAA,KAAAY,mDAAA,6BAA0D,KAAAC,mDAAA,2BA+CX;IA+DnDzD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhJgCH,EAAA,CAAAc,SAAA,GAAqF;IAArFd,EAAA,CAAA0D,kBAAA,eAAAjD,MAAA,CAAAO,OAAA,CAAA2C,qBAAA,eAAAlD,MAAA,CAAAO,OAAA,CAAA4C,WAAA,WAAqF;IACzE5D,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAA6C,aAAA,aAAuC;IAgBrB7D,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAuC,UAAA,YAAAvC,EAAA,CAAA8D,eAAA,IAAAC,GAAA,EAAAtD,MAAA,CAAAuD,WAAA,gBAAiD;IAMjDhE,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAuC,UAAA,YAAAvC,EAAA,CAAA8D,eAAA,IAAAC,GAAA,EAAAtD,MAAA,CAAAuD,WAAA,gBAAiD;IAWlGhE,EAAA,CAAAc,SAAA,GAAyC;IAAzCd,EAAA,CAAAuC,UAAA,SAAA9B,MAAA,CAAAuD,WAAA,iBAAAvD,MAAA,CAAAO,OAAA,CAAyC;IA+CzChB,EAAA,CAAAc,SAAA,EAA8B;IAA9Bd,EAAA,CAAAuC,UAAA,SAAA9B,MAAA,CAAAuD,WAAA,cAA8B;;;ADpFnD,OAAM,MAAOC,oBAAoB;EAWrBC,KAAA;EACAC,MAAA;EACAC,GAAA;EACAC,UAAA;EACAC,eAAA;EACAC,cAAA;EACAC,YAAA;EACAC,eAAA;EAjBHC,SAAS,GAAkB,IAAI;EAC/B1D,OAAO,GAAQ,IAAI;EACnByB,SAAS,GAAY,KAAK;EAC1BuB,WAAW,GAAW,SAAS;EAC/BlB,cAAc,GAAU,EAAE;EAC1B6B,SAAS,GAAQ,EAAE;EAClBC,iBAAiB,GAAiB,IAAI/E,YAAY,EAAE;EACpDgF,mBAAmB,GAAiB,IAAIhF,YAAY,EAAE;EAE9DiF,YACUZ,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,cAA8B,EAC9BC,YAAsB,EACtBC,eAAiC;IAPjC,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;EACtB;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACN,UAAU,CAACW,eAAe,EAAE;IAElD;IACA,IAAI,CAACH,mBAAmB,GAAG,IAAI,CAACJ,eAAe,CAACQ,cAAc,CAACC,SAAS,CACrEC,OAAY,IAAI;MACf,IAAI,CAAC1C,SAAS,GAAG0C,OAAO,KAAK,IAAI;IACnC,CAAC,CACF;IAED;IACA,IAAI,CAACP,iBAAiB,GAAG9E,aAAa,CAAC,CACrC,IAAI,CAACoE,KAAK,CAACkB,QAAQ,EACnB,IAAI,CAAClB,KAAK,CAACmB,WAAW,CACvB,CAAC,CAACH,SAAS,CAAC,CAAC,CAACE,QAAQ,EAAEC,WAAW,CAAC,KAAI;MACvC,MAAMC,OAAO,GAAGF,QAAQ,CAACG,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,CAACb,SAAS,GAAGY,OAAO,GAAGE,MAAM,CAACF,OAAO,CAAC,GAAG,IAAI;MAEjDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAAEhB,SAAS,EAAE,IAAI,CAACA,SAAS;QAAEW;MAAW,CAAE,CAAC;MAE1F;MACA,MAAMM,SAAS,GAAGN,WAAW,CAAC,WAAW,CAAC;MAC1C,IAAIM,SAAS,KAAKA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,SAAS,CAAC,EAAE;QACrE,IAAI,CAAC3B,WAAW,GAAG2B,SAAS;QAC5BF,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,SAAS,CAAC;MAClE,CAAC,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC1B,WAAW,CAAC;MAC7E;MAEA,IAAI,IAAI,CAACU,SAAS,EAAE;QAClB,IAAI,CAACkB,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;MAEA,IAAI,CAACzB,GAAG,CAAC0B,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACnB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACoB,WAAW,EAAE;IACtC;IACA,IAAI,IAAI,CAACnB,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACmB,WAAW,EAAE;IACxC;EACF;EAEOJ,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAClB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACD,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC3B,eAAe,CAAC4B,UAAU,CAAC;MAAExB,SAAS,EAAE,IAAI,CAACA;IAAS,CAAE,CAAC,CAACQ,SAAS,CAAC;MACvEe,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC1B,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/CR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAES,GAAG,CAAC;QACzC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB;UACA,IAAI,CAACpF,OAAO,GAAGmF,GAAG,CAACE,YAAY,EAAEC,OAAO,IAAIH,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACE,YAAY,IAAI,IAAI;UAC9FZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC1E,OAAO,CAAC;UACnDyE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEc,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzF,OAAO,IAAI,EAAE,CAAC,CAAC;UACzE;QACF,CAAC,MAAM;UACLyE,OAAO,CAACiB,KAAK,CAAC,qBAAqB,EAAEP,GAAG,CAACQ,YAAY,CAAC;UACtD,IAAI,CAAC3F,OAAO,GAAG,IAAI;QACrB;QACA,IAAI,CAACoD,GAAG,CAAC0B,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAG,IAAI;QACb,IAAI,CAACnC,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/CR,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEE,GAAG,CAAC;QACrD,IAAI,CAACxC,GAAG,CAAC0B,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOD,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACnB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACD,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAAC1B,cAAc,CAACsC,sBAAsB,CAAC;MACzCC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;QACNC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,CACP;UACEC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,IAAI;UACdlF,KAAK,EAAE,IAAI,CAACuC;SACb;OAEJ;MACD4C,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,IAAI,CAAC5C,SAAS,CAAC6C;KAChC,CAAC,CAACtC,SAAS,CAAC;MACXe,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAC1B,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/CR,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,GAAG,CAAC;QACjD,IAAIA,GAAG,EAAEC,OAAO,EAAE;UAChBX,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEP,GAAG,CAACQ,YAAY,CAAC;UAClE,IAAI,CAAC7D,cAAc,GAAG,EAAE;QAC1B,CAAC,MAAM;UACL,MAAM2E,UAAU,GAAGtB,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACI,IAAI,IAAI,EAAE;UAC3D;UACA,IAAI,CAACzD,cAAc,GAAG,CAAC2E,UAAU,IAAI,EAAE,EAAER,MAAM,CAAES,CAAM,IAAI;YACzD,MAAMC,eAAe,GAAGD,CAAC,EAAEhD,SAAS,IAAIgD,CAAC,EAAEE,SAAS,IAAIF,CAAC,EAAEG,UAAU,IAAIH,CAAC,EAAEI,SAAS,IAAIJ,CAAC,EAAEK,SAAS;YACrG,OAAO,IAAI,CAACrD,SAAS,IAAI,IAAI,GAAGc,MAAM,CAACmC,eAAe,CAAC,KAAKnC,MAAM,CAAC,IAAI,CAACd,SAAS,CAAC,GAAG,IAAI;UAC3F,CAAC,CAAC;UACFe,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC5C,cAAc,CAAC;QAC1E;QACA,IAAI,CAACsB,GAAG,CAAC0B,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAQ,IAAI;QAClB,IAAI,CAACnC,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/CR,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEE,GAAG,CAAC;QACpD,IAAI,CAAC9D,cAAc,GAAG,EAAE;QACxB,IAAI,CAACsB,GAAG,CAAC0B,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEO1C,MAAMA,CAAA;IACX,IAAI,CAACe,MAAM,CAAC6D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEOpH,WAAWA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC8D,SAAS,EAAE;MAAE;IAAQ;IAE/B,MAAMuD,eAAe,GAKjB;MACFC,IAAI,EAAE,IAAI;MAAE;MACZC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC9D,YAAY,CAAC+D,IAAI,CACrCxI,qBAAqB,EACrBkI,eAAe,CAChB;IAED;IACAK,QAAQ,CAACE,iBAAiB,CAACC,EAAE,GAAG,IAAI,CAAC/D,SAAS;IAC9C4D,QAAQ,CAACE,iBAAiB,CAACxH,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACAsH,QAAQ,CAACI,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAIA,MAAM,EAAE;QACVjD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgD,MAAM,CAAC;QACnD;QACA,IAAI,CAAC9C,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;IACF,CAAC,EACA+C,MAAM,IAAI;MACT;MACAnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,MAAM,CAAC;IACzC,CAAC,CACF;EACH;EAEO/G,UAAUA,CAACC,QAAgB;IAChC,IAAI,CAACqC,MAAM,CAAC6D,QAAQ,CAAC,CAAC,eAAe,EAAElG,QAAQ,CAAC,EAAE;MAChDuD,WAAW,EAAE;QAAEwD,IAAI,EAAE,SAAS;QAAEnE,SAAS,EAAE,IAAI,CAACA;MAAS;KAC1D,CAAC;EACJ;EAEOzC,cAAcA,CAAC6G,MAAW,EAAEC,SAAiB;IAClD,IAAI,CAACD,MAAM,EAAEhH,QAAQ,IAAI,CAACiH,SAAS,EAAE;MAAE;IAAQ;IAC/C,MAAMC,OAAO,GAAG,CACd,UAAU,EACV,uBAAuB,EACvB,cAAc,EACd,SAAS,EACT,cAAc,EACd,WAAW,EACX,mBAAmB,CACpB;IACD,IAAI,CAACA,OAAO,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MAAE;IAAQ;IAE5C,MAAMG,QAAQ,GAAGJ,MAAM,CAACtG,oBAAoB;IAC5CsG,MAAM,CAACtG,oBAAoB,GAAGuG,SAAS;IACvC,IAAI,CAACtE,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC7B,GAAG,CAAC0B,YAAY,EAAE;IAEvB,IAAI,CAACvB,cAAc,CAChB4E,gCAAgC,CAAC;MAAErH,QAAQ,EAAEgH,MAAM,CAAChH,QAAQ;MAAEU,oBAAoB,EAAEuG;IAAS,CAAE,CAAC,CAChG7D,SAAS,CAAC;MACTe,IAAI,EAAGE,GAAQ,IAAI;QACjB,MAAMC,OAAO,GAAGD,GAAG,EAAEC,OAAO,IAAID,GAAG,EAAEE,YAAY,EAAED,OAAO;QAC1D,IAAIA,OAAO,EAAE;UACX0C,MAAM,CAACtG,oBAAoB,GAAG0G,QAAQ;QACxC;QACA,IAAI,CAACzE,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC7B,GAAG,CAAC0B,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAEA,CAAA,KAAK;QACVoC,MAAM,CAACtG,oBAAoB,GAAG0G,QAAQ;QACtC,IAAI,CAACzE,eAAe,CAACQ,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC7B,GAAG,CAAC0B,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEOsD,cAAcA,CAACC,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,OACE,SAAS,GAAGA,MAAM,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE7E;EAEAjG,OAAOA,CAACkG,GAAW,EAAExH,MAAW;IAC9B,IAAI,CAACgC,WAAW,GAAGwF,GAAG;IACtB,IAAI,CAACpF,GAAG,CAAC0B,YAAY,EAAE;EACzB;;qCAnPW7B,oBAAoB,EAAAjE,EAAA,CAAAyJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3J,EAAA,CAAAyJ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA5J,EAAA,CAAAyJ,iBAAA,CAAAzJ,EAAA,CAAA6J,iBAAA,GAAA7J,EAAA,CAAAyJ,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA/J,EAAA,CAAAyJ,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAjK,EAAA,CAAAyJ,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAnK,EAAA,CAAAyJ,iBAAA,CAAAW,EAAA,CAAAC,QAAA,GAAArK,EAAA,CAAAyJ,iBAAA,CAAAa,EAAA,CAAAC,gBAAA;EAAA;;UAApBtG,oBAAoB;IAAAuG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdjC9K,EAAA,CAAA4C,UAAA,IAAAoI,mCAAA,iBAA0D;QAS1DhL,EAAA,CAAAC,cAAA,aAAoC;QAElCD,EAAA,CAAA4C,UAAA,IAAAqI,mCAAA,mBAAsD;QAuJxDjL,EAAA,CAAAG,YAAA,EAAM;;;QAlKAH,EAAA,CAAAuC,UAAA,SAAAwI,GAAA,CAAAtI,SAAA,CAAe;QAWoBzC,EAAA,CAAAc,SAAA,GAAa;QAAbd,EAAA,CAAAuC,UAAA,SAAAwI,GAAA,CAAA/J,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}