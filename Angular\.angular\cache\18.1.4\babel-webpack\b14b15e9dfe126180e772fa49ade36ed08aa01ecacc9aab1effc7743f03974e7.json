{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Project - \", ctx_r0.projectName, \"\");\n  }\n}\nfunction ProjectPopupComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23)(3, \"span\", 24);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵtext(6, \"Loading project data...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectPopupComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 22)(2, \"div\", 23)(3, \"span\", 24);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵtext(6, \"Initializing form...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"div\", 10)(3, \"div\", 30)(4, \"label\", 31);\n    i0.ɵɵtext(5, \"Project name\");\n    i0.ɵɵelementStart(6, \"sup\", 32);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"input\", 33);\n    i0.ɵɵtemplate(9, ProjectPopupComponent_form_21_ng_container_1_span_9_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 35)(12, \"div\", 30)(13, \"label\", 31);\n    i0.ɵɵtext(14, \"Internal project # \");\n    i0.ɵɵelementStart(15, \"sup\", 32);\n    i0.ɵɵtext(16, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"input\", 36);\n    i0.ɵɵtemplate(18, ProjectPopupComponent_form_21_ng_container_1_span_18_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 37)(20, \"label\", 31);\n    i0.ɵɵtext(21, \"Start date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 37)(24, \"label\", 31);\n    i0.ɵɵtext(25, \"End date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 10)(28, \"label\", 31);\n    i0.ɵɵtext(29, \"Location\");\n    i0.ɵɵelementStart(30, \"sup\", 32);\n    i0.ɵɵtext(31, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(32, \"input\", 40);\n    i0.ɵɵtemplate(33, ProjectPopupComponent_form_21_ng_container_1_span_33_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 29)(35, \"div\", 10)(36, \"label\", 31);\n    i0.ɵɵtext(37, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"textarea\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"projectName\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"internalProjectNo\"));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"location\"));\n  }\n}\nfunction ProjectPopupComponent_form_21_div_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 35)(2, \"label\", 31);\n    i0.ɵɵtext(3, \"Internal project manager \");\n    i0.ɵɵelementStart(4, \"sup\", 32);\n    i0.ɵɵtext(5, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"ng-select\", 43);\n    i0.ɵɵlistener(\"change\", function ProjectPopupComponent_form_21_div_2_Template_ng_select_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeInternalManager($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ProjectPopupComponent_form_21_div_2_span_7_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 44)(9, \"label\", 31);\n    i0.ɵɵtext(10, \"External project manager (multiple)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ng-select\", 45);\n    i0.ɵɵlistener(\"change\", function ProjectPopupComponent_form_21_div_2_Template_ng_select_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeexternalPM($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.managers)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"manager\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"items\", ctx_r0.externalPMs)(\"clearable\", true)(\"multiple\", true);\n  }\n}\nfunction ProjectPopupComponent_form_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 27);\n    i0.ɵɵtemplate(1, ProjectPopupComponent_form_21_ng_container_1_Template, 39, 3, \"ng-container\", 3)(2, ProjectPopupComponent_form_21_div_2_Template, 12, 7, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.projectForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedTab == \"basic\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedTab != \"basic\");\n  }\n}\nfunction ProjectPopupComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.projectForm == null ? null : ctx_r0.projectForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.id ? \"Update\" : \"Save\", \" \");\n  }\n}\nfunction ProjectPopupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_30_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.showTab(\"role\", $event));\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ProjectPopupComponent {\n  fb;\n  modal;\n  projectsService;\n  customLayoutUtilsService;\n  appService;\n  cdr;\n  id = 0; // 0 for new project, existing ID for edit\n  project = null; // Project data for editing\n  passEntry = new EventEmitter();\n  projectForm;\n  selectedTab = 'basic';\n  isLoading = false;\n  projectName = '';\n  // Data arrays\n  managers = [];\n  loginUser = {};\n  constructor(fb, modal, projectsService, customLayoutUtilsService, appService, cdr) {\n    this.fb = fb;\n    this.modal = modal;\n    this.projectsService = projectsService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.appService = appService;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.initializeForm();\n    this.loadDropdownData();\n    if (this.id !== 0 && this.project) {\n      this.populateFormForEdit();\n      this.projectName = this.project.projectName || '';\n    }\n  }\n  initializeForm() {\n    this.projectForm = this.fb.group({\n      projectName: ['', [Validators.required]],\n      internalProjectNumber: [''],\n      externalProjectNumber: [''],\n      projectDescription: [''],\n      manager: ['', [Validators.required]],\n      clientName: [''],\n      clientContactName: [''],\n      clientContactPhone: [''],\n      clientContactEmail: [''],\n      projectAddress: [''],\n      projectCity: [''],\n      projectState: [''],\n      projectZip: ['']\n    });\n  }\n  loadDropdownData() {\n    this.loadManagers();\n  }\n  loadManagers() {\n    // Load managers/users - adjust service call as needed\n    this.projectsService.getAllUsers().subscribe({\n      next: res => {\n        if (!res?.isFault) {\n          this.managers = res.responseData?.data || res.data || [];\n        }\n      },\n      error: err => {\n        console.error('Error loading managers:', err);\n        this.managers = [];\n      }\n    });\n  }\n  populateFormForEdit() {\n    if (this.project) {\n      this.projectForm.patchValue({\n        projectName: this.project.projectName || '',\n        internalProjectNumber: this.project.internalProjectNumber || '',\n        externalProjectNumber: this.project.externalProjectNumber || '',\n        projectDescription: this.project.projectDescription || '',\n        manager: this.project.managerId || this.project.manager || '',\n        clientName: this.project.clientName || '',\n        clientContactName: this.project.clientContactName || '',\n        clientContactPhone: this.project.clientContactPhone || '',\n        clientContactEmail: this.project.clientContactEmail || '',\n        projectAddress: this.project.projectAddress || '',\n        projectCity: this.project.projectCity || '',\n        projectState: this.project.projectState || '',\n        projectZip: this.project.projectZip || ''\n      });\n    }\n  }\n  controlHasError(validation, controlName) {\n    const control = this.projectForm.get(controlName);\n    return !!(control && control.hasError(validation) && (control.dirty || control.touched));\n  }\n  markFormGroupTouched() {\n    Object.keys(this.projectForm.controls).forEach(key => {\n      const control = this.projectForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  showTab(tab, event) {\n    event.preventDefault();\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'role';\n    }\n  }\n  goToPreviousTab() {\n    if (this.selectedTab === 'role') {\n      this.selectedTab = 'basic';\n    }\n  }\n  changeInternalManager(managerId) {\n    // Handle manager change if needed\n    console.log('Manager changed:', managerId);\n  }\n  save() {\n    if (this.projectForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.isLoading = true;\n    const formData = this.projectForm.value;\n    const saveObservable = this.id === 0 ? this.projectsService.createProject(formData) : this.projectsService.updateProject({\n      ...formData,\n      projectId: this.id\n    });\n    saveObservable.subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (!res?.isFault) {\n          this.customLayoutUtilsService.showSuccess(this.id === 0 ? 'Project created successfully' : 'Project updated successfully', '');\n          this.passEntry.emit(true);\n          this.modal.close(res.responseData || res);\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage || 'An error occurred while saving the project', '');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error saving project:', err);\n        this.customLayoutUtilsService.showError('An error occurred while saving the project', '');\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  static ɵfac = function ProjectPopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectPopupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i5.AppService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectPopupComponent,\n    selectors: [[\"app-project-popup\"]],\n    inputs: {\n      id: \"id\",\n      project: \"project\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 31,\n    vars: 14,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [\"class\", \"loading-overlay-inside\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center align-items-center h-100\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"class\", \"form form-label-right\", 3, \"formGroup\", 4, \"ngIf\"], [1, \"modal-footer\", \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"loading-overlay-inside\"], [1, \"text-center\"], [\"role\", \"status\", 1, \"custom-colored-spinner\", \"mb-3\"], [1, \"visually-hidden\"], [1, \"text-muted\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"h-100\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [\"class\", \"row mt-4\", 4, \"ngIf\"], [1, \"row\", \"mt-4\"], [1, \"form-group\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"formControlName\", \"projectName\", \"placeholder\", \"Project name\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"custom-error-css\", 4, \"ngIf\"], [1, \"col-xl-6\"], [\"type\", \"text\", \"formControlName\", \"internalProjectNo\", \"placeholder\", \"Internal project\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-3\"], [\"type\", \"date\", \"formControlName\", \"startDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"endDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", \"placeholder\", \"Location\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"projectDescription\", 1, \"form-control\", \"form-control-sm\"], [1, \"custom-error-css\"], [\"bindLabel\", \"userFullName\", \"name\", \"manager\", \"formControlName\", \"manager\", \"bindValue\", \"userId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [1, \"col-xl-12\", \"mt-4\"], [\"bindLabel\", \"userFullName\", \"name\", \"externalPM\", \"formControlName\", \"externalPM\", \"bindValue\", \"userId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n    template: function ProjectPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, ProjectPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, ProjectPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_i_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6);\n        i0.ɵɵtemplate(9, ProjectPopupComponent_div_9_Template, 7, 0, \"div\", 7)(10, ProjectPopupComponent_div_10_Template, 7, 0, \"div\", 8);\n        i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"ul\", 12)(15, \"li\", 13)(16, \"a\", 14);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_a_click_16_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(17, \" Project details \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"li\", 13)(19, \"a\", 14);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_a_click_19_listener($event) {\n          return ctx.showTab(\"role\", $event);\n        });\n        i0.ɵɵtext(20, \" Project manager \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵtemplate(21, ProjectPopupComponent_form_21_Template, 3, 3, \"form\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\");\n        i0.ɵɵtemplate(24, ProjectPopupComponent_button_24_Template, 2, 0, \"button\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\")(26, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_button_click_26_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵtext(27, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(28, \" \\u00A0 \");\n        i0.ɵɵtemplate(29, ProjectPopupComponent_button_29_Template, 2, 2, \"button\", 19)(30, ProjectPopupComponent_button_30_Template, 2, 0, \"button\", 20);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.projectForm && !ctx.isLoading);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.selectedTab === \"role\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.projectForm);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab != \"basic\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab != \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.NgSelectComponent],\n    styles: [\".input-group-text[_ngcontent-%COMP%] {\\n  padding-top: 5px;\\n  padding-bottom: 5px;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n}\\n\\n.mx-10px[_ngcontent-%COMP%] {\\n  margin-right: 6rem !important;\\n  margin-left: 8rem !important;\\n}\\n\\nbody[_ngcontent-%COMP%]:not(:-moz-handler-blocked)   fieldset[_ngcontent-%COMP%] {\\n  display: table-cell;\\n}\\n\\n.toggle[_ngcontent-%COMP%] {\\n  font-size: 0;\\n  display: flex;\\n  flex-flow: row nowrap;\\n  justify-content: flex-start;\\n  align-items: stretch;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 0;\\n  height: 0;\\n  position: absolute;\\n  left: -9999px;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]    + label[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0.6rem 1.5rem;\\n  box-sizing: border-box;\\n  position: relative;\\n  display: inline-block;\\n  border: solid 1px #ddd;\\n  background-color: #fff;\\n  font-size: 1.1rem;\\n  line-height: 160%;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]    + label[_ngcontent-%COMP%]:first-of-type {\\n  border-radius: 6px 0 0 6px;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]    + label[_ngcontent-%COMP%]:last-of-type {\\n  border-radius: 0 6px 6px 0;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover    + label[_ngcontent-%COMP%] {\\n  border-color: #213140;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + label[_ngcontent-%COMP%] {\\n  background-color: #11a7db;\\n  color: #fff;\\n  box-shadow: 0 0 10px rgba(17, 167, 219, 0.5);\\n  border-color: #11a7db;\\n  z-index: 1;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked:disabled    + label[_ngcontent-%COMP%] {\\n  background-color: #e1e3ea;\\n  color: white;\\n  border-color: #e1e3ea;\\n  box-shadow: none;\\n}\\n.toggle[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked:disabled    + label[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover    + label[_ngcontent-%COMP%] {\\n  border-color: #ddd;\\n}\\n\\n.color-picker[_ngcontent-%COMP%] {\\n  opacity: 1 !important;\\n  visibility: visible !important;\\n}\\n\\n.k-picker-md[_ngcontent-%COMP%] {\\n  font-size: 12px !important;\\n  line-height: 1.4285714286;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "projectName", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵtemplate", "ProjectPopupComponent_form_21_ng_container_1_span_9_Template", "ProjectPopupComponent_form_21_ng_container_1_span_18_Template", "ProjectPopupComponent_form_21_ng_container_1_span_33_Template", "ɵɵproperty", "controlHasError", "ɵɵlistener", "ProjectPopupComponent_form_21_div_2_Template_ng_select_change_6_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "changeInternalManager", "ProjectPopupComponent_form_21_div_2_span_7_Template", "ProjectPopupComponent_form_21_div_2_Template_ng_select_change_11_listener", "changeexternalPM", "managers", "externalPMs", "ProjectPopupComponent_form_21_ng_container_1_Template", "ProjectPopupComponent_form_21_div_2_Template", "projectForm", "selectedTab", "ProjectPopupComponent_button_24_Template_button_click_0_listener", "_r3", "goToPreviousTab", "ProjectPopupComponent_button_29_Template_button_click_0_listener", "_r4", "save", "invalid", "id", "ProjectPopupComponent_button_30_Template_button_click_0_listener", "_r5", "showTab", "ProjectPopupComponent", "fb", "modal", "projectsService", "customLayoutUtilsService", "appService", "cdr", "project", "passEntry", "isLoading", "loginUser", "constructor", "ngOnInit", "getLoggedInUser", "initializeForm", "loadDropdownData", "populateFormForEdit", "group", "required", "internalProjectNumber", "externalProjectNumber", "projectDescription", "manager", "clientName", "clientContactName", "clientContactPhone", "clientContactEmail", "projectAddress", "projectCity", "projectState", "projectZip", "loadManagers", "getAllUsers", "subscribe", "next", "res", "<PERSON><PERSON><PERSON>", "responseData", "data", "error", "err", "console", "patchValue", "managerId", "validation", "controlName", "control", "get", "<PERSON><PERSON><PERSON><PERSON>", "dirty", "touched", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "tab", "event", "preventDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goToNextTab", "log", "formData", "value", "saveObservable", "createProject", "updateProject", "projectId", "showSuccess", "emit", "close", "showError", "faultMessage", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "NgbActiveModal", "i3", "ProjectsService", "i4", "CustomLayoutUtilsService", "i5", "AppService", "ChangeDetectorRef", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ProjectPopupComponent_Template", "rf", "ctx", "ProjectPopupComponent_div_4_Template", "ProjectPopupComponent_div_5_Template", "ProjectPopupComponent_Template_i_click_7_listener", "dismiss", "ProjectPopupComponent_div_9_Template", "ProjectPopupComponent_div_10_Template", "ProjectPopupComponent_Template_a_click_16_listener", "ProjectPopupComponent_Template_a_click_19_listener", "ProjectPopupComponent_form_21_Template", "ProjectPopupComponent_button_24_Template", "ProjectPopupComponent_Template_button_click_26_listener", "ProjectPopupComponent_button_29_Template", "ProjectPopupComponent_button_30_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-popup\\project-popup.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-popup\\project-popup.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, ChangeDetectorRef } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { UserService } from '../../services/user.service';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { AppService } from '../../services/app.service';\r\n\r\n@Component({\r\n  selector: 'app-project-popup',\r\n  templateUrl: './project-popup.component.html',\r\n  styleUrls: ['./project-popup.component.scss']\r\n})\r\nexport class ProjectPopupComponent implements OnInit {\r\n  @Input() id: number = 0; // 0 for new project, existing ID for edit\r\n  @Input() project: any = null; // Project data for editing\r\n  @Output() passEntry = new EventEmitter<boolean>();\r\n\r\n  projectForm!: FormGroup;\r\n  selectedTab: string = 'basic';\r\n  isLoading: boolean = false;\r\n  projectName: string = '';\r\n\r\n  // Data arrays\r\n  managers: any[] = [];\r\n  loginUser: any = {};\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    public modal: NgbActiveModal,\r\n    private projectsService: ProjectsService,\r\n    private customLayoutUtilsService: CustomLayoutUtilsService,\r\n    private appService: AppService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n    this.initializeForm();\r\n    this.loadDropdownData();\r\n\r\n    if (this.id !== 0 && this.project) {\r\n      this.populateFormForEdit();\r\n      this.projectName = this.project.projectName || '';\r\n    }\r\n  }\r\n\r\n  private initializeForm(): void {\r\n    this.projectForm = this.fb.group({\r\n      projectName: ['', [Validators.required]],\r\n      internalProjectNumber: [''],\r\n      externalProjectNumber: [''],\r\n      projectDescription: [''],\r\n      manager: ['', [Validators.required]],\r\n      clientName: [''],\r\n      clientContactName: [''],\r\n      clientContactPhone: [''],\r\n      clientContactEmail: [''],\r\n      projectAddress: [''],\r\n      projectCity: [''],\r\n      projectState: [''],\r\n      projectZip: ['']\r\n    });\r\n  }\r\n\r\n  private loadDropdownData(): void {\r\n    this.loadManagers();\r\n  }\r\n\r\n  private loadManagers(): void {\r\n    // Load managers/users - adjust service call as needed\r\n    this.projectsService.getAllUsers().subscribe({\r\n      next: (res: any) => {\r\n        if (!res?.isFault) {\r\n          this.managers = res.responseData?.data || res.data || [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading managers:', err);\r\n        this.managers = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  private populateFormForEdit(): void {\r\n    if (this.project) {\r\n      this.projectForm.patchValue({\r\n        projectName: this.project.projectName || '',\r\n        internalProjectNumber: this.project.internalProjectNumber || '',\r\n        externalProjectNumber: this.project.externalProjectNumber || '',\r\n        projectDescription: this.project.projectDescription || '',\r\n        manager: this.project.managerId || this.project.manager || '',\r\n        clientName: this.project.clientName || '',\r\n        clientContactName: this.project.clientContactName || '',\r\n        clientContactPhone: this.project.clientContactPhone || '',\r\n        clientContactEmail: this.project.clientContactEmail || '',\r\n        projectAddress: this.project.projectAddress || '',\r\n        projectCity: this.project.projectCity || '',\r\n        projectState: this.project.projectState || '',\r\n        projectZip: this.project.projectZip || ''\r\n      });\r\n    }\r\n  }\r\n\r\n  controlHasError(validation: string, controlName: string): boolean {\r\n    const control = this.projectForm.get(controlName);\r\n    return !!(control && control.hasError(validation) && (control.dirty || control.touched));\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.projectForm.controls).forEach(key => {\r\n      const control = this.projectForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  showTab(tab: string, event: any): void {\r\n    event.preventDefault();\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  goToNextTab(): void {\r\n    if (this.selectedTab === 'basic') {\r\n      this.selectedTab = 'role';\r\n    }\r\n  }\r\n\r\n  goToPreviousTab(): void {\r\n    if (this.selectedTab === 'role') {\r\n      this.selectedTab = 'basic';\r\n    }\r\n  }\r\n\r\n  changeInternalManager(managerId: any): void {\r\n    // Handle manager change if needed\r\n    console.log('Manager changed:', managerId);\r\n  }\r\n\r\n  save(): void {\r\n    if (this.projectForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    const formData = this.projectForm.value;\r\n\r\n    const saveObservable = this.id === 0\r\n      ? this.projectsService.createProject(formData)\r\n      : this.projectsService.updateProject({ ...formData, projectId: this.id });\r\n\r\n    saveObservable.subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        if (!res?.isFault) {\r\n          this.customLayoutUtilsService.showSuccess(\r\n            this.id === 0 ? 'Project created successfully' : 'Project updated successfully',\r\n            ''\r\n          );\r\n          this.passEntry.emit(true);\r\n          this.modal.close(res.responseData || res);\r\n        } else {\r\n          this.customLayoutUtilsService.showError(\r\n            res.faultMessage || 'An error occurred while saving the project',\r\n            ''\r\n          );\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err) => {\r\n        this.isLoading = false;\r\n        console.error('Error saving project:', err);\r\n        this.customLayoutUtilsService.showError(\r\n          'An error occurred while saving the project',\r\n          ''\r\n        );\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n}", "<div class=\"modal-content h-auto\">\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container>\n        <div *ngIf=\"id === 0\">Add Project</div>\n        <div *ngIf=\"id !== 0\">Edit Project - {{ projectName }}</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i\n        class=\"fa-solid fs-2 fa-xmark text-white\"\n        (click)=\"modal.dismiss()\"\n      ></i>\n    </div>\n  </div>\n\n  <div\n    class=\"modal-body large-modal-body\"\n    \n  >\n    <!--   max-height: calc(100vh - 250px);\n      overflow-y: auto;\n      position: relative; -->\n    <!-- Loading Overlay -->\n    <div *ngIf=\"isLoading\" class=\"loading-overlay-inside\">\n      <div class=\"text-center\">\n        <div class=\"custom-colored-spinner mb-3\" role=\"status\">\n          <span class=\"visually-hidden\">Loading...</span>\n        </div>\n        <div class=\"text-muted\">Loading project data...</div>\n      </div>\n    </div>\n\n    <!-- Initial loading state for form -->\n    <div *ngIf=\"!projectForm && !isLoading\" class=\"d-flex justify-content-center align-items-center h-100\">\n      <div class=\"text-center\">\n        <div class=\"custom-colored-spinner mb-3\" role=\"status\">\n          <span class=\"visually-hidden\">Loading...</span>\n        </div>\n        <div class=\"text-muted\">Initializing form...</div>\n      </div>\n    </div>\n\n    <div class=\"row\">\n      <div class=\"col-xl-12\">\n        <div class=\"d-flex\">\n          <ul\n            class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\"\n          >\n            <li class=\"nav-item\">\n              <a\n                class=\"nav-link text-active-primary me-6 cursor-pointer\"\n                data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'basic' }\"\n                (click)=\"showTab('basic', $event)\"\n              >\n                Project details\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a\n                class=\"nav-link text-active-primary me-6 cursor-pointer\"\n                data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'role' }\"\n                (click)=\"showTab('role', $event)\"\n              >\n                Project manager\n              </a>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <form class=\"form form-label-right\" [formGroup]=\"projectForm\" *ngIf=\"projectForm\">\n      <ng-container *ngIf=\"selectedTab == 'basic'\">\n        <div class=\"row mt-4\">\n          <!-- Project Name -->\n\n          <div class=\"col-xl-12\">\n            <div class=\"form-group\">\n              <label class=\"fw-bold form-label mb-2\">Project name<sup class=\"text-danger\">*</sup></label>\n              <input\n                type=\"text\"\n                class=\"form-control form-control-sm\"\n                formControlName=\"projectName\"\n                placeholder=\"Project name\"\n              />\n               <span\n                class=\"custom-error-css\"\n                *ngIf=\"controlHasError('required', 'projectName')\"\n                >Required Field</span\n              >\n            </div>\n          </div>\n\n          <!-- Internal Project Number -->\n\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-6\">\n            <div class=\"form-group\">\n              <label class=\"fw-bold form-label mb-2\"\n                >Internal project # <sup class=\"text-danger\">*</sup></label\n              >\n              <input\n                type=\"text\"\n                class=\"form-control form-control-sm\"\n                formControlName=\"internalProjectNo\"\n                placeholder=\"Internal project\"\n              />\n              <span\n                class=\"custom-error-css\"\n                *ngIf=\"controlHasError('required', 'internalProjectNo')\"\n                >Required Field</span\n              >\n            </div>\n          </div>\n          <!-- Start Date -->\n          <div class=\"col-xl-3\">\n            <label class=\"fw-bold form-label mb-2\">Start date</label>\n            <input\n              type=\"date\"\n              class=\"form-control form-control-sm\"\n              formControlName=\"startDate\"\n            />\n          </div>\n\n          <!-- End Date -->\n          <div class=\"col-xl-3\">\n            <label class=\"fw-bold form-label mb-2\">End date</label>\n            <input\n              type=\"date\"\n              class=\"form-control form-control-sm\"\n              formControlName=\"endDate\"\n            />\n          </div>\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Location<sup class=\"text-danger\">*</sup></label>\n            <input\n              type=\"text\"\n              class=\"form-control form-control-sm\"\n              formControlName=\"location\"\n              placeholder=\"Location\"\n            />\n             <span\n                class=\"custom-error-css\"\n                *ngIf=\"controlHasError('required', 'location')\"\n                >Required Field</span\n              >\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Description</label>\n            <textarea\n              class=\"form-control form-control-sm\"\n              rows=\"3\"\n              formControlName=\"projectDescription\"\n            ></textarea>\n          </div>\n        </div>\n      </ng-container>\n\n      <div class=\"row mt-4\" *ngIf=\"selectedTab != 'basic'\">\n        <!-- Location -->\n\n        <!-- Manager -->\n        <div class=\"col-xl-6\">\n          <label class=\"fw-bold form-label mb-2\"\n            >Internal project manager <sup class=\"text-danger\">*</sup></label\n          >\n          <ng-select\n            [items]=\"managers\"\n            [clearable]=\"false\"\n            [multiple]=\"false\"\n            bindLabel=\"userFullName\"\n            name=\"manager\"\n            formControlName=\"manager\"\n            bindValue=\"userId\"\n            (change)=\"changeInternalManager($event)\"\n            placeholder=\"Select an option\"\n          >\n          </ng-select>\n          <span\n            class=\"custom-error-css\"\n            *ngIf=\"controlHasError('required', 'manager')\"\n            >Required Field</span\n          >\n        </div>\n\n        <!-- External PM -->\n        <div class=\"col-xl-12 mt-4\">\n          <label class=\"fw-bold form-label mb-2\"\n            >External project manager (multiple)</label\n          >\n          <ng-select\n            [items]=\"externalPMs\"\n            [clearable]=\"true\"\n            [multiple]=\"true\"\n            bindLabel=\"userFullName\"\n            name=\"externalPM\"\n            formControlName=\"externalPM\"\n            bindValue=\"userId\"\n            (change)=\"changeexternalPM($event)\"\n            placeholder=\"Select an option\"\n          >\n          </ng-select>\n        </div>\n      </div>\n    </form>\n  </div>\n\n<div class=\"modal-footer d-flex justify-content-between\">\n  <!-- Left Side -->\n  <div>\n    <button\n      *ngIf=\"selectedTab != 'basic'\"\n      type=\"button\"\n      class=\"btn btn-secondary btn-sm btn-elevate\"\n      (click)=\"goToPreviousTab()\"\n    >\n      Previous\n    </button>\n  </div>\n\n  <!-- Right Side -->\n  <div>\n    <button\n      class=\"btn btn-danger btn-sm btn-elevate mr-2\"\n      (click)=\"modal.dismiss()\"\n    >\n      Cancel\n    </button>\n    &nbsp;\n    <button\n      *ngIf=\"selectedTab != 'basic'\"\n      type=\"button\"\n      class=\"btn btn-primary btn-sm\"\n      [disabled]=\"projectForm?.invalid\"\n      (click)=\"save()\"\n    >\n      {{ id ? \"Update\" : \"Save\" }}\n    </button>\n    <button\n      *ngIf=\"selectedTab == 'basic'\"\n      type=\"button\"\n      class=\"btn btn-primary btn-sm\"\n      (click)=\"showTab('role', $event)\"\n    >\n      Next\n    </button>\n  </div>\n</div>\n\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAmC,eAAe;AACjG,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;ICG3DC,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACvCH,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,oBAAAC,MAAA,CAAAC,WAAA,KAAgC;;;;;IAsBpDP,EAHN,CAAAC,cAAA,cAAsD,cAC3B,cACgC,eACvB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAEnDF,EAFmD,CAAAG,YAAA,EAAM,EACjD,EACF;;;;;IAMAH,EAHN,CAAAC,cAAA,cAAuG,cAC5E,cACgC,eACvB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAEhDF,EAFgD,CAAAG,YAAA,EAAM,EAC9C,EACF;;;;;IA+CKH,EAAA,CAAAC,cAAA,eAGE;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IAoBDH,EAAA,CAAAC,cAAA,eAGG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IA8BFH,EAAA,CAAAC,cAAA,eAGI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IA3ETH,EAAA,CAAAQ,uBAAA,GAA6C;IAMrCR,EALN,CAAAC,cAAA,cAAsB,cAGG,cACG,gBACiB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IAC3FH,EAAA,CAAAS,SAAA,gBAKE;IACDT,EAAA,CAAAU,UAAA,IAAAC,4DAAA,mBAGE;IAOTX,EALI,CAAAG,YAAA,EAAM,EACF,EAIF;IAKAH,EAHN,CAAAC,cAAA,eAAsB,eACE,eACI,iBAEnB;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EACrD;IACDH,EAAA,CAAAS,SAAA,iBAKE;IACFT,EAAA,CAAAU,UAAA,KAAAE,6DAAA,mBAGG;IAGPZ,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAS,SAAA,iBAIE;IACJT,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvDH,EAAA,CAAAS,SAAA,iBAIE;IACJT,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IACvFH,EAAA,CAAAS,SAAA,iBAKE;IACDT,EAAA,CAAAU,UAAA,KAAAG,6DAAA,mBAGI;IAGTb,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAS,SAAA,oBAIY;IAEhBT,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAzEGH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,4BAAgD;IAwBhDf,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,kCAAsD;IAkCtDf,EAAA,CAAAI,SAAA,IAA6C;IAA7CJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,yBAA6C;;;;;IAsCpDf,EAAA,CAAAC,cAAA,eAGG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;;IAnBDH,EALJ,CAAAC,cAAA,cAAqD,cAI7B,gBAEjB;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAC3D;IACDH,EAAA,CAAAC,cAAA,oBAUC;IAFCD,EAAA,CAAAgB,UAAA,oBAAAC,yEAAAC,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAUhB,MAAA,CAAAiB,qBAAA,CAAAL,MAAA,CAA6B;IAAA,EAAC;IAG1ClB,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAU,UAAA,IAAAc,mDAAA,mBAGG;IAELxB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA4B,gBAEvB;IAAAD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EACrC;IACDH,EAAA,CAAAC,cAAA,qBAUC;IAFCD,EAAA,CAAAgB,UAAA,oBAAAS,0EAAAP,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAUhB,MAAA,CAAAoB,gBAAA,CAAAR,MAAA,CAAwB;IAAA,EAAC;IAKzClB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;;;;IApCAH,EAAA,CAAAI,SAAA,GAAkB;IAElBJ,EAFA,CAAAc,UAAA,UAAAR,MAAA,CAAAqB,QAAA,CAAkB,oBACC,mBACD;IAWjB3B,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,wBAA4C;IAW7Cf,EAAA,CAAAI,SAAA,GAAqB;IAErBJ,EAFA,CAAAc,UAAA,UAAAR,MAAA,CAAAsB,WAAA,CAAqB,mBACH,kBACD;;;;;IA/HzB5B,EAAA,CAAAC,cAAA,eAAkF;IA4FhFD,EA3FA,CAAAU,UAAA,IAAAmB,qDAAA,2BAA6C,IAAAC,4CAAA,mBA2FQ;IA8CvD9B,EAAA,CAAAG,YAAA,EAAO;;;;IA1I6BH,EAAA,CAAAc,UAAA,cAAAR,MAAA,CAAAyB,WAAA,CAAyB;IAC5C/B,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA0B,WAAA,YAA4B;IA2FpBhC,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA0B,WAAA,YAA4B;;;;;;IAoDrDhC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAgB,UAAA,mBAAAiB,iEAAA;MAAAjC,EAAA,CAAAmB,aAAA,CAAAe,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAA6B,eAAA,EAAiB;IAAA,EAAC;IAE3BnC,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAYTH,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAgB,UAAA,mBAAAoB,iEAAA;MAAApC,EAAA,CAAAmB,aAAA,CAAAkB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAAgC,IAAA,EAAM;IAAA,EAAC;IAEhBtC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAc,UAAA,aAAAR,MAAA,CAAAyB,WAAA,kBAAAzB,MAAA,CAAAyB,WAAA,CAAAQ,OAAA,CAAiC;IAGjCvC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAkC,EAAA,0BACF;;;;;;IACAxC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAgB,UAAA,mBAAAyB,iEAAAvB,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAuB,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAAqC,OAAA,CAAQ,MAAM,EAAAzB,MAAA,CAAS;IAAA,EAAC;IAEjClB,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADhPb,OAAM,MAAOyC,qBAAqB;EAetBC,EAAA;EACDC,KAAA;EACCC,eAAA;EACAC,wBAAA;EACAC,UAAA;EACAC,GAAA;EAnBDV,EAAE,GAAW,CAAC,CAAC,CAAC;EAChBW,OAAO,GAAQ,IAAI,CAAC,CAAC;EACpBC,SAAS,GAAG,IAAItD,YAAY,EAAW;EAEjDiC,WAAW;EACXC,WAAW,GAAW,OAAO;EAC7BqB,SAAS,GAAY,KAAK;EAC1B9C,WAAW,GAAW,EAAE;EAExB;EACAoB,QAAQ,GAAU,EAAE;EACpB2B,SAAS,GAAQ,EAAE;EAEnBC,YACUV,EAAe,EAChBC,KAAqB,EACpBC,eAAgC,EAChCC,wBAAkD,EAClDC,UAAsB,EACtBC,GAAsB;IALtB,KAAAL,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,GAAG,GAAHA,GAAG;EACV;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACF,SAAS,GAAG,IAAI,CAACL,UAAU,CAACQ,eAAe,EAAE;IAClD,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,IAAI,CAACnB,EAAE,KAAK,CAAC,IAAI,IAAI,CAACW,OAAO,EAAE;MACjC,IAAI,CAACS,mBAAmB,EAAE;MAC1B,IAAI,CAACrD,WAAW,GAAG,IAAI,CAAC4C,OAAO,CAAC5C,WAAW,IAAI,EAAE;IACnD;EACF;EAEQmD,cAAcA,CAAA;IACpB,IAAI,CAAC3B,WAAW,GAAG,IAAI,CAACc,EAAE,CAACgB,KAAK,CAAC;MAC/BtD,WAAW,EAAE,CAAC,EAAE,EAAE,CAACR,UAAU,CAAC+D,QAAQ,CAAC,CAAC;MACxCC,qBAAqB,EAAE,CAAC,EAAE,CAAC;MAC3BC,qBAAqB,EAAE,CAAC,EAAE,CAAC;MAC3BC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACnE,UAAU,CAAC+D,QAAQ,CAAC,CAAC;MACpCK,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,UAAU,EAAE,CAAC,EAAE;KAChB,CAAC;EACJ;EAEQf,gBAAgBA,CAAA;IACtB,IAAI,CAACgB,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAClB;IACA,IAAI,CAAC5B,eAAe,CAAC6B,WAAW,EAAE,CAACC,SAAS,CAAC;MAC3CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB,IAAI,CAACrD,QAAQ,GAAGoD,GAAG,CAACE,YAAY,EAAEC,IAAI,IAAIH,GAAG,CAACG,IAAI,IAAI,EAAE;QAC1D;MACF,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEC,GAAG,CAAC;QAC7C,IAAI,CAACzD,QAAQ,GAAG,EAAE;MACpB;KACD,CAAC;EACJ;EAEQiC,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACT,OAAO,EAAE;MAChB,IAAI,CAACpB,WAAW,CAACuD,UAAU,CAAC;QAC1B/E,WAAW,EAAE,IAAI,CAAC4C,OAAO,CAAC5C,WAAW,IAAI,EAAE;QAC3CwD,qBAAqB,EAAE,IAAI,CAACZ,OAAO,CAACY,qBAAqB,IAAI,EAAE;QAC/DC,qBAAqB,EAAE,IAAI,CAACb,OAAO,CAACa,qBAAqB,IAAI,EAAE;QAC/DC,kBAAkB,EAAE,IAAI,CAACd,OAAO,CAACc,kBAAkB,IAAI,EAAE;QACzDC,OAAO,EAAE,IAAI,CAACf,OAAO,CAACoC,SAAS,IAAI,IAAI,CAACpC,OAAO,CAACe,OAAO,IAAI,EAAE;QAC7DC,UAAU,EAAE,IAAI,CAAChB,OAAO,CAACgB,UAAU,IAAI,EAAE;QACzCC,iBAAiB,EAAE,IAAI,CAACjB,OAAO,CAACiB,iBAAiB,IAAI,EAAE;QACvDC,kBAAkB,EAAE,IAAI,CAAClB,OAAO,CAACkB,kBAAkB,IAAI,EAAE;QACzDC,kBAAkB,EAAE,IAAI,CAACnB,OAAO,CAACmB,kBAAkB,IAAI,EAAE;QACzDC,cAAc,EAAE,IAAI,CAACpB,OAAO,CAACoB,cAAc,IAAI,EAAE;QACjDC,WAAW,EAAE,IAAI,CAACrB,OAAO,CAACqB,WAAW,IAAI,EAAE;QAC3CC,YAAY,EAAE,IAAI,CAACtB,OAAO,CAACsB,YAAY,IAAI,EAAE;QAC7CC,UAAU,EAAE,IAAI,CAACvB,OAAO,CAACuB,UAAU,IAAI;OACxC,CAAC;IACJ;EACF;EAEA3D,eAAeA,CAACyE,UAAkB,EAAEC,WAAmB;IACrD,MAAMC,OAAO,GAAG,IAAI,CAAC3D,WAAW,CAAC4D,GAAG,CAACF,WAAW,CAAC;IACjD,OAAO,CAAC,EAAEC,OAAO,IAAIA,OAAO,CAACE,QAAQ,CAACJ,UAAU,CAAC,KAAKE,OAAO,CAACG,KAAK,IAAIH,OAAO,CAACI,OAAO,CAAC,CAAC;EAC1F;EAEQC,oBAAoBA,CAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClE,WAAW,CAACmE,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMV,OAAO,GAAG,IAAI,CAAC3D,WAAW,CAAC4D,GAAG,CAACS,GAAG,CAAC;MACzCV,OAAO,EAAEW,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA1D,OAAOA,CAAC2D,GAAW,EAAEC,KAAU;IAC7BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACxE,WAAW,GAAGsE,GAAG;IACtB,IAAI,CAACpD,GAAG,CAACuD,YAAY,EAAE;EACzB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC1E,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,MAAM;IAC3B;EACF;EAEAG,eAAeA,CAAA;IACb,IAAI,IAAI,CAACH,WAAW,KAAK,MAAM,EAAE;MAC/B,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;EACF;EAEAT,qBAAqBA,CAACgE,SAAc;IAClC;IACAF,OAAO,CAACsB,GAAG,CAAC,kBAAkB,EAAEpB,SAAS,CAAC;EAC5C;EAEAjD,IAAIA,CAAA;IACF,IAAI,IAAI,CAACP,WAAW,CAACQ,OAAO,EAAE;MAC5B,IAAI,CAACwD,oBAAoB,EAAE;MAC3B;IACF;IAEA,IAAI,CAAC1C,SAAS,GAAG,IAAI;IACrB,MAAMuD,QAAQ,GAAG,IAAI,CAAC7E,WAAW,CAAC8E,KAAK;IAEvC,MAAMC,cAAc,GAAG,IAAI,CAACtE,EAAE,KAAK,CAAC,GAChC,IAAI,CAACO,eAAe,CAACgE,aAAa,CAACH,QAAQ,CAAC,GAC5C,IAAI,CAAC7D,eAAe,CAACiE,aAAa,CAAC;MAAE,GAAGJ,QAAQ;MAAEK,SAAS,EAAE,IAAI,CAACzE;IAAE,CAAE,CAAC;IAE3EsE,cAAc,CAACjC,SAAS,CAAC;MACvBC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC1B,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC0B,GAAG,EAAEC,OAAO,EAAE;UACjB,IAAI,CAAChC,wBAAwB,CAACkE,WAAW,CACvC,IAAI,CAAC1E,EAAE,KAAK,CAAC,GAAG,8BAA8B,GAAG,8BAA8B,EAC/E,EAAE,CACH;UACD,IAAI,CAACY,SAAS,CAAC+D,IAAI,CAAC,IAAI,CAAC;UACzB,IAAI,CAACrE,KAAK,CAACsE,KAAK,CAACrC,GAAG,CAACE,YAAY,IAAIF,GAAG,CAAC;QAC3C,CAAC,MAAM;UACL,IAAI,CAAC/B,wBAAwB,CAACqE,SAAS,CACrCtC,GAAG,CAACuC,YAAY,IAAI,4CAA4C,EAChE,EAAE,CACH;QACH;QACA,IAAI,CAACpE,GAAG,CAACuD,YAAY,EAAE;MACzB,CAAC;MACDtB,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAAC/B,SAAS,GAAG,KAAK;QACtBgC,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;QAC3C,IAAI,CAACpC,wBAAwB,CAACqE,SAAS,CACrC,4CAA4C,EAC5C,EAAE,CACH;QACD,IAAI,CAACnE,GAAG,CAACuD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;;qCAvKW7D,qBAAqB,EAAA5C,EAAA,CAAAuH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzH,EAAA,CAAAuH,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3H,EAAA,CAAAuH,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA7H,EAAA,CAAAuH,iBAAA,CAAAO,EAAA,CAAAC,wBAAA,GAAA/H,EAAA,CAAAuH,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAAjI,EAAA,CAAAuH,iBAAA,CAAAvH,EAAA,CAAAkI,iBAAA;EAAA;;UAArBtF,qBAAqB;IAAAuF,SAAA;IAAAC,MAAA;MAAA5F,EAAA;MAAAW,OAAA;IAAA;IAAAkF,OAAA;MAAAjF,SAAA;IAAA;IAAAkF,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCX9B3I,EAFJ,CAAAC,cAAA,aAAkC,aACW,aACR;QAC/BD,EAAA,CAAAQ,uBAAA,GAAc;QAEZR,EADA,CAAAU,UAAA,IAAAmI,oCAAA,iBAAsB,IAAAC,oCAAA,iBACA;;QAE1B9I,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAItB;QADCD,EAAA,CAAAgB,UAAA,mBAAA+H,kDAAA;UAAA,OAASH,GAAA,CAAA9F,KAAA,CAAAkG,OAAA,EAAe;QAAA,EAAC;QAG/BhJ,EAFK,CAAAG,YAAA,EAAI,EACD,EACF;QAENH,EAAA,CAAAC,cAAA,aAGC;QAeCD,EAVA,CAAAU,UAAA,IAAAuI,oCAAA,iBAAsD,KAAAC,qCAAA,iBAUiD;QAgB7FlJ,EAPV,CAAAC,cAAA,cAAiB,eACQ,eACD,cAGjB,cACsB,aAMlB;QADCD,EAAA,CAAAgB,UAAA,mBAAAmI,mDAAAjI,MAAA;UAAA,OAAS0H,GAAA,CAAAjG,OAAA,CAAQ,OAAO,EAAAzB,MAAA,CAAS;QAAA,EAAC;QAElClB,EAAA,CAAAE,MAAA,yBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAMlB;QADCD,EAAA,CAAAgB,UAAA,mBAAAoI,mDAAAlI,MAAA;UAAA,OAAS0H,GAAA,CAAAjG,OAAA,CAAQ,MAAM,EAAAzB,MAAA,CAAS;QAAA,EAAC;QAEjClB,EAAA,CAAAE,MAAA,yBACF;QAKVF,EALU,CAAAG,YAAA,EAAI,EACD,EACF,EACD,EACF,EACF;QAENH,EAAA,CAAAU,UAAA,KAAA2I,sCAAA,mBAAkF;QA2IpFrJ,EAAA,CAAAG,YAAA,EAAM;QAINH,EAFF,CAAAC,cAAA,eAAyD,WAElD;QACHD,EAAA,CAAAU,UAAA,KAAA4I,wCAAA,qBAKC;QAGHtJ,EAAA,CAAAG,YAAA,EAAM;QAIJH,EADF,CAAAC,cAAA,WAAK,kBAIF;QADCD,EAAA,CAAAgB,UAAA,mBAAAuI,wDAAA;UAAA,OAASX,GAAA,CAAA9F,KAAA,CAAAkG,OAAA,EAAe;QAAA,EAAC;QAEzBhJ,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAE,MAAA,gBACA;QASAF,EATA,CAAAU,UAAA,KAAA8I,wCAAA,qBAMC,KAAAC,wCAAA,qBAQA;QAMLzJ,EAHE,CAAAG,YAAA,EAAM,EACF,EAEA;;;QA7PQH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAc,UAAA,SAAA8H,GAAA,CAAApG,EAAA,OAAc;QACdxC,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAc,UAAA,SAAA8H,GAAA,CAAApG,EAAA,OAAc;QAmBlBxC,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAc,UAAA,SAAA8H,GAAA,CAAAvF,SAAA,CAAe;QAUfrD,EAAA,CAAAI,SAAA,EAAgC;QAAhCJ,EAAA,CAAAc,UAAA,UAAA8H,GAAA,CAAA7G,WAAA,KAAA6G,GAAA,CAAAvF,SAAA,CAAgC;QAmB1BrD,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAA0J,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAA5G,WAAA,cAA+C;QAU/ChC,EAAA,CAAAI,SAAA,GAA8C;QAA9CJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAA0J,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAA5G,WAAA,aAA8C;QAWKhC,EAAA,CAAAI,SAAA,GAAiB;QAAjBJ,EAAA,CAAAc,UAAA,SAAA8H,GAAA,CAAA7G,WAAA,CAAiB;QAiJ7E/B,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA8H,GAAA,CAAA5G,WAAA,YAA4B;QAmB5BhC,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA8H,GAAA,CAAA5G,WAAA,YAA4B;QAS5BhC,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA8H,GAAA,CAAA5G,WAAA,YAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}