{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport * as _ from 'lodash';\nimport { each } from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/http-utils.service\";\nimport * as i3 from \"../../services/custom-layout.utils.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"../../services/user.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../shared/checkbox-group/checkbox.component\";\nimport * as i9 from \"../../shared/checkbox-group/checkbox-group.component\";\nfunction RoleEditComponent_ng_container_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Role - \", ctx_r0.roleName, \"\");\n  }\n}\nfunction RoleEditComponent_ng_container_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1, \"Add Role\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RoleEditComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, RoleEditComponent_ng_container_3_div_1_Template, 2, 1, \"div\", 27)(2, RoleEditComponent_ng_container_3_div_2_Template, 2, 0, \"div\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id !== 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id === 0);\n  }\n}\nfunction RoleEditComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RoleEditComponent_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30)(2, \"label\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 30)(5, \"checkbox-group\", 32);\n    i0.ɵɵelement(6, \"checkbox\", 33)(7, \"checkbox\", 34)(8, \"checkbox\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const p_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(p_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formControlName\", p_r2);\n  }\n}\nexport class RoleEditComponent {\n  modal;\n  cdr;\n  httpUtilService;\n  layoutUtilService;\n  fb;\n  appService;\n  UserService;\n  id; // RolePID -input from the role component\n  permissions; // Permissions - input from the role component\n  passEntry = new EventEmitter(); // output tp the role component\n  editroles; //Initialize the form\n  role = {}; // field to save the role details\n  permissionArray = []; // field to save the initial values of default permissions\n  perNameArray = []; // field to save the Name's  of the Permissions in a array\n  rolePermissions = []; // field to save the final values of the Permissions for the role\n  Permissions = []; //store permission array\n  loginUser = {}; //store local storage data of logged in user\n  selectedpermission = []; //store permission array based on role name changes\n  roleName = '';\n  constructor(modal, cdr, httpUtilService, layoutUtilService, fb, appService, UserService) {\n    this.modal = modal;\n    this.cdr = cdr;\n    this.httpUtilService = httpUtilService;\n    this.layoutUtilService = layoutUtilService;\n    this.fb = fb;\n    this.appService = appService;\n    this.UserService = UserService;\n  }\n  // Method to handle cancel button click\n  onCancelClick() {\n    // Reset loading state when cancel is clicked\n    this.httpUtilService.loadingSubject.next(false);\n    this.modal.dismiss();\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.permissionArray = this.permissions; // get the initial values of the default permissions\n    this.loadForm(); // load the form variables\n    if (this.id !== 0) {\n      //get the Role details and patch the values to the form fields\n      this.patchForm();\n    }\n  }\n  /**\n  * Form initialization\n  * Default params, validators\n  */\n  loadForm() {\n    const formGroup = {};\n    //assign the form fields\n    formGroup['roleName'] = new FormControl('', Validators.compose([Validators.required]));\n    // formGroup['systemRole'] = new FormControl(null,Validators.compose([Validators.required]));\n    formGroup['description'] = new FormControl('');\n    // formGroup['status'] = new FormControl('');\n    let pArray = [];\n    // assign the form fields for Permissions\n    this.permissionArray.forEach(perm => {\n      pArray.push(perm.Name);\n      formGroup[perm.Name] = new FormControl('');\n    });\n    //Group the form field and assign it to the form group\n    this.editroles = this.fb.group(formGroup);\n    // field to display the Permission in UI\n    this.perNameArray = pArray;\n  }\n  //function to patch field from API\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.getRole({\n      roleId: this.id\n    }).subscribe(role => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!role.isFault) {\n        let rPerms = JSON.parse(role.responseData.rolePermissions);\n        this.roleName = role.responseData.roleName;\n        this.editroles.patchValue({\n          roleName: role.responseData.roleName,\n          description: role.responseData.description\n          // systemRole:role.responseData.DefaultRoleId,\n          // status:role.responseData.status\n        });\n        // patch the permission values\n        let self = this;\n        each(rPerms, r => {\n          _.forEach(r, function (value, key) {\n            self.editroles.patchValue({\n              [key]: value\n            });\n          });\n        });\n      }\n    });\n  }\n  //function to change permission array based on role name changes\n  changeSystemAccess(event) {\n    this.selectedpermission = JSON.parse(event.Permissions);\n    let perArray = [];\n    let self = this;\n    each(this.selectedpermission, r => {\n      _.forEach(r, function (value, key) {\n        self.editroles.patchValue({\n          [key]: value\n        });\n      });\n    });\n  }\n  //Form submit\n  save() {\n    let roleData = this.prepareRole();\n    console.log('roleData ', roleData);\n    const controls = this.editroles.controls;\n    if (this.editroles.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      this.layoutUtilService.showError('Please fill all required fields', '');\n      return;\n    }\n    if (this.id === 0) {\n      this.create(roleData);\n    } else {\n      this.edit(roleData);\n    }\n  }\n  // API to update the role details based on the RolePID\n  edit(roleData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.editRole(roleData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n      }\n    });\n  }\n  // API to save new role details\n  create(roleData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.addRole(roleData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n      }\n    });\n  }\n  //function to map input form fields to API fields\n  prepareRole() {\n    const formData = this.editroles.value;\n    this.role.roleId = this.id;\n    // this.role.DefaultRoleId = formData.systemRole;\n    // this.role.DefaultRoleId = '';\n    this.role.roleName = formData.roleName;\n    this.role.description = formData.description;\n    this.role.loggedInUserId = this.loginUser.userId;\n    this.role.status = 'Active';\n    let controls = this.editroles.controls;\n    let perArray = [];\n    let self = this;\n    console.log('permissionArray ', self.permissionArray);\n    _.forEach(controls, function (value, key) {\n      console.log('valeu ', value, 'Key ', key);\n      let rjson = _.find(self.permissionArray, function (o) {\n        return o.Name === key;\n      });\n      if (rjson !== undefined) {\n        let permissionJson = self.getPermissionJson(rjson, value);\n        perArray.push(permissionJson);\n      }\n    });\n    this.role.rolePermissions = perArray;\n    return this.role;\n  }\n  // format the permission data for each permission\n  getPermissionJson(permission, controls) {\n    let newPermission = {\n      [permission.Name]: controls.value\n    };\n    return newPermission;\n  }\n  static ɵfac = function RoleEditComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RoleEditComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.HttpUtilsService), i0.ɵɵdirectiveInject(i3.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.AppService), i0.ɵɵdirectiveInject(i6.UserService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RoleEditComponent,\n    selectors: [[\"app-role-edit\"]],\n    inputs: {\n      id: \"id\",\n      permissions: \"permissions\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 47,\n    vars: 5,\n    consts: [[1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"example-modal-sizes-title-lg\", 1, \"modal-title\"], [4, \"ngIf\"], [1, \"float-right\", \"cursor-pointer\", \"ir-12\"], [1, \"btn\", \"btn-icon\", \"btn-sm\", \"mx-1\", 3, \"click\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\"], [1, \"modal-body\", \"large-modal-body\"], [1, \"overlay-layer\", \"bg-transparent\"], [1, \"spinner\", \"spinner-lg\", \"spinner-success\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"card-body\", \"response-list\"], [1, \"form-group\", \"row\", \"mb-4\", \"px-10\", \"pt-2\"], [1, \"col-lg-12\"], [1, \"fw-semibold\", \"fs-6\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"name\", \"roleName\", \"placeholder\", \"Type Here\", \"autocomplete\", \"off\", \"formControlName\", \"roleName\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"custom-error-css\", 4, \"ngIf\"], [\"type\", \"text\", \"name\", \"description\", \"placeholder\", \"Type Here\", \"rows\", \"2\", \"autocomplete\", \"off\", \"formControlName\", \"description\", 1, \"form-control\"], [1, \"col-12\", \"d-flex\", \"justify-content-start\"], [1, \"w-100\"], [1, \"p-1\", \"fw-bold\", \"fs-6\", 2, \"width\", \"390px !important\"], [1, \"d-flex\", \"justify-content-between\"], [4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-elevate\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"fw-bold fs-2 text-white\", 4, \"ngIf\"], [1, \"fw-bold\", \"fs-2\", \"text-white\"], [1, \"custom-error-css\"], [1, \"p-1\"], [1, \"fw-semibold\", \"fs-6\"], [3, \"formControlName\"], [\"value\", \"Read\"], [\"value\", \"Write\", 2, \"padding-left\", \"145px\"], [\"value\", \"Delete\", 2, \"padding-left\", \"145px\"]],\n    template: function RoleEditComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵtemplate(3, RoleEditComponent_ng_container_3_Template, 3, 2, \"ng-container\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"a\", 5);\n        i0.ɵɵlistener(\"click\", function RoleEditComponent_Template_a_click_5_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelement(6, \"i\", 6);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 7);\n        i0.ɵɵelementContainerStart(8);\n        i0.ɵɵelementStart(9, \"div\", 8);\n        i0.ɵɵelement(10, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(11);\n        i0.ɵɵelementStart(12, \"form\", 10)(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"label\", 14);\n        i0.ɵɵtext(17, \"Name\");\n        i0.ɵɵelementStart(18, \"sup\", 15);\n        i0.ɵɵtext(19, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(20, \"input\", 16);\n        i0.ɵɵtemplate(21, RoleEditComponent_span_21_Template, 2, 0, \"span\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 12)(23, \"div\", 13)(24, \"label\", 14);\n        i0.ɵɵtext(25, \"Description\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(26, \"textarea\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 12)(28, \"div\", 19)(29, \"table\", 20)(30, \"th\", 21);\n        i0.ɵɵtext(31, \"Permissions\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"th\", 21)(33, \"div\", 22)(34, \"span\");\n        i0.ɵɵtext(35, \"Read\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"span\");\n        i0.ɵɵtext(37, \"Write\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"span\");\n        i0.ɵɵtext(39, \"Delete\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(40, RoleEditComponent_tr_40_Template, 9, 2, \"tr\", 23);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"div\", 24)(42, \"button\", 25);\n        i0.ɵɵlistener(\"click\", function RoleEditComponent_Template_button_click_42_listener() {\n          return ctx.onCancelClick();\n        });\n        i0.ɵɵtext(43, \"Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerStart(44);\n        i0.ɵɵelementStart(45, \"button\", 26);\n        i0.ɵɵlistener(\"click\", function RoleEditComponent_Template_button_click_45_listener() {\n          return ctx.save();\n        });\n        i0.ɵɵtext(46, \" Save\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.role);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"formGroup\", ctx.editroles);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.appService.controlHasError(\"required\", \"roleName\", ctx.editroles));\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngForOf\", ctx.perNameArray);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", ctx.editroles.invalid);\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i8.CheckboxComponent, i9.CheckboxGroupComponent],\n    styles: [\"ng-select[_ngcontent-%COMP%]   .custom[_ngcontent-%COMP%] {\\n  height: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9zZXR0aW5nL3JvbGUtZWRpdC9yb2xlLWVkaXQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxZQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyJuZy1zZWxlY3QgLmN1c3RvbSB7XHJcbiAgICBoZWlnaHQ6IDIwcHg7XHJcbiAgfVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "Validators", "_", "each", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "<PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "ɵɵtemplate", "RoleEditComponent_ng_container_3_div_1_Template", "RoleEditComponent_ng_container_3_div_2_Template", "ɵɵproperty", "id", "ɵɵelement", "ɵɵtextInterpolate", "p_r2", "RoleEditComponent", "modal", "cdr", "httpUtilService", "layoutUtilService", "fb", "appService", "UserService", "permissions", "passEntry", "editroles", "role", "permissionArray", "perNameArray", "rolePermissions", "Permissions", "loginUser", "selectedpermission", "constructor", "onCancelClick", "loadingSubject", "next", "dismiss", "ngOnInit", "getLoggedInUser", "loadForm", "patchForm", "formGroup", "compose", "required", "p<PERSON><PERSON>y", "for<PERSON>ach", "perm", "push", "Name", "group", "getRole", "roleId", "subscribe", "<PERSON><PERSON><PERSON>", "rPerms", "JSON", "parse", "responseData", "patchValue", "description", "self", "r", "value", "key", "changeSystemAccess", "event", "perArray", "save", "roleData", "prepareRole", "console", "log", "controls", "invalid", "Object", "keys", "controlName", "<PERSON><PERSON><PERSON><PERSON>ched", "showError", "create", "edit", "editRole", "res", "showSuccess", "message", "emit", "close", "addRole", "formData", "loggedInUserId", "userId", "status", "r<PERSON><PERSON>", "find", "o", "undefined", "<PERSON><PERSON><PERSON>", "getPermission<PERSON>son", "permission", "newPermission", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "ChangeDetectorRef", "i2", "HttpUtilsService", "i3", "CustomLayoutUtilsService", "i4", "FormBuilder", "i5", "AppService", "i6", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "RoleEditComponent_Template", "rf", "ctx", "RoleEditComponent_ng_container_3_Template", "ɵɵlistener", "RoleEditComponent_Template_a_click_5_listener", "RoleEditComponent_span_21_Template", "RoleEditComponent_tr_40_Template", "RoleEditComponent_Template_button_click_42_listener", "RoleEditComponent_Template_button_click_45_listener", "controlHasError"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\role-edit\\role-edit.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\role-edit\\role-edit.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';\nimport { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport * as _ from 'lodash';\nimport { each } from 'lodash';\nimport { AppService } from '../../services/app.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { UserService } from '../../services/user.service';\n\n@Component({\n  selector: 'app-role-edit',\n  templateUrl: './role-edit.component.html',\n  styleUrls: ['./role-edit.component.scss']\n})\nexport class RoleEditComponent {\n @Input() id: number; // RolePID -input from the role component\n  @Input() permissions: any;// Permissions - input from the role component\n  @Output() passEntry: EventEmitter<any> = new EventEmitter(); // output tp the role component\n  editroles: FormGroup; //Initialize the form\n  role: any = {}; // field to save the role details\n  permissionArray: any = [];// field to save the initial values of default permissions\n  perNameArray: any = [];// field to save the Name's  of the Permissions in a array\n  rolePermissions: any = [];// field to save the final values of the Permissions for the role\n  Permissions: any = []; //store permission array\n  loginUser:any={}; //store local storage data of logged in user\n  selectedpermission:any=[];//store permission array based on role name changes\n  roleName:any ='';\n  constructor(public modal: NgbActiveModal,\n    private cdr:ChangeDetectorRef,\n    private httpUtilService:HttpUtilsService,\n    private layoutUtilService:CustomLayoutUtilsService,\n    private fb: FormBuilder,\n    public appService:AppService,\n    private UserService:UserService) { }\n\n  // Method to handle cancel button click\n  onCancelClick(): void {\n    // Reset loading state when cancel is clicked\n    this.httpUtilService.loadingSubject.next(false);\n    this.modal.dismiss();\n  }\n\n  ngOnInit(): void {\n    this.loginUser = this.appService.getLoggedInUser()\n    this.permissionArray = this.permissions; // get the initial values of the default permissions\n    this.loadForm(); // load the form variables\n    if (this.id !== 0) {\n      //get the Role details and patch the values to the form fields\n      this.patchForm();\n    }\n  }\n\n    /**\n   * Form initialization\n   * Default params, validators\n   */\n\n   loadForm() {\n    const formGroup: any = {};\n    //assign the form fields\n    formGroup['roleName'] = new FormControl('', Validators.compose([Validators.required]));\n    // formGroup['systemRole'] = new FormControl(null,Validators.compose([Validators.required]));\n    formGroup['description'] = new FormControl('');\n    // formGroup['status'] = new FormControl('');\n\n    let pArray: any = [];\n    // assign the form fields for Permissions\n    this.permissionArray.forEach((perm: any) => {\n      pArray.push(perm.Name);\n      formGroup[perm.Name] = new FormControl('');\n    });\n    //Group the form field and assign it to the form group\n    this.editroles = this.fb.group(formGroup);\n    // field to display the Permission in UI\n    this.perNameArray = pArray;\n  }\n  //function to patch field from API\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.getRole({ roleId: this.id }).subscribe((role: any) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!role.isFault) {\n        let rPerms = JSON.parse(role.responseData.rolePermissions);\n        this.roleName = role.responseData.roleName\n        this.editroles.patchValue({\n          roleName: role.responseData.roleName,\n          description: role.responseData.description,\n          // systemRole:role.responseData.DefaultRoleId,\n          // status:role.responseData.status\n        });\n\n        // patch the permission values\n       let self: any = this;\n        each(rPerms, (r) => {\n           _.forEach(r, function (value, key) {\n             self.editroles.patchValue({\n              [key]: value,\n            });\n\n          });\n        });\n      }\n    });\n\n  }\n  //function to change permission array based on role name changes\n  changeSystemAccess(event:any){\n    this.selectedpermission  = JSON.parse(event.Permissions);\n\n    let perArray: any = [];\n    let self: any = this;\n    each(this.selectedpermission, (r) => {\n      _.forEach(r, function (value, key) {\n        self.editroles.patchValue({\n          [key]: value,\n        });\n      });\n    });\n\n  }\n\n  //Form submit\n  save(){\n    let roleData: any = this.prepareRole();\n    console.log('roleData ', roleData)\n    const controls = this.editroles.controls;\n    if (this.editroles.invalid) {\n      Object.keys(controls).forEach(controlName =>\n        controls[controlName].markAsTouched()\n      );\n      this.layoutUtilService.showError('Please fill all required fields', '');\n      return;\n    }\n    if (this.id === 0) {\n      this.create(roleData);\n    } else {\n      this.edit(roleData);\n    }\n  }\n   // API to update the role details based on the RolePID\n   edit(roleData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.editRole(roleData).subscribe((res:any) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close()\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n      }\n    });\n\n  }\n // API to save new role details\n  create(roleData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.addRole(roleData).subscribe((res:any) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close()\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n      }\n    });\n\n  }\n  //function to map input form fields to API fields\n  prepareRole() {\n    const formData = this.editroles.value;\n    this.role.roleId = this.id;\n    // this.role.DefaultRoleId = formData.systemRole;\n    // this.role.DefaultRoleId = '';\n    this.role.roleName = formData.roleName;\n    this.role.description = formData.description;\n    this.role.loggedInUserId= this.loginUser.userId;\n    this.role.status= 'Active';\n    let controls = this.editroles.controls;\n\n    let perArray: any = [];\n    let self: any = this;\n    console.log('permissionArray ', self.permissionArray)\n    _.forEach(controls, function (value, key) {\n      console.log('valeu ', value, 'Key ',key);\n      let rjson = _.find(self.permissionArray, function (o) {\n        return o.Name === key;\n      });\n      if (rjson !== undefined) {\n        let permissionJson = self.getPermissionJson(rjson, value);\n        perArray.push(permissionJson);\n      }\n    });\n    this.role.rolePermissions = perArray;\n    return this.role;\n  }\n\n   // format the permission data for each permission\n   getPermissionJson(permission: any, controls: any) {\n    let newPermission = {\n      [permission.Name]:controls.value,\n    };\n    return newPermission;\n  }\n}\n", "<div class=\"modal-content\">\n    <div class=\"modal-header\">\n        <div class=\"modal-title\" id=\"example-modal-sizes-title-lg\">\n            <ng-container *ngIf=\"role\">\n                <div class=\"fw-bold fs-2 text-white\" *ngIf=\"id !==0\">Edit Role - {{roleName}}</div>\n                <div class=\"fw-bold fs-2 text-white\" *ngIf=\"id===0\">Add Role</div>\n            </ng-container>\n        </div>\n        <div class=\"float-right cursor-pointer ir-12 \" >\n            <a class=\"btn btn-icon  btn-sm mx-1\" (click)=\"modal.dismiss()\">\n              <i class=\"fa-solid fs-2 fa-xmark text-white\"></i>\n            </a>\n          </div>\n    </div>\n    <div class=\"modal-body large-modal-body\">\n        <ng-container>\n            <div class=\"overlay-layer bg-transparent\">\n                <div class=\"spinner spinner-lg spinner-success\"></div>\n            </div>\n        </ng-container>\n        <ng-container>\n            <form class=\"form form-label-right\" [formGroup]=\"editroles\">\n                <div class=\"card-body response-list\">\n                    <div class=\"form-group row mb-4 px-10 pt-2\">\n                        <div class=\"col-lg-12\">\n                            <label class=\"fw-semibold fs-6 mb-2\">Name<sup class=\"text-danger\">*</sup></label>\n                            <input type=\"text\" class=\"form-control form-control-sm\" name=\"roleName\" placeholder=\"Type Here\" autocomplete=\"off\" formControlName=\"roleName\" />\n                            <span class=\"custom-error-css\" *ngIf=\"appService.controlHasError('required', 'roleName',editroles)\">Required Field</span>\n                        </div>\n                        <!-- <div class=\"col-lg-6\">\n                            <label class=\"fw-semibold fs-6 mb-2\">System Role<sup class=\"text-danger\">*</sup></label>\n                            <ng-select [items]=\"systemRoles\" (change)=\"changeSystemAccess($event)\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"RoleName\"  bindValue=\"RoleId\" formControlName=\"systemRole\"\n                              placeholder=\"Select an option\" class=\"custom\">\n                            </ng-select>\n                            <span class=\"custom-error-css\" *ngIf=\"appService.controlHasError('required', 'systemRole',editroles)\">Required Field</span>\n                        </div> -->\n                    </div>\n                    <div class=\"form-group row mb-4 px-10 pt-2\">\n                        <div class=\"col-lg-12\">\n                            <label class=\"fw-semibold fs-6 mb-2\">Description</label>\n                            <textarea type=\"text\" class=\"form-control\" name=\"description\" placeholder=\"Type Here\" rows=\"2\"\n                              autocomplete=\"off\"  formControlName=\"description\"></textarea>\n                        </div>\n                    </div>\n\n                    <div class=\"form-group row mb-4 px-10 pt-2\">\n                        <div class=\"col-12 d-flex justify-content-start\">\n                            <table class=\"w-100\">\n                                <th class=\"p-1 fw-bold fs-6\" style=\"width:390px !important;\">Permissions</th>\n                                <th class=\"p-1 fw-bold fs-6\" style=\"width:390px !important;\">\n                                    <div class=\"d-flex justify-content-between\">\n                                        <span>Read</span>\n                                        <span>Write</span>\n                                        <span>Delete</span>\n                                    </div>\n                                </th>\n                                <tr *ngFor=\"let p of perNameArray\">\n                                    <td class=\"p-1\">\n                                      <label class=\"fw-semibold fs-6\">{{p}}</label>\n                                    </td>\n                                    <td class=\"p-1\">\n                                      <checkbox-group [formControlName]=\"p\">\n                                        <checkbox value=\"Read\"></checkbox>\n                                        <checkbox style=\"padding-left:145px\" value=\"Write\"></checkbox>\n                                        <checkbox style=\"padding-left:145px\" value=\"Delete\"></checkbox>\n                                      </checkbox-group>\n                                    </td>\n                                  </tr>\n                            </table>\n                        </div>\n                    </div>\n                </div>\n            </form>\n        </ng-container>\n    </div>\n    <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary btn-sm btn-elevate mr-2\" (click)=\"onCancelClick()\">Cancel</button>\n        <ng-container>\n            <button type=\"button\" class=\"btn btn-primary btn-elevate btn-sm\" (click)=\"save()\" [disabled]=\"editroles.invalid\">\n                Save</button>\n        </ng-container>\n    </div>\n</div>\n\n\n\n"], "mappings": "AAAA,SAAuCA,YAAY,QAAuB,eAAe;AACzF,SAAiCC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAEhF,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAC3B,SAASC,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;ICAbC,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA9BH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAK,kBAAA,iBAAAC,MAAA,CAAAC,QAAA,KAAwB;;;;;IAC7EP,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFtEH,EAAA,CAAAQ,uBAAA,GAA2B;IAEvBR,EADA,CAAAS,UAAA,IAAAC,+CAAA,kBAAqD,IAAAC,+CAAA,kBACD;;;;;IADdX,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAO,EAAA,OAAa;IACbb,EAAA,CAAAI,SAAA,EAAY;IAAZJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAO,EAAA,OAAY;;;;;IAsBtCb,EAAA,CAAAC,cAAA,eAAoG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA+B/GH,EAFN,CAAAC,cAAA,SAAmC,aACf,gBACkB;IAAAD,EAAA,CAAAE,MAAA,GAAK;IACvCF,EADuC,CAAAG,YAAA,EAAQ,EAC1C;IAEHH,EADF,CAAAC,cAAA,aAAgB,yBACwB;IAGpCD,EAFA,CAAAc,SAAA,mBAAkC,mBAC4B,mBACC;IAGrEd,EAFI,CAAAG,YAAA,EAAiB,EACd,EACF;;;;IAT+BH,EAAA,CAAAI,SAAA,GAAK;IAALJ,EAAA,CAAAe,iBAAA,CAAAC,IAAA,CAAK;IAGrBhB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAY,UAAA,oBAAAI,IAAA,CAAqB;;;AD9C3E,OAAM,MAAOC,iBAAiB;EAaTC,KAAA;EACTC,GAAA;EACAC,eAAA;EACAC,iBAAA;EACAC,EAAA;EACDC,UAAA;EACCC,WAAA;EAlBFX,EAAE,CAAS,CAAC;EACXY,WAAW,CAAM;EAChBC,SAAS,GAAsB,IAAI/B,YAAY,EAAE,CAAC,CAAC;EAC7DgC,SAAS,CAAY,CAAC;EACtBC,IAAI,GAAQ,EAAE,CAAC,CAAC;EAChBC,eAAe,GAAQ,EAAE,CAAC;EAC1BC,YAAY,GAAQ,EAAE,CAAC;EACvBC,eAAe,GAAQ,EAAE,CAAC;EAC1BC,WAAW,GAAQ,EAAE,CAAC,CAAC;EACvBC,SAAS,GAAK,EAAE,CAAC,CAAC;EAClBC,kBAAkB,GAAK,EAAE,CAAC;EAC1B3B,QAAQ,GAAM,EAAE;EAChB4B,YAAmBjB,KAAqB,EAC9BC,GAAqB,EACrBC,eAAgC,EAChCC,iBAA0C,EAC1CC,EAAe,EAChBC,UAAqB,EACpBC,WAAuB;IANd,KAAAN,KAAK,GAALA,KAAK;IACd,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,UAAU,GAAVA,UAAU;IACT,KAAAC,WAAW,GAAXA,WAAW;EAAgB;EAErC;EACAY,aAAaA,CAAA;IACX;IACA,IAAI,CAAChB,eAAe,CAACiB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;IAC/C,IAAI,CAACpB,KAAK,CAACqB,OAAO,EAAE;EACtB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACP,SAAS,GAAG,IAAI,CAACV,UAAU,CAACkB,eAAe,EAAE;IAClD,IAAI,CAACZ,eAAe,GAAG,IAAI,CAACJ,WAAW,CAAC,CAAC;IACzC,IAAI,CAACiB,QAAQ,EAAE,CAAC,CAAC;IACjB,IAAI,IAAI,CAAC7B,EAAE,KAAK,CAAC,EAAE;MACjB;MACA,IAAI,CAAC8B,SAAS,EAAE;IAClB;EACF;EAEE;;;;EAKDD,QAAQA,CAAA;IACP,MAAME,SAAS,GAAQ,EAAE;IACzB;IACAA,SAAS,CAAC,UAAU,CAAC,GAAG,IAAIhD,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACgD,OAAO,CAAC,CAAChD,UAAU,CAACiD,QAAQ,CAAC,CAAC,CAAC;IACtF;IACAF,SAAS,CAAC,aAAa,CAAC,GAAG,IAAIhD,WAAW,CAAC,EAAE,CAAC;IAC9C;IAEA,IAAImD,MAAM,GAAQ,EAAE;IACpB;IACA,IAAI,CAAClB,eAAe,CAACmB,OAAO,CAAEC,IAAS,IAAI;MACzCF,MAAM,CAACG,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC;MACtBP,SAAS,CAACK,IAAI,CAACE,IAAI,CAAC,GAAG,IAAIvD,WAAW,CAAC,EAAE,CAAC;IAC5C,CAAC,CAAC;IACF;IACA,IAAI,CAAC+B,SAAS,GAAG,IAAI,CAACL,EAAE,CAAC8B,KAAK,CAACR,SAAS,CAAC;IACzC;IACA,IAAI,CAACd,YAAY,GAAGiB,MAAM;EAC5B;EACA;EACAJ,SAASA,CAAA;IACP,IAAI,CAACvB,eAAe,CAACiB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACd,WAAW,CAAC6B,OAAO,CAAC;MAAEC,MAAM,EAAE,IAAI,CAACzC;IAAE,CAAE,CAAC,CAAC0C,SAAS,CAAE3B,IAAS,IAAI;MACpE,IAAI,CAACR,eAAe,CAACiB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACV,IAAI,CAAC4B,OAAO,EAAE;QACjB,IAAIC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC/B,IAAI,CAACgC,YAAY,CAAC7B,eAAe,CAAC;QAC1D,IAAI,CAACxB,QAAQ,GAAGqB,IAAI,CAACgC,YAAY,CAACrD,QAAQ;QAC1C,IAAI,CAACoB,SAAS,CAACkC,UAAU,CAAC;UACxBtD,QAAQ,EAAEqB,IAAI,CAACgC,YAAY,CAACrD,QAAQ;UACpCuD,WAAW,EAAElC,IAAI,CAACgC,YAAY,CAACE;UAC/B;UACA;SACD,CAAC;QAEF;QACD,IAAIC,IAAI,GAAQ,IAAI;QACnBhE,IAAI,CAAC0D,MAAM,EAAGO,CAAC,IAAI;UAChBlE,CAAC,CAACkD,OAAO,CAACgB,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG;YAC/BH,IAAI,CAACpC,SAAS,CAACkC,UAAU,CAAC;cACzB,CAACK,GAAG,GAAGD;aACR,CAAC;UAEJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EAEJ;EACA;EACAE,kBAAkBA,CAACC,KAAS;IAC1B,IAAI,CAAClC,kBAAkB,GAAIwB,IAAI,CAACC,KAAK,CAACS,KAAK,CAACpC,WAAW,CAAC;IAExD,IAAIqC,QAAQ,GAAQ,EAAE;IACtB,IAAIN,IAAI,GAAQ,IAAI;IACpBhE,IAAI,CAAC,IAAI,CAACmC,kBAAkB,EAAG8B,CAAC,IAAI;MAClClE,CAAC,CAACkD,OAAO,CAACgB,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG;QAC/BH,IAAI,CAACpC,SAAS,CAACkC,UAAU,CAAC;UACxB,CAACK,GAAG,GAAGD;SACR,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EAEJ;EAEA;EACAK,IAAIA,CAAA;IACF,IAAIC,QAAQ,GAAQ,IAAI,CAACC,WAAW,EAAE;IACtCC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEH,QAAQ,CAAC;IAClC,MAAMI,QAAQ,GAAG,IAAI,CAAChD,SAAS,CAACgD,QAAQ;IACxC,IAAI,IAAI,CAAChD,SAAS,CAACiD,OAAO,EAAE;MAC1BC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAAC3B,OAAO,CAAC+B,WAAW,IACvCJ,QAAQ,CAACI,WAAW,CAAC,CAACC,aAAa,EAAE,CACtC;MACD,IAAI,CAAC3D,iBAAiB,CAAC4D,SAAS,CAAC,iCAAiC,EAAE,EAAE,CAAC;MACvE;IACF;IACA,IAAI,IAAI,CAACpE,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAACqE,MAAM,CAACX,QAAQ,CAAC;IACvB,CAAC,MAAM;MACL,IAAI,CAACY,IAAI,CAACZ,QAAQ,CAAC;IACrB;EACF;EACC;EACAY,IAAIA,CAACZ,QAAa;IACjB,IAAI,CAACnD,eAAe,CAACiB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACd,WAAW,CAAC4D,QAAQ,CAACb,QAAQ,CAAC,CAAChB,SAAS,CAAE8B,GAAO,IAAI;MACxD,IAAI,CAACjE,eAAe,CAACiB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAAC+C,GAAG,CAAC7B,OAAO,EAAE;QAChB,IAAI,CAACnC,iBAAiB,CAACiE,WAAW,CAACD,GAAG,CAACzB,YAAY,CAAC2B,OAAO,EAAE,EAAE,CAAC;QAChE,IAAI,CAAC7D,SAAS,CAAC8D,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACtE,KAAK,CAACuE,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACpE,iBAAiB,CAAC4D,SAAS,CAACI,GAAG,CAACzB,YAAY,CAAC2B,OAAO,EAAE,EAAE,CAAC;MAChE;IACF,CAAC,CAAC;EAEJ;EACD;EACCL,MAAMA,CAACX,QAAa;IAClB,IAAI,CAACnD,eAAe,CAACiB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACd,WAAW,CAACkE,OAAO,CAACnB,QAAQ,CAAC,CAAChB,SAAS,CAAE8B,GAAO,IAAI;MACvD,IAAI,CAACjE,eAAe,CAACiB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAAC+C,GAAG,CAAC7B,OAAO,EAAE;QAChB,IAAI,CAACnC,iBAAiB,CAACiE,WAAW,CAACD,GAAG,CAACzB,YAAY,CAAC2B,OAAO,EAAE,EAAE,CAAC;QAChE,IAAI,CAAC7D,SAAS,CAAC8D,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACtE,KAAK,CAACuE,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACpE,iBAAiB,CAAC4D,SAAS,CAACI,GAAG,CAACzB,YAAY,CAAC2B,OAAO,EAAE,EAAE,CAAC;MAChE;IACF,CAAC,CAAC;EAEJ;EACA;EACAf,WAAWA,CAAA;IACT,MAAMmB,QAAQ,GAAG,IAAI,CAAChE,SAAS,CAACsC,KAAK;IACrC,IAAI,CAACrC,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAACzC,EAAE;IAC1B;IACA;IACA,IAAI,CAACe,IAAI,CAACrB,QAAQ,GAAGoF,QAAQ,CAACpF,QAAQ;IACtC,IAAI,CAACqB,IAAI,CAACkC,WAAW,GAAG6B,QAAQ,CAAC7B,WAAW;IAC5C,IAAI,CAAClC,IAAI,CAACgE,cAAc,GAAE,IAAI,CAAC3D,SAAS,CAAC4D,MAAM;IAC/C,IAAI,CAACjE,IAAI,CAACkE,MAAM,GAAE,QAAQ;IAC1B,IAAInB,QAAQ,GAAG,IAAI,CAAChD,SAAS,CAACgD,QAAQ;IAEtC,IAAIN,QAAQ,GAAQ,EAAE;IACtB,IAAIN,IAAI,GAAQ,IAAI;IACpBU,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEX,IAAI,CAAClC,eAAe,CAAC;IACrD/B,CAAC,CAACkD,OAAO,CAAC2B,QAAQ,EAAE,UAAUV,KAAK,EAAEC,GAAG;MACtCO,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAET,KAAK,EAAE,MAAM,EAACC,GAAG,CAAC;MACxC,IAAI6B,KAAK,GAAGjG,CAAC,CAACkG,IAAI,CAACjC,IAAI,CAAClC,eAAe,EAAE,UAAUoE,CAAC;QAClD,OAAOA,CAAC,CAAC9C,IAAI,KAAKe,GAAG;MACvB,CAAC,CAAC;MACF,IAAI6B,KAAK,KAAKG,SAAS,EAAE;QACvB,IAAIC,cAAc,GAAGpC,IAAI,CAACqC,iBAAiB,CAACL,KAAK,EAAE9B,KAAK,CAAC;QACzDI,QAAQ,CAACnB,IAAI,CAACiD,cAAc,CAAC;MAC/B;IACF,CAAC,CAAC;IACF,IAAI,CAACvE,IAAI,CAACG,eAAe,GAAGsC,QAAQ;IACpC,OAAO,IAAI,CAACzC,IAAI;EAClB;EAEC;EACAwE,iBAAiBA,CAACC,UAAe,EAAE1B,QAAa;IAC/C,IAAI2B,aAAa,GAAG;MAClB,CAACD,UAAU,CAAClD,IAAI,GAAEwB,QAAQ,CAACV;KAC5B;IACD,OAAOqC,aAAa;EACtB;;qCA9LWrF,iBAAiB,EAAAjB,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzG,EAAA,CAAAuG,iBAAA,CAAAvG,EAAA,CAAA0G,iBAAA,GAAA1G,EAAA,CAAAuG,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAA5G,EAAA,CAAAuG,iBAAA,CAAAM,EAAA,CAAAC,wBAAA,GAAA9G,EAAA,CAAAuG,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAhH,EAAA,CAAAuG,iBAAA,CAAAU,EAAA,CAAAC,UAAA,GAAAlH,EAAA,CAAAuG,iBAAA,CAAAY,EAAA,CAAA3F,WAAA;EAAA;;UAAjBP,iBAAiB;IAAAmG,SAAA;IAAAC,MAAA;MAAAxG,EAAA;MAAAY,WAAA;IAAA;IAAA6F,OAAA;MAAA5F,SAAA;IAAA;IAAA6F,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbtB5H,EAFR,CAAAC,cAAA,aAA2B,aACG,aACqC;QACvDD,EAAA,CAAAS,UAAA,IAAAqH,yCAAA,0BAA2B;QAI/B9H,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,aAAgD,WACmB;QAA1BD,EAAA,CAAA+H,UAAA,mBAAAC,8CAAA;UAAA,OAASH,GAAA,CAAA3G,KAAA,CAAAqB,OAAA,EAAe;QAAA,EAAC;QAC5DvC,EAAA,CAAAc,SAAA,WAAiD;QAG3Dd,EAFQ,CAAAG,YAAA,EAAI,EACA,EACN;QACNH,EAAA,CAAAC,cAAA,aAAyC;QACrCD,EAAA,CAAAQ,uBAAA,GAAc;QACVR,EAAA,CAAAC,cAAA,aAA0C;QACtCD,EAAA,CAAAc,SAAA,cAAsD;QAC1Dd,EAAA,CAAAG,YAAA,EAAM;;QAEVH,EAAA,CAAAQ,uBAAA,IAAc;QAKMR,EAJhB,CAAAC,cAAA,gBAA4D,eACnB,eACW,eACjB,iBACkB;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;QACjFH,EAAA,CAAAc,SAAA,iBAAgJ;QAChJd,EAAA,CAAAS,UAAA,KAAAwH,kCAAA,mBAAoG;QAS5GjI,EARI,CAAAG,YAAA,EAAM,EAQJ;QAGEH,EAFR,CAAAC,cAAA,eAA4C,eACjB,iBACkB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxDH,EAAA,CAAAc,SAAA,oBAC+D;QAEvEd,EADI,CAAAG,YAAA,EAAM,EACJ;QAKMH,EAHZ,CAAAC,cAAA,eAA4C,eACS,iBACxB,cAC4C;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGrEH,EAFR,CAAAC,cAAA,cAA6D,eACb,YAClC;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACjBH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAClBH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAEpBF,EAFoB,CAAAG,YAAA,EAAO,EACjB,EACL;QACLH,EAAA,CAAAS,UAAA,KAAAyH,gCAAA,iBAAmC;QAgBvDlI,EAJgB,CAAAG,YAAA,EAAQ,EACN,EACJ,EACJ,EACH;;QAEfH,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAA0B,kBAC4E;QAA1BD,EAAA,CAAA+H,UAAA,mBAAAI,oDAAA;UAAA,OAASN,GAAA,CAAAzF,aAAA,EAAe;QAAA,EAAC;QAACpC,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACjHH,EAAA,CAAAQ,uBAAA,IAAc;QACVR,EAAA,CAAAC,cAAA,kBAAiH;QAAhDD,EAAA,CAAA+H,UAAA,mBAAAK,oDAAA;UAAA,OAASP,GAAA,CAAAvD,IAAA,EAAM;QAAA,EAAC;QAC7EtE,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAS;;QAG7BH,EADI,CAAAG,YAAA,EAAM,EACJ;;;QA/EqBH,EAAA,CAAAI,SAAA,GAAU;QAAVJ,EAAA,CAAAY,UAAA,SAAAiH,GAAA,CAAAjG,IAAA,CAAU;QAkBW5B,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAY,UAAA,cAAAiH,GAAA,CAAAlG,SAAA,CAAuB;QAMX3B,EAAA,CAAAI,SAAA,GAAkE;QAAlEJ,EAAA,CAAAY,UAAA,SAAAiH,GAAA,CAAAtG,UAAA,CAAA8G,eAAA,yBAAAR,GAAA,CAAAlG,SAAA,EAAkE;QA6B5E3B,EAAA,CAAAI,SAAA,IAAe;QAAfJ,EAAA,CAAAY,UAAA,YAAAiH,GAAA,CAAA/F,YAAA,CAAe;QAsB6B9B,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAY,UAAA,aAAAiH,GAAA,CAAAlG,SAAA,CAAAiD,OAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}