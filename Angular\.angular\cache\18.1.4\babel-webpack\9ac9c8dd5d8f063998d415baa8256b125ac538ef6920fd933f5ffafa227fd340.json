{"ast": null, "code": "import { AppSettings } from 'src/app/app.settings';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"src/app/modules/services/app.service\";\nexport class ShowImageComponent {\n  modal;\n  appService;\n  cdr;\n  image;\n  imageUrl;\n  constructor(modal, appService, cdr) {\n    this.modal = modal;\n    this.appService = appService;\n    this.cdr = cdr;\n  }\n  // Method to handle cancel button click\n  onCancelClick() {\n    this.modal.dismiss();\n  }\n  ngOnInit() {\n    this.imageUrl = AppSettings.IMAGEPATH + this.image;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function ShowImageComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ShowImageComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ShowImageComponent,\n    selectors: [[\"app-show-image\"]],\n    inputs: {\n      image: \"image\"\n    },\n    decls: 11,\n    vars: 2,\n    consts: [[1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"example-modal-sizes-Title-lg\", 1, \"modal-title\"], [1, \"fw-bold\", \"fs-2\", \"text-white\"], [1, \"float-right\", \"cursor-pointer\", \"ir-12\"], [1, \"btn\", \"btn-icon\", \"btn-sm\", \"mx-1\", 3, \"click\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\"], [1, \"modal-body\"], [\"alt\", \"Image\", 1, \"img-fluid\", \"rounded-top\", \"user-timeline-image\", 3, \"src\"]],\n    template: function ShowImageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵelementStart(4, \"div\", 3);\n        i0.ɵɵtext(5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"a\", 5);\n        i0.ɵɵlistener(\"click\", function ShowImageComponent_Template_a_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelement(8, \"i\", 6);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 7);\n        i0.ɵɵelement(10, \"img\", 8);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.image);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"src\", ctx.imageUrl, i0.ɵɵsanitizeUrl);\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["AppSettings", "ShowImageComponent", "modal", "appService", "cdr", "image", "imageUrl", "constructor", "onCancelClick", "dismiss", "ngOnInit", "IMAGEPATH", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i0", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "AppService", "ChangeDetectorRef", "selectors", "inputs", "decls", "vars", "consts", "template", "ShowImageComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ShowImageComponent_Template_a_click_7_listener", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty", "ɵɵsanitizeUrl"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\shared\\show-image\\show-image.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\shared\\show-image\\show-image.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { AppSettings } from 'src/app/app.settings';\r\nimport { AppService } from 'src/app/modules/services/app.service';\r\n\r\n@Component({\r\n  selector: 'app-show-image',\r\n  templateUrl: './show-image.component.html',\r\n  styleUrls: ['./show-image.component.scss']\r\n})\r\nexport class ShowImageComponent implements OnInit {\r\n  @Input() image: any;\r\n  imageUrl:any\r\n  constructor(public modal: NgbActiveModal,public appService:AppService,\r\n    private cdr: ChangeDetectorRef,) { }\r\n\r\n  // Method to handle cancel button click\r\n  onCancelClick(): void {\r\n    this.modal.dismiss();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.imageUrl = AppSettings.IMAGEPATH+this.image;\r\n    this.cdr.markForCheck();\r\n  }\r\n}\r\n", "<div class=\"modal-content\">\r\n    <div class=\"modal-header\">\r\n        <div class=\"modal-title\" id=\"example-modal-sizes-Title-lg\">\r\n            <ng-container>\r\n                <div class=\"fw-bold fs-2 text-white\">{{image}}</div>\r\n            </ng-container>\r\n        </div>\r\n        <div class=\"float-right cursor-pointer ir-12\">\r\n            <a class=\"btn btn-icon  btn-sm mx-1\" (click)=\"modal.dismiss()\">\r\n                <i class=\"fa-solid fs-2 fa-xmark text-white\"></i>\r\n            </a>\r\n        </div>\r\n    </div>\r\n    <div class=\"modal-body\">\r\n        <img [src]=\"imageUrl\" class=\"img-fluid rounded-top user-timeline-image\" alt=\"Image\">\r\n    </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,sBAAsB;;;;AAQlD,OAAM,MAAOC,kBAAkB;EAGVC,KAAA;EAA6BC,UAAA;EACtCC,GAAA;EAHDC,KAAK;EACdC,QAAQ;EACRC,YAAmBL,KAAqB,EAAQC,UAAqB,EAC3DC,GAAsB;IADb,KAAAF,KAAK,GAALA,KAAK;IAAwB,KAAAC,UAAU,GAAVA,UAAU;IAChD,KAAAC,GAAG,GAAHA,GAAG;EAAwB;EAErC;EACAI,aAAaA,CAAA;IACX,IAAI,CAACN,KAAK,CAACO,OAAO,EAAE;EACtB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACJ,QAAQ,GAAGN,WAAW,CAACW,SAAS,GAAC,IAAI,CAACN,KAAK;IAChD,IAAI,CAACD,GAAG,CAACQ,YAAY,EAAE;EACzB;;qCAdWX,kBAAkB,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAM,iBAAA;EAAA;;UAAlBlB,kBAAkB;IAAAmB,SAAA;IAAAC,MAAA;MAAAhB,KAAA;IAAA;IAAAiB,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRvBd,EAFR,CAAAgB,cAAA,aAA2B,aACG,aACqC;QACvDhB,EAAA,CAAAiB,uBAAA,GAAc;QACVjB,EAAA,CAAAgB,cAAA,aAAqC;QAAAhB,EAAA,CAAAkB,MAAA,GAAS;QAAAlB,EAAA,CAAAmB,YAAA,EAAM;;QAE5DnB,EAAA,CAAAmB,YAAA,EAAM;QAEFnB,EADJ,CAAAgB,cAAA,aAA8C,WACqB;QAA1BhB,EAAA,CAAAoB,UAAA,mBAAAC,+CAAA;UAAA,OAASN,GAAA,CAAA1B,KAAA,CAAAO,OAAA,EAAe;QAAA,EAAC;QAC1DI,EAAA,CAAAsB,SAAA,WAAiD;QAG7DtB,EAFQ,CAAAmB,YAAA,EAAI,EACF,EACJ;QACNnB,EAAA,CAAAgB,cAAA,aAAwB;QACpBhB,EAAA,CAAAsB,SAAA,cAAoF;QAE5FtB,EADI,CAAAmB,YAAA,EAAM,EACJ;;;QAZ+CnB,EAAA,CAAAuB,SAAA,GAAS;QAATvB,EAAA,CAAAwB,iBAAA,CAAAT,GAAA,CAAAvB,KAAA,CAAS;QAUjDQ,EAAA,CAAAuB,SAAA,GAAgB;QAAhBvB,EAAA,CAAAyB,UAAA,QAAAV,GAAA,CAAAtB,QAAA,EAAAO,EAAA,CAAA0B,aAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}