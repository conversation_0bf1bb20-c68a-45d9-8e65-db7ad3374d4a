// Grid Container Styles
.grid-container {
  position: relative;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;

  &.fullscreen-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
    padding: 20px;
    overflow: auto;
  }
}

// Loading Overlay Styles
.fullscreen-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .loading-content {
    text-align: center;
    color: #3699ff;
    padding: 2rem;
  }
}

// Search Section Styles
.search-section {
  .k-textbox {
    border-radius: 6px;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
}

// Advanced Filters Panel
.advanced-filters-panel {
  border-radius: 8px;
  margin-bottom: 1rem;

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .k-dropdownlist {
    width: 100%;
    border-radius: 6px;
  }
}

// Kendo Grid Customization
:host ::ng-deep {
  .k-grid {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .k-grid-header {
      background: #f8f9fa;
      border-bottom: 2px solid #dee2e6;

      .k-header {
        background: #f8f9fa;
        border-color: #dee2e6;
        font-weight: 600;
        color: #495057;

        &:hover {
          background: #e9ecef;
        }
      }
    }

    .k-grid-toolbar {
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      padding: 1rem;

      .k-button {
        border-radius: 6px;
        font-weight: 500;

        &.k-primary {
          background: #007bff;
          border-color: #007bff;

          &:hover {
            background: #0056b3;
            border-color: #0056b3;
          }
        }
      }
    }

    .k-grid-content {
      .k-grid-row {
        &:hover {
          background: #f8f9fa;
        }

        &.k-alt {
          background: #f8f9fa;

          &:hover {
            background: #e9ecef;
          }
        }
      }
    }

    .k-pager {
      background: #f8f9fa;
      border-top: 1px solid #dee2e6;

      .k-pager-info {
        color: #6c757d;
      }

      .k-pager-numbers {
        .k-link {
          border-radius: 4px;

          &:hover {
            background: #e9ecef;
          }

          &.k-state-selected {
            background: #007bff;
            color: white;
          }
        }
      }
    }
  }

  // Custom Dropdown Button
  .custom-dropdown {
    .k-button {
      border-radius: 6px;
      font-weight: 500;
    }
  }

  // Kendo Textbox
  .k-textbox {
    border-radius: 6px;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  // Kendo Dropdownlist
  .k-dropdownlist {
    border-radius: 6px;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
}

// Badge Styles
.badge {
  padding: 0.5em 0.75em;
  font-size: 0.75em;
  font-weight: 600;
  border-radius: 6px;

  &.badge-light-success {
    background: #d4edda;
    color: #155724;
  }

  &.badge-light-warning {
    background: #fff3cd;
    color: #856404;
  }

  &.badge-light-danger {
    background: #f8d7da;
    color: #721c24;
  }

  &.badge-light-info {
    background: #d1ecf1;
    color: #0c5460;
  }

  &.badge-light-secondary {
    background: #e2e3e5;
    color: #383d41;
  }

  &.badge-light-primary {
    background: #cce7ff;
    color: #004085;
  }
}

// Button Styles
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  &.btn-primary {
    background: #007bff;
    border-color: #007bff;

    &:hover {
      background: #0056b3;
      border-color: #0056b3;
    }
  }

  &.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;

    &:hover {
      background: #545b62;
      border-color: #545b62;
    }
  }

  &.btn-success {
    background: #28a745;
    border-color: #28a745;

    &:hover {
      background: #1e7e34;
      border-color: #1e7e34;
    }
  }

  &.btn-warning {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;

    &:hover {
      background: #e0a800;
      border-color: #d39e00;
    }
  }

  &.btn-info {
    background: #17a2b8;
    border-color: #17a2b8;

    &:hover {
      background: #138496;
      border-color: #138496;
    }
  }

  &.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;

    &:hover {
      background: #6c757d;
      color: white;
    }
  }
}

// Action Buttons
.btn-icon {
  width: 32px;
  height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

// No Data Message
.text-muted {
  .fas {
    color: #6c757d;
  }

  h4 {
    color: #495057;
    margin-top: 1rem;
  }

  p {
    color: #6c757d;
    margin-bottom: 1.5rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .grid-container {
    .k-grid-toolbar {
      flex-direction: column;
      gap: 1rem;

      .search-section {
        width: 100%;

        .k-textbox {
          width: 100% !important;
        }
      }

      .d-flex {
        justify-content: center;
      }
    }
  }

  .advanced-filters-panel {
    .row {

      .col-md-3,
      .col-md-6 {
        margin-bottom: 1rem;
      }
    }
  }
}

// Animation for grid expansion
.grid-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.fullscreen-grid {
    animation: expandGrid 0.3s ease-out;
  }
}

@keyframes expandGrid {
  from {
    opacity: 0.8;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}