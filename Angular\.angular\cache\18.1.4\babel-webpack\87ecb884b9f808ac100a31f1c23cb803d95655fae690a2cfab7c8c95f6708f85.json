{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../services/app.service\";\nimport * as i4 from \"../../services/http-utils.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../../modules/services/projects.service\";\nimport * as i7 from \"../../services/user.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Project - \", ctx_r0.projectName, \"\");\n  }\n}\nfunction ProjectPopupComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23)(3, \"span\", 24);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵtext(6, \"Loading project data...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectPopupComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 22)(2, \"div\", 23)(3, \"span\", 24);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵtext(6, \"Initializing form...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"div\", 10)(3, \"div\", 30)(4, \"label\", 31);\n    i0.ɵɵtext(5, \"Project name\");\n    i0.ɵɵelementStart(6, \"sup\", 32);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"input\", 33);\n    i0.ɵɵtemplate(9, ProjectPopupComponent_form_21_ng_container_1_span_9_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 35)(12, \"div\", 30)(13, \"label\", 31);\n    i0.ɵɵtext(14, \"Internal project # \");\n    i0.ɵɵelementStart(15, \"sup\", 32);\n    i0.ɵɵtext(16, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"input\", 36);\n    i0.ɵɵtemplate(18, ProjectPopupComponent_form_21_ng_container_1_span_18_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 37)(20, \"label\", 31);\n    i0.ɵɵtext(21, \"Start date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 37)(24, \"label\", 31);\n    i0.ɵɵtext(25, \"End date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 10)(28, \"label\", 31);\n    i0.ɵɵtext(29, \"Location\");\n    i0.ɵɵelementStart(30, \"sup\", 32);\n    i0.ɵɵtext(31, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(32, \"input\", 40);\n    i0.ɵɵtemplate(33, ProjectPopupComponent_form_21_ng_container_1_span_33_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 29)(35, \"div\", 10)(36, \"label\", 31);\n    i0.ɵɵtext(37, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"textarea\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"projectName\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"internalProjectNo\"));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"location\"));\n  }\n}\nfunction ProjectPopupComponent_form_21_div_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 35)(2, \"label\", 31);\n    i0.ɵɵtext(3, \"Internal project manager \");\n    i0.ɵɵelementStart(4, \"sup\", 32);\n    i0.ɵɵtext(5, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"ng-select\", 43);\n    i0.ɵɵlistener(\"change\", function ProjectPopupComponent_form_21_div_2_Template_ng_select_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeInternalManager($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ProjectPopupComponent_form_21_div_2_span_7_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 44)(9, \"label\", 31);\n    i0.ɵɵtext(10, \"External project manager (multiple)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ng-select\", 45);\n    i0.ɵɵlistener(\"change\", function ProjectPopupComponent_form_21_div_2_Template_ng_select_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeexternalPM($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.managers)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"manager\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"items\", ctx_r0.externalPMs)(\"clearable\", true)(\"multiple\", true);\n  }\n}\nfunction ProjectPopupComponent_form_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 27);\n    i0.ɵɵtemplate(1, ProjectPopupComponent_form_21_ng_container_1_Template, 39, 3, \"ng-container\", 3)(2, ProjectPopupComponent_form_21_div_2_Template, 12, 7, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.projectForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedTab == \"basic\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedTab != \"basic\");\n  }\n}\nfunction ProjectPopupComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.projectForm == null ? null : ctx_r0.projectForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.id ? \"Update\" : \"Save\", \" \");\n  }\n}\nfunction ProjectPopupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_30_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.showTab(\"role\", $event));\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ProjectPopupComponent {\n  modal;\n  cdr;\n  fb;\n  appService;\n  httpUtilService;\n  customLayoutUtilsService;\n  projectsService;\n  usersService;\n  id = 0; // 0 = Add, otherwise Edit\n  project; // incoming project data (for edit)\n  passEntry = new EventEmitter();\n  projectForm = null;\n  loginUser = {};\n  managers = [];\n  externalPMs = [];\n  isLoading = false;\n  selectedTab = 'basic'; //store navigation tab\n  projectName;\n  subscriptions = [];\n  constructor(modal, cdr, fb, appService, httpUtilService, customLayoutUtilsService, projectsService,\n  // adjust path\n  usersService) {\n    this.modal = modal;\n    this.cdr = cdr;\n    this.fb = fb;\n    this.appService = appService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.projectsService = projectsService;\n    this.usersService = usersService;\n    // Subscribe to loading state\n    const loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n      console.log('Project popup - loading state changed:', loading);\n      this.isLoading = loading;\n    });\n    this.subscriptions.push(loadingSubscription);\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadForm();\n    // Set loading state immediately for edit mode\n    if (this.id !== 0) {\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      // Wait for both manager lists to load before patching form\n      this.loadManagersAndPatchForm();\n    } else {\n      // For add mode, load managers without loading state\n      this.loadInternalManager();\n      this.loadExternalManagers();\n    }\n  }\n  loadForm() {\n    this.projectForm = this.fb.group({\n      projectName: ['', Validators.required],\n      internalProjectNo: ['', Validators.required],\n      startDate: [''],\n      endDate: [''],\n      // Make end date optional\n      location: ['', Validators.required],\n      projectDescription: [''],\n      manager: [null, Validators.required],\n      externalPM: [[]] // Remove required validator for external PM as it's optional\n    });\n    // Trigger change detection to update the view\n    this.cdr.detectChanges();\n  }\n  // Load MedicalCenters\n  loadInternalManager() {\n    const subscription = this.usersService.getUserlistForDropdown({\n      roleName: 'Internal PM'\n    }).subscribe(internalPMSResponse => {\n      const users = internalPMSResponse?.responseData?.users;\n      this.managers = Array.isArray(users) ? users : [];\n    });\n    this.subscriptions.push(subscription);\n  }\n  // Load roles for advanced filters\n  loadExternalManagers() {\n    const subscription = this.usersService.getUserlistForDropdown({\n      roleName: 'External PM'\n    }).subscribe(externalPMSResponse => {\n      const users = externalPMSResponse?.responseData?.users;\n      this.externalPMs = Array.isArray(users) ? users : [];\n    });\n    this.subscriptions.push(subscription);\n  }\n  // Load both manager lists and then patch the form\n  loadManagersAndPatchForm() {\n    // Use Promise.all to wait for both API calls to complete\n    const internalPMsPromise = this.usersService.getUserlistForDropdown({\n      roleName: 'Internal PM'\n    }).toPromise();\n    const externalPMsPromise = this.usersService.getUserlistForDropdown({\n      roleName: 'External PM'\n    }).toPromise();\n    Promise.all([internalPMsPromise, externalPMsPromise]).then(([internalResponse, externalResponse]) => {\n      // Set the manager lists\n      this.managers = Array.isArray(internalResponse?.responseData?.users) ? internalResponse.responseData.users : [];\n      this.externalPMs = Array.isArray(externalResponse?.responseData?.users) ? externalResponse.responseData.users : [];\n      // Now patch the form with the loaded data\n      this.patchForm();\n    }).catch(error => {\n      console.error('Error loading manager lists:', error);\n      // Still try to patch form even if manager lists fail\n      this.patchForm();\n    });\n  }\n  patchForm() {\n    // Loading state is already set in ngOnInit for edit mode\n    const subscription = this.projectsService.getProject({\n      projectId: this.id,\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: projectResponse => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!projectResponse.isFault) {\n          let projectData = projectResponse.responseData.Project;\n          // this.projectId = projectData.projectId\n          console.log('Project data received:', projectData);\n          console.log('Available managers:', this.managers);\n          console.log('Available external PMs:', this.externalPMs);\n          this.projectName = projectData.projectName;\n          // Format dates for HTML date inputs (YYYY-MM-DD format)\n          const formatDateForInput = dateValue => {\n            if (!dateValue) return '';\n            const date = new Date(dateValue);\n            return date.toISOString().split('T')[0];\n          };\n          // The manager is already stored as userId in the database\n          const getManagerId = managerId => {\n            if (!managerId) return null;\n            return parseInt(managerId);\n          };\n          // Parse external PM names string to array of userIds\n          const parseExternalPMs = externalPMNamesString => {\n            if (!externalPMNamesString) return [];\n            // Split comma-separated names and find matching userIds\n            const pmNames = externalPMNamesString.split(',').map(name => name.trim()).filter(name => name !== '');\n            return pmNames.map(name => {\n              const pm = this.externalPMs.find(epm => epm.userFullName === name);\n              return pm ? pm.userId : null;\n            }).filter(id => id !== null);\n          };\n          const formData = {\n            projectName: projectData.projectName,\n            internalProjectNo: projectData.internalProjectNumber,\n            startDate: formatDateForInput(projectData.projectStartDate),\n            endDate: formatDateForInput(projectData.projectEndDate),\n            location: projectData.projectLocation,\n            projectDescription: projectData.projectDescription,\n            manager: getManagerId(projectData.internalProjectManager),\n            externalPM: parseExternalPMs(projectData.externalPMNames)\n          };\n          console.log('Form data to patch:', formData);\n          if (this.projectForm) {\n            this.projectForm.patchValue(formData);\n          }\n        } else {\n          console.warn('User response has isFault = true', projectResponse.responseData);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('API call failed', err);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  save() {\n    if (!this.projectForm) {\n      this.customLayoutUtilsService.showError('Form is not initialized. Please try again.', '');\n      return;\n    }\n    let controls = this.projectForm.controls;\n    if (this.projectForm.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      this.customLayoutUtilsService.showError('Please fill all required fields', '');\n      return;\n    }\n    let projectData = this.prepareProjectData();\n    console.log('Line: 203', projectData);\n    if (this.id === 0) {\n      this.create(projectData);\n    } else {\n      this.edit(projectData);\n    }\n  }\n  prepareProjectData() {\n    if (!this.projectForm) {\n      throw new Error('Form is not initialized');\n    }\n    const formData = this.projectForm.value;\n    let projectRequestData = {};\n    projectRequestData.projectName = formData.projectName;\n    projectRequestData.internalProjectNumber = formData.internalProjectNo;\n    projectRequestData.projectStartDate = formData.startDate;\n    projectRequestData.projectEndDate = formData.endDate;\n    projectRequestData.projectLocation = formData.location;\n    projectRequestData.projectDescription = formData.projectDescription;\n    // Send manager userId directly to backend\n    projectRequestData.internalProjectManager = formData.manager;\n    // Convert external PM userIds to comma-separated string for backend\n    const getExternalPMIds = userIds => {\n      if (!userIds || !Array.isArray(userIds)) return '';\n      return userIds.filter(id => id !== null && id !== undefined).join(',');\n    };\n    projectRequestData.externalPM = getExternalPMIds(formData.externalPM);\n    projectRequestData.loggedInUserId = this.loginUser.userId;\n    console.log('Prepared project data for backend:', projectRequestData);\n    return projectRequestData;\n  }\n  // API to update the user details based on the userid\n  edit(projectData) {\n    projectData.projectId = this.id;\n    this.httpUtilService.loadingSubject.next(true);\n    const subscription = this.projectsService.updateProject(projectData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close(true); // Pass true to indicate successful edit\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  // API to save new user details\n  create(projectData) {\n    this.httpUtilService.loadingSubject.next(true);\n    const subscription = this.projectsService.createProject(projectData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close(true); // Pass true to indicate successful creation\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  changeInternalManager(event) {}\n  changeexternalPM(event) {}\n  controlHasError(validation, controlName) {\n    if (!this.projectForm) {\n      return false;\n    }\n    const control = this.projectForm.controls[controlName];\n    if (!control) {\n      return false;\n    }\n    let result = control.hasError(validation) && (control.dirty || control.touched);\n    return result;\n  }\n  goToPreviousTab() {\n    // if (this.selectedTab === 'notes') {\n    //   this.selectedTab = 'details';\n    // } else if (this.selectedTab === 'details') {\n    // }\n    this.selectedTab = 'basic';\n    this.cdr.markForCheck();\n  }\n  // Method to handle modal dismiss and reset loading state\n  dismissModal() {\n    console.log('dismissModal called - resetting loading state');\n    this.httpUtilService.loadingSubject.next(false);\n    this.modal.dismiss();\n  }\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions to prevent memory leaks\n    this.subscriptions.forEach(subscription => {\n      if (subscription && !subscription.closed) {\n        subscription.unsubscribe();\n      }\n    });\n    this.subscriptions = [];\n    // Ensure loading state is reset when component is destroyed\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  static ɵfac = function ProjectPopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectPopupComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AppService), i0.ɵɵdirectiveInject(i4.HttpUtilsService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.ProjectsService), i0.ɵɵdirectiveInject(i7.UserService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectPopupComponent,\n    selectors: [[\"app-project-popup\"]],\n    inputs: {\n      id: \"id\",\n      project: \"project\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 31,\n    vars: 14,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [\"class\", \"loading-overlay-inside\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center align-items-center h-100\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"class\", \"form form-label-right\", 3, \"formGroup\", 4, \"ngIf\"], [1, \"modal-footer\", \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"loading-overlay-inside\"], [1, \"text-center\"], [\"role\", \"status\", 1, \"custom-colored-spinner\", \"mb-3\"], [1, \"visually-hidden\"], [1, \"text-muted\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"h-100\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [\"class\", \"row mt-4\", 4, \"ngIf\"], [1, \"row\", \"mt-4\"], [1, \"form-group\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"formControlName\", \"projectName\", \"placeholder\", \"Project name\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"custom-error-css\", 4, \"ngIf\"], [1, \"col-xl-6\"], [\"type\", \"text\", \"formControlName\", \"internalProjectNo\", \"placeholder\", \"Internal project\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-3\"], [\"type\", \"date\", \"formControlName\", \"startDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"endDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", \"placeholder\", \"Location\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"projectDescription\", 1, \"form-control\", \"form-control-sm\"], [1, \"custom-error-css\"], [\"bindLabel\", \"userFullName\", \"name\", \"manager\", \"formControlName\", \"manager\", \"bindValue\", \"userId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [1, \"col-xl-12\", \"mt-4\"], [\"bindLabel\", \"userFullName\", \"name\", \"externalPM\", \"formControlName\", \"externalPM\", \"bindValue\", \"userId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n    template: function ProjectPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, ProjectPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, ProjectPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_i_click_7_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6);\n        i0.ɵɵtemplate(9, ProjectPopupComponent_div_9_Template, 7, 0, \"div\", 7)(10, ProjectPopupComponent_div_10_Template, 7, 0, \"div\", 8);\n        i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"ul\", 12)(15, \"li\", 13)(16, \"a\", 14);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_a_click_16_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(17, \" Project details \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"li\", 13)(19, \"a\", 14);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_a_click_19_listener($event) {\n          return ctx.showTab(\"role\", $event);\n        });\n        i0.ɵɵtext(20, \" Project manager \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵtemplate(21, ProjectPopupComponent_form_21_Template, 3, 3, \"form\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\");\n        i0.ɵɵtemplate(24, ProjectPopupComponent_button_24_Template, 2, 0, \"button\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\")(26, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_button_click_26_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵtext(27, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(28, \" \\u00A0 \");\n        i0.ɵɵtemplate(29, ProjectPopupComponent_button_29_Template, 2, 2, \"button\", 19)(30, ProjectPopupComponent_button_30_Template, 2, 0, \"button\", 20);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.projectForm && !ctx.isLoading);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.selectedTab === \"role\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.projectForm);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab != \"basic\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab != \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i9.NgSelectComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "projectName", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵtemplate", "ProjectPopupComponent_form_21_ng_container_1_span_9_Template", "ProjectPopupComponent_form_21_ng_container_1_span_18_Template", "ProjectPopupComponent_form_21_ng_container_1_span_33_Template", "ɵɵproperty", "controlHasError", "ɵɵlistener", "ProjectPopupComponent_form_21_div_2_Template_ng_select_change_6_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "changeInternalManager", "ProjectPopupComponent_form_21_div_2_span_7_Template", "ProjectPopupComponent_form_21_div_2_Template_ng_select_change_11_listener", "changeexternalPM", "managers", "externalPMs", "ProjectPopupComponent_form_21_ng_container_1_Template", "ProjectPopupComponent_form_21_div_2_Template", "projectForm", "selectedTab", "ProjectPopupComponent_button_24_Template_button_click_0_listener", "_r3", "goToPreviousTab", "ProjectPopupComponent_button_29_Template_button_click_0_listener", "_r4", "save", "invalid", "id", "ProjectPopupComponent_button_30_Template_button_click_0_listener", "_r5", "showTab", "ProjectPopupComponent", "modal", "cdr", "fb", "appService", "httpUtilService", "customLayoutUtilsService", "projectsService", "usersService", "project", "passEntry", "loginUser", "isLoading", "subscriptions", "constructor", "loadingSubscription", "loadingSubject", "subscribe", "loading", "console", "log", "push", "ngOnInit", "getLoggedInUser", "loadForm", "next", "loadManagersAndPatchForm", "loadInternalManager", "loadExternalManagers", "group", "required", "internalProjectNo", "startDate", "endDate", "location", "projectDescription", "manager", "externalPM", "detectChanges", "subscription", "getUserlistForDropdown", "<PERSON><PERSON><PERSON>", "internalPMSResponse", "users", "responseData", "Array", "isArray", "externalPMSResponse", "internalPMsPromise", "to<PERSON>romise", "externalPMsPromise", "Promise", "all", "then", "internalResponse", "externalResponse", "patchForm", "catch", "error", "getProject", "projectId", "loggedInUserId", "userId", "projectResponse", "<PERSON><PERSON><PERSON>", "projectData", "Project", "formatDateForInput", "dateValue", "date", "Date", "toISOString", "split", "getManagerId", "managerId", "parseInt", "parseExternalPMs", "externalPMNamesString", "pmNames", "map", "name", "trim", "filter", "pm", "find", "epm", "userFullName", "formData", "internalProjectNumber", "projectStartDate", "projectEndDate", "projectLocation", "internalProjectManager", "externalPMNames", "patchValue", "warn", "err", "showError", "controls", "Object", "keys", "for<PERSON>ach", "controlName", "<PERSON><PERSON><PERSON><PERSON>ched", "prepareProjectData", "create", "edit", "Error", "value", "projectRequestData", "getExternalPMIds", "userIds", "undefined", "join", "updateProject", "res", "showSuccess", "message", "emit", "close", "createProject", "tab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "validation", "control", "result", "<PERSON><PERSON><PERSON><PERSON>", "dirty", "touched", "dismissModal", "dismiss", "ngOnDestroy", "closed", "unsubscribe", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "ChangeDetectorRef", "i2", "FormBuilder", "i3", "AppService", "i4", "HttpUtilsService", "i5", "CustomLayoutUtilsService", "i6", "ProjectsService", "i7", "UserService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ProjectPopupComponent_Template", "rf", "ctx", "ProjectPopupComponent_div_4_Template", "ProjectPopupComponent_div_5_Template", "ProjectPopupComponent_Template_i_click_7_listener", "ProjectPopupComponent_div_9_Template", "ProjectPopupComponent_div_10_Template", "ProjectPopupComponent_Template_a_click_16_listener", "ProjectPopupComponent_Template_a_click_19_listener", "ProjectPopupComponent_form_21_Template", "ProjectPopupComponent_button_24_Template", "ProjectPopupComponent_Template_button_click_26_listener", "ProjectPopupComponent_button_29_Template", "ProjectPopupComponent_button_30_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-popup\\project-popup.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-popup\\project-popup.component.html"], "sourcesContent": ["import {\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  Input,\n  Output,\n  OnDestroy,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ProjectsService } from '../../../modules/services/projects.service'; // adjust path\nimport { AppService } from '../../services/app.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { UserService } from '../../services/user.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-project-popup',\n  templateUrl: './project-popup.component.html',\n})\nexport class ProjectPopupComponent implements OnDestroy {\n  @Input() id: number = 0; // 0 = Add, otherwise Edit\n  @Input() project: any; // incoming project data (for edit)\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\n  projectForm: FormGroup | null = null;\n  loginUser: any = {};\n  managers: any = [];\n  externalPMs: any = [];\n  isLoading: boolean = false;\n  selectedTab: string = 'basic'; //store navigation tab\n  projectName: any;\n  private subscriptions: Subscription[] = [];\n  constructor(\n    public modal: NgbActiveModal,\n    private cdr: ChangeDetectorRef,\n        \n    private fb: FormBuilder,\n    private appService: AppService,\n    private httpUtilService: HttpUtilsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private projectsService: ProjectsService, // adjust path\n    private usersService: UserService\n  ) {\n    // Subscribe to loading state\n    const loadingSubscription = this.httpUtilService.loadingSubject.subscribe((loading) => {\n      console.log('Project popup - loading state changed:', loading);\n      this.isLoading = loading;\n    });\n    this.subscriptions.push(loadingSubscription);\n  }\n\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadForm();\n\n    // Set loading state immediately for edit mode\n    if (this.id !== 0) {\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      // Wait for both manager lists to load before patching form\n      this.loadManagersAndPatchForm();\n    } else {\n      // For add mode, load managers without loading state\n      this.loadInternalManager();\n      this.loadExternalManagers();\n    }\n  }\n\n  loadForm() {\n    this.projectForm = this.fb.group({\n      projectName: ['', Validators.required],\n      internalProjectNo: ['', Validators.required],\n      startDate: [''],\n      endDate: [''], // Make end date optional\n      location: ['', Validators.required],\n      projectDescription: [''],\n      manager: [null, Validators.required],\n      externalPM: [[]], // Remove required validator for external PM as it's optional\n    });\n\n    // Trigger change detection to update the view\n    this.cdr.detectChanges();\n  }\n  // Load MedicalCenters\n  loadInternalManager(): void {\n    const subscription = this.usersService\n      .getUserlistForDropdown({ roleName: 'Internal PM' })\n      .subscribe((internalPMSResponse: any) => {\n        const users = internalPMSResponse?.responseData?.users;\n        this.managers = Array.isArray(users) ? users : [];\n      });\n    this.subscriptions.push(subscription);\n  }\n  // Load roles for advanced filters\n  loadExternalManagers(): void {\n    const subscription = this.usersService\n      .getUserlistForDropdown({ roleName: 'External PM' })\n      .subscribe((externalPMSResponse: any) => {\n        const users = externalPMSResponse?.responseData?.users;\n        this.externalPMs = Array.isArray(users) ? users : [];\n      });\n    this.subscriptions.push(subscription);\n  }\n\n  // Load both manager lists and then patch the form\n  loadManagersAndPatchForm(): void {\n    // Use Promise.all to wait for both API calls to complete\n    const internalPMsPromise = this.usersService\n      .getUserlistForDropdown({ roleName: 'Internal PM' })\n      .toPromise();\n\n    const externalPMsPromise = this.usersService\n      .getUserlistForDropdown({ roleName: 'External PM' })\n      .toPromise();\n\n    Promise.all([internalPMsPromise, externalPMsPromise])\n      .then(([internalResponse, externalResponse]) => {\n        // Set the manager lists\n        this.managers = Array.isArray(internalResponse?.responseData?.users)\n          ? internalResponse.responseData.users : [];\n        this.externalPMs = Array.isArray(externalResponse?.responseData?.users)\n          ? externalResponse.responseData.users : [];\n\n        // Now patch the form with the loaded data\n        this.patchForm();\n      })\n      .catch(error => {\n        console.error('Error loading manager lists:', error);\n        // Still try to patch form even if manager lists fail\n        this.patchForm();\n      });\n  }\n\n  patchForm() {\n    // Loading state is already set in ngOnInit for edit mode\n    const subscription = this.projectsService\n      .getProject({ projectId: this.id, loggedInUserId: this.loginUser.userId })\n      .subscribe({\n        next: (projectResponse: any) => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!projectResponse.isFault) {\n            let projectData = projectResponse.responseData.Project;\n            // this.projectId = projectData.projectId\n            console.log('Project data received:', projectData);\n            console.log('Available managers:', this.managers);\n            console.log('Available external PMs:', this.externalPMs);\n            this.projectName = projectData.projectName;\n\n            // Format dates for HTML date inputs (YYYY-MM-DD format)\n            const formatDateForInput = (dateValue: any) => {\n              if (!dateValue) return '';\n              const date = new Date(dateValue);\n              return date.toISOString().split('T')[0];\n            };\n\n            // The manager is already stored as userId in the database\n            const getManagerId = (managerId: any) => {\n              if (!managerId) return null;\n              return parseInt(managerId);\n            };\n\n            // Parse external PM names string to array of userIds\n            const parseExternalPMs = (externalPMNamesString: string) => {\n              if (!externalPMNamesString) return [];\n              // Split comma-separated names and find matching userIds\n              const pmNames = externalPMNamesString.split(',').map(name => name.trim()).filter(name => name !== '');\n              return pmNames.map(name => {\n                const pm = this.externalPMs.find((epm: any) => epm.userFullName === name);\n                return pm ? pm.userId : null;\n              }).filter(id => id !== null);\n            };\n\n            const formData = {\n              projectName: projectData.projectName,\n              internalProjectNo: projectData.internalProjectNumber,\n              startDate: formatDateForInput(projectData.projectStartDate),\n              endDate: formatDateForInput(projectData.projectEndDate),\n              location: projectData.projectLocation,\n              projectDescription: projectData.projectDescription,\n              manager: getManagerId(projectData.internalProjectManager),\n              externalPM: parseExternalPMs(projectData.externalPMNames),\n            };\n\n            console.log('Form data to patch:', formData);\n            if (this.projectForm) {\n              this.projectForm.patchValue(formData);\n            }\n          } else {\n            console.warn(\n              'User response has isFault = true',\n              projectResponse.responseData\n            );\n          }\n        },\n        error: (err) => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('API call failed', err);\n        },\n      });\n    this.subscriptions.push(subscription);\n  }\n  save() {\n    if (!this.projectForm) {\n      this.customLayoutUtilsService.showError(\n        'Form is not initialized. Please try again.',\n        ''\n      );\n      return;\n    }\n\n    let controls = this.projectForm!.controls;\n    if (this.projectForm!.invalid) {\n      Object.keys(controls).forEach((controlName) =>\n        controls[controlName].markAsTouched()\n      );\n      this.customLayoutUtilsService.showError(\n        'Please fill all required fields',\n        ''\n      );\n      return;\n    }\n    let projectData: any = this.prepareProjectData();\n    console.log('Line: 203', projectData);\n    if (this.id === 0) {\n      this.create(projectData);\n    } else {\n      this.edit(projectData);\n    }\n  }\n\n  prepareProjectData() {\n    if (!this.projectForm) {\n      throw new Error('Form is not initialized');\n    }\n\n    const formData = this.projectForm!.value;\n    let projectRequestData: any = {};\n    projectRequestData.projectName = formData.projectName;\n    projectRequestData.internalProjectNumber = formData.internalProjectNo;\n    projectRequestData.projectStartDate = formData.startDate;\n    projectRequestData.projectEndDate = formData.endDate;\n    projectRequestData.projectLocation = formData.location;\n    projectRequestData.projectDescription = formData.projectDescription;\n\n    // Send manager userId directly to backend\n    projectRequestData.internalProjectManager = formData.manager;\n\n    // Convert external PM userIds to comma-separated string for backend\n    const getExternalPMIds = (userIds: number[]) => {\n      if (!userIds || !Array.isArray(userIds)) return '';\n      return userIds.filter(id => id !== null && id !== undefined).join(',');\n    };\n\n    projectRequestData.externalPM = getExternalPMIds(formData.externalPM);\n    projectRequestData.loggedInUserId = this.loginUser.userId;\n\n    console.log('Prepared project data for backend:', projectRequestData);\n    return projectRequestData;\n  }\n  // API to update the user details based on the userid\n  edit(projectData: any) {\n    projectData.projectId = this.id\n    this.httpUtilService.loadingSubject.next(true);\n    const subscription = this.projectsService.updateProject(projectData).subscribe((res) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close(true); // Pass true to indicate successful edit\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  // API to save new user details\n  create(projectData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    const subscription = this.projectsService.createProject(projectData).subscribe((res: any) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close(true); // Pass true to indicate successful creation\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  showTab(tab: any, $event: any) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  changeInternalManager(event: any) {}\n  changeexternalPM(event: any) {}\n  controlHasError(validation: any, controlName: string | number): boolean {\n    if (!this.projectForm) {\n      return false;\n    }\n\n    const control = this.projectForm!.controls[controlName];\n    if (!control) {\n      return false;\n    }\n    let result =\n      control.hasError(validation) && (control.dirty || control.touched);\n    return result;\n  }\n    goToPreviousTab() {\n    // if (this.selectedTab === 'notes') {\n    //   this.selectedTab = 'details';\n    // } else if (this.selectedTab === 'details') {\n    // }\n    this.selectedTab = 'basic';\n    this.cdr.markForCheck();\n  }\n\n  // Method to handle modal dismiss and reset loading state\n  dismissModal() {\n    console.log('dismissModal called - resetting loading state');\n    this.httpUtilService.loadingSubject.next(false);\n    this.modal.dismiss();\n  }\n\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions to prevent memory leaks\n    this.subscriptions.forEach(subscription => {\n      if (subscription && !subscription.closed) {\n        subscription.unsubscribe();\n      }\n    });\n    this.subscriptions = [];\n    \n    // Ensure loading state is reset when component is destroyed\n    this.httpUtilService.loadingSubject.next(false);\n  }\n}\n", "<div class=\"modal-content h-auto\">\r\n  <div class=\"modal-header bg-light-primary\">\r\n    <div class=\"modal-title h5 fs-3\">\r\n      <ng-container>\r\n        <div *ngIf=\"id === 0\">Add Project</div>\r\n        <div *ngIf=\"id !== 0\">Edit Project - {{ projectName }}</div>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n      <i\r\n        class=\"fa-solid fs-2 fa-xmark text-white\"\r\n        (click)=\"dismissModal()\"\r\n      ></i>\r\n    </div>\r\n  </div>\r\n\r\n  <div\r\n    class=\"modal-body large-modal-body\"\r\n    \r\n  >\r\n    <!--   max-height: calc(100vh - 250px);\r\n      overflow-y: auto;\r\n      position: relative; -->\r\n    <!-- Loading Overlay -->\r\n    <div *ngIf=\"isLoading\" class=\"loading-overlay-inside\">\r\n      <div class=\"text-center\">\r\n        <div class=\"custom-colored-spinner mb-3\" role=\"status\">\r\n          <span class=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n        <div class=\"text-muted\">Loading project data...</div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Initial loading state for form -->\r\n    <div *ngIf=\"!projectForm && !isLoading\" class=\"d-flex justify-content-center align-items-center h-100\">\r\n      <div class=\"text-center\">\r\n        <div class=\"custom-colored-spinner mb-3\" role=\"status\">\r\n          <span class=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n        <div class=\"text-muted\">Initializing form...</div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n      <div class=\"col-xl-12\">\r\n        <div class=\"d-flex\">\r\n          <ul\r\n            class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\"\r\n          >\r\n            <li class=\"nav-item\">\r\n              <a\r\n                class=\"nav-link text-active-primary me-6 cursor-pointer\"\r\n                data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'basic' }\"\r\n                (click)=\"showTab('basic', $event)\"\r\n              >\r\n                Project details\r\n              </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a\r\n                class=\"nav-link text-active-primary me-6 cursor-pointer\"\r\n                data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'role' }\"\r\n                (click)=\"showTab('role', $event)\"\r\n              >\r\n                Project manager\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <form class=\"form form-label-right\" [formGroup]=\"projectForm\" *ngIf=\"projectForm\">\r\n      <ng-container *ngIf=\"selectedTab == 'basic'\">\r\n        <div class=\"row mt-4\">\r\n          <!-- Project Name -->\r\n\r\n          <div class=\"col-xl-12\">\r\n            <div class=\"form-group\">\r\n              <label class=\"fw-bold form-label mb-2\">Project name<sup class=\"text-danger\">*</sup></label>\r\n              <input\r\n                type=\"text\"\r\n                class=\"form-control form-control-sm\"\r\n                formControlName=\"projectName\"\r\n                placeholder=\"Project name\"\r\n              />\r\n               <span\r\n                class=\"custom-error-css\"\r\n                *ngIf=\"controlHasError('required', 'projectName')\"\r\n                >Required Field</span\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Internal Project Number -->\r\n\r\n        </div>\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-6\">\r\n            <div class=\"form-group\">\r\n              <label class=\"fw-bold form-label mb-2\"\r\n                >Internal project # <sup class=\"text-danger\">*</sup></label\r\n              >\r\n              <input\r\n                type=\"text\"\r\n                class=\"form-control form-control-sm\"\r\n                formControlName=\"internalProjectNo\"\r\n                placeholder=\"Internal project\"\r\n              />\r\n              <span\r\n                class=\"custom-error-css\"\r\n                *ngIf=\"controlHasError('required', 'internalProjectNo')\"\r\n                >Required Field</span\r\n              >\r\n            </div>\r\n          </div>\r\n          <!-- Start Date -->\r\n          <div class=\"col-xl-3\">\r\n            <label class=\"fw-bold form-label mb-2\">Start date</label>\r\n            <input\r\n              type=\"date\"\r\n              class=\"form-control form-control-sm\"\r\n              formControlName=\"startDate\"\r\n            />\r\n          </div>\r\n\r\n          <!-- End Date -->\r\n          <div class=\"col-xl-3\">\r\n            <label class=\"fw-bold form-label mb-2\">End date</label>\r\n            <input\r\n              type=\"date\"\r\n              class=\"form-control form-control-sm\"\r\n              formControlName=\"endDate\"\r\n            />\r\n          </div>\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Location<sup class=\"text-danger\">*</sup></label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control form-control-sm\"\r\n              formControlName=\"location\"\r\n              placeholder=\"Location\"\r\n            />\r\n             <span\r\n                class=\"custom-error-css\"\r\n                *ngIf=\"controlHasError('required', 'location')\"\r\n                >Required Field</span\r\n              >\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Description</label>\r\n            <textarea\r\n              class=\"form-control form-control-sm\"\r\n              rows=\"3\"\r\n              formControlName=\"projectDescription\"\r\n            ></textarea>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <div class=\"row mt-4\" *ngIf=\"selectedTab != 'basic'\">\r\n        <!-- Location -->\r\n\r\n        <!-- Manager -->\r\n        <div class=\"col-xl-6\">\r\n          <label class=\"fw-bold form-label mb-2\"\r\n            >Internal project manager <sup class=\"text-danger\">*</sup></label\r\n          >\r\n          <ng-select\r\n            [items]=\"managers\"\r\n            [clearable]=\"false\"\r\n            [multiple]=\"false\"\r\n            bindLabel=\"userFullName\"\r\n            name=\"manager\"\r\n            formControlName=\"manager\"\r\n            bindValue=\"userId\"\r\n            (change)=\"changeInternalManager($event)\"\r\n            placeholder=\"Select an option\"\r\n          >\r\n          </ng-select>\r\n          <span\r\n            class=\"custom-error-css\"\r\n            *ngIf=\"controlHasError('required', 'manager')\"\r\n            >Required Field</span\r\n          >\r\n        </div>\r\n\r\n        <!-- External PM -->\r\n        <div class=\"col-xl-12 mt-4\">\r\n          <label class=\"fw-bold form-label mb-2\"\r\n            >External project manager (multiple)</label\r\n          >\r\n          <ng-select\r\n            [items]=\"externalPMs\"\r\n            [clearable]=\"true\"\r\n            [multiple]=\"true\"\r\n            bindLabel=\"userFullName\"\r\n            name=\"externalPM\"\r\n            formControlName=\"externalPM\"\r\n            bindValue=\"userId\"\r\n            (change)=\"changeexternalPM($event)\"\r\n            placeholder=\"Select an option\"\r\n          >\r\n          </ng-select>\r\n        </div>\r\n      </div>\r\n    </form>\r\n  </div>\r\n\r\n<div class=\"modal-footer d-flex justify-content-between\">\r\n  <!-- Left Side -->\r\n  <div>\r\n    <button\r\n      *ngIf=\"selectedTab != 'basic'\"\r\n      type=\"button\"\r\n      class=\"btn btn-secondary btn-sm btn-elevate\"\r\n      (click)=\"goToPreviousTab()\"\r\n    >\r\n      Previous\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Right Side -->\r\n  <div>\r\n    <button\r\n      class=\"btn btn-danger btn-sm btn-elevate mr-2\"\r\n      (click)=\"dismissModal()\"\r\n    >\r\n      Cancel\r\n    </button>\r\n    &nbsp;\r\n    <button\r\n      *ngIf=\"selectedTab != 'basic'\"\r\n      type=\"button\"\r\n      class=\"btn btn-primary btn-sm\"\r\n      [disabled]=\"projectForm?.invalid\"\r\n      (click)=\"save()\"\r\n    >\r\n      {{ id ? \"Update\" : \"Save\" }}\r\n    </button>\r\n    <button\r\n      *ngIf=\"selectedTab == 'basic'\"\r\n      type=\"button\"\r\n      class=\"btn btn-primary btn-sm\"\r\n      (click)=\"showTab('role', $event)\"\r\n    >\r\n      Next\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n</div>\r\n"], "mappings": "AAAA,SAGEA,YAAY,QAIP,eAAe;AACtB,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICJ3DC,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACvCH,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,oBAAAC,MAAA,CAAAC,WAAA,KAAgC;;;;;IAsBpDP,EAHN,CAAAC,cAAA,cAAsD,cAC3B,cACgC,eACvB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAEnDF,EAFmD,CAAAG,YAAA,EAAM,EACjD,EACF;;;;;IAMAH,EAHN,CAAAC,cAAA,cAAuG,cAC5E,cACgC,eACvB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAEhDF,EAFgD,CAAAG,YAAA,EAAM,EAC9C,EACF;;;;;IA+CKH,EAAA,CAAAC,cAAA,eAGE;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IAoBDH,EAAA,CAAAC,cAAA,eAGG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IA8BFH,EAAA,CAAAC,cAAA,eAGI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IA3ETH,EAAA,CAAAQ,uBAAA,GAA6C;IAMrCR,EALN,CAAAC,cAAA,cAAsB,cAGG,cACG,gBACiB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IAC3FH,EAAA,CAAAS,SAAA,gBAKE;IACDT,EAAA,CAAAU,UAAA,IAAAC,4DAAA,mBAGE;IAOTX,EALI,CAAAG,YAAA,EAAM,EACF,EAIF;IAKAH,EAHN,CAAAC,cAAA,eAAsB,eACE,eACI,iBAEnB;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EACrD;IACDH,EAAA,CAAAS,SAAA,iBAKE;IACFT,EAAA,CAAAU,UAAA,KAAAE,6DAAA,mBAGG;IAGPZ,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAS,SAAA,iBAIE;IACJT,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvDH,EAAA,CAAAS,SAAA,iBAIE;IACJT,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IACvFH,EAAA,CAAAS,SAAA,iBAKE;IACDT,EAAA,CAAAU,UAAA,KAAAG,6DAAA,mBAGI;IAGTb,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAS,SAAA,oBAIY;IAEhBT,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAzEGH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,4BAAgD;IAwBhDf,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,kCAAsD;IAkCtDf,EAAA,CAAAI,SAAA,IAA6C;IAA7CJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,yBAA6C;;;;;IAsCpDf,EAAA,CAAAC,cAAA,eAGG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;;IAnBDH,EALJ,CAAAC,cAAA,cAAqD,cAI7B,gBAEjB;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAC3D;IACDH,EAAA,CAAAC,cAAA,oBAUC;IAFCD,EAAA,CAAAgB,UAAA,oBAAAC,yEAAAC,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAUhB,MAAA,CAAAiB,qBAAA,CAAAL,MAAA,CAA6B;IAAA,EAAC;IAG1ClB,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAU,UAAA,IAAAc,mDAAA,mBAGG;IAELxB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA4B,gBAEvB;IAAAD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EACrC;IACDH,EAAA,CAAAC,cAAA,qBAUC;IAFCD,EAAA,CAAAgB,UAAA,oBAAAS,0EAAAP,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAUhB,MAAA,CAAAoB,gBAAA,CAAAR,MAAA,CAAwB;IAAA,EAAC;IAKzClB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;;;;IApCAH,EAAA,CAAAI,SAAA,GAAkB;IAElBJ,EAFA,CAAAc,UAAA,UAAAR,MAAA,CAAAqB,QAAA,CAAkB,oBACC,mBACD;IAWjB3B,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,wBAA4C;IAW7Cf,EAAA,CAAAI,SAAA,GAAqB;IAErBJ,EAFA,CAAAc,UAAA,UAAAR,MAAA,CAAAsB,WAAA,CAAqB,mBACH,kBACD;;;;;IA/HzB5B,EAAA,CAAAC,cAAA,eAAkF;IA4FhFD,EA3FA,CAAAU,UAAA,IAAAmB,qDAAA,2BAA6C,IAAAC,4CAAA,mBA2FQ;IA8CvD9B,EAAA,CAAAG,YAAA,EAAO;;;;IA1I6BH,EAAA,CAAAc,UAAA,cAAAR,MAAA,CAAAyB,WAAA,CAAyB;IAC5C/B,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA0B,WAAA,YAA4B;IA2FpBhC,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA0B,WAAA,YAA4B;;;;;;IAoDrDhC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAgB,UAAA,mBAAAiB,iEAAA;MAAAjC,EAAA,CAAAmB,aAAA,CAAAe,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAA6B,eAAA,EAAiB;IAAA,EAAC;IAE3BnC,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAYTH,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAgB,UAAA,mBAAAoB,iEAAA;MAAApC,EAAA,CAAAmB,aAAA,CAAAkB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAAgC,IAAA,EAAM;IAAA,EAAC;IAEhBtC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAc,UAAA,aAAAR,MAAA,CAAAyB,WAAA,kBAAAzB,MAAA,CAAAyB,WAAA,CAAAQ,OAAA,CAAiC;IAGjCvC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAkC,EAAA,0BACF;;;;;;IACAxC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAgB,UAAA,mBAAAyB,iEAAAvB,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAuB,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAAqC,OAAA,CAAQ,MAAM,EAAAzB,MAAA,CAAS;IAAA,EAAC;IAEjClB,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADxOb,OAAM,MAAOyC,qBAAqB;EAavBC,KAAA;EACCC,GAAA;EAEAC,EAAA;EACAC,UAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,eAAA;EACAC,YAAA;EApBDZ,EAAE,GAAW,CAAC,CAAC,CAAC;EAChBa,OAAO,CAAM,CAAC;EACbC,SAAS,GAAsB,IAAIxD,YAAY,EAAE;EAC3DiC,WAAW,GAAqB,IAAI;EACpCwB,SAAS,GAAQ,EAAE;EACnB5B,QAAQ,GAAQ,EAAE;EAClBC,WAAW,GAAQ,EAAE;EACrB4B,SAAS,GAAY,KAAK;EAC1BxB,WAAW,GAAW,OAAO,CAAC,CAAC;EAC/BzB,WAAW;EACHkD,aAAa,GAAmB,EAAE;EAC1CC,YACSb,KAAqB,EACpBC,GAAsB,EAEtBC,EAAe,EACfC,UAAsB,EACtBC,eAAiC,EACjCC,wBAAkD,EAClDC,eAAgC;EAAE;EAClCC,YAAyB;IAR1B,KAAAP,KAAK,GAALA,KAAK;IACJ,KAAAC,GAAG,GAAHA,GAAG;IAEH,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IAEpB;IACA,MAAMO,mBAAmB,GAAG,IAAI,CAACV,eAAe,CAACW,cAAc,CAACC,SAAS,CAAEC,OAAO,IAAI;MACpFC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEF,OAAO,CAAC;MAC9D,IAAI,CAACN,SAAS,GAAGM,OAAO;IAC1B,CAAC,CAAC;IACF,IAAI,CAACL,aAAa,CAACQ,IAAI,CAACN,mBAAmB,CAAC;EAC9C;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACX,SAAS,GAAG,IAAI,CAACP,UAAU,CAACmB,eAAe,EAAE;IAClD,IAAI,CAACC,QAAQ,EAAE;IAEf;IACA,IAAI,IAAI,CAAC5B,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAACgB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACP,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,IAAI,CAAC;MAC9C;MACA,IAAI,CAACC,wBAAwB,EAAE;IACjC,CAAC,MAAM;MACL;MACA,IAAI,CAACC,mBAAmB,EAAE;MAC1B,IAAI,CAACC,oBAAoB,EAAE;IAC7B;EACF;EAEAJ,QAAQA,CAAA;IACN,IAAI,CAACrC,WAAW,GAAG,IAAI,CAACgB,EAAE,CAAC0B,KAAK,CAAC;MAC/BlE,WAAW,EAAE,CAAC,EAAE,EAAER,UAAU,CAAC2E,QAAQ,CAAC;MACtCC,iBAAiB,EAAE,CAAC,EAAE,EAAE5E,UAAU,CAAC2E,QAAQ,CAAC;MAC5CE,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,OAAO,EAAE,CAAC,EAAE,CAAC;MAAE;MACfC,QAAQ,EAAE,CAAC,EAAE,EAAE/E,UAAU,CAAC2E,QAAQ,CAAC;MACnCK,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,OAAO,EAAE,CAAC,IAAI,EAAEjF,UAAU,CAAC2E,QAAQ,CAAC;MACpCO,UAAU,EAAE,CAAC,EAAE,CAAC,CAAE;KACnB,CAAC;IAEF;IACA,IAAI,CAACnC,GAAG,CAACoC,aAAa,EAAE;EAC1B;EACA;EACAX,mBAAmBA,CAAA;IACjB,MAAMY,YAAY,GAAG,IAAI,CAAC/B,YAAY,CACnCgC,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDxB,SAAS,CAAEyB,mBAAwB,IAAI;MACtC,MAAMC,KAAK,GAAGD,mBAAmB,EAAEE,YAAY,EAAED,KAAK;MACtD,IAAI,CAAC5D,QAAQ,GAAG8D,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;IACnD,CAAC,CAAC;IACJ,IAAI,CAAC9B,aAAa,CAACQ,IAAI,CAACkB,YAAY,CAAC;EACvC;EACA;EACAX,oBAAoBA,CAAA;IAClB,MAAMW,YAAY,GAAG,IAAI,CAAC/B,YAAY,CACnCgC,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDxB,SAAS,CAAE8B,mBAAwB,IAAI;MACtC,MAAMJ,KAAK,GAAGI,mBAAmB,EAAEH,YAAY,EAAED,KAAK;MACtD,IAAI,CAAC3D,WAAW,GAAG6D,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;IACtD,CAAC,CAAC;IACJ,IAAI,CAAC9B,aAAa,CAACQ,IAAI,CAACkB,YAAY,CAAC;EACvC;EAEA;EACAb,wBAAwBA,CAAA;IACtB;IACA,MAAMsB,kBAAkB,GAAG,IAAI,CAACxC,YAAY,CACzCgC,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDQ,SAAS,EAAE;IAEd,MAAMC,kBAAkB,GAAG,IAAI,CAAC1C,YAAY,CACzCgC,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDQ,SAAS,EAAE;IAEdE,OAAO,CAACC,GAAG,CAAC,CAACJ,kBAAkB,EAAEE,kBAAkB,CAAC,CAAC,CAClDG,IAAI,CAAC,CAAC,CAACC,gBAAgB,EAAEC,gBAAgB,CAAC,KAAI;MAC7C;MACA,IAAI,CAACxE,QAAQ,GAAG8D,KAAK,CAACC,OAAO,CAACQ,gBAAgB,EAAEV,YAAY,EAAED,KAAK,CAAC,GAChEW,gBAAgB,CAACV,YAAY,CAACD,KAAK,GAAG,EAAE;MAC5C,IAAI,CAAC3D,WAAW,GAAG6D,KAAK,CAACC,OAAO,CAACS,gBAAgB,EAAEX,YAAY,EAAED,KAAK,CAAC,GACnEY,gBAAgB,CAACX,YAAY,CAACD,KAAK,GAAG,EAAE;MAE5C;MACA,IAAI,CAACa,SAAS,EAAE;IAClB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;MACbvC,OAAO,CAACuC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACA,IAAI,CAACF,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EAEAA,SAASA,CAAA;IACP;IACA,MAAMjB,YAAY,GAAG,IAAI,CAAChC,eAAe,CACtCoD,UAAU,CAAC;MAAEC,SAAS,EAAE,IAAI,CAAChE,EAAE;MAAEiE,cAAc,EAAE,IAAI,CAAClD,SAAS,CAACmD;IAAM,CAAE,CAAC,CACzE7C,SAAS,CAAC;MACTQ,IAAI,EAAGsC,eAAoB,IAAI;QAC7B,IAAI,CAAC1D,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACsC,eAAe,CAACC,OAAO,EAAE;UAC5B,IAAIC,WAAW,GAAGF,eAAe,CAACnB,YAAY,CAACsB,OAAO;UACtD;UACA/C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6C,WAAW,CAAC;UAClD9C,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACrC,QAAQ,CAAC;UACjDoC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACpC,WAAW,CAAC;UACxD,IAAI,CAACrB,WAAW,GAAGsG,WAAW,CAACtG,WAAW;UAE1C;UACA,MAAMwG,kBAAkB,GAAIC,SAAc,IAAI;YAC5C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;YACzB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;YAChC,OAAOC,IAAI,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC;UAED;UACA,MAAMC,YAAY,GAAIC,SAAc,IAAI;YACtC,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;YAC3B,OAAOC,QAAQ,CAACD,SAAS,CAAC;UAC5B,CAAC;UAED;UACA,MAAME,gBAAgB,GAAIC,qBAA6B,IAAI;YACzD,IAAI,CAACA,qBAAqB,EAAE,OAAO,EAAE;YACrC;YACA,MAAMC,OAAO,GAAGD,qBAAqB,CAACL,KAAK,CAAC,GAAG,CAAC,CAACO,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAAC,CAACC,MAAM,CAACF,IAAI,IAAIA,IAAI,KAAK,EAAE,CAAC;YACrG,OAAOF,OAAO,CAACC,GAAG,CAACC,IAAI,IAAG;cACxB,MAAMG,EAAE,GAAG,IAAI,CAACnG,WAAW,CAACoG,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACC,YAAY,KAAKN,IAAI,CAAC;cACzE,OAAOG,EAAE,GAAGA,EAAE,CAACrB,MAAM,GAAG,IAAI;YAC9B,CAAC,CAAC,CAACoB,MAAM,CAACtF,EAAE,IAAIA,EAAE,KAAK,IAAI,CAAC;UAC9B,CAAC;UAED,MAAM2F,QAAQ,GAAG;YACf5H,WAAW,EAAEsG,WAAW,CAACtG,WAAW;YACpCoE,iBAAiB,EAAEkC,WAAW,CAACuB,qBAAqB;YACpDxD,SAAS,EAAEmC,kBAAkB,CAACF,WAAW,CAACwB,gBAAgB,CAAC;YAC3DxD,OAAO,EAAEkC,kBAAkB,CAACF,WAAW,CAACyB,cAAc,CAAC;YACvDxD,QAAQ,EAAE+B,WAAW,CAAC0B,eAAe;YACrCxD,kBAAkB,EAAE8B,WAAW,CAAC9B,kBAAkB;YAClDC,OAAO,EAAEqC,YAAY,CAACR,WAAW,CAAC2B,sBAAsB,CAAC;YACzDvD,UAAU,EAAEuC,gBAAgB,CAACX,WAAW,CAAC4B,eAAe;WACzD;UAED1E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEmE,QAAQ,CAAC;UAC5C,IAAI,IAAI,CAACpG,WAAW,EAAE;YACpB,IAAI,CAACA,WAAW,CAAC2G,UAAU,CAACP,QAAQ,CAAC;UACvC;QACF,CAAC,MAAM;UACLpE,OAAO,CAAC4E,IAAI,CACV,kCAAkC,EAClChC,eAAe,CAACnB,YAAY,CAC7B;QACH;MACF,CAAC;MACDc,KAAK,EAAGsC,GAAG,IAAI;QACb,IAAI,CAAC3F,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,KAAK,CAAC;QAC/CN,OAAO,CAACuC,KAAK,CAAC,iBAAiB,EAAEsC,GAAG,CAAC;MACvC;KACD,CAAC;IACJ,IAAI,CAACnF,aAAa,CAACQ,IAAI,CAACkB,YAAY,CAAC;EACvC;EACA7C,IAAIA,CAAA;IACF,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;MACrB,IAAI,CAACmB,wBAAwB,CAAC2F,SAAS,CACrC,4CAA4C,EAC5C,EAAE,CACH;MACD;IACF;IAEA,IAAIC,QAAQ,GAAG,IAAI,CAAC/G,WAAY,CAAC+G,QAAQ;IACzC,IAAI,IAAI,CAAC/G,WAAY,CAACQ,OAAO,EAAE;MAC7BwG,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAEC,WAAW,IACxCJ,QAAQ,CAACI,WAAW,CAAC,CAACC,aAAa,EAAE,CACtC;MACD,IAAI,CAACjG,wBAAwB,CAAC2F,SAAS,CACrC,iCAAiC,EACjC,EAAE,CACH;MACD;IACF;IACA,IAAIhC,WAAW,GAAQ,IAAI,CAACuC,kBAAkB,EAAE;IAChDrF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE6C,WAAW,CAAC;IACrC,IAAI,IAAI,CAACrE,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC6G,MAAM,CAACxC,WAAW,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACyC,IAAI,CAACzC,WAAW,CAAC;IACxB;EACF;EAEAuC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACrH,WAAW,EAAE;MACrB,MAAM,IAAIwH,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMpB,QAAQ,GAAG,IAAI,CAACpG,WAAY,CAACyH,KAAK;IACxC,IAAIC,kBAAkB,GAAQ,EAAE;IAChCA,kBAAkB,CAAClJ,WAAW,GAAG4H,QAAQ,CAAC5H,WAAW;IACrDkJ,kBAAkB,CAACrB,qBAAqB,GAAGD,QAAQ,CAACxD,iBAAiB;IACrE8E,kBAAkB,CAACpB,gBAAgB,GAAGF,QAAQ,CAACvD,SAAS;IACxD6E,kBAAkB,CAACnB,cAAc,GAAGH,QAAQ,CAACtD,OAAO;IACpD4E,kBAAkB,CAAClB,eAAe,GAAGJ,QAAQ,CAACrD,QAAQ;IACtD2E,kBAAkB,CAAC1E,kBAAkB,GAAGoD,QAAQ,CAACpD,kBAAkB;IAEnE;IACA0E,kBAAkB,CAACjB,sBAAsB,GAAGL,QAAQ,CAACnD,OAAO;IAE5D;IACA,MAAM0E,gBAAgB,GAAIC,OAAiB,IAAI;MAC7C,IAAI,CAACA,OAAO,IAAI,CAAClE,KAAK,CAACC,OAAO,CAACiE,OAAO,CAAC,EAAE,OAAO,EAAE;MAClD,OAAOA,OAAO,CAAC7B,MAAM,CAACtF,EAAE,IAAIA,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAKoH,SAAS,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACxE,CAAC;IAEDJ,kBAAkB,CAACxE,UAAU,GAAGyE,gBAAgB,CAACvB,QAAQ,CAAClD,UAAU,CAAC;IACrEwE,kBAAkB,CAAChD,cAAc,GAAG,IAAI,CAAClD,SAAS,CAACmD,MAAM;IAEzD3C,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEyF,kBAAkB,CAAC;IACrE,OAAOA,kBAAkB;EAC3B;EACA;EACAH,IAAIA,CAACzC,WAAgB;IACnBA,WAAW,CAACL,SAAS,GAAG,IAAI,CAAChE,EAAE;IAC/B,IAAI,CAACS,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAMc,YAAY,GAAG,IAAI,CAAChC,eAAe,CAAC2G,aAAa,CAACjD,WAAW,CAAC,CAAChD,SAAS,CAAEkG,GAAG,IAAI;MACrF,IAAI,CAAC9G,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAAC0F,GAAG,CAACnD,OAAO,EAAE;QAChB,IAAI,CAAC1D,wBAAwB,CAAC8G,WAAW,CAACD,GAAG,CAACvE,YAAY,CAACyE,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAC3G,SAAS,CAAC4G,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACrH,KAAK,CAACsH,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;MAC1B,CAAC,MAAM;QACL,IAAI,CAACjH,wBAAwB,CAAC2F,SAAS,CAACkB,GAAG,CAACvE,YAAY,CAACyE,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC3G,SAAS,CAAC4G,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,IAAI,CAACzG,aAAa,CAACQ,IAAI,CAACkB,YAAY,CAAC;EACvC;EACA;EACAkE,MAAMA,CAACxC,WAAgB;IACrB,IAAI,CAAC5D,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAMc,YAAY,GAAG,IAAI,CAAChC,eAAe,CAACiH,aAAa,CAACvD,WAAW,CAAC,CAAChD,SAAS,CAAEkG,GAAQ,IAAI;MAC1F,IAAI,CAAC9G,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAAC0F,GAAG,CAACnD,OAAO,EAAE;QAChB,IAAI,CAAC1D,wBAAwB,CAAC8G,WAAW,CAACD,GAAG,CAACvE,YAAY,CAACyE,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAC3G,SAAS,CAAC4G,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACrH,KAAK,CAACsH,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;MAC1B,CAAC,MAAM;QACL,IAAI,CAACjH,wBAAwB,CAAC2F,SAAS,CAACkB,GAAG,CAACvE,YAAY,CAACyE,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC3G,SAAS,CAAC4G,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,IAAI,CAACzG,aAAa,CAACQ,IAAI,CAACkB,YAAY,CAAC;EACvC;EACAxC,OAAOA,CAAC0H,GAAQ,EAAEnJ,MAAW;IAC3B,IAAI,CAACc,WAAW,GAAGqI,GAAG;IACtB,IAAI,CAACvH,GAAG,CAACwH,YAAY,EAAE;EACzB;EACA/I,qBAAqBA,CAACgJ,KAAU,GAAG;EACnC7I,gBAAgBA,CAAC6I,KAAU,GAAG;EAC9BxJ,eAAeA,CAACyJ,UAAe,EAAEtB,WAA4B;IAC3D,IAAI,CAAC,IAAI,CAACnH,WAAW,EAAE;MACrB,OAAO,KAAK;IACd;IAEA,MAAM0I,OAAO,GAAG,IAAI,CAAC1I,WAAY,CAAC+G,QAAQ,CAACI,WAAW,CAAC;IACvD,IAAI,CAACuB,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;IACA,IAAIC,MAAM,GACRD,OAAO,CAACE,QAAQ,CAACH,UAAU,CAAC,KAAKC,OAAO,CAACG,KAAK,IAAIH,OAAO,CAACI,OAAO,CAAC;IACpE,OAAOH,MAAM;EACf;EACEvI,eAAeA,CAAA;IACf;IACA;IACA;IACA;IACA,IAAI,CAACH,WAAW,GAAG,OAAO;IAC1B,IAAI,CAACc,GAAG,CAACwH,YAAY,EAAE;EACzB;EAEA;EACAQ,YAAYA,CAAA;IACV/G,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5D,IAAI,CAACf,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,KAAK,CAAC;IAC/C,IAAI,CAACxB,KAAK,CAACkI,OAAO,EAAE;EACtB;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACvH,aAAa,CAACwF,OAAO,CAAC9D,YAAY,IAAG;MACxC,IAAIA,YAAY,IAAI,CAACA,YAAY,CAAC8F,MAAM,EAAE;QACxC9F,YAAY,CAAC+F,WAAW,EAAE;MAC5B;IACF,CAAC,CAAC;IACF,IAAI,CAACzH,aAAa,GAAG,EAAE;IAEvB;IACA,IAAI,CAACR,eAAe,CAACW,cAAc,CAACS,IAAI,CAAC,KAAK,CAAC;EACjD;;qCA9TWzB,qBAAqB,EAAA5C,EAAA,CAAAmL,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArL,EAAA,CAAAmL,iBAAA,CAAAnL,EAAA,CAAAsL,iBAAA,GAAAtL,EAAA,CAAAmL,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAxL,EAAA,CAAAmL,iBAAA,CAAAM,EAAA,CAAAC,UAAA,GAAA1L,EAAA,CAAAmL,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAA5L,EAAA,CAAAmL,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAA9L,EAAA,CAAAmL,iBAAA,CAAAY,EAAA,CAAAC,eAAA,GAAAhM,EAAA,CAAAmL,iBAAA,CAAAc,EAAA,CAAAC,WAAA;EAAA;;UAArBtJ,qBAAqB;IAAAuJ,SAAA;IAAAC,MAAA;MAAA5J,EAAA;MAAAa,OAAA;IAAA;IAAAgJ,OAAA;MAAA/I,SAAA;IAAA;IAAAgJ,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnB9B3M,EAFJ,CAAAC,cAAA,aAAkC,aACW,aACR;QAC/BD,EAAA,CAAAQ,uBAAA,GAAc;QAEZR,EADA,CAAAU,UAAA,IAAAmM,oCAAA,iBAAsB,IAAAC,oCAAA,iBACA;;QAE1B9M,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAItB;QADCD,EAAA,CAAAgB,UAAA,mBAAA+L,kDAAA;UAAA,OAASH,GAAA,CAAA9B,YAAA,EAAc;QAAA,EAAC;QAG9B9K,EAFK,CAAAG,YAAA,EAAI,EACD,EACF;QAENH,EAAA,CAAAC,cAAA,aAGC;QAeCD,EAVA,CAAAU,UAAA,IAAAsM,oCAAA,iBAAsD,KAAAC,qCAAA,iBAUiD;QAgB7FjN,EAPV,CAAAC,cAAA,cAAiB,eACQ,eACD,cAGjB,cACsB,aAMlB;QADCD,EAAA,CAAAgB,UAAA,mBAAAkM,mDAAAhM,MAAA;UAAA,OAAS0L,GAAA,CAAAjK,OAAA,CAAQ,OAAO,EAAAzB,MAAA,CAAS;QAAA,EAAC;QAElClB,EAAA,CAAAE,MAAA,yBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAMlB;QADCD,EAAA,CAAAgB,UAAA,mBAAAmM,mDAAAjM,MAAA;UAAA,OAAS0L,GAAA,CAAAjK,OAAA,CAAQ,MAAM,EAAAzB,MAAA,CAAS;QAAA,EAAC;QAEjClB,EAAA,CAAAE,MAAA,yBACF;QAKVF,EALU,CAAAG,YAAA,EAAI,EACD,EACF,EACD,EACF,EACF;QAENH,EAAA,CAAAU,UAAA,KAAA0M,sCAAA,mBAAkF;QA2IpFpN,EAAA,CAAAG,YAAA,EAAM;QAINH,EAFF,CAAAC,cAAA,eAAyD,WAElD;QACHD,EAAA,CAAAU,UAAA,KAAA2M,wCAAA,qBAKC;QAGHrN,EAAA,CAAAG,YAAA,EAAM;QAIJH,EADF,CAAAC,cAAA,WAAK,kBAIF;QADCD,EAAA,CAAAgB,UAAA,mBAAAsM,wDAAA;UAAA,OAASV,GAAA,CAAA9B,YAAA,EAAc;QAAA,EAAC;QAExB9K,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAE,MAAA,gBACA;QASAF,EATA,CAAAU,UAAA,KAAA6M,wCAAA,qBAMC,KAAAC,wCAAA,qBAQA;QAMLxN,EAHE,CAAAG,YAAA,EAAM,EACF,EAEA;;;QA7PQH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAc,UAAA,SAAA8L,GAAA,CAAApK,EAAA,OAAc;QACdxC,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAc,UAAA,SAAA8L,GAAA,CAAApK,EAAA,OAAc;QAmBlBxC,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAc,UAAA,SAAA8L,GAAA,CAAApJ,SAAA,CAAe;QAUfxD,EAAA,CAAAI,SAAA,EAAgC;QAAhCJ,EAAA,CAAAc,UAAA,UAAA8L,GAAA,CAAA7K,WAAA,KAAA6K,GAAA,CAAApJ,SAAA,CAAgC;QAmB1BxD,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAyN,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAA5K,WAAA,cAA+C;QAU/ChC,EAAA,CAAAI,SAAA,GAA8C;QAA9CJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAyN,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAA5K,WAAA,aAA8C;QAWKhC,EAAA,CAAAI,SAAA,GAAiB;QAAjBJ,EAAA,CAAAc,UAAA,SAAA8L,GAAA,CAAA7K,WAAA,CAAiB;QAiJ7E/B,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA8L,GAAA,CAAA5K,WAAA,YAA4B;QAmB5BhC,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA8L,GAAA,CAAA5K,WAAA,YAA4B;QAS5BhC,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA8L,GAAA,CAAA5K,WAAA,YAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}