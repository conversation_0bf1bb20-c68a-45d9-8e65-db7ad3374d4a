{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { jwtDecode } from 'jwt-decode';\nimport { ChangePasswordComponent } from '../change-password/change-password.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/modules/services/app.service\";\nimport * as i5 from \"src/app/modules/services/http-utils.service\";\nimport * as i6 from \"src/app/modules/services/custom-layout.utils.service\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"src/app/_metronic/layout/core/page-info.service\";\nimport * as i9 from \"@angular/platform-browser\";\nimport * as i10 from \"@angular/common\";\nconst _c0 = (a0, a1) => ({\n  \"is-invalid\": a0,\n  \"is-valid\": a1\n});\nconst _c1 = a0 => ({\n  validation: \"required\",\n  message: \"Email is required\",\n  control: a0\n});\nconst _c2 = a0 => ({\n  validation: \"email\",\n  message: \"Email is invalid\",\n  control: a0\n});\nconst _c3 = a0 => ({\n  validation: \"required\",\n  message: \"Password is required\",\n  control: a0\n});\nfunction LoginComponent_ng_template_36_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"span\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r2 = i0.ɵɵnextContext().message;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", message_r2, \" \");\n  }\n}\nfunction LoginComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LoginComponent_ng_template_36_ng_container_0_Template, 4, 1, \"ng-container\", 25);\n  }\n  if (rf & 2) {\n    const control_r3 = ctx.control;\n    const validation_r4 = ctx.validation;\n    i0.ɵɵproperty(\"ngIf\", control_r3.hasError(validation_r4) && (control_r3.dirty || control_r3.touched));\n  }\n}\nexport class LoginComponent {\n  fb;\n  authService;\n  route;\n  appService;\n  httpUtilService;\n  customLayoutUtilsService;\n  router;\n  modalService;\n  pageInfo;\n  titleService;\n  // KeenThemes mock, change it to:\n  defaultAuth = {\n    email: '<EMAIL>',\n    password: 'SwiJZf'\n  };\n  loginForm;\n  hasError;\n  returnUrl;\n  isLoading$;\n  passwordshown = true; // password show/hide\n  userData = {};\n  // private fields\n  unsubscribe = []; // Read more: => https://brianflove.com/2016/12/11/anguar-2-unsubscribe-observables/\n  constructor(fb, authService, route, appService, httpUtilService, customLayoutUtilsService, router, modalService, pageInfo, titleService) {\n    this.fb = fb;\n    this.authService = authService;\n    this.route = route;\n    this.appService = appService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.router = router;\n    this.modalService = modalService;\n    this.pageInfo = pageInfo;\n    this.titleService = titleService;\n    this.isLoading$ = this.authService.isLoading$;\n    // redirect to home if already logged in\n    if (this.authService.currentUserValue) {\n      this.router.navigate(['/']);\n    }\n  }\n  ngOnInit() {\n    // Ensure title is set in auth layout where global scripts may be inactive\n    this.pageInfo.updateTitle('Login');\n    this.titleService.setTitle('Login - Permit Tracker');\n    this.initForm();\n    // get return url from route parameters or default to '/'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'.toString()] || '/';\n  }\n  // convenience getter for easy access to form fields\n  get f() {\n    return this.loginForm.controls;\n  }\n  initForm() {\n    this.loginForm = this.fb.group({\n      email: ['', Validators.compose([Validators.required, Validators.email, Validators.minLength(3), Validators.maxLength(320) // https://stackoverflow.com/questions/386294/what-is-the-maximum-length-of-a-valid-email-address\n      ])],\n      password: ['', Validators.compose([Validators.required, Validators.minLength(3), Validators.maxLength(100)])],\n      remember: [false]\n    });\n    let rememberMeLocalStorage = this.appService.getLocalStorageItem('permitRemember', false);\n    let userLocallyStoredData = this.appService.getLocalStorageItem('permitUserAuth', true);\n    //condition for patching  remember me based on boolean\n    if (rememberMeLocalStorage === 'true' || rememberMeLocalStorage === true) {\n      this.loginForm.patchValue({\n        email: userLocallyStoredData.userName,\n        password: userLocallyStoredData.password,\n        remember: true\n      });\n    }\n  }\n  submit() {\n    this.hasError = false;\n    const controlsOfForm = this.loginForm.controls;\n    if (this.loginForm.invalid) {\n      return;\n    }\n    const authData = {\n      userName: controlsOfForm.email.value,\n      password: controlsOfForm.password.value\n    };\n    // Show global loader\n    this.httpUtilService.loadingSubject.next(true);\n    // //API call for login\n    this.authService.login(authData).subscribe(data => {\n      if (data.isFault === false) {\n        this.userData = data.responseData;\n        console.log(\"data.responseData\", data.responseData);\n        if (this.loginForm.value.remember === true) {\n          this.appService.setLocalStorageItem('permitRemember', 'true', false);\n        } else {\n          this.appService.setLocalStorageItem('permitRemember', 'false', false);\n        }\n        this.appService.setLocalStorageItem('permitUserAuth', authData, true);\n        // set the local storage for the user details\n        this.appService.setLocalStorageItem(\"permitUser\", data.responseData, true);\n        this.appService.setLocalStorageItem(\"permitToken\", data.responseData.token, true);\n        // this.layoutUtilService.showSuccess(data.responseData.message, '');\n        const token = data.responseData.token;\n        const decodedToken = jwtDecode(token);\n        const expirationTime = decodedToken.exp;\n        console.log('decodedToken ', decodedToken);\n        console.log('expirationTime ', expirationTime);\n        this.authService.setToken(data.responseData.token, expirationTime);\n        if (data.responseData.isPasswordChanged === false) {\n          this.changePassword();\n        } else {\n          // Keep loader active during navigation and page reload\n          this.router.navigate(['/dashboard'], {\n            replaceUrl: true\n          }).then(() => {\n            location.reload();\n          });\n        }\n      } else {\n        // Hide loader on error\n        this.httpUtilService.loadingSubject.next(false);\n        this.customLayoutUtilsService.showSuccess(data.responseData.message, '');\n        // //alert(data.responseData.message);\n      }\n    }, error => {\n      // Hide loader on error\n      this.httpUtilService.loadingSubject.next(false);\n      console.error('Login error:', error);\n    });\n  }\n  //function for opening pop up for change password\n  changePassword() {\n    // define the NgbModels options.\n    const modalOption = {};\n    modalOption.backdrop = 'static';\n    modalOption.keyboard = false;\n    modalOption.size = 'md';\n    // open the NgbModel for Confirmation.\n    const modalRef = this.modalService.open(ChangePasswordComponent, modalOption);\n    modalRef.componentInstance.showClose = false;\n    //get response from edit user modal\n    modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n      if (receivedEntry == true) {\n        // this.router.navigate(['/dashboard'], {relativeTo: this.route});\n        this.router.navigate(['/dashboard'], {\n          replaceUrl: true\n        }).then(() => {\n          location.reload();\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n  //function for show hide password\n  showpassword(event) {\n    this.passwordshown = event;\n  }\n  static ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.HttpUtilsService), i0.ɵɵdirectiveInject(i6.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i7.NgbModal), i0.ɵɵdirectiveInject(i8.PageInfoService), i0.ɵɵdirectiveInject(i9.Title));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 38,\n    vars: 26,\n    consts: [[\"formError\", \"\"], [\"novalidate\", \"novalidate\", \"id\", \"kt_login_signin_form\", 1, \"form\", \"w-100\", 3, \"ngSubmit\", \"formGroup\"], [1, \"text-center\", \"mb-10\"], [\"src\", \"./assets/media/logos/cropped-Pacifica-Logo.png\", \"alt\", \"Pacifica Engineering Services\", 1, \"h-120px\", \"logo\", 2, \"width\", \"250px\"], [1, \"fv-row\", \"mb-10\"], [1, \"form-label\", \"fw-bold\"], [1, \"text-danger\"], [\"type\", \"email\", \"name\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"off\", 1, \"form-control\", \"form-control-sm\", \"form-control-solid\", 3, \"ngClass\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"fv-row\", \"mb-4\"], [1, \"d-flex\", \"justify-content-between\", \"mt-n5\"], [1, \"d-flex\", \"flex-stack\", \"mb-2\"], [1, \"form-label\", \"fw-bold\", \"mb-0\"], [1, \"mb-0\"], [\"name\", \"password\", \"autocomplete\", \"off\", \"formControlName\", \"password\", 1, \"form-control\", \"form-control-sm\", \"form-control-solid\", 3, \"type\", \"ngClass\"], [1, \"toggle-password\"], [2, \"margin-top\", \"-29px\", 3, \"click\", \"ngClass\"], [1, \"d-flex\", \"flex-stack\", \"flex-wrap\", \"gap-3\", \"fs-base\", \"fw-semibold\", \"mb-8\"], [1, \"form-check\", \"form-check-sm\", \"form-check-custom\", \"me-5\"], [\"type\", \"checkbox\", \"formControlName\", \"remember\", 1, \"form-check-input\"], [1, \"form-check-label\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"link-primary\"], [1, \"text-center\"], [\"type\", \"submit\", \"id\", \"kt_sign_in_submit\", 1, \"btn\", \"btn-sm\", \"btn-primary\", \"mb-5\", 3, \"disabled\"], [1, \"indicator-label\"], [4, \"ngIf\"], [1, \"fv-plugins-message-container\"], [\"role\", \"alert\", 1, \"text-danger\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"form\", 1);\n        i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.submit());\n        });\n        i0.ɵɵelementStart(1, \"div\", 2);\n        i0.ɵɵelement(2, \"img\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 4)(4, \"label\", 5);\n        i0.ɵɵtext(5, \"Email\");\n        i0.ɵɵelementStart(6, \"sup\", 6);\n        i0.ɵɵtext(7, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(8, \"input\", 7);\n        i0.ɵɵelementContainer(9, 8)(10, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"label\", 12);\n        i0.ɵɵtext(15, \"Password\");\n        i0.ɵɵelementStart(16, \"sup\", 6);\n        i0.ɵɵtext(17, \"*\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(18, \"div\", 13);\n        i0.ɵɵelement(19, \"input\", 14);\n        i0.ɵɵelementStart(20, \"div\", 15)(21, \"span\", 16);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_span_click_21_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.passwordshown === true ? ctx.showpassword(false) : ctx.showpassword(true));\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementContainer(22, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\")(25, \"label\", 18);\n        i0.ɵɵelement(26, \"input\", 19);\n        i0.ɵɵelementStart(27, \"span\", 20);\n        i0.ɵɵtext(28, \"Remember me ?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(29, \"a\", 21);\n        i0.ɵɵtext(30, \" Forgot Password ? \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"div\", 22)(32, \"button\", 23);\n        i0.ɵɵpipe(33, \"async\");\n        i0.ɵɵelementStart(34, \"span\", 24);\n        i0.ɵɵtext(35, \"Login\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(36, LoginComponent_ng_template_36_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const formError_r5 = i0.ɵɵreference(37);\n        i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c0, ctx.loginForm.controls[\"email\"].invalid, ctx.loginForm.controls[\"email\"].valid));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", formError_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(17, _c1, ctx.loginForm.controls[\"email\"]));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", formError_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(19, _c2, ctx.loginForm.controls[\"email\"]));\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"type\", ctx.passwordshown === true ? \"password\" : \"text\")(\"ngClass\", i0.ɵɵpureFunction2(21, _c0, ctx.loginForm.controls[\"password\"].invalid, ctx.loginForm.controls[\"password\"].valid));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", ctx.passwordshown === true ? \"bi bi-eye-slash-fill\" : \"bi bi-eye-fill\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", formError_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(24, _c3, ctx.loginForm.controls[\"password\"]));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || i0.ɵɵpipeBind1(33, 12, ctx.isLoading$));\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgIf, i10.NgTemplateOutlet, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i10.AsyncPipe],\n    styles: [\"[_nghost-%COMP%] {\\n  width: 100%;\\n}\\n@media (min-width: 992px) {\\n  [_nghost-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 450px;\\n  }\\n  [_nghost-%COMP%]   .login-form[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n.toggle-password[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  float: right;\\n  cursor: pointer;\\n  margin-right: 43px;\\n  margin-top: -33px;\\n  font-size: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9hdXRoL2NvbXBvbmVudHMvbG9naW4vbG9naW4uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0FBQ0Y7QUFBRTtFQUNFO0lBQ0UsV0FBQTtJQUNBLGdCQUFBO0VBRUo7RUFBSTtJQUNFLFdBQUE7RUFFTjtBQUNGOztBQUdBO0VBQ0UsWUFBQTtFQUNFLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZUFBQTtBQUFKIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIEBtZWRpYSAobWluLXdpZHRoOiA5OTJweCkge1xyXG4gICAgLmxvZ2luLWZvcm0ge1xyXG4gICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgbWF4LXdpZHRoOiA0NTBweDtcclxuXHJcbiAgICAgIC5tYXQtZm9ybS1maWVsZCB7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi50b2dnbGUtcGFzc3dvcmQgc3BhbntcclxuICBmbG9hdDogcmlnaHQ7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICBtYXJnaW4tcmlnaHQ6IDQzcHg7XHJcbiAgICBtYXJnaW4tdG9wOiAtMzNweDtcclxuICAgIGZvbnQtc2l6ZTogMTZweDtcclxufVxyXG5cclxuLy8gLmZvcm0tY2hlY2stY3VzdG9tLmZvcm0tY2hlY2stc29saWQgLmZvcm0tY2hlY2staW5wdXQge1xyXG4vLyAgIGJvcmRlcjogMnB4IHNvbGlkIHJnYigxMDIsIDEwNSwgMTAyKSAhaW1wb3J0YW50O1xyXG4vLyAgIGJhY2tncm91bmQtY29sb3I6ICNmMWYxZjEgIWltcG9ydGFudDtcclxuICAgIFxyXG4vLyB9XHJcblxyXG4vLyAuZm9ybS1jaGVjay1jdXN0b20uZm9ybS1jaGVjay1zb2xpZCAuZm9ybS1jaGVjay1pbnB1dFt0eXBlPWNoZWNrYm94XSB7XHJcbi8vICAgYmFja2dyb3VuZC1jb2xvcjogI2YxZjFmMSAhaW1wb3J0YW50O1xyXG4gICAgXHJcbi8vIH1cclxuXHJcbi8vIC5mb3JtLWNoZWNrLWN1c3RvbS5mb3JtLWNoZWNrLXNvbGlkIC5mb3JtLWNoZWNrLWlucHV0W3R5cGU9Y2hlY2tib3hdOmZvY3VzIHtcclxuLy8gICBiYWNrZ3JvdW5kLWNvbG9yOiAjM2M5ZmFkICFpbXBvcnRhbnQ7XHJcbiAgICBcclxuLy8gfVxyXG5cclxuXHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "jwtDecode", "ChangePasswordComponent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "message_r2", "ɵɵtemplate", "LoginComponent_ng_template_36_ng_container_0_Template", "ɵɵproperty", "control_r3", "<PERSON><PERSON><PERSON><PERSON>", "validation_r4", "dirty", "touched", "LoginComponent", "fb", "authService", "route", "appService", "httpUtilService", "customLayoutUtilsService", "router", "modalService", "pageInfo", "titleService", "defaultAuth", "email", "password", "loginForm", "returnUrl", "isLoading$", "passwordshown", "userData", "unsubscribe", "constructor", "currentUserValue", "navigate", "ngOnInit", "updateTitle", "setTitle", "initForm", "snapshot", "queryParams", "toString", "f", "controls", "group", "compose", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "remember", "rememberMeLocalStorage", "getLocalStorageItem", "userLocallyStoredData", "patchValue", "userName", "submit", "controlsOfForm", "invalid", "authData", "value", "loadingSubject", "next", "login", "subscribe", "data", "<PERSON><PERSON><PERSON>", "responseData", "console", "log", "setLocalStorageItem", "token", "decodedToken", "expirationTime", "exp", "setToken", "isPasswordChanged", "changePassword", "replaceUrl", "then", "location", "reload", "showSuccess", "message", "error", "modalOption", "backdrop", "keyboard", "size", "modalRef", "open", "componentInstance", "showClose", "passEntry", "receivedEntry", "ngOnDestroy", "for<PERSON>ach", "sb", "showpassword", "event", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "ActivatedRoute", "i4", "AppService", "i5", "HttpUtilsService", "i6", "CustomLayoutUtilsService", "Router", "i7", "NgbModal", "i8", "PageInfoService", "i9", "Title", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelement", "ɵɵelementContainer", "LoginComponent_Template_span_click_21_listener", "LoginComponent_ng_template_36_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction2", "_c0", "valid", "formError_r5", "ɵɵpureFunction1", "_c1", "_c2", "_c3", "ɵɵpipeBind1"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\auth\\components\\login\\login.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\auth\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';\r\nimport { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';\r\nimport { Subscription, Observable } from 'rxjs';\r\nimport { first } from 'rxjs/operators';\r\nimport { UserModel } from '../../models/user.model';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AppService } from 'src/app/modules/services/app.service';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport { HttpUtilsService } from 'src/app/modules/services/http-utils.service';\r\nimport { ChangePasswordComponent } from '../change-password/change-password.component';\r\nimport { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';\r\nimport { PageInfoService } from 'src/app/_metronic/layout/core/page-info.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { CustomLayoutUtilsService } from 'src/app/modules/services/custom-layout.utils.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n})\r\nexport class LoginComponent implements OnInit, OnDestroy {\r\n  // KeenThemes mock, change it to:\r\n  defaultAuth: any = {\r\n    email: '<EMAIL>',\r\n    password: 'SwiJZf',\r\n  };\r\n  loginForm: FormGroup;\r\n  hasError: boolean;\r\n  returnUrl: string;\r\n  isLoading$: Observable<boolean>;\r\n  passwordshown = true;// password show/hide\r\n  userData:any ={};\r\n  // private fields\r\n  private unsubscribe: Subscription[] = []; // Read more: => https://brianflove.com/2016/12/11/anguar-2-unsubscribe-observables/\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private route: ActivatedRoute,\r\n    private appService:AppService,\r\n    private httpUtilService:HttpUtilsService,\r\n        private customLayoutUtilsService: CustomLayoutUtilsService,\r\n\r\n    private router: Router,\r\n    private modalService: NgbModal,\r\n    private pageInfo: PageInfoService,\r\n    private titleService: Title,\r\n  ) {\r\n    this.isLoading$ = this.authService.isLoading$;\r\n    // redirect to home if already logged in\r\n    if (this.authService.currentUserValue) {\r\n      this.router.navigate(['/']);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Ensure title is set in auth layout where global scripts may be inactive\r\n    this.pageInfo.updateTitle('Login');\r\n    this.titleService.setTitle('Login - Permit Tracker');\r\n    this.initForm();\r\n    // get return url from route parameters or default to '/'\r\n    this.returnUrl =\r\n      this.route.snapshot.queryParams['returnUrl'.toString()] || '/';\r\n  }\r\n\r\n  // convenience getter for easy access to form fields\r\n  get f() {\r\n    return this.loginForm.controls;\r\n  }\r\n\r\n  initForm() {\r\n    this.loginForm = this.fb.group({\r\n      email: [\r\n        '',\r\n        Validators.compose([\r\n          Validators.required,\r\n          Validators.email,\r\n          Validators.minLength(3),\r\n          Validators.maxLength(320), // https://stackoverflow.com/questions/386294/what-is-the-maximum-length-of-a-valid-email-address\r\n        ]),\r\n      ],\r\n      password: [\r\n        '',\r\n        Validators.compose([\r\n          Validators.required,\r\n          Validators.minLength(3),\r\n          Validators.maxLength(100),\r\n        ]),\r\n      ],\r\n      remember: [false]\r\n    });\r\n    let rememberMeLocalStorage = this.appService.getLocalStorageItem('permitRemember', false);\r\n    let userLocallyStoredData = this.appService.getLocalStorageItem('permitUserAuth', true);\r\n    //condition for patching  remember me based on boolean\r\n    if (rememberMeLocalStorage === 'true' || rememberMeLocalStorage === true) {\r\n      this.loginForm.patchValue({\r\n        email: userLocallyStoredData.userName,\r\n        password: userLocallyStoredData.password,\r\n        remember: true\r\n      })\r\n    }\r\n  }\r\n\r\n  submit() {\r\n    this.hasError = false;\r\n    const controlsOfForm = this.loginForm.controls;\r\n    if (this.loginForm.invalid) {\r\n      return;\r\n    }\r\n     const authData = {\r\n      userName: controlsOfForm.email.value,\r\n      password: controlsOfForm.password.value,\r\n    };\r\n    \r\n    // Show global loader\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    \r\n    // //API call for login\r\n    this.authService.login(authData)\r\n      .subscribe((data:any) => {\r\n        if (data.isFault === false) {\r\n          this.userData = data.responseData;\r\n          console.log(\"data.responseData\",data.responseData)\r\n          if (this.loginForm.value.remember === true) {\r\n            this.appService.setLocalStorageItem('permitRemember', 'true', false);\r\n          } else {\r\n            this.appService.setLocalStorageItem('permitRemember', 'false', false);\r\n          }\r\n          this.appService.setLocalStorageItem('permitUserAuth', authData, true);\r\n          // set the local storage for the user details\r\n          this.appService.setLocalStorageItem(\"permitUser\", data.responseData, true);\r\n          this.appService.setLocalStorageItem(\"permitToken\", data.responseData.token, true);\r\n          // this.layoutUtilService.showSuccess(data.responseData.message, '');\r\n          const token = data.responseData.token;\r\n          const decodedToken:any = jwtDecode(token);\r\n          const expirationTime = decodedToken.exp;\r\n          console.log('decodedToken ',decodedToken)\r\n              console.log('expirationTime ',expirationTime)\r\n          this.authService.setToken(data.responseData.token,expirationTime);\r\n          if (data.responseData.isPasswordChanged === false) {\r\n            this.changePassword();\r\n          }else{\r\n            // Keep loader active during navigation and page reload\r\n            this.router.navigate(['/dashboard'], { replaceUrl: true }).then(() => {\r\n              location.reload();\r\n            });\r\n          }\r\n        } else {\r\n          // Hide loader on error\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          this.customLayoutUtilsService.showSuccess(data.responseData.message, '');\r\n          // //alert(data.responseData.message);\r\n        }\r\n      },\r\n      (error) => {\r\n        // Hide loader on error\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Login error:', error);\r\n      });\r\n  }\r\n\r\n   //function for opening pop up for change password\r\n  changePassword() {\r\n    // define the NgbModels options.\r\n    const modalOption: NgbModalOptions = {};\r\n    modalOption.backdrop = 'static';\r\n    modalOption.keyboard = false;\r\n    modalOption.size = 'md';\r\n    // open the NgbModel for Confirmation.\r\n    const modalRef = this.modalService.open(ChangePasswordComponent, modalOption);\r\n    modalRef.componentInstance.showClose = false;\r\n    //get response from edit user modal\r\n    modalRef.componentInstance.passEntry.subscribe((receivedEntry: any) => {\r\n      if (receivedEntry == true) {\r\n        // this.router.navigate(['/dashboard'], {relativeTo: this.route});\r\n\r\n        this.router.navigate(['/dashboard'], { replaceUrl: true }).then(() => {\r\n          location.reload();\r\n        });\r\n      }\r\n    })\r\n\r\n  }\r\n  ngOnDestroy() {\r\n    this.unsubscribe.forEach((sb) => sb.unsubscribe());\r\n  }\r\n\r\n  //function for show hide password\r\n   showpassword(event: any) {\r\n    this.passwordshown = event;\r\n  }\r\n}\r\n", "<!--begin::Form-->\n<form class=\"form w-100\" [formGroup]=\"loginForm\" novalidate=\"novalidate\" id=\"kt_login_signin_form\"\n  (ngSubmit)=\"submit()\">\n  <!--begin::Heading-->\n  <div class=\"text-center mb-10\">\n    <img src=\"./assets/media/logos/cropped-Pacifica-Logo.png\" alt=\"Pacifica Engineering Services\" class=\"h-120px logo\" style=\"width:250px\">\n    <!-- <img alt=\"Logo\" src=\"./assets/media/logos/Practice-Logo.png\" class=\"h-50px logo\" style=\"width:140px\"> -->\n  </div>\n  <!--end::Heading-->\n\n  <!-- begin::Alert info-->\n  <!-- <ng-container *ngIf=\"!hasError\">\n    <div class=\"mb-10 bg-light-info p-8 rounded\">\n      <div class=\"text-info\">\n        Use account <strong>{{ defaultAuth.email }}</strong> and password\n        <strong>{{ defaultAuth.password }}</strong> to continue.\n      </div>\n    </div>\n  </ng-container> -->\n  <!-- end::Alert info-->\n\n  <!-- begin::Alert error-->\n  <!-- <ng-container *ngIf=\"hasError\">\n    <div class=\"mb-lg-15 alert alert-danger\">\n      <div class=\"alert-text font-weight-bold\">\n        The login details are incorrect\n      </div>\n    </div>\n  </ng-container> -->\n  <!-- end::Alert error-->\n\n  <!--begin::Form group-->\n  <div class=\"fv-row mb-10\">\n    <label class=\"form-label  fw-bold \">Email<sup class=\"text-danger\">*</sup></label>\n    <input class=\"form-control form-control-sm form-control-solid\" type=\"email\" name=\"email\" formControlName=\"email\"\n      autocomplete=\"off\" [ngClass]=\"{\n        'is-invalid': loginForm.controls['email'].invalid,\n        'is-valid': loginForm.controls['email'].valid\n      }\" />\n    <ng-container [ngTemplateOutlet]=\"formError\" [ngTemplateOutletContext]=\"{\n        validation: 'required',\n        message: 'Email is required',\n        control: loginForm.controls['email']\n      }\"></ng-container>\n    <ng-container [ngTemplateOutlet]=\"formError\" [ngTemplateOutletContext]=\"{\n        validation: 'email',\n        message: 'Email is invalid',\n        control: loginForm.controls['email']\n      }\"></ng-container>\n    <!-- <ng-container [ngTemplateOutlet]=\"formError\" [ngTemplateOutletContext]=\"{\n        validation: 'minLength',\n        message: 'Email should have at least 3 symbols',\n        control: loginForm.controls['email']\n      }\"></ng-container>\n    <ng-container [ngTemplateOutlet]=\"formError\" [ngTemplateOutletContext]=\"{\n        validation: 'maxLength',\n        message: 'Email should have maximum 360 symbols',\n        control: loginForm.controls['email']\n      }\"></ng-container> -->\n  </div>\n  <!--end::Form group-->\n\n  <!--begin::Form group-->\n  <div class=\"fv-row mb-4\">\n    <div class=\"d-flex justify-content-between mt-n5\">\n      <div class=\"d-flex flex-stack mb-2\">\n        <label class=\"form-label fw-bold  mb-0\">Password<sup class=\"text-danger\">*</sup></label>\n      </div>\n    </div>\n    <div class=\" mb-0\">\n      <input class=\"form-control form-control-sm form-control-solid\" [type]=\"passwordshown === true ?'password':'text'\" name=\"password\" autocomplete=\"off\"\n      formControlName=\"password\" [ngClass]=\"{\n        'is-invalid': loginForm.controls['password'].invalid,\n        'is-valid': loginForm.controls['password'].valid\n      }\" />\n      <div class=\"toggle-password\" >\n        <span [ngClass]=\"passwordshown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'\" style=\"margin-top:-29px\"\n          (click)=\"passwordshown===true? showpassword(false):showpassword(true)\"></span>\n      </div>\n    </div>\n\n    <ng-container [ngTemplateOutlet]=\"formError\" [ngTemplateOutletContext]=\"{\n        validation: 'required',\n        message: 'Password is required',\n        control: loginForm.controls['password']\n      }\"></ng-container>\n    <!-- <ng-container [ngTemplateOutlet]=\"formError\" [ngTemplateOutletContext]=\"{\n        validation: 'minlength',\n        message: 'Password should have at least 3 symbols',\n        control: loginForm.controls['password']\n      }\"></ng-container>\n    <ng-container [ngTemplateOutlet]=\"formError\" [ngTemplateOutletContext]=\"{\n        validation: 'maxLength',\n        message: 'Password should have maximum 100 symbols',\n        control: loginForm.controls['password']\n      }\"></ng-container> -->\n  </div>\n  <div class=\"d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8\">\n    <div>\n      <label class=\"form-check form-check-sm form-check-custom me-5\">\n        <input class=\"form-check-input\" type=\"checkbox\" formControlName=\"remember\" />\n        <span class=\"form-check-label \">Remember me ?</span>\n      </label>\n    </div>\n\n    <!--begin::Link-->\n    <a routerLink=\"/auth/forgot-password\" class=\"link-primary\">\n      Forgot Password ?\n    </a>\n    <!--end::Link  -->\n  </div>\n\n\n\n\n  <!--end::Form group-->\n\n  <!--begin::Action-->\n  <div class=\"text-center\">\n    <button type=\"submit\" id=\"kt_sign_in_submit\" class=\"btn btn-sm btn-primary  mb-5\" [disabled]=\"loginForm.invalid || (isLoading$ | async)\">\n      <span class=\"indicator-label\">Login</span>\n    </button>\n\n    <!-- begin::Separator  -->\n    <!-- <div class=\"text-center text-muted text-uppercase fw-bolder mb-5\">or</div> -->\n    <!-- end::Separator  -->\n\n    <!-- <a\n      class=\"\n        btn btn-flex\n        flex-center\n        btn-light btn-lg\n        w-100\n        mb-5\n        cursor-pointer\n      \"\n    >\n      <img\n        class=\"h-20px me-3\"\n        src=\"./assets/media/svg/brand-logos/google-icon.svg\"\n      />\n\n      Continue with Google\n    </a> -->\n  </div>\n  <!--end::Action-->\n</form>\n<!--end::Form-->\n\n<ng-template #formError let-control=\"control\" let-message=\"message\" let-validation=\"validation\">\n  <ng-container *ngIf=\"control.hasError(validation) && (control.dirty || control.touched)\">\n    <div class=\"fv-plugins-message-container\">\n      <span role=\"alert\" class=\"text-danger\">\n        {{ message }}\n      </span>\n    </div>\n  </ng-container>\n</ng-template>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAOnE,SAASC,SAAS,QAAQ,YAAY;AAEtC,SAASC,uBAAuB,QAAQ,8CAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC4IpFC,EAAA,CAAAC,uBAAA,GAAyF;IAErFD,EADF,CAAAE,cAAA,cAA0C,eACD;IACrCF,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;;;;;IAFFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,UAAA,MACF;;;;;IAJJP,EAAA,CAAAQ,UAAA,IAAAC,qDAAA,2BAAyF;;;;;IAA1ET,EAAA,CAAAU,UAAA,SAAAC,UAAA,CAAAC,QAAA,CAAAC,aAAA,MAAAF,UAAA,CAAAG,KAAA,IAAAH,UAAA,CAAAI,OAAA,EAAwE;;;ADjIzF,OAAM,MAAOC,cAAc;EAgBfC,EAAA;EACAC,WAAA;EACAC,KAAA;EACAC,UAAA;EACAC,eAAA;EACIC,wBAAA;EAEJC,MAAA;EACAC,YAAA;EACAC,QAAA;EACAC,YAAA;EAzBV;EACAC,WAAW,GAAQ;IACjBC,KAAK,EAAE,sBAAsB;IAC7BC,QAAQ,EAAE;GACX;EACDC,SAAS;EACTlB,QAAQ;EACRmB,SAAS;EACTC,UAAU;EACVC,aAAa,GAAG,IAAI,CAAC;EACrBC,QAAQ,GAAM,EAAE;EAChB;EACQC,WAAW,GAAmB,EAAE,CAAC,CAAC;EAE1CC,YACUnB,EAAe,EACfC,WAAwB,EACxBC,KAAqB,EACrBC,UAAqB,EACrBC,eAAgC,EAC5BC,wBAAkD,EAEtDC,MAAc,EACdC,YAAsB,EACtBC,QAAyB,EACzBC,YAAmB;IAVnB,KAAAT,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACX,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAE5B,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IAEpB,IAAI,CAACM,UAAU,GAAG,IAAI,CAACd,WAAW,CAACc,UAAU;IAC7C;IACA,IAAI,IAAI,CAACd,WAAW,CAACmB,gBAAgB,EAAE;MACrC,IAAI,CAACd,MAAM,CAACe,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7B;EACF;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACd,QAAQ,CAACe,WAAW,CAAC,OAAO,CAAC;IAClC,IAAI,CAACd,YAAY,CAACe,QAAQ,CAAC,wBAAwB,CAAC;IACpD,IAAI,CAACC,QAAQ,EAAE;IACf;IACA,IAAI,CAACX,SAAS,GACZ,IAAI,CAACZ,KAAK,CAACwB,QAAQ,CAACC,WAAW,CAAC,WAAW,CAACC,QAAQ,EAAE,CAAC,IAAI,GAAG;EAClE;EAEA;EACA,IAAIC,CAACA,CAAA;IACH,OAAO,IAAI,CAAChB,SAAS,CAACiB,QAAQ;EAChC;EAEAL,QAAQA,CAAA;IACN,IAAI,CAACZ,SAAS,GAAG,IAAI,CAACb,EAAE,CAAC+B,KAAK,CAAC;MAC7BpB,KAAK,EAAE,CACL,EAAE,EACF/B,UAAU,CAACoD,OAAO,CAAC,CACjBpD,UAAU,CAACqD,QAAQ,EACnBrD,UAAU,CAAC+B,KAAK,EAChB/B,UAAU,CAACsD,SAAS,CAAC,CAAC,CAAC,EACvBtD,UAAU,CAACuD,SAAS,CAAC,GAAG,CAAC,CAAE;MAAA,CAC5B,CAAC,CACH;MACDvB,QAAQ,EAAE,CACR,EAAE,EACFhC,UAAU,CAACoD,OAAO,CAAC,CACjBpD,UAAU,CAACqD,QAAQ,EACnBrD,UAAU,CAACsD,SAAS,CAAC,CAAC,CAAC,EACvBtD,UAAU,CAACuD,SAAS,CAAC,GAAG,CAAC,CAC1B,CAAC,CACH;MACDC,QAAQ,EAAE,CAAC,KAAK;KACjB,CAAC;IACF,IAAIC,sBAAsB,GAAG,IAAI,CAAClC,UAAU,CAACmC,mBAAmB,CAAC,gBAAgB,EAAE,KAAK,CAAC;IACzF,IAAIC,qBAAqB,GAAG,IAAI,CAACpC,UAAU,CAACmC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC;IACvF;IACA,IAAID,sBAAsB,KAAK,MAAM,IAAIA,sBAAsB,KAAK,IAAI,EAAE;MACxE,IAAI,CAACxB,SAAS,CAAC2B,UAAU,CAAC;QACxB7B,KAAK,EAAE4B,qBAAqB,CAACE,QAAQ;QACrC7B,QAAQ,EAAE2B,qBAAqB,CAAC3B,QAAQ;QACxCwB,QAAQ,EAAE;OACX,CAAC;IACJ;EACF;EAEAM,MAAMA,CAAA;IACJ,IAAI,CAAC/C,QAAQ,GAAG,KAAK;IACrB,MAAMgD,cAAc,GAAG,IAAI,CAAC9B,SAAS,CAACiB,QAAQ;IAC9C,IAAI,IAAI,CAACjB,SAAS,CAAC+B,OAAO,EAAE;MAC1B;IACF;IACC,MAAMC,QAAQ,GAAG;MAChBJ,QAAQ,EAAEE,cAAc,CAAChC,KAAK,CAACmC,KAAK;MACpClC,QAAQ,EAAE+B,cAAc,CAAC/B,QAAQ,CAACkC;KACnC;IAED;IACA,IAAI,CAAC1C,eAAe,CAAC2C,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAAC/C,WAAW,CAACgD,KAAK,CAACJ,QAAQ,CAAC,CAC7BK,SAAS,CAAEC,IAAQ,IAAI;MACtB,IAAIA,IAAI,CAACC,OAAO,KAAK,KAAK,EAAE;QAC1B,IAAI,CAACnC,QAAQ,GAAGkC,IAAI,CAACE,YAAY;QACjCC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAACJ,IAAI,CAACE,YAAY,CAAC;QAClD,IAAI,IAAI,CAACxC,SAAS,CAACiC,KAAK,CAACV,QAAQ,KAAK,IAAI,EAAE;UAC1C,IAAI,CAACjC,UAAU,CAACqD,mBAAmB,CAAC,gBAAgB,EAAE,MAAM,EAAE,KAAK,CAAC;QACtE,CAAC,MAAM;UACL,IAAI,CAACrD,UAAU,CAACqD,mBAAmB,CAAC,gBAAgB,EAAE,OAAO,EAAE,KAAK,CAAC;QACvE;QACA,IAAI,CAACrD,UAAU,CAACqD,mBAAmB,CAAC,gBAAgB,EAAEX,QAAQ,EAAE,IAAI,CAAC;QACrE;QACA,IAAI,CAAC1C,UAAU,CAACqD,mBAAmB,CAAC,YAAY,EAAEL,IAAI,CAACE,YAAY,EAAE,IAAI,CAAC;QAC1E,IAAI,CAAClD,UAAU,CAACqD,mBAAmB,CAAC,aAAa,EAAEL,IAAI,CAACE,YAAY,CAACI,KAAK,EAAE,IAAI,CAAC;QACjF;QACA,MAAMA,KAAK,GAAGN,IAAI,CAACE,YAAY,CAACI,KAAK;QACrC,MAAMC,YAAY,GAAO7E,SAAS,CAAC4E,KAAK,CAAC;QACzC,MAAME,cAAc,GAAGD,YAAY,CAACE,GAAG;QACvCN,OAAO,CAACC,GAAG,CAAC,eAAe,EAACG,YAAY,CAAC;QACrCJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAACI,cAAc,CAAC;QACjD,IAAI,CAAC1D,WAAW,CAAC4D,QAAQ,CAACV,IAAI,CAACE,YAAY,CAACI,KAAK,EAACE,cAAc,CAAC;QACjE,IAAIR,IAAI,CAACE,YAAY,CAACS,iBAAiB,KAAK,KAAK,EAAE;UACjD,IAAI,CAACC,cAAc,EAAE;QACvB,CAAC,MAAI;UACH;UACA,IAAI,CAACzD,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;YAAE2C,UAAU,EAAE;UAAI,CAAE,CAAC,CAACC,IAAI,CAAC,MAAK;YACnEC,QAAQ,CAACC,MAAM,EAAE;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA,IAAI,CAAC/D,eAAe,CAAC2C,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC3C,wBAAwB,CAAC+D,WAAW,CAACjB,IAAI,CAACE,YAAY,CAACgB,OAAO,EAAE,EAAE,CAAC;QACxE;MACF;IACF,CAAC,EACAC,KAAK,IAAI;MACR;MACA,IAAI,CAAClE,eAAe,CAAC2C,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/CM,OAAO,CAACgB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC,CAAC;EACN;EAEC;EACDP,cAAcA,CAAA;IACZ;IACA,MAAMQ,WAAW,GAAoB,EAAE;IACvCA,WAAW,CAACC,QAAQ,GAAG,QAAQ;IAC/BD,WAAW,CAACE,QAAQ,GAAG,KAAK;IAC5BF,WAAW,CAACG,IAAI,GAAG,IAAI;IACvB;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACpE,YAAY,CAACqE,IAAI,CAAC9F,uBAAuB,EAAEyF,WAAW,CAAC;IAC7EI,QAAQ,CAACE,iBAAiB,CAACC,SAAS,GAAG,KAAK;IAC5C;IACAH,QAAQ,CAACE,iBAAiB,CAACE,SAAS,CAAC7B,SAAS,CAAE8B,aAAkB,IAAI;MACpE,IAAIA,aAAa,IAAI,IAAI,EAAE;QACzB;QAEA,IAAI,CAAC1E,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;UAAE2C,UAAU,EAAE;QAAI,CAAE,CAAC,CAACC,IAAI,CAAC,MAAK;UACnEC,QAAQ,CAACC,MAAM,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EAEJ;EACAc,WAAWA,CAAA;IACT,IAAI,CAAC/D,WAAW,CAACgE,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACjE,WAAW,EAAE,CAAC;EACpD;EAEA;EACCkE,YAAYA,CAACC,KAAU;IACtB,IAAI,CAACrE,aAAa,GAAGqE,KAAK;EAC5B;;qCA1KWtF,cAAc,EAAAhB,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzG,EAAA,CAAAuG,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3G,EAAA,CAAAuG,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA7G,EAAA,CAAAuG,iBAAA,CAAAO,EAAA,CAAAC,UAAA,GAAA/G,EAAA,CAAAuG,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAjH,EAAA,CAAAuG,iBAAA,CAAAW,EAAA,CAAAC,wBAAA,GAAAnH,EAAA,CAAAuG,iBAAA,CAAAK,EAAA,CAAAQ,MAAA,GAAApH,EAAA,CAAAuG,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAtH,EAAA,CAAAuG,iBAAA,CAAAgB,EAAA,CAAAC,eAAA,GAAAxH,EAAA,CAAAuG,iBAAA,CAAAkB,EAAA,CAAAC,KAAA;EAAA;;UAAd1G,cAAc;IAAA2G,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCpB3BjI,EAAA,CAAAE,cAAA,cACwB;QAAtBF,EAAA,CAAAmI,UAAA,sBAAAC,iDAAA;UAAApI,EAAA,CAAAqI,aAAA,CAAAC,GAAA;UAAA,OAAAtI,EAAA,CAAAuI,WAAA,CAAYL,GAAA,CAAAvE,MAAA,EAAQ;QAAA,EAAC;QAErB3D,EAAA,CAAAE,cAAA,aAA+B;QAC7BF,EAAA,CAAAwI,SAAA,aAAuI;QAEzIxI,EAAA,CAAAI,YAAA,EAAM;QA0BJJ,EADF,CAAAE,cAAA,aAA0B,eACY;QAAAF,EAAA,CAAAG,MAAA,YAAK;QAAAH,EAAA,CAAAE,cAAA,aAAyB;QAAAF,EAAA,CAAAG,MAAA,QAAC;QAAMH,EAAN,CAAAI,YAAA,EAAM,EAAQ;QACjFJ,EAAA,CAAAwI,SAAA,eAIO;QAMPxI,EALA,CAAAyI,kBAAA,MAIoB,OAKA;QAWtBzI,EAAA,CAAAI,YAAA,EAAM;QAOAJ,EAHN,CAAAE,cAAA,cAAyB,eAC2B,eACZ,iBACM;QAAAF,EAAA,CAAAG,MAAA,gBAAQ;QAAAH,EAAA,CAAAE,cAAA,cAAyB;QAAAF,EAAA,CAAAG,MAAA,SAAC;QAE9EH,EAF8E,CAAAI,YAAA,EAAM,EAAQ,EACpF,EACF;QACNJ,EAAA,CAAAE,cAAA,eAAmB;QACjBF,EAAA,CAAAwI,SAAA,iBAIK;QAEHxI,EADF,CAAAE,cAAA,eAA8B,gBAE6C;QAAvEF,EAAA,CAAAmI,UAAA,mBAAAO,+CAAA;UAAA1I,EAAA,CAAAqI,aAAA,CAAAC,GAAA;UAAA,OAAAtI,EAAA,CAAAuI,WAAA,CAAAL,GAAA,CAAAjG,aAAA,KAAyB,IAAI,GAAEiG,GAAA,CAAA7B,YAAA,CAAa,KAAK,CAAC,GAAC6B,GAAA,CAAA7B,YAAA,CAAa,IAAI,CAAC;QAAA,EAAC;QAE5ErG,EAF6E,CAAAI,YAAA,EAAO,EAC5E,EACF;QAENJ,EAAA,CAAAyI,kBAAA,OAIoB;QAWtBzI,EAAA,CAAAI,YAAA,EAAM;QAGFJ,EAFJ,CAAAE,cAAA,eAAwE,WACjE,iBAC4D;QAC7DF,EAAA,CAAAwI,SAAA,iBAA6E;QAC7ExI,EAAA,CAAAE,cAAA,gBAAgC;QAAAF,EAAA,CAAAG,MAAA,qBAAa;QAEjDH,EAFiD,CAAAI,YAAA,EAAO,EAC9C,EACJ;QAGNJ,EAAA,CAAAE,cAAA,aAA2D;QACzDF,EAAA,CAAAG,MAAA,2BACF;QAEFH,EAFE,CAAAI,YAAA,EAAI,EAEA;QASJJ,EADF,CAAAE,cAAA,eAAyB,kBACkH;;QACvIF,EAAA,CAAAE,cAAA,gBAA8B;QAAAF,EAAA,CAAAG,MAAA,aAAK;QA0BzCH,EA1ByC,CAAAI,YAAA,EAAO,EACnC,EAuBL,EAED;QAGPJ,EAAA,CAAAQ,UAAA,KAAAmI,sCAAA,gCAAA3I,EAAA,CAAA4I,sBAAA,CAAgG;;;;QApJvE5I,EAAA,CAAAU,UAAA,cAAAwH,GAAA,CAAApG,SAAA,CAAuB;QAkCvB9B,EAAA,CAAAK,SAAA,GAGjB;QAHiBL,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA6I,eAAA,KAAAC,GAAA,EAAAZ,GAAA,CAAApG,SAAA,CAAAiB,QAAA,UAAAc,OAAA,EAAAqE,GAAA,CAAApG,SAAA,CAAAiB,QAAA,UAAAgG,KAAA,EAGjB;QACU/I,EAAA,CAAAK,SAAA,EAA8B;QAACL,EAA/B,CAAAU,UAAA,qBAAAsI,YAAA,CAA8B,4BAAAhJ,EAAA,CAAAiJ,eAAA,KAAAC,GAAA,EAAAhB,GAAA,CAAApG,SAAA,CAAAiB,QAAA,WAIxC;QACU/C,EAAA,CAAAK,SAAA,EAA8B;QAACL,EAA/B,CAAAU,UAAA,qBAAAsI,YAAA,CAA8B,4BAAAhJ,EAAA,CAAAiJ,eAAA,KAAAE,GAAA,EAAAjB,GAAA,CAAApG,SAAA,CAAAiB,QAAA,WAIxC;QAsB6D/C,EAAA,CAAAK,SAAA,GAAkD;QACtFL,EADoC,CAAAU,UAAA,SAAAwH,GAAA,CAAAjG,aAAA,gCAAkD,YAAAjC,EAAA,CAAA6I,eAAA,KAAAC,GAAA,EAAAZ,GAAA,CAAApG,SAAA,CAAAiB,QAAA,aAAAc,OAAA,EAAAqE,GAAA,CAAApG,SAAA,CAAAiB,QAAA,aAAAgG,KAAA,EAI/G;QAEM/I,EAAA,CAAAK,SAAA,GAAyE;QAAzEL,EAAA,CAAAU,UAAA,YAAAwH,GAAA,CAAAjG,aAAA,sDAAyE;QAKrEjC,EAAA,CAAAK,SAAA,EAA8B;QAACL,EAA/B,CAAAU,UAAA,qBAAAsI,YAAA,CAA8B,4BAAAhJ,EAAA,CAAAiJ,eAAA,KAAAG,GAAA,EAAAlB,GAAA,CAAApG,SAAA,CAAAiB,QAAA,cAIxC;QAkC8E/C,EAAA,CAAAK,SAAA,IAAsD;QAAtDL,EAAA,CAAAU,UAAA,aAAAwH,GAAA,CAAApG,SAAA,CAAA+B,OAAA,IAAA7D,EAAA,CAAAqJ,WAAA,SAAAnB,GAAA,CAAAlG,UAAA,EAAsD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}