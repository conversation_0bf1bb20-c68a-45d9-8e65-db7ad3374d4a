{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/permits.service\";\nimport * as i4 from \"../../services/projects.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/app.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Permit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Permit - \", ctx_r0.permitNumber, \"\");\n  }\n}\nfunction PermitPopupComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"span\", 22);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 22);\n    i0.ɵɵtext(5, \"This may take longer than usual....\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Project is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_8_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit / Sub Project Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_16_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Number is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_24_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.permitNumberError, \" \");\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \" Checking permit number availability... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Internal Review Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_33_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_40_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_48_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Review Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_48_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Municipality is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_54_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"div\", 9)(3, \"label\", 24);\n    i0.ɵɵtext(4, \"Project \");\n    i0.ɵɵelementStart(5, \"span\", 25);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"ng-select\", 26);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_21_Template_ng_select_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onProjectChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, PermitPopupComponent_ng_container_21_div_8_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 23)(10, \"div\", 9)(11, \"label\", 24);\n    i0.ɵɵtext(12, \"Permit / Sub Project Name \");\n    i0.ɵɵelementStart(13, \"span\", 25);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(15, \"input\", 28);\n    i0.ɵɵtemplate(16, PermitPopupComponent_ng_container_21_div_16_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 23)(18, \"div\", 29)(19, \"label\", 24);\n    i0.ɵɵtext(20, \"Permit Number \");\n    i0.ɵɵelementStart(21, \"span\", 25);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(23, \"input\", 30);\n    i0.ɵɵtemplate(24, PermitPopupComponent_ng_container_21_div_24_Template, 2, 1, \"div\", 27)(25, PermitPopupComponent_ng_container_21_div_25_Template, 2, 1, \"div\", 27)(26, PermitPopupComponent_ng_container_21_div_26_Template, 3, 0, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 29)(28, \"label\", 24);\n    i0.ɵɵtext(29, \"Internal Review Status \");\n    i0.ɵɵelementStart(30, \"span\", 25);\n    i0.ɵɵtext(31, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(32, \"ng-select\", 32);\n    i0.ɵɵtemplate(33, PermitPopupComponent_ng_container_21_div_33_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 29)(35, \"label\", 24);\n    i0.ɵɵtext(36, \"Permit Category \");\n    i0.ɵɵelementStart(37, \"span\", 25);\n    i0.ɵɵtext(38, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(39, \"ng-select\", 33);\n    i0.ɵɵtemplate(40, PermitPopupComponent_ng_container_21_div_40_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 23)(42, \"div\", 34)(43, \"label\", 24);\n    i0.ɵɵtext(44, \"Permit Review Type\");\n    i0.ɵɵelementStart(45, \"span\", 25);\n    i0.ɵɵtext(46, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"ng-select\", 35);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_21_Template_ng_select_change_47_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitReviewTypeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(48, PermitPopupComponent_ng_container_21_div_48_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 34)(50, \"label\", 24);\n    i0.ɵɵtext(51, \"Permit Municipality (External Review)\");\n    i0.ɵɵtemplate(52, PermitPopupComponent_ng_container_21_span_52_Template, 2, 0, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"ng-select\", 37);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_21_Template_ng_select_change_53_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitMunicipalityChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, PermitPopupComponent_ng_container_21_div_54_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_13_0;\n    let tmp_17_0;\n    let tmp_19_0;\n    let tmp_24_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.projects)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r0.permitNumberError || ((tmp_6_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_6_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_7_0.touched) && ((tmp_7_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_7_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.permitNumberError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isCheckingPermitNumber);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.internalStatusArray)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_13_0.touched) && ((tmp_13_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_13_0.invalid));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.categories)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_17_0.touched) && ((tmp_17_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_17_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.reviewTypeArray);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_19_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_19_0.touched) && ((tmp_19_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_19_0.invalid));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPermitMunicipalRequired);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"items\", ctx_r0.muncipalities)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_24_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_24_0.touched) && ((tmp_24_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_24_0.invalid));\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_ng_container_22_div_1_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.syncPermitDetails());\n    });\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2, \" Sync \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.getSyncButtonDisableStatus());\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_1_button_1_Template, 3, 1, \"button\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.value) !== \"Internal\" && ((tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.value) !== \"\" && ((tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.value) !== null);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Review Responsible Party is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_9_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_20_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_28_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Applied Date is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_39_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_60_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Location is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_60_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_1_Template, 2, 1, \"div\", 41);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"div\", 29)(4, \"label\", 24);\n    i0.ɵɵtext(5, \"Review Responsible Party\");\n    i0.ɵɵelementStart(6, \"span\", 25);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"input\", 42);\n    i0.ɵɵtemplate(9, PermitPopupComponent_ng_container_22_div_9_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"label\", 24);\n    i0.ɵɵtext(12, \"Primary Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"label\", 24);\n    i0.ɵɵtext(16, \"Permit Status \");\n    i0.ɵɵelementStart(17, \"span\", 25);\n    i0.ɵɵtext(18, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(19, \"ng-select\", 44);\n    i0.ɵɵtemplate(20, PermitPopupComponent_ng_container_22_div_20_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 23)(22, \"div\", 29)(23, \"label\", 24);\n    i0.ɵɵtext(24, \"Permit Type \");\n    i0.ɵɵelementStart(25, \"span\", 25);\n    i0.ɵɵtext(26, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(27, \"ng-select\", 45);\n    i0.ɵɵtemplate(28, PermitPopupComponent_ng_container_22_div_28_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 29)(30, \"label\", 24);\n    i0.ɵɵtext(31, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 29)(34, \"label\", 24);\n    i0.ɵɵtext(35, \"Applied Date \");\n    i0.ɵɵelementStart(36, \"span\", 25);\n    i0.ɵɵtext(37, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(38, \"input\", 47);\n    i0.ɵɵtemplate(39, PermitPopupComponent_ng_container_22_div_39_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 23)(41, \"div\", 29)(42, \"label\", 24);\n    i0.ɵɵtext(43, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"input\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 29)(46, \"label\", 24);\n    i0.ɵɵtext(47, \"Completed Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"input\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 29)(50, \"label\", 24);\n    i0.ɵɵtext(51, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"input\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 23)(54, \"div\", 9)(55, \"label\", 24);\n    i0.ɵɵtext(56, \"Location\");\n    i0.ɵɵelementStart(57, \"span\", 25);\n    i0.ɵɵtext(58, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(59, \"input\", 51);\n    i0.ɵɵtemplate(60, PermitPopupComponent_ng_container_22_div_60_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 23)(62, \"div\", 9)(63, \"label\", 24);\n    i0.ɵɵtext(64, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(65, \"textarea\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_6_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id === 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.invalid));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", ctx_r0.statuses)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_6_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.permitTypes)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_10_0.touched) && ((tmp_10_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_10_0.invalid));\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_11_0.touched) && ((tmp_11_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_11_0.invalid));\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_12_0.touched) && ((tmp_12_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_12_0.invalid));\n  }\n}\nfunction PermitPopupComponent_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1, \" Save \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.permitForm.invalid);\n  }\n}\nfunction PermitPopupComponent_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToNextTab());\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PermitPopupComponent {\n  fb;\n  modal;\n  permitsService;\n  projectsService;\n  customLayoutUtilsService;\n  httpUtilService;\n  appService;\n  cdr;\n  id = 0; // 0 for new permit, existing ID for edit\n  permit = null; // Permit data for editing\n  passEntry = new EventEmitter();\n  permitForm;\n  selectedTab = 'basic';\n  isLoading = false;\n  permitNumber = '';\n  permitNumberError = '';\n  isCheckingPermitNumber = false;\n  // Data arrays\n  projects = [];\n  municipalities = [];\n  permitTypes = [];\n  permitReviewTypes = [];\n  statusList = [];\n  // Subject for permit number validation debouncing\n  permitNumberSubject = new Subject();\n  constructor(fb, modal, permitsService, projectsService, customLayoutUtilsService, httpUtilService, appService, cdr) {\n    this.fb = fb;\n    this.modal = modal;\n    this.permitsService = permitsService;\n    this.projectsService = projectsService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilService = httpUtilService;\n    this.appService = appService;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    this.initializeForm();\n    this.loadDropdownData();\n    this.setupPermitNumberValidation();\n    if (this.id !== 0 && this.permit) {\n      this.populateFormForEdit();\n      this.permitNumber = this.permit.permitNumber || '';\n    }\n  }\n  initializeForm() {\n    this.permitForm = this.fb.group({\n      projectId: ['', Validators.required],\n      permitName: ['', Validators.required],\n      permitNumber: ['', Validators.required],\n      municipalityId: [''],\n      permitType: [''],\n      permitReviewType: [''],\n      status: [''],\n      permitEntityID: [''],\n      cityReviewLink: [''],\n      description: [''],\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: ['']\n    });\n  }\n  setupPermitNumberValidation() {\n    // Set up debounced permit number validation\n    this.permitNumberSubject.pipe(debounceTime(500), distinctUntilChanged()).subscribe(permitNumber => {\n      if (permitNumber && permitNumber.trim()) {\n        this.checkPermitNumberAvailability(permitNumber.trim());\n      } else {\n        this.permitNumberError = '';\n        this.isCheckingPermitNumber = false;\n      }\n    });\n    // Listen to permit number changes\n    this.permitForm.get('permitNumber')?.valueChanges.subscribe(value => {\n      if (value !== this.permitNumber) {\n        // Don't validate if it's the same as original\n        this.permitNumberSubject.next(value);\n      }\n    });\n  }\n  checkPermitNumberAvailability(permitNumber) {\n    this.isCheckingPermitNumber = true;\n    this.permitNumberError = '';\n    this.permitsService.checkPermitNumberExists({\n      permitNumber\n    }).subscribe({\n      next: res => {\n        this.isCheckingPermitNumber = false;\n        if (res?.responseData?.exists) {\n          this.permitNumberError = 'This permit number already exists';\n        } else {\n          this.permitNumberError = '';\n        }\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        this.isCheckingPermitNumber = false;\n        this.permitNumberError = '';\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  loadDropdownData() {\n    this.loadProjects();\n    this.loadMunicipalities();\n    this.loadPermitTypes();\n    this.loadPermitReviewTypes();\n    this.loadStatusList();\n  }\n  loadProjects() {\n    this.projectsService.getAllProjects({\n      take: 1000,\n      skip: 0\n    }).subscribe({\n      next: res => {\n        if (!res?.isFault) {\n          this.projects = res.responseData?.data || [];\n        }\n      },\n      error: err => console.error('Error loading projects:', err)\n    });\n  }\n  loadMunicipalities() {\n    // Load municipalities - adjust service call as needed\n    this.municipalities = [{\n      municipalityId: 1,\n      municipalityName: 'Default Municipality'\n    }];\n  }\n  loadPermitTypes() {\n    this.permitTypes = [{\n      value: 'Building',\n      text: 'Building'\n    }, {\n      value: 'Electrical',\n      text: 'Electrical'\n    }, {\n      value: 'Mechanical',\n      text: 'Mechanical'\n    }, {\n      value: 'Plumbing',\n      text: 'Plumbing'\n    }, {\n      value: 'Fire',\n      text: 'Fire'\n    }, {\n      value: 'Other',\n      text: 'Other'\n    }];\n  }\n  loadPermitReviewTypes() {\n    this.permitReviewTypes = [{\n      value: 'Internal',\n      text: 'Internal'\n    }, {\n      value: 'External',\n      text: 'External'\n    }, {\n      value: 'Both',\n      text: 'Both'\n    }];\n  }\n  loadStatusList() {\n    this.statusList = [{\n      text: 'Pending',\n      value: 'Pending'\n    }, {\n      text: 'In Progress',\n      value: 'In Progress'\n    }, {\n      text: 'Completed',\n      value: 'Completed'\n    }, {\n      text: 'On Hold',\n      value: 'On Hold'\n    }];\n  }\n  populateFormForEdit() {\n    if (this.permit) {\n      this.permitForm.patchValue({\n        projectId: this.permit.projectId,\n        permitName: this.permit.permitName,\n        permitNumber: this.permit.permitNumber,\n        municipalityId: this.permit.municipalityId,\n        permitType: this.permit.permitType,\n        permitReviewType: this.permit.permitReviewType,\n        status: this.permit.status,\n        permitEntityID: this.permit.permitEntityID,\n        cityReviewLink: this.permit.cityReviewLink,\n        description: this.permit.description,\n        attentionReason: this.permit.attentionReason,\n        internalNotes: this.permit.internalNotes,\n        actionTaken: this.permit.actionTaken\n      });\n    }\n  }\n  showTab(tab, event) {\n    if (event) {\n      event.preventDefault();\n    }\n    this.selectedTab = tab;\n  }\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'details';\n    }\n  }\n  goToPreviousTab() {\n    if (this.selectedTab === 'details') {\n      this.selectedTab = 'basic';\n    }\n  }\n  onProjectChange(projectId) {\n    // Handle project change if needed\n    console.log('Project changed:', projectId);\n  }\n  save() {\n    if (this.permitForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    if (this.permitNumberError) {\n      this.customLayoutUtilsService.showError('Please fix the permit number error before saving', '');\n      return;\n    }\n    this.isLoading = true;\n    const formData = this.permitForm.value;\n    const saveObservable = this.id === 0 ? this.permitsService.createPermit(formData) : this.permitsService.updatePermit({\n      ...formData,\n      permitId: this.id\n    });\n    saveObservable.subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (!res?.isFault) {\n          const message = this.id === 0 ? 'Permit created successfully' : 'Permit updated successfully';\n          this.customLayoutUtilsService.showSuccess(message, '');\n          this.passEntry.emit(true);\n          this.modal.close('saved');\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage || 'Error saving permit', '');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('Error saving permit', '');\n        console.error('Save error:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  markFormGroupTouched() {\n    Object.keys(this.permitForm.controls).forEach(key => {\n      const control = this.permitForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  static ɵfac = function PermitPopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitPopupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.PermitsService), i0.ɵɵdirectiveInject(i4.ProjectsService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.AppService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitPopupComponent,\n    selectors: [[\"app-permit-popup\"]],\n    inputs: {\n      id: \"id\",\n      permit: \"permit\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 32,\n    vars: 15,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [\"class\", \"loading-overlay-inside\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"modal-footer\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"loading-overlay-inside\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"row\", \"mt-4\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"bindLabel\", \"projectName\", \"formControlName\", \"projectId\", \"bindValue\", \"projectId\", \"placeholder\", \"Select Project\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"class\", \"text-danger mt-1 small\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"permitName\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-4\"], [\"type\", \"text\", \"formControlName\", \"permitNumber\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"text-info mt-1 small\", 4, \"ngIf\"], [\"formControlName\", \"internalReviewStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"formControlName\", \"permitCategory\", \"placeholder\", \"Select Category\", 3, \"items\", \"clearable\", \"multiple\"], [1, \"col-xl-6\"], [\"formControlName\", \"permitReviewType\", 3, \"change\", \"items\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"bindLabel\", \"cityName\", \"formControlName\", \"permitMunicipality\", \"bindValue\", \"municipalityId\", \"placeholder\", \"Select Project\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [1, \"text-danger\", \"mt-1\", \"small\"], [1, \"text-info\", \"mt-1\", \"small\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [\"class\", \"w-100 d-flex justify-content-end\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"reviewResponsibleParty\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"primaryContact\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"permitStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"formControlName\", \"permitType\", \"placeholder\", \"Select Type\", 3, \"items\", \"clearable\", \"multiple\"], [\"type\", \"date\", \"formControlName\", \"permitIssueDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitAppliedDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitExpirationDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitCompleteDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitFinalDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"description\", 1, \"form-control\", \"form-control-sm\"], [1, \"w-100\", \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-sync-alt\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n    template: function PermitPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, PermitPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, PermitPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_i_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6);\n        i0.ɵɵtemplate(9, PermitPopupComponent_div_9_Template, 6, 0, \"div\", 7);\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"ul\", 11)(14, \"li\", 12)(15, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_15_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(16, \" Basic Info \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"li\", 12)(18, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_18_listener($event) {\n          return ctx.showTab(\"details\", $event);\n        });\n        i0.ɵɵtext(19, \" Permit Details \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(20, \"form\", 14);\n        i0.ɵɵtemplate(21, PermitPopupComponent_ng_container_21_Template, 55, 25, \"ng-container\", 3)(22, PermitPopupComponent_ng_container_22_Template, 66, 12, \"ng-container\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\");\n        i0.ɵɵtemplate(25, PermitPopupComponent_button_25_Template, 2, 0, \"button\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\")(27, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_button_click_27_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵtext(28, \" Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(29, \"\\u00A0 \");\n        i0.ɵɵtemplate(30, PermitPopupComponent_button_30_Template, 2, 1, \"button\", 18)(31, PermitPopupComponent_button_31_Template, 2, 0, \"button\", 19);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, ctx.selectedTab === \"details\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.permitForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i9.NgSelectComponent],\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  transition: all 0.3s ease;\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 9999;\\n  background: white;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.4);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #3699ff;\\n  padding: 2rem;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 6px;\\n}\\n\\n[_nghost-%COMP%]     .k-grid {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header {\\n  background: #f8f9fa;\\n  border-bottom: 2px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header {\\n  background: #f8f9fa;\\n  border-color: #dee2e6;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar {\\n  background: #f8f9fa;\\n  border-bottom: 1px solid #dee2e6;\\n  padding: 1rem;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button {\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary {\\n  background: #007bff;\\n  border-color: #007bff;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary:hover {\\n  background: #0056b3;\\n  border-color: #0056b3;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager {\\n  background: #f8f9fa;\\n  border-top: 1px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-info {\\n  color: #6c757d;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link {\\n  border-radius: 4px;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected {\\n  background: #007bff;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .custom-dropdown .k-button {\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-textbox {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-textbox:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n[_nghost-%COMP%]     .k-dropdownlist {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-dropdownlist:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 0.5em 0.75em;\\n  font-size: 0.75em;\\n  font-weight: 600;\\n  border-radius: 6px;\\n}\\n.badge.badge-light-success[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n.badge.badge-light-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n}\\n.badge.badge-light-danger[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n.badge.badge-light-info[_ngcontent-%COMP%] {\\n  background: #d1ecf1;\\n  color: #0c5460;\\n}\\n.badge.badge-light-secondary[_ngcontent-%COMP%] {\\n  background: #e2e3e5;\\n  color: #383d41;\\n}\\n.badge.badge-light-primary[_ngcontent-%COMP%] {\\n  background: #cce7ff;\\n  color: #004085;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  border-color: #007bff;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n  border-color: #0056b3;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  border-color: #6c757d;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #545b62;\\n  border-color: #545b62;\\n}\\n.btn.btn-success[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  border-color: #28a745;\\n}\\n.btn.btn-success[_ngcontent-%COMP%]:hover {\\n  background: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%] {\\n  background: #ffc107;\\n  border-color: #ffc107;\\n  color: #212529;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%]:hover {\\n  background: #e0a800;\\n  border-color: #d39e00;\\n}\\n.btn.btn-info[_ngcontent-%COMP%] {\\n  background: #17a2b8;\\n  border-color: #17a2b8;\\n}\\n.btn.btn-info[_ngcontent-%COMP%]:hover {\\n  background: #138496;\\n  border-color: #138496;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #6c757d;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background: #6c757d;\\n  color: white;\\n}\\n\\n.btn-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  padding: 0;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.btn-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.text-muted[_ngcontent-%COMP%]   .fas[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.text-muted[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  margin-top: 1rem;\\n}\\n.text-muted[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], \\n   .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n}\\n.grid-container[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_expandGrid 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_expandGrid {\\n  from {\\n    opacity: 0.8;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "Subject", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "permitNumber", "ɵɵtemplate", "PermitPopupComponent_ng_container_21_div_8_div_1_Template", "ɵɵproperty", "tmp_2_0", "permitForm", "get", "errors", "PermitPopupComponent_ng_container_21_div_16_div_1_Template", "PermitPopupComponent_ng_container_21_div_24_div_1_Template", "permitNumberError", "ɵɵelement", "PermitPopupComponent_ng_container_21_div_33_div_1_Template", "PermitPopupComponent_ng_container_21_div_40_div_1_Template", "PermitPopupComponent_ng_container_21_div_48_div_1_Template", "PermitPopupComponent_ng_container_21_div_54_div_1_Template", "ɵɵelementContainerStart", "ɵɵlistener", "PermitPopupComponent_ng_container_21_Template_ng_select_change_7_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onProjectChange", "PermitPopupComponent_ng_container_21_div_8_Template", "PermitPopupComponent_ng_container_21_div_16_Template", "PermitPopupComponent_ng_container_21_div_24_Template", "PermitPopupComponent_ng_container_21_div_25_Template", "PermitPopupComponent_ng_container_21_div_26_Template", "PermitPopupComponent_ng_container_21_div_33_Template", "PermitPopupComponent_ng_container_21_div_40_Template", "PermitPopupComponent_ng_container_21_Template_ng_select_change_47_listener", "onPermitReviewTypeChange", "PermitPopupComponent_ng_container_21_div_48_Template", "PermitPopupComponent_ng_container_21_span_52_Template", "PermitPopupComponent_ng_container_21_Template_ng_select_change_53_listener", "onPermitMunicipalityChange", "PermitPopupComponent_ng_container_21_div_54_Template", "projects", "tmp_4_0", "touched", "invalid", "tmp_5_0", "ɵɵclassProp", "tmp_6_0", "tmp_7_0", "isCheckingPermitNumber", "internalStatusArray", "tmp_13_0", "categories", "tmp_17_0", "reviewTypeArray", "tmp_19_0", "isPermitMunicipalRequired", "muncipalities", "tmp_24_0", "PermitPopupComponent_ng_container_22_div_1_button_1_Template_button_click_0_listener", "_r3", "syncPermitDetails", "getSyncButtonDisableStatus", "PermitPopupComponent_ng_container_22_div_1_button_1_Template", "value", "PermitPopupComponent_ng_container_22_div_9_div_1_Template", "PermitPopupComponent_ng_container_22_div_20_div_1_Template", "PermitPopupComponent_ng_container_22_div_28_div_1_Template", "PermitPopupComponent_ng_container_22_div_39_div_1_Template", "PermitPopupComponent_ng_container_22_div_60_div_1_Template", "PermitPopupComponent_ng_container_22_div_1_Template", "PermitPopupComponent_ng_container_22_div_9_Template", "PermitPopupComponent_ng_container_22_div_20_Template", "PermitPopupComponent_ng_container_22_div_28_Template", "PermitPopupComponent_ng_container_22_div_39_Template", "PermitPopupComponent_ng_container_22_div_60_Template", "id", "statuses", "permitTypes", "tmp_10_0", "tmp_11_0", "tmp_12_0", "PermitPopupComponent_button_25_Template_button_click_0_listener", "_r4", "goToPreviousTab", "PermitPopupComponent_button_30_Template_button_click_0_listener", "_r5", "save", "PermitPopupComponent_button_31_Template_button_click_0_listener", "_r6", "goToNextTab", "PermitPopupComponent", "fb", "modal", "permitsService", "projectsService", "customLayoutUtilsService", "httpUtilService", "appService", "cdr", "permit", "passEntry", "selectedTab", "isLoading", "municipalities", "permitReviewTypes", "statusList", "permitNumberSubject", "constructor", "ngOnInit", "initializeForm", "loadDropdownData", "setupPermitNumberValidation", "populateFormForEdit", "group", "projectId", "required", "permitName", "municipalityId", "permitType", "permitReviewType", "status", "permitEntityID", "cityReviewLink", "description", "attentionReason", "internalNotes", "actionTaken", "pipe", "subscribe", "trim", "checkPermitNumberAvailability", "valueChanges", "next", "checkPermitNumberExists", "res", "responseData", "exists", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "loadProjects", "loadMunicipalities", "loadPermitTypes", "loadPermitReviewTypes", "loadStatusList", "getAllProjects", "take", "skip", "<PERSON><PERSON><PERSON>", "data", "err", "console", "municipalityName", "text", "patchValue", "showTab", "tab", "event", "preventDefault", "log", "markFormGroupTouched", "showError", "formData", "saveObservable", "createPermit", "updatePermit", "permitId", "message", "showSuccess", "emit", "close", "faultMessage", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "NgbActiveModal", "i3", "PermitsService", "i4", "ProjectsService", "i5", "CustomLayoutUtilsService", "i6", "HttpUtilsService", "i7", "AppService", "ChangeDetectorRef", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "PermitPopupComponent_Template", "rf", "ctx", "PermitPopupComponent_div_4_Template", "PermitPopupComponent_div_5_Template", "PermitPopupComponent_Template_i_click_7_listener", "dismiss", "PermitPopupComponent_div_9_Template", "PermitPopupComponent_Template_a_click_15_listener", "PermitPopupComponent_Template_a_click_18_listener", "PermitPopupComponent_ng_container_21_Template", "PermitPopupComponent_ng_container_22_Template", "PermitPopupComponent_button_25_Template", "PermitPopupComponent_Template_button_click_27_listener", "PermitPopupComponent_button_30_Template", "PermitPopupComponent_button_31_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, ChangeDetectorRef } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-permit-popup',\r\n  templateUrl: './permit-popup.component.html',\r\n  styleUrls: ['./permit-popup.component.scss']\r\n})\r\nexport class PermitPopupComponent implements OnInit {\r\n  @Input() id: number = 0; // 0 for new permit, existing ID for edit\r\n  @Input() permit: any = null; // Permit data for editing\r\n  @Output() passEntry = new EventEmitter<boolean>();\r\n\r\n  permitForm!: FormGroup;\r\n  selectedTab: string = 'basic';\r\n  isLoading: boolean = false;\r\n  permitNumber: string = '';\r\n  permitNumberError: string = '';\r\n  isCheckingPermitNumber: boolean = false;\r\n\r\n  // Data arrays\r\n  projects: any[] = [];\r\n  municipalities: any[] = [];\r\n  permitTypes: any[] = [];\r\n  permitReviewTypes: any[] = [];\r\n  statusList: any[] = [];\r\n\r\n  // Subject for permit number validation debouncing\r\n  private permitNumberSubject = new Subject<string>();\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    public modal: NgbActiveModal,\r\n    private permitsService: PermitsService,\r\n    private projectsService: ProjectsService,\r\n    private customLayoutUtilsService: CustomLayoutUtilsService,\r\n    private httpUtilService: HttpUtilsService,\r\n    private appService: AppService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.initializeForm();\r\n    this.loadDropdownData();\r\n    this.setupPermitNumberValidation();\r\n\r\n    if (this.id !== 0 && this.permit) {\r\n      this.populateFormForEdit();\r\n      this.permitNumber = this.permit.permitNumber || '';\r\n    }\r\n  }\r\n\r\n  private initializeForm(): void {\r\n    this.permitForm = this.fb.group({\r\n      projectId: ['', Validators.required],\r\n      permitName: ['', Validators.required],\r\n      permitNumber: ['', Validators.required],\r\n      municipalityId: [''],\r\n      permitType: [''],\r\n      permitReviewType: [''],\r\n      status: [''],\r\n      permitEntityID: [''],\r\n      cityReviewLink: [''],\r\n      description: [''],\r\n      attentionReason: [''],\r\n      internalNotes: [''],\r\n      actionTaken: ['']\r\n    });\r\n  }\r\n\r\n  private setupPermitNumberValidation(): void {\r\n    // Set up debounced permit number validation\r\n    this.permitNumberSubject.pipe(\r\n      debounceTime(500),\r\n      distinctUntilChanged()\r\n    ).subscribe(permitNumber => {\r\n      if (permitNumber && permitNumber.trim()) {\r\n        this.checkPermitNumberAvailability(permitNumber.trim());\r\n      } else {\r\n        this.permitNumberError = '';\r\n        this.isCheckingPermitNumber = false;\r\n      }\r\n    });\r\n\r\n    // Listen to permit number changes\r\n    this.permitForm.get('permitNumber')?.valueChanges.subscribe(value => {\r\n      if (value !== this.permitNumber) { // Don't validate if it's the same as original\r\n        this.permitNumberSubject.next(value);\r\n      }\r\n    });\r\n  }\r\n\r\n  private checkPermitNumberAvailability(permitNumber: string): void {\r\n    this.isCheckingPermitNumber = true;\r\n    this.permitNumberError = '';\r\n\r\n    this.permitsService.checkPermitNumberExists({ permitNumber }).subscribe({\r\n      next: (res: any) => {\r\n        this.isCheckingPermitNumber = false;\r\n        if (res?.responseData?.exists) {\r\n          this.permitNumberError = 'This permit number already exists';\r\n        } else {\r\n          this.permitNumberError = '';\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: () => {\r\n        this.isCheckingPermitNumber = false;\r\n        this.permitNumberError = '';\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  private loadDropdownData(): void {\r\n    this.loadProjects();\r\n    this.loadMunicipalities();\r\n    this.loadPermitTypes();\r\n    this.loadPermitReviewTypes();\r\n    this.loadStatusList();\r\n  }\r\n\r\n  private loadProjects(): void {\r\n    this.projectsService.getAllProjects({ take: 1000, skip: 0 }).subscribe({\r\n      next: (res: any) => {\r\n        if (!res?.isFault) {\r\n          this.projects = res.responseData?.data || [];\r\n        }\r\n      },\r\n      error: (err) => console.error('Error loading projects:', err)\r\n    });\r\n  }\r\n\r\n  private loadMunicipalities(): void {\r\n    // Load municipalities - adjust service call as needed\r\n    this.municipalities = [\r\n      { municipalityId: 1, municipalityName: 'Default Municipality' }\r\n    ];\r\n  }\r\n\r\n  private loadPermitTypes(): void {\r\n    this.permitTypes = [\r\n      { value: 'Building', text: 'Building' },\r\n      { value: 'Electrical', text: 'Electrical' },\r\n      { value: 'Mechanical', text: 'Mechanical' },\r\n      { value: 'Plumbing', text: 'Plumbing' },\r\n      { value: 'Fire', text: 'Fire' },\r\n      { value: 'Other', text: 'Other' }\r\n    ];\r\n  }\r\n\r\n  private loadPermitReviewTypes(): void {\r\n    this.permitReviewTypes = [\r\n      { value: 'Internal', text: 'Internal' },\r\n      { value: 'External', text: 'External' },\r\n      { value: 'Both', text: 'Both' }\r\n    ];\r\n  }\r\n\r\n  private loadStatusList(): void {\r\n    this.statusList = [\r\n      { text: 'Pending', value: 'Pending' },\r\n      { text: 'In Progress', value: 'In Progress' },\r\n      { text: 'Completed', value: 'Completed' },\r\n      { text: 'On Hold', value: 'On Hold' }\r\n    ];\r\n  }\r\n\r\n  private populateFormForEdit(): void {\r\n    if (this.permit) {\r\n      this.permitForm.patchValue({\r\n        projectId: this.permit.projectId,\r\n        permitName: this.permit.permitName,\r\n        permitNumber: this.permit.permitNumber,\r\n        municipalityId: this.permit.municipalityId,\r\n        permitType: this.permit.permitType,\r\n        permitReviewType: this.permit.permitReviewType,\r\n        status: this.permit.status,\r\n        permitEntityID: this.permit.permitEntityID,\r\n        cityReviewLink: this.permit.cityReviewLink,\r\n        description: this.permit.description,\r\n        attentionReason: this.permit.attentionReason,\r\n        internalNotes: this.permit.internalNotes,\r\n        actionTaken: this.permit.actionTaken\r\n      });\r\n    }\r\n  }\r\n\r\n  showTab(tab: string, event?: any): void {\r\n    if (event) {\r\n      event.preventDefault();\r\n    }\r\n    this.selectedTab = tab;\r\n  }\r\n\r\n  goToNextTab(): void {\r\n    if (this.selectedTab === 'basic') {\r\n      this.selectedTab = 'details';\r\n    }\r\n  }\r\n\r\n  goToPreviousTab(): void {\r\n    if (this.selectedTab === 'details') {\r\n      this.selectedTab = 'basic';\r\n    }\r\n  }\r\n\r\n  onProjectChange(projectId: any): void {\r\n    // Handle project change if needed\r\n    console.log('Project changed:', projectId);\r\n  }\r\n\r\n  save(): void {\r\n    if (this.permitForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      return;\r\n    }\r\n\r\n    if (this.permitNumberError) {\r\n      this.customLayoutUtilsService.showError('Please fix the permit number error before saving', '');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    const formData = this.permitForm.value;\r\n\r\n    const saveObservable = this.id === 0\r\n      ? this.permitsService.createPermit(formData)\r\n      : this.permitsService.updatePermit({ ...formData, permitId: this.id });\r\n\r\n    saveObservable.subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        if (!res?.isFault) {\r\n          const message = this.id === 0 ? 'Permit created successfully' : 'Permit updated successfully';\r\n          this.customLayoutUtilsService.showSuccess(message, '');\r\n          this.passEntry.emit(true);\r\n          this.modal.close('saved');\r\n        } else {\r\n          this.customLayoutUtilsService.showError(res.faultMessage || 'Error saving permit', '');\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err) => {\r\n        this.isLoading = false;\r\n        this.customLayoutUtilsService.showError('Error saving permit', '');\r\n        console.error('Save error:', err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.permitForm.controls).forEach(key => {\r\n      const control = this.permitForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n}", "<div class=\"modal-content h-auto\">\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container>\n        <div *ngIf=\"id === 0\">Add Permit</div>\n        <div *ngIf=\"id !== 0\">Edit Permit - {{ permitNumber }}</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"modal.dismiss()\"></i>\n    </div>\n  </div>\n\n  <div class=\"modal-body large-modal-body\">\n    <!-- Loading Overlay -->\n    <div *ngIf=\"isLoading\" class=\"loading-overlay-inside\">\n      <div class=\"spinner-border text-primary spinner-md\" role=\"status\">\n        <span class=\"visually-hidden\">Loading...</span>\n        <span class=\"visually-hidden\">This may take longer than usual....</span>\n      </div>\n    </div>\n\n    <div class=\"row\">\n      <div class=\"col-xl-12\">\n        <div class=\"d-flex\">\n          <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\">\n            <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'basic' }\" (click)=\"showTab('basic', $event)\">\n                Basic Info\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'details' }\" (click)=\"showTab('details', $event)\">\n                Permit Details\n              </a>\n            </li>\n            <!-- <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'notes' }\" (click)=\"showTab('notes', $event)\">\n                Notes/Actions\n              </a>\n            </li> -->\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <form class=\"form form-label-right\" [formGroup]=\"permitForm\">\n      <ng-container *ngIf=\"selectedTab == 'basic'\">\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Project <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"projects\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"projectName\"\n              formControlName=\"projectId\" bindValue=\"projectId\" placeholder=\"Select Project\"\n              (change)=\"onProjectChange($event)\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('projectId')?.touched && permitForm.get('projectId')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('projectId')?.errors?.['required']\">\n                Project is required.\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Permit / Sub Project Name <span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitName\" />\n            <div *ngIf=\"permitForm.get('permitName')?.touched && permitForm.get('permitName')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitName')?.errors?.['required']\">\n                Permit / Sub Project Name is required.\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Permit Number <span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitNumber\" \n                   [class.is-invalid]=\"permitNumberError || (permitForm.get('permitNumber')?.touched && permitForm.get('permitNumber')?.invalid)\" />\n            <!-- *ngIf=\"id === 0\"  -->\n\n            <!-- <p *ngIf=\"id !== 0\">{{this.permitNumber}}</p> -->\n            <div *ngIf=\"permitForm.get('permitNumber')?.touched && permitForm.get('permitNumber')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitNumber')?.errors?.['required']\">\n                Permit Number is required.\n              </div>\n            </div>\n            <div *ngIf=\"permitNumberError\" class=\"text-danger mt-1 small\">\n              {{ permitNumberError }}\n            </div>\n            <div *ngIf=\"isCheckingPermitNumber\" class=\"text-info mt-1 small\">\n              <i class=\"fas fa-spinner fa-spin\"></i> Checking permit number availability...\n            </div>\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Internal Review Status <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"internalStatusArray\" [clearable]=\"false\" [multiple]=\"false\"\n              formControlName=\"internalReviewStatus\" placeholder=\"Select Status\">\n            </ng-select>\n            <div\n              *ngIf=\"permitForm.get('internalReviewStatus')?.touched && permitForm.get('internalReviewStatus')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('internalReviewStatus')?.errors?.['required']\">\n                Internal Review Status is required.\n              </div>\n            </div>\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Permit Category <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"categories\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitCategory\"\n              placeholder=\"Select Category\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitCategory')?.touched && permitForm.get('permitCategory')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitCategory')?.errors?.['required']\">\n                Permit Category is required.\n              </div>\n            </div>\n          </div>\n\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-6\">\n            <label class=\"fw-bold form-label mb-2\">Permit Review Type<span\n                class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"reviewTypeArray\" formControlName=\"permitReviewType\"\n              (change)=\"onPermitReviewTypeChange($event)\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitReviewType')?.touched && permitForm.get('permitReviewType')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitReviewType')?.errors?.['required']\">\n                Permit Review Type is required.\n              </div>\n            </div>\n          </div>\n          <div class=\"col-xl-6\">\n            <label class=\"fw-bold form-label mb-2\">Permit Municipality (External Review)<span\n                *ngIf=\"isPermitMunicipalRequired\" class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"muncipalities\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"cityName\"\n              formControlName=\"permitMunicipality\" bindValue=\"municipalityId\" placeholder=\"Select Project\"\n              (change)=\"onPermitMunicipalityChange($event)\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitMunicipality')?.touched && permitForm.get('permitMunicipality')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitMunicipality')?.errors?.['required']\">\n                Permit Municipality is required.\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- <i class=\"fas fa-sync-alt action-icon edit-icon\" [disabled]=\"getSyncButtonDisableStatus()\"  (click)=\"syncPermitDetails()\"\n                    title=\"Sync Review\"></i> -->\n\n\n\n\n\n\n\n      </ng-container>\n      <ng-container *ngIf=\"selectedTab == 'details'\">\n        <div class=\"w-100 d-flex justify-content-end\" *ngIf=\"id === 0\">\n          <button type=\"button\" class=\"btn btn-primary btn-sm\" *ngIf=\"permitForm.get('permitReviewType')?.value !== 'Internal' && permitForm.get('permitReviewType')?.value !== '' && permitForm.get('permitReviewType')?.value !== null\" [disabled]=\"getSyncButtonDisableStatus()\"\n            (click)=\"syncPermitDetails()\">\n            <i class=\"fas fa-sync-alt\"></i> Sync\n          </button>\n        </div>\n\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Review Responsible Party<span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"reviewResponsibleParty\" />\n            <div\n              *ngIf=\"permitForm.get('reviewResponsibleParty')?.touched && permitForm.get('reviewResponsibleParty')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('reviewResponsibleParty')?.errors?.['required']\">\n                Review Responsible Party is required.\n              </div>\n            </div>\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Primary Contact</label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"primaryContact\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Permit Status <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"statuses\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitStatus\"\n              placeholder=\"Select Status\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitStatus')?.touched && permitForm.get('permitStatus')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitStatus')?.errors?.['required']\">\n                Permit Status is required.\n              </div>\n            </div>\n          </div>\n\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Permit Type <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"permitTypes\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitType\"\n              placeholder=\"Select Type\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitType')?.touched && permitForm.get('permitType')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitType')?.errors?.['required']\">\n                Permit Type is required.\n              </div>\n            </div>\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Issue Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitIssueDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Applied Date <span class=\"text-danger\">*</span></label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitAppliedDate\" />\n            <div *ngIf=\"permitForm.get('permitAppliedDate')?.touched && permitForm.get('permitAppliedDate')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitAppliedDate')?.errors?.['required']\">\n                Applied Date is required.\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n\n\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Expiration Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitExpirationDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Completed Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitCompleteDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Final Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitFinalDate\" />\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Location<span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"location\" />\n            <div *ngIf=\"permitForm.get('location')?.touched && permitForm.get('location')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('location')?.errors?.['required']\">\n                Location is required.\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Description</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"description\"></textarea>\n          </div>\n        </div>\n      </ng-container>\n      <!-- <ng-container *ngIf=\"selectedTab == 'role'\">\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Permit City Review Link<span *ngIf=\"isPermitMunicipalRequired\"\n                class=\"text-danger\">*</span></label>\n            <input type=\"url\" class=\"form-control form-control-sm\" formControlName=\"cityReviewLink\" />\n            <div *ngIf=\"permitForm.get('cityReviewLink')?.touched && permitForm.get('cityReviewLink')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('cityReviewLink')?.errors?.['required']\">\n                Permit City Review Link is required\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n\n        </div>\n\n\n      </ng-container> -->\n      <!-- <ng-container *ngIf=\"selectedTab == 'notes'\">\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Attention Reason</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"attentionReason\"></textarea>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Internal Notes</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"internalNotes\"></textarea>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Action Taken</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"actionTaken\"></textarea>\n          </div>\n        </div>\n      </ng-container> -->\n    </form>\n  </div>\n\n  <div class=\"modal-footer justify-content-between\">\n    <div>\n      <button *ngIf=\"selectedTab == 'details'\" type=\"button\" class=\"btn btn-secondary btn-sm btn-elevate\"\n        (click)=\"goToPreviousTab()\">\n        Previous\n      </button>\n    </div>\n    <div>\n      <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate mr-2\" (click)=\"modal.dismiss()\">\n        Cancel</button>&nbsp;\n      <!-- *ngIf=\"selectedTab == 'notes'\"  -->\n      <button type=\"button\" class=\"btn btn-primary btn-sm\" *ngIf=\"selectedTab == 'details'\"\n        [disabled]=\"permitForm.invalid\" (click)=\"save()\">\n        Save\n      </button>\n      <button *ngIf=\"selectedTab == 'basic'\" type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"goToNextTab()\">\n        Next\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAmC,eAAe;AACjG,SAAiCC,UAAU,QAAQ,gBAAgB;AAOnE,SAASC,OAAO,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;;;;;;;;;;;;;;;;ICJ1DC,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACtCH,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,YAAA,KAAgC;;;;;IAYtDP,EAFJ,CAAAC,cAAA,cAAsD,cACc,eAClC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAErEF,EAFqE,CAAAG,YAAA,EAAO,EACpE,EACF;;;;;IAwCIH,EAAA,CAAAC,cAAA,UAA+D;IAC7DD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAC,yDAAA,iBAA+D;IAGjET,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAuD;IAAvDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAuD;;;;;IAa7Dd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAO,0DAAA,iBAAgE;IAGlEf,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAgB9Dd,EAAA,CAAAC,cAAA,UAAkE;IAChED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAQ,0DAAA,iBAAkE;IAGpEhB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;;;;;IAIlEd,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAW,iBAAA,MACF;;;;;IACAjB,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAkB,SAAA,YAAsC;IAAClB,EAAA,CAAAE,MAAA,+CACzC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUJH,EAAA,CAAAC,cAAA,UAA0E;IACxED,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAEiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAW,0DAAA,iBAA0E;IAG5EnB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAkE;IAAlEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAkE;;;;;IAYxEd,EAAA,CAAAC,cAAA,UAAoE;IAClED,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAY,0DAAA,iBAAoE;IAGtEpB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA4D;IAA5DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA4D;;;;;IAgBlEd,EAAA,CAAAC,cAAA,UAAsE;IACpED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAa,0DAAA,iBAAsE;IAGxErB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA8D;;;;;IAMMd,EAAA,CAAAC,cAAA,eAClB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAOhEH,EAAA,CAAAC,cAAA,UAAwE;IACtED,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAc,0DAAA,iBAAwE;IAG1EtB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAgE;IAAhEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAgE;;;;;;IApG9Ed,EAAA,CAAAuB,uBAAA,GAA6C;IAGvCvB,EAFJ,CAAAC,cAAA,cAAsB,aACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAC,cAAA,oBAEqC;IAAnCD,EAAA,CAAAwB,UAAA,oBAAAC,0EAAAC,MAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAAyB,eAAA,CAAAL,MAAA,CAAuB;IAAA,EAAC;IACpC1B,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,IAAAwB,mDAAA,kBACiC;IAMrChC,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3GH,EAAA,CAAAkB,SAAA,iBAAuF;IACvFlB,EAAA,CAAAQ,UAAA,KAAAyB,oDAAA,kBACiC;IAMrCjC,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC/FH,EAAA,CAAAkB,SAAA,iBACwI;IAaxIlB,EATA,CAAAQ,UAAA,KAAA0B,oDAAA,kBACiC,KAAAC,oDAAA,kBAK6B,KAAAC,oDAAA,kBAGG;IAGnEpC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,+BAAuB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACxGH,EAAA,CAAAkB,SAAA,qBAEY;IACZlB,EAAA,CAAAQ,UAAA,KAAA6B,oDAAA,kBAEiC;IAKnCrC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACjGH,EAAA,CAAAkB,SAAA,qBAEY;IACZlB,EAAA,CAAAQ,UAAA,KAAA8B,oDAAA,kBACiC;IAOrCtC,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAC,cAAA,gBACjC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACxCH,EAAA,CAAAC,cAAA,qBAC8C;IAA5CD,EAAA,CAAAwB,UAAA,oBAAAe,2EAAAb,MAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAAkC,wBAAA,CAAAd,MAAA,CAAgC;IAAA,EAAC;IAC7C1B,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,KAAAiC,oDAAA,kBACiC;IAKnCzC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAQ,UAAA,KAAAkC,qDAAA,mBAClB;IAAQ1C,EAAA,CAAAG,YAAA,EAAQ;IAC1EH,EAAA,CAAAC,cAAA,qBAEgD;IAA9CD,EAAA,CAAAwB,UAAA,oBAAAmB,2EAAAjB,MAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAAsC,0BAAA,CAAAlB,MAAA,CAAkC;IAAA,EAAC;IAC/C1B,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,KAAAqC,oDAAA,kBACiC;IAMrC7C,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;;;;;IArGSH,EAAA,CAAAI,SAAA,GAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAAwC,QAAA,CAAkB,oBAAoB,mBAAmB;IAI9D9C,EAAA,CAAAI,SAAA,EAAkF;IAAlFJ,EAAA,CAAAU,UAAA,WAAAqC,OAAA,GAAAzC,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAkC,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAzC,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAkC,OAAA,CAAAE,OAAA,EAAkF;IAalFjD,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAAwC,OAAA,GAAA5C,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAqC,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA5C,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAqC,OAAA,CAAAD,OAAA,EAAoF;IAYnFjD,EAAA,CAAAI,SAAA,GAA8H;IAA9HJ,EAAA,CAAAmD,WAAA,eAAA7C,MAAA,CAAAW,iBAAA,MAAAmC,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAH,OAAA,EAA8H;IAI/HjD,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAA2C,OAAA,GAAA/C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAwC,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAA/C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAwC,OAAA,CAAAJ,OAAA,EAAwF;IAMxFjD,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAW,iBAAA,CAAuB;IAGvBjB,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAgD,sBAAA,CAA4B;IAMvBtD,EAAA,CAAAI,SAAA,GAA6B;IAAqBJ,EAAlD,CAAAU,UAAA,UAAAJ,MAAA,CAAAiD,mBAAA,CAA6B,oBAAoB,mBAAmB;IAI5EvD,EAAA,CAAAI,SAAA,EAAwG;IAAxGJ,EAAA,CAAAU,UAAA,WAAA8C,QAAA,GAAAlD,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAA2C,QAAA,CAAAR,OAAA,OAAAQ,QAAA,GAAAlD,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAA2C,QAAA,CAAAP,OAAA,EAAwG;IAShGjD,EAAA,CAAAI,SAAA,GAAoB;IAAqBJ,EAAzC,CAAAU,UAAA,UAAAJ,MAAA,CAAAmD,UAAA,CAAoB,oBAAoB,mBAAmB;IAGhEzD,EAAA,CAAAI,SAAA,EAA4F;IAA5FJ,EAAA,CAAAU,UAAA,WAAAgD,QAAA,GAAApD,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAA6C,QAAA,CAAAV,OAAA,OAAAU,QAAA,GAAApD,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAA6C,QAAA,CAAAT,OAAA,EAA4F;IAavFjD,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAU,UAAA,UAAAJ,MAAA,CAAAqD,eAAA,CAAyB;IAG9B3D,EAAA,CAAAI,SAAA,EAAgG;IAAhGJ,EAAA,CAAAU,UAAA,WAAAkD,QAAA,GAAAtD,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA+C,QAAA,CAAAZ,OAAA,OAAAY,QAAA,GAAAtD,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA+C,QAAA,CAAAX,OAAA,EAAgG;IASjGjD,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAuD,yBAAA,CAA+B;IACzB7D,EAAA,CAAAI,SAAA,EAAuB;IAAqBJ,EAA5C,CAAAU,UAAA,UAAAJ,MAAA,CAAAwD,aAAA,CAAuB,oBAAoB,mBAAmB;IAInE9D,EAAA,CAAAI,SAAA,EAAoG;IAApGJ,EAAA,CAAAU,UAAA,WAAAqD,QAAA,GAAAzD,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAkD,QAAA,CAAAf,OAAA,OAAAe,QAAA,GAAAzD,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAkD,QAAA,CAAAd,OAAA,EAAoG;;;;;;IAoB5GjD,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAwB,UAAA,mBAAAwC,qFAAA;MAAAhE,EAAA,CAAA2B,aAAA,CAAAsC,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA4D,iBAAA,EAAmB;IAAA,EAAC;IAC7BlE,EAAA,CAAAkB,SAAA,YAA+B;IAAClB,EAAA,CAAAE,MAAA,aAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHuNH,EAAA,CAAAU,UAAA,aAAAJ,MAAA,CAAA6D,0BAAA,GAAyC;;;;;IAD3QnE,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAQ,UAAA,IAAA4D,4DAAA,qBACgC;IAGlCpE,EAAA,CAAAG,YAAA,EAAM;;;;;IAJkDH,EAAA,CAAAI,SAAA,EAAwK;IAAxKJ,EAAA,CAAAU,UAAA,WAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAA0D,KAAA,sBAAA1D,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAA0D,KAAA,cAAA1D,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAA0D,KAAA,WAAwK;;;;;IAc1NrE,EAAA,CAAAC,cAAA,UAA4E;IAC1ED,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAEiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA8D,yDAAA,iBAA4E;IAG9EtE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAoE;;;;;IAgB1Ed,EAAA,CAAAC,cAAA,UAAkE;IAChED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA+D,0DAAA,iBAAkE;IAGpEvE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;;;;;IAehEd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAgE,0DAAA,iBAAgE;IAGlExE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAc9Dd,EAAA,CAAAC,cAAA,UAAuE;IACrED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAiE,0DAAA,iBAAuE;IAGzEzE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA+D;IAA/DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA+D;;;;;IA4BrEd,EAAA,CAAAC,cAAA,UAA8D;IAC5DD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAkE,0DAAA,iBAA8D;IAGhE1E,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAsD;;;;;IAzFpEd,EAAA,CAAAuB,uBAAA,GAA+C;IAC7CvB,EAAA,CAAAQ,UAAA,IAAAmE,mDAAA,kBAA+D;IAU3D3E,EAFJ,CAAAC,cAAA,cAAsB,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzGH,EAAA,CAAAkB,SAAA,gBAAmG;IACnGlB,EAAA,CAAAQ,UAAA,IAAAoE,mDAAA,kBAEiC;IAKnC5E,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAkB,SAAA,iBAA2F;IAC7FlB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC/FH,EAAA,CAAAkB,SAAA,qBAEY;IACZlB,EAAA,CAAAQ,UAAA,KAAAqE,oDAAA,kBACiC;IAOrC7E,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC7FH,EAAA,CAAAkB,SAAA,qBAEY;IACZlB,EAAA,CAAAQ,UAAA,KAAAsE,oDAAA,kBACiC;IAKnC9E,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAkB,SAAA,iBAA4F;IAC9FlB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAAkB,SAAA,iBAA8F;IAC9FlB,EAAA,CAAAQ,UAAA,KAAAuE,oDAAA,kBACiC;IAMrC/E,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAJJ,CAAAC,cAAA,eAAsB,eAGE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAkB,SAAA,iBAAiG;IACnGlB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAkB,SAAA,iBAA+F;IACjGlB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAkB,SAAA,iBAA4F;IAEhGlB,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAkB,SAAA,iBAAqF;IACrFlB,EAAA,CAAAQ,UAAA,KAAAwE,oDAAA,kBACiC;IAMrChF,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAkB,SAAA,oBAAiG;IAErGlB,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;;IAnGyCH,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAA2E,EAAA,OAAc;IAatDjF,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAU,UAAA,WAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAqC,OAAA,OAAArC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAsC,OAAA,EAA4G;IAapGjD,EAAA,CAAAI,SAAA,IAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAA4E,QAAA,CAAkB,oBAAoB,mBAAmB;IAG9DlF,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAA0C,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAH,OAAA,EAAwF;IAYnFjD,EAAA,CAAAI,SAAA,GAAqB;IAAqBJ,EAA1C,CAAAU,UAAA,UAAAJ,MAAA,CAAA6E,WAAA,CAAqB,oBAAoB,mBAAmB;IAGjEnF,EAAA,CAAAI,SAAA,EAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAA0E,QAAA,GAAA9E,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAuE,QAAA,CAAApC,OAAA,OAAAoC,QAAA,GAAA9E,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAuE,QAAA,CAAAnC,OAAA,EAAoF;IAcpFjD,EAAA,CAAAI,SAAA,IAAkG;IAAlGJ,EAAA,CAAAU,UAAA,WAAA2E,QAAA,GAAA/E,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAwE,QAAA,CAAArC,OAAA,OAAAqC,QAAA,GAAA/E,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAwE,QAAA,CAAApC,OAAA,EAAkG;IA4BlGjD,EAAA,CAAAI,SAAA,IAAgF;IAAhFJ,EAAA,CAAAU,UAAA,WAAA4E,QAAA,GAAAhF,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAyE,QAAA,CAAAtC,OAAA,OAAAsC,QAAA,GAAAhF,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAyE,QAAA,CAAArC,OAAA,EAAgF;;;;;;IA+D5FjD,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAAwB,UAAA,mBAAA+D,gEAAA;MAAAvF,EAAA,CAAA2B,aAAA,CAAA6D,GAAA;MAAA,MAAAlF,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAmF,eAAA,EAAiB;IAAA,EAAC;IAC3BzF,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,iBACmD;IAAjBD,EAAA,CAAAwB,UAAA,mBAAAkE,gEAAA;MAAA1F,EAAA,CAAA2B,aAAA,CAAAgE,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAsF,IAAA,EAAM;IAAA,EAAC;IAChD5F,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAU,UAAA,aAAAJ,MAAA,CAAAM,UAAA,CAAAqC,OAAA,CAA+B;;;;;;IAGjCjD,EAAA,CAAAC,cAAA,iBAA4G;IAAxBD,EAAA,CAAAwB,UAAA,mBAAAqE,gEAAA;MAAA7F,EAAA,CAAA2B,aAAA,CAAAmE,GAAA;MAAA,MAAAxF,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAyF,WAAA,EAAa;IAAA,EAAC;IACzG/F,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;AD5Tf,OAAM,MAAO6F,oBAAoB;EAuBrBC,EAAA;EACDC,KAAA;EACCC,cAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,eAAA;EACAC,UAAA;EACAC,GAAA;EA7BDvB,EAAE,GAAW,CAAC,CAAC,CAAC;EAChBwB,MAAM,GAAQ,IAAI,CAAC,CAAC;EACnBC,SAAS,GAAG,IAAI/G,YAAY,EAAW;EAEjDiB,UAAU;EACV+F,WAAW,GAAW,OAAO;EAC7BC,SAAS,GAAY,KAAK;EAC1BrG,YAAY,GAAW,EAAE;EACzBU,iBAAiB,GAAW,EAAE;EAC9BqC,sBAAsB,GAAY,KAAK;EAEvC;EACAR,QAAQ,GAAU,EAAE;EACpB+D,cAAc,GAAU,EAAE;EAC1B1B,WAAW,GAAU,EAAE;EACvB2B,iBAAiB,GAAU,EAAE;EAC7BC,UAAU,GAAU,EAAE;EAEtB;EACQC,mBAAmB,GAAG,IAAInH,OAAO,EAAU;EAEnDoH,YACUhB,EAAe,EAChBC,KAAqB,EACpBC,cAA8B,EAC9BC,eAAgC,EAChCC,wBAAkD,EAClDC,eAAiC,EACjCC,UAAsB,EACtBC,GAAsB;IAPtB,KAAAP,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,GAAG,GAAHA,GAAG;EACV;EAEHU,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,2BAA2B,EAAE;IAElC,IAAI,IAAI,CAACpC,EAAE,KAAK,CAAC,IAAI,IAAI,CAACwB,MAAM,EAAE;MAChC,IAAI,CAACa,mBAAmB,EAAE;MAC1B,IAAI,CAAC/G,YAAY,GAAG,IAAI,CAACkG,MAAM,CAAClG,YAAY,IAAI,EAAE;IACpD;EACF;EAEQ4G,cAAcA,CAAA;IACpB,IAAI,CAACvG,UAAU,GAAG,IAAI,CAACqF,EAAE,CAACsB,KAAK,CAAC;MAC9BC,SAAS,EAAE,CAAC,EAAE,EAAE5H,UAAU,CAAC6H,QAAQ,CAAC;MACpCC,UAAU,EAAE,CAAC,EAAE,EAAE9H,UAAU,CAAC6H,QAAQ,CAAC;MACrClH,YAAY,EAAE,CAAC,EAAE,EAAEX,UAAU,CAAC6H,QAAQ,CAAC;MACvCE,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC,EAAE;KACjB,CAAC;EACJ;EAEQf,2BAA2BA,CAAA;IACjC;IACA,IAAI,CAACL,mBAAmB,CAACqB,IAAI,CAC3BvI,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CAACuI,SAAS,CAAC/H,YAAY,IAAG;MACzB,IAAIA,YAAY,IAAIA,YAAY,CAACgI,IAAI,EAAE,EAAE;QACvC,IAAI,CAACC,6BAA6B,CAACjI,YAAY,CAACgI,IAAI,EAAE,CAAC;MACzD,CAAC,MAAM;QACL,IAAI,CAACtH,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAACqC,sBAAsB,GAAG,KAAK;MACrC;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC1C,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAE4H,YAAY,CAACH,SAAS,CAACjE,KAAK,IAAG;MAClE,IAAIA,KAAK,KAAK,IAAI,CAAC9D,YAAY,EAAE;QAAE;QACjC,IAAI,CAACyG,mBAAmB,CAAC0B,IAAI,CAACrE,KAAK,CAAC;MACtC;IACF,CAAC,CAAC;EACJ;EAEQmE,6BAA6BA,CAACjI,YAAoB;IACxD,IAAI,CAAC+C,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACrC,iBAAiB,GAAG,EAAE;IAE3B,IAAI,CAACkF,cAAc,CAACwC,uBAAuB,CAAC;MAAEpI;IAAY,CAAE,CAAC,CAAC+H,SAAS,CAAC;MACtEI,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAACtF,sBAAsB,GAAG,KAAK;QACnC,IAAIsF,GAAG,EAAEC,YAAY,EAAEC,MAAM,EAAE;UAC7B,IAAI,CAAC7H,iBAAiB,GAAG,mCAAmC;QAC9D,CAAC,MAAM;UACL,IAAI,CAACA,iBAAiB,GAAG,EAAE;QAC7B;QACA,IAAI,CAACuF,GAAG,CAACuC,YAAY,EAAE;MACzB,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC1F,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACrC,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAACuF,GAAG,CAACuC,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEQ3B,gBAAgBA,CAAA;IACtB,IAAI,CAAC6B,YAAY,EAAE;IACnB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQJ,YAAYA,CAAA;IAClB,IAAI,CAAC7C,eAAe,CAACkD,cAAc,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAC,CAAE,CAAC,CAAClB,SAAS,CAAC;MACrEI,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAACA,GAAG,EAAEa,OAAO,EAAE;UACjB,IAAI,CAAC3G,QAAQ,GAAG8F,GAAG,CAACC,YAAY,EAAEa,IAAI,IAAI,EAAE;QAC9C;MACF,CAAC;MACDV,KAAK,EAAGW,GAAG,IAAKC,OAAO,CAACZ,KAAK,CAAC,yBAAyB,EAAEW,GAAG;KAC7D,CAAC;EACJ;EAEQT,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACrC,cAAc,GAAG,CACpB;MAAEc,cAAc,EAAE,CAAC;MAAEkC,gBAAgB,EAAE;IAAsB,CAAE,CAChE;EACH;EAEQV,eAAeA,CAAA;IACrB,IAAI,CAAChE,WAAW,GAAG,CACjB;MAAEd,KAAK,EAAE,UAAU;MAAEyF,IAAI,EAAE;IAAU,CAAE,EACvC;MAAEzF,KAAK,EAAE,YAAY;MAAEyF,IAAI,EAAE;IAAY,CAAE,EAC3C;MAAEzF,KAAK,EAAE,YAAY;MAAEyF,IAAI,EAAE;IAAY,CAAE,EAC3C;MAAEzF,KAAK,EAAE,UAAU;MAAEyF,IAAI,EAAE;IAAU,CAAE,EACvC;MAAEzF,KAAK,EAAE,MAAM;MAAEyF,IAAI,EAAE;IAAM,CAAE,EAC/B;MAAEzF,KAAK,EAAE,OAAO;MAAEyF,IAAI,EAAE;IAAO,CAAE,CAClC;EACH;EAEQV,qBAAqBA,CAAA;IAC3B,IAAI,CAACtC,iBAAiB,GAAG,CACvB;MAAEzC,KAAK,EAAE,UAAU;MAAEyF,IAAI,EAAE;IAAU,CAAE,EACvC;MAAEzF,KAAK,EAAE,UAAU;MAAEyF,IAAI,EAAE;IAAU,CAAE,EACvC;MAAEzF,KAAK,EAAE,MAAM;MAAEyF,IAAI,EAAE;IAAM,CAAE,CAChC;EACH;EAEQT,cAAcA,CAAA;IACpB,IAAI,CAACtC,UAAU,GAAG,CAChB;MAAE+C,IAAI,EAAE,SAAS;MAAEzF,KAAK,EAAE;IAAS,CAAE,EACrC;MAAEyF,IAAI,EAAE,aAAa;MAAEzF,KAAK,EAAE;IAAa,CAAE,EAC7C;MAAEyF,IAAI,EAAE,WAAW;MAAEzF,KAAK,EAAE;IAAW,CAAE,EACzC;MAAEyF,IAAI,EAAE,SAAS;MAAEzF,KAAK,EAAE;IAAS,CAAE,CACtC;EACH;EAEQiD,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACb,MAAM,EAAE;MACf,IAAI,CAAC7F,UAAU,CAACmJ,UAAU,CAAC;QACzBvC,SAAS,EAAE,IAAI,CAACf,MAAM,CAACe,SAAS;QAChCE,UAAU,EAAE,IAAI,CAACjB,MAAM,CAACiB,UAAU;QAClCnH,YAAY,EAAE,IAAI,CAACkG,MAAM,CAAClG,YAAY;QACtCoH,cAAc,EAAE,IAAI,CAAClB,MAAM,CAACkB,cAAc;QAC1CC,UAAU,EAAE,IAAI,CAACnB,MAAM,CAACmB,UAAU;QAClCC,gBAAgB,EAAE,IAAI,CAACpB,MAAM,CAACoB,gBAAgB;QAC9CC,MAAM,EAAE,IAAI,CAACrB,MAAM,CAACqB,MAAM;QAC1BC,cAAc,EAAE,IAAI,CAACtB,MAAM,CAACsB,cAAc;QAC1CC,cAAc,EAAE,IAAI,CAACvB,MAAM,CAACuB,cAAc;QAC1CC,WAAW,EAAE,IAAI,CAACxB,MAAM,CAACwB,WAAW;QACpCC,eAAe,EAAE,IAAI,CAACzB,MAAM,CAACyB,eAAe;QAC5CC,aAAa,EAAE,IAAI,CAAC1B,MAAM,CAAC0B,aAAa;QACxCC,WAAW,EAAE,IAAI,CAAC3B,MAAM,CAAC2B;OAC1B,CAAC;IACJ;EACF;EAEA4B,OAAOA,CAACC,GAAW,EAAEC,KAAW;IAC9B,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,cAAc,EAAE;IACxB;IACA,IAAI,CAACxD,WAAW,GAAGsD,GAAG;EACxB;EAEAlE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACY,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B;EACF;EAEAlB,eAAeA,CAAA;IACb,IAAI,IAAI,CAACkB,WAAW,KAAK,SAAS,EAAE;MAClC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;EACF;EAEA5E,eAAeA,CAACyF,SAAc;IAC5B;IACAoC,OAAO,CAACQ,GAAG,CAAC,kBAAkB,EAAE5C,SAAS,CAAC;EAC5C;EAEA5B,IAAIA,CAAA;IACF,IAAI,IAAI,CAAChF,UAAU,CAACqC,OAAO,EAAE;MAC3B,IAAI,CAACoH,oBAAoB,EAAE;MAC3B;IACF;IAEA,IAAI,IAAI,CAACpJ,iBAAiB,EAAE;MAC1B,IAAI,CAACoF,wBAAwB,CAACiE,SAAS,CAAC,kDAAkD,EAAE,EAAE,CAAC;MAC/F;IACF;IAEA,IAAI,CAAC1D,SAAS,GAAG,IAAI;IACrB,MAAM2D,QAAQ,GAAG,IAAI,CAAC3J,UAAU,CAACyD,KAAK;IAEtC,MAAMmG,cAAc,GAAG,IAAI,CAACvF,EAAE,KAAK,CAAC,GAChC,IAAI,CAACkB,cAAc,CAACsE,YAAY,CAACF,QAAQ,CAAC,GAC1C,IAAI,CAACpE,cAAc,CAACuE,YAAY,CAAC;MAAE,GAAGH,QAAQ;MAAEI,QAAQ,EAAE,IAAI,CAAC1F;IAAE,CAAE,CAAC;IAExEuF,cAAc,CAAClC,SAAS,CAAC;MACvBI,IAAI,EAAGE,GAAQ,IAAI;QACjB,IAAI,CAAChC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgC,GAAG,EAAEa,OAAO,EAAE;UACjB,MAAMmB,OAAO,GAAG,IAAI,CAAC3F,EAAE,KAAK,CAAC,GAAG,6BAA6B,GAAG,6BAA6B;UAC7F,IAAI,CAACoB,wBAAwB,CAACwE,WAAW,CAACD,OAAO,EAAE,EAAE,CAAC;UACtD,IAAI,CAAClE,SAAS,CAACoE,IAAI,CAAC,IAAI,CAAC;UACzB,IAAI,CAAC5E,KAAK,CAAC6E,KAAK,CAAC,OAAO,CAAC;QAC3B,CAAC,MAAM;UACL,IAAI,CAAC1E,wBAAwB,CAACiE,SAAS,CAAC1B,GAAG,CAACoC,YAAY,IAAI,qBAAqB,EAAE,EAAE,CAAC;QACxF;QACA,IAAI,CAACxE,GAAG,CAACuC,YAAY,EAAE;MACzB,CAAC;MACDC,KAAK,EAAGW,GAAG,IAAI;QACb,IAAI,CAAC/C,SAAS,GAAG,KAAK;QACtB,IAAI,CAACP,wBAAwB,CAACiE,SAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC;QAClEV,OAAO,CAACZ,KAAK,CAAC,aAAa,EAAEW,GAAG,CAAC;QACjC,IAAI,CAACnD,GAAG,CAACuC,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEQsB,oBAAoBA,CAAA;IAC1BY,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtK,UAAU,CAACuK,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAAC1K,UAAU,CAACC,GAAG,CAACwK,GAAG,CAAC;MACxCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;;qCAzPWvF,oBAAoB,EAAAhG,EAAA,CAAAwL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1L,EAAA,CAAAwL,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5L,EAAA,CAAAwL,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA9L,EAAA,CAAAwL,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAhM,EAAA,CAAAwL,iBAAA,CAAAS,EAAA,CAAAC,wBAAA,GAAAlM,EAAA,CAAAwL,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAApM,EAAA,CAAAwL,iBAAA,CAAAa,EAAA,CAAAC,UAAA,GAAAtM,EAAA,CAAAwL,iBAAA,CAAAxL,EAAA,CAAAuM,iBAAA;EAAA;;UAApBvG,oBAAoB;IAAAwG,SAAA;IAAAC,MAAA;MAAAxH,EAAA;MAAAwB,MAAA;IAAA;IAAAiG,OAAA;MAAAhG,SAAA;IAAA;IAAAiG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCb7BhN,EAFJ,CAAAC,cAAA,aAAkC,aACW,aACR;QAC/BD,EAAA,CAAAuB,uBAAA,GAAc;QAEZvB,EADA,CAAAQ,UAAA,IAAA0M,mCAAA,iBAAsB,IAAAC,mCAAA,iBACA;;QAE1BnN,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WACgD;QAA1BD,EAAA,CAAAwB,UAAA,mBAAA4L,iDAAA;UAAA,OAASH,GAAA,CAAA/G,KAAA,CAAAmH,OAAA,EAAe;QAAA,EAAC;QAE1ErN,EAF2E,CAAAG,YAAA,EAAI,EACvE,EACF;QAENH,EAAA,CAAAC,cAAA,aAAyC;QAEvCD,EAAA,CAAAQ,UAAA,IAAA8M,mCAAA,iBAAsD;QAY5CtN,EALV,CAAAC,cAAA,cAAiB,cACQ,eACD,cACqF,cAChF,aAEkE;QAAnCD,EAAA,CAAAwB,UAAA,mBAAA+L,kDAAA7L,MAAA;UAAA,OAASuL,GAAA,CAAAjD,OAAA,CAAQ,OAAO,EAAAtI,MAAA,CAAS;QAAA,EAAC;QAClF1B,EAAA,CAAAE,MAAA,oBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAEsE;QAArCD,EAAA,CAAAwB,UAAA,mBAAAgM,kDAAA9L,MAAA;UAAA,OAASuL,GAAA,CAAAjD,OAAA,CAAQ,SAAS,EAAAtI,MAAA,CAAS;QAAA,EAAC;QACtF1B,EAAA,CAAAE,MAAA,wBACF;QAWVF,EAXU,CAAAG,YAAA,EAAI,EACD,EAOF,EACD,EACF,EACF;QAENH,EAAA,CAAAC,cAAA,gBAA6D;QAqH3DD,EApHA,CAAAQ,UAAA,KAAAiN,6CAAA,4BAA6C,KAAAC,6CAAA,4BAoHE;QAkJnD1N,EADE,CAAAG,YAAA,EAAO,EACH;QAGJH,EADF,CAAAC,cAAA,eAAkD,WAC3C;QACHD,EAAA,CAAAQ,UAAA,KAAAmN,uCAAA,qBAC8B;QAGhC3N,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,WAAK,kBAC4F;QAA1BD,EAAA,CAAAwB,UAAA,mBAAAoM,uDAAA;UAAA,OAASX,GAAA,CAAA/G,KAAA,CAAAmH,OAAA,EAAe;QAAA,EAAC;QAC5FrN,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAAAH,EAAA,CAAAE,MAAA,eACjB;QAKAF,EAJA,CAAAQ,UAAA,KAAAqN,uCAAA,qBACmD,KAAAC,uCAAA,qBAGyD;QAKlH9N,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;QA1UQH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAAuM,GAAA,CAAAhI,EAAA,OAAc;QACdjF,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAAuM,GAAA,CAAAhI,EAAA,OAAc;QAUlBjF,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAU,UAAA,SAAAuM,GAAA,CAAArG,SAAA,CAAe;QAaT5G,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA+N,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAtG,WAAA,cAA+C;QAM/C3G,EAAA,CAAAI,SAAA,GAAiD;QAAjDJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA+N,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAtG,WAAA,gBAAiD;QAezB3G,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAU,UAAA,cAAAuM,GAAA,CAAArM,UAAA,CAAwB;QAC3CZ,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAAuM,GAAA,CAAAtG,WAAA,YAA4B;QAoH5B3G,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAuM,GAAA,CAAAtG,WAAA,cAA8B;QAsJpC3G,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAuM,GAAA,CAAAtG,WAAA,cAA8B;QASe3G,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAuM,GAAA,CAAAtG,WAAA,cAA8B;QAI3E3G,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAAuM,GAAA,CAAAtG,WAAA,YAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}