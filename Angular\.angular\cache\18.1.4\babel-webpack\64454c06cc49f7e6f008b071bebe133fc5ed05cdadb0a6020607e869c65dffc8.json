{"ast": null, "code": "// language list\nimport { locale as enLang } from './modules/i18n/vocabs/en';\nimport { locale as chLang } from './modules/i18n/vocabs/ch';\nimport { locale as esLang } from './modules/i18n/vocabs/es';\nimport { locale as jpLang } from './modules/i18n/vocabs/jp';\nimport { locale as deLang } from './modules/i18n/vocabs/de';\nimport { locale as frLang } from './modules/i18n/vocabs/fr';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./modules/i18n\";\nimport * as i2 from \"./_metronic/partials/layout/theme-mode-switcher/theme-mode.service\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = [\"root\", \"\"];\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    translationService;\n    modeService;\n    constructor(translationService, modeService) {\n      this.translationService = translationService;\n      this.modeService = modeService;\n      // register translations\n      this.translationService.loadTranslations(enLang, chLang, esLang, jpLang, deLang, frLang);\n    }\n    ngOnInit() {\n      this.modeService.init();\n      // Hide splash screen when app is ready\n      this.hideSplashScreen();\n      // Test Kendo UI setup\n      console.log('Kendo UI setup check:', {\n        kendoAvailable: typeof window !== 'undefined' && window.kendo,\n        angularVersion: '18.1.4'\n      });\n    }\n    hideSplashScreen() {\n      // Hide the static splash screen from index.html\n      setTimeout(() => {\n        const splashScreen = document.getElementById('splash-screen');\n        if (splashScreen) {\n          splashScreen.style.opacity = '0';\n          splashScreen.style.transition = 'opacity 0.5s ease-out';\n          setTimeout(() => {\n            splashScreen.style.display = 'none';\n          }, 500);\n        }\n      }, 100); // Small delay to ensure smooth transition\n    }\n    static ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.TranslationService), i0.ɵɵdirectiveInject(i2.ThemeModeService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"body\", \"root\", \"\"]],\n      attrs: _c0,\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i3.RouterOutlet],\n      styles: [\"[_nghost-%COMP%]{height:100%;margin:0}\"],\n      changeDetection: 0\n    });\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}