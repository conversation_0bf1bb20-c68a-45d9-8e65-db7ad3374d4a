{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { PermitsComponent } from './permits.component';\nimport { PermitEditComponent } from './permit-edit/permit-edit.component';\nimport { PermitListComponent } from './permit-list/permit-list.component';\nimport { PermitViewComponent } from './permit-view/permit-view.component';\nimport { PermitsRoutingModule } from './permits-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonsModule } from '@progress/kendo-angular-buttons';\nimport { DateInputsModule } from '@progress/kendo-angular-dateinputs';\nimport { DropDownsModule } from '@progress/kendo-angular-dropdowns';\nimport { GridModule } from '@progress/kendo-angular-grid';\nimport { InputsModule } from '@progress/kendo-angular-inputs';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { PermitPopupComponent } from './permit-popup/permit-popup.component';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ReviewDetailsModalComponent } from './review-details-modal/review-details-modal.component';\nimport { AddEditInternalReviewComponent } from './add-edit-internal-review/add-edit-internal-review.component';\nimport { EditExternalReviewComponent } from './edit-external-review/edit-external-review.component';\nimport { ResponseModalComponent } from './response-modal/response-modal.component';\nimport * as i0 from \"@angular/core\";\nexport class PermitsModule {\n  static ɵfac = function PermitsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitsModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: PermitsModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, PermitsRoutingModule, SharedModule, FormsModule, GridModule, InputsModule, DropDownsModule, ButtonsModule, DateInputsModule, InlineSVGModule, ReactiveFormsModule,\n    // ✅ Needed for [formGroup]\n    NgbModule, NgSelectModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PermitsModule, {\n    declarations: [PermitsComponent, PermitListComponent, PermitEditComponent, PermitPopupComponent, PermitViewComponent, ReviewDetailsModalComponent, AddEditInternalReviewComponent, EditExternalReviewComponent, ResponseModalComponent],\n    imports: [CommonModule, PermitsRoutingModule, SharedModule, FormsModule, GridModule, InputsModule, DropDownsModule, ButtonsModule, DateInputsModule, InlineSVGModule, ReactiveFormsModule,\n    // ✅ Needed for [formGroup]\n    NgbModule, NgSelectModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "PermitsComponent", "PermitEditComponent", "PermitListComponent", "PermitViewComponent", "PermitsRoutingModule", "SharedModule", "FormsModule", "ReactiveFormsModule", "ButtonsModule", "DateInputsModule", "DropDownsModule", "GridModule", "InputsModule", "InlineSVGModule", "PermitPopupComponent", "NgbModule", "NgSelectModule", "ReviewDetailsModalComponent", "AddEditInternalReviewComponent", "EditExternalReviewComponent", "ResponseModalComponent", "PermitsModule", "declarations", "imports"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permits.module.ts"], "sourcesContent": ["import {\n  NgModule,\n  NO_ERRORS_SCHEMA,\n  CUSTOM_ELEMENTS_SCHEMA,\n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { PermitsComponent } from './permits.component';\nimport { PermitEditComponent } from './permit-edit/permit-edit.component';\nimport { PermitListComponent } from './permit-list/permit-list.component';\nimport { PermitViewComponent } from './permit-view/permit-view.component';\nimport { PermitsRoutingModule } from './permits-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonsModule } from '@progress/kendo-angular-buttons';\nimport { DateInputsModule } from '@progress/kendo-angular-dateinputs';\nimport { DropDownsModule } from '@progress/kendo-angular-dropdowns';\nimport { GridModule } from '@progress/kendo-angular-grid';\nimport { InputsModule } from '@progress/kendo-angular-inputs';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { PermitPopupComponent } from './permit-popup/permit-popup.component';\nimport { NgbModal, NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgSelectModule } from '@ng-select/ng-select';\n\nimport { ReviewDetailsModalComponent } from './review-details-modal/review-details-modal.component';\nimport { AddEditInternalReviewComponent } from './add-edit-internal-review/add-edit-internal-review.component';\nimport { EditExternalReviewComponent } from './edit-external-review/edit-external-review.component';\nimport { ResponseModalComponent } from './response-modal/response-modal.component';\n\n@NgModule({\n  declarations: [\n    PermitsComponent,\n    PermitListComponent,\n    PermitEditComponent,\n    PermitPopupComponent,\n    PermitViewComponent,\n    ReviewDetailsModalComponent,\n    AddEditInternalReviewComponent,\n    EditExternalReviewComponent,\n    ResponseModalComponent\n  ],\n  imports: [\n    CommonModule,\n    PermitsRoutingModule,\n    SharedModule,\n    FormsModule,\n    GridModule,\n    InputsModule,\n    DropDownsModule,\n    ButtonsModule,\n    DateInputsModule,\n    InlineSVGModule,\n    ReactiveFormsModule, // ✅ Needed for [formGroup]\n    NgbModule,\n\n    NgSelectModule,\n  ],\n  schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],\n})\nexport class PermitsModule {}\n"], "mappings": "AAKA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAAmBC,SAAS,QAAQ,4BAA4B;AAChE,SAASC,cAAc,QAAQ,sBAAsB;AAErD,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,8BAA8B,QAAQ,+DAA+D;AAC9G,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,sBAAsB,QAAQ,2CAA2C;;AAgClF,OAAM,MAAOC,aAAa;;qCAAbA,aAAa;EAAA;;UAAbA;EAAa;;cAjBtBtB,YAAY,EACZK,oBAAoB,EACpBC,YAAY,EACZC,WAAW,EACXK,UAAU,EACVC,YAAY,EACZF,eAAe,EACfF,aAAa,EACbC,gBAAgB,EAChBI,eAAe,EACfN,mBAAmB;IAAE;IACrBQ,SAAS,EAETC,cAAc;EAAA;;;2EAILK,aAAa;IAAAC,YAAA,GA5BtBtB,gBAAgB,EAChBE,mBAAmB,EACnBD,mBAAmB,EACnBa,oBAAoB,EACpBX,mBAAmB,EACnBc,2BAA2B,EAC3BC,8BAA8B,EAC9BC,2BAA2B,EAC3BC,sBAAsB;IAAAG,OAAA,GAGtBxB,YAAY,EACZK,oBAAoB,EACpBC,YAAY,EACZC,WAAW,EACXK,UAAU,EACVC,YAAY,EACZF,eAAe,EACfF,aAAa,EACbC,gBAAgB,EAChBI,eAAe,EACfN,mBAAmB;IAAE;IACrBQ,SAAS,EAETC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}