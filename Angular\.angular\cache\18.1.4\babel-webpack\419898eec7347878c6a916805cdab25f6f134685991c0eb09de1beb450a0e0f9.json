{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction ResponseModalComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 5)(2, \"div\", 15)(3, \"input\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ResponseModalComponent_div_15_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.responseForm.lockResponse, $event) || (ctx_r1.responseForm.lockResponse = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function ResponseModalComponent_div_15_Template_input_ngModelChange_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onLockResponseChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\", 17);\n    i0.ɵɵtext(5, \" Lock Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 18);\n    i0.ɵɵtext(7, \" When checked, this response cannot be edited by other users \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.responseForm.lockResponse);\n  }\n}\nfunction ResponseModalComponent_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n}\nexport let ResponseModalComponent = /*#__PURE__*/(() => {\n  class ResponseModalComponent {\n    activeModal;\n    correction;\n    review;\n    permitId;\n    loggedInUserId;\n    isAdmin = false;\n    responseSubmitted = new EventEmitter();\n    responseCompleted = new EventEmitter();\n    responseForm = {\n      EORAOROwner_Response: '',\n      commentResponsedBy: '',\n      lockResponse: false\n    };\n    isLoading = false;\n    constructor(activeModal) {\n      this.activeModal = activeModal;\n    }\n    ngOnInit() {\n      if (this.correction) {\n        this.responseForm = {\n          EORAOROwner_Response: this.correction.EORAOROwner_Response || '',\n          commentResponsedBy: this.correction.commentResponsedBy || '',\n          lockResponse: this.correction.lockResponse || false\n        };\n      }\n    }\n    onLockResponseChange() {\n      // This method is called when the checkbox state changes\n      // The form fields will be automatically enabled/disabled based on the lockResponse value\n    }\n    submitResponse() {\n      if (!this.correction || !this.review) {\n        return;\n      }\n      this.isLoading = true;\n      const formData = {\n        EORAOROwner_Response: this.responseForm.EORAOROwner_Response,\n        commentResponsedBy: this.responseForm.commentResponsedBy,\n        lockResponse: this.responseForm.lockResponse,\n        permitId: this.permitId,\n        correctionId: this.correction.CorrectionID,\n        commentsId: this.review.commentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      // Emit the form data to the parent component\n      this.responseSubmitted.emit(formData);\n    }\n    closeModal() {\n      this.activeModal.dismiss();\n    }\n    static ɵfac = function ResponseModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ResponseModalComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResponseModalComponent,\n      selectors: [[\"app-response-modal\"]],\n      inputs: {\n        correction: \"correction\",\n        review: \"review\",\n        permitId: \"permitId\",\n        loggedInUserId: \"loggedInUserId\",\n        isAdmin: \"isAdmin\"\n      },\n      outputs: {\n        responseSubmitted: \"responseSubmitted\",\n        responseCompleted: \"responseCompleted\"\n      },\n      decls: 22,\n      vars: 7,\n      consts: [[1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\", \"medium-modal-body\"], [1, \"row\"], [1, \"col-12\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"rows\", \"4\", \"placeholder\", \"Enter your response to this correction\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"row\", \"mt-3\"], [\"type\", \"text\", \"placeholder\", \"Who is responding to this correction\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"row mt-3\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"lockResponse\", 1, \"form-check-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"lockResponse\", 1, \"form-check-label\", \"fw-bold\"], [1, \"form-text\", \"text-muted\", \"d-block\", \"mt-1\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function ResponseModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n          i0.ɵɵtext(2, \"Respond to Correction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function ResponseModalComponent_Template_button_click_3_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6);\n          i0.ɵɵtext(8, \"EOR / AOR / Owner Response\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"textarea\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ResponseModalComponent_Template_textarea_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.responseForm.EORAOROwner_Response, $event) || (ctx.responseForm.EORAOROwner_Response = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 5)(12, \"label\", 6);\n          i0.ɵɵtext(13, \"Comment Responded By\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ResponseModalComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.responseForm.commentResponsedBy, $event) || (ctx.responseForm.commentResponsedBy = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(15, ResponseModalComponent_div_15_Template, 8, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 11)(17, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function ResponseModalComponent_Template_button_click_17_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(18, \"Cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function ResponseModalComponent_Template_button_click_19_listener() {\n            return ctx.submitResponse();\n          });\n          i0.ɵɵtemplate(20, ResponseModalComponent_span_20_Template, 1, 0, \"span\", 14);\n          i0.ɵɵtext(21, \" Submit Response \");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.responseForm.EORAOROwner_Response);\n          i0.ɵɵproperty(\"disabled\", ctx.responseForm.lockResponse);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.responseForm.commentResponsedBy);\n          i0.ɵɵproperty(\"disabled\", ctx.responseForm.lockResponse);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i2.NgIf, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".modal-header[_ngcontent-%COMP%]{background-color:#549c54;color:#fff;padding:1rem 1.75rem}.modal-title[_ngcontent-%COMP%]{color:#fff;font-weight:600}.btn-close[_ngcontent-%COMP%]{filter:invert(1)}.modal-body[_ngcontent-%COMP%]{padding:1.5rem}.modal-footer[_ngcontent-%COMP%]{padding:1rem 1.5rem}.form-label[_ngcontent-%COMP%]{font-weight:600;color:#495057}.form-control[_ngcontent-%COMP%]{border:1px solid #ced4da;border-radius:.375rem}.form-control[_ngcontent-%COMP%]:focus{border-color:#3699ff;box-shadow:0 0 0 .2rem #3699ff40}.btn-success[_ngcontent-%COMP%]{background-color:#198754;border-color:#198754}.btn-success[_ngcontent-%COMP%]:hover{background-color:#157347;border-color:#146c43}\"]\n    });\n  }\n  return ResponseModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}