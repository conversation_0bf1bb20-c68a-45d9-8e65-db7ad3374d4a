{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { NavigationStart } from '@angular/router';\nimport { EmailTemplatesEditComponent } from '../email-templates-edit/email-templates-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"src/app/modules/services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/email-template.service\";\nimport * as i8 from \"../../services/kendo-column.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@progress/kendo-angular-grid\";\nimport * as i12 from \"@progress/kendo-angular-inputs\";\nimport * as i13 from \"@progress/kendo-angular-buttons\";\nimport * as i14 from \"ng-inline-svg-2\";\nimport * as i15 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c7 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction EmailTemplatesListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"span\", 19);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"kendo-textbox\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    })(\"clear\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_clear_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"span\", 24);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 27);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(12, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(14, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(16, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_16_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37)(3, \"label\", 38);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"label\", 38);\n    i0.ɵɵtext(8, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.role, $event) || (ctx_r2.appliedFilters.role = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 41)(11, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(12, \"i\", 43);\n    i0.ɵɵtext(13, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(15, \"i\", 45);\n    i0.ɵɵtext(16, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.roles);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.role);\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_template_16_div_0_Template, 17, 4, \"div\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const dataItem_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.unlockUser(dataItem_r6));\n    });\n    i0.ɵɵelement(1, \"span\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 55);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.templatePID));\n    });\n    i0.ɵɵelement(1, \"span\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template, 2, 1, \"a\", 57);\n  }\n  if (rf & 2) {\n    const dataItem_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen055.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r6.IsLocked);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 53);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template, 3, 2, \"ng-template\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c6));\n    i0.ɵɵproperty(\"width\", 125)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(10, _c7))(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r8.templateName, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r9 = ctx.$implicit;\n    const column_r10 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r10)(\"filter\", filter_r9)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 60);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"templateName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"templateName\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r11.emailTo, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r12 = ctx.$implicit;\n    const column_r13 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r13)(\"filter\", filter_r12)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 64);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 250)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"email\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"emailTo\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r14.emailSubject, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r15 = ctx.$implicit;\n    const column_r16 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r16)(\"filter\", filter_r15)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 65);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"title\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"title\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r17.category, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r18 = ctx.$implicit;\n    const column_r19 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r19)(\"filter\", filter_r18)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 66);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"roleName\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"roleName\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 70);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 71);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen040.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_0_Template, 1, 1, \"span\", 68)(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_1_Template, 1, 1, \"span\", 69);\n  }\n  if (rf & 2) {\n    const dataItem_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", dataItem_r20.userStatus === \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r20.userStatus === \"Inactive\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 72);\n    i0.ɵɵlistener(\"valueChange\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r21 = i0.ɵɵrestoreView(_r21);\n      const filter_r23 = ctx_r21.$implicit;\n      const column_r24 = ctx_r21.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onStatusFilterChange($event, filter_r23, column_r24));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r23 = ctx.$implicit;\n    const column_r24 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"data\", ctx_r2.filterOptions)(\"value\", ctx_r2.getFilterValue(filter_r23, column_r24));\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 67);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_Template, 2, 2, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template, 1, 2, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 100)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userStatus\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"userStatus\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"span\", 74);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r25 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, dataItem_r25.lastUpdatedDate, \"MM/dd/yyyy hh:mm a\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(dataItem_r25.lastUpdatedByFullName);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 75);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r27 = ctx.column;\n    const filterService_r28 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r27)(\"filter\", filter_r26)(\"filterService\", filterService_r28);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 73);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_1_Template, 6, 5, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_2_Template, 7, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 160)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"maxResizableWidth\", 240)(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_Template, 2, 11, \"kendo-grid-column\", 46)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_Template, 3, 8, \"kendo-grid-column\", 47)(3, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_Template, 3, 7, \"kendo-grid-column\", 48)(4, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_Template, 3, 7, \"kendo-grid-column\", 49)(5, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_Template, 3, 7, \"kendo-grid-column\", 50)(6, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_Template, 3, 7, \"kendo-grid-column\", 51)(7, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_Template, 3, 8, \"kendo-grid-column\", 52);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r29 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"templateName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"emailTo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"emailSubject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"category\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"userStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"lastUpdatedDate\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_18_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78)(2, \"div\", 79)(3, \"span\", 19);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 24);\n    i0.ɵɵtext(6, \"Loading users... Please wait...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 80);\n    i0.ɵɵelement(2, \"i\", 81);\n    i0.ɵɵelementStart(3, \"p\", 24);\n    i0.ɵɵtext(4, \"No users found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_18_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 83);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_template_18_div_0_Template, 7, 0, \"div\", 76)(1, EmailTemplatesListComponent_ng_template_18_div_1_Template, 8, 0, \"div\", 76);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading === false && ctx_r2.serverSideRowData.length === 0);\n  }\n}\nexport class EmailTemplatesListComponent {\n  usersService;\n  cdr;\n  router;\n  route;\n  modalService;\n  AppService;\n  customLayoutUtilsService;\n  httpUtilService;\n  emailTemplateService;\n  kendoColumnService;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Active',\n    value: 'Active'\n  }, {\n    text: 'Inactive',\n    value: 'Inactive'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }],\n    roles: [],\n    // Will be populated from backend\n    categories: [] // Will be populated from backend\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration for the new system\n  gridColumns = [];\n  defaultColumns = [];\n  fixedColumns = [];\n  draggableColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Enhanced Columns with Kendo UI features\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Actions',\n    width: 80,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'templateName',\n    title: 'Template Name',\n    width: 150,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  },\n  // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\n  {\n    field: 'emailTo',\n    title: 'Email',\n    width: 250,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 4\n  }, {\n    field: 'emailSubject',\n    title: 'Email Subject',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 5\n  }, {\n    field: 'emailBody',\n    title: 'Email Body',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 6\n  }, {\n    field: 'category',\n    title: 'Category',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 7\n  }, {\n    field: 'templateStatus',\n    title: 'Template Status',\n    width: 100,\n    type: 'status',\n    isFixed: false,\n    filterable: true,\n    order: 8\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated Date',\n    width: 160,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 9\n  }];\n  // OLD SYSTEM - to be removed\n  columnsVisibility = {};\n  // Old column configuration management removed - replaced with new system\n  // State\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  // Router subscription for saving state on navigation\n  routerSubscription;\n  // Storage key for state persistence\n  GRID_STATE_KEY = 'form-templates-grid-state';\n  // Pagination\n  page = {\n    size: 10,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  // Export options\n  exportOptions = [{\n    text: 'Export All',\n    value: 'all'\n  }, {\n    text: 'Export Selected',\n    value: 'selected'\n  }, {\n    text: 'Export Filtered',\n    value: 'filtered'\n  }];\n  // Selection state\n  selectedUsers = [];\n  isAllSelected = false;\n  // Statistics\n  userStatistics = {\n    activeUsers: 0,\n    inactiveUsers: 0,\n    suspendedUsers: 0,\n    lockedUsers: 0,\n    totalUsers: 0\n  };\n  // Bulk operations\n  showBulkActions = false;\n  bulkActionStatus = 'Active';\n  //add or edit default paramters\n  permissionArray = [];\n  constructor(usersService, cdr, router, route, modalService,\n  // Provides modal functionality to display modals\n  AppService, customLayoutUtilsService, httpUtilService, emailTemplateService, kendoColumnService) {\n    this.usersService = usersService;\n    this.cdr = cdr;\n    this.router = router;\n    this.route = route;\n    this.modalService = modalService;\n    this.AppService = AppService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilService = httpUtilService;\n    this.emailTemplateService = emailTemplateService;\n    this.kendoColumnService = kendoColumnService;\n  }\n  goBack() {\n    this.router.navigate(['/setting/view']);\n  }\n  ngOnInit() {\n    this.loginUser = this.AppService.getLoggedInUser();\n    console.log('Login user loaded:', this.loginUser);\n    // Setup search with debounce\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n      // Set loading state for search\n      this.loading = true;\n      this.isLoading = true;\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.loadTable();\n    });\n    // Subscribe to router events to save state before navigation\n    this.routerSubscription = this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.saveGridState();\n      }\n    });\n    // Load saved state if available\n    this.loadGridState();\n    // Load roles for advanced filters\n    this.loadRoles();\n    // Load user statistics\n    // this.loadUserStatistics();\n    // Initialize with default page load\n    this.onPageLoad();\n    // Initialize new column visibility system\n    this.initializeColumnVisibilitySystem();\n    // Load column configuration after a short delay to ensure loginUser is available\n    setTimeout(() => {\n      this.loadColumnConfigFromDatabase();\n    }, 100);\n  }\n  /**\n   * Initialize the new column visibility system\n   */\n  initializeColumnVisibilitySystem() {\n    // Initialize default columns\n    this.defaultColumns = this.gridColumnConfig.map(col => col.field);\n    this.gridColumns = [...this.defaultColumns];\n    // Set fixed columns (first 3 columns)\n    this.fixedColumns = ['action', 'FirstName', 'LastName'];\n    // Set draggable columns (all except fixed)\n    this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\n    // Initialize normal and expanded grid references\n    this.normalGrid = this.grid;\n    this.expandedGrid = this.grid;\n    // Load additional data after main data is loaded\n    setTimeout(() => {\n      this.loadCategories();\n      this.loadTemplateStatistics();\n    }, 100);\n  }\n  ngAfterViewInit() {\n    // Load the table after the view is initialized\n    // Small delay to ensure the grid is properly rendered\n    setTimeout(() => {\n      this.loadTable();\n    }, 200);\n  }\n  // Method to handle when the component becomes visible\n  onTabActivated() {\n    // Set loading state for tab activation\n    this.loading = true;\n    this.isLoading = true;\n    // Refresh the data when the tab is activated\n    this.loadTable();\n    this.loadTemplateStatistics();\n  }\n  // Method to handle initial page load\n  onPageLoad() {\n    // Initialize the component with default data\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.searchData = '';\n    // Load the data\n    this.loadTable();\n  }\n  // Refresh grid data - only refresh the grid with latest API call\n  refreshGrid() {\n    // Set loading state to show full-screen loader\n    this.loading = true;\n    this.isLoading = true;\n    // Reset to first page and clear any applied filters\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    // Clear search data\n    this.searchData = '';\n    // Load fresh data from API\n    this.loadTable();\n  }\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.routerSubscription) {\n      this.routerSubscription.unsubscribe();\n    }\n    if (this.searchSubscription) {\n      this.searchSubscription.unsubscribe();\n    }\n    this.searchTerms.complete();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId\n    };\n    this.emailTemplateService.getEmailTemplatesForKendoGrid(state).subscribe({\n      next: data => {\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          this.handleEmptyResponse();\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const userData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = userData.length !== 0;\n          this.serverSideRowData = userData;\n          this.gridData = this.serverSideRowData;\n          console.log('this.gridData', this.gridData);\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n        }\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      error: error => {\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Enhanced loadTable method that can use either endpoint\n  loadTable() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Use the new Kendo UI specific endpoint for better performance\n      _this.loadTableWithKendoEndpoint();\n    })();\n  }\n  handleEmptyResponse() {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n    this.cdr.detectChanges();\n  }\n  // Enhanced search handling\n  clearSearch() {\n    if (this.searchData === '') {\n      this.searchTerms.next('');\n    }\n  }\n  onSearchChange() {\n    this.searchTerms.next(this.searchData || '');\n  }\n  // Clear all filters and search\n  clearAllFilters() {\n    this.searchData = '';\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.loadTable();\n  }\n  // Apply advanced filters\n  applyAdvancedFilters() {\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.loadTable();\n  }\n  // Toggle advanced filters panel\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  // Load roles for advanced filters\n  loadRoles() {\n    const queryParams = {\n      pageSize: 1000,\n      sortOrder: 'ASC',\n      sortField: 'roleName',\n      pageNumber: 0\n    };\n    this.usersService.getAllRoles(queryParams).subscribe({\n      next: data => {\n        if (data && data.responseData && data.responseData.content) {\n          this.advancedFilterOptions.roles = [{\n            text: 'All Roles',\n            value: null\n          }, ...data.responseData.content.map(role => ({\n            text: role.roleName,\n            value: role.roleName\n          }))];\n        } else {\n          // Set default if no data\n          this.advancedFilterOptions.categories = [{\n            text: 'All Categories',\n            value: null\n          }];\n        }\n      },\n      error: error => {\n        console.error('Error loading roles:', error);\n        // Set default roles if loading fails\n        this.advancedFilterOptions.roles = [{\n          text: 'All Roles',\n          value: null\n        }];\n      }\n    });\n    this.usersService.getDefaultPermissions({}).subscribe(permissions => {\n      this.permissionArray = permissions.responseData;\n    });\n  }\n  // Load user statistics\n  loadUserStatistics() {\n    this.usersService.getUserStatistics().subscribe({\n      next: data => {\n        if (data && data.statistics) {\n          this.userStatistics = data.statistics;\n        }\n      },\n      error: error => {\n        console.error('Error loading user statistics:', error);\n      }\n    });\n  }\n  // Selection handling\n  onSelectionChange(selection) {\n    this.selectedUsers = selection.selectedRows || [];\n    this.isAllSelected = this.selectedUsers.length === this.serverSideRowData.length;\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n  // Select all users\n  selectAllUsers() {\n    if (this.isAllSelected) {\n      this.selectedUsers = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedUsers = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n  // Delete user\n  deleteUser(user) {\n    if (confirm(`Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const deleteData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.deleteUser(deleteData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: error => {\n          console.error('Error deleting user:', error);\n          this.customLayoutUtilsService.showError('Error deleting user', '');\n          //alert('Error deleting user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  // Bulk update user status\n  bulkUpdateUserStatus() {\n    if (this.selectedUsers.length === 0) {\n      this.customLayoutUtilsService.showError('Please select users to update', '');\n      //alert('Please select users to update.');\n      return;\n    }\n    if (confirm(`Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const bulkUpdateData = {\n        userIds: this.selectedUsers.map(user => user.userId),\n        status: this.bulkActionStatus,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n            this.selectedUsers = []; // Clear selection\n            this.showBulkActions = false;\n          }\n        },\n        error: error => {\n          console.error('Error updating users:', error);\n          //alert('Error updating users. Please try again.');\n          this.customLayoutUtilsService.showError('Error updating users', '');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  // Unlock user\n  unlockUser(user) {\n    if (confirm(`Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const unlockData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.unlockUser(unlockData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: error => {\n          console.error('Error unlocking user:', error);\n          this.customLayoutUtilsService.showSuccess('Error unlocking user. Please try again', '');\n          //alert('Error unlocking user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  onSearchKeyDown(event) {\n    if (event.key === 'Enter') {\n      this.searchTerms.next(this.searchData);\n    }\n  }\n  // Enhanced function to filter data from search and advanced filters\n  filterConfiguration() {\n    let filter = {\n      paginate: true,\n      search: '',\n      columnFilter: []\n    };\n    // Handle search text\n    let searchText;\n    if (this.searchData === null || this.searchData === undefined) {\n      searchText = '';\n    } else {\n      searchText = this.searchData;\n    }\n    filter.search = searchText.trim();\n    // Handle Kendo UI grid filters\n    if (this.activeFilters && this.activeFilters.length > 0) {\n      filter.columnFilter = [...this.activeFilters];\n    }\n    // Add advanced filters\n    if (this.appliedFilters.status && this.appliedFilters.status !== null) {\n      filter.columnFilter.push({\n        field: 'userStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    if (this.appliedFilters.role && this.appliedFilters.role !== null) {\n      filter.columnFilter.push({\n        field: 'roleName',\n        operator: 'eq',\n        value: this.appliedFilters.role\n      });\n    }\n    return filter;\n  }\n  // Grid event handlers\n  pageChange(event) {\n    this.skip = event.skip;\n    this.page.pageNumber = event.skip / event.take;\n    this.page.size = event.take;\n    this.loadTable();\n  }\n  onSortChange(sort) {\n    console.log('Sort change triggered:', sort);\n    // Handle empty sort array (normalize/unsort case)\n    const incomingSort = Array.isArray(sort) ? sort : [];\n    if (incomingSort.length === 0) {\n      // Normalize/unsort case - return to default sorting\n      console.log('Normalize triggered - returning to default sort');\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n      // Reset the grid's sort state to default\n      if (this.grid) {\n        this.grid.sort = [{\n          field: 'lastUpdatedDate',\n          dir: 'desc'\n        }];\n      }\n    } else {\n      // Normal sorting case\n      this.sort = incomingSort;\n      this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = this.sort[0].dir || 'desc';\n    }\n    console.log('Final sort state:', this.sort);\n    console.log('Page order:', {\n      orderBy: this.page.orderBy,\n      orderDir: this.page.orderDir\n    });\n    this.loadTable();\n  }\n  filterChange(filter) {\n    this.filter = filter;\n    this.gridFilter = filter;\n    this.activeFilters = this.flattenFilters(filter);\n    this.page.pageNumber = 0;\n    this.saveGridState();\n    // Set loading state for sorting\n    this.loading = true;\n    this.isLoading = true;\n    this.skip = 0;\n    this.loadTable();\n  }\n  // Old column visibility methods removed - replaced with new system\n  // Fix 2: More robust getFilterValue method\n  getFilterValue(filter, column) {\n    if (!filter || !filter.filters || !column) {\n      return null;\n    }\n    const predicate = filter.filters.find(f => f && 'field' in f && f.field === column.field);\n    return predicate && 'value' in predicate ? predicate.value : null;\n  }\n  // Fix 3: More robust onStatusFilterChange method\n  onStatusFilterChange(value, filter, column) {\n    if (!filter || !filter.filters || !column) {\n      console.error('Invalid filter or column:', {\n        filter,\n        column\n      });\n      return;\n    }\n    const exists = filter.filters.findIndex(f => f && 'field' in f && f.field === column.field);\n    if (exists > -1) {\n      filter.filters.splice(exists, 1);\n    }\n    if (value !== null) {\n      filter.filters.push({\n        field: column.field,\n        operator: 'eq',\n        value: value\n      });\n    }\n    this.filterChange(filter);\n  }\n  // Fix 4: More robust flattenFilters method\n  flattenFilters(filter) {\n    const filters = [];\n    if (!filter || !filter.filters) {\n      return filters;\n    }\n    filter.filters.forEach(f => {\n      if (f && 'field' in f) {\n        // It's a FilterDescriptor\n        filters.push({\n          field: f.field,\n          operator: f.operator,\n          value: f.value\n        });\n      } else if (f && 'filters' in f) {\n        // It's a CompositeFilterDescriptor\n        filters.push(...this.flattenFilters(f));\n      }\n    });\n    return filters;\n  }\n  // Fix 5: More robust loadGridState method\n  loadGridState() {\n    try {\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n      if (!savedState) {\n        return;\n      }\n      const state = JSON.parse(savedState);\n      // Restore sort state\n      if (state && state.sort) {\n        this.sort = state.sort;\n        if (this.sort && this.sort.length > 0 && this.sort[0]) {\n          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n          this.page.orderDir = this.sort[0].dir || 'desc';\n        }\n      }\n      // Restore filter state\n      if (state && state.filter) {\n        this.filter = state.filter;\n        this.gridFilter = state.filter;\n        this.activeFilters = state.activeFilters || [];\n      }\n      // Restore pagination state\n      if (state && state.page) {\n        this.page = state.page;\n      }\n      if (state && state.skip !== undefined) {\n        this.skip = state.skip;\n      }\n      // Restore column visibility\n      if (state && state.columnsVisibility) {\n        this.columnsVisibility = state.columnsVisibility;\n      }\n      // Restore search state\n      if (state && state.searchData) {\n        this.searchData = state.searchData;\n      }\n      // Restore advanced filter states\n      if (state && state.appliedFilters) {\n        this.appliedFilters = state.appliedFilters;\n      }\n      if (state && state.showAdvancedFilters !== undefined) {\n        this.showAdvancedFilters = state.showAdvancedFilters;\n      }\n    } catch (error) {\n      console.error('Error loading grid state:', error);\n      // If there's an error, use default state\n    }\n  }\n  /**\n   * Reset the current state of column visibility and order in the grid to its original state.\n   */\n  resetTable() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page and try again.', '');\n      return;\n    }\n    // Reset all grid state to default\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.searchData = '';\n    this.appliedFilters = {};\n    this.showAdvancedFilters = false;\n    // Reset column visibility and order\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        const index = this.gridColumns.indexOf(column.field);\n        if (index !== -1) {\n          column.orderIndex = index;\n        }\n        // Reset column visibility - show all columns\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n    }\n    // Clear hidden columns\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.hiddenFields = [];\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Prepare reset data\n    const userData = {\n      pageName: 'EmailTemplates',\n      userID: this.loginUser.userId,\n      hiddenData: [],\n      kendoColOrder: [],\n      LoggedId: this.loginUser.userId\n    };\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n    // Save reset state to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Also clear from localStorage\n          this.kendoColumnService.clearFromLocalStorage('EmailTemplates');\n          this.customLayoutUtilsService.showSuccess(res.message || 'Column settings reset successfully.', '');\n        } else {\n          this.customLayoutUtilsService.showError(res.message || 'Failed to reset column settings.', '');\n        }\n        // Trigger change detection and refresh grid\n        this.cdr.detectChanges();\n        // Small delay to ensure the grid is updated\n        setTimeout(() => {\n          if (this.grid) {\n            this.grid.refresh();\n          }\n        }, 100);\n        this.loadTable();\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error resetting column settings:', error);\n        // Check if it's an authentication error\n        if (error.status === 401 || error.error && error.error.status === 401) {\n          this.customLayoutUtilsService.showError('Authentication failed. Please login again.', '');\n          // Optionally redirect to login page\n        } else {\n          this.customLayoutUtilsService.showError('Error resetting column settings. Please try again.', '');\n        }\n      }\n    });\n  }\n  // Grid state persistence methods\n  saveGridState() {\n    const state = {\n      sort: this.sort,\n      filter: this.filter,\n      page: this.page,\n      skip: this.skip,\n      columnsVisibility: this.columnsVisibility,\n      searchData: this.searchData,\n      activeFilters: this.activeFilters,\n      appliedFilters: this.appliedFilters,\n      showAdvancedFilters: this.showAdvancedFilters\n    };\n    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n  }\n  // Function to add a new company (calls edit function with ID 0)\n  add() {\n    this.edit(0);\n  }\n  // Function to open the edit modal for adding/editing a company\n  edit(id) {\n    console.log('Line: 413', 'call edit function: ', id);\n    // Configuration options for the modal dialog\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddCompaniesComponent\n    const modalRef = this.modalService.open(EmailTemplatesEditComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = id;\n    // modalRef.componentInstance.defaultPermissions = this.permissionArray;\n    // // Subscribe to the modal event when data is updated\n    // modalRef.componentInstance.passEntry.subscribe((receivedEntry: boolean) => {\n    //   if (receivedEntry === true) {\n    //     // Reload the table data after a successful update\n    //     this.loadTable();\n    //   }\n    // });\n  }\n  // Delete functionality removed\n  // Load categories for advanced filters\n  loadCategories() {\n    // Implementation for loading categories\n    console.log('Loading categories...');\n  }\n  // Load template statistics\n  loadTemplateStatistics() {\n    // Implementation for loading template statistics\n    console.log('Loading template statistics...');\n  }\n  // Setup search subscription\n  setupSearchSubscription() {\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.loadTable();\n    });\n  }\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Enhanced export functionality\n  onExportClick(event) {\n    switch (event.item.value) {\n      case 'all':\n        this.exportAllUsers();\n        break;\n      case 'selected':\n        this.exportSelectedUsers();\n        break;\n      case 'filtered':\n        this.exportFilteredUsers();\n        break;\n      default:\n        console.warn('Unknown export option:', event.item.value);\n    }\n  }\n  exportAllUsers() {\n    const exportParams = {\n      filters: {},\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'All_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting users:', error);\n        //alert('Error exporting users. Please try again.');\n      }\n    });\n  }\n  exportSelectedUsers() {\n    if (this.selectedUsers.length === 0) {\n      //alert('Please select users to export.');\n      return;\n    }\n    const exportParams = {\n      filters: {\n        userIds: this.selectedUsers.map(user => user.UserId)\n      },\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Selected_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting selected users:', error);\n        //alert('Error exporting selected users. Please try again.');\n      }\n    });\n  }\n  exportFilteredUsers() {\n    const exportParams = {\n      filters: {\n        status: this.appliedFilters.status,\n        role: this.appliedFilters.role,\n        searchTerm: this.searchData\n      },\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Filtered_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting filtered users:', error);\n        //alert('Error exporting filtered users. Please try again.');\n      }\n    });\n  }\n  downloadExcel(data, filename) {\n    // This would typically use a library like xlsx or similar\n    // For now, we'll create a simple CSV download\n    const csvContent = this.convertToCSV(data);\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  convertToCSV(data) {\n    if (data.length === 0) return '';\n    const headers = Object.keys(data[0]);\n    const csvRows = [headers.join(',')];\n    for (const row of data) {\n      const values = headers.map(header => {\n        const value = row[header];\n        return typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\n      });\n      csvRows.push(values.join(','));\n    }\n    return csvRows.join('\\n');\n  }\n  // NEW COLUMN VISIBILITY SYSTEM METHODS\n  /**\n   * Saves the current state of column visibility and order in the grid.\n   * This function categorizes columns into visible and hidden columns, records their titles,\n   * fields, and visibility status, and also captures the order of draggable columns.\n   * After gathering the necessary data, it sends this information to the backend for saving.\n   */\n  saveHead() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page.', '');\n      return;\n    }\n    const nonHiddenColumns = [];\n    const hiddenColumns = [];\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        if (!column.hidden) {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          nonHiddenColumns.push(columnData);\n        } else {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          hiddenColumns.push(columnData);\n        }\n      });\n    }\n    const draggableColumnsOrder = this.gridColumns.filter(col => !this.fixedColumns.includes(col)).map((field, index) => ({\n      field,\n      orderIndex: index\n    }));\n    // Prepare data for backend\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: hiddenColumns,\n      kendoColOrder: draggableColumnsOrder,\n      LoggedId: this.loginUser.userId\n    };\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n    // Save to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          // Also save to localStorage as backup\n          this.kendoColumnService.saveToLocalStorage(userData);\n          this.customLayoutUtilsService.showSuccess(res.message || 'Column settings saved successfully.', '');\n        } else {\n          this.customLayoutUtilsService.showError(res.message || 'Failed to save column settings.', '');\n        }\n        this.cdr.markForCheck();\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error saving column settings:', error);\n        // Fallback to localStorage on error\n        this.kendoColumnService.saveToLocalStorage(userData);\n        // Update local state\n        this.hiddenData = hiddenColumns;\n        this.kendoColOrder = draggableColumnsOrder;\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.customLayoutUtilsService.showError('Failed to save to server. Settings saved locally.', '');\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Loads and applies the saved column order from the user preferences or configuration.\n   * This function updates the grid column order, ensuring the fixed columns remain in place\n   * and the draggable columns are ordered according to the saved preferences.\n   */\n  loadSavedColumnOrder(kendoColOrder) {\n    try {\n      const savedOrder = kendoColOrder;\n      if (savedOrder) {\n        const parsedOrder = savedOrder;\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n          // Get only the draggable columns from saved order\n          const savedDraggableColumns = parsedOrder.sort((a, b) => a.orderIndex - b.orderIndex).map(col => col.field).filter(field => !this.fixedColumns.includes(field));\n          // Add any missing draggable columns at the end\n          const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\n          // Combine fixed columns with saved draggable columns\n          this.gridColumns = [...this.fixedColumns, ...savedDraggableColumns, ...missingColumns];\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } else {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    } catch (error) {\n      this.gridColumns = [...this.defaultColumns];\n    }\n  }\n  /**\n   * Checks if a given column is marked as hidden.\n   * This function searches the `hiddenFields` array to determine if the column should be hidden.\n   */\n  getHiddenField(columnName) {\n    return this.hiddenFields.indexOf(columnName) > -1;\n  }\n  /**\n   * Handles the column reordering event triggered when a column is moved by the user.\n   * The function checks if the column being moved is in the fixed columns and prevents reordering\n   * of fixed columns.\n   */\n  onColumnReorder(event) {\n    const {\n      columns,\n      newIndex,\n      oldIndex\n    } = event;\n    // Prevent reordering of fixed columns\n    if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\n      return;\n    }\n    // Update the gridColumns array\n    const reorderedColumns = [...this.gridColumns];\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n    reorderedColumns.splice(newIndex, 0, movedColumn);\n    this.gridColumns = reorderedColumns;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Handles column visibility changes from the Kendo Grid.\n   * Updates the local state when columns are shown or hidden.\n   */\n  updateColumnVisibility(event) {\n    if (this.isExpanded === false) {\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          if (column.hidden) {\n            const exists = this.hiddenData.some(item => item.field === columnData.field && item.hidden === true);\n            if (!exists) {\n              this.hiddenData.push(columnData);\n            }\n          } else {\n            let indexExists = this.hiddenData.findIndex(item => item.field === columnData.field && item.hidden === true);\n            if (indexExists !== -1) {\n              this.hiddenData.splice(indexExists, 1);\n            }\n          }\n        });\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.cdr.markForCheck();\n      }\n    }\n  }\n  /**\n   * Loads the saved column configuration from the backend or localStorage as fallback.\n   * This method is called during component initialization to restore user preferences.\n   */\n  loadColumnConfigFromDatabase() {\n    try {\n      // First try to load from backend\n      if (this.loginUser && this.loginUser.userId) {\n        this.kendoColumnService.getHideFields({\n          pageName: 'Users',\n          userID: this.loginUser.userId\n        }).subscribe({\n          next: res => {\n            if (!res.isFault && res.Data) {\n              this.kendoHide = res.Data;\n              this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\n              this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\n              this.hiddenFields = this.hiddenData.map(col => col.field);\n              // Update grid columns based on the hidden fields\n              if (this.grid && this.grid.columns) {\n                this.grid.columns.forEach(column => {\n                  if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                    column.includeInChooser = true;\n                    column.hidden = true;\n                  } else {\n                    column.hidden = false;\n                  }\n                });\n              }\n              // Load saved column order and update grid\n              this.loadSavedColumnOrder(this.kendoInitColOrder);\n              // Also save to localStorage as backup\n              this.kendoColumnService.saveToLocalStorage({\n                pageName: 'Users',\n                userID: this.loginUser.userId,\n                hiddenData: this.hiddenData,\n                kendoColOrder: this.kendoInitColOrder\n              });\n            }\n          },\n          error: error => {\n            console.error('Error loading from backend, falling back to localStorage:', error);\n            this.loadFromLocalStorageFallback();\n          }\n        });\n      } else {\n        // Fallback to localStorage if no user ID\n        this.loadFromLocalStorageFallback();\n      }\n    } catch (error) {\n      console.error('Error loading column configuration:', error);\n      this.loadFromLocalStorageFallback();\n    }\n  }\n  /**\n   * Fallback method to load column configuration from localStorage\n   */\n  loadFromLocalStorageFallback() {\n    try {\n      const savedConfig = this.kendoColumnService.getFromLocalStorage('Users', this.loginUser?.UserId || 0);\n      if (savedConfig) {\n        this.kendoHide = savedConfig;\n        this.hiddenData = savedConfig.hiddenData || [];\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        // Update grid columns based on the hidden fields\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach(column => {\n            if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n              column.includeInChooser = true;\n              column.hidden = true;\n            } else {\n              column.hidden = false;\n            }\n          });\n        }\n        // Load saved column order and update grid\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\n      }\n    } catch (error) {\n      console.error('Error loading from localStorage fallback:', error);\n    }\n  }\n  static ɵfac = function EmailTemplatesListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EmailTemplatesListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.EmailTemplateService), i0.ɵɵdirectiveInject(i8.KendoColumnService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmailTemplatesListComponent,\n    selectors: [[\"app-email-templates-list\"]],\n    viewQuery: function EmailTemplatesListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    decls: 19,\n    vars: 22,\n    consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"card\", \"mb-5\", \"mb-xl-5\"], [1, \"card-body\", \"pb-0\", \"pt-0\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-3x\", \"border-transparent\", \"fs-5\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", \"active\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"button\", \"title\", \"Go back to Settings Dashboard\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", \"back-button\", 3, \"click\"], [1, \"fa\", \"fa-arrow-left\", \"me-2\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\", \"loading\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"clear\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"type\", \"button\", \"title\", \"Refresh\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Role\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"templateName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"emailTo\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"emailSubject\", \"title\", \"Email Subject\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"category\", \"title\", \"category\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [\"title\", \"Edit\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-primary\", 3, \"inlineSVG\"], [\"title\", \"Unlock\", \"class\", \"btn btn-icon btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Unlock\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-warning\", 3, \"inlineSVG\"], [\"field\", \"templateName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bolder\", \"cursor-pointer\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"emailTo\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"emailSubject\", \"title\", \"Email Subject\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"category\", \"title\", \"category\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"ngbTooltip\", \"Active\", \"class\", \"svg-icon svg-icon-3 svg-icon-success\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Inactive\", \"class\", \"svg-icon svg-icon-3 svg-icon-danger text-danger\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Active\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-success\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"ngbTooltip\", \"Inactive\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", \"text-danger\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"valueChange\", \"data\", \"value\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\"], [1, \"text-gray-600\", \"fs-1r\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"me-3\"], [1, \"text-center\"], [1, \"fas\", \"fa-users\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n    template: function EmailTemplatesListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, EmailTemplatesListComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"ul\", 5)(5, \"li\", 6)(6, \"a\", 7);\n        i0.ɵɵtext(7, \" Email Templates \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_Template_button_click_9_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.goBack());\n        });\n        i0.ɵɵelement(10, \"i\", 10);\n        i0.ɵɵtext(11, \" Back \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(12, \"div\", 11)(13, \"kendo-grid\", 12, 0);\n        i0.ɵɵlistener(\"columnReorder\", function EmailTemplatesListComponent_Template_kendo_grid_columnReorder_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onColumnReorder($event));\n        })(\"selectionChange\", function EmailTemplatesListComponent_Template_kendo_grid_selectionChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function EmailTemplatesListComponent_Template_kendo_grid_filterChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function EmailTemplatesListComponent_Template_kendo_grid_pageChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"sortChange\", function EmailTemplatesListComponent_Template_kendo_grid_sortChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function EmailTemplatesListComponent_Template_kendo_grid_columnVisibilityChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(15, EmailTemplatesListComponent_ng_template_15_Template, 17, 10, \"ng-template\", 13)(16, EmailTemplatesListComponent_ng_template_16_Template, 1, 1, \"ng-template\", 13)(17, EmailTemplatesListComponent_ng_container_17_Template, 8, 7, \"ng-container\", 14)(18, EmailTemplatesListComponent_ng_template_18_Template, 2, 2, \"ng-template\", 15);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"data\", ctx.serverSideRowData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(17, _c2, i0.ɵɵpureFunction0(16, _c1)))(\"sortable\", i0.ɵɵpureFunction0(19, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(20, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.page.pageNumber * ctx.page.size)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(21, _c5))(\"loading\", false);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n      }\n    },\n    dependencies: [i9.NgForOf, i9.NgIf, i10.NgControlStatus, i10.NgModel, i3.NgbTooltip, i11.GridComponent, i11.ToolbarTemplateDirective, i11.GridSpacerComponent, i11.ColumnComponent, i11.CellTemplateDirective, i11.NoRecordsTemplateDirective, i11.ContainsFilterOperatorComponent, i11.EndsWithFilterOperatorComponent, i11.EqualFilterOperatorComponent, i11.NotEqualFilterOperatorComponent, i11.StartsWithFilterOperatorComponent, i11.AfterFilterOperatorComponent, i11.AfterEqFilterOperatorComponent, i11.BeforeEqFilterOperatorComponent, i11.BeforeFilterOperatorComponent, i11.StringFilterMenuComponent, i11.FilterMenuTemplateDirective, i11.DateFilterMenuComponent, i12.TextBoxComponent, i13.ButtonComponent, i14.InlineSVGDirective, i15.DropDownListComponent, i9.DatePipe],\n    styles: [\".nav-line-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.nav-line-tabs[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #009ef7 !important;\\n  border-bottom: 2px solid #009ef7;\\n  font-weight: 600;\\n}\\n.nav-line-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #009ef7 !important;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n\\n\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #646367;\\n  box-shadow: 0 0 6px rgba(57, 58, 58, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: white;\\n  padding: 40px;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n  min-width: 200px;\\n}\\n\\n.grid-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 500;\\n}\\n.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  width: 300px;\\n}\\n\\n.grid-toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n\\n\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n\\n\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.template-statistics[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  padding: 15px;\\n}\\n.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n}\\n.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n\\n\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n}\\n\\n.card-body[_ngcontent-%COMP%]    > .d-flex.justify-content-between.align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  align-self: center;\\n  padding: 0.15rem 0.5rem;\\n  border-radius: 0.55rem;\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1;\\n  margin-top: 0.1rem;\\n}\\n.back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.badge-success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n\\n.badge-danger[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.badge-info[_ngcontent-%COMP%] {\\n  background-color: #d1ecf1;\\n  color: #0c5460;\\n}\\n\\n.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #e2e3e5;\\n  color: #383d41;\\n}\\n\\n\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n}\\n\\n\\n\\n.custom-no-records[_ngcontent-%COMP%] {\\n  padding: 40px;\\n  text-align: center;\\n}\\n.custom-no-records[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n}\\n\\n\\n\\n.bulk-actions[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 6px;\\n  padding: 10px;\\n  margin-bottom: 15px;\\n}\\n\\n\\n\\n.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 9999;\\n  background: #ffffff;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subject", "debounceTime", "distinctUntilChanged", "NavigationStart", "EmailTemplatesEditComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchData", "ɵɵresetView", "ɵɵlistener", "EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "onSearchChange", "EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_clear_1_listener", "clearSearch", "ɵɵelement", "EmailTemplatesListComponent_ng_template_15_Template_button_click_8_listener", "add", "EmailTemplatesListComponent_ng_template_15_Template_button_click_11_listener", "resetTable", "EmailTemplatesListComponent_ng_template_15_Template_button_click_13_listener", "toggleExpand", "EmailTemplatesListComponent_ng_template_15_Template_button_click_15_listener", "refreshGrid", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r4", "appliedFilters", "status", "EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener", "role", "EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_11_listener", "applyAdvancedFilters", "EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_14_listener", "clearAllFilters", "advancedFilterOptions", "roles", "ɵɵtemplate", "EmailTemplatesListComponent_ng_template_16_div_0_Template", "showAdvancedFilters", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener", "_r7", "dataItem_r6", "$implicit", "unlockUser", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener", "_r5", "edit", "templatePID", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template", "IsLocked", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c6", "fixedColumns", "includes", "_c7", "getHiddenField", "ɵɵtextInterpolate1", "dataItem_r8", "templateName", "column_r10", "filter_r9", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_2_Template", "dataItem_r11", "emailTo", "column_r13", "filter_r12", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_2_Template", "dataItem_r14", "emailSubject", "column_r16", "filter_r15", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_2_Template", "dataItem_r17", "category", "column_r19", "filter_r18", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_2_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_0_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_1_Template", "dataItem_r20", "userStatus", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r21", "_r21", "filter_r23", "column_r24", "column", "onStatusFilterChange", "filterOptions", "getFilterValue", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template", "ɵɵpipeBind2", "dataItem_r25", "lastUpdatedDate", "lastUpdatedByFullName", "column_r27", "filter_r26", "filterService_r28", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_2_Template", "ɵɵelementContainerStart", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_Template", "column_r29", "EmailTemplatesListComponent_ng_template_18_div_1_Template_button_click_5_listener", "_r30", "loadTable", "EmailTemplatesListComponent_ng_template_18_div_0_Template", "EmailTemplatesListComponent_ng_template_18_div_1_Template", "loading", "serverSideRowData", "length", "EmailTemplatesListComponent", "usersService", "cdr", "router", "route", "modalService", "AppService", "customLayoutUtilsService", "httpUtilService", "emailTemplateService", "kendoColumnService", "grid", "gridData", "IsListHasValue", "isLoading", "loginUser", "searchTerms", "searchSubscription", "filter", "logic", "filters", "gridFilter", "activeFilters", "text", "value", "categories", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "draggableColumns", "normalGrid", "expandedGrid", "gridColumnConfig", "field", "title", "width", "isFixed", "type", "order", "filterable", "columnsVisibility", "sort", "dir", "routerSubscription", "GRID_STATE_KEY", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "exportOptions", "selectedUsers", "isAllSelected", "userStatistics", "activeUsers", "inactiveUsers", "suspendedUsers", "lockedUsers", "totalUsers", "showBulkActions", "bulkActionStatus", "permissionArray", "constructor", "goBack", "navigate", "ngOnInit", "getLoggedInUser", "console", "log", "pipe", "subscribe", "events", "event", "saveGridState", "loadGridState", "loadRoles", "onPageLoad", "initializeColumnVisibilitySystem", "setTimeout", "loadColumnConfigFromDatabase", "map", "col", "loadCategories", "loadTemplateStatistics", "ngAfterViewInit", "onTabActivated", "ngOnDestroy", "unsubscribe", "complete", "loadTableWithKendoEndpoint", "loadingSubject", "next", "state", "take", "search", "loggedInUserId", "userId", "getEmailTemplatesForKendoGrid", "data", "<PERSON><PERSON><PERSON>", "responseData", "errors", "error", "handleEmptyResponse", "userData", "total", "Math", "ceil", "detectChanges", "_this", "_asyncToGenerator", "toggleAdvancedFilters", "queryParams", "pageSize", "sortOrder", "sortField", "getAllRoles", "content", "<PERSON><PERSON><PERSON>", "getDefaultPermissions", "permissions", "loadUserStatistics", "getUserStatistics", "statistics", "onSelectionChange", "selection", "selectedRows", "selectAllUsers", "deleteUser", "user", "confirm", "FirstName", "LastName", "deleteData", "response", "message", "showSuccess", "showError", "bulkUpdateUserStatus", "bulkUpdateData", "userIds", "firstName", "lastName", "unlockData", "key", "filterConfiguration", "paginate", "columnFilter", "searchText", "undefined", "trim", "push", "operator", "pageChange", "onSortChange", "incomingSort", "Array", "isArray", "filterChange", "flattenFilters", "predicate", "find", "f", "exists", "findIndex", "splice", "for<PERSON>ach", "savedState", "localStorage", "getItem", "JSON", "parse", "columns", "index", "indexOf", "orderIndex", "hidden", "pageName", "userID", "LoggedId", "createHideFields", "res", "clearFromLocalStorage", "refresh", "setItem", "stringify", "id", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "setupSearchSubscription", "gridContainer", "document", "querySelector", "classList", "toggle", "onExportClick", "item", "exportAllUsers", "exportSelectedUsers", "exportFilteredUsers", "warn", "exportParams", "format", "exportUsers", "exportData", "downloadExcel", "UserId", "searchTerm", "filename", "csv<PERSON><PERSON>nt", "convertToCSV", "blob", "Blob", "link", "createElement", "url", "URL", "createObjectURL", "setAttribute", "Date", "toISOString", "split", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "Object", "keys", "csvRows", "join", "row", "values", "header", "saveHead", "nonHiddenColumns", "hiddenColumns", "columnData", "draggableColumnsOrder", "saveToLocalStorage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadSavedColumnOrder", "savedOrder", "parsedOrder", "savedDraggableColumns", "a", "b", "missingColumns", "columnName", "onColumnReorder", "newIndex", "oldIndex", "reorderedColumns", "movedColumn", "updateColumnVisibility", "some", "indexExists", "getHideFields", "Data", "hideData", "includeInChooser", "loadFromLocalStorageFallback", "savedConfig", "getFromLocalStorage", "ɵɵdirectiveInject", "i1", "UserService", "ChangeDetectorRef", "i2", "Router", "ActivatedRoute", "i3", "NgbModal", "i4", "i5", "CustomLayoutUtilsService", "i6", "HttpUtilsService", "i7", "EmailTemplateService", "i8", "KendoColumnService", "selectors", "viewQuery", "EmailTemplatesListComponent_Query", "rf", "ctx", "EmailTemplatesListComponent_div_0_Template", "EmailTemplatesListComponent_Template_button_click_9_listener", "_r1", "EmailTemplatesListComponent_Template_kendo_grid_columnReorder_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_selectionChange_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_filterChange_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_pageChange_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_sortChange_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_columnVisibilityChange_13_listener", "EmailTemplatesListComponent_ng_template_15_Template", "EmailTemplatesListComponent_ng_template_16_Template", "EmailTemplatesListComponent_ng_container_17_Template", "EmailTemplatesListComponent_ng_template_18_Template", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\email-templates-list\\email-templates-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\email-templates-list\\email-templates-list.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ChangeDetector<PERSON>ef,\n  ViewChild,\n  AfterViewInit,\n} from '@angular/core';\nimport { SortDescriptor } from '@progress/kendo-data-query';\nimport {\n  FilterDescriptor,\n  CompositeFilterDescriptor,\n  process,\n} from '@progress/kendo-data-query';\nimport { State } from '@progress/kendo-data-query';\nimport {\n  Subject,\n  debounceTime,\n  distinctUntilChanged,\n  Subscription,\n} from 'rxjs';\nimport { Router, NavigationStart, ActivatedRoute } from '@angular/router';\nimport { saveAs } from '@progress/kendo-file-saver';\nimport { AppService } from 'src/app/modules/services/app.service';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\n\nimport { AddUserComponent } from '../add-user/user-add.component';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { UserService } from '../../services/user.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { KendoColumnService } from '../../services/kendo-column.service';\nimport { EmailTemplateService } from '../../services/email-template.service';\nimport { EmailTemplatesEditComponent } from '../email-templates-edit/email-templates-edit.component';\n\n// Type definitions\ninterface EmailTemplateData {\n  templatePID: number;\n  templateName: string;\n  emailSubject: string;\n  emailBody: string;\n  category: string;\n  templateStatus: string;\n  lastUpdatedDate: string;\n  lastUpdatedUserFullName: string;\n  createdDate: string;\n  createdUserFullName: string;\n}\n\n// Type for page configuration\ninterface PageConfig {\n  size: number;\n  pageNumber: number;\n  totalElements: number;\n  totalPages: number;\n  orderBy: string;\n  orderDir: string;\n}\ninterface UserData {\n  UserId: number;\n  FirstName: string;\n  LastName: string;\n  Email: string;\n  Status: string;\n  Title: string;\n  PhoneNo: string;\n  RoleName: string;\n  LastUpdatedDate: string;\n  CreatedDate: string;\n  IsEmailNotificationEnabled: boolean;\n  IsPasswordChanged: boolean;\n  IsLocked: boolean;\n  PharmacyId?: number;\n  MedicalCenterId?: number;\n  CreatedBy?: string;\n  LastUpdatedBy?: string;\n}\n\n// Type for page configuration\ninterface PageConfig {\n  size: number;\n  pageNumber: number;\n  totalElements: number;\n  totalPages: number;\n  orderBy: string;\n  orderDir: string;\n}\n\n@Component({\n  selector: 'app-email-templates-list',\n  templateUrl: './email-templates-list.component.html',\n  styleUrls: ['./email-templates-list.component.scss'],\n})\nexport class EmailTemplatesListComponent implements OnInit, OnDestroy, AfterViewInit{\n  @ViewChild('normalGrid') grid: any;\n\n  // Data\n  public serverSideRowData: any[] = [];\n  public gridData: any[] = [];\n  public IsListHasValue: boolean = false;\n\n  public loading: boolean = false;\n  public isLoading: boolean = false;\n\n  loginUser: any = {};\n\n  // Search\n  public searchData: string = '';\n  private searchTerms = new Subject<string>();\n  private searchSubscription: Subscription;\n\n  // Enhanced Filters for Kendo UI\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public activeFilters: Array<{\n    field: string;\n    operator: string;\n    value: any;\n  }> = [];\n\n  public filterOptions: Array<{ text: string; value: string | null }> = [\n    { text: 'All', value: null },\n    { text: 'Active', value: 'Active' },\n    { text: 'Inactive', value: 'Inactive' },\n  ];\n\n  // Advanced filter options\n  public advancedFilterOptions = {\n    status: [\n      { text: 'All', value: null },\n      { text: 'Active', value: 'Active' },\n      { text: 'Inactive', value: 'Inactive' },\n    ] as Array<{ text: string; value: string | null }>,\n    roles: [] as Array<{ text: string; value: string | null }>, // Will be populated from backend\n    categories: [] as Array<{ text: string; value: string | null }>, // Will be populated from backend\n  };\n\n  // Filter state\n  public showAdvancedFilters = false;\n  public appliedFilters: {\n    status?: string | null;\n    role?: string | null;\n  } = {};\n\n  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\n  public kendoHide: any;\n  public hiddenData: any = [];\n  public kendoColOrder: any = [];\n  public kendoInitColOrder: any = [];\n  public hiddenFields: any = [];\n\n  // Column configuration for the new system\n  public gridColumns: string[] = [];\n  public defaultColumns: string[] = [];\n  public fixedColumns: string[] = [];\n  public draggableColumns: string[] = [];\n  public normalGrid: any;\n  public expandedGrid: any;\n  public isExpanded = false;\n\n  // Enhanced Columns with Kendo UI features\n  public gridColumnConfig: Array<{\n    field: string;\n    title: string;\n    width: number;\n    isFixed: boolean;\n    type: string;\n    filterable?: boolean;\n    order: number;\n  }> = [\n    {\n      field: 'action',\n      title: 'Actions',\n      width: 80,\n      isFixed: true,\n      type: 'action',\n      order: 1,\n    },\n    {\n      field: 'templateName',\n      title: 'Template Name',\n      width: 150,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2,\n    },\n    // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\n    {\n      field: 'emailTo',\n      title: 'Email',\n      width: 250,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 4,\n    },\n    {\n      field: 'emailSubject',\n      title: 'Email Subject',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5,\n    },\n    {\n      field: 'emailBody',\n      title: 'Email Body',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6,\n    },\n    {\n      field: 'category',\n      title: 'Category',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 7,\n    },\n    {\n      field: 'templateStatus',\n      title: 'Template Status',\n      width: 100,\n      type: 'status',\n      isFixed: false,\n      filterable: true,\n      order: 8,\n    },\n    {\n      field: 'lastUpdatedDate',\n      title: 'Updated Date',\n      width: 160,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 9,\n    },\n  ];\n\n  // OLD SYSTEM - to be removed\n  public columnsVisibility: Record<string, boolean> = {};\n\n  // Old column configuration management removed - replaced with new system\n\n  // State\n  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n\n  // Router subscription for saving state on navigation\n  private routerSubscription: Subscription;\n\n  // Storage key for state persistence\n  private readonly GRID_STATE_KEY = 'form-templates-grid-state';\n\n  // Pagination\n  public page: PageConfig = {\n    size: 10,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc',\n  };\n  public skip: number = 0;\n\n  // Export options\n  public exportOptions: Array<{ text: string; value: string }> = [\n    { text: 'Export All', value: 'all' },\n    { text: 'Export Selected', value: 'selected' },\n    { text: 'Export Filtered', value: 'filtered' },\n  ];\n\n  // Selection state\n  public selectedUsers: any[] = [];\n  public isAllSelected: boolean = false;\n\n  // Statistics\n  public userStatistics: {\n    activeUsers: number;\n    inactiveUsers: number;\n    suspendedUsers: number;\n    lockedUsers: number;\n    totalUsers: number;\n  } = {\n    activeUsers: 0,\n    inactiveUsers: 0,\n    suspendedUsers: 0,\n    lockedUsers: 0,\n    totalUsers: 0,\n  };\n\n  // Bulk operations\n  public showBulkActions = false;\n  public bulkActionStatus: string = 'Active';\n\n  //add or edit default paramters\n  public permissionArray: any = [];\n\n  constructor(\n    private usersService: UserService,\n    private cdr: ChangeDetectorRef,\n    private router: Router,\n    private route: ActivatedRoute,\n    private modalService: NgbModal, // Provides modal functionality to display modals\n    public AppService: AppService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private httpUtilService: HttpUtilsService,\n    private emailTemplateService: EmailTemplateService,\n    private kendoColumnService: KendoColumnService\n  ) {}\n\n  goBack(): void {\n    this.router.navigate(['/setting/view']);\n  }\n\n  ngOnInit(): void {\n    this.loginUser = this.AppService.getLoggedInUser();\n    console.log('Login user loaded:', this.loginUser);\n\n    // Setup search with debounce\n    this.searchSubscription = this.searchTerms\n      .pipe(debounceTime(500), distinctUntilChanged())\n      .subscribe(() => {\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        this.page.pageNumber = 0;\n        this.skip = 0;\n        this.loadTable();\n      });\n\n    // Subscribe to router events to save state before navigation\n    this.routerSubscription = this.router.events.subscribe((event) => {\n      if (event instanceof NavigationStart) {\n        this.saveGridState();\n      }\n    });\n\n    // Load saved state if available\n    this.loadGridState();\n\n    // Load roles for advanced filters\n    this.loadRoles();\n\n    // Load user statistics\n    // this.loadUserStatistics();\n\n    // Initialize with default page load\n    this.onPageLoad();\n\n    // Initialize new column visibility system\n    this.initializeColumnVisibilitySystem();\n\n    // Load column configuration after a short delay to ensure loginUser is available\n    setTimeout(() => {\n      this.loadColumnConfigFromDatabase();\n    }, 100);\n  }\n\n  /**\n   * Initialize the new column visibility system\n   */\n  private initializeColumnVisibilitySystem(): void {\n    // Initialize default columns\n    this.defaultColumns = this.gridColumnConfig.map((col) => col.field);\n    this.gridColumns = [...this.defaultColumns];\n\n    // Set fixed columns (first 3 columns)\n    this.fixedColumns = ['action', 'FirstName', 'LastName'];\n\n    // Set draggable columns (all except fixed)\n    this.draggableColumns = this.defaultColumns.filter(\n      (col) => !this.fixedColumns.includes(col)\n    );\n\n    // Initialize normal and expanded grid references\n    this.normalGrid = this.grid;\n    this.expandedGrid = this.grid;\n    \n    // Load additional data after main data is loaded\n    setTimeout(() => {\n      this.loadCategories();\n      this.loadTemplateStatistics();\n    }, 100);\n  }\n\n  ngAfterViewInit(): void {\n    // Load the table after the view is initialized\n    // Small delay to ensure the grid is properly rendered\n    setTimeout(() => {\n      this.loadTable();\n    }, 200);\n  }\n\n  // Method to handle when the component becomes visible\n  onTabActivated(): void {\n    // Set loading state for tab activation\n    this.loading = true;\n    this.isLoading = true;\n    // Refresh the data when the tab is activated\n    this.loadTable();\n    this.loadTemplateStatistics();\n  }\n\n  // Method to handle initial page load\n  onPageLoad(): void {\n    // Initialize the component with default data\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n    this.filter = { logic: 'and', filters: [] };\n    this.searchData = '';\n\n    // Load the data\n    this.loadTable();\n  }\n\n  // Refresh grid data - only refresh the grid with latest API call\n  refreshGrid(): void {\n    // Set loading state to show full-screen loader\n    this.loading = true;\n    this.isLoading = true;\n\n    // Reset to first page and clear any applied filters\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.filter = { logic: 'and', filters: [] };\n    this.gridFilter = { logic: 'and', filters: [] };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n\n    // Clear search data\n    this.searchData = '';\n\n    // Load fresh data from API\n    this.loadTable();\n  }\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.routerSubscription) {\n      this.routerSubscription.unsubscribe();\n    }\n    if (this.searchSubscription) {\n      this.searchSubscription.unsubscribe();\n    }\n    this.searchTerms.complete();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId,\n    };\n\n    this.emailTemplateService.getEmailTemplatesForKendoGrid(state).subscribe({\n      next: (data: {\n        isFault?: boolean;\n        responseData?: {\n          data: any[];\n          total: number;\n          errors?: string[];\n          status?: number;\n        };\n        data?: any[];\n        total?: number;\n        errors?: string[];\n        status?: number;\n      }) => {\n        // Handle the new API response structure\n        if (\n          data.isFault ||\n          (data.responseData &&\n            data.responseData.errors &&\n            data.responseData.errors.length > 0)\n        ) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          this.handleEmptyResponse();\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const userData = responseData.data || [];\n          const total = responseData.total || 0;\n\n          this.IsListHasValue = userData.length !== 0;\n          this.serverSideRowData = userData;\n          this.gridData = this.serverSideRowData;\n          console.log('this.gridData', this.gridData);\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n        }\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      error: (error: unknown) => {\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.detectChanges();\n      },\n    });\n  }\n\n  // Enhanced loadTable method that can use either endpoint\n  async loadTable() {\n    // Use the new Kendo UI specific endpoint for better performance\n    this.loadTableWithKendoEndpoint();\n  }\n\n  private handleEmptyResponse(): void {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n    this.cdr.detectChanges();\n  }\n\n  // Enhanced search handling\n  clearSearch(): void {\n    if (this.searchData === '') {\n      this.searchTerms.next('');\n    }\n  }\n\n  onSearchChange(): void {\n    this.searchTerms.next(this.searchData || '');\n  }\n\n  // Clear all filters and search\n  clearAllFilters(): void {\n    this.searchData = '';\n    this.filter = { logic: 'and', filters: [] };\n    this.gridFilter = { logic: 'and', filters: [] };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.loadTable();\n  }\n\n  // Apply advanced filters\n  applyAdvancedFilters(): void {\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.loadTable();\n  }\n\n  // Toggle advanced filters panel\n  toggleAdvancedFilters(): void {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n\n  // Load roles for advanced filters\n  loadRoles(): void {\n    const queryParams: {\n      pageSize: number;\n      sortOrder: string;\n      sortField: string;\n      pageNumber: number;\n    } = {\n      pageSize: 1000,\n      sortOrder: 'ASC',\n      sortField: 'roleName',\n      pageNumber: 0,\n    };\n\n    this.usersService.getAllRoles(queryParams).subscribe({\n      next: (data: {\n        responseData?: {\n          content: Array<{ roleName: string }>;\n        };\n      }) => {\n        if (data && data.responseData && data.responseData.content) {\n          this.advancedFilterOptions.roles = [\n            { text: 'All Roles', value: null },\n            ...data.responseData.content.map((role: { roleName: string }) => ({\n              text: role.roleName,\n              value: role.roleName,\n            })),\n          ];\n        } else {\n          // Set default if no data\n          this.advancedFilterOptions.categories = [\n            { text: 'All Categories', value: null },\n          ];\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error loading roles:', error);\n        // Set default roles if loading fails\n        this.advancedFilterOptions.roles = [{ text: 'All Roles', value: null }];\n      },\n    });\n    this.usersService\n      .getDefaultPermissions({})\n      .subscribe((permissions: any) => {\n        this.permissionArray = permissions.responseData;\n      });\n  }\n\n  // Load user statistics\n  loadUserStatistics(): void {\n    this.usersService.getUserStatistics().subscribe({\n      next: (data: any) => {\n        if (data && data.statistics) {\n          this.userStatistics = data.statistics;\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error loading user statistics:', error);\n      },\n    });\n  }\n\n  // Selection handling\n  onSelectionChange(selection: any): void {\n    this.selectedUsers = selection.selectedRows || [];\n    this.isAllSelected =\n      this.selectedUsers.length === this.serverSideRowData.length;\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n\n  // Select all users\n  selectAllUsers(): void {\n    if (this.isAllSelected) {\n      this.selectedUsers = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedUsers = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n\n  // Delete user\n  deleteUser(user: any): void {\n    if (\n      confirm(\n        `Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`\n      )\n    ) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n\n      const deleteData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0,\n      };\n\n      this.usersService.deleteUser(deleteData).subscribe({\n        next: (response: any) => {\n          if (response && response.message) {\n            //alert(response.message);\n                            this.customLayoutUtilsService.showSuccess(response.message, '');\n\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: (error: unknown) => {\n          console.error('Error deleting user:', error);\n                            this.customLayoutUtilsService.showError('Error deleting user', '');\n          \n          //alert('Error deleting user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  // Bulk update user status\n  bulkUpdateUserStatus(): void {\n    if (this.selectedUsers.length === 0) {\n                            this.customLayoutUtilsService.showError('Please select users to update', '');\n      //alert('Please select users to update.');\n\n\n      return;\n    }\n\n    if (\n      confirm(\n        `Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`\n      )\n    ) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n\n      const bulkUpdateData = {\n        userIds: this.selectedUsers.map((user) => user.userId),\n        status: this.bulkActionStatus,\n        loggedInUserId: this.loginUser.userId || 0,\n      };\n\n      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\n        next: (response: any) => {\n          if (response && response.message) {\n            //alert(response.message);\n                            this.customLayoutUtilsService.showSuccess(response.message, '');\n\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n            this.selectedUsers = []; // Clear selection\n            this.showBulkActions = false;\n          }\n        },\n        error: (error: unknown) => {\n          console.error('Error updating users:', error);\n          //alert('Error updating users. Please try again.');\n                            this.customLayoutUtilsService.showError('Error updating users', '');\n\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  // Unlock user\n  unlockUser(user: any): void {\n    if (\n      confirm(\n        `Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`\n      )\n    ) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n\n      const unlockData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0,\n      };\n\n      this.usersService.unlockUser(unlockData).subscribe({\n        next: (response: any) => {\n          if (response && response.message) {\n            //alert(response.message);\n                            this.customLayoutUtilsService.showSuccess(response.message, '');\n\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: (error: unknown) => {\n          console.error('Error unlocking user:', error);\n                            this.customLayoutUtilsService.showSuccess('Error unlocking user. Please try again', '');\n\n          //alert('Error unlocking user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  onSearchKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter') {\n      this.searchTerms.next(this.searchData);\n    }\n  }\n\n  // Enhanced function to filter data from search and advanced filters\n  filterConfiguration(): {\n    paginate: boolean;\n    search: string;\n    columnFilter: Array<{\n      field: string;\n      operator: string;\n      value: any;\n    }>;\n  } {\n    let filter: {\n      paginate: boolean;\n      search: string;\n      columnFilter: Array<{\n        field: string;\n        operator: string;\n        value: any;\n      }>;\n    } = {\n      paginate: true,\n      search: '',\n      columnFilter: [],\n    };\n\n    // Handle search text\n    let searchText: string;\n    if (this.searchData === null || this.searchData === undefined) {\n      searchText = '';\n    } else {\n      searchText = this.searchData;\n    }\n    filter.search = searchText.trim();\n\n    // Handle Kendo UI grid filters\n    if (this.activeFilters && this.activeFilters.length > 0) {\n      filter.columnFilter = [...this.activeFilters];\n    }\n\n    // Add advanced filters\n    if (this.appliedFilters.status && this.appliedFilters.status !== null) {\n      filter.columnFilter.push({\n        field: 'userStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status,\n      });\n    }\n\n    if (this.appliedFilters.role && this.appliedFilters.role !== null) {\n      filter.columnFilter.push({\n        field: 'roleName',\n        operator: 'eq',\n        value: this.appliedFilters.role,\n      });\n    }\n\n    return filter;\n  }\n\n  // Grid event handlers\n  public pageChange(event: { skip: number; take: number }): void {\n    this.skip = event.skip;\n    this.page.pageNumber = event.skip / event.take;\n    this.page.size = event.take;\n    this.loadTable();\n  }\n\n  public onSortChange(sort: SortDescriptor[]): void {\n    console.log('Sort change triggered:', sort);\n    \n    // Handle empty sort array (normalize/unsort case)\n    const incomingSort = Array.isArray(sort) ? sort : [];\n    \n    if (incomingSort.length === 0) {\n      // Normalize/unsort case - return to default sorting\n      console.log('Normalize triggered - returning to default sort');\n      this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n      \n      // Reset the grid's sort state to default\n      if (this.grid) {\n        this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n      }\n    } else {\n      // Normal sorting case\n      this.sort = incomingSort;\n      this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = this.sort[0].dir || 'desc';\n    }\n\n    console.log('Final sort state:', this.sort);\n    console.log('Page order:', { orderBy: this.page.orderBy, orderDir: this.page.orderDir });\n    \n    this.loadTable();\n  }\n\n  public filterChange(filter: CompositeFilterDescriptor): void {\n    this.filter = filter;\n    this.gridFilter = filter;\n    this.activeFilters = this.flattenFilters(filter);\n    this.page.pageNumber = 0;\n    this.saveGridState();\n    // Set loading state for sorting\n    this.loading = true;\n    this.isLoading = true;\n    this.skip = 0;\n    this.loadTable();\n  }\n\n  // Old column visibility methods removed - replaced with new system\n\n  // Fix 2: More robust getFilterValue method\n  public getFilterValue(\n    filter: CompositeFilterDescriptor,\n    column: { field: string }\n  ): any {\n    if (!filter || !filter.filters || !column) {\n      return null;\n    }\n    const predicate = filter.filters.find(\n      (f: any) => f && 'field' in f && f.field === column.field\n    );\n    return predicate && 'value' in predicate ? predicate.value : null;\n  }\n\n  // Fix 3: More robust onStatusFilterChange method\n  public onStatusFilterChange(\n    value: string | null,\n    filter: CompositeFilterDescriptor,\n    column: { field: string }\n  ): void {\n    if (!filter || !filter.filters || !column) {\n      console.error('Invalid filter or column:', { filter, column });\n      return;\n    }\n\n    const exists = filter.filters.findIndex(\n      (f: any) => f && 'field' in f && f.field === column.field\n    );\n    if (exists > -1) {\n      filter.filters.splice(exists, 1);\n    }\n\n    if (value !== null) {\n      filter.filters.push({\n        field: column.field,\n        operator: 'eq',\n        value: value,\n      });\n    }\n\n    this.filterChange(filter);\n  }\n\n  // Fix 4: More robust flattenFilters method\n  private flattenFilters(filter: CompositeFilterDescriptor): Array<{\n    field: string;\n    operator: string;\n    value: any;\n  }> {\n    const filters: Array<{\n      field: string;\n      operator: string;\n      value: any;\n    }> = [];\n\n    if (!filter || !filter.filters) {\n      return filters;\n    }\n\n    filter.filters.forEach((f: any) => {\n      if (f && 'field' in f) {\n        // It's a FilterDescriptor\n        filters.push({\n          field: f.field,\n          operator: f.operator,\n          value: f.value,\n        });\n      } else if (f && 'filters' in f) {\n        // It's a CompositeFilterDescriptor\n        filters.push(...this.flattenFilters(f));\n      }\n    });\n\n    return filters;\n  }\n\n  // Fix 5: More robust loadGridState method\n  private loadGridState(): void {\n    try {\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n\n      if (!savedState) {\n        return;\n      }\n\n      const state: {\n        sort?: SortDescriptor[];\n        filter?: CompositeFilterDescriptor;\n        page?: {\n          size: number;\n          pageNumber: number;\n          totalElements: number;\n          totalPages: number;\n          orderBy: string;\n          orderDir: string;\n        };\n        skip?: number;\n        columnsVisibility?: Record<string, boolean>;\n        searchData?: string;\n        activeFilters?: Array<{\n          field: string;\n          operator: string;\n          value: any;\n        }>;\n        appliedFilters?: {\n          status?: string | null;\n          role?: string | null;\n        };\n        showAdvancedFilters?: boolean;\n      } = JSON.parse(savedState);\n\n      // Restore sort state\n      if (state && state.sort) {\n        this.sort = state.sort;\n        if (this.sort && this.sort.length > 0 && this.sort[0]) {\n          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n          this.page.orderDir = this.sort[0].dir || 'desc';\n        }\n      }\n\n      // Restore filter state\n      if (state && state.filter) {\n        this.filter = state.filter;\n        this.gridFilter = state.filter;\n        this.activeFilters = state.activeFilters || [];\n      }\n\n      // Restore pagination state\n      if (state && state.page) {\n        this.page = state.page;\n      }\n\n      if (state && state.skip !== undefined) {\n        this.skip = state.skip;\n      }\n\n      // Restore column visibility\n      if (state && state.columnsVisibility) {\n        this.columnsVisibility = state.columnsVisibility;\n      }\n\n      // Restore search state\n      if (state && state.searchData) {\n        this.searchData = state.searchData;\n      }\n\n      // Restore advanced filter states\n      if (state && state.appliedFilters) {\n        this.appliedFilters = state.appliedFilters;\n      }\n\n      if (state && state.showAdvancedFilters !== undefined) {\n        this.showAdvancedFilters = state.showAdvancedFilters;\n      }\n    } catch (error) {\n      console.error('Error loading grid state:', error);\n      // If there's an error, use default state\n    }\n  }\n\n  /**\n   * Reset the current state of column visibility and order in the grid to its original state.\n   */\n  resetTable(): void {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page and try again.', '');\n      return;\n    }\n\n    // Reset all grid state to default\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n    this.filter = { logic: 'and', filters: [] };\n    this.searchData = '';\n    this.appliedFilters = {};\n    this.showAdvancedFilters = false;\n\n    // Reset column visibility and order\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach((column: any) => {\n        const index = this.gridColumns.indexOf(column.field);\n        if (index !== -1) {\n          column.orderIndex = index;\n        }\n        // Reset column visibility - show all columns\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n    }\n\n    // Clear hidden columns\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.hiddenFields = [];\n\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = { logic: 'and', filters: [] };\n      \n      // Reset sorting\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n      \n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n\n    // Prepare reset data\n    const userData = {\n      pageName: 'EmailTemplates',\n      userID: this.loginUser.userId,\n      hiddenData: [],\n      kendoColOrder: [],\n      LoggedId: this.loginUser.userId\n    };\n\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n\n    // Save reset state to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: (res) => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Also clear from localStorage\n          this.kendoColumnService.clearFromLocalStorage('EmailTemplates');\n          this.customLayoutUtilsService.showSuccess(res.message || 'Column settings reset successfully.', '');\n        } else {\n          this.customLayoutUtilsService.showError(res.message || 'Failed to reset column settings.', '');\n        }\n        \n        // Trigger change detection and refresh grid\n        this.cdr.detectChanges();\n        \n        // Small delay to ensure the grid is updated\n        setTimeout(() => {\n          if (this.grid) {\n            this.grid.refresh();\n          }\n        }, 100);\n        \n        this.loadTable();\n      },\n      error: (error) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error resetting column settings:', error);\n\n        // Check if it's an authentication error\n        if (error.status === 401 || (error.error && error.error.status === 401)) {\n          this.customLayoutUtilsService.showError('Authentication failed. Please login again.', '');\n          // Optionally redirect to login page\n        } else {\n          this.customLayoutUtilsService.showError('Error resetting column settings. Please try again.', '');\n        }\n      }\n    });\n  }\n\n  // Grid state persistence methods\n  private saveGridState(): void {\n    const state: {\n      sort: SortDescriptor[];\n      filter: CompositeFilterDescriptor;\n      page: {\n        size: number;\n        pageNumber: number;\n        totalElements: number;\n        totalPages: number;\n        orderBy: string;\n        orderDir: string;\n      };\n      skip: number;\n      columnsVisibility: Record<string, boolean>;\n      searchData: string;\n      activeFilters: Array<{\n        field: string;\n        operator: string;\n        value: any;\n      }>;\n      appliedFilters: {\n        status?: string | null;\n        role?: string | null;\n      };\n      showAdvancedFilters: boolean;\n    } = {\n      sort: this.sort,\n      filter: this.filter,\n      page: this.page,\n      skip: this.skip,\n      columnsVisibility: this.columnsVisibility,\n      searchData: this.searchData,\n      activeFilters: this.activeFilters,\n      appliedFilters: this.appliedFilters,\n      showAdvancedFilters: this.showAdvancedFilters,\n    };\n\n    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n  }\n\n  // Function to add a new company (calls edit function with ID 0)\n  add() {\n    this.edit(0);\n  }\n\n  // Function to open the edit modal for adding/editing a company\n  edit(id: number) {\n    console.log('Line: 413', 'call edit function: ', id);\n    // Configuration options for the modal dialog\n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the AddCompaniesComponent\n    const modalRef = this.modalService.open(\n      EmailTemplatesEditComponent,\n      NgbModalOptions\n    );\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = id;\n    // modalRef.componentInstance.defaultPermissions = this.permissionArray;\n    // // Subscribe to the modal event when data is updated\n    // modalRef.componentInstance.passEntry.subscribe((receivedEntry: boolean) => {\n    //   if (receivedEntry === true) {\n    //     // Reload the table data after a successful update\n    //     this.loadTable();\n    //   }\n    // });\n  }\n\n  // Delete functionality removed\n\n  // Load categories for advanced filters\n  loadCategories(): void {\n    // Implementation for loading categories\n    console.log('Loading categories...');\n  }\n\n  // Load template statistics\n  loadTemplateStatistics(): void {\n    // Implementation for loading template statistics\n    console.log('Loading template statistics...');\n  }\n\n  // Setup search subscription\n  setupSearchSubscription(): void {\n    this.searchSubscription = this.searchTerms\n      .pipe(debounceTime(500), distinctUntilChanged())\n      .subscribe(() => {\n        this.page.pageNumber = 0;\n        this.skip = 0;\n        this.loadTable();\n      });\n  }\n  public toggleExpand(): void {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector(\n      '.grid-container'\n    ) as HTMLElement;\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n\n  // Enhanced export functionality\n  public onExportClick(event: { item: { value: string } }): void {\n    switch (event.item.value) {\n      case 'all':\n        this.exportAllUsers();\n        break;\n      case 'selected':\n        this.exportSelectedUsers();\n        break;\n      case 'filtered':\n        this.exportFilteredUsers();\n        break;\n      default:\n        console.warn('Unknown export option:', event.item.value);\n    }\n  }\n\n  private exportAllUsers(): void {\n    const exportParams = {\n      filters: {},\n      format: 'excel',\n    };\n\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: (response: any) => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'All_Users');\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error exporting users:', error);\n        //alert('Error exporting users. Please try again.');\n      },\n    });\n  }\n\n  private exportSelectedUsers(): void {\n    if (this.selectedUsers.length === 0) {\n      //alert('Please select users to export.');\n      return;\n    }\n\n    const exportParams = {\n      filters: {\n        userIds: this.selectedUsers.map((user) => user.UserId),\n      },\n      format: 'excel',\n    };\n\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: (response: any) => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Selected_Users');\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error exporting selected users:', error);\n        //alert('Error exporting selected users. Please try again.');\n      },\n    });\n  }\n\n  private exportFilteredUsers(): void {\n    const exportParams = {\n      filters: {\n        status: this.appliedFilters.status,\n        role: this.appliedFilters.role,\n        searchTerm: this.searchData,\n      },\n      format: 'excel',\n    };\n\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: (response: any) => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Filtered_Users');\n        }\n      },\n      error: (error: unknown) => {\n        console.error('Error exporting filtered users:', error);\n        //alert('Error exporting filtered users. Please try again.');\n      },\n    });\n  }\n\n  private downloadExcel(data: any[], filename: string): void {\n    // This would typically use a library like xlsx or similar\n    // For now, we'll create a simple CSV download\n    const csvContent = this.convertToCSV(data);\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute(\n      'download',\n      `${filename}_${new Date().toISOString().split('T')[0]}.csv`\n    );\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n\n  private convertToCSV(data: any[]): string {\n    if (data.length === 0) return '';\n\n    const headers = Object.keys(data[0]);\n    const csvRows = [headers.join(',')];\n\n    for (const row of data) {\n      const values = headers.map((header) => {\n        const value = row[header];\n        return typeof value === 'string' && value.includes(',')\n          ? `\"${value}\"`\n          : value;\n      });\n      csvRows.push(values.join(','));\n    }\n\n    return csvRows.join('\\n');\n  }\n\n  // NEW COLUMN VISIBILITY SYSTEM METHODS\n\n  /**\n   * Saves the current state of column visibility and order in the grid.\n   * This function categorizes columns into visible and hidden columns, records their titles,\n   * fields, and visibility status, and also captures the order of draggable columns.\n   * After gathering the necessary data, it sends this information to the backend for saving.\n   */\n  saveHead(): void {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError(\n        'User not logged in. Please refresh the page.',\n        ''\n      );\n      return;\n    }\n\n    const nonHiddenColumns: any[] = [];\n    const hiddenColumns: any[] = [];\n\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach((column: any) => {\n        if (!column.hidden) {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden,\n          };\n          nonHiddenColumns.push(columnData);\n        } else {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden,\n          };\n          hiddenColumns.push(columnData);\n        }\n      });\n    }\n\n    const draggableColumnsOrder = this.gridColumns\n      .filter((col) => !this.fixedColumns.includes(col))\n      .map((field, index) => ({\n        field,\n        orderIndex: index,\n      }));\n\n    // Prepare data for backend\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: hiddenColumns,\n      kendoColOrder: draggableColumnsOrder,\n      LoggedId: this.loginUser.userId,\n    };\n\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n\n    // Save to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: (res) => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map((col: any) => col.field);\n\n          // Also save to localStorage as backup\n          this.kendoColumnService.saveToLocalStorage(userData);\n\n          this.customLayoutUtilsService.showSuccess(\n            res.message || 'Column settings saved successfully.',\n            ''\n          );\n        } else {\n          this.customLayoutUtilsService.showError(\n            res.message || 'Failed to save column settings.',\n            ''\n          );\n        }\n        this.cdr.markForCheck();\n      },\n      error: (error) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error saving column settings:', error);\n\n        // Fallback to localStorage on error\n        this.kendoColumnService.saveToLocalStorage(userData);\n\n        // Update local state\n        this.hiddenData = hiddenColumns;\n        this.kendoColOrder = draggableColumnsOrder;\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\n\n        this.customLayoutUtilsService.showError(\n          'Failed to save to server. Settings saved locally.',\n          ''\n        );\n        this.cdr.markForCheck();\n      },\n    });\n  }\n\n\n  /**\n   * Loads and applies the saved column order from the user preferences or configuration.\n   * This function updates the grid column order, ensuring the fixed columns remain in place\n   * and the draggable columns are ordered according to the saved preferences.\n   */\n  loadSavedColumnOrder(kendoColOrder: any): void {\n    try {\n      const savedOrder = kendoColOrder;\n      if (savedOrder) {\n        const parsedOrder = savedOrder;\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n          // Get only the draggable columns from saved order\n          const savedDraggableColumns = parsedOrder\n            .sort((a, b) => a.orderIndex - b.orderIndex)\n            .map((col) => col.field)\n            .filter((field) => !this.fixedColumns.includes(field));\n\n          // Add any missing draggable columns at the end\n          const missingColumns = this.draggableColumns.filter(\n            (col) => !savedDraggableColumns.includes(col)\n          );\n\n          // Combine fixed columns with saved draggable columns\n          this.gridColumns = [\n            ...this.fixedColumns,\n            ...savedDraggableColumns,\n            ...missingColumns,\n          ];\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } else {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    } catch (error) {\n      this.gridColumns = [...this.defaultColumns];\n    }\n  }\n\n  /**\n   * Checks if a given column is marked as hidden.\n   * This function searches the `hiddenFields` array to determine if the column should be hidden.\n   */\n  getHiddenField(columnName: any): boolean {\n    return this.hiddenFields.indexOf(columnName) > -1;\n  }\n\n  /**\n   * Handles the column reordering event triggered when a column is moved by the user.\n   * The function checks if the column being moved is in the fixed columns and prevents reordering\n   * of fixed columns.\n   */\n  onColumnReorder(event: any): void {\n    const { columns, newIndex, oldIndex } = event;\n\n    // Prevent reordering of fixed columns\n    if (\n      this.fixedColumns.includes(columns[oldIndex].field) ||\n      this.fixedColumns.includes(columns[newIndex].field)\n    ) {\n      return;\n    }\n\n    // Update the gridColumns array\n    const reorderedColumns = [...this.gridColumns];\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n    reorderedColumns.splice(newIndex, 0, movedColumn);\n\n    this.gridColumns = reorderedColumns;\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Handles column visibility changes from the Kendo Grid.\n   * Updates the local state when columns are shown or hidden.\n   */\n  updateColumnVisibility(event: any): void {\n    if (this.isExpanded === false) {\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach((column: any) => {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden,\n          };\n          if (column.hidden) {\n            const exists = this.hiddenData.some(\n              (item: any) =>\n                item.field === columnData.field && item.hidden === true\n            );\n            if (!exists) {\n              this.hiddenData.push(columnData);\n            }\n          } else {\n            let indexExists = this.hiddenData.findIndex(\n              (item: any) =>\n                item.field === columnData.field && item.hidden === true\n            );\n            if (indexExists !== -1) {\n              this.hiddenData.splice(indexExists, 1);\n            }\n          }\n        });\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\n        this.cdr.markForCheck();\n      }\n    }\n  }\n\n  /**\n   * Loads the saved column configuration from the backend or localStorage as fallback.\n   * This method is called during component initialization to restore user preferences.\n   */\n  private loadColumnConfigFromDatabase(): void {\n    try {\n      // First try to load from backend\n      if (this.loginUser && this.loginUser.userId) {\n        this.kendoColumnService\n          .getHideFields({\n            pageName: 'Users',\n            userID: this.loginUser.userId,\n          })\n          .subscribe({\n            next: (res) => {\n              if (!res.isFault && res.Data) {\n                this.kendoHide = res.Data;\n                this.hiddenData = res.Data.hideData\n                  ? JSON.parse(res.Data.hideData)\n                  : [];\n                this.kendoInitColOrder = res.Data.kendoColOrder\n                  ? JSON.parse(res.Data.kendoColOrder)\n                  : [];\n                this.hiddenFields = this.hiddenData.map(\n                  (col: any) => col.field\n                );\n\n                // Update grid columns based on the hidden fields\n                if (this.grid && this.grid.columns) {\n                  this.grid.columns.forEach((column: any) => {\n                    if (\n                      this.hiddenData.some(\n                        (item: any) =>\n                          item.title === column.title && item.hidden\n                      )\n                    ) {\n                      column.includeInChooser = true;\n                      column.hidden = true;\n                    } else {\n                      column.hidden = false;\n                    }\n                  });\n                }\n\n                // Load saved column order and update grid\n                this.loadSavedColumnOrder(this.kendoInitColOrder);\n\n                // Also save to localStorage as backup\n                this.kendoColumnService.saveToLocalStorage({\n                  pageName: 'Users',\n                  userID: this.loginUser.userId,\n                  hiddenData: this.hiddenData,\n                  kendoColOrder: this.kendoInitColOrder,\n                });\n              }\n            },\n            error: (error) => {\n              console.error(\n                'Error loading from backend, falling back to localStorage:',\n                error\n              );\n              this.loadFromLocalStorageFallback();\n            },\n          });\n      } else {\n        // Fallback to localStorage if no user ID\n        this.loadFromLocalStorageFallback();\n      }\n    } catch (error) {\n      console.error('Error loading column configuration:', error);\n      this.loadFromLocalStorageFallback();\n    }\n  }\n\n  /**\n   * Fallback method to load column configuration from localStorage\n   */\n  private loadFromLocalStorageFallback(): void {\n    try {\n      const savedConfig = this.kendoColumnService.getFromLocalStorage(\n        'Users',\n        this.loginUser?.UserId || 0\n      );\n      if (savedConfig) {\n        this.kendoHide = savedConfig;\n        this.hiddenData = savedConfig.hiddenData || [];\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\n\n        // Update grid columns based on the hidden fields\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach((column: any) => {\n            if (\n              this.hiddenData.some(\n                (item: any) => item.title === column.title && item.hidden\n              )\n            ) {\n              column.includeInChooser = true;\n              column.hidden = true;\n            } else {\n              column.hidden = false;\n            }\n          });\n        }\n\n        // Load saved column order and update grid\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\n      }\n    } catch (error) {\n      console.error('Error loading from localStorage fallback:', error);\n    }\n  }\n}\n", "<!-- Full Screen Loading Overlay -->\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"spinner-border text-primary spinner-md\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<!-- Email Templates Card -->\n<div class=\"card mb-5 mb-xl-5\">\n  <div class=\"card-body pb-0 pt-0\">\n    <div class=\"d-flex justify-content-between align-items-center\">\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-3x border-transparent fs-5 fw-bold flex-nowrap\">\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer active\">\n            <PERSON>ail Templates\n          </a>\n        </li>\n      </ul>\n      \n      <!-- Back Button -->\n      <div class=\"d-flex align-items-center\">\n        <button \n          type=\"button\" \n          class=\"btn btn-sm btn-light-primary d-flex align-items-center back-button\"\n          (click)=\"goBack()\"\n          title=\"Go back to Settings Dashboard\">\n          <i class=\"fa fa-arrow-left me-2\"></i>\n          Back\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"grid-container\">\n  <kendo-grid\n    #normalGrid\n    [data]=\"serverSideRowData\"\n    [pageSize]=\"page.size\"\n    [sort]=\"sort\"\n    [pageable]=\"{\n      pageSizes: [10, 15, 20, 50, 100],\n      previousNext: true,\n      info: true,\n      type: 'numeric',\n      buttonCount: 5\n    }\"\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\n    [groupable]=\"false\"\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\n    (columnReorder)=\"onColumnReorder($event)\"\n    (selectionChange)=\"onSelectionChange($event)\"\n    [reorderable]=\"true\"\n    style=\"width: auto; overflow-x: auto\"\n    [resizable]=\"false\"\n    [height]=\"720\"\n    [skip]=\"page.pageNumber * page.size\"\n    [filter]=\"filter\"\n    [columnMenu]=\"{ filter: true }\"\n    (filterChange)=\"filterChange($event)\"\n    (pageChange)=\"pageChange($event)\"\n    (sortChange)=\"onSortChange($event)\"\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\n    [loading]=\"false\"\n  >\n    <ng-template kendoGridToolbarTemplate>\n      <!-- Search Section -->\n      <div class=\"d-flex align-items-center me-3 search-section\">\n        <kendo-textbox\n          [style.width.px]=\"500\"\n          placeholder=\"Search...\"\n          [(ngModel)]=\"searchData\"\n          [clearButton]=\"true\"\n          (keydown)=\"onSearchKeyDown($event)\"\n          (ngModelChange)=\"onSearchChange()\"\n          (clear)=\"clearSearch()\"\n        ></kendo-textbox>\n        <!-- <button\n          kendoButton\n          [disabled]=\"!searchData || searchData.trim() === ''\"\n          (click)=\"loadTable()\"\n          class=\"ms-2\"\n        >\n          <i class=\"fas fa-search\"></i> Search\n        </button> -->\n      </div>\n\n      <kendo-grid-spacer></kendo-grid-spacer>\n\n      <!-- Total Count - Repositioned to the right -->\n      <div class=\"d-flex align-items-center me-3\">\n        <span class=\"text-muted\">Total: </span>\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\n      </div>\n\n      <!-- Action Buttons -->\n      <button type=\"button\" class=\"btn btn-primary btn-sm me-2\" (click)=\"add()\">\n        <span\n          [inlineSVG]=\"'./assets/media/icons/duotune/arrows/arr075.svg'\"\n          class=\"svg-icon svg-icon-3\"\n        ></span>\n        Add\n      </button>\n\n      <!-- Reset Button -->\n      <button\n        type=\"button\"\n        class=\"btn btn-warning btn-sm me-2\"\n        (click)=\"resetTable()\"\n        title=\"Reset to Default\"\n      >\n        <i class=\"fas fa-undo\"></i>\n      </button>\n\n      <button\n        type=\"button\"\n        class=\"btn btn-secondary btn-sm me-2\"\n        (click)=\"toggleExpand()\"\n        title=\"Toggle Grid Expansion\"\n      >\n        <i\n          class=\"fas\"\n          [class.fa-expand]=\"!isExpanded\"\n          [class.fa-compress]=\"isExpanded\"\n        ></i>\n      </button>\n\n      <!-- <kendo-dropdownbutton\n        text=\"Export Excel\"\n        iconClass=\"fas fa-file-excel\"\n        [data]=\"exportOptions\"\n        class=\"custom-dropdown\"\n        (itemClick)=\"onExportClick($event)\"\n        title=\"Export\"\n      >\n      </kendo-dropdownbutton> -->\n\n      <!-- Save Column Settings Button -->\n      <!-- <button\n        type=\"button\"\n        class=\"btn btn-success btn-sm me-2\"\n        (click)=\"saveHead()\"\n        title=\"Save Column Settings\"\n      >\n        <i class=\"fas fa-save\"></i>\n      </button> -->\n\n      <!-- Reset Button -->\n      <!-- <button\n        type=\"button\"\n        class=\"btn btn-warning btn-sm me-2\"\n        (click)=\"resetTable()\"\n        title=\"Reset\"\n      >\n        <i class=\"fas fa-undo\"></i>\n      </button> -->\n\n      <!-- Refresh Button -->\n      <button\n        type=\"button\"\n        class=\"btn btn-info btn-sm me-2\"\n        (click)=\"refreshGrid()\"\n        title=\"Refresh\"\n      >\n        <i class=\"fas fa-sync-alt\"></i>\n      </button>\n    </ng-template>\n\n    <!-- Advanced Filters Panel -->\n    <ng-template kendoGridToolbarTemplate>\n      <div\n        *ngIf=\"showAdvancedFilters\"\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\n      >\n        <div class=\"row\">\n          <div class=\"col-md-3\">\n            <label class=\"form-label\">Status</label>\n            <kendo-dropdownlist\n              [data]=\"advancedFilterOptions.status\"\n              [(ngModel)]=\"appliedFilters.status\"\n              textField=\"text\"\n              valueField=\"value\"\n              placeholder=\"Select Status\"\n            >\n            </kendo-dropdownlist>\n          </div>\n          <div class=\"col-md-3\">\n            <label class=\"form-label\">Role</label>\n            <kendo-dropdownlist\n              [data]=\"advancedFilterOptions.roles\"\n              [(ngModel)]=\"appliedFilters.role\"\n              textField=\"text\"\n              valueField=\"value\"\n              placeholder=\"Select Role\"\n            >\n            </kendo-dropdownlist>\n          </div>\n          <div class=\"col-md-3 d-flex align-items-end\">\n            <button\n              kendoButton\n              (click)=\"applyAdvancedFilters()\"\n              class=\"btn-primary me-2\"\n            >\n              <i class=\"fas fa-check\"></i> Apply Filters\n            </button>\n            <button\n              kendoButton\n              (click)=\"clearAllFilters()\"\n              class=\"btn-secondary\"\n            >\n              <i class=\"fas fa-times\"></i> Clear\n            </button>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n\n    <ng-container *ngFor=\"let column of gridColumns\">\n      <!-- Action Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'action'\"\n        title=\"Actions\"\n        [width]=\"125\"\n        [sticky]=\"true\"\n        [reorderable]=\"!fixedColumns.includes('action')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [includeInChooser]=\"false\"\n        [columnMenu]=\"false\"\n        [style]=\"{ 'background-color': '#efefef !important' }\"\n        [hidden]=\"getHiddenField('action')\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <a\n            title=\"Edit\"\n            class=\"btn btn-icon btn-sm\"\n            (click)=\"edit(dataItem.templatePID)\"\n          >\n            <span\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen055.svg'\"\n              class=\"svg-icon svg-icon-3 svg-icon-primary\"\n            >\n            </span>\n          </a>\n          <!-- Delete button removed -->\n          <a\n            *ngIf=\"dataItem.IsLocked\"\n            title=\"Unlock\"\n            class=\"btn btn-icon btn-sm\"\n            (click)=\"unlockUser(dataItem)\"\n          >\n            <span\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\n              class=\"svg-icon svg-icon-3 svg-icon-warning\"\n            >\n            </span>\n          </a>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- First Name Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'templateName'\"\n        field=\"templateName\"\n        title=\"Name\"\n        [width]=\"150\"\n        [sticky]=\"true\"\n        [reorderable]=\"!fixedColumns.includes('templateName')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [includeInChooser]=\"false\"\n        [hidden]=\"getHiddenField('templateName')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <div>\n            <span class=\"fw-bolder cursor-pointer\">\n              {{ dataItem.templateName }}\n            </span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"true\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Last Name Column -->\n      <!-- <kendo-grid-column *ngIf=\"column === 'lastName'\"\n        field=\"LastName\"\n        title=\"Last Name\"\n        [width]=\"150\"\n        [sticky]=\"true\"\n        [reorderable]=\"!fixedColumns.includes('LastName')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3','font-weight':'600' }\"\n        [includeInChooser]=\"false\"\n        [hidden]=\"getHiddenField('LastName')\"\n        [filterable]=\"true\">\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <div>\n            <span class=\"fw-bolder cursor-pointer\">\n              {{ dataItem.LastName }}\n            </span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu [column]=\"column\" [filter]=\"filter\" [extra]=\"true\">\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column> -->\n\n      <!-- Email Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'emailTo'\"\n        field=\"emailTo\"\n        title=\"Email\"\n        [width]=\"250\"\n        [sticky]=\"false\"\n        [reorderable]=\"!fixedColumns.includes('email')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('emailTo')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <div>\n            <span class=\"fw-bolder cursor-pointer\">\n              {{ dataItem.emailTo }}\n            </span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"true\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Title Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'emailSubject'\"\n        field=\"emailSubject\"\n        title=\"Email Subject\"\n        [width]=\"120\"\n        [sticky]=\"false\"\n        [reorderable]=\"!fixedColumns.includes('title')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('title')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <div>\n            <span class=\"fw-bolder cursor-pointer\">\n              {{ dataItem.emailSubject }}\n            </span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"true\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Phone Column -->\n      <!-- <kendo-grid-column\n        *ngIf=\"column === 'emailBody'\"\n        field=\"emailBody\"\n        title=\"Phone\"\n        [width]=\"120\"\n        [sticky]=\"false\"\n        [reorderable]=\"!fixedColumns.includes('phoneNo')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('phoneNo')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <div>\n            <span class=\"fw-bolder cursor-pointer\">\n              {{ dataItem.emailBody }}\n            </span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"true\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column> -->\n\n      <!-- Role Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'category'\"\n        field=\"category\"\n        title=\"category\"\n        [width]=\"120\"\n        [sticky]=\"false\"\n        [reorderable]=\"!fixedColumns.includes('roleName')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('roleName')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <div>\n            <span class=\"fw-bolder cursor-pointer\">\n              {{ dataItem.category }}\n            </span>\n          </div>\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-grid-string-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            [extra]=\"true\"\n          >\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\n          </kendo-grid-string-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Status Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'userStatus'\"\n        field=\"userStatus\"\n        title=\"Status\"\n        [width]=\"100\"\n        [sticky]=\"false\"\n        [reorderable]=\"!fixedColumns.includes('userStatus')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        [hidden]=\"getHiddenField('userStatus')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <span\n            *ngIf=\"dataItem.userStatus === 'Active'\"\n            ngbTooltip=\"Active\"\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\n            class=\"svg-icon svg-icon-3 svg-icon-success\"\n            style=\"margin-left: 1.5rem\"\n          >\n          </span>\n          <span\n            *ngIf=\"dataItem.userStatus === 'Inactive'\"\n            ngbTooltip=\"Inactive\"\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen040.svg'\"\n            class=\"svg-icon svg-icon-3 svg-icon-danger text-danger\"\n            style=\"margin-left: 1.5rem\"\n          >\n          </span>\n        </ng-template>\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\n          <kendo-dropdownlist\n            [data]=\"filterOptions\"\n            [value]=\"getFilterValue(filter, column)\"\n            (valueChange)=\"onStatusFilterChange($event, filter, column)\"\n            textField=\"text\"\n            valueField=\"value\"\n          >\n          </kendo-dropdownlist>\n        </ng-template>\n      </kendo-grid-column>\n\n      <!-- Updated Date Column -->\n      <kendo-grid-column\n        *ngIf=\"column === 'lastUpdatedDate'\"\n        field=\"lastUpdatedDate\"\n        title=\"Updated Date\"\n        [width]=\"160\"\n        [sticky]=\"false\"\n        [reorderable]=\"!fixedColumns.includes('lastUpdatedDate')\"\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\n        filter=\"date\"\n        format=\"MM/dd/yyyy\"\n        [maxResizableWidth]=\"240\"\n        [hidden]=\"getHiddenField('lastUpdatedDate')\"\n        [filterable]=\"true\"\n      >\n        <ng-template kendoGridCellTemplate let-dataItem>\n          <span class=\"text-gray-600 fs-1r\">{{\n            dataItem.lastUpdatedDate | date : \"MM/dd/yyyy hh:mm a\"\n          }}</span>\n          <br /><span class=\"text-gray-600 fs-1r\">{{\n            dataItem.lastUpdatedByFullName\n          }}</span>\n        </ng-template>\n        <ng-template\n          kendoGridFilterMenuTemplate\n          let-filter\n          let-column=\"column\"\n          let-filterService=\"filterService\"\n        >\n          <kendo-grid-date-filter-menu\n            [column]=\"column\"\n            [filter]=\"filter\"\n            operator=\"eq\"\n            [filterService]=\"filterService\"\n          >\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\n            <kendo-filter-before-operator></kendo-filter-before-operator>\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\n            <kendo-filter-after-operator></kendo-filter-after-operator>\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\n          </kendo-grid-date-filter-menu>\n        </ng-template>\n      </kendo-grid-column>\n    </ng-container>\n\n    <ng-template kendoGridNoRecordsTemplate>\n      <div class=\"custom-no-records\" *ngIf=\"loading === true\">\n        <div class=\"d-flex align-items-center justify-content-center\">\n          <div class=\"spinner-border text-primary me-3\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n          <span class=\"text-muted\">Loading users... Please wait...</span>\n        </div>\n      </div>\n      <div\n        class=\"custom-no-records\"\n        *ngIf=\"loading === false && serverSideRowData.length === 0\"\n      >\n        <div class=\"text-center\">\n          <i class=\"fas fa-users text-muted mb-2\" style=\"font-size: 2rem\"></i>\n          <p class=\"text-muted\">No users found</p>\n          <button kendoButton (click)=\"loadTable()\" class=\"btn-primary\">\n            <i class=\"fas fa-refresh me-2\"></i>Refresh\n          </button>\n        </div>\n      </div>\n    </ng-template>\n  </kendo-grid>\n  </div>\n</div>\n"], "mappings": ";AAeA,SACEA,OAAO,EACPC,YAAY,EACZC,oBAAoB,QAEf,MAAM;AACb,SAAiBC,eAAe,QAAwB,iBAAiB;AAWzE,SAASC,2BAA2B,QAAQ,wDAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC5B9FC,EAHN,CAAAC,cAAA,cAAqE,cACtC,cACuC,eAClC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IA8DEH,EADF,CAAAC,cAAA,cAA2D,wBASxD;IALCD,EAAA,CAAAI,gBAAA,2BAAAC,2FAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,UAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,UAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAIxBN,EAFA,CAAAc,UAAA,qBAAAC,qFAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWJ,MAAA,CAAAO,eAAA,CAAAV,MAAA,CAAuB;IAAA,EAAC,2BAAAD,2FAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAClBJ,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC,mBAAAC,mFAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CACzBJ,MAAA,CAAAU,WAAA,EAAa;IAAA,EAAC;IAU3BnB,EATG,CAAAG,YAAA,EAAgB,EASb;IAENH,EAAA,CAAAoB,SAAA,wBAAuC;IAIrCpB,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAA0E;IAAhBD,EAAA,CAAAc,UAAA,mBAAAO,4EAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAa,GAAA,EAAK;IAAA,EAAC;IACvEtB,EAAA,CAAAoB,SAAA,eAGQ;IACRpB,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAS,6EAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAe,UAAA,EAAY;IAAA,EAAC;IAGtBxB,EAAA,CAAAoB,SAAA,aAA2B;IAC7BpB,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAW,6EAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAiB,YAAA,EAAc;IAAA,EAAC;IAGxB1B,EAAA,CAAAoB,SAAA,aAIK;IACPpB,EAAA,CAAAG,YAAA,EAAS;IAiCTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAa,6EAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmB,WAAA,EAAa;IAAA,EAAC;IAGvB5B,EAAA,CAAAoB,SAAA,aAA+B;IACjCpB,EAAA,CAAAG,YAAA,EAAS;;;;IAhGLH,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAA8B,WAAA,oBAAsB;IAEtB9B,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAG,UAAA,CAAwB;IACxBZ,EAAA,CAAAgC,UAAA,qBAAoB;IAoBKhC,EAAA,CAAA6B,SAAA,GAA6B;IAA7B7B,EAAA,CAAAiC,iBAAA,CAAAxB,MAAA,CAAAyB,IAAA,CAAAC,aAAA,MAA6B;IAMtDnC,EAAA,CAAA6B,SAAA,GAA8D;IAA9D7B,EAAA,CAAAgC,UAAA,+DAA8D;IAwB9DhC,EAAA,CAAA6B,SAAA,GAA+B;IAC/B7B,EADA,CAAAoC,WAAA,eAAA3B,MAAA,CAAA4B,UAAA,CAA+B,gBAAA5B,MAAA,CAAA4B,UAAA,CACC;;;;;;IAqD9BrC,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAkC,sGAAAhC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA+B,cAAA,CAAAC,MAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAA+B,cAAA,CAAAC,MAAA,GAAAnC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAMvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAAsB,gBACM;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAsC,sGAAApC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA+B,cAAA,CAAAG,IAAA,EAAArC,MAAA,MAAAG,MAAA,CAAA+B,cAAA,CAAAG,IAAA,GAAArC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAiC;IAMrCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,eAA6C,kBAK1C;IAFCD,EAAA,CAAAc,UAAA,mBAAA8B,mFAAA;MAAA5C,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAoC,oBAAA,EAAsB;IAAA,EAAC;IAGhC7C,EAAA,CAAAoB,SAAA,aAA4B;IAACpB,EAAA,CAAAE,MAAA,uBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAc,UAAA,mBAAAgC,mFAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAsC,eAAA,EAAiB;IAAA,EAAC;IAG3B/C,EAAA,CAAAoB,SAAA,aAA4B;IAACpB,EAAA,CAAAE,MAAA,eAC/B;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IApCEH,EAAA,CAAA6B,SAAA,GAAqC;IAArC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAuC,qBAAA,CAAAP,MAAA,CAAqC;IACrCzC,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAA+B,cAAA,CAAAC,MAAA,CAAmC;IAUnCzC,EAAA,CAAA6B,SAAA,GAAoC;IAApC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAuC,qBAAA,CAAAC,KAAA,CAAoC;IACpCjD,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAA+B,cAAA,CAAAG,IAAA,CAAiC;;;;;IApBzC3C,EAAA,CAAAkD,UAAA,IAAAC,yDAAA,mBAGC;;;;IAFEnD,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA2C,mBAAA,CAAyB;;;;;;IAyExBpD,EAAA,CAAAC,cAAA,YAKC;IADCD,EAAA,CAAAc,UAAA,mBAAAuC,8GAAA;MAAArD,EAAA,CAAAO,aAAA,CAAA+C,GAAA;MAAA,MAAAC,WAAA,GAAAvD,EAAA,CAAAU,aAAA,GAAA8C,SAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgD,UAAA,CAAAF,WAAA,CAAoB;IAAA,EAAC;IAE9BvD,EAAA,CAAAoB,SAAA,eAIO;IACTpB,EAAA,CAAAG,YAAA,EAAI;;;IAJAH,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;;;;;;IAnBnEhC,EAAA,CAAAC,cAAA,YAIC;IADCD,EAAA,CAAAc,UAAA,mBAAA4C,0GAAA;MAAA,MAAAH,WAAA,GAAAvD,EAAA,CAAAO,aAAA,CAAAoD,GAAA,EAAAH,SAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmD,IAAA,CAAAL,WAAA,CAAAM,WAAA,CAA0B;IAAA,EAAC;IAEpC7D,EAAA,CAAAoB,SAAA,eAIO;IACTpB,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAkD,UAAA,IAAAY,0FAAA,gBAKC;;;;IAXG9D,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;IAOhEhC,EAAA,CAAA6B,SAAA,EAAuB;IAAvB7B,EAAA,CAAAgC,UAAA,SAAAuB,WAAA,CAAAQ,QAAA,CAAuB;;;;;IA1B9B/D,EAAA,CAAAC,cAAA,4BAWC;IACCD,EAAA,CAAAkD,UAAA,IAAAc,sFAAA,0BAAgD;IA0BlDhE,EAAA,CAAAG,YAAA,EAAoB;;;;IA7BlBH,EAAA,CAAAiE,UAAA,CAAAjE,EAAA,CAAAkE,eAAA,IAAAC,GAAA,EAAsD;IACtDnE,EAPA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,WACiC,gBAAArE,EAAA,CAAAkE,eAAA,KAAAI,GAAA,EACuB,2BAC7C,qBACN,WAAA7D,MAAA,CAAA8D,cAAA,WAEe;;;;;IA6C/BvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAC,WAAA,CAAAC,YAAA,MACF;;;;;IAIF1E,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAA2C,UAAA,CAAiB,WAAAC,SAAA,CACA,eACH;;;;;IAvBpB5E,EAAA,CAAAC,cAAA,4BAWC;IAQCD,EAPA,CAAAkD,UAAA,IAAA2B,sFAAA,0BAAgD,IAAAC,sFAAA,0BAOwB;IAa1E9E,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EANA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,iBACuC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACiB,2BAC7C,WAAA7D,MAAA,CAAA8D,cAAA,iBACe,oBACtB;;;;;IAmEfvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAO,YAAA,CAAAC,OAAA,MACF;;;;;IAIFhF,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAAiD,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAtBpBlF,EAAA,CAAAC,cAAA,4BAUC;IAQCD,EAPA,CAAAkD,UAAA,IAAAiC,sFAAA,0BAAgD,IAAAC,sFAAA,0BAOwB;IAa1EpF,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,UAC+B,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACwB,WAAA7D,MAAA,CAAA8D,cAAA,YACnC,oBACjB;;;;;IAsCfvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAa,YAAA,CAAAC,YAAA,MACF;;;;;IAIFtF,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAAuD,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAtBpBxF,EAAA,CAAAC,cAAA,4BAUC;IAQCD,EAPA,CAAAkD,UAAA,IAAAuC,sFAAA,0BAAgD,IAAAC,sFAAA,0BAOwB;IAa1E1F,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,UAC+B,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACwB,WAAA7D,MAAA,CAAA8D,cAAA,UACrC,oBACf;;;;;IAwEfvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAmB,YAAA,CAAAC,QAAA,MACF;;;;;IAIF5F,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAA6D,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAtBpB9F,EAAA,CAAAC,cAAA,4BAUC;IAQCD,EAPA,CAAAkD,UAAA,IAAA6C,sFAAA,0BAAgD,IAAAC,sFAAA,0BAOwB;IAa1EhG,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,aACkC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACqB,WAAA7D,MAAA,CAAA8D,cAAA,aAClC,oBAClB;;;;;IAqCjBvE,EAAA,CAAAoB,SAAA,eAOO;;;IAJLpB,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IAKjEhC,EAAA,CAAAoB,SAAA,eAOO;;;IAJLpB,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IAHjEhC,EARA,CAAAkD,UAAA,IAAA+C,6FAAA,mBAMC,IAAAC,6FAAA,mBAQA;;;;IAbElG,EAAA,CAAAgC,UAAA,SAAAmE,YAAA,CAAAC,UAAA,cAAsC;IAQtCpG,EAAA,CAAA6B,SAAA,EAAwC;IAAxC7B,EAAA,CAAAgC,UAAA,SAAAmE,YAAA,CAAAC,UAAA,gBAAwC;;;;;;IAS3CpG,EAAA,CAAAC,cAAA,6BAMC;IAHCD,EAAA,CAAAc,UAAA,yBAAAuF,iIAAA/F,MAAA;MAAA,MAAAgG,OAAA,GAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAAC,UAAA,GAAAF,OAAA,CAAA9C,SAAA;MAAA,MAAAiD,UAAA,GAAAH,OAAA,CAAAI,MAAA;MAAA,MAAAjG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAeJ,MAAA,CAAAkG,oBAAA,CAAArG,MAAA,EAAAkG,UAAA,EAAAC,UAAA,CAA4C;IAAA,EAAC;IAI9DzG,EAAA,CAAAG,YAAA,EAAqB;;;;;;IALnBH,EADA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmG,aAAA,CAAsB,UAAAnG,MAAA,CAAAoG,cAAA,CAAAL,UAAA,EAAAC,UAAA,EACkB;;;;;IAhC9CzG,EAAA,CAAAC,cAAA,4BAUC;IAmBCD,EAlBA,CAAAkD,UAAA,IAAA4D,sFAAA,0BAAgD,IAAAC,sFAAA,0BAkBwB;IAU1E/G,EAAA,CAAAG,YAAA,EAAoB;;;;IA9BlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,eACoC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACmB,WAAA7D,MAAA,CAAA8D,cAAA,eAChC,oBACpB;;;;;IAgDjBvE,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAEhC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAoB,SAAA,SAAM;IAAApB,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAEtC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IALyBH,EAAA,CAAA6B,SAAA,EAEhC;IAFgC7B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAgH,WAAA,OAAAC,YAAA,CAAAC,eAAA,wBAEhC;IACsClH,EAAA,CAAA6B,SAAA,GAEtC;IAFsC7B,EAAA,CAAAiC,iBAAA,CAAAgF,YAAA,CAAAE,qBAAA,CAEtC;;;;;IAQFnH,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAAoB,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnEpB,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAAgC,UAAA,WAAAoF,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IAhCrCtH,EAAA,CAAAC,cAAA,4BAaC;IASCD,EARA,CAAAkD,UAAA,IAAAqE,sFAAA,0BAAgD,IAAAC,sFAAA,0BAa/C;IAeHxH,EAAA,CAAAG,YAAA,EAAoB;;;;IA9BlBH,EARA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,oBACyC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACc,0BAG9C,WAAA7D,MAAA,CAAA8D,cAAA,oBACmB,oBACzB;;;;;IAzSvBvE,EAAA,CAAAyH,uBAAA,GAAiD;IA6R/CzH,EA3RA,CAAAkD,UAAA,IAAAwE,wEAAA,iCAWC,IAAAC,wEAAA,gCAyCA,IAAAC,wEAAA,gCA+DA,IAAAC,wEAAA,gCAkCA,IAAAC,wEAAA,gCAoEA,IAAAC,wEAAA,gCAkCA,IAAAC,wEAAA,gCA6CA;;;;;IAvSEhI,EAAA,CAAA6B,SAAA,EAAyB;IAAzB7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,cAAyB;IAyCzBjI,EAAA,CAAA6B,SAAA,EAA+B;IAA/B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,oBAA+B;IAgE/BjI,EAAA,CAAA6B,SAAA,EAA0B;IAA1B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,eAA0B;IAkC1BjI,EAAA,CAAA6B,SAAA,EAA+B;IAA/B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,oBAA+B;IAoE/BjI,EAAA,CAAA6B,SAAA,EAA2B;IAA3B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,gBAA2B;IAkC3BjI,EAAA,CAAA6B,SAAA,EAA6B;IAA7B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,kBAA6B;IA0C7BjI,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,uBAAkC;;;;;IAgD/BjI,EAHN,CAAAC,cAAA,cAAwD,cACQ,cACA,eAC5B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAE5DF,EAF4D,CAAAG,YAAA,EAAO,EAC3D,EACF;;;;;;IAKJH,EAJF,CAAAC,cAAA,cAGC,cAC0B;IACvBD,EAAA,CAAAoB,SAAA,YAAoE;IACpEpB,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8D;IAA1CD,EAAA,CAAAc,UAAA,mBAAAoH,kFAAA;MAAAlI,EAAA,CAAAO,aAAA,CAAA4H,IAAA;MAAA,MAAA1H,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA2H,SAAA,EAAW;IAAA,EAAC;IACvCpI,EAAA,CAAAoB,SAAA,YAAmC;IAAApB,EAAA,CAAAE,MAAA,eACrC;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAXNH,EARA,CAAAkD,UAAA,IAAAmF,yDAAA,kBAAwD,IAAAC,yDAAA,kBAWvD;;;;IAX+BtI,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA8H,OAAA,UAAsB;IAUnDvI,EAAA,CAAA6B,SAAA,EAAyD;IAAzD7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA8H,OAAA,cAAA9H,MAAA,CAAA+H,iBAAA,CAAAC,MAAA,OAAyD;;;ADpdlE,OAAM,MAAOC,2BAA2B;EAkN5BC,YAAA;EACAC,GAAA;EACAC,MAAA;EACAC,KAAA;EACAC,YAAA;EACDC,UAAA;EACCC,wBAAA;EACAC,eAAA;EACAC,oBAAA;EACAC,kBAAA;EA1NeC,IAAI;EAE7B;EACOb,iBAAiB,GAAU,EAAE;EAC7Bc,QAAQ,GAAU,EAAE;EACpBC,cAAc,GAAY,KAAK;EAE/BhB,OAAO,GAAY,KAAK;EACxBiB,SAAS,GAAY,KAAK;EAEjCC,SAAS,GAAQ,EAAE;EAEnB;EACO7I,UAAU,GAAW,EAAE;EACtB8I,WAAW,GAAG,IAAI/J,OAAO,EAAU;EACnCgK,kBAAkB;EAE1B;EACOC,MAAM,GAA8B;IAAEC,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACjEC,UAAU,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACrEE,aAAa,GAIf,EAAE;EAEApD,aAAa,GAAkD,CACpE;IAAEqD,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,CACxC;EAED;EACOlH,qBAAqB,GAAG;IAC7BP,MAAM,EAAE,CACN;MAAEwH,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,CACS;IAClDjH,KAAK,EAAE,EAAmD;IAAE;IAC5DkH,UAAU,EAAE,EAAmD,CAAE;GAClE;EAED;EACO/G,mBAAmB,GAAG,KAAK;EAC3BZ,cAAc,GAGjB,EAAE;EAEN;EACO4H,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7BtG,YAAY,GAAa,EAAE;EAC3BuG,gBAAgB,GAAa,EAAE;EAC/BC,UAAU;EACVC,YAAY;EACZxI,UAAU,GAAG,KAAK;EAEzB;EACOyI,gBAAgB,GAQlB,CACH;IACEC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR;EACD;EACA;IACEL,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,GAAG;IACVE,IAAI,EAAE,QAAQ;IACdD,OAAO,EAAE,KAAK;IACdG,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,CACF;EAED;EACOE,iBAAiB,GAA4B,EAAE;EAEtD;EAEA;EACOC,IAAI,GAAqB,CAAC;IAAER,KAAK,EAAE,iBAAiB;IAAES,GAAG,EAAE;EAAM,CAAE,CAAC;EAE3E;EACQC,kBAAkB;EAE1B;EACiBC,cAAc,GAAG,2BAA2B;EAE7D;EACOxJ,IAAI,GAAe;IACxByJ,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACbzJ,aAAa,EAAE,CAAC;IAChB0J,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EACMC,IAAI,GAAW,CAAC;EAEvB;EACOC,aAAa,GAA2C,CAC7D;IAAEhC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAK,CAAE,EACpC;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,EAC9C;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,CAC/C;EAED;EACOgC,aAAa,GAAU,EAAE;EACzBC,aAAa,GAAY,KAAK;EAErC;EACOC,cAAc,GAMjB;IACFC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE;GACb;EAED;EACOC,eAAe,GAAG,KAAK;EACvBC,gBAAgB,GAAW,QAAQ;EAE1C;EACOC,eAAe,GAAQ,EAAE;EAEhCC,YACUlE,YAAyB,EACzBC,GAAsB,EACtBC,MAAc,EACdC,KAAqB,EACrBC,YAAsB;EAAE;EACzBC,UAAsB,EACrBC,wBAAkD,EAClDC,eAAiC,EACjCC,oBAA0C,EAC1CC,kBAAsC;IATtC,KAAAT,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,UAAU,GAAVA,UAAU;IACT,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,kBAAkB,GAAlBA,kBAAkB;EACzB;EAEH0D,MAAMA,CAAA;IACJ,IAAI,CAACjE,MAAM,CAACkE,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACvD,SAAS,GAAG,IAAI,CAACT,UAAU,CAACiE,eAAe,EAAE;IAClDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC1D,SAAS,CAAC;IAEjD;IACA,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvC0D,IAAI,CAACxN,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/CwN,SAAS,CAAC,MAAK;MACd;MACA,IAAI,CAAC9E,OAAO,GAAG,IAAI;MACnB,IAAI,CAACiB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACtH,IAAI,CAAC0J,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MACb,IAAI,CAAC5D,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACqD,kBAAkB,GAAG,IAAI,CAAC5C,MAAM,CAACyE,MAAM,CAACD,SAAS,CAAEE,KAAK,IAAI;MAC/D,IAAIA,KAAK,YAAYzN,eAAe,EAAE;QACpC,IAAI,CAAC0N,aAAa,EAAE;MACtB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,aAAa,EAAE;IAEpB;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB;IACA;IAEA;IACA,IAAI,CAACC,UAAU,EAAE;IAEjB;IACA,IAAI,CAACC,gCAAgC,EAAE;IAEvC;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQF,gCAAgCA,CAAA;IACtC;IACA,IAAI,CAAClD,cAAc,GAAG,IAAI,CAACI,gBAAgB,CAACiD,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACjD,KAAK,CAAC;IACnE,IAAI,CAACN,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAE3C;IACA,IAAI,CAACtG,YAAY,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;IAEvD;IACA,IAAI,CAACuG,gBAAgB,GAAG,IAAI,CAACD,cAAc,CAACd,MAAM,CAC/CoE,GAAG,IAAK,CAAC,IAAI,CAAC5J,YAAY,CAACC,QAAQ,CAAC2J,GAAG,CAAC,CAC1C;IAED;IACA,IAAI,CAACpD,UAAU,GAAG,IAAI,CAACvB,IAAI;IAC3B,IAAI,CAACwB,YAAY,GAAG,IAAI,CAACxB,IAAI;IAE7B;IACAwE,UAAU,CAAC,MAAK;MACd,IAAI,CAACI,cAAc,EAAE;MACrB,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,eAAeA,CAAA;IACb;IACA;IACAN,UAAU,CAAC,MAAK;MACd,IAAI,CAACzF,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAgG,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC7F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACpB,SAAS,EAAE;IAChB,IAAI,CAAC8F,sBAAsB,EAAE;EAC/B;EAEA;EACAP,UAAUA,CAAA;IACR;IACA,IAAI,CAACzL,IAAI,CAAC0J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACT,IAAI,GAAG,CAAC;MAAER,KAAK,EAAE,iBAAiB;MAAES,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAC5B,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAAClJ,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACwH,SAAS,EAAE;EAClB;EAEA;EACAxG,WAAWA,CAAA;IACT;IACA,IAAI,CAAC2G,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiB,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACtH,IAAI,CAAC0J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACpC,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACxH,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAC5B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACwH,SAAS,EAAE;EAClB;EACAiG,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC5C,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC6C,WAAW,EAAE;IACvC;IACA,IAAI,IAAI,CAAC3E,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC2E,WAAW,EAAE;IACvC;IACA,IAAI,CAAC5E,WAAW,CAAC6E,QAAQ,EAAE;EAC7B;EACA;EACAC,0BAA0BA,CAAA;IACxB,IAAI,CAACjG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiB,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACN,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,MAAMC,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAAC1M,IAAI,CAACyJ,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfT,IAAI,EAAE,IAAI,CAACA,IAAI;MACf3B,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBiF,MAAM,EAAE,IAAI,CAACjO,UAAU;MACvBkO,cAAc,EAAE,IAAI,CAACrF,SAAS,CAACsF;KAChC;IAED,IAAI,CAAC5F,oBAAoB,CAAC6F,6BAA6B,CAACL,KAAK,CAAC,CAACtB,SAAS,CAAC;MACvEqB,IAAI,EAAGO,IAYN,IAAI;QACH;QACA,IACEA,IAAI,CAACC,OAAO,IACXD,IAAI,CAACE,YAAY,IAChBF,IAAI,CAACE,YAAY,CAACC,MAAM,IACxBH,IAAI,CAACE,YAAY,CAACC,MAAM,CAAC3G,MAAM,GAAG,CAAE,EACtC;UACA,MAAM2G,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7DlC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAED,MAAM,CAAC;UAC9C,IAAI,CAACE,mBAAmB,EAAE;QAC5B,CAAC,MAAM;UACL;UACA,MAAMH,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMM,QAAQ,GAAGJ,YAAY,CAACF,IAAI,IAAI,EAAE;UACxC,MAAMO,KAAK,GAAGL,YAAY,CAACK,KAAK,IAAI,CAAC;UAErC,IAAI,CAACjG,cAAc,GAAGgG,QAAQ,CAAC9G,MAAM,KAAK,CAAC;UAC3C,IAAI,CAACD,iBAAiB,GAAG+G,QAAQ;UACjC,IAAI,CAACjG,QAAQ,GAAG,IAAI,CAACd,iBAAiB;UACtC0E,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC7D,QAAQ,CAAC;UAC3C,IAAI,CAACpH,IAAI,CAACC,aAAa,GAAGqN,KAAK;UAC/B,IAAI,CAACtN,IAAI,CAAC2J,UAAU,GAAG4D,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACtN,IAAI,CAACyJ,IAAI,CAAC;QAC1D;QACA,IAAI,CAACzC,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDW,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAAC/G,OAAO,GAAG,KAAK;QACpB,IAAI,CAACiB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACN,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDH,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAChG,OAAO,GAAG,KAAK;QACpB,IAAI,CAACiB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACN,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC9F,GAAG,CAAC+G,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA;EACMvH,SAASA,CAAA;IAAA,IAAAwH,KAAA;IAAA,OAAAC,iBAAA;MACb;MACAD,KAAI,CAACpB,0BAA0B,EAAE;IAAC;EACpC;EAEQc,mBAAmBA,CAAA;IACzB,IAAI,CAAC/G,OAAO,GAAG,KAAK;IACpB,IAAI,CAACiB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACN,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;IAC/C,IAAI,CAACnF,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACf,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACc,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACpH,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAAC2J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACjD,GAAG,CAAC+G,aAAa,EAAE;EAC1B;EAEA;EACAxO,WAAWA,CAAA;IACT,IAAI,IAAI,CAACP,UAAU,KAAK,EAAE,EAAE;MAC1B,IAAI,CAAC8I,WAAW,CAACgF,IAAI,CAAC,EAAE,CAAC;IAC3B;EACF;EAEAzN,cAAcA,CAAA;IACZ,IAAI,CAACyI,WAAW,CAACgF,IAAI,CAAC,IAAI,CAAC9N,UAAU,IAAI,EAAE,CAAC;EAC9C;EAEA;EACAmC,eAAeA,CAAA;IACb,IAAI,CAACnC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACgJ,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACxH,cAAc,GAAG,EAAE;IACxB,IAAI,CAACN,IAAI,CAAC0J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAAC5D,SAAS,EAAE;EAClB;EAEA;EACAvF,oBAAoBA,CAAA;IAClB,IAAI,CAACX,IAAI,CAAC0J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAAC5D,SAAS,EAAE;EAClB;EAEA;EACA0H,qBAAqBA,CAAA;IACnB,IAAI,CAAC1M,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;EACAsK,SAASA,CAAA;IACP,MAAMqC,WAAW,GAKb;MACFC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,UAAU;MACrBtE,UAAU,EAAE;KACb;IAED,IAAI,CAACjD,YAAY,CAACwH,WAAW,CAACJ,WAAW,CAAC,CAAC1C,SAAS,CAAC;MACnDqB,IAAI,EAAGO,IAIN,IAAI;QACH,IAAIA,IAAI,IAAIA,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,CAACiB,OAAO,EAAE;UAC1D,IAAI,CAACpN,qBAAqB,CAACC,KAAK,GAAG,CACjC;YAAEgH,IAAI,EAAE,WAAW;YAAEC,KAAK,EAAE;UAAI,CAAE,EAClC,GAAG+E,IAAI,CAACE,YAAY,CAACiB,OAAO,CAACrC,GAAG,CAAEpL,IAA0B,KAAM;YAChEsH,IAAI,EAAEtH,IAAI,CAAC0N,QAAQ;YACnBnG,KAAK,EAAEvH,IAAI,CAAC0N;WACb,CAAC,CAAC,CACJ;QACH,CAAC,MAAM;UACL;UACA,IAAI,CAACrN,qBAAqB,CAACmH,UAAU,GAAG,CACtC;YAAEF,IAAI,EAAE,gBAAgB;YAAEC,KAAK,EAAE;UAAI,CAAE,CACxC;QACH;MACF,CAAC;MACDmF,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACA,IAAI,CAACrM,qBAAqB,CAACC,KAAK,GAAG,CAAC;UAAEgH,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC;MACzE;KACD,CAAC;IACF,IAAI,CAACvB,YAAY,CACd2H,qBAAqB,CAAC,EAAE,CAAC,CACzBjD,SAAS,CAAEkD,WAAgB,IAAI;MAC9B,IAAI,CAAC3D,eAAe,GAAG2D,WAAW,CAACpB,YAAY;IACjD,CAAC,CAAC;EACN;EAEA;EACAqB,kBAAkBA,CAAA;IAChB,IAAI,CAAC7H,YAAY,CAAC8H,iBAAiB,EAAE,CAACpD,SAAS,CAAC;MAC9CqB,IAAI,EAAGO,IAAS,IAAI;QAClB,IAAIA,IAAI,IAAIA,IAAI,CAACyB,UAAU,EAAE;UAC3B,IAAI,CAACtE,cAAc,GAAG6C,IAAI,CAACyB,UAAU;QACvC;MACF,CAAC;MACDrB,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACJ;EAEA;EACAsB,iBAAiBA,CAACC,SAAc;IAC9B,IAAI,CAAC1E,aAAa,GAAG0E,SAAS,CAACC,YAAY,IAAI,EAAE;IACjD,IAAI,CAAC1E,aAAa,GAChB,IAAI,CAACD,aAAa,CAACzD,MAAM,KAAK,IAAI,CAACD,iBAAiB,CAACC,MAAM;IAC7D,IAAI,CAACiE,eAAe,GAAG,IAAI,CAACR,aAAa,CAACzD,MAAM,GAAG,CAAC;EACtD;EAEA;EACAqI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC3E,aAAa,EAAE;MACtB,IAAI,CAACD,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC1D,iBAAiB,CAAC;MAChD,IAAI,CAAC2D,aAAa,GAAG,IAAI;IAC3B;IACA,IAAI,CAACO,eAAe,GAAG,IAAI,CAACR,aAAa,CAACzD,MAAM,GAAG,CAAC;EACtD;EAEA;EACAsI,UAAUA,CAACC,IAAS;IAClB,IACEC,OAAO,CACL,wCAAwCD,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACG,QAAQ,GAAG,CAC3E,EACD;MACA;MACA,IAAI,CAAC5I,OAAO,GAAG,IAAI;MACnB,IAAI,CAACiB,SAAS,GAAG,IAAI;MAErB,MAAM4H,UAAU,GAAG;QACjBrC,MAAM,EAAEiC,IAAI,CAACjC,MAAM;QACnBD,cAAc,EAAE,IAAI,CAACrF,SAAS,CAACsF,MAAM,IAAI;OAC1C;MAED,IAAI,CAACpG,YAAY,CAACoI,UAAU,CAACK,UAAU,CAAC,CAAC/D,SAAS,CAAC;QACjDqB,IAAI,EAAG2C,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YACgB,IAAI,CAACrI,wBAAwB,CAACsI,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE/E,IAAI,CAAClJ,SAAS,EAAE,CAAC,CAAC;YAClB;UACF;QACF,CAAC;QACDiH,KAAK,EAAGA,KAAc,IAAI;UACxBnC,OAAO,CAACmC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC1B,IAAI,CAACpG,wBAAwB,CAACuI,SAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC;UAEpF;UACA;UACA,IAAI,CAACjJ,OAAO,GAAG,KAAK;UACpB,IAAI,CAACiB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACAiI,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACvF,aAAa,CAACzD,MAAM,KAAK,CAAC,EAAE;MACb,IAAI,CAACQ,wBAAwB,CAACuI,SAAS,CAAC,+BAA+B,EAAE,EAAE,CAAC;MAClG;MAGA;IACF;IAEA,IACEP,OAAO,CACL,mCAAmC,IAAI,CAAC/E,aAAa,CAACzD,MAAM,qBAAqB,IAAI,CAACkE,gBAAgB,GAAG,CAC1G,EACD;MACA;MACA,IAAI,CAACpE,OAAO,GAAG,IAAI;MACnB,IAAI,CAACiB,SAAS,GAAG,IAAI;MAErB,MAAMkI,cAAc,GAAG;QACrBC,OAAO,EAAE,IAAI,CAACzF,aAAa,CAAC6B,GAAG,CAAEiD,IAAI,IAAKA,IAAI,CAACjC,MAAM,CAAC;QACtDtM,MAAM,EAAE,IAAI,CAACkK,gBAAgB;QAC7BmC,cAAc,EAAE,IAAI,CAACrF,SAAS,CAACsF,MAAM,IAAI;OAC1C;MAED,IAAI,CAACpG,YAAY,CAAC8I,oBAAoB,CAACC,cAAc,CAAC,CAACrE,SAAS,CAAC;QAC/DqB,IAAI,EAAG2C,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YACgB,IAAI,CAACrI,wBAAwB,CAACsI,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE/E,IAAI,CAAClJ,SAAS,EAAE,CAAC,CAAC;YAClB;YACA,IAAI,CAAC8D,aAAa,GAAG,EAAE,CAAC,CAAC;YACzB,IAAI,CAACQ,eAAe,GAAG,KAAK;UAC9B;QACF,CAAC;QACD2C,KAAK,EAAGA,KAAc,IAAI;UACxBnC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C;UACkB,IAAI,CAACpG,wBAAwB,CAACuI,SAAS,CAAC,sBAAsB,EAAE,EAAE,CAAC;UAErF;UACA,IAAI,CAACjJ,OAAO,GAAG,KAAK;UACpB,IAAI,CAACiB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACA/F,UAAUA,CAACuN,IAAS;IAClB,IACEC,OAAO,CACL,wCAAwCD,IAAI,CAACY,SAAS,IAAIZ,IAAI,CAACa,QAAQ,GAAG,CAC3E,EACD;MACA;MACA,IAAI,CAACtJ,OAAO,GAAG,IAAI;MACnB,IAAI,CAACiB,SAAS,GAAG,IAAI;MAErB,MAAMsI,UAAU,GAAG;QACjB/C,MAAM,EAAEiC,IAAI,CAACjC,MAAM;QACnBD,cAAc,EAAE,IAAI,CAACrF,SAAS,CAACsF,MAAM,IAAI;OAC1C;MAED,IAAI,CAACpG,YAAY,CAAClF,UAAU,CAACqO,UAAU,CAAC,CAACzE,SAAS,CAAC;QACjDqB,IAAI,EAAG2C,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YACgB,IAAI,CAACrI,wBAAwB,CAACsI,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE/E,IAAI,CAAClJ,SAAS,EAAE,CAAC,CAAC;YAClB;UACF;QACF,CAAC;QACDiH,KAAK,EAAGA,KAAc,IAAI;UACxBnC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC3B,IAAI,CAACpG,wBAAwB,CAACsI,WAAW,CAAC,wCAAwC,EAAE,EAAE,CAAC;UAEzG;UACA;UACA,IAAI,CAAChJ,OAAO,GAAG,KAAK;UACpB,IAAI,CAACiB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEAxI,eAAeA,CAACuM,KAAoB;IAClC,IAAIA,KAAK,CAACwE,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACrI,WAAW,CAACgF,IAAI,CAAC,IAAI,CAAC9N,UAAU,CAAC;IACxC;EACF;EAEA;EACAoR,mBAAmBA,CAAA;IASjB,IAAIpI,MAAM,GAQN;MACFqI,QAAQ,EAAE,IAAI;MACdpD,MAAM,EAAE,EAAE;MACVqD,YAAY,EAAE;KACf;IAED;IACA,IAAIC,UAAkB;IACtB,IAAI,IAAI,CAACvR,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,KAAKwR,SAAS,EAAE;MAC7DD,UAAU,GAAG,EAAE;IACjB,CAAC,MAAM;MACLA,UAAU,GAAG,IAAI,CAACvR,UAAU;IAC9B;IACAgJ,MAAM,CAACiF,MAAM,GAAGsD,UAAU,CAACE,IAAI,EAAE;IAEjC;IACA,IAAI,IAAI,CAACrI,aAAa,IAAI,IAAI,CAACA,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvDmB,MAAM,CAACsI,YAAY,GAAG,CAAC,GAAG,IAAI,CAAClI,aAAa,CAAC;IAC/C;IAEA;IACA,IAAI,IAAI,CAACxH,cAAc,CAACC,MAAM,IAAI,IAAI,CAACD,cAAc,CAACC,MAAM,KAAK,IAAI,EAAE;MACrEmH,MAAM,CAACsI,YAAY,CAACI,IAAI,CAAC;QACvBvH,KAAK,EAAE,YAAY;QACnBwH,QAAQ,EAAE,IAAI;QACdrI,KAAK,EAAE,IAAI,CAAC1H,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA,IAAI,IAAI,CAACD,cAAc,CAACG,IAAI,IAAI,IAAI,CAACH,cAAc,CAACG,IAAI,KAAK,IAAI,EAAE;MACjEiH,MAAM,CAACsI,YAAY,CAACI,IAAI,CAAC;QACvBvH,KAAK,EAAE,UAAU;QACjBwH,QAAQ,EAAE,IAAI;QACdrI,KAAK,EAAE,IAAI,CAAC1H,cAAc,CAACG;OAC5B,CAAC;IACJ;IAEA,OAAOiH,MAAM;EACf;EAEA;EACO4I,UAAUA,CAACjF,KAAqC;IACrD,IAAI,CAACvB,IAAI,GAAGuB,KAAK,CAACvB,IAAI;IACtB,IAAI,CAAC9J,IAAI,CAAC0J,UAAU,GAAG2B,KAAK,CAACvB,IAAI,GAAGuB,KAAK,CAACqB,IAAI;IAC9C,IAAI,CAAC1M,IAAI,CAACyJ,IAAI,GAAG4B,KAAK,CAACqB,IAAI;IAC3B,IAAI,CAACxG,SAAS,EAAE;EAClB;EAEOqK,YAAYA,CAAClH,IAAsB;IACxC2B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE5B,IAAI,CAAC;IAE3C;IACA,MAAMmH,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACrH,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;IAEpD,IAAImH,YAAY,CAACjK,MAAM,KAAK,CAAC,EAAE;MAC7B;MACAyE,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D,IAAI,CAAC5B,IAAI,GAAG,CAAC;QAAER,KAAK,EAAE,iBAAiB;QAAES,GAAG,EAAE;MAAM,CAAE,CAAC;MACvD,IAAI,CAACtJ,IAAI,CAAC4J,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAAC5J,IAAI,CAAC6J,QAAQ,GAAG,MAAM;MAE3B;MACA,IAAI,IAAI,CAAC1C,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACkC,IAAI,GAAG,CAAC;UAAER,KAAK,EAAE,iBAAiB;UAAES,GAAG,EAAE;QAAM,CAAE,CAAC;MAC9D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACD,IAAI,GAAGmH,YAAY;MACxB,IAAI,CAACxQ,IAAI,CAAC4J,OAAO,GAAG,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,CAACR,KAAK,IAAI,iBAAiB;MAC3D,IAAI,CAAC7I,IAAI,CAAC6J,QAAQ,GAAG,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,IAAI,MAAM;IACjD;IAEA0B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC5B,IAAI,CAAC;IAC3C2B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;MAAErB,OAAO,EAAE,IAAI,CAAC5J,IAAI,CAAC4J,OAAO;MAAEC,QAAQ,EAAE,IAAI,CAAC7J,IAAI,CAAC6J;IAAQ,CAAE,CAAC;IAExF,IAAI,CAAC3D,SAAS,EAAE;EAClB;EAEOyK,YAAYA,CAACjJ,MAAiC;IACnD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,UAAU,GAAGH,MAAM;IACxB,IAAI,CAACI,aAAa,GAAG,IAAI,CAAC8I,cAAc,CAAClJ,MAAM,CAAC;IAChD,IAAI,CAAC1H,IAAI,CAAC0J,UAAU,GAAG,CAAC;IACxB,IAAI,CAAC4B,aAAa,EAAE;IACpB;IACA,IAAI,CAACjF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACwC,IAAI,GAAG,CAAC;IACb,IAAI,CAAC5D,SAAS,EAAE;EAClB;EAEA;EAEA;EACOvB,cAAcA,CACnB+C,MAAiC,EACjClD,MAAyB;IAEzB,IAAI,CAACkD,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,IAAI,CAACpD,MAAM,EAAE;MACzC,OAAO,IAAI;IACb;IACA,MAAMqM,SAAS,GAAGnJ,MAAM,CAACE,OAAO,CAACkJ,IAAI,CAClCC,CAAM,IAAKA,CAAC,IAAI,OAAO,IAAIA,CAAC,IAAIA,CAAC,CAAClI,KAAK,KAAKrE,MAAM,CAACqE,KAAK,CAC1D;IACD,OAAOgI,SAAS,IAAI,OAAO,IAAIA,SAAS,GAAGA,SAAS,CAAC7I,KAAK,GAAG,IAAI;EACnE;EAEA;EACOvD,oBAAoBA,CACzBuD,KAAoB,EACpBN,MAAiC,EACjClD,MAAyB;IAEzB,IAAI,CAACkD,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,IAAI,CAACpD,MAAM,EAAE;MACzCwG,OAAO,CAACmC,KAAK,CAAC,2BAA2B,EAAE;QAAEzF,MAAM;QAAElD;MAAM,CAAE,CAAC;MAC9D;IACF;IAEA,MAAMwM,MAAM,GAAGtJ,MAAM,CAACE,OAAO,CAACqJ,SAAS,CACpCF,CAAM,IAAKA,CAAC,IAAI,OAAO,IAAIA,CAAC,IAAIA,CAAC,CAAClI,KAAK,KAAKrE,MAAM,CAACqE,KAAK,CAC1D;IACD,IAAImI,MAAM,GAAG,CAAC,CAAC,EAAE;MACftJ,MAAM,CAACE,OAAO,CAACsJ,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;IAClC;IAEA,IAAIhJ,KAAK,KAAK,IAAI,EAAE;MAClBN,MAAM,CAACE,OAAO,CAACwI,IAAI,CAAC;QAClBvH,KAAK,EAAErE,MAAM,CAACqE,KAAK;QACnBwH,QAAQ,EAAE,IAAI;QACdrI,KAAK,EAAEA;OACR,CAAC;IACJ;IAEA,IAAI,CAAC2I,YAAY,CAACjJ,MAAM,CAAC;EAC3B;EAEA;EACQkJ,cAAcA,CAAClJ,MAAiC;IAKtD,MAAME,OAAO,GAIR,EAAE;IAEP,IAAI,CAACF,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;MAC9B,OAAOA,OAAO;IAChB;IAEAF,MAAM,CAACE,OAAO,CAACuJ,OAAO,CAAEJ,CAAM,IAAI;MAChC,IAAIA,CAAC,IAAI,OAAO,IAAIA,CAAC,EAAE;QACrB;QACAnJ,OAAO,CAACwI,IAAI,CAAC;UACXvH,KAAK,EAAEkI,CAAC,CAAClI,KAAK;UACdwH,QAAQ,EAAEU,CAAC,CAACV,QAAQ;UACpBrI,KAAK,EAAE+I,CAAC,CAAC/I;SACV,CAAC;MACJ,CAAC,MAAM,IAAI+I,CAAC,IAAI,SAAS,IAAIA,CAAC,EAAE;QAC9B;QACAnJ,OAAO,CAACwI,IAAI,CAAC,GAAG,IAAI,CAACQ,cAAc,CAACG,CAAC,CAAC,CAAC;MACzC;IACF,CAAC,CAAC;IAEF,OAAOnJ,OAAO;EAChB;EAEA;EACQ2D,aAAaA,CAAA;IACnB,IAAI;MACF,MAAM6F,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC9H,cAAc,CAAC;MAE5D,IAAI,CAAC4H,UAAU,EAAE;QACf;MACF;MAEA,MAAM3E,KAAK,GAwBP8E,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;MAE1B;MACA,IAAI3E,KAAK,IAAIA,KAAK,CAACpD,IAAI,EAAE;QACvB,IAAI,CAACA,IAAI,GAAGoD,KAAK,CAACpD,IAAI;QACtB,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC9C,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC8C,IAAI,CAAC,CAAC,CAAC,EAAE;UACrD,IAAI,CAACrJ,IAAI,CAAC4J,OAAO,GAAG,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,CAACR,KAAK,IAAI,iBAAiB;UAC3D,IAAI,CAAC7I,IAAI,CAAC6J,QAAQ,GAAG,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,IAAI,MAAM;QACjD;MACF;MAEA;MACA,IAAImD,KAAK,IAAIA,KAAK,CAAC/E,MAAM,EAAE;QACzB,IAAI,CAACA,MAAM,GAAG+E,KAAK,CAAC/E,MAAM;QAC1B,IAAI,CAACG,UAAU,GAAG4E,KAAK,CAAC/E,MAAM;QAC9B,IAAI,CAACI,aAAa,GAAG2E,KAAK,CAAC3E,aAAa,IAAI,EAAE;MAChD;MAEA;MACA,IAAI2E,KAAK,IAAIA,KAAK,CAACzM,IAAI,EAAE;QACvB,IAAI,CAACA,IAAI,GAAGyM,KAAK,CAACzM,IAAI;MACxB;MAEA,IAAIyM,KAAK,IAAIA,KAAK,CAAC3C,IAAI,KAAKoG,SAAS,EAAE;QACrC,IAAI,CAACpG,IAAI,GAAG2C,KAAK,CAAC3C,IAAI;MACxB;MAEA;MACA,IAAI2C,KAAK,IAAIA,KAAK,CAACrD,iBAAiB,EAAE;QACpC,IAAI,CAACA,iBAAiB,GAAGqD,KAAK,CAACrD,iBAAiB;MAClD;MAEA;MACA,IAAIqD,KAAK,IAAIA,KAAK,CAAC/N,UAAU,EAAE;QAC7B,IAAI,CAACA,UAAU,GAAG+N,KAAK,CAAC/N,UAAU;MACpC;MAEA;MACA,IAAI+N,KAAK,IAAIA,KAAK,CAACnM,cAAc,EAAE;QACjC,IAAI,CAACA,cAAc,GAAGmM,KAAK,CAACnM,cAAc;MAC5C;MAEA,IAAImM,KAAK,IAAIA,KAAK,CAACvL,mBAAmB,KAAKgP,SAAS,EAAE;QACpD,IAAI,CAAChP,mBAAmB,GAAGuL,KAAK,CAACvL,mBAAmB;MACtD;IACF,CAAC,CAAC,OAAOiM,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;IACF;EACF;EAEA;;;EAGA7N,UAAUA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAACiI,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACsF,MAAM,EAAE;MAC7C7B,OAAO,CAACmC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC5F,SAAS,CAAC;MACzD,IAAI,CAACR,wBAAwB,CAACuI,SAAS,CAAC,4DAA4D,EAAE,EAAE,CAAC;MACzG;IACF;IAEA;IACA,IAAI,CAACtP,IAAI,CAAC0J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACT,IAAI,GAAG,CAAC;MAAER,KAAK,EAAE,iBAAiB;MAAES,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAC5B,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAAClJ,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC4B,cAAc,GAAG,EAAE;IACxB,IAAI,CAACY,mBAAmB,GAAG,KAAK;IAEhC;IACA,IAAI,IAAI,CAACiG,IAAI,IAAI,IAAI,CAACA,IAAI,CAACsK,OAAO,EAAE;MAClC,IAAI,CAACtK,IAAI,CAACsK,OAAO,CAACN,OAAO,CAAE3M,MAAW,IAAI;QACxC,MAAMkN,KAAK,GAAG,IAAI,CAACnJ,WAAW,CAACoJ,OAAO,CAACnN,MAAM,CAACqE,KAAK,CAAC;QACpD,IAAI6I,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBlN,MAAM,CAACoN,UAAU,GAAGF,KAAK;QAC3B;QACA;QACA,IAAIlN,MAAM,CAACqE,KAAK,IAAIrE,MAAM,CAACqE,KAAK,KAAK,QAAQ,EAAE;UAC7CrE,MAAM,CAACqN,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAC1J,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACE,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,IAAI,CAACnB,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACO,MAAM,GAAG;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACT,IAAI,CAACkC,IAAI,GAAG,CAAC;QAAER,KAAK,EAAE,iBAAiB;QAAES,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAACnC,IAAI,CAAC2C,IAAI,GAAG,CAAC;MAClB,IAAI,CAAC3C,IAAI,CAAC2G,QAAQ,GAAG,IAAI,CAAC9N,IAAI,CAACyJ,IAAI;IACrC;IAEA;IACA,MAAM4D,QAAQ,GAAG;MACfyE,QAAQ,EAAE,gBAAgB;MAC1BC,MAAM,EAAE,IAAI,CAACxK,SAAS,CAACsF,MAAM;MAC7B1E,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjB4J,QAAQ,EAAE,IAAI,CAACzK,SAAS,CAACsF;KAC1B;IAED;IACA,IAAI,CAAC7F,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACtF,kBAAkB,CAAC+K,gBAAgB,CAAC5E,QAAQ,CAAC,CAAClC,SAAS,CAAC;MAC3DqB,IAAI,EAAG0F,GAAG,IAAI;QACZ,IAAI,CAAClL,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC0F,GAAG,CAAClF,OAAO,EAAE;UAChB;UACA,IAAI,CAAC9F,kBAAkB,CAACiL,qBAAqB,CAAC,gBAAgB,CAAC;UAC/D,IAAI,CAACpL,wBAAwB,CAACsI,WAAW,CAAC6C,GAAG,CAAC9C,OAAO,IAAI,qCAAqC,EAAE,EAAE,CAAC;QACrG,CAAC,MAAM;UACL,IAAI,CAACrI,wBAAwB,CAACuI,SAAS,CAAC4C,GAAG,CAAC9C,OAAO,IAAI,kCAAkC,EAAE,EAAE,CAAC;QAChG;QAEA;QACA,IAAI,CAAC1I,GAAG,CAAC+G,aAAa,EAAE;QAExB;QACA9B,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACxE,IAAI,EAAE;YACb,IAAI,CAACA,IAAI,CAACiL,OAAO,EAAE;UACrB;QACF,CAAC,EAAE,GAAG,CAAC;QAEP,IAAI,CAAClM,SAAS,EAAE;MAClB,CAAC;MACDiH,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnG,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CxB,OAAO,CAACmC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAExD;QACA,IAAIA,KAAK,CAAC5M,MAAM,KAAK,GAAG,IAAK4M,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC5M,MAAM,KAAK,GAAI,EAAE;UACvE,IAAI,CAACwG,wBAAwB,CAACuI,SAAS,CAAC,4CAA4C,EAAE,EAAE,CAAC;UACzF;QACF,CAAC,MAAM;UACL,IAAI,CAACvI,wBAAwB,CAACuI,SAAS,CAAC,oDAAoD,EAAE,EAAE,CAAC;QACnG;MACF;KACD,CAAC;EACJ;EAEA;EACQhE,aAAaA,CAAA;IACnB,MAAMmB,KAAK,GAwBP;MACFpD,IAAI,EAAE,IAAI,CAACA,IAAI;MACf3B,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB1H,IAAI,EAAE,IAAI,CAACA,IAAI;MACf8J,IAAI,EAAE,IAAI,CAACA,IAAI;MACfV,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzC1K,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BoJ,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCxH,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCY,mBAAmB,EAAE,IAAI,CAACA;KAC3B;IAEDmQ,YAAY,CAACgB,OAAO,CAAC,IAAI,CAAC7I,cAAc,EAAE+H,IAAI,CAACe,SAAS,CAAC7F,KAAK,CAAC,CAAC;EAClE;EAEA;EACArN,GAAGA,CAAA;IACD,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC;EACd;EAEA;EACAA,IAAIA,CAAC6Q,EAAU;IACbvH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,sBAAsB,EAAEsH,EAAE,CAAC;IACpD;IACA,MAAMC,eAAe,GAKjB;MACF/I,IAAI,EAAE,IAAI;MAAE;MACZgJ,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC/L,YAAY,CAACgM,IAAI,CACrChV,2BAA2B,EAC3B2U,eAAe,CAChB;IACD;IACAI,QAAQ,CAACE,iBAAiB,CAACP,EAAE,GAAGA,EAAE;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA;EAEA;EACAxG,cAAcA,CAAA;IACZ;IACAf,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAEA;EACAe,sBAAsBA,CAAA;IACpB;IACAhB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C;EAEA;EACA8H,uBAAuBA,CAAA;IACrB,IAAI,CAACtL,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvC0D,IAAI,CAACxN,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/CwN,SAAS,CAAC,MAAK;MACd,IAAI,CAACnL,IAAI,CAAC0J,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MACb,IAAI,CAAC5D,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EACO1G,YAAYA,CAAA;IACjB;IACA,MAAMwT,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiB,CACH;IAChB,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAACjT,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAACgH,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACiL,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACOiB,aAAaA,CAAChI,KAAkC;IACrD,QAAQA,KAAK,CAACiI,IAAI,CAACtL,KAAK;MACtB,KAAK,KAAK;QACR,IAAI,CAACuL,cAAc,EAAE;QACrB;MACF,KAAK,UAAU;QACb,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF,KAAK,UAAU;QACb,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF;QACEzI,OAAO,CAAC0I,IAAI,CAAC,wBAAwB,EAAErI,KAAK,CAACiI,IAAI,CAACtL,KAAK,CAAC;IAC5D;EACF;EAEQuL,cAAcA,CAAA;IACpB,MAAMI,YAAY,GAAG;MACnB/L,OAAO,EAAE,EAAE;MACXgM,MAAM,EAAE;KACT;IAED,IAAI,CAACnN,YAAY,CAACoN,WAAW,CAACF,YAAY,CAAC,CAACxI,SAAS,CAAC;MACpDqB,IAAI,EAAG2C,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAAC2E,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAC5E,QAAQ,CAAC2E,UAAU,EAAE,WAAW,CAAC;QACtD;MACF,CAAC;MACD3G,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;MACF;KACD,CAAC;EACJ;EAEQqG,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACxJ,aAAa,CAACzD,MAAM,KAAK,CAAC,EAAE;MACnC;MACA;IACF;IAEA,MAAMoN,YAAY,GAAG;MACnB/L,OAAO,EAAE;QACP6H,OAAO,EAAE,IAAI,CAACzF,aAAa,CAAC6B,GAAG,CAAEiD,IAAI,IAAKA,IAAI,CAACkF,MAAM;OACtD;MACDJ,MAAM,EAAE;KACT;IAED,IAAI,CAACnN,YAAY,CAACoN,WAAW,CAACF,YAAY,CAAC,CAACxI,SAAS,CAAC;MACpDqB,IAAI,EAAG2C,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAAC2E,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAC5E,QAAQ,CAAC2E,UAAU,EAAE,gBAAgB,CAAC;QAC3D;MACF,CAAC;MACD3G,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;MACF;KACD,CAAC;EACJ;EAEQsG,mBAAmBA,CAAA;IACzB,MAAME,YAAY,GAAG;MACnB/L,OAAO,EAAE;QACPrH,MAAM,EAAE,IAAI,CAACD,cAAc,CAACC,MAAM;QAClCE,IAAI,EAAE,IAAI,CAACH,cAAc,CAACG,IAAI;QAC9BwT,UAAU,EAAE,IAAI,CAACvV;OAClB;MACDkV,MAAM,EAAE;KACT;IAED,IAAI,CAACnN,YAAY,CAACoN,WAAW,CAACF,YAAY,CAAC,CAACxI,SAAS,CAAC;MACpDqB,IAAI,EAAG2C,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAAC2E,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAC5E,QAAQ,CAAC2E,UAAU,EAAE,gBAAgB,CAAC;QAC3D;MACF,CAAC;MACD3G,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;MACF;KACD,CAAC;EACJ;EAEQ4G,aAAaA,CAAChH,IAAW,EAAEmH,QAAgB;IACjD;IACA;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,YAAY,CAACrH,IAAI,CAAC;IAC1C,MAAMsH,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAElL,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMsL,IAAI,GAAGtB,QAAQ,CAACuB,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IACrCE,IAAI,CAACK,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BF,IAAI,CAACK,YAAY,CACf,UAAU,EACV,GAAGV,QAAQ,IAAI,IAAIW,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAC5D;IACDR,IAAI,CAACS,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChChC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC;IAC/BA,IAAI,CAACa,KAAK,EAAE;IACZnC,QAAQ,CAACiC,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;EACjC;EAEQH,YAAYA,CAACrH,IAAW;IAC9B,IAAIA,IAAI,CAACxG,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEhC,MAAM+O,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACzI,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM0I,OAAO,GAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC;IAEnC,KAAK,MAAMC,GAAG,IAAI5I,IAAI,EAAE;MACtB,MAAM6I,MAAM,GAAGN,OAAO,CAACzJ,GAAG,CAAEgK,MAAM,IAAI;QACpC,MAAM7N,KAAK,GAAG2N,GAAG,CAACE,MAAM,CAAC;QACzB,OAAO,OAAO7N,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC7F,QAAQ,CAAC,GAAG,CAAC,GACnD,IAAI6F,KAAK,GAAG,GACZA,KAAK;MACX,CAAC,CAAC;MACFyN,OAAO,CAACrF,IAAI,CAACwF,MAAM,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC;IAEA,OAAOD,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;EAC3B;EAEA;EAEA;;;;;;EAMAI,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACvO,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACsF,MAAM,EAAE;MAC7C7B,OAAO,CAACmC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC5F,SAAS,CAAC;MACzD,IAAI,CAACR,wBAAwB,CAACuI,SAAS,CACrC,8CAA8C,EAC9C,EAAE,CACH;MACD;IACF;IAEA,MAAMyG,gBAAgB,GAAU,EAAE;IAClC,MAAMC,aAAa,GAAU,EAAE;IAE/B,IAAI,IAAI,CAAC7O,IAAI,IAAI,IAAI,CAACA,IAAI,CAACsK,OAAO,EAAE;MAClC,IAAI,CAACtK,IAAI,CAACsK,OAAO,CAACN,OAAO,CAAE3M,MAAW,IAAI;QACxC,IAAI,CAACA,MAAM,CAACqN,MAAM,EAAE;UAClB,MAAMoE,UAAU,GAAG;YACjBnN,KAAK,EAAEtE,MAAM,CAACsE,KAAK;YACnBD,KAAK,EAAErE,MAAM,CAACqE,KAAK;YACnBgJ,MAAM,EAAErN,MAAM,CAACqN;WAChB;UACDkE,gBAAgB,CAAC3F,IAAI,CAAC6F,UAAU,CAAC;QACnC,CAAC,MAAM;UACL,MAAMA,UAAU,GAAG;YACjBnN,KAAK,EAAEtE,MAAM,CAACsE,KAAK;YACnBD,KAAK,EAAErE,MAAM,CAACqE,KAAK;YACnBgJ,MAAM,EAAErN,MAAM,CAACqN;WAChB;UACDmE,aAAa,CAAC5F,IAAI,CAAC6F,UAAU,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,MAAMC,qBAAqB,GAAG,IAAI,CAAC3N,WAAW,CAC3Cb,MAAM,CAAEoE,GAAG,IAAK,CAAC,IAAI,CAAC5J,YAAY,CAACC,QAAQ,CAAC2J,GAAG,CAAC,CAAC,CACjDD,GAAG,CAAC,CAAChD,KAAK,EAAE6I,KAAK,MAAM;MACtB7I,KAAK;MACL+I,UAAU,EAAEF;KACb,CAAC,CAAC;IAEL;IACA,MAAMrE,QAAQ,GAAG;MACfyE,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,IAAI,CAACxK,SAAS,CAACsF,MAAM;MAC7B1E,UAAU,EAAE6N,aAAa;MACzB5N,aAAa,EAAE8N,qBAAqB;MACpClE,QAAQ,EAAE,IAAI,CAACzK,SAAS,CAACsF;KAC1B;IAED;IACA,IAAI,CAAC7F,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACtF,kBAAkB,CAAC+K,gBAAgB,CAAC5E,QAAQ,CAAC,CAAClC,SAAS,CAAC;MAC3DqB,IAAI,EAAG0F,GAAG,IAAI;QACZ,IAAI,CAAClL,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC0F,GAAG,CAAClF,OAAO,EAAE;UAChB;UACA,IAAI,CAAC7E,UAAU,GAAG6N,aAAa;UAC/B,IAAI,CAAC5N,aAAa,GAAG8N,qBAAqB;UAC1C,IAAI,CAAC5N,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;UAEhE;UACA,IAAI,CAAC3B,kBAAkB,CAACiP,kBAAkB,CAAC9I,QAAQ,CAAC;UAEpD,IAAI,CAACtG,wBAAwB,CAACsI,WAAW,CACvC6C,GAAG,CAAC9C,OAAO,IAAI,qCAAqC,EACpD,EAAE,CACH;QACH,CAAC,MAAM;UACL,IAAI,CAACrI,wBAAwB,CAACuI,SAAS,CACrC4C,GAAG,CAAC9C,OAAO,IAAI,iCAAiC,EAChD,EAAE,CACH;QACH;QACA,IAAI,CAAC1I,GAAG,CAAC0P,YAAY,EAAE;MACzB,CAAC;MACDjJ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnG,eAAe,CAACuF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CxB,OAAO,CAACmC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QAErD;QACA,IAAI,CAACjG,kBAAkB,CAACiP,kBAAkB,CAAC9I,QAAQ,CAAC;QAEpD;QACA,IAAI,CAAClF,UAAU,GAAG6N,aAAa;QAC/B,IAAI,CAAC5N,aAAa,GAAG8N,qBAAqB;QAC1C,IAAI,CAAC5N,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAEhE,IAAI,CAAC9B,wBAAwB,CAACuI,SAAS,CACrC,mDAAmD,EACnD,EAAE,CACH;QACD,IAAI,CAAC5I,GAAG,CAAC0P,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAGA;;;;;EAKAC,oBAAoBA,CAACjO,aAAkB;IACrC,IAAI;MACF,MAAMkO,UAAU,GAAGlO,aAAa;MAChC,IAAIkO,UAAU,EAAE;QACd,MAAMC,WAAW,GAAGD,UAAU;QAC9B,IAAI7F,KAAK,CAACC,OAAO,CAAC6F,WAAW,CAAC,IAAIA,WAAW,CAAChQ,MAAM,GAAG,CAAC,EAAE;UACxD;UACA,MAAMiQ,qBAAqB,GAAGD,WAAW,CACtClN,IAAI,CAAC,CAACoN,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7E,UAAU,GAAG8E,CAAC,CAAC9E,UAAU,CAAC,CAC3C/F,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACjD,KAAK,CAAC,CACvBnB,MAAM,CAAEmB,KAAK,IAAK,CAAC,IAAI,CAAC3G,YAAY,CAACC,QAAQ,CAAC0G,KAAK,CAAC,CAAC;UAExD;UACA,MAAM8N,cAAc,GAAG,IAAI,CAAClO,gBAAgB,CAACf,MAAM,CAChDoE,GAAG,IAAK,CAAC0K,qBAAqB,CAACrU,QAAQ,CAAC2J,GAAG,CAAC,CAC9C;UAED;UACA,IAAI,CAACvD,WAAW,GAAG,CACjB,GAAG,IAAI,CAACrG,YAAY,EACpB,GAAGsU,qBAAqB,EACxB,GAAGG,cAAc,CAClB;QACH,CAAC,MAAM;UACL,IAAI,CAACpO,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACd,IAAI,CAAC5E,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC7C;EACF;EAEA;;;;EAIAnG,cAAcA,CAACuU,UAAe;IAC5B,OAAO,IAAI,CAACtO,YAAY,CAACqJ,OAAO,CAACiF,UAAU,CAAC,GAAG,CAAC,CAAC;EACnD;EAEA;;;;;EAKAC,eAAeA,CAACxL,KAAU;IACxB,MAAM;MAAEoG,OAAO;MAAEqF,QAAQ;MAAEC;IAAQ,CAAE,GAAG1L,KAAK;IAE7C;IACA,IACE,IAAI,CAACnJ,YAAY,CAACC,QAAQ,CAACsP,OAAO,CAACsF,QAAQ,CAAC,CAAClO,KAAK,CAAC,IACnD,IAAI,CAAC3G,YAAY,CAACC,QAAQ,CAACsP,OAAO,CAACqF,QAAQ,CAAC,CAACjO,KAAK,CAAC,EACnD;MACA;IACF;IAEA;IACA,MAAMmO,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACzO,WAAW,CAAC;IAC9C,MAAM,CAAC0O,WAAW,CAAC,GAAGD,gBAAgB,CAAC9F,MAAM,CAAC6F,QAAQ,EAAE,CAAC,CAAC;IAC1DC,gBAAgB,CAAC9F,MAAM,CAAC4F,QAAQ,EAAE,CAAC,EAAEG,WAAW,CAAC;IAEjD,IAAI,CAAC1O,WAAW,GAAGyO,gBAAgB;IACnC,IAAI,CAACtQ,GAAG,CAAC0P,YAAY,EAAE;EACzB;EAEA;;;;EAIAc,sBAAsBA,CAAC7L,KAAU;IAC/B,IAAI,IAAI,CAAClL,UAAU,KAAK,KAAK,EAAE;MAC7B,IAAI,IAAI,CAACgH,IAAI,IAAI,IAAI,CAACA,IAAI,CAACsK,OAAO,EAAE;QAClC,IAAI,CAACtK,IAAI,CAACsK,OAAO,CAACN,OAAO,CAAE3M,MAAW,IAAI;UACxC,MAAMyR,UAAU,GAAG;YACjBnN,KAAK,EAAEtE,MAAM,CAACsE,KAAK;YACnBD,KAAK,EAAErE,MAAM,CAACqE,KAAK;YACnBgJ,MAAM,EAAErN,MAAM,CAACqN;WAChB;UACD,IAAIrN,MAAM,CAACqN,MAAM,EAAE;YACjB,MAAMb,MAAM,GAAG,IAAI,CAAC7I,UAAU,CAACgP,IAAI,CAChC7D,IAAS,IACRA,IAAI,CAACzK,KAAK,KAAKoN,UAAU,CAACpN,KAAK,IAAIyK,IAAI,CAACzB,MAAM,KAAK,IAAI,CAC1D;YACD,IAAI,CAACb,MAAM,EAAE;cACX,IAAI,CAAC7I,UAAU,CAACiI,IAAI,CAAC6F,UAAU,CAAC;YAClC;UACF,CAAC,MAAM;YACL,IAAImB,WAAW,GAAG,IAAI,CAACjP,UAAU,CAAC8I,SAAS,CACxCqC,IAAS,IACRA,IAAI,CAACzK,KAAK,KAAKoN,UAAU,CAACpN,KAAK,IAAIyK,IAAI,CAACzB,MAAM,KAAK,IAAI,CAC1D;YACD,IAAIuF,WAAW,KAAK,CAAC,CAAC,EAAE;cACtB,IAAI,CAACjP,UAAU,CAAC+I,MAAM,CAACkG,WAAW,EAAE,CAAC,CAAC;YACxC;UACF;QACF,CAAC,CAAC;QACF,IAAI,CAAC9O,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAChE,IAAI,CAACnC,GAAG,CAAC0P,YAAY,EAAE;MACzB;IACF;EACF;EAEA;;;;EAIQxK,4BAA4BA,CAAA;IAClC,IAAI;MACF;MACA,IAAI,IAAI,CAACrE,SAAS,IAAI,IAAI,CAACA,SAAS,CAACsF,MAAM,EAAE;QAC3C,IAAI,CAAC3F,kBAAkB,CACpBmQ,aAAa,CAAC;UACbvF,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,IAAI,CAACxK,SAAS,CAACsF;SACxB,CAAC,CACD1B,SAAS,CAAC;UACTqB,IAAI,EAAG0F,GAAG,IAAI;YACZ,IAAI,CAACA,GAAG,CAAClF,OAAO,IAAIkF,GAAG,CAACoF,IAAI,EAAE;cAC5B,IAAI,CAACpP,SAAS,GAAGgK,GAAG,CAACoF,IAAI;cACzB,IAAI,CAACnP,UAAU,GAAG+J,GAAG,CAACoF,IAAI,CAACC,QAAQ,GAC/BhG,IAAI,CAACC,KAAK,CAACU,GAAG,CAACoF,IAAI,CAACC,QAAQ,CAAC,GAC7B,EAAE;cACN,IAAI,CAAClP,iBAAiB,GAAG6J,GAAG,CAACoF,IAAI,CAAClP,aAAa,GAC3CmJ,IAAI,CAACC,KAAK,CAACU,GAAG,CAACoF,IAAI,CAAClP,aAAa,CAAC,GAClC,EAAE;cACN,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CACpCC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CACxB;cAED;cACA,IAAI,IAAI,CAAC1B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACsK,OAAO,EAAE;gBAClC,IAAI,CAACtK,IAAI,CAACsK,OAAO,CAACN,OAAO,CAAE3M,MAAW,IAAI;kBACxC,IACE,IAAI,CAAC2D,UAAU,CAACgP,IAAI,CACjB7D,IAAS,IACRA,IAAI,CAACxK,KAAK,KAAKtE,MAAM,CAACsE,KAAK,IAAIwK,IAAI,CAACzB,MAAM,CAC7C,EACD;oBACArN,MAAM,CAACgT,gBAAgB,GAAG,IAAI;oBAC9BhT,MAAM,CAACqN,MAAM,GAAG,IAAI;kBACtB,CAAC,MAAM;oBACLrN,MAAM,CAACqN,MAAM,GAAG,KAAK;kBACvB;gBACF,CAAC,CAAC;cACJ;cAEA;cACA,IAAI,CAACwE,oBAAoB,CAAC,IAAI,CAAChO,iBAAiB,CAAC;cAEjD;cACA,IAAI,CAACnB,kBAAkB,CAACiP,kBAAkB,CAAC;gBACzCrE,QAAQ,EAAE,OAAO;gBACjBC,MAAM,EAAE,IAAI,CAACxK,SAAS,CAACsF,MAAM;gBAC7B1E,UAAU,EAAE,IAAI,CAACA,UAAU;gBAC3BC,aAAa,EAAE,IAAI,CAACC;eACrB,CAAC;YACJ;UACF,CAAC;UACD8E,KAAK,EAAGA,KAAK,IAAI;YACfnC,OAAO,CAACmC,KAAK,CACX,2DAA2D,EAC3DA,KAAK,CACN;YACD,IAAI,CAACsK,4BAA4B,EAAE;UACrC;SACD,CAAC;MACN,CAAC,MAAM;QACL;QACA,IAAI,CAACA,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,OAAOtK,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACsK,4BAA4B,EAAE;IACrC;EACF;EAEA;;;EAGQA,4BAA4BA,CAAA;IAClC,IAAI;MACF,MAAMC,WAAW,GAAG,IAAI,CAACxQ,kBAAkB,CAACyQ,mBAAmB,CAC7D,OAAO,EACP,IAAI,CAACpQ,SAAS,EAAEyM,MAAM,IAAI,CAAC,CAC5B;MACD,IAAI0D,WAAW,EAAE;QACf,IAAI,CAACxP,SAAS,GAAGwP,WAAW;QAC5B,IAAI,CAACvP,UAAU,GAAGuP,WAAW,CAACvP,UAAU,IAAI,EAAE;QAC9C,IAAI,CAACE,iBAAiB,GAAGqP,WAAW,CAACtP,aAAa,IAAI,EAAE;QACxD,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAEhE;QACA,IAAI,IAAI,CAAC1B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACsK,OAAO,EAAE;UAClC,IAAI,CAACtK,IAAI,CAACsK,OAAO,CAACN,OAAO,CAAE3M,MAAW,IAAI;YACxC,IACE,IAAI,CAAC2D,UAAU,CAACgP,IAAI,CACjB7D,IAAS,IAAKA,IAAI,CAACxK,KAAK,KAAKtE,MAAM,CAACsE,KAAK,IAAIwK,IAAI,CAACzB,MAAM,CAC1D,EACD;cACArN,MAAM,CAACgT,gBAAgB,GAAG,IAAI;cAC9BhT,MAAM,CAACqN,MAAM,GAAG,IAAI;YACtB,CAAC,MAAM;cACLrN,MAAM,CAACqN,MAAM,GAAG,KAAK;YACvB;UACF,CAAC,CAAC;QACJ;QAEA;QACA,IAAI,CAACwE,oBAAoB,CAAC,IAAI,CAAChO,iBAAiB,CAAC;MACnD;IACF,CAAC,CAAC,OAAO8E,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACnE;EACF;;qCA1mDW3G,2BAA2B,EAAA1I,EAAA,CAAA8Z,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAha,EAAA,CAAA8Z,iBAAA,CAAA9Z,EAAA,CAAAia,iBAAA,GAAAja,EAAA,CAAA8Z,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAna,EAAA,CAAA8Z,iBAAA,CAAAI,EAAA,CAAAE,cAAA,GAAApa,EAAA,CAAA8Z,iBAAA,CAAAO,EAAA,CAAAC,QAAA,GAAAta,EAAA,CAAA8Z,iBAAA,CAAAS,EAAA,CAAAvR,UAAA,GAAAhJ,EAAA,CAAA8Z,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAAza,EAAA,CAAA8Z,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAA3a,EAAA,CAAA8Z,iBAAA,CAAAc,EAAA,CAAAC,oBAAA,GAAA7a,EAAA,CAAA8Z,iBAAA,CAAAgB,EAAA,CAAAC,kBAAA;EAAA;;UAA3BrS,2BAA2B;IAAAsS,SAAA;IAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QC3FxCnb,EAAA,CAAAkD,UAAA,IAAAmY,0CAAA,iBAAqE;QAe3Drb,EALV,CAAAC,cAAA,aAA+B,aACI,aACgC,YAC0C,YAChF,WACgD;QACjED,EAAA,CAAAE,MAAA,wBACF;QAEJF,EAFI,CAAAG,YAAA,EAAI,EACD,EACF;QAIHH,EADF,CAAAC,cAAA,aAAuC,gBAKG;QADtCD,EAAA,CAAAc,UAAA,mBAAAwa,6DAAA;UAAAtb,EAAA,CAAAO,aAAA,CAAAgb,GAAA;UAAA,OAAAvb,EAAA,CAAAa,WAAA,CAASua,GAAA,CAAAtO,MAAA,EAAQ;QAAA,EAAC;QAElB9M,EAAA,CAAAoB,SAAA,aAAqC;QACrCpB,EAAA,CAAAE,MAAA,cACF;QAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;QAGNH,EADA,CAAAC,cAAA,eAA4B,yBA8B3B;QAFCD,EAZA,CAAAc,UAAA,2BAAA0a,0EAAAlb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAgb,GAAA;UAAA,OAAAvb,EAAA,CAAAa,WAAA,CAAiBua,GAAA,CAAArC,eAAA,CAAAzY,MAAA,CAAuB;QAAA,EAAC,6BAAAmb,4EAAAnb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAgb,GAAA;UAAA,OAAAvb,EAAA,CAAAa,WAAA,CACtBua,GAAA,CAAAzK,iBAAA,CAAArQ,MAAA,CAAyB;QAAA,EAAC,0BAAAob,yEAAApb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAgb,GAAA;UAAA,OAAAvb,EAAA,CAAAa,WAAA,CAQ7Bua,GAAA,CAAAvI,YAAA,CAAAvS,MAAA,CAAoB;QAAA,EAAC,wBAAAqb,uEAAArb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAgb,GAAA;UAAA,OAAAvb,EAAA,CAAAa,WAAA,CACvBua,GAAA,CAAA5I,UAAA,CAAAlS,MAAA,CAAkB;QAAA,EAAC,wBAAAsb,uEAAAtb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAgb,GAAA;UAAA,OAAAvb,EAAA,CAAAa,WAAA,CACnBua,GAAA,CAAA3I,YAAA,CAAAnS,MAAA,CAAoB;QAAA,EAAC,oCAAAub,mFAAAvb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAgb,GAAA;UAAA,OAAAvb,EAAA,CAAAa,WAAA,CACTua,GAAA,CAAAhC,sBAAA,CAAA9Y,MAAA,CAA8B;QAAA,EAAC;QAqezDN,EAleA,CAAAkD,UAAA,KAAA4Y,mDAAA,4BAAsC,KAAAC,mDAAA,0BAwGA,KAAAC,oDAAA,2BAgDW,KAAAC,mDAAA,0BA0UT;QAwB5Cjc,EAFE,CAAAG,YAAA,EAAa,EACP,EACF;;;QA5jBAH,EAAA,CAAAgC,UAAA,SAAAoZ,GAAA,CAAA7S,OAAA,IAAA6S,GAAA,CAAA5R,SAAA,CAA0B;QAsC5BxJ,EAAA,CAAA6B,SAAA,IAA0B;QA0B1B7B,EA1BA,CAAAgC,UAAA,SAAAoZ,GAAA,CAAA5S,iBAAA,CAA0B,aAAA4S,GAAA,CAAAlZ,IAAA,CAAAyJ,IAAA,CACJ,SAAAyP,GAAA,CAAA7P,IAAA,CACT,aAAAvL,EAAA,CAAAkc,eAAA,KAAAC,GAAA,EAAAnc,EAAA,CAAAkE,eAAA,KAAAkY,GAAA,GAOX,aAAApc,EAAA,CAAAkE,eAAA,KAAAmY,GAAA,EACgD,oBAC/B,eAAArc,EAAA,CAAAkE,eAAA,KAAAoY,GAAA,EACoC,qBAGnC,oBAED,eACL,SAAAlB,GAAA,CAAAlZ,IAAA,CAAA0J,UAAA,GAAAwP,GAAA,CAAAlZ,IAAA,CAAAyJ,IAAA,CACsB,WAAAyP,GAAA,CAAAxR,MAAA,CACnB,eAAA5J,EAAA,CAAAkE,eAAA,KAAAqY,GAAA,EACc,kBAKd;QA0JgBvc,EAAA,CAAA6B,SAAA,GAAc;QAAd7B,EAAA,CAAAgC,UAAA,YAAAoZ,GAAA,CAAA3Q,WAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}