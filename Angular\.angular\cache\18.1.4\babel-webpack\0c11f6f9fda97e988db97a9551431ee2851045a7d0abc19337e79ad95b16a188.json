{"ast": null, "code": "import { each } from 'lodash';\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/permits.service\";\nimport * as i3 from \"../../services/http-utils.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nimport * as i5 from \"../../services/kendo-column.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"../../services/app.service\";\nimport * as i8 from \"../../services/exceljs.service\";\nimport * as i9 from \"src/app/_metronic/layout/core/page-info.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"@progress/kendo-angular-grid\";\nimport * as i13 from \"@progress/kendo-angular-inputs\";\nimport * as i14 from \"@progress/kendo-angular-dropdowns\";\nimport * as i15 from \"@progress/kendo-angular-buttons\";\nimport * as i16 from \"ng-inline-svg-2\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  text: \"All\",\n  value: null\n});\nfunction PermitListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 11);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"kendo-textbox\", 13);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function PermitListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function PermitListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"span\", 15);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 18);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"kendo-dropdownbutton\", 21);\n    i0.ɵɵlistener(\"itemClick\", function PermitListComponent_ng_template_4_Template_kendo_dropdownbutton_itemClick_13_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onExportClick($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(15, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(17, \"i\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"data\", ctx_r2.exportOptions);\n  }\n}\nfunction PermitListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29)(3, \"label\", 30);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 29)(7, \"label\", 30);\n    i0.ɵɵtext(8, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.category, $event) || (ctx_r2.appliedFilters.category = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 33)(11, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵtext(12, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAdvancedFilters());\n    });\n    i0.ɵɵtext(14, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showAdvancedFilters = false);\n    });\n    i0.ɵɵtext(16, \" Hide Filters \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.categories);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.category);\n  }\n}\nfunction PermitListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PermitListComponent_ng_template_5_div_0_Template, 17, 4, \"div\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction PermitListComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showAdvancedFilters = !ctx_r2.showAdvancedFilters);\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.showAdvancedFilters ? \"Hide\" : \"Show\", \" Advanced Filters \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"a\", 57);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener() {\n      const dataItem_r7 = i0.ɵɵrestoreView(_r6).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r7.permitId));\n    });\n    i0.ɵɵelement(2, \"i\", 58);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 54);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template, 3, 0, \"ng-template\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 80)(\"sortable\", false)(\"filterable\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r8.permitNumber, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 61);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r9 = ctx.$implicit;\n    const column_r10 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r10)(\"filter\", filter_r9)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 59);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_2_Template, 2, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 180);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r11.permitName, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 61);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r12 = ctx.$implicit;\n    const column_r13 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r13)(\"filter\", filter_r12)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 62);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_2_Template, 2, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 180);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r14 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r14.projectName, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 61);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r15 = ctx.$implicit;\n    const column_r16 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r16)(\"filter\", filter_r15)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 63);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_2_Template, 2, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 180);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r17 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r17.permitType, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 61);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r18 = ctx.$implicit;\n    const column_r19 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r19)(\"filter\", filter_r18)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 64);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_2_Template, 2, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 200);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r20 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getCategoryClass(dataItem_r20.permitCategory));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r20.permitCategory, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r21 = i0.ɵɵrestoreView(_r21);\n      const filterService_r23 = ctx_r21.filterService;\n      const column_r24 = ctx_r21.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r24.field, filterService_r23));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r25 = ctx.$implicit;\n    const column_r24 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r24)(\"filter\", filter_r25)(\"data\", ctx_r2.advancedFilterOptions.categories);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c6));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 65);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_1_Template, 2, 2, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template, 1, 7, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 120);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r26 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r26.internalReviewStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r26.internalReviewStatus, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.internalReviewStatus, $event) || (ctx_r2.appliedFilters.internalReviewStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r27 = i0.ɵɵrestoreView(_r27);\n      const filterService_r29 = ctx_r27.filterService;\n      const column_r30 = ctx_r27.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r30.field, filterService_r29));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r31 = ctx.$implicit;\n    const column_r30 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r30)(\"filter\", filter_r31)(\"data\", ctx_r2.advancedFilterOptions.categoriess);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.internalReviewStatus);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c6));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 68);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_1_Template, 2, 2, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template, 1, 7, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 150);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r32 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r32.permitStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r32.permitStatus, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r33 = i0.ɵɵrestoreView(_r33);\n      const filterService_r35 = ctx_r33.filterService;\n      const column_r36 = ctx_r33.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r36.field, filterService_r35));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r37 = ctx.$implicit;\n    const column_r36 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r36)(\"filter\", filter_r37)(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c6));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 69);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_1_Template, 2, 2, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template, 1, 7, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 150);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r38 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r38.location, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 61);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r39 = ctx.$implicit;\n    const column_r40 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r40)(\"filter\", filter_r39)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 70);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template, 2, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 200);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r41 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r41.permitAppliedDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 72);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r42 = ctx.$implicit;\n    const column_r43 = ctx.column;\n    const filterService_r44 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r43)(\"filter\", filter_r42)(\"filterService\", filterService_r44);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 71);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_2_Template, 7, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r45 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r45.permitExpirationDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 72);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r46 = ctx.$implicit;\n    const column_r47 = ctx.column;\n    const filterService_r48 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r47)(\"filter\", filter_r46)(\"filterService\", filterService_r48);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 73);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_2_Template, 7, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r49 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r49.permitFinalDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 72);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r50 = ctx.$implicit;\n    const column_r51 = ctx.column;\n    const filterService_r52 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r51)(\"filter\", filter_r50)(\"filterService\", filterService_r52);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 74);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_2_Template, 7, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r53 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r53.permitCompleteDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 72);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r54 = ctx.$implicit;\n    const column_r55 = ctx.column;\n    const filterService_r56 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r55)(\"filter\", filter_r54)(\"filterService\", filterService_r56);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 75);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_2_Template, 7, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r57 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r57.attentionReason || \"\", \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 61);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r58 = ctx.$implicit;\n    const column_r59 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r59)(\"filter\", filter_r58)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 76);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_2_Template, 2, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 180);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r60 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r60.lastUpdatedDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 61);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r61 = ctx.$implicit;\n    const column_r62 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r62)(\"filter\", filter_r61)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 77);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_1_Template, 1, 1, \"ng-template\", 55)(2, PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_2_Template, 2, 3, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_1_Template, 2, 3, \"kendo-grid-column\", 39)(2, PermitListComponent_ng_container_7_kendo_grid_column_2_Template, 3, 1, \"kendo-grid-column\", 40)(3, PermitListComponent_ng_container_7_kendo_grid_column_3_Template, 3, 1, \"kendo-grid-column\", 41)(4, PermitListComponent_ng_container_7_kendo_grid_column_4_Template, 3, 1, \"kendo-grid-column\", 42)(5, PermitListComponent_ng_container_7_kendo_grid_column_5_Template, 3, 1, \"kendo-grid-column\", 43)(6, PermitListComponent_ng_container_7_kendo_grid_column_6_Template, 3, 1, \"kendo-grid-column\", 44)(7, PermitListComponent_ng_container_7_kendo_grid_column_7_Template, 3, 1, \"kendo-grid-column\", 45)(8, PermitListComponent_ng_container_7_kendo_grid_column_8_Template, 3, 1, \"kendo-grid-column\", 46)(9, PermitListComponent_ng_container_7_kendo_grid_column_9_Template, 3, 1, \"kendo-grid-column\", 47)(10, PermitListComponent_ng_container_7_kendo_grid_column_10_Template, 3, 1, \"kendo-grid-column\", 48)(11, PermitListComponent_ng_container_7_kendo_grid_column_11_Template, 3, 1, \"kendo-grid-column\", 49)(12, PermitListComponent_ng_container_7_kendo_grid_column_12_Template, 3, 1, \"kendo-grid-column\", 50)(13, PermitListComponent_ng_container_7_kendo_grid_column_13_Template, 3, 1, \"kendo-grid-column\", 51)(14, PermitListComponent_ng_container_7_kendo_grid_column_14_Template, 3, 1, \"kendo-grid-column\", 52)(15, PermitListComponent_ng_container_7_kendo_grid_column_15_Template, 3, 1, \"kendo-grid-column\", 53);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r63 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitNumber\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"projectName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitType\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitCategory\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"internalReviewStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"location\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitAppliedDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitExpirationDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitFinalDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"permitCompleteDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"attentionReason\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r63 === \"lastUpdatedDate\");\n  }\n}\nfunction PermitListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 15);\n    i0.ɵɵelement(2, \"i\", 79);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Permits Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No permits match your current search criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.clearSearch();\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵtext(8, \" Clear Search \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class PermitListComponent {\n  router;\n  route;\n  permitsService;\n  httpUtilService;\n  customLayoutUtilsService;\n  kendoColumnService;\n  modalService;\n  cdr;\n  appService;\n  ExceljsService;\n  pageInfo;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  onMenuDropdownChange(value, field, filterService) {\n    const next = value == null ? {\n      filters: [],\n      logic: 'and'\n    } : {\n      filters: [{\n        field,\n        operator: 'eq',\n        value\n      }],\n      logic: 'and'\n    };\n    filterService.filter(next);\n  }\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Active',\n    value: 'Active'\n  }, {\n    text: 'Inactive',\n    value: 'Inactive'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Requires Resubmit',\n      value: 'Requires Resubmit'\n    }, {\n      text: 'On Hold',\n      value: 'On Hold'\n    }, {\n      text: 'Approved',\n      value: 'Approved'\n    }, {\n      text: 'Pending',\n      value: 'Pending'\n    }, {\n      text: 'Canceled',\n      value: 'Canceled'\n    }, {\n      text: 'Complete',\n      value: 'Complete'\n    }, {\n      text: 'Expired',\n      value: 'Expired'\n    }, {\n      text: 'Fees Due',\n      value: 'Fees Due'\n    }, {\n      text: 'In Review',\n      value: 'In Review'\n    }, {\n      text: 'Issued',\n      value: 'Issued'\n    }, {\n      text: 'Requires Resubmit for Prescreen',\n      value: 'Requires Resubmit for Prescreen'\n    }, {\n      text: 'Submitted - Online',\n      value: 'Submitted - Online'\n    }, {\n      text: 'Void',\n      value: 'Void'\n    }],\n    categories: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Primary',\n      value: 'Primary'\n    }, {\n      text: 'Sub Permit',\n      value: 'Sub Permit'\n    }, {\n      text: 'Industrial',\n      value: 'Industrial'\n    }, {\n      text: 'Municipal',\n      value: 'Municipal'\n    }, {\n      text: 'Environmental',\n      value: 'Environmental'\n    }],\n    categoriess: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Approved',\n      value: 'Approved'\n    }, {\n      text: 'Pacifica Verification',\n      value: 'Pacifica Verification'\n    }, {\n      text: 'Dis-Approved',\n      value: 'Dis-Approved'\n    }, {\n      text: 'Pending',\n      value: 'Pending'\n    }, {\n      text: 'Not Required',\n      value: 'Not Required'\n    }, {\n      text: 'In Review',\n      value: 'In Review'\n    }, {\n      text: '1 Cycle Completed',\n      value: '1 Cycle Completed'\n    }\n    // 'Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed'\n    ]\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // Column visibility system\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration\n  gridColumns = [];\n  defaultColumns = [];\n  fixedColumns = [];\n  draggableColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Enhanced Columns with Kendo UI features - adapted for permits\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Action',\n    width: 100,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'permitName',\n    title: 'Permit/Sub Project Name',\n    width: 180,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  }, {\n    field: 'permitNumber',\n    title: 'Permit #',\n    width: 180,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 3\n  }, {\n    field: 'projectName',\n    title: 'Project Name',\n    width: 180,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 4\n  }, {\n    field: 'permitCategory',\n    title: 'Category',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 5\n  }, {\n    field: 'permitType',\n    title: 'Permit Type',\n    width: 200,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 6\n  }, {\n    field: 'permitStatus',\n    title: 'Permit Status',\n    width: 150,\n    isFixed: false,\n    type: 'status',\n    filterable: true,\n    order: 7\n  }, {\n    field: 'internalReviewStatus',\n    title: 'Internal Review Status',\n    width: 150,\n    isFixed: false,\n    type: 'status',\n    filterable: true,\n    order: 8\n  }, {\n    field: 'location',\n    title: 'Location',\n    width: 200,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 9\n  }, {\n    field: 'permitAppliedDate',\n    title: 'Applied Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 10\n  }, {\n    field: 'permitExpirationDate',\n    title: 'Expiration Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 11\n  }, {\n    field: 'permitFinalDate',\n    title: 'Final Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 12\n  }, {\n    field: 'permitCompleteDate',\n    title: 'Complete Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 13\n  }, {\n    field: 'attentionReason',\n    title: 'Attention Reason',\n    width: 180,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 14\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 15\n  }];\n  statusList = [{\n    text: 'Approved',\n    value: 'Approved'\n  }, {\n    text: 'Pending',\n    value: 'Pending'\n  }, {\n    text: 'Rejected',\n    value: 'Rejected'\n  }];\n  // State\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  page = {\n    size: 15,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  // Selection\n  selectedRows = [];\n  isAllSelected = false;\n  // Export options\n  exportOptions = [{\n    text: 'All',\n    value: 'all'\n  }, {\n    text: 'Page Results',\n    value: 'selected'\n  }];\n  columnJSONFormat;\n  permitId;\n  singlePermit;\n  resetToDefaultSettings() {\n    console.log('Resetting to default settings...');\n    // Reset column visibility - show all columns\n    this.hiddenFields = [];\n    this.gridColumns = [...this.defaultColumns];\n    this.kendoColOrder = [...this.defaultColumns];\n    // Reset sort state to default\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    // Reset page state\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Reset all filters - clear everything\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    // Reset advanced filters\n    this.appliedFilters = {};\n    // Reset search\n    this.searchData = '';\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n    console.log('Reset completed:', {\n      hiddenFields: this.hiddenFields,\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      sort: this.sort,\n      filter: this.filter,\n      searchData: this.searchData\n    });\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset column visibility - show all columns\n      this.grid.columns.forEach(column => {\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Trigger change detection\n    this.cdr.detectChanges();\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        // Also try to reset the grid state completely\n        this.grid.reset();\n      }, 100);\n    }\n    // Reload data with clean state\n    this.loadTable();\n  }\n  // private saveColumnState(): void {\n  //   try {\n  //     const columnState = {\n  //       columns: this.gridColumns,\n  //       hidden: this.hiddenFields,\n  //       order: this.kendoColOrder,\n  //     };\n  //     localStorage.setItem(\n  //       `${this.GRID_STATE_KEY}-columns`,\n  //       JSON.stringify(columnState)\n  //     );\n  //   } catch (error) {\n  //     console.warn('Error saving column state:', error);\n  //   }\n  // }\n  constructor(router, route, permitsService, httpUtilService, customLayoutUtilsService, kendoColumnService, modalService, cdr, appService, ExceljsService, pageInfo) {\n    this.router = router;\n    this.route = route;\n    this.permitsService = permitsService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.kendoColumnService = kendoColumnService;\n    this.modalService = modalService;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.ExceljsService = ExceljsService;\n    this.pageInfo = pageInfo;\n    // Initialize search subscription\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n      // Set loading state for search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    });\n  }\n  ngOnInit() {\n    this.pageInfo.updateTitle('Permits');\n    this.initializeComponent();\n    this.loadTable();\n  }\n  ngAfterViewInit() {\n    this.initializeGrid();\n  }\n  ngOnDestroy() {\n    if (this.searchSubscription) {\n      this.searchTerms.complete();\n    }\n  }\n  initializeComponent() {\n    // Get login user info\n    this.loginUser = this.appService.getLoggedInUser();\n    // Initialize column visibility system\n    this.initializeColumnVisibility();\n  }\n  initializeColumnVisibility() {\n    // Set up column arrays first\n    this.setupColumnArrays();\n    // Try to load from local storage first\n    const savedConfig = this.kendoColumnService.getFromLocalStorage('permits', this.loginUser.userId);\n    if (savedConfig) {\n      // Load saved settings from local storage\n      this.kendoHide = savedConfig.hiddenData || [];\n      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.kendoColOrder];\n    } else {\n      // Initialize with default values\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.defaultColumns];\n    }\n    // Apply settings\n    this.applySavedColumnSettings();\n  }\n  loadColumnSettingsFromServer() {\n    const config = {\n      pageName: 'permits',\n      userID: this.loginUser.userId\n    };\n    this.kendoColumnService.getHideFields(config).subscribe({\n      next: response => {\n        if (response.isFault === false && response.Data) {\n          // Parse the saved settings\n          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.kendoColOrder];\n          // Apply the settings\n          this.applySavedColumnSettings();\n          console.log('Column settings loaded from server:', {\n            kendoHide: this.kendoHide,\n            kendoColOrder: this.kendoColOrder\n          });\n        } else {\n          // No saved settings, use defaults\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      },\n      error: error => {\n        console.error('Error loading column settings:', error);\n        // Use defaults on error\n        this.kendoHide = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n        this.applySavedColumnSettings();\n      }\n    });\n  }\n  setupColumnArrays() {\n    this.gridColumns = this.gridColumnConfig.map(col => col.field);\n    this.defaultColumns = [...this.gridColumns];\n    this.fixedColumns = this.gridColumnConfig.filter(col => col.isFixed).map(col => col.field);\n    this.draggableColumns = this.gridColumnConfig.filter(col => !col.isFixed).map(col => col.field);\n  }\n  initializeGrid() {\n    if (this.grid) {\n      // Apply saved column settings\n      this.applySavedColumnSettings();\n    }\n  }\n  applySavedColumnSettings() {\n    if (this.kendoHide && this.kendoHide.length > 0) {\n      this.hiddenFields = this.kendoHide;\n    }\n    if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n      // Apply column order\n      this.gridColumnConfig.sort((a, b) => {\n        const aOrder = this.kendoColOrder.indexOf(a.field);\n        const bOrder = this.kendoColOrder.indexOf(b.field);\n        return aOrder - bOrder;\n      });\n    }\n  }\n  // Load table data\n  loadTable() {\n    this.loadTableWithKendoEndpoint();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Safety timeout to prevent loader from getting stuck\n    const loadingTimeout = setTimeout(() => {\n      console.warn('Loading timeout reached, resetting loading states');\n      this.resetLoadingStates();\n    }, 30000); // 30 seconds timeout\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId\n    };\n    console.log('Loading table with state:', state);\n    this.permitsService.getPermitsForKendoGrid(state).subscribe({\n      next: data => {\n        // Clear the safety timeout since we got a response\n        clearTimeout(loadingTimeout);\n        console.log('API Response:', data);\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          // Check if this is an authentication error\n          if (data.responseData?.status === 401 || data.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n          this.handleEmptyResponse();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const permitData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = permitData.length !== 0;\n          this.serverSideRowData = permitData;\n          this.gridData = this.serverSideRowData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n          // Create a data source with total count for Kendo Grid\n          this.gridData = {\n            data: permitData,\n            total: total\n          };\n          console.log('this.serverSideRowData ', this.serverSideRowData);\n          console.log('this.gridData ', this.gridData);\n          console.log('this.IsListHasValue ', this.IsListHasValue);\n          console.log('this.page ', this.page);\n          console.log('Total elements set to:', this.page.totalElements);\n          console.log('Total pages calculated:', this.page.totalPages);\n          console.log('Current skip:', this.skip, 'Page size:', this.page.size);\n          console.log('Expected items range:', this.skip + 1, '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n          // Debug grid state after data load\n          setTimeout(() => {\n            if (this.grid) {\n              console.log('Grid total:', this.grid.total);\n              console.log('Grid pageSize:', this.grid.pageSize);\n              console.log('Grid skip:', this.grid.skip);\n              console.log('Grid pageIndex:', this.grid.pageIndex);\n              console.log('Grid data length:', this.grid.data ? this.grid.data.length : 'no data');\n              // Force grid to update its total and maintain pagination state\n              this.grid.total = this.page.totalElements;\n              this.grid.skip = this.skip;\n              this.grid.pageIndex = this.page.pageNumber;\n              console.log('Forced grid total to:', this.grid.total, 'skip to:', this.skip, 'pageIndex to:', this.page.pageNumber);\n            }\n          }, 100);\n          this.cdr.markForCheck();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      },\n      error: error => {\n        // Clear the safety timeout since we got an error\n        clearTimeout(loadingTimeout);\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        // Check if this is an authentication error\n        if (error && typeof error === 'object' && 'status' in error) {\n          const httpError = error;\n          if (httpError.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n        }\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        // Clear the safety timeout\n        clearTimeout(loadingTimeout);\n        // Ensure loading states are reset in complete block as well\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      }\n    });\n  }\n  handleEmptyResponse() {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n    // Ensure loading states are reset when handling empty response\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  // Method to manually reset loading states if they get stuck\n  resetLoadingStates() {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  // Public method to manually refresh the grid and reset any stuck loading states\n  refreshGrid() {\n    console.log('Manually refreshing grid...');\n    this.resetLoadingStates();\n    this.loadTable();\n  }\n  // Search functionality\n  onSearchKeyDown(event) {\n    if (event.key === 'Enter') {\n      this.loadTable();\n    }\n  }\n  onSearchChange() {\n    console.log('Search changed:', this.searchData);\n    // Trigger search with debounce\n    this.searchTerms.next(this.searchData || '');\n  }\n  applySearch() {\n    this.loadTable();\n  }\n  clearSearch() {\n    this.searchTerms.next(this.searchData);\n  }\n  // Filter functionality\n  filterChange(filter) {\n    console.log('filter', filter);\n    this.filter = filter;\n    this.loadTable();\n  }\n  applyAdvancedFilters() {\n    console.log('yes it came here');\n    // Apply status filter\n    if (this.appliedFilters.status) {\n      this.filter.filters = this.filter.filters.filter(f => {\n        if ('field' in f) {\n          return f.field !== 'permitStatus';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'permitStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    // Apply category filter\n    if (this.appliedFilters.category) {\n      this.filter.filters = this.filter.filters.filter(f => {\n        if ('field' in f) {\n          return f.field !== 'permitCategory';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'permitCategory',\n        operator: 'eq',\n        value: this.appliedFilters.category\n      });\n    }\n    this.loadTable();\n  }\n  clearAdvancedFilters() {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n    this.loadTable();\n  }\n  // Sorting functionality\n  onSortChange(sort) {\n    console.log('Sort change triggered:', sort);\n    // Handle empty sort array (normalize/unsort case)\n    const incomingSort = Array.isArray(sort) ? sort : [];\n    this.sort = incomingSort.length > 0 ? incomingSort : [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    // Update page order fields for consistency\n    this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n    this.page.orderDir = this.sort[0].dir || 'desc';\n    console.log('Final sort state:', this.sort);\n    console.log('Page order:', {\n      orderBy: this.page.orderBy,\n      orderDir: this.page.orderDir\n    });\n    this.loadTable();\n  }\n  // Pagination functionality\n  pageChange(event) {\n    console.log('Page change event:', event);\n    console.log('Current page size:', this.page.size);\n    console.log('Event page size:', event.pageSize);\n    console.log('Event page index:', event.pageIndex);\n    console.log('Event skip:', event.skip);\n    // Use Kendo's provided values as source of truth\n    this.skip = event.skip;\n    this.page.size = event.take || this.page.size;\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\n    console.log('Updated skip:', this.skip, 'page size:', this.page.size, 'page number:', this.page.pageNumber);\n    console.log('Expected items range:', this.skip + 1, '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n    this.loadTable();\n  }\n  // Handle page size change specifically\n  onPageSizeChange(event) {\n    console.log('Page size change event:', event);\n    console.log('New page size:', event.pageSize);\n    if (event.pageSize && event.pageSize !== this.page.size) {\n      console.log('Page size changing from', this.page.size, 'to', event.pageSize);\n      this.page.size = event.pageSize;\n      this.page.pageNumber = 0; // Reset to first page when changing page size\n      this.skip = 0;\n      // Recalculate total pages based on new page size\n      if (this.page.totalElements > 0) {\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        console.log('Recalculated total pages:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n      }\n      // Force grid to update its page size and reset to first page\n      if (this.grid) {\n        this.grid.pageSize = this.page.size;\n        this.grid.skip = 0;\n        this.grid.pageIndex = 0;\n        this.grid.total = this.page.totalElements;\n        console.log('Updated grid total to:', this.grid.total);\n      }\n      console.log('Updated page size:', this.page.size, 'skip:', this.skip);\n      this.loadTable();\n    }\n  }\n  // Handle data state changes (includes page size changes)\n  onDataStateChange(event) {\n    console.log('Data state change event:', event);\n    // Check if page size changed\n    if (event.take && event.take !== this.page.size) {\n      console.log('Page size changing via data state from', this.page.size, 'to', event.take);\n      this.page.size = event.take;\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Recalculate total pages based on new page size\n      if (this.page.totalElements > 0) {\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        console.log('Recalculated total pages via data state:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n      }\n      // Force grid to update its page size and reset to first page\n      if (this.grid) {\n        this.grid.pageSize = this.page.size;\n        this.grid.skip = 0;\n        this.grid.pageIndex = 0;\n        this.grid.total = this.page.totalElements;\n        console.log('Updated grid total via data state to:', this.grid.total);\n      }\n      console.log('Updated page size via data state:', this.page.size, 'skip:', this.skip);\n      this.loadTable();\n    }\n  }\n  // Column management\n  onColumnReorder(event) {\n    // Handle column reordering\n    const reorderedColumns = event.columns.map(col => col.field);\n    this.kendoColOrder = reorderedColumns;\n  }\n  updateColumnVisibility(event) {\n    // Handle column visibility changes\n    const hiddenColumns = event.hiddenColumns || [];\n    this.hiddenFields = hiddenColumns;\n  }\n  // Selection functionality\n  onSelectionChange(event) {\n    this.selectedRows = event.selectedRows || [];\n    this.isAllSelected = this.selectedRows.length === this.serverSideRowData.length;\n  }\n  selectAll() {\n    if (this.isAllSelected) {\n      this.selectedRows = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedRows = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n  }\n  // Grid expansion\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Export functionality\n  onExportClick(event) {\n    const selectedOption = event.value; // Get selected option\n    let prdItems = [];\n    if (selectedOption === 'selected') {\n      prdItems = this.serverSideRowData;\n      // declare the title and header data for excel\n      // get the data for excel in a array format\n      this.exportExcel(prdItems);\n    } else if (selectedOption === 'all') {\n      const queryparamsExcel = {\n        pageSize: this.page.totalElements,\n        sortOrder: this.page.orderDir,\n        sortField: this.page.orderBy,\n        pageNumber: this.page.pageNumber\n        // filter: this.filterConfiguration()\n      };\n      // Enable loading indicator\n      this.httpUtilService.loadingSubject.next(true);\n      // API call\n      this.permitsService.getAllPermits(queryparamsExcel)\n      // .pipe(map((data: any) => data as any))\n      .subscribe(data => {\n        // Disable loading indicator\n        this.httpUtilService.loadingSubject.next(false);\n        if (data.isFault) {\n          this.IsListHasValue = false;\n          this.cdr.markForCheck();\n          return; // Exit early if the response has a fault\n        }\n        this.IsListHasValue = true;\n        prdItems = data.responseData.data || [];\n        this.cdr.detectChanges(); // Manually trigger UI update\n        this.exportExcel(prdItems);\n      });\n    }\n  }\n  exportExcel(listOfItems) {\n    // Define local variables for the items and current date\n    let prdItems = listOfItems;\n    let currentDate = this.appService.formatMonthDate(new Date());\n    console.log('prdItems', prdItems);\n    // Check if the data exists and is not empty\n    if (prdItems !== undefined && prdItems.length > 0) {\n      // Define the title for the Excel file\n      const tableTitle = 'Events';\n      // Filter out hidden columns and sort by order\n      // const visibleColumns = this.columnJSONFormat\n      //   .filter((col: any) => !col.hidden)\n      //   .sort((a: any, b: any) => a.order - b.order);\n      // Create header from visible columns\n      const headerArray = ['Permit Number', 'Permit Type', 'Category', 'Status', 'Location', 'Project Name', 'Applied Date', 'Expiration Date', 'Attention Reason'];\n      // ...visibleColumns.map((col: any) => col.title),\n      // Define which columns should have currency and percentage formatting\n      // const currencyColumns: any = [\n      //   'Pending',\n      //   'ACAT',\n      //   'Annuity',\n      //   'AUM',\n      //   'Total Assets',\n      //   'Event Cost',\n      //   'Gross Profit',\n      // ].filter((col) => headerArray.includes(col));\n      const percentageColumns = [];\n      // Get the data for excel in an array format\n      const respResult = [];\n      // Prepare the data for export based on visible columns\n      each(prdItems, prdItem => {\n        // Create an array with the same length as headerArray\n        const respData = Array(headerArray.length).fill(null);\n        respData[0] = prdItem.eventDescription;\n        respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n        // Fill in data for each visible column\n        headerArray.forEach((col, i) => {\n          const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n          switch (col) {\n            case 'Permit Number':\n              respData[adjustedIndex] = prdItem.permitNumber;\n              break;\n            case 'Permit Type':\n              respData[adjustedIndex] = prdItem.permitType;\n              break;\n            case 'Category':\n              respData[adjustedIndex] = prdItem.permitCategory;\n              break;\n            case 'Status':\n              respData[adjustedIndex] = prdItem.permitStatus;\n              break;\n            case 'Location':\n              respData[adjustedIndex] = prdItem.location;\n              break;\n            case 'Project Name':\n              respData[adjustedIndex] = prdItem.projectName;\n              break;\n            case 'Applied Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitAppliedDate);\n              break;\n            case 'Expiration Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitExpirationDate);\n              break;\n            case 'Attention Reason':\n              respData[adjustedIndex] = prdItem.attentionReason;\n              break;\n            // case 'kept_appointments':\n            //   respData[adjustedIndex] = prdItem.kept_appointments;\n            //   break;\n            // case 'kept_appt_ratio':\n            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n            //   break;\n            // case 'apptKeptNo':\n            //   respData[adjustedIndex] = prdItem.apptKeptNo;\n            //   break;\n            // case 'has_assets':\n            //   respData[adjustedIndex] = prdItem.has_assets;\n            //   break;\n            // case 'prospects_closed':\n            //   respData[adjustedIndex] = prdItem.prospects_closed;\n            //   break;\n            // case 'closing_ratio':\n            //   respData[adjustedIndex] = prdItem.closing_ratio;\n            //   break;\n            // case 'totalPending':\n            //   respData[adjustedIndex] = prdItem.totalPending;\n            //   break;\n            // case 'acatproduction':\n            //   respData[adjustedIndex] = prdItem.acatproduction;\n            //   break;\n            // case 'annuityproduction':\n            //   respData[adjustedIndex] = prdItem.annuityproduction;\n            //   break;\n            // case 'aumproduction':\n            //   respData[adjustedIndex] = prdItem.aumproduction;\n            //   break;\n            // case 'totalAssets':\n            //   respData[adjustedIndex] = prdItem.totalAssets;\n            //   break;\n            // case 'eventCost':\n            //   respData[adjustedIndex] = prdItem.eventCost;\n            //   break;\n            // case 'grossProfit':\n            //   respData[adjustedIndex] = prdItem.grossProfit;\n            //   break;\n            // case 'status':\n            //   respData[adjustedIndex] = prdItem.status;\n            //   break;\n          }\n        });\n        respResult.push(respData);\n      });\n      // Define column sizes for the Excel file\n      const colSize = headerArray.map((header, index) => ({\n        id: index + 1,\n        width: 20\n      }));\n      // Generate the Excel file using the exceljsService\n      this.ExceljsService.generateExcel(tableTitle, headerArray, respResult, colSize\n      // currencyColumns,\n      // percentageColumns\n      );\n    } else {\n      const message = 'There are no records available to export.';\n      // this.layoutUtilService.showError(message, '');\n    }\n  }\n  // public onExportClick(event: any): void {\n  //   const exportType = event.item.value;\n  //   let selectedIds: number[] = [];\n  //   switch (exportType) {\n  //     case 'selected':\n  //       selectedIds = this.selectedRows.map((row) => row.permitId);\n  //       if (selectedIds.length === 0) {\n  //         //alert('Please select permits to export');\n  //         return;\n  //       }\n  //       break;\n  //     case 'filtered':\n  //       // Export filtered data\n  //       break;\n  //     case 'all':\n  //     default:\n  //       // Export all data\n  //       break;\n  //   }\n  //   this.exportPermits(exportType, selectedIds);\n  // }\n  // private exportPermits(exportType: string, selectedIds: number[]): void {\n  //   this.permitsService.exportPermits(exportType, selectedIds).subscribe({\n  //     next: (response: any) => {\n  //       if (response.data) {\n  //         const blob = new Blob([response.data], {\n  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  //         });\n  //         saveAs(\n  //           blob,\n  //           `permits_${exportType}_${\n  //             new Date().toISOString().split('T')[0]\n  //           }.xlsx`\n  //         );\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       console.error('Export error:', error);\n  //       //alert('Error exporting permits data');\n  //     },\n  //   });\n  // }\n  // Column settings management\n  saveHead() {\n    const settings = {\n      kendoHide: this.hiddenFields,\n      kendoColOrder: this.kendoColOrder,\n      kendoInitColOrder: this.kendoInitColOrder\n    };\n    // Save to local storage only\n    this.kendoColumnService.saveToLocalStorage({\n      pageName: 'permits',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    });\n    console.log('Column settings saved locally:', settings);\n    this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n    //alert('Column settings saved locally');\n  }\n  saveColumnSettingsToServer(settings) {\n    const config = {\n      pageName: 'permits',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    };\n    this.kendoColumnService.createHideFields(config).subscribe({\n      next: response => {\n        if (response.isFault === false) {\n          console.log('Column settings saved successfully:', response);\n          this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n          //alert('Column settings saved successfully');\n        } else {\n          console.error('Failed to save column settings:', response.message);\n          this.customLayoutUtilsService.showError(response.message, '');\n          //alert('Failed to save column settings: ' + response.message);\n        }\n      },\n      error: error => {\n        console.error('Error saving column settings:', error);\n        this.customLayoutUtilsService.showError('Error saving column setting', '');\n        //alert('Error saving column settings. Please try again.');\n      }\n    });\n  }\n  saveResetToServer() {\n    // First delete existing settings\n    const deleteConfig = {\n      pageName: 'permits',\n      userID: this.loginUser.userId\n    };\n    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n      next: response => {\n        console.log('Existing settings deleted:', response);\n        // Then save the reset state (all columns visible)\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      },\n      error: error => {\n        console.error('Error deleting existing settings:', error);\n        // Still try to save the reset state\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      }\n    });\n  }\n  resetTable() {\n    console.log('Resetting Kendo settings for permits');\n    // Clear all saved settings first\n    this.kendoHide = [];\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.kendoInitColOrder = [];\n    // Clear local storage\n    this.kendoColumnService.clearFromLocalStorage('permits');\n    // Reset to default settings\n    this.resetToDefaultSettings();\n    // Trigger change detection to update the template\n    this.cdr.detectChanges();\n    // Force grid refresh to show all columns\n    if (this.grid) {\n      this.grid.refresh();\n    }\n    // Show success message\n    console.log('Table reset to default settings');\n    //alert('Table reset to default settings - all columns restored');\n  }\n  // Navigation\n  add() {\n    // this.router.navigate(['/permits/add']);\n    this.edit(0);\n  }\n  view(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'permit-list'\n      }\n    });\n  }\n  edit(permitId) {\n    if (permitId == 0) {\n      const permit = this.serverSideRowData.find(p => p.permitId === permitId);\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Open the modal and load the ProjectPopup\n      const modalRef = this.modalService.open(PermitPopupComponent, NgbModalOptions);\n      // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n      modalRef.componentInstance.id = permitId;\n      modalRef.componentInstance.permit = permit;\n      // Subscribe to the modal event when data is updated\n      modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n        if (receivedEntry === true) {\n          // Reload the table data after a successful update\n          this.loadTable();\n        }\n      });\n    } else {\n      this.router.navigate(['/permits/view', permitId], {\n        queryParams: {\n          from: 'permit-list'\n        }\n      });\n    }\n  }\n  deletePop(content) {\n    this.modalService.open(content, {\n      centered: true\n    });\n  }\n  confirmDelete() {\n    console.log('Item deleted ✅');\n    // your delete logic here\n  }\n  delete(permitId) {\n    if (confirm('Are you sure you want to delete this permit?')) {\n      this.permitsService.deletePermit({\n        permitId\n      }).subscribe({\n        next: response => {\n          if (response.message) {\n            //alert('Permit deleted successfully');\n            this.customLayoutUtilsService.showSuccess('Permit deleted successfully', '');\n            this.loadTable();\n          }\n        },\n        error: error => {\n          console.error('Delete error:', error);\n          this.customLayoutUtilsService.showError('Error deleting permit', '');\n          //alert('Error deleting permit');\n        }\n      });\n    }\n  }\n  // Utility methods\n  formatDate(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const year = date.getFullYear();\n    return `${month}/${day}/${year}`;\n  }\n  getStatusClass(status) {\n    switch (status) {\n      case 'Approved':\n        return 'badge-light-success';\n      case 'Requires Resubmit':\n        return 'badge-light-warning';\n      case 'On Hold':\n        return 'badge-light-danger';\n      case 'Pending':\n        return 'badge-light-info';\n      default:\n        return 'badge-light-secondary';\n    }\n  }\n  getCategoryClass(category) {\n    return category === 'Primary' ? 'badge-light-primary' : 'badge-light-secondary';\n  }\n  syncPermits(i) {\n    this.isLoading = true;\n    this.singlePermit = i || false;\n    this.permitsService.syncPermits({\n      municipalityId: 1,\n      singlePermit: this.singlePermit,\n      autoLogin: true\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Sync response:', res);\n        // Handle wrapped response structure from interceptor\n        const responseData = res?.responseData || res;\n        if (responseData?.isFault) {\n          //alert(responseData.faultMessage || 'Failed to sync permit');\n          this.customLayoutUtilsService.showError(responseData.faultMessage || 'Failed to sync permit', '');\n        } else if (responseData?.success === false) {\n          // Handle specific error messages from the API\n          if (responseData.message === 'Permit not found in Energov system') {\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          } else if (responseData.message === 'No permits found for any keywords') {\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          } else {\n            this.customLayoutUtilsService.showError(responseData.message || 'Failed to sync permit', '');\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n          }\n        } else {\n          this.customLayoutUtilsService.showSuccess('✅ Permit synced successfully', '');\n          //alert('✅ Permit synced successfully');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        // Handle HTTP error responses\n        console.log('Error response:', err);\n        console.log('Error type:', typeof err);\n        console.log('Error keys:', Object.keys(err || {}));\n        console.log('Error status:', err?.status);\n        console.log('Error message:', err?.message);\n        console.log('Error error:', err?.error);\n        // The interceptor passes err.error to the error handler\n        // So err might actually be the response data\n        if (err?.success === false) {\n          // Handle specific error messages from the API\n          if (err.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n          }\n        } else if (err?.error?.message) {\n          if (err.error.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.error.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.error.message}`)\n            // ;\n          }\n        } else if (err?.status === 404) {\n          this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          // Handle 404 specifically for permit not found\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n        } else {\n          this.customLayoutUtilsService.showError('❌ Error syncing permit', '');\n          //alert('❌ Error syncing permit');\n        }\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  onTabActivated() {\n    // This method is called when the tab is activated\n    // You can add any specific logic here if needed\n    console.log('Permits tab activated');\n  }\n  static ɵfac = function PermitListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitListComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.PermitsService), i0.ɵɵdirectiveInject(i3.HttpUtilsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i5.KendoColumnService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.AppService), i0.ɵɵdirectiveInject(i8.ExceljsService), i0.ɵɵdirectiveInject(i9.PageInfoService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitListComponent,\n    selectors: [[\"app-permit-list\"]],\n    viewQuery: function PermitListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    decls: 9,\n    vars: 24,\n    consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"pageSizeChange\", \"dataStateChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"total\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\", \"loading\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"text\", \"Export Excel\", \"iconClass\", \"fas fa-file-excel\", \"title\", \"Export\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"itemClick\", \"data\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Category\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-6\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", \"themeColor\", \"primary\", 1, \"me-2\", 3, \"click\"], [\"kendoButton\", \"\", \"themeColor\", \"secondary\", 1, \"me-2\", 3, \"click\"], [\"kendoButton\", \"\", \"themeColor\", \"light\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Toggle Advanced Filters\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [\"field\", \"action\", \"title\", \"Actions\", 3, \"width\", \"sortable\", \"filterable\", 4, \"ngIf\"], [\"field\", \"permitNumber\", \"title\", \"Permit #\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitName\", \"title\", \"Permit/Sub Project Name\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"projectName\", \"title\", \"Project Name\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitType\", \"title\", \"Permit Type\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitCategory\", \"title\", \"Category\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"internalReviewStatus\", \"title\", \"Internal Review Status\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitStatus\", \"title\", \"Permit Status\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"location\", \"title\", \"Location\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitAppliedDate\", \"title\", \"Applied Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitExpirationDate\", \"title\", \"Expiration Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitFinalDate\", \"title\", \"Final Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitCompleteDate\", \"title\", \"Complete Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"attentionReason\", \"title\", \"Attention Reason\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"action\", \"title\", \"Actions\", 3, \"width\", \"sortable\", \"filterable\"], [\"kendoGridCellTemplate\", \"\"], [1, \"d-flex\", \"gap-1\"], [\"title\", \"View\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-pencil\", 2, \"color\", \"var(--bs-primary, #0d6efd)\", \"font-size\", \"1rem\"], [\"field\", \"permitNumber\", \"title\", \"Permit #\", 3, \"width\"], [\"kendoGridFilterMenuTemplate\", \"\"], [\"operator\", \"contains\", 3, \"column\", \"filter\", \"extra\"], [\"field\", \"permitName\", \"title\", \"Permit/Sub Project Name\", 3, \"width\"], [\"field\", \"projectName\", \"title\", \"Project Name\", 3, \"width\"], [\"field\", \"permitType\", \"title\", \"Permit Type\", 3, \"width\"], [\"field\", \"permitCategory\", \"title\", \"Category\", 3, \"width\"], [1, \"badge\", 3, \"ngClass\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"ngModelChange\", \"valueChange\", \"column\", \"filter\", \"data\", \"ngModel\", \"valuePrimitive\", \"defaultItem\"], [\"field\", \"internalReviewStatus\", \"title\", \"Internal Review Status\", 3, \"width\"], [\"field\", \"permitStatus\", \"title\", \"Permit Status\", 3, \"width\"], [\"field\", \"location\", \"title\", \"Location\", 3, \"width\"], [\"field\", \"permitAppliedDate\", \"title\", \"Applied Date\", 3, \"width\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"field\", \"permitExpirationDate\", \"title\", \"Expiration Date\", 3, \"width\"], [\"field\", \"permitFinalDate\", \"title\", \"Final Date\", 3, \"width\"], [\"field\", \"permitCompleteDate\", \"title\", \"Complete Date\", 3, \"width\"], [\"field\", \"attentionReason\", \"title\", \"Attention Reason\", 3, \"width\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\"], [1, \"text-center\", \"py-5\"], [1, \"fas\", \"fa-inbox\", \"fa-3x\", \"mb-3\"], [1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function PermitListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, PermitListComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"kendo-grid\", 3, 0);\n        i0.ɵɵlistener(\"columnReorder\", function PermitListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onColumnReorder($event));\n        })(\"selectionChange\", function PermitListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function PermitListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function PermitListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"pageSizeChange\", function PermitListComponent_Template_kendo_grid_pageSizeChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onPageSizeChange($event));\n        })(\"dataStateChange\", function PermitListComponent_Template_kendo_grid_dataStateChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDataStateChange($event));\n        })(\"sortChange\", function PermitListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function PermitListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(4, PermitListComponent_ng_template_4_Template, 18, 11, \"ng-template\", 4)(5, PermitListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 4)(6, PermitListComponent_ng_template_6_Template, 3, 1, \"ng-template\", 4)(7, PermitListComponent_ng_container_7_Template, 16, 15, \"ng-container\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, PermitListComponent_div_8_Template, 9, 0, \"div\", 6);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.gridData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(19, _c2, i0.ɵɵpureFunction0(18, _c1)))(\"total\", ctx.page.totalElements)(\"sortable\", i0.ɵɵpureFunction0(21, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(22, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(23, _c5))(\"loading\", false);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.IsListHasValue);\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i11.NgControlStatus, i11.NgModel, i12.GridComponent, i12.ToolbarTemplateDirective, i12.GridSpacerComponent, i12.ColumnComponent, i12.CellTemplateDirective, i12.ContainsFilterOperatorComponent, i12.EqualFilterOperatorComponent, i12.NotEqualFilterOperatorComponent, i12.AfterFilterOperatorComponent, i12.AfterEqFilterOperatorComponent, i12.BeforeEqFilterOperatorComponent, i12.BeforeFilterOperatorComponent, i12.StringFilterMenuComponent, i12.FilterMenuTemplateDirective, i12.DateFilterMenuComponent, i13.TextBoxComponent, i14.DropDownListComponent, i15.ButtonComponent, i15.DropDownButtonComponent, i16.InlineSVGDirective],\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 9999;\\n  background: white;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #646367;\\n  box-shadow: 0 0 6px rgba(57, 58, 58, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 6px;\\n}\\n\\n.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  font-size: 0.75rem !important;\\n  padding: 0.25rem 0.5rem !important;\\n  background-color: #6c757d !important;\\n  border-color: #6c757d !important;\\n  color: #fff !important;\\n  margin-right: 0.5rem !important;\\n  transition: all 0.2s ease !important;\\n  height: auto !important;\\n  min-height: 31px !important;\\n}\\n.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268 !important;\\n  border-color: #545b62 !important;\\n  transform: translateY(-1px) !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\\n}\\n.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5) !important;\\n}\\n\\n[_nghost-%COMP%]     .k-grid {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header {\\n  background: #f8f9fa;\\n  border-bottom: 2px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header {\\n  background: #f8f9fa;\\n  border-color: #dee2e6;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar {\\n  background: #f8f9fa;\\n  border-bottom: 1px solid #dee2e6;\\n  padding: 1rem;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button {\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary {\\n  background: #007bff;\\n  border-color: #007bff;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary:hover {\\n  background: #0056b3;\\n  border-color: #0056b3;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager {\\n  background: #f8f9fa;\\n  border-top: 1px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-info {\\n  color: #6c757d;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link {\\n  border-radius: 4px;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected {\\n  background: #007bff;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .custom-dropdown .k-button {\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-textbox {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-textbox:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n[_nghost-%COMP%]     .k-dropdownlist {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-dropdownlist:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 0.5em 0.75em;\\n  font-size: 0.75em;\\n  font-weight: 600;\\n  border-radius: 6px;\\n}\\n.badge.badge-light-success[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n.badge.badge-light-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n}\\n.badge.badge-light-danger[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n.badge.badge-light-info[_ngcontent-%COMP%] {\\n  background: #d1ecf1;\\n  color: #0c5460;\\n}\\n.badge.badge-light-secondary[_ngcontent-%COMP%] {\\n  background: #e2e3e5;\\n  color: #383d41;\\n}\\n.badge.badge-light-primary[_ngcontent-%COMP%] {\\n  background: #cce7ff;\\n  color: #004085;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n.btn.btn-success[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  border-color: #28a745;\\n}\\n.btn.btn-success[_ngcontent-%COMP%]:hover {\\n  background: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%] {\\n  background: #ffc107;\\n  border-color: #ffc107;\\n  color: #212529;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%]:hover {\\n  background: #e0a800;\\n  border-color: #d39e00;\\n}\\n.btn.btn-info[_ngcontent-%COMP%] {\\n  background: #17a2b8;\\n  border-color: #17a2b8;\\n}\\n.btn.btn-info[_ngcontent-%COMP%]:hover {\\n  background: #138496;\\n  border-color: #138496;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #6c757d;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background: #6c757d;\\n  color: white;\\n}\\n\\n.btn-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  padding: 0;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.btn-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.text-muted[_ngcontent-%COMP%]   .fas[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.text-muted[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  margin-top: 1rem;\\n}\\n.text-muted[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], \\n   .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n}\\n.grid-container[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_expandGrid 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_expandGrid {\\n  from {\\n    opacity: 0.8;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["each", "Subject", "debounceTime", "distinctUntilChanged", "PermitPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "PermitListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchData", "ɵɵresetView", "ɵɵlistener", "PermitListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "onSearchChange", "ɵɵelement", "PermitListComponent_ng_template_4_Template_button_click_8_listener", "add", "PermitListComponent_ng_template_4_Template_button_click_11_listener", "toggleExpand", "PermitListComponent_ng_template_4_Template_kendo_dropdownbutton_itemClick_13_listener", "onExportClick", "PermitListComponent_ng_template_4_Template_button_click_14_listener", "resetTable", "PermitListComponent_ng_template_4_Template_button_click_16_listener", "refreshGrid", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "exportOptions", "PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r4", "appliedFilters", "status", "PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener", "category", "PermitListComponent_ng_template_5_div_0_Template_button_click_11_listener", "applyAdvancedFilters", "PermitListComponent_ng_template_5_div_0_Template_button_click_13_listener", "clearAdvancedFilters", "PermitListComponent_ng_template_5_div_0_Template_button_click_15_listener", "showAdvancedFilters", "advancedFilterOptions", "categories", "ɵɵtemplate", "PermitListComponent_ng_template_5_div_0_Template", "PermitListComponent_ng_template_6_Template_button_click_0_listener", "_r5", "ɵɵtextInterpolate1", "PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener", "dataItem_r7", "_r6", "dataItem", "edit", "permitId", "PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template", "dataItem_r8", "permitNumber", "column_r10", "filter_r9", "PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_2_Template", "dataItem_r11", "permitName", "column_r13", "filter_r12", "PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_2_Template", "dataItem_r14", "projectName", "column_r16", "filter_r15", "PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_2_Template", "dataItem_r17", "permitType", "column_r19", "filter_r18", "PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_2_Template", "getCategoryClass", "dataItem_r20", "permitCategory", "PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener", "_r21", "PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r21", "filterService_r23", "filterService", "column_r24", "column", "onMenuDropdownChange", "field", "filter_r25", "ɵɵpureFunction0", "_c6", "PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template", "getStatusClass", "dataItem_r26", "internalReviewStatus", "PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener", "_r27", "PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r27", "filterService_r29", "column_r30", "filter_r31", "categoriess", "PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template", "dataItem_r32", "permitStatus", "PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener", "_r33", "PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r33", "filterService_r35", "column_r36", "filter_r37", "PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template", "dataItem_r38", "location", "column_r40", "filter_r39", "PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template", "formatDate", "dataItem_r41", "permitAppliedDate", "column_r43", "filter_r42", "filterService_r44", "PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_2_Template", "dataItem_r45", "permitExpirationDate", "column_r47", "filter_r46", "filterService_r48", "PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_2_Template", "dataItem_r49", "permitFinalDate", "column_r51", "filter_r50", "filterService_r52", "PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_2_Template", "dataItem_r53", "permitCompleteDate", "column_r55", "filter_r54", "filterService_r56", "PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_2_Template", "dataItem_r57", "attentionReason", "column_r59", "filter_r58", "PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_2_Template", "dataItem_r60", "lastUpdatedDate", "column_r62", "filter_r61", "PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_2_Template", "ɵɵelementContainerStart", "PermitListComponent_ng_container_7_kendo_grid_column_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_2_Template", "PermitListComponent_ng_container_7_kendo_grid_column_3_Template", "PermitListComponent_ng_container_7_kendo_grid_column_4_Template", "PermitListComponent_ng_container_7_kendo_grid_column_5_Template", "PermitListComponent_ng_container_7_kendo_grid_column_6_Template", "PermitListComponent_ng_container_7_kendo_grid_column_7_Template", "PermitListComponent_ng_container_7_kendo_grid_column_8_Template", "PermitListComponent_ng_container_7_kendo_grid_column_9_Template", "PermitListComponent_ng_container_7_kendo_grid_column_10_Template", "PermitListComponent_ng_container_7_kendo_grid_column_11_Template", "PermitListComponent_ng_container_7_kendo_grid_column_12_Template", "PermitListComponent_ng_container_7_kendo_grid_column_13_Template", "PermitListComponent_ng_container_7_kendo_grid_column_14_Template", "PermitListComponent_ng_container_7_kendo_grid_column_15_Template", "column_r63", "PermitListComponent_div_8_Template_button_click_7_listener", "_r64", "clearSearch", "loadTable", "PermitListComponent", "router", "route", "permitsService", "httpUtilService", "customLayoutUtilsService", "kendoColumnService", "modalService", "cdr", "appService", "ExceljsService", "pageInfo", "grid", "serverSideRowData", "gridData", "IsListHasValue", "loading", "isLoading", "loginUser", "searchTerms", "searchSubscription", "value", "next", "filters", "logic", "operator", "filter", "gridFilter", "activeFilters", "filterOptions", "text", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "fixedColumns", "draggableColumns", "normalGrid", "expandedGrid", "gridColumnConfig", "title", "width", "isFixed", "type", "order", "filterable", "statusList", "sort", "dir", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "selectedRows", "isAllSelected", "columnJSONFormat", "singlePermit", "resetToDefaultSettings", "console", "log", "columns", "for<PERSON>ach", "hidden", "pageSize", "detectChanges", "setTimeout", "refresh", "reset", "constructor", "pipe", "subscribe", "loadingSubject", "ngOnInit", "updateTitle", "initializeComponent", "ngAfterViewInit", "initializeGrid", "ngOnDestroy", "complete", "getLoggedInUser", "initializeColumnVisibility", "setupColumnArrays", "savedConfig", "getFromLocalStorage", "userId", "applySavedColumnSettings", "loadColumnSettingsFromServer", "config", "pageName", "userID", "getHideFields", "response", "<PERSON><PERSON><PERSON>", "Data", "hideData", "JSON", "parse", "error", "map", "col", "length", "a", "b", "aOrder", "indexOf", "b<PERSON>rder", "loadTableWithKendoEndpoint", "loadingTimeout", "warn", "resetLoadingStates", "state", "take", "search", "loggedInUserId", "getPermitsForKendoGrid", "data", "clearTimeout", "responseData", "errors", "handleEmptyResponse", "permitData", "total", "Math", "ceil", "min", "pageIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "httpError", "event", "key", "applySearch", "filterChange", "f", "push", "onSortChange", "incomingSort", "Array", "isArray", "pageChange", "floor", "onPageSizeChange", "onDataStateChange", "onColumnReorder", "reorderedColumns", "updateColumnVisibility", "hiddenColumns", "onSelectionChange", "selectAll", "gridContainer", "document", "querySelector", "classList", "toggle", "selectedOption", "prdItems", "exportExcel", "queryparamsExcel", "sortOrder", "sortField", "getAllPermits", "listOfItems", "currentDate", "formatMonthDate", "Date", "undefined", "tableTitle", "headerArray", "percentageColumns", "respResult", "prdItem", "respData", "fill", "eventDescription", "event_date", "i", "adjustedIndex", "colSize", "header", "index", "id", "generateExcel", "message", "saveHead", "settings", "saveToLocalStorage", "LoggedId", "showSuccess", "saveColumnSettingsToServer", "createHideFields", "showError", "saveResetToServer", "deleteConfig", "deleteHideFields", "clearFromLocalStorage", "view", "navigate", "queryParams", "from", "permit", "find", "p", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "passEntry", "receivedEntry", "deletePop", "content", "centered", "confirmDelete", "delete", "confirm", "deletePermit", "dateString", "date", "month", "getMonth", "toString", "padStart", "day", "getDate", "year", "getFullYear", "syncPermits", "municipalityId", "autoLogin", "res", "faultMessage", "success", "err", "Object", "keys", "onTabActivated", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "PermitsService", "i3", "HttpUtilsService", "i4", "CustomLayoutUtilsService", "i5", "KendoColumnService", "i6", "NgbModal", "ChangeDetectorRef", "i7", "AppService", "i8", "i9", "PageInfoService", "selectors", "viewQuery", "PermitListComponent_Query", "rf", "ctx", "PermitListComponent_div_0_Template", "PermitListComponent_Template_kendo_grid_columnReorder_2_listener", "_r1", "PermitListComponent_Template_kendo_grid_selectionChange_2_listener", "PermitListComponent_Template_kendo_grid_filterChange_2_listener", "PermitListComponent_Template_kendo_grid_pageChange_2_listener", "PermitListComponent_Template_kendo_grid_pageSizeChange_2_listener", "PermitListComponent_Template_kendo_grid_dataStateChange_2_listener", "PermitListComponent_Template_kendo_grid_sortChange_2_listener", "PermitListComponent_Template_kendo_grid_columnVisibilityChange_2_listener", "PermitListComponent_ng_template_4_Template", "PermitListComponent_ng_template_5_Template", "PermitListComponent_ng_template_6_Template", "PermitListComponent_ng_container_7_Template", "PermitListComponent_div_8_Template", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-list\\permit-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-list\\permit-list.component.html"], "sourcesContent": ["import { formatDate } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectorRef,\n  Component,\n  OnDestroy,\n  OnInit,\n  ViewChild,\n} from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\nimport {  FilterService } from '@progress/kendo-angular-grid';\n\nimport { saveAs } from '@progress/kendo-file-saver';\nimport { add, each } from 'lodash';\nimport {\n  Subject,\n  Subscription,\n  debounceTime,\n  distinctUntilChanged,\n  filter,\n} from 'rxjs';\nimport { AppService } from '../../services/app.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { KendoColumnService } from '../../services/kendo-column.service';\nimport {\n  CompositeFilterDescriptor,\n  SortDescriptor,\n} from '@progress/kendo-data-query';\nimport { PermitsService } from '../../services/permits.service';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport { ExceljsService } from '../../services/exceljs.service';\nimport { PageInfoService } from 'src/app/_metronic/layout/core/page-info.service';\n\n@Component({\n  selector: 'app-permit-list',\n  templateUrl: './permit-list.component.html',\n  styleUrl: './permit-list.component.scss',\n})\nexport class PermitListComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('normalGrid') grid: any;\n\n  // Data\n  public serverSideRowData: any[] = [];\n  public gridData: any = [];\n  public IsListHasValue: boolean = false;\n\n  public loading: boolean = false;\n  public isLoading: boolean = false;\n\n  loginUser: any = {};\n\n  // Search\n  public searchData: string = '';\n  private searchTerms = new Subject<string>();\n  private searchSubscription: Subscription;\nonMenuDropdownChange(value: any, field: string, filterService: FilterService): void {\n  const next: CompositeFilterDescriptor =\n    value == null\n      ? { filters: [], logic: 'and' }\n      : { filters: [{ field, operator: 'eq', value }], logic: 'and' };\n  filterService.filter(next);\n}\n  // Enhanced Filters for Kendo UI\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public activeFilters: Array<{\n    field: string;\n    operator: string;\n    value: any;\n  }> = [];\n\n  public filterOptions: Array<{ text: string; value: string | null }> = [\n    { text: 'All', value: null },\n    { text: 'Active', value: 'Active' },\n    { text: 'Inactive', value: 'Inactive' },\n  ];\n\n  // Advanced filter options\n  public advancedFilterOptions = {\n    status: [\n       { text: 'All', value: null },\n  { text: 'Requires Resubmit', value: 'Requires Resubmit' },\n  { text: 'On Hold', value: 'On Hold' },\n  { text: 'Approved', value: 'Approved' },\n  { text: 'Pending', value: 'Pending' },\n  { text: 'Canceled', value: 'Canceled' },\n  { text: 'Complete', value: 'Complete' },\n  { text: 'Expired', value: 'Expired' },\n  { text: 'Fees Due', value: 'Fees Due' },\n  { text: 'In Review', value: 'In Review' },\n  { text: 'Issued', value: 'Issued' },\n  { text: 'Requires Resubmit for Prescreen', value: 'Requires Resubmit for Prescreen' },\n  { text: 'Submitted - Online', value: 'Submitted - Online' },\n  { text: 'Void', value: 'Void' },\n    ] as Array<{ text: string; value: string | null }>,\n    categories: [\n      { text: 'All', value: null },\n      { text: 'Primary', value: 'Primary' },\n      { text: 'Sub Permit', value: 'Sub Permit' },\n      { text: 'Industrial', value: 'Industrial' },\n      { text: 'Municipal', value: 'Municipal' },\n      { text: 'Environmental', value: 'Environmental' },\n    \n    ] as Array<{ text: string; value: string | null }>,\n    categoriess: [\n      { text: 'All', value: null },\n      { text: 'Approved', value: 'Approved' },\n      { text: 'Pacifica Verification', value: 'Pacifica Verification' },\n      { text: 'Dis-Approved', value: 'Dis-Approved' },\n      { text: 'Pending', value: 'Pending' },\n      { text: 'Not Required', value: 'Not Required' },\n      { text: 'In Review', value: 'In Review' },\n      { text: '1 Cycle Completed', value: '1 Cycle Completed' },\n      \n      // 'Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed'\n    \n    ] as Array<{ text: string; value: string | null }>,\n  };\n\n  // Filter state\n  public showAdvancedFilters = false;\n  public appliedFilters: {\n    status?: string | null;\n    category?: string | null;\n    internalReviewStatus?: string | null;\n  } = {};\n\n  // Column visibility system\n  public kendoHide: any;\n  public hiddenData: any = [];\n  public kendoColOrder: any = [];\n  public kendoInitColOrder: any = [];\n  public hiddenFields: any = [];\n\n  // Column configuration\n  public gridColumns: string[] = [];\n  public defaultColumns: string[] = [];\n  public fixedColumns: string[] = [];\n  public draggableColumns: string[] = [];\n  public normalGrid: any;\n  public expandedGrid: any;\n  public isExpanded = false;\n\n  // Enhanced Columns with Kendo UI features - adapted for permits\n  public gridColumnConfig: Array<{\n    field: string;\n    title: string;\n    width: number;\n    isFixed: boolean;\n    type: string;\n    filterable?: boolean;\n    order: number;\n  }> = [\n    {\n      field: 'action',\n      title: 'Action',\n      width: 100,\n      isFixed: true,\n      type: 'action',\n      order: 1,\n    },\n     {\n      field: 'permitName',\n      title: 'Permit/Sub Project Name',\n      width: 180,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2,\n    },\n    {\n      field: 'permitNumber',\n      title: 'Permit #',\n      width: 180,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 3,\n    },\n   \n      {\n      field: 'projectName',\n      title: 'Project Name',\n      width: 180,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 4,\n    },\n       {\n      field: 'permitCategory',\n      title: 'Category',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5,\n    },\n    {\n      field: 'permitType',\n      title: 'Permit Type',\n      width: 200,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6,\n    },\n \n    {\n      field: 'permitStatus',\n      title: 'Permit Status',\n      width: 150,\n      isFixed: false,\n      type: 'status',\n      filterable: true,\n      order: 7,\n    },\n    {\n      field: 'internalReviewStatus',\n      title: 'Internal Review Status',\n      width: 150,\n      isFixed: false,\n      type: 'status',\n      filterable: true,\n      order: 8,\n    },\n    {\n      field: 'location',\n      title: 'Location',\n      width: 200,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 9,\n    },\n  \n    {\n      field: 'permitAppliedDate',\n      title: 'Applied Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 10,\n    },\n    {\n      field: 'permitExpirationDate',\n      title: 'Expiration Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 11,\n    },\n    {\n      field: 'permitFinalDate',\n      title: 'Final Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 12,\n    },\n    {\n      field: 'permitCompleteDate',\n      title: 'Complete Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 13,\n    },\n    {\n      field: 'attentionReason',\n      title: 'Attention Reason',\n      width: 180,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 14,\n    },\n    {\n      field: 'lastUpdatedDate',\n      title: 'Updated Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 15,\n    },\n  ];\npublic statusList:any[] = [\n  { text: 'Approved', value: 'Approved' },\n  { text: 'Pending', value: 'Pending' },\n  { text: 'Rejected', value: 'Rejected' }\n];\n  // State\n  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n\n  public page: any = {\n    size: 15,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc',\n  };\n\n  public skip: number = 0;\n\n  // Selection\n  public selectedRows: any[] = [];\n  public isAllSelected: boolean = false;\n\n  // Export options\n  public exportOptions = [\n    { text: 'All', value: 'all' },\n    { text: 'Page Results', value: 'selected' },\n  ];\n  columnJSONFormat: any;\n  permitId: number;\n  singlePermit: any;\n  private resetToDefaultSettings(): void {\n    console.log('Resetting to default settings...');\n    \n    // Reset column visibility - show all columns\n    this.hiddenFields = [];\n    this.gridColumns = [...this.defaultColumns];\n    this.kendoColOrder = [...this.defaultColumns];\n\n    // Reset sort state to default\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    \n    // Reset page state\n    this.page.pageNumber = 0;\n    this.skip = 0;\n\n    // Reset all filters - clear everything\n    this.filter = { logic: 'and', filters: [] };\n    this.activeFilters = [];\n\n    // Reset advanced filters\n    this.appliedFilters = {};\n\n    // Reset search\n    this.searchData = '';\n\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n\n    console.log('Reset completed:', {\n      hiddenFields: this.hiddenFields,\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      sort: this.sort,\n      filter: this.filter,\n      searchData: this.searchData\n    });\n\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = { logic: 'and', filters: [] };\n      \n      // Reset sorting\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n      \n      // Reset column visibility - show all columns\n      this.grid.columns.forEach((column: any) => {\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n      \n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n\n    // Trigger change detection\n    this.cdr.detectChanges();\n    \n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        // Also try to reset the grid state completely\n        this.grid.reset();\n      }, 100);\n    }\n    \n    // Reload data with clean state\n    this.loadTable();\n  }\n  // private saveColumnState(): void {\n  //   try {\n  //     const columnState = {\n  //       columns: this.gridColumns,\n  //       hidden: this.hiddenFields,\n  //       order: this.kendoColOrder,\n  //     };\n  //     localStorage.setItem(\n  //       `${this.GRID_STATE_KEY}-columns`,\n  //       JSON.stringify(columnState)\n  //     );\n  //   } catch (error) {\n  //     console.warn('Error saving column state:', error);\n  //   }\n  // }\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute,\n    private permitsService: PermitsService,\n    private httpUtilService: HttpUtilsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private kendoColumnService: KendoColumnService,\n    private modalService: NgbModal,\n    private cdr: ChangeDetectorRef,\n    public appService: AppService,\n    public ExceljsService: ExceljsService,\n    private pageInfo: PageInfoService\n  ) {\n    // Initialize search subscription\n    this.searchSubscription = this.searchTerms\n      .pipe(debounceTime(500), distinctUntilChanged())\n      .subscribe(() => {\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        this.httpUtilService.loadingSubject.next(true);\n        this.loadTable();\n      });\n  }\n\n  ngOnInit(): void {\n    this.pageInfo.updateTitle('Permits');\n    this.initializeComponent();\n    this.loadTable();\n  }\n\n  ngAfterViewInit(): void {\n    this.initializeGrid();\n  }\n\n  ngOnDestroy(): void {\n    if (this.searchSubscription) {\n      this.searchTerms.complete();\n    }\n  }\n\n  private initializeComponent(): void {\n    // Get login user info\n    this.loginUser = this.appService.getLoggedInUser();\n    // Initialize column visibility system\n    this.initializeColumnVisibility();\n  }\n\n  private initializeColumnVisibility(): void {\n    // Set up column arrays first\n    this.setupColumnArrays();\n    \n    // Try to load from local storage first\n    const savedConfig = this.kendoColumnService.getFromLocalStorage('permits', this.loginUser.userId);\n    \n    if (savedConfig) {\n      // Load saved settings from local storage\n      this.kendoHide = savedConfig.hiddenData || [];\n      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.kendoColOrder];\n    } else {\n      // Initialize with default values\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.defaultColumns];\n    }\n    \n    // Apply settings\n    this.applySavedColumnSettings();\n  }\n\n  private loadColumnSettingsFromServer(): void {\n    const config = {\n      pageName: 'permits',\n      userID: this.loginUser.userId\n    };\n\n    this.kendoColumnService.getHideFields(config).subscribe({\n      next: (response) => {\n        if (response.isFault === false && response.Data) {\n          // Parse the saved settings\n          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.kendoColOrder];\n          \n          // Apply the settings\n          this.applySavedColumnSettings();\n          \n          console.log('Column settings loaded from server:', {\n            kendoHide: this.kendoHide,\n            kendoColOrder: this.kendoColOrder\n          });\n        } else {\n          // No saved settings, use defaults\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      },\n      error: (error) => {\n        console.error('Error loading column settings:', error);\n        // Use defaults on error\n        this.kendoHide = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n        this.applySavedColumnSettings();\n      }\n    });\n  }\n\n  private setupColumnArrays(): void {\n    this.gridColumns = this.gridColumnConfig.map((col) => col.field);\n    this.defaultColumns = [...this.gridColumns];\n    this.fixedColumns = this.gridColumnConfig\n      .filter((col) => col.isFixed)\n      .map((col) => col.field);\n    this.draggableColumns = this.gridColumnConfig\n      .filter((col) => !col.isFixed)\n      .map((col) => col.field);\n  }\n\n  private initializeGrid(): void {\n    if (this.grid) {\n      // Apply saved column settings\n      this.applySavedColumnSettings();\n    }\n  }\n\n  private applySavedColumnSettings(): void {\n    if (this.kendoHide && this.kendoHide.length > 0) {\n      this.hiddenFields = this.kendoHide;\n    }\n\n    if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n      // Apply column order\n      this.gridColumnConfig.sort((a, b) => {\n        const aOrder = this.kendoColOrder.indexOf(a.field);\n        const bOrder = this.kendoColOrder.indexOf(b.field);\n        return aOrder - bOrder;\n      });\n    }\n  }\n\n  // Load table data\n  public loadTable(): void {\n    this.loadTableWithKendoEndpoint();\n  }\n\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n\n    // Safety timeout to prevent loader from getting stuck\n    const loadingTimeout = setTimeout(() => {\n      console.warn('Loading timeout reached, resetting loading states');\n      this.resetLoadingStates();\n    }, 30000); // 30 seconds timeout\n\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId,\n    };\n    \n    console.log('Loading table with state:', state);\n\n    this.permitsService.getPermitsForKendoGrid(state).subscribe({\n      next: (data: {\n        isFault?: boolean;\n        responseData?: {\n          data: any[];\n          total: number;\n          errors?: string[];\n          status?: number;\n        };\n        data?: any[];\n        total?: number;\n        errors?: string[];\n        status?: number;\n      }) => {\n        // Clear the safety timeout since we got a response\n        clearTimeout(loadingTimeout);\n\n        console.log('API Response:', data);\n\n        // Handle the new API response structure\n        if (\n          data.isFault ||\n          (data.responseData &&\n            data.responseData.errors &&\n            data.responseData.errors.length > 0)\n        ) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n\n          // Check if this is an authentication error\n          if (data.responseData?.status === 401 || data.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n\n          this.handleEmptyResponse();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const permitData = responseData.data || [];\n          const total = responseData.total || 0;\n\n          this.IsListHasValue = permitData.length !== 0;\n          this.serverSideRowData = permitData;\n          this.gridData = this.serverSideRowData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n          \n          // Create a data source with total count for Kendo Grid\n          this.gridData = {\n            data: permitData,\n            total: total\n          };\n          console.log('this.serverSideRowData ', this.serverSideRowData);\n          console.log('this.gridData ', this.gridData);\n          console.log('this.IsListHasValue ', this.IsListHasValue);\n          console.log('this.page ', this.page);\n          console.log('Total elements set to:', this.page.totalElements);\n          console.log('Total pages calculated:', this.page.totalPages);\n          console.log('Current skip:', this.skip, 'Page size:', this.page.size);\n          console.log('Expected items range:', (this.skip + 1), '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n          \n          // Debug grid state after data load\n          setTimeout(() => {\n            if (this.grid) {\n              console.log('Grid total:', this.grid.total);\n              console.log('Grid pageSize:', this.grid.pageSize);\n              console.log('Grid skip:', this.grid.skip);\n              console.log('Grid pageIndex:', this.grid.pageIndex);\n              console.log('Grid data length:', this.grid.data ? this.grid.data.length : 'no data');\n              \n              // Force grid to update its total and maintain pagination state\n              this.grid.total = this.page.totalElements;\n              this.grid.skip = this.skip;\n              this.grid.pageIndex = this.page.pageNumber;\n              console.log('Forced grid total to:', this.grid.total, 'skip to:', this.skip, 'pageIndex to:', this.page.pageNumber);\n            }\n          }, 100);\n          \n          this.cdr.markForCheck();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      },\n      error: (error: unknown) => {\n        // Clear the safety timeout since we got an error\n        clearTimeout(loadingTimeout);\n\n        console.error('Error loading data with Kendo UI endpoint:', error);\n\n        // Check if this is an authentication error\n        if (error && typeof error === 'object' && 'status' in error) {\n          const httpError = error as any;\n          if (httpError.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n        }\n\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        // Clear the safety timeout\n        clearTimeout(loadingTimeout);\n\n        // Ensure loading states are reset in complete block as well\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n    });\n  }\n\n  private handleEmptyResponse(): void {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n\n    // Ensure loading states are reset when handling empty response\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n\n  // Method to manually reset loading states if they get stuck\n  private resetLoadingStates(): void {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n\n  // Public method to manually refresh the grid and reset any stuck loading states\n  public refreshGrid(): void {\n    console.log('Manually refreshing grid...');\n    this.resetLoadingStates();\n    this.loadTable();\n  }\n\n  // Search functionality\n  public onSearchKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter') {\n      this.loadTable();\n    }\n  }\n\n  public onSearchChange(): void {\n    console.log('Search changed:', this.searchData);\n    // Trigger search with debounce\n    this.searchTerms.next(this.searchData || '');\n  }\n\n  private applySearch(): void {\n    this.loadTable();\n  }\n\n  public clearSearch(): void {\n    this.searchTerms.next(this.searchData);\n  }\n\n  // Filter functionality\n  public filterChange(filter: CompositeFilterDescriptor): void {\n    console.log('filter', filter);\n    this.filter = filter;\n    this.loadTable();\n  }\n\n  public applyAdvancedFilters(): void {\n    console.log('yes it came here');\n    // Apply status filter\n    if (this.appliedFilters.status) {\n      this.filter.filters = this.filter.filters.filter((f) => {\n        if ('field' in f) {\n          return f.field !== 'permitStatus';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'permitStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status,\n      });\n    }\n\n    // Apply category filter\n    if (this.appliedFilters.category) {\n      this.filter.filters = this.filter.filters.filter((f) => {\n        if ('field' in f) {\n          return f.field !== 'permitCategory';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'permitCategory',\n        operator: 'eq',\n        value: this.appliedFilters.category,\n      });\n    }\n\n    this.loadTable();\n  }\n\n  public clearAdvancedFilters(): void {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n    this.loadTable();\n  }\n\n  // Sorting functionality\n  public onSortChange(sort: SortDescriptor[]): void {\n    console.log('Sort change triggered:', sort);\n    \n    // Handle empty sort array (normalize/unsort case)\n    const incomingSort = Array.isArray(sort) ? sort : [];\n    this.sort = incomingSort.length > 0\n      ? incomingSort\n      : [{ field: 'lastUpdatedDate', dir: 'desc' }];\n\n    // Update page order fields for consistency\n    this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n    this.page.orderDir = this.sort[0].dir || 'desc';\n\n    console.log('Final sort state:', this.sort);\n    console.log('Page order:', { orderBy: this.page.orderBy, orderDir: this.page.orderDir });\n\n    this.loadTable();\n  }\n\n  // Pagination functionality\n  public pageChange(event: any): void {\n    console.log('Page change event:', event);\n    console.log('Current page size:', this.page.size);\n    console.log('Event page size:', event.pageSize);\n    console.log('Event page index:', event.pageIndex);\n    console.log('Event skip:', event.skip);\n    \n    // Use Kendo's provided values as source of truth\n    this.skip = event.skip;\n    this.page.size = event.take || this.page.size;\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\n    \n    console.log('Updated skip:', this.skip, 'page size:', this.page.size, 'page number:', this.page.pageNumber);\n    console.log('Expected items range:', (this.skip + 1), '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n    \n    this.loadTable();\n  }\n\n  // Handle page size change specifically\n  public onPageSizeChange(event: any): void {\n    console.log('Page size change event:', event);\n    console.log('New page size:', event.pageSize);\n    \n    if (event.pageSize && event.pageSize !== this.page.size) {\n      console.log('Page size changing from', this.page.size, 'to', event.pageSize);\n      this.page.size = event.pageSize;\n      this.page.pageNumber = 0; // Reset to first page when changing page size\n      this.skip = 0;\n      \n      // Recalculate total pages based on new page size\n      if (this.page.totalElements > 0) {\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        console.log('Recalculated total pages:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n      }\n      \n      // Force grid to update its page size and reset to first page\n      if (this.grid) {\n        this.grid.pageSize = this.page.size;\n        this.grid.skip = 0;\n        this.grid.pageIndex = 0;\n        this.grid.total = this.page.totalElements;\n        console.log('Updated grid total to:', this.grid.total);\n      }\n      \n      console.log('Updated page size:', this.page.size, 'skip:', this.skip);\n      this.loadTable();\n    }\n  }\n\n  // Handle data state changes (includes page size changes)\n  public onDataStateChange(event: any): void {\n    console.log('Data state change event:', event);\n    \n    // Check if page size changed\n    if (event.take && event.take !== this.page.size) {\n      console.log('Page size changing via data state from', this.page.size, 'to', event.take);\n      this.page.size = event.take;\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      \n      // Recalculate total pages based on new page size\n      if (this.page.totalElements > 0) {\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        console.log('Recalculated total pages via data state:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n      }\n      \n      // Force grid to update its page size and reset to first page\n      if (this.grid) {\n        this.grid.pageSize = this.page.size;\n        this.grid.skip = 0;\n        this.grid.pageIndex = 0;\n        this.grid.total = this.page.totalElements;\n        console.log('Updated grid total via data state to:', this.grid.total);\n      }\n      \n      console.log('Updated page size via data state:', this.page.size, 'skip:', this.skip);\n      this.loadTable();\n    }\n  }\n\n  // Column management\n  public onColumnReorder(event: any): void {\n    // Handle column reordering\n    const reorderedColumns = event.columns.map((col: any) => col.field);\n    this.kendoColOrder = reorderedColumns;\n  }\n\n  public updateColumnVisibility(event: any): void {\n    // Handle column visibility changes\n    const hiddenColumns = event.hiddenColumns || [];\n    this.hiddenFields = hiddenColumns;\n  }\n\n  // Selection functionality\n  public onSelectionChange(event: any): void {\n    this.selectedRows = event.selectedRows || [];\n    this.isAllSelected =\n      this.selectedRows.length === this.serverSideRowData.length;\n  }\n\n  public selectAll(): void {\n    if (this.isAllSelected) {\n      this.selectedRows = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedRows = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n  }\n\n  // Grid expansion\n  public toggleExpand(): void {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector(\n      '.grid-container'\n    ) as HTMLElement;\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n\n  // Export functionality\n  onExportClick(event: any) {\n    const selectedOption = event.value; // Get selected option\n\n    let prdItems: any = [];\n    if (selectedOption === 'selected') {\n      prdItems = this.serverSideRowData;\n\n      // declare the title and header data for excel\n      // get the data for excel in a array format\n      this.exportExcel(prdItems);\n    } else if (selectedOption === 'all') {\n      const queryparamsExcel = {\n        pageSize: this.page.totalElements,\n        sortOrder: this.page.orderDir,\n        sortField: this.page.orderBy,\n        pageNumber: this.page.pageNumber,\n        // filter: this.filterConfiguration()\n      };\n\n      // Enable loading indicator\n      this.httpUtilService.loadingSubject.next(true);\n      // API call\n      this.permitsService\n        .getAllPermits(queryparamsExcel)\n        // .pipe(map((data: any) => data as any))\n        .subscribe((data) => {\n          // Disable loading indicator\n          this.httpUtilService.loadingSubject.next(false);\n          if (data.isFault) {\n            this.IsListHasValue = false;\n            this.cdr.markForCheck();\n            return; // Exit early if the response has a fault\n          }\n\n          this.IsListHasValue = true;\n          prdItems = data.responseData.data || [];\n\n          this.cdr.detectChanges(); // Manually trigger UI update\n          this.exportExcel(prdItems);\n        });\n    }\n  }\n\n  exportExcel(listOfItems: any): void {\n    // Define local variables for the items and current date\n    let prdItems: any = listOfItems;\n    let currentDate: Date = this.appService.formatMonthDate(new Date());\n\n    console.log('prdItems', prdItems);\n\n    // Check if the data exists and is not empty\n    if (prdItems !== undefined && prdItems.length > 0) {\n      // Define the title for the Excel file\n      const tableTitle = 'Events';\n\n      // Filter out hidden columns and sort by order\n      // const visibleColumns = this.columnJSONFormat\n      //   .filter((col: any) => !col.hidden)\n      //   .sort((a: any, b: any) => a.order - b.order);\n\n      // Create header from visible columns\n\n      const headerArray = [\n        'Permit Number',\n        'Permit Type',\n        'Category',\n        'Status',\n        'Location',\n        'Project Name',\n        'Applied Date',\n\n        'Expiration Date',\n        'Attention Reason',\n      ];\n      // ...visibleColumns.map((col: any) => col.title),\n\n      // Define which columns should have currency and percentage formatting\n      // const currencyColumns: any = [\n      //   'Pending',\n      //   'ACAT',\n      //   'Annuity',\n      //   'AUM',\n      //   'Total Assets',\n      //   'Event Cost',\n      //   'Gross Profit',\n      // ].filter((col) => headerArray.includes(col));\n\n      const percentageColumns: any = [];\n\n      // Get the data for excel in an array format\n      const respResult: any = [];\n\n      // Prepare the data for export based on visible columns\n      each(prdItems, (prdItem: any) => {\n        // Create an array with the same length as headerArray\n        const respData = Array(headerArray.length).fill(null);\n        respData[0] = prdItem.eventDescription;\n        respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n        // Fill in data for each visible column\n        headerArray.forEach((col: any, i: number) => {\n          const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n          switch (col) {\n            case 'Permit Number':\n              respData[adjustedIndex] = prdItem.permitNumber;\n              break;\n            case 'Permit Type':\n              respData[adjustedIndex] = prdItem.permitType;\n              break;\n            case 'Category':\n              respData[adjustedIndex] = prdItem.permitCategory;\n              break;\n            case 'Status':\n              respData[adjustedIndex] = prdItem.permitStatus;\n              break;\n            case 'Location':\n              respData[adjustedIndex] = prdItem.location;\n              break;\n            case 'Project Name':\n              respData[adjustedIndex] = prdItem.projectName;\n              break;\n            case 'Applied Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitAppliedDate);\n              break;\n            case 'Expiration Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitExpirationDate);\n              break;\n            case 'Attention Reason':\n              respData[adjustedIndex] = prdItem.attentionReason;\n              break;\n            // case 'kept_appointments':\n            //   respData[adjustedIndex] = prdItem.kept_appointments;\n            //   break;\n            // case 'kept_appt_ratio':\n            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n            //   break;\n            // case 'apptKeptNo':\n            //   respData[adjustedIndex] = prdItem.apptKeptNo;\n            //   break;\n            // case 'has_assets':\n            //   respData[adjustedIndex] = prdItem.has_assets;\n            //   break;\n            // case 'prospects_closed':\n            //   respData[adjustedIndex] = prdItem.prospects_closed;\n            //   break;\n            // case 'closing_ratio':\n            //   respData[adjustedIndex] = prdItem.closing_ratio;\n            //   break;\n            // case 'totalPending':\n            //   respData[adjustedIndex] = prdItem.totalPending;\n            //   break;\n            // case 'acatproduction':\n            //   respData[adjustedIndex] = prdItem.acatproduction;\n            //   break;\n            // case 'annuityproduction':\n            //   respData[adjustedIndex] = prdItem.annuityproduction;\n            //   break;\n            // case 'aumproduction':\n            //   respData[adjustedIndex] = prdItem.aumproduction;\n            //   break;\n            // case 'totalAssets':\n            //   respData[adjustedIndex] = prdItem.totalAssets;\n            //   break;\n            // case 'eventCost':\n            //   respData[adjustedIndex] = prdItem.eventCost;\n            //   break;\n            // case 'grossProfit':\n            //   respData[adjustedIndex] = prdItem.grossProfit;\n            //   break;\n            // case 'status':\n            //   respData[adjustedIndex] = prdItem.status;\n            //   break;\n          }\n        });\n\n        respResult.push(respData);\n      });\n\n      // Define column sizes for the Excel file\n      const colSize = headerArray.map((header, index) => ({\n        id: index + 1,\n        width: 20,\n      }));\n\n      // Generate the Excel file using the exceljsService\n      this.ExceljsService.generateExcel(\n        tableTitle,\n        headerArray,\n        respResult,\n        colSize\n        // currencyColumns,\n        // percentageColumns\n      );\n    } else {\n      const message = 'There are no records available to export.';\n      // this.layoutUtilService.showError(message, '');\n    }\n  }\n\n  // public onExportClick(event: any): void {\n  //   const exportType = event.item.value;\n  //   let selectedIds: number[] = [];\n\n  //   switch (exportType) {\n  //     case 'selected':\n  //       selectedIds = this.selectedRows.map((row) => row.permitId);\n  //       if (selectedIds.length === 0) {\n  //         //alert('Please select permits to export');\n  //         return;\n  //       }\n  //       break;\n  //     case 'filtered':\n  //       // Export filtered data\n  //       break;\n  //     case 'all':\n  //     default:\n  //       // Export all data\n  //       break;\n  //   }\n\n  //   this.exportPermits(exportType, selectedIds);\n  // }\n\n  // private exportPermits(exportType: string, selectedIds: number[]): void {\n  //   this.permitsService.exportPermits(exportType, selectedIds).subscribe({\n  //     next: (response: any) => {\n  //       if (response.data) {\n  //         const blob = new Blob([response.data], {\n  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  //         });\n  //         saveAs(\n  //           blob,\n  //           `permits_${exportType}_${\n  //             new Date().toISOString().split('T')[0]\n  //           }.xlsx`\n  //         );\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       console.error('Export error:', error);\n  //       //alert('Error exporting permits data');\n  //     },\n  //   });\n  // }\n\n  // Column settings management\n  public saveHead(): void {\n    const settings = {\n      kendoHide: this.hiddenFields,\n      kendoColOrder: this.kendoColOrder,\n      kendoInitColOrder: this.kendoInitColOrder,\n    };\n\n    // Save to local storage only\n    this.kendoColumnService.saveToLocalStorage({\n      pageName: 'permits',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    });\n\n    console.log('Column settings saved locally:', settings);\n                        this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n\n    //alert('Column settings saved locally');\n  }\n\n  private saveColumnSettingsToServer(settings: any): void {\n    const config = {\n      pageName: 'permits',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    };\n\n    this.kendoColumnService.createHideFields(config).subscribe({\n      next: (response) => {\n        if (response.isFault === false) {\n          console.log('Column settings saved successfully:', response);\n                        this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n\n          //alert('Column settings saved successfully');\n        } else {\n          console.error('Failed to save column settings:', response.message);\n                        this.customLayoutUtilsService.showError(response.message, '');\n\n          //alert('Failed to save column settings: ' + response.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error saving column settings:', error);\n                        this.customLayoutUtilsService.showError('Error saving column setting', '');\n\n        //alert('Error saving column settings. Please try again.');\n      }\n    });\n  }\n\n  private saveResetToServer(): void {\n    // First delete existing settings\n    const deleteConfig = {\n      pageName: 'permits',\n      userID: this.loginUser.userId\n    };\n\n    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n      next: (response) => {\n        console.log('Existing settings deleted:', response);\n        // Then save the reset state (all columns visible)\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      },\n      error: (error) => {\n        console.error('Error deleting existing settings:', error);\n        // Still try to save the reset state\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      }\n    });\n  }\n\n  public resetTable(): void {\n    console.log('Resetting Kendo settings for permits');\n    \n    // Clear all saved settings first\n    this.kendoHide = [];\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.kendoInitColOrder = [];\n    \n    // Clear local storage\n    this.kendoColumnService.clearFromLocalStorage('permits');\n    \n    // Reset to default settings\n    this.resetToDefaultSettings();\n    \n    // Trigger change detection to update the template\n    this.cdr.detectChanges();\n    \n    // Force grid refresh to show all columns\n    if (this.grid) {\n      this.grid.refresh();\n    }\n\n    // Show success message\n    console.log('Table reset to default settings');\n    //alert('Table reset to default settings - all columns restored');\n  }\n\n  // Navigation\n  public add(): void {\n    // this.router.navigate(['/permits/add']);\n    this.edit(0);\n  }\n\n  public view(permitId: number): void {\n    this.router.navigate(['/permits/view', permitId], { \n      queryParams: { from: 'permit-list' } \n    });\n  }\n\n  public edit(permitId: number): void {\n    if (permitId == 0) {\n      const permit = this.serverSideRowData.find(\n        (p) => p.permitId === permitId\n      );\n      const NgbModalOptions: {\n        size: string;\n        backdrop: boolean | 'static';\n        keyboard: boolean;\n        scrollable: boolean;\n      } = {\n        size: 'lg', // Large modal size\n        backdrop: 'static', // Prevents closing when clicking outside\n        keyboard: false, // Disables closing with the Escape key\n        scrollable: true, // Allows scrolling inside the modal\n      };\n\n      // Open the modal and load the ProjectPopup\n      const modalRef = this.modalService.open(\n        PermitPopupComponent,\n        NgbModalOptions\n      );\n      // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n      modalRef.componentInstance.id = permitId;\n      modalRef.componentInstance.permit = permit;\n      // Subscribe to the modal event when data is updated\n      modalRef.componentInstance.passEntry.subscribe(\n        (receivedEntry: boolean) => {\n          if (receivedEntry === true) {\n            // Reload the table data after a successful update\n            this.loadTable();\n          }\n        }\n      );\n    } else {\n      this.router.navigate(['/permits/view', permitId], { \n        queryParams: { from: 'permit-list' } \n      });\n    }\n  }\n  deletePop(content: any) {\n    this.modalService.open(content, { centered: true });\n  }\n\n  confirmDelete() {\n    console.log('Item deleted ✅');\n    // your delete logic here\n  }\n\n  public delete(permitId: number): void {\n    if (confirm('Are you sure you want to delete this permit?')) {\n      this.permitsService.deletePermit({ permitId }).subscribe({\n        next: (response: any) => {\n          if (response.message) {\n            //alert('Permit deleted successfully');\n                        this.customLayoutUtilsService.showSuccess('Permit deleted successfully', '');\n\n            this.loadTable();\n          }\n        },\n        error: (error: any) => {\n          console.error('Delete error:', error);\n                        this.customLayoutUtilsService.showError('Error deleting permit', '');\n\n          //alert('Error deleting permit');\n        },\n      });\n    }\n  }\n\n  // Utility methods\n  public formatDate(dateString: string): string {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const year = date.getFullYear();\n    return `${month}/${day}/${year}`;\n  }\n\n  public getStatusClass(status: string): string {\n    switch (status) {\n      case 'Approved':\n        return 'badge-light-success';\n      case 'Requires Resubmit':\n        return 'badge-light-warning';\n      case 'On Hold':\n        return 'badge-light-danger';\n      case 'Pending':\n        return 'badge-light-info';\n      default:\n        return 'badge-light-secondary';\n    }\n  }\n\n  public getCategoryClass(category: string): string {\n    return category === 'Primary'\n      ? 'badge-light-primary'\n      : 'badge-light-secondary';\n  }\n\n  syncPermits(i: any) {\n    this.isLoading = true;\n    this.singlePermit = i || false;\n    this.permitsService.syncPermits({ municipalityId: 1, singlePermit: this.singlePermit, autoLogin: true }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        console.log('Sync response:', res);\n        \n        // Handle wrapped response structure from interceptor\n        const responseData = res?.responseData || res;\n        \n        if (responseData?.isFault) {\n          //alert(responseData.faultMessage || 'Failed to sync permit');\n                        this.customLayoutUtilsService.showError(responseData.faultMessage|| 'Failed to sync permit', '');\n\n        } else if (responseData?.success === false) {\n          // Handle specific error messages from the API\n          if (responseData.message === 'Permit not found in Energov system') {\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n          } else if (responseData.message === 'No permits found for any keywords') {\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n         \n                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n           } else {\n                                    this.customLayoutUtilsService.showError(responseData.message ||'Failed to sync permit', '');\n\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n          }\n        } else {\n                        this.customLayoutUtilsService.showSuccess('✅ Permit synced successfully', '');\n\n          //alert('✅ Permit synced successfully');\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        // Handle HTTP error responses\n        console.log('Error response:', err);\n        console.log('Error type:', typeof err);\n        console.log('Error keys:', Object.keys(err || {}));\n        console.log('Error status:', err?.status);\n        console.log('Error message:', err?.message);\n        console.log('Error error:', err?.error);\n        \n        // The interceptor passes err.error to the error handler\n        // So err might actually be the response data\n        if (err?.success === false) {\n          // Handle specific error messages from the API\n          if (err.message === 'Permit not found in Energov system') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.message === 'No permits found for any keywords') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n          }\n        } else if (err?.error?.message) {\n          if (err.error.message === 'Permit not found in Energov system') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.error.message === 'No permits found for any keywords') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert(`❌ ${err.error.message}`)\n            // ;\n          }\n        } else if (err?.status === 404) {\n                                  this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n          // Handle 404 specifically for permit not found\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n        } else {\n                                  this.customLayoutUtilsService.showError('❌ Error syncing permit', '');\n\n          //alert('❌ Error syncing permit');\n        }\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  onTabActivated() {\n    // This method is called when the tab is activated\n    // You can add any specific logic here if needed\n    console.log('Permits tab activated');\n  }\n}\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"grid-container\">\r\n  <kendo-grid\r\n    #normalGrid\r\n    [data]=\"gridData\"\r\n    [pageSize]=\"page.size\"\r\n    [sort]=\"sort\"\r\n    [pageable]=\"{\r\n      pageSizes: [15, 20, 50, 100],\r\n      previousNext: true,\r\n      info: true,\r\n      type: 'numeric',\r\n      buttonCount: 5\r\n    }\"\r\n    [total]=\"page.totalElements\"\r\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\r\n    [groupable]=\"false\"\r\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\r\n    (columnReorder)=\"onColumnReorder($event)\"\r\n    (selectionChange)=\"onSelectionChange($event)\"\r\n    [reorderable]=\"true\"\r\n    style=\"width: auto; overflow-x: auto\"\r\n    [resizable]=\"false\"\r\n    [height]=\"720\"\r\n    [skip]=\"skip\"\r\n    [filter]=\"filter\"\r\n    [columnMenu]=\"{ filter: true }\"\r\n    (filterChange)=\"filterChange($event)\"\r\n    (pageChange)=\"pageChange($event)\"\r\n    (pageSizeChange)=\"onPageSizeChange($event)\"\r\n    (dataStateChange)=\"onDataStateChange($event)\"\r\n    (sortChange)=\"onSortChange($event)\"\r\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\r\n    [loading]=\"false\"\r\n  >\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <!-- Search Section -->\r\n      <div class=\"d-flex align-items-center me-3 search-section\">\r\n        <kendo-textbox\r\n          [style.width.px]=\"500\"\r\n          placeholder=\"Search...\"\r\n          [(ngModel)]=\"searchData\"\r\n          [clearButton]=\"true\"\r\n          (keydown)=\"onSearchKeyDown($event)\"\r\n          (ngModelChange)=\"onSearchChange()\"\r\n        ></kendo-textbox>\r\n        <!-- <button kendoButton [disabled]=\"!searchData || searchData.trim() === ''\" (click)=\"loadTable()\" class=\"ms-2\">\r\n          <i class=\"fas fa-search\"></i> Search\r\n        </button> -->\r\n      </div>\r\n\r\n      <kendo-grid-spacer></kendo-grid-spacer>\r\n\r\n      <!-- Total Count - Repositioned to the right -->\r\n      <div class=\"d-flex align-items-center me-3\">\r\n        <span class=\"text-muted\">Total: </span>\r\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\r\n      </div>\r\n\r\n      <!-- Action Buttons -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-sm me-2\" (click)=\"add()\">\r\n        <span\r\n          [inlineSVG]=\"'./assets/media/icons/duotune/arrows/arr075.svg'\"\r\n          class=\"svg-icon svg-icon-3\"\r\n        ></span>\r\n        Add\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-secondary btn-sm me-2\"\r\n        (click)=\"toggleExpand()\"\r\n        title=\"Toggle Grid Expansion\"\r\n      >\r\n        <i\r\n          class=\"fas\"\r\n          [class.fa-expand]=\"!isExpanded\"\r\n          [class.fa-compress]=\"isExpanded\"\r\n        ></i>\r\n      </button>\r\n\r\n      <kendo-dropdownbutton\r\n        text=\"Export Excel\"\r\n        iconClass=\"fas fa-file-excel\"\r\n        [data]=\"exportOptions\"\r\n        class=\"btn btn-secondary btn-sm me-2\"\r\n        (itemClick)=\"onExportClick($event)\"\r\n        title=\"Export\"\r\n      >\r\n      </kendo-dropdownbutton>\r\n\r\n      <!-- Save Column Settings Button -->\r\n      <!-- <button\r\n        type=\"button\"\r\n        class=\"btn btn-success btn-sm me-2\"\r\n        (click)=\"saveHead()\"\r\n        title=\"Save Column Settings\"\r\n      >\r\n        <i class=\"fas fa-save\"></i>\r\n      </button> -->\r\n\r\n      <!-- Reset Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-warning btn-sm me-2\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset to Default\"\r\n      >\r\n        <i class=\"fas fa-undo\"></i>\r\n      </button>\r\n\r\n      <!-- Refresh Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-info btn-sm me-2\"\r\n        (click)=\"refreshGrid()\"\r\n        title=\"Refresh Grid Data\"\r\n      >\r\n        <i class=\"fas fa-sync-alt\"></i>\r\n      </button>\r\n    </ng-template>\r\n\r\n    <!-- Advanced Filters Panel -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <div\r\n        *ngIf=\"showAdvancedFilters\"\r\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\r\n      >\r\n        <div class=\"row\">\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Status</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.status\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              [(ngModel)]=\"appliedFilters.status\"\r\n              placeholder=\"Select Status\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Category</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.categories\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              [(ngModel)]=\"appliedFilters.category\"\r\n              placeholder=\"Select Category\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-6 d-flex align-items-end\">\r\n            <button\r\n              kendoButton\r\n              themeColor=\"primary\"\r\n              class=\"me-2\"\r\n              (click)=\"applyAdvancedFilters()\"\r\n            >\r\n              Apply Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              themeColor=\"secondary\"\r\n              class=\"me-2\"\r\n              (click)=\"clearAdvancedFilters()\"\r\n            >\r\n              Clear Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              themeColor=\"light\"\r\n              (click)=\"showAdvancedFilters = false\"\r\n            >\r\n              Hide Filters\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n\r\n    <!-- Toggle Advanced Filters Button -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-outline-secondary btn-sm me-2\"\r\n        (click)=\"showAdvancedFilters = !showAdvancedFilters\"\r\n        title=\"Toggle Advanced Filters\"\r\n      >\r\n        <i class=\"fas fa-filter\"></i>\r\n        {{ showAdvancedFilters ? \"Hide\" : \"Show\" }} Advanced Filters\r\n      </button>\r\n    </ng-template>\r\n\r\n    <ng-container *ngFor=\"let column of gridColumns\">\r\n      <!-- Action Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'action'\"\r\n        field=\"action\"\r\n        title=\"Actions\"\r\n        [width]=\"80\"\r\n        [sortable]=\"false\"\r\n        [filterable]=\"false\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <div class=\"d-flex gap-1\">\r\n            <!-- <a\r\n              title=\"View\"\r\n              class=\"btn btn-icon btn-sm\"\r\n              (click)=\"view(dataItem.permitId)\"\r\n            >\r\n              <span\r\n                [inlineSVG]=\"'./assets/media/icons/duotune/general/gen019.svg'\"\r\n                class=\"svg-icon svg-icon-3 svg-icon-primary\"\r\n              >\r\n              </span>\r\n            </a> -->\r\n            <a\r\n              title=\"View\"\r\n              class=\"btn btn-icon btn-sm\"\r\n              (click)=\"edit(dataItem.permitId)\"\r\n            >\r\n              <i class=\"fas fa-pencil\" style=\"color: var(--bs-primary, #0d6efd); font-size: 1rem;\"></i>\r\n            </a>\r\n          </div>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permit Number Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitNumber'\"\r\n        field=\"permitNumber\"\r\n        title=\"Permit #\"\r\n        [width]=\"180\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.permitNumber }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitName'\"\r\n        field=\"permitName\"\r\n        title=\"Permit/Sub Project Name\"\r\n        [width]=\"180\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.permitName }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n  <kendo-grid-column\r\n        *ngIf=\"column === 'projectName'\"\r\n        field=\"projectName\"\r\n        title=\"Project Name\"\r\n        [width]=\"180\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.projectName }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <!-- Permit Type Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitType'\"\r\n        field=\"permitType\"\r\n        title=\"Permit Type\"\r\n        [width]=\"200\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.permitType }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permit Category Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitCategory'\"\r\n        field=\"permitCategory\"\r\n        title=\"Category\"\r\n        [width]=\"120\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span\r\n            class=\"badge\"\r\n            [ngClass]=\"getCategoryClass(dataItem.permitCategory)\"\r\n          >\r\n            {{ dataItem.permitCategory }}\r\n          </span>\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter  let-filterService=\"filterService\" let-column=\"column\">\r\n           <kendo-dropdownlist\r\n       [column]=\"column\"\r\n      [filter]=\"filter\"\r\n      [data]=\"advancedFilterOptions.categories\"\r\n      [(ngModel)]=\"appliedFilters.status\"\r\n      textField=\"text\"\r\n      valueField=\"value\"\r\n      [valuePrimitive]=\"true\"\r\n      [defaultItem]=\"{ text: 'All', value: null }\"\r\n      (valueChange)=\"onMenuDropdownChange($event, column.field, filterService)\">\r\n    </kendo-dropdownlist>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permit Status Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'internalReviewStatus'\"\r\n        field=\"internalReviewStatus\"\r\n        title=\"Internal Review Status\"\r\n        [width]=\"150\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span class=\"badge\" [ngClass]=\"getStatusClass(dataItem.internalReviewStatus)\">\r\n            {{ dataItem.internalReviewStatus }}\r\n          </span>\r\n        </ng-template>\r\n\r\n       <ng-template kendoGridFilterMenuTemplate let-filter let-filterService=\"filterService\" let-column=\"column\">\r\n            <kendo-dropdownlist\r\n       [column]=\"column\"\r\n      [filter]=\"filter\"\r\n      [data]=\"advancedFilterOptions.categoriess\"\r\n      [(ngModel)]=\"appliedFilters.internalReviewStatus\"\r\n      textField=\"text\"\r\n      valueField=\"value\"\r\n      [valuePrimitive]=\"true\"\r\n      [defaultItem]=\"{ text: 'All', value: null }\"\r\n      (valueChange)=\"onMenuDropdownChange($event, column.field, filterService)\">\r\n    </kendo-dropdownlist>\r\n          <!-- <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu> -->\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitStatus'\"\r\n        field=\"permitStatus\"\r\n        title=\"Permit Status\"\r\n        [width]=\"150\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span class=\"badge\" [ngClass]=\"getStatusClass(dataItem.permitStatus)\">\r\n            {{ dataItem.permitStatus }}\r\n          </span>\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-filterService=\"filterService\" let-column=\"column\">\r\n            <kendo-dropdownlist\r\n       [column]=\"column\"\r\n      [filter]=\"filter\"\r\n      [data]=\"advancedFilterOptions.status\"\r\n      [(ngModel)]=\"appliedFilters.status\"\r\n      textField=\"text\"\r\n      valueField=\"value\"\r\n      [valuePrimitive]=\"true\"\r\n      [defaultItem]=\"{ text: 'All', value: null }\"\r\n      (valueChange)=\"onMenuDropdownChange($event, column.field, filterService)\">\r\n    </kendo-dropdownlist>\r\n          <!-- <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu> -->\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Location Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'location'\"\r\n        field=\"location\"\r\n        title=\"Location\"\r\n        [width]=\"200\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.location }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Project Name Column -->\r\n    \r\n\r\n      <!-- Permit Applied Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitAppliedDate'\"\r\n        field=\"permitAppliedDate\"\r\n        title=\"Applied Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.permitAppliedDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permit Expiration Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitExpirationDate'\"\r\n        field=\"permitExpirationDate\"\r\n        title=\"Expiration Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.permitExpirationDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitFinalDate'\"\r\n        field=\"permitFinalDate\"\r\n        title=\"Final Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.permitFinalDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitCompleteDate'\"\r\n        field=\"permitCompleteDate\"\r\n        title=\"Complete Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.permitCompleteDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Attention Reason Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'attentionReason'\"\r\n        field=\"attentionReason\"\r\n        title=\"Attention Reason\"\r\n        [width]=\"180\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.attentionReason || \"\" }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Last Updated Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'lastUpdatedDate'\"\r\n        field=\"lastUpdatedDate\"\r\n        title=\"Updated Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.lastUpdatedDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n    </ng-container>\r\n  </kendo-grid>\r\n</div>\r\n\r\n<!-- No Data Message -->\r\n<div *ngIf=\"!IsListHasValue\" class=\"text-center py-5\">\r\n  <div class=\"text-muted\">\r\n    <i class=\"fas fa-inbox fa-3x mb-3\"></i>\r\n    <h4>No Permits Found</h4>\r\n    <p>No permits match your current search criteria.</p>\r\n    <button class=\"btn btn-primary\" (click)=\"clearSearch(); loadTable()\">\r\n      Clear Search\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n<!-- Example trigger button -->\r\n<!-- <button class=\"btn btn-danger\" (click)=\"open(deleteModal)\">Delete</button> -->\r\n"], "mappings": "AAcA,SAAcA,IAAI,QAAQ,QAAQ;AAClC,SACEC,OAAO,EAEPC,YAAY,EACZC,oBAAoB,QAEf,MAAM;AAUb,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC3BvEC,EAHN,CAAAC,cAAA,aAAqE,aACtC,aACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IAuCEH,EADF,CAAAC,cAAA,cAA2D,wBAQxD;IAJCD,EAAA,CAAAI,gBAAA,2BAAAC,kFAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,UAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,UAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAGxBN,EADA,CAAAc,UAAA,qBAAAC,4EAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWJ,MAAA,CAAAO,eAAA,CAAAV,MAAA,CAAuB;IAAA,EAAC,2BAAAD,kFAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAClBJ,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC;IAKtCjB,EAJG,CAAAG,YAAA,EAAgB,EAIb;IAENH,EAAA,CAAAkB,SAAA,wBAAuC;IAIrClB,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAA0E;IAAhBD,EAAA,CAAAc,UAAA,mBAAAK,mEAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAW,GAAA,EAAK;IAAA,EAAC;IACvEpB,EAAA,CAAAkB,SAAA,eAGQ;IACRlB,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAO,oEAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IAGxBtB,EAAA,CAAAkB,SAAA,aAIK;IACPlB,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,gCAOC;IAFCD,EAAA,CAAAc,UAAA,uBAAAS,sFAAAjB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAaJ,MAAA,CAAAe,aAAA,CAAAlB,MAAA,CAAqB;IAAA,EAAC;IAGrCN,EAAA,CAAAG,YAAA,EAAuB;IAavBH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAW,oEAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAiB,UAAA,EAAY;IAAA,EAAC;IAGtB1B,EAAA,CAAAkB,SAAA,aAA2B;IAC7BlB,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAa,oEAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmB,WAAA,EAAa;IAAA,EAAC;IAGvB5B,EAAA,CAAAkB,SAAA,aAA+B;IACjClB,EAAA,CAAAG,YAAA,EAAS;;;;IAhFLH,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAA8B,WAAA,oBAAsB;IAEtB9B,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAG,UAAA,CAAwB;IACxBZ,EAAA,CAAAgC,UAAA,qBAAoB;IAcKhC,EAAA,CAAA6B,SAAA,GAA6B;IAA7B7B,EAAA,CAAAiC,iBAAA,CAAAxB,MAAA,CAAAyB,IAAA,CAAAC,aAAA,MAA6B;IAMtDnC,EAAA,CAAA6B,SAAA,GAA8D;IAA9D7B,EAAA,CAAAgC,UAAA,+DAA8D;IAc9DhC,EAAA,CAAA6B,SAAA,GAA+B;IAC/B7B,EADA,CAAAoC,WAAA,eAAA3B,MAAA,CAAA4B,UAAA,CAA+B,gBAAA5B,MAAA,CAAA4B,UAAA,CACC;IAOlCrC,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA6B,aAAA,CAAsB;;;;;;IA8ClBtC,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxCH,EAAA,CAAAC,cAAA,6BAMC;IAFCD,EAAA,CAAAI,gBAAA,2BAAAmC,6FAAAjC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAgC,cAAA,CAAAC,MAAA,EAAApC,MAAA,MAAAG,MAAA,CAAAgC,cAAA,CAAAC,MAAA,GAAApC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAIvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAAsB,gBACM;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1CH,EAAA,CAAAC,cAAA,6BAMC;IAFCD,EAAA,CAAAI,gBAAA,2BAAAuC,6FAAArC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAgC,cAAA,CAAAG,QAAA,EAAAtC,MAAA,MAAAG,MAAA,CAAAgC,cAAA,CAAAG,QAAA,GAAAtC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAqC;IAIzCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,eAA6C,kBAM1C;IADCD,EAAA,CAAAc,UAAA,mBAAA+B,0EAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAqC,oBAAA,EAAsB;IAAA,EAAC;IAEhC9C,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAc,UAAA,mBAAAiC,0EAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAuC,oBAAA,EAAsB;IAAA,EAAC;IAEhChD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAc,UAAA,mBAAAmC,0EAAA;MAAAjD,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAAJ,MAAA,CAAAyC,mBAAA,GAA+B,KAAK;IAAA,EAAC;IAErClD,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IA7CEH,EAAA,CAAA6B,SAAA,GAAqC;IAArC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA0C,qBAAA,CAAAT,MAAA,CAAqC;IAGrC1C,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAgC,cAAA,CAAAC,MAAA,CAAmC;IAQnC1C,EAAA,CAAA6B,SAAA,GAAyC;IAAzC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA0C,qBAAA,CAAAC,UAAA,CAAyC;IAGzCpD,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAgC,cAAA,CAAAG,QAAA,CAAqC;;;;;IAtB7C5C,EAAA,CAAAqD,UAAA,IAAAC,gDAAA,mBAGC;;;;IAFEtD,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAyC,mBAAA,CAAyB;;;;;;IAyD5BlD,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAyC,mEAAA;MAAAvD,EAAA,CAAAO,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAAJ,MAAA,CAAAyC,mBAAA,IAAAzC,MAAA,CAAAyC,mBAAA;IAAA,EAAoD;IAGpDlD,EAAA,CAAAkB,SAAA,YAA6B;IAC7BlB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAyD,kBAAA,MAAAhD,MAAA,CAAAyC,mBAAA,yCACF;;;;;;IA0BMlD,EAZF,CAAAC,cAAA,cAA0B,YAgBvB;IADCD,EAAA,CAAAc,UAAA,mBAAA4C,iGAAA;MAAA,MAAAC,WAAA,GAAA3D,EAAA,CAAAO,aAAA,CAAAqD,GAAA,EAAAC,QAAA;MAAA,MAAApD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAqD,IAAA,CAAAH,WAAA,CAAAI,QAAA,CAAuB;IAAA,EAAC;IAEjC/D,EAAA,CAAAkB,SAAA,YAAyF;IAE7FlB,EADE,CAAAG,YAAA,EAAI,EACA;;;;;IA5BVH,EAAA,CAAAC,cAAA,4BAOC;IACCD,EAAA,CAAAqD,UAAA,IAAAW,6EAAA,0BAA2D;IAsB7DhE,EAAA,CAAAG,YAAA,EAAoB;;;IAxBlBH,EAFA,CAAAgC,UAAA,aAAY,mBACM,qBACE;;;;;IAkClBhC,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAAQ,WAAA,CAAAC,YAAA,MACF;;;;;IAGElE,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IACnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAmC,UAAA,CAAiB,WAAAC,SAAA,CACA,gBACF;;;;;IAdrBpE,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAAgB,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1EtE,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAwBXhC,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAAc,YAAA,CAAAC,UAAA,MACF;;;;;IAGExE,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IACnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAyC,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdrB1E,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAAsB,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1E5E,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAwBXhC,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAAoB,YAAA,CAAAC,WAAA,MACF;;;;;IAGE9E,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IACnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAA+C,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdzBhF,EAAA,CAAAC,cAAA,4BAKK;IAKCD,EAJA,CAAAqD,UAAA,IAAA4B,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1ElF,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAyBXhC,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAA0B,YAAA,CAAAC,UAAA,MACF;;;;;IAGEpF,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IACnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAqD,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdrBtF,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAAkC,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1ExF,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IA0BXhC,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHLH,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAAgF,gBAAA,CAAAC,YAAA,CAAAC,cAAA,EAAqD;IAErD3F,EAAA,CAAA6B,SAAA,EACF;IADE7B,EAAA,CAAAyD,kBAAA,MAAAiC,YAAA,CAAAC,cAAA,MACF;;;;;;IAIC3F,EAAA,CAAAC,cAAA,6BASqE;IAL1ED,EAAA,CAAAI,gBAAA,2BAAAwF,0HAAAtF,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAsF,IAAA;MAAA,MAAApF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAgC,cAAA,CAAAC,MAAA,EAAApC,MAAA,MAAAG,MAAA,CAAAgC,cAAA,CAAAC,MAAA,GAAApC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAKnCN,EAAA,CAAAc,UAAA,yBAAAgF,wHAAAxF,MAAA;MAAA,MAAAyF,OAAA,GAAA/F,EAAA,CAAAO,aAAA,CAAAsF,IAAA;MAAA,MAAAG,iBAAA,GAAAD,OAAA,CAAAE,aAAA;MAAA,MAAAC,UAAA,GAAAH,OAAA,CAAAI,MAAA;MAAA,MAAA1F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAeJ,MAAA,CAAA2F,oBAAA,CAAA9F,MAAA,EAAA4F,UAAA,CAAAG,KAAA,EAAAL,iBAAA,CAAyD;IAAA,EAAC;IAC3EhG,EAAA,CAAAG,YAAA,EAAqB;;;;;;IAPnBH,EAFC,CAAAgC,UAAA,WAAAkE,UAAA,CAAiB,WAAAI,UAAA,CACD,SAAA7F,MAAA,CAAA0C,qBAAA,CAAAC,UAAA,CACwB;IACzCpD,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAgC,cAAA,CAAAC,MAAA,CAAmC;IAInC1C,EADA,CAAAgC,UAAA,wBAAuB,gBAAAhC,EAAA,CAAAuG,eAAA,IAAAC,GAAA,EACqB;;;;;IAxB5CxG,EAAA,CAAAC,cAAA,4BAKC;IAUCD,EATA,CAAAqD,UAAA,IAAAoD,6EAAA,0BAA2D,IAAAC,6EAAA,0BASgD;IAa7G1G,EAAA,CAAAG,YAAA,EAAoB;;;IAxBlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAkCXhC,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFaH,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAAkG,cAAA,CAAAC,YAAA,CAAAC,oBAAA,EAAyD;IAC3E7G,EAAA,CAAA6B,SAAA,EACF;IADE7B,EAAA,CAAAyD,kBAAA,MAAAmD,YAAA,CAAAC,oBAAA,MACF;;;;;;IAIE7G,EAAA,CAAAC,cAAA,6BASoE;IAL1ED,EAAA,CAAAI,gBAAA,2BAAA0G,0HAAAxG,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAAtG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAgC,cAAA,CAAAoE,oBAAA,EAAAvG,MAAA,MAAAG,MAAA,CAAAgC,cAAA,CAAAoE,oBAAA,GAAAvG,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAiD;IAKjDN,EAAA,CAAAc,UAAA,yBAAAkG,wHAAA1G,MAAA;MAAA,MAAA2G,OAAA,GAAAjH,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAAG,iBAAA,GAAAD,OAAA,CAAAhB,aAAA;MAAA,MAAAkB,UAAA,GAAAF,OAAA,CAAAd,MAAA;MAAA,MAAA1F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAeJ,MAAA,CAAA2F,oBAAA,CAAA9F,MAAA,EAAA6G,UAAA,CAAAd,KAAA,EAAAa,iBAAA,CAAyD;IAAA,EAAC;IAC3ElH,EAAA,CAAAG,YAAA,EAAqB;;;;;;IAPnBH,EAFC,CAAAgC,UAAA,WAAAmF,UAAA,CAAiB,WAAAC,UAAA,CACD,SAAA3G,MAAA,CAAA0C,qBAAA,CAAAkE,WAAA,CACyB;IAC1CrH,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAgC,cAAA,CAAAoE,oBAAA,CAAiD;IAIjD7G,EADA,CAAAgC,UAAA,wBAAuB,gBAAAhC,EAAA,CAAAuG,eAAA,IAAAC,GAAA,EACqB;;;;;IArB5CxG,EAAA,CAAAC,cAAA,4BAKC;IAOAD,EANC,CAAAqD,UAAA,IAAAiE,6EAAA,0BAA2D,IAAAC,6EAAA,0BAM8C;IAqB3GvH,EAAA,CAAAG,YAAA,EAAoB;;;IA7BlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAqCXhC,EAAA,CAAAC,cAAA,eAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFaH,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAAkG,cAAA,CAAAa,YAAA,CAAAC,YAAA,EAAiD;IACnEzH,EAAA,CAAA6B,SAAA,EACF;IADE7B,EAAA,CAAAyD,kBAAA,MAAA+D,YAAA,CAAAC,YAAA,MACF;;;;;;IAIEzH,EAAA,CAAAC,cAAA,6BASoE;IAL1ED,EAAA,CAAAI,gBAAA,2BAAAsH,0HAAApH,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAgC,cAAA,CAAAC,MAAA,EAAApC,MAAA,MAAAG,MAAA,CAAAgC,cAAA,CAAAC,MAAA,GAAApC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAKnCN,EAAA,CAAAc,UAAA,yBAAA8G,wHAAAtH,MAAA;MAAA,MAAAuH,OAAA,GAAA7H,EAAA,CAAAO,aAAA,CAAAoH,IAAA;MAAA,MAAAG,iBAAA,GAAAD,OAAA,CAAA5B,aAAA;MAAA,MAAA8B,UAAA,GAAAF,OAAA,CAAA1B,MAAA;MAAA,MAAA1F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAeJ,MAAA,CAAA2F,oBAAA,CAAA9F,MAAA,EAAAyH,UAAA,CAAA1B,KAAA,EAAAyB,iBAAA,CAAyD;IAAA,EAAC;IAC3E9H,EAAA,CAAAG,YAAA,EAAqB;;;;;;IAPnBH,EAFC,CAAAgC,UAAA,WAAA+F,UAAA,CAAiB,WAAAC,UAAA,CACD,SAAAvH,MAAA,CAAA0C,qBAAA,CAAAT,MAAA,CACoB;IACrC1C,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAgC,cAAA,CAAAC,MAAA,CAAmC;IAInC1C,EADA,CAAAgC,UAAA,wBAAuB,gBAAAhC,EAAA,CAAAuG,eAAA,IAAAC,GAAA,EACqB;;;;;IArB5CxG,EAAA,CAAAC,cAAA,4BAKC;IAOCD,EANA,CAAAqD,UAAA,IAAA4E,6EAAA,0BAA2D,IAAAC,6EAAA,0BAM+C;IAqB5GlI,EAAA,CAAAG,YAAA,EAAoB;;;IA7BlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAuCXhC,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAA0E,YAAA,CAAAC,QAAA,MACF;;;;;IAGEpI,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IACnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAqG,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdrBtI,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAAkF,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1ExI,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IA6BXhC,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAAhD,MAAA,CAAAgI,UAAA,CAAAC,YAAA,CAAAC,iBAAA,OACF;;;;;IAQE3I,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAAkB,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnElB,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAAgC,UAAA,WAAA4G,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IApBrC9I,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAA0F,8EAAA,0BAA2D,IAAAC,8EAAA,0BAS1D;IAeHhJ,EAAA,CAAAG,YAAA,EAAoB;;;IA1BlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAoCXhC,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAAhD,MAAA,CAAAgI,UAAA,CAAAQ,YAAA,CAAAC,oBAAA,OACF;;;;;IAQElJ,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAAkB,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnElB,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAAgC,UAAA,WAAAmH,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IApBrCrJ,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAAiG,8EAAA,0BAA2D,IAAAC,8EAAA,0BAS1D;IAeHvJ,EAAA,CAAAG,YAAA,EAAoB;;;IA1BlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAkCXhC,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAAhD,MAAA,CAAAgI,UAAA,CAAAe,YAAA,CAAAC,eAAA,OACF;;;;;IAQEzJ,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAAkB,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnElB,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAAgC,UAAA,WAAA0H,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IApBrC5J,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAAwG,8EAAA,0BAA2D,IAAAC,8EAAA,0BAS1D;IAeH9J,EAAA,CAAAG,YAAA,EAAoB;;;IA1BlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAkCXhC,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAAhD,MAAA,CAAAgI,UAAA,CAAAsB,YAAA,CAAAC,kBAAA,OACF;;;;;IAQEhK,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAAkB,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnElB,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAAgC,UAAA,WAAAiI,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IApBrCnK,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAA+G,8EAAA,0BAA2D,IAAAC,8EAAA,0BAS1D;IAeHrK,EAAA,CAAAG,YAAA,EAAoB;;;IA1BlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IAoCXhC,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAA6G,YAAA,CAAAC,eAAA,YACF;;;;;IAGEvK,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IACnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAwI,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdrBzK,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAAqH,8EAAA,0BAA2D,IAAAC,8EAAA,0BAIa;IAU1E3K,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IA0BXhC,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAyD,kBAAA,MAAAhD,MAAA,CAAAgI,UAAA,CAAAmC,YAAA,CAAAC,eAAA,OACF;;;;;IAGE7K,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAkB,SAAA,qCAAiE;IACnElB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAA8I,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdrB/K,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAqD,UAAA,IAAA2H,8EAAA,0BAA2D,IAAAC,8EAAA,0BAIa;IAU1EjL,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAAgC,UAAA,cAAa;;;;;IApZjBhC,EAAA,CAAAkL,uBAAA,GAAiD;IAgZ/ClL,EA9YA,CAAAqD,UAAA,IAAA8H,+DAAA,gCAOC,IAAAC,+DAAA,gCA+BA,IAAAC,+DAAA,gCAqBA,IAAAC,+DAAA,gCAqBA,IAAAC,+DAAA,gCAsBA,IAAAC,+DAAA,gCAuBA,IAAAC,+DAAA,gCA+BA,IAAAC,+DAAA,gCAkCA,IAAAC,+DAAA,gCAoCA,KAAAC,gEAAA,gCA0BA,KAAAC,gEAAA,gCAiCA,KAAAC,gEAAA,gCA+BA,KAAAC,gEAAA,gCA+BA,KAAAC,gEAAA,gCAiCA,KAAAC,gEAAA,gCAuBA;;;;;IAlZEjM,EAAA,CAAA6B,SAAA,EAAyB;IAAzB7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,cAAyB;IAiCzBlM,EAAA,CAAA6B,SAAA,EAA+B;IAA/B7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,oBAA+B;IAqB/BlM,EAAA,CAAA6B,SAAA,EAA6B;IAA7B7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,kBAA6B;IAqB7BlM,EAAA,CAAA6B,SAAA,EAA8B;IAA9B7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,mBAA8B;IAsB9BlM,EAAA,CAAA6B,SAAA,EAA6B;IAA7B7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,kBAA6B;IAuB7BlM,EAAA,CAAA6B,SAAA,EAAiC;IAAjC7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,sBAAiC;IA+BjClM,EAAA,CAAA6B,SAAA,EAAuC;IAAvC7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,4BAAuC;IAkCvClM,EAAA,CAAA6B,SAAA,EAA+B;IAA/B7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,oBAA+B;IAoC/BlM,EAAA,CAAA6B,SAAA,EAA2B;IAA3B7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,gBAA2B;IA0B3BlM,EAAA,CAAA6B,SAAA,EAAoC;IAApC7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,yBAAoC;IAiCpClM,EAAA,CAAA6B,SAAA,EAAuC;IAAvC7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,4BAAuC;IA+BvClM,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,uBAAkC;IA+BlClM,EAAA,CAAA6B,SAAA,EAAqC;IAArC7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,0BAAqC;IAiCrClM,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,uBAAkC;IAuBlClM,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAkK,UAAA,uBAAkC;;;;;;IA0BzClM,EADF,CAAAC,cAAA,cAAsD,cAC5B;IACtBD,EAAA,CAAAkB,SAAA,YAAuC;IACvClB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qDAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrDH,EAAA,CAAAC,cAAA,iBAAqE;IAArCD,EAAA,CAAAc,UAAA,mBAAAqL,2DAAA;MAAAnM,EAAA,CAAAO,aAAA,CAAA6L,IAAA;MAAA,MAAA3L,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAA4L,WAAA,EAAa;MAAA,OAAArM,EAAA,CAAAa,WAAA,CAAEJ,MAAA,CAAA6L,SAAA,EAAW;IAAA,EAAC;IAClEtM,EAAA,CAAAE,MAAA,qBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;ADrlBN,OAAM,MAAOoM,mBAAmB;EAsXpBC,MAAA;EACAC,KAAA;EACAC,cAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,kBAAA;EACAC,YAAA;EACAC,GAAA;EACDC,UAAA;EACAC,cAAA;EACCC,QAAA;EA/XeC,IAAI;EAE7B;EACOC,iBAAiB,GAAU,EAAE;EAC7BC,QAAQ,GAAQ,EAAE;EAClBC,cAAc,GAAY,KAAK;EAE/BC,OAAO,GAAY,KAAK;EACxBC,SAAS,GAAY,KAAK;EAEjCC,SAAS,GAAQ,EAAE;EAEnB;EACO7M,UAAU,GAAW,EAAE;EACtB8M,WAAW,GAAG,IAAI9N,OAAO,EAAU;EACnC+N,kBAAkB;EAC5BvH,oBAAoBA,CAACwH,KAAU,EAAEvH,KAAa,EAAEJ,aAA4B;IAC1E,MAAM4H,IAAI,GACRD,KAAK,IAAI,IAAI,GACT;MAAEE,OAAO,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE,GAC7B;MAAED,OAAO,EAAE,CAAC;QAAEzH,KAAK;QAAE2H,QAAQ,EAAE,IAAI;QAAEJ;MAAK,CAAE,CAAC;MAAEG,KAAK,EAAE;IAAK,CAAE;IACnE9H,aAAa,CAACgI,MAAM,CAACJ,IAAI,CAAC;EAC5B;EACE;EACOI,MAAM,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAED,OAAO,EAAE;EAAE,CAAE;EACjEI,UAAU,GAA8B;IAAEH,KAAK,EAAE,KAAK;IAAED,OAAO,EAAE;EAAE,CAAE;EACrEK,aAAa,GAIf,EAAE;EAEAC,aAAa,GAAkD,CACpE;IAAEC,IAAI,EAAE,KAAK;IAAET,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAES,IAAI,EAAE,QAAQ;IAAET,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAES,IAAI,EAAE,UAAU;IAAET,KAAK,EAAE;EAAU,CAAE,CACxC;EAED;EACOzK,qBAAqB,GAAG;IAC7BT,MAAM,EAAE,CACL;MAAE2L,IAAI,EAAE,KAAK;MAAET,KAAK,EAAE;IAAI,CAAE,EACjC;MAAES,IAAI,EAAE,mBAAmB;MAAET,KAAK,EAAE;IAAmB,CAAE,EACzD;MAAES,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAS,CAAE,EACrC;MAAES,IAAI,EAAE,UAAU;MAAET,KAAK,EAAE;IAAU,CAAE,EACvC;MAAES,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAS,CAAE,EACrC;MAAES,IAAI,EAAE,UAAU;MAAET,KAAK,EAAE;IAAU,CAAE,EACvC;MAAES,IAAI,EAAE,UAAU;MAAET,KAAK,EAAE;IAAU,CAAE,EACvC;MAAES,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAS,CAAE,EACrC;MAAES,IAAI,EAAE,UAAU;MAAET,KAAK,EAAE;IAAU,CAAE,EACvC;MAAES,IAAI,EAAE,WAAW;MAAET,KAAK,EAAE;IAAW,CAAE,EACzC;MAAES,IAAI,EAAE,QAAQ;MAAET,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAES,IAAI,EAAE,iCAAiC;MAAET,KAAK,EAAE;IAAiC,CAAE,EACrF;MAAES,IAAI,EAAE,oBAAoB;MAAET,KAAK,EAAE;IAAoB,CAAE,EAC3D;MAAES,IAAI,EAAE,MAAM;MAAET,KAAK,EAAE;IAAM,CAAE,CACqB;IAClDxK,UAAU,EAAE,CACV;MAAEiL,IAAI,EAAE,KAAK;MAAET,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAES,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAS,CAAE,EACrC;MAAES,IAAI,EAAE,YAAY;MAAET,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAES,IAAI,EAAE,YAAY;MAAET,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAES,IAAI,EAAE,WAAW;MAAET,KAAK,EAAE;IAAW,CAAE,EACzC;MAAES,IAAI,EAAE,eAAe;MAAET,KAAK,EAAE;IAAe,CAAE,CAED;IAClDvG,WAAW,EAAE,CACX;MAAEgH,IAAI,EAAE,KAAK;MAAET,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAES,IAAI,EAAE,UAAU;MAAET,KAAK,EAAE;IAAU,CAAE,EACvC;MAAES,IAAI,EAAE,uBAAuB;MAAET,KAAK,EAAE;IAAuB,CAAE,EACjE;MAAES,IAAI,EAAE,cAAc;MAAET,KAAK,EAAE;IAAc,CAAE,EAC/C;MAAES,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAS,CAAE,EACrC;MAAES,IAAI,EAAE,cAAc;MAAET,KAAK,EAAE;IAAc,CAAE,EAC/C;MAAES,IAAI,EAAE,WAAW;MAAET,KAAK,EAAE;IAAW,CAAE,EACzC;MAAES,IAAI,EAAE,mBAAmB;MAAET,KAAK,EAAE;IAAmB;IAEvD;IAAA;GAGH;EAED;EACO1K,mBAAmB,GAAG,KAAK;EAC3BT,cAAc,GAIjB,EAAE;EAEN;EACO6L,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7BC,YAAY,GAAa,EAAE;EAC3BC,gBAAgB,GAAa,EAAE;EAC/BC,UAAU;EACVC,YAAY;EACZ3M,UAAU,GAAG,KAAK;EAEzB;EACO4M,gBAAgB,GAQlB,CACH;IACE5I,KAAK,EAAE,QAAQ;IACf6I,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;GACR,EACA;IACCjJ,KAAK,EAAE,YAAY;IACnB6I,KAAK,EAAE,yBAAyB;IAChCC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,cAAc;IACrB6I,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EAEC;IACAjJ,KAAK,EAAE,aAAa;IACpB6I,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACE;IACDjJ,KAAK,EAAE,gBAAgB;IACvB6I,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,YAAY;IACnB6I,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EAED;IACEjJ,KAAK,EAAE,cAAc;IACrB6I,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,QAAQ;IACdE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,sBAAsB;IAC7B6I,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,QAAQ;IACdE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,UAAU;IACjB6I,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EAED;IACEjJ,KAAK,EAAE,mBAAmB;IAC1B6I,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,sBAAsB;IAC7B6I,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,iBAAiB;IACxB6I,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,oBAAoB;IAC3B6I,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,iBAAiB;IACxB6I,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEjJ,KAAK,EAAE,iBAAiB;IACxB6I,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,CACF;EACIE,UAAU,GAAS,CACxB;IAAEnB,IAAI,EAAE,UAAU;IAAET,KAAK,EAAE;EAAU,CAAE,EACvC;IAAES,IAAI,EAAE,SAAS;IAAET,KAAK,EAAE;EAAS,CAAE,EACrC;IAAES,IAAI,EAAE,UAAU;IAAET,KAAK,EAAE;EAAU,CAAE,CACxC;EACC;EACO6B,IAAI,GAAqB,CAAC;IAAEpJ,KAAK,EAAE,iBAAiB;IAAEqJ,GAAG,EAAE;EAAM,CAAE,CAAC;EAEpExN,IAAI,GAAQ;IACjByN,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACbzN,aAAa,EAAE,CAAC;IAChB0N,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EAEMC,IAAI,GAAW,CAAC;EAEvB;EACOC,YAAY,GAAU,EAAE;EACxBC,aAAa,GAAY,KAAK;EAErC;EACO5N,aAAa,GAAG,CACrB;IAAE+L,IAAI,EAAE,KAAK;IAAET,KAAK,EAAE;EAAK,CAAE,EAC7B;IAAES,IAAI,EAAE,cAAc;IAAET,KAAK,EAAE;EAAU,CAAE,CAC5C;EACDuC,gBAAgB;EAChBpM,QAAQ;EACRqM,YAAY;EACJC,sBAAsBA,CAAA;IAC5BC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAE/C;IACA,IAAI,CAAC7B,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC3C,IAAI,CAACJ,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;IAE7C;IACA,IAAI,CAACa,IAAI,GAAG,CAAC;MAAEpJ,KAAK,EAAE,iBAAiB;MAAEqJ,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAACxN,IAAI,CAAC4N,OAAO,GAAG,iBAAiB;IACrC,IAAI,CAAC5N,IAAI,CAAC6N,QAAQ,GAAG,MAAM;IAE3B;IACA,IAAI,CAAC7N,IAAI,CAAC0N,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IAEb;IACA,IAAI,CAAC/B,MAAM,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAED,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACK,aAAa,GAAG,EAAE;IAEvB;IACA,IAAI,CAAC1L,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAC7B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACsC,mBAAmB,GAAG,KAAK;IAEhCoN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9B7B,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCa,IAAI,EAAE,IAAI,CAACA,IAAI;MACfxB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBrN,UAAU,EAAE,IAAI,CAACA;KAClB,CAAC;IAEF;IACA,IAAI,IAAI,CAACuM,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACc,MAAM,GAAG;QAAEF,KAAK,EAAE,KAAK;QAAED,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACX,IAAI,CAACsC,IAAI,GAAG,CAAC;QAAEpJ,KAAK,EAAE,iBAAiB;QAAEqJ,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAACvC,IAAI,CAACqD,OAAO,CAACC,OAAO,CAAEtK,MAAW,IAAI;QACxC,IAAIA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,KAAK,QAAQ,EAAE;UAC7CF,MAAM,CAACuK,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAACvD,IAAI,CAAC6C,IAAI,GAAG,CAAC;MAClB,IAAI,CAAC7C,IAAI,CAACwD,QAAQ,GAAG,IAAI,CAACzO,IAAI,CAACyN,IAAI;IACrC;IAEA;IACA,IAAI,CAAC5C,GAAG,CAAC6D,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACzD,IAAI,EAAE;MACb0D,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1D,IAAI,CAAC2D,OAAO,EAAE;QACnB;QACA,IAAI,CAAC3D,IAAI,CAAC4D,KAAK,EAAE;MACnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACA,IAAI,CAACzE,SAAS,EAAE;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA0E,YACUxE,MAAc,EACdC,KAAqB,EACrBC,cAA8B,EAC9BC,eAAiC,EACjCC,wBAAkD,EAClDC,kBAAsC,EACtCC,YAAsB,EACtBC,GAAsB,EACvBC,UAAsB,EACtBC,cAA8B,EAC7BC,QAAyB;IAVzB,KAAAV,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,QAAQ,GAARA,QAAQ;IAEhB;IACA,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvCuD,IAAI,CAACpR,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/CoR,SAAS,CAAC,MAAK;MACd;MACA,IAAI,CAAC3D,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACb,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC;MAC9C,IAAI,CAACvB,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EAEA8E,QAAQA,CAAA;IACN,IAAI,CAAClE,QAAQ,CAACmE,WAAW,CAAC,SAAS,CAAC;IACpC,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAChF,SAAS,EAAE;EAClB;EAEAiF,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC9D,kBAAkB,EAAE;MAC3B,IAAI,CAACD,WAAW,CAACgE,QAAQ,EAAE;IAC7B;EACF;EAEQJ,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAAC7D,SAAS,GAAG,IAAI,CAACT,UAAU,CAAC2E,eAAe,EAAE;IAClD;IACA,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEQA,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,MAAMC,WAAW,GAAG,IAAI,CAACjF,kBAAkB,CAACkF,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACtE,SAAS,CAACuE,MAAM,CAAC;IAEjG,IAAIF,WAAW,EAAE;MACf;MACA,IAAI,CAACxD,SAAS,GAAGwD,WAAW,CAACvD,UAAU,IAAI,EAAE;MAC7C,IAAI,CAACC,aAAa,GAAGsD,WAAW,CAACtD,aAAa,IAAI,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;MAC1E,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC;IAClD,CAAC,MAAM;MACL;MACA,IAAI,CAACF,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;MAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;IACnD;IAEA;IACA,IAAI,CAACqD,wBAAwB,EAAE;EACjC;EAEQC,4BAA4BA,CAAA;IAClC,MAAMC,MAAM,GAAG;MACbC,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,IAAI,CAAC5E,SAAS,CAACuE;KACxB;IAED,IAAI,CAACnF,kBAAkB,CAACyF,aAAa,CAACH,MAAM,CAAC,CAACjB,SAAS,CAAC;MACtDrD,IAAI,EAAG0E,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,KAAK,KAAK,IAAID,QAAQ,CAACE,IAAI,EAAE;UAC/C;UACA,IAAI,CAACnE,SAAS,GAAGiE,QAAQ,CAACE,IAAI,CAACC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAAC,GAAG,EAAE;UACjF,IAAI,CAAClE,aAAa,GAAG+D,QAAQ,CAACE,IAAI,CAACjE,aAAa,GAAGmE,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACjE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;UACrH,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC;UAEhD;UACA,IAAI,CAACyD,wBAAwB,EAAE;UAE/B3B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;YACjDjC,SAAS,EAAE,IAAI,CAACA,SAAS;YACzBE,aAAa,EAAE,IAAI,CAACA;WACrB,CAAC;QACJ,CAAC,MAAM;UACL;UACA,IAAI,CAACF,SAAS,GAAG,EAAE;UACnB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;UAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;UACjD,IAAI,CAACqD,wBAAwB,EAAE;QACjC;MACF,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,IAAI,CAACvE,SAAS,GAAG,EAAE;QACnB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;QAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;QACjD,IAAI,CAACqD,wBAAwB,EAAE;MACjC;KACD,CAAC;EACJ;EAEQJ,iBAAiBA,CAAA;IACvB,IAAI,CAAClD,WAAW,GAAG,IAAI,CAACM,gBAAgB,CAAC6D,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAC1M,KAAK,CAAC;IAChE,IAAI,CAACuI,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,WAAW,CAAC;IAC3C,IAAI,CAACE,YAAY,GAAG,IAAI,CAACI,gBAAgB,CACtChB,MAAM,CAAE8E,GAAG,IAAKA,GAAG,CAAC3D,OAAO,CAAC,CAC5B0D,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAC1M,KAAK,CAAC;IAC1B,IAAI,CAACyI,gBAAgB,GAAG,IAAI,CAACG,gBAAgB,CAC1ChB,MAAM,CAAE8E,GAAG,IAAK,CAACA,GAAG,CAAC3D,OAAO,CAAC,CAC7B0D,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAC1M,KAAK,CAAC;EAC5B;EAEQmL,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACrE,IAAI,EAAE;MACb;MACA,IAAI,CAAC8E,wBAAwB,EAAE;IACjC;EACF;EAEQA,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAAC3D,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC0E,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACtE,YAAY,GAAG,IAAI,CAACJ,SAAS;IACpC;IAEA,IAAI,IAAI,CAACE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACwE,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAI,CAAC/D,gBAAgB,CAACQ,IAAI,CAAC,CAACwD,CAAC,EAAEC,CAAC,KAAI;QAClC,MAAMC,MAAM,GAAG,IAAI,CAAC3E,aAAa,CAAC4E,OAAO,CAACH,CAAC,CAAC5M,KAAK,CAAC;QAClD,MAAMgN,MAAM,GAAG,IAAI,CAAC7E,aAAa,CAAC4E,OAAO,CAACF,CAAC,CAAC7M,KAAK,CAAC;QAClD,OAAO8M,MAAM,GAAGE,MAAM;MACxB,CAAC,CAAC;IACJ;EACF;EAEA;EACO/G,SAASA,CAAA;IACd,IAAI,CAACgH,0BAA0B,EAAE;EACnC;EAEA;EACAA,0BAA0BA,CAAA;IACxB,IAAI,CAAC/F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACb,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,MAAM0F,cAAc,GAAG1C,UAAU,CAAC,MAAK;MACrCP,OAAO,CAACkD,IAAI,CAAC,mDAAmD,CAAC;MACjE,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX;IACA,MAAMC,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAACzR,IAAI,CAACyN,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfP,IAAI,EAAE,IAAI,CAACA,IAAI;MACfxB,MAAM,EAAE,IAAI,CAACA,MAAM,CAACH,OAAO;MAC3B8F,MAAM,EAAE,IAAI,CAAChT,UAAU;MACvBiT,cAAc,EAAE,IAAI,CAACpG,SAAS,CAACuE;KAChC;IAED1B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmD,KAAK,CAAC;IAE/C,IAAI,CAAChH,cAAc,CAACoH,sBAAsB,CAACJ,KAAK,CAAC,CAACxC,SAAS,CAAC;MAC1DrD,IAAI,EAAGkG,IAYN,IAAI;QACH;QACAC,YAAY,CAACT,cAAc,CAAC;QAE5BjD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwD,IAAI,CAAC;QAElC;QACA,IACEA,IAAI,CAACvB,OAAO,IACXuB,IAAI,CAACE,YAAY,IAChBF,IAAI,CAACE,YAAY,CAACC,MAAM,IACxBH,IAAI,CAACE,YAAY,CAACC,MAAM,CAAClB,MAAM,GAAG,CAAE,EACtC;UACA,MAAMkB,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7D5D,OAAO,CAACuC,KAAK,CAAC,uBAAuB,EAAEqB,MAAM,CAAC;UAE9C;UACA,IAAIH,IAAI,CAACE,YAAY,EAAEvR,MAAM,KAAK,GAAG,IAAIqR,IAAI,CAACrR,MAAM,KAAK,GAAG,EAAE;YAC5D4N,OAAO,CAACkD,IAAI,CAAC,6CAA6C,CAAC;YAC3D;YACA;UACF;UAEA,IAAI,CAACW,mBAAmB,EAAE;UAC1B;UACA,IAAI,CAAC5G,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACb,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;QACjD,CAAC,MAAM;UACL;UACA,MAAMoG,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMK,UAAU,GAAGH,YAAY,CAACF,IAAI,IAAI,EAAE;UAC1C,MAAMM,KAAK,GAAGJ,YAAY,CAACI,KAAK,IAAI,CAAC;UAErC,IAAI,CAAC/G,cAAc,GAAG8G,UAAU,CAACpB,MAAM,KAAK,CAAC;UAC7C,IAAI,CAAC5F,iBAAiB,GAAGgH,UAAU;UACnC,IAAI,CAAC/G,QAAQ,GAAG,IAAI,CAACD,iBAAiB;UACtC,IAAI,CAAClL,IAAI,CAACC,aAAa,GAAGkS,KAAK;UAC/B,IAAI,CAACnS,IAAI,CAAC2N,UAAU,GAAGyE,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACnS,IAAI,CAACyN,IAAI,CAAC;UAExD;UACA,IAAI,CAACtC,QAAQ,GAAG;YACd0G,IAAI,EAAEK,UAAU;YAChBC,KAAK,EAAEA;WACR;UACD/D,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACnD,iBAAiB,CAAC;UAC9DkD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAClD,QAAQ,CAAC;UAC5CiD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACjD,cAAc,CAAC;UACxDgD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACrO,IAAI,CAAC;UACpCoO,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACrO,IAAI,CAACC,aAAa,CAAC;UAC9DmO,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACrO,IAAI,CAAC2N,UAAU,CAAC;UAC5DS,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACP,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC9N,IAAI,CAACyN,IAAI,CAAC;UACrEW,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAG,IAAI,CAACP,IAAI,GAAG,CAAC,EAAG,GAAG,EAAEsE,IAAI,CAACE,GAAG,CAAC,IAAI,CAACxE,IAAI,GAAG,IAAI,CAAC9N,IAAI,CAACyN,IAAI,EAAE,IAAI,CAACzN,IAAI,CAACC,aAAa,CAAC,CAAC;UAEzH;UACA0O,UAAU,CAAC,MAAK;YACd,IAAI,IAAI,CAAC1D,IAAI,EAAE;cACbmD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACpD,IAAI,CAACkH,KAAK,CAAC;cAC3C/D,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACpD,IAAI,CAACwD,QAAQ,CAAC;cACjDL,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACpD,IAAI,CAAC6C,IAAI,CAAC;cACzCM,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACpD,IAAI,CAACsH,SAAS,CAAC;cACnDnE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACpD,IAAI,CAAC4G,IAAI,GAAG,IAAI,CAAC5G,IAAI,CAAC4G,IAAI,CAACf,MAAM,GAAG,SAAS,CAAC;cAEpF;cACA,IAAI,CAAC7F,IAAI,CAACkH,KAAK,GAAG,IAAI,CAACnS,IAAI,CAACC,aAAa;cACzC,IAAI,CAACgL,IAAI,CAAC6C,IAAI,GAAG,IAAI,CAACA,IAAI;cAC1B,IAAI,CAAC7C,IAAI,CAACsH,SAAS,GAAG,IAAI,CAACvS,IAAI,CAAC0N,UAAU;cAC1CU,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACpD,IAAI,CAACkH,KAAK,EAAE,UAAU,EAAE,IAAI,CAACrE,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC9N,IAAI,CAAC0N,UAAU,CAAC;YACrH;UACF,CAAC,EAAE,GAAG,CAAC;UAEP,IAAI,CAAC7C,GAAG,CAAC2H,YAAY,EAAE;UACvB;UACA,IAAI,CAACnH,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACb,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;QACjD;MACF,CAAC;MACDgF,KAAK,EAAGA,KAAc,IAAI;QACxB;QACAmB,YAAY,CAACT,cAAc,CAAC;QAE5BjD,OAAO,CAACuC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAElE;QACA,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAIA,KAAK,EAAE;UAC3D,MAAM8B,SAAS,GAAG9B,KAAY;UAC9B,IAAI8B,SAAS,CAACjS,MAAM,KAAK,GAAG,EAAE;YAC5B4N,OAAO,CAACkD,IAAI,CAAC,6CAA6C,CAAC;YAC3D;YACA;UACF;QACF;QAEA,IAAI,CAACW,mBAAmB,EAAE;QAC1B,IAAI,CAAC5G,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACb,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACD6D,QAAQ,EAAEA,CAAA,KAAK;QACb;QACAsC,YAAY,CAACT,cAAc,CAAC;QAE5B;QACA,IAAI,CAAChG,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACb,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEQsG,mBAAmBA,CAAA;IACzB,IAAI,CAAC7G,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACnL,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAAC2N,UAAU,GAAG,CAAC;IAExB;IACA,IAAI,CAACtC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACb,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;EACjD;EAEA;EACQ4F,kBAAkBA,CAAA;IACxB,IAAI,CAAClG,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACb,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;EACjD;EAEA;EACOjM,WAAWA,CAAA;IAChB0O,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,IAAI,CAACkD,kBAAkB,EAAE;IACzB,IAAI,CAACnH,SAAS,EAAE;EAClB;EAEA;EACOtL,eAAeA,CAAC4T,KAAoB;IACzC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACvI,SAAS,EAAE;IAClB;EACF;EAEOrL,cAAcA,CAAA;IACnBqP,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC3P,UAAU,CAAC;IAC/C;IACA,IAAI,CAAC8M,WAAW,CAACG,IAAI,CAAC,IAAI,CAACjN,UAAU,IAAI,EAAE,CAAC;EAC9C;EAEQkU,WAAWA,CAAA;IACjB,IAAI,CAACxI,SAAS,EAAE;EAClB;EAEOD,WAAWA,CAAA;IAChB,IAAI,CAACqB,WAAW,CAACG,IAAI,CAAC,IAAI,CAACjN,UAAU,CAAC;EACxC;EAEA;EACOmU,YAAYA,CAAC9G,MAAiC;IACnDqC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEtC,MAAM,CAAC;IAC7B,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC3B,SAAS,EAAE;EAClB;EAEOxJ,oBAAoBA,CAAA;IACzBwN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;IACA,IAAI,IAAI,CAAC9N,cAAc,CAACC,MAAM,EAAE;MAC9B,IAAI,CAACuL,MAAM,CAACH,OAAO,GAAG,IAAI,CAACG,MAAM,CAACH,OAAO,CAACG,MAAM,CAAE+G,CAAC,IAAI;QACrD,IAAI,OAAO,IAAIA,CAAC,EAAE;UAChB,OAAOA,CAAC,CAAC3O,KAAK,KAAK,cAAc;QACnC;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAAC4H,MAAM,CAACH,OAAO,CAACmH,IAAI,CAAC;QACvB5O,KAAK,EAAE,cAAc;QACrB2H,QAAQ,EAAE,IAAI;QACdJ,KAAK,EAAE,IAAI,CAACnL,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACD,cAAc,CAACG,QAAQ,EAAE;MAChC,IAAI,CAACqL,MAAM,CAACH,OAAO,GAAG,IAAI,CAACG,MAAM,CAACH,OAAO,CAACG,MAAM,CAAE+G,CAAC,IAAI;QACrD,IAAI,OAAO,IAAIA,CAAC,EAAE;UAChB,OAAOA,CAAC,CAAC3O,KAAK,KAAK,gBAAgB;QACrC;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAAC4H,MAAM,CAACH,OAAO,CAACmH,IAAI,CAAC;QACvB5O,KAAK,EAAE,gBAAgB;QACvB2H,QAAQ,EAAE,IAAI;QACdJ,KAAK,EAAE,IAAI,CAACnL,cAAc,CAACG;OAC5B,CAAC;IACJ;IAEA,IAAI,CAAC0J,SAAS,EAAE;EAClB;EAEOtJ,oBAAoBA,CAAA;IACzB,IAAI,CAACP,cAAc,GAAG,EAAE;IACxB,IAAI,CAACwL,MAAM,CAACH,OAAO,GAAG,EAAE;IACxB,IAAI,CAACxB,SAAS,EAAE;EAClB;EAEA;EACO4I,YAAYA,CAACzF,IAAsB;IACxCa,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEd,IAAI,CAAC;IAE3C;IACA,MAAM0F,YAAY,GAAGC,KAAK,CAACC,OAAO,CAAC5F,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;IACpD,IAAI,CAACA,IAAI,GAAG0F,YAAY,CAACnC,MAAM,GAAG,CAAC,GAC/BmC,YAAY,GACZ,CAAC;MAAE9O,KAAK,EAAE,iBAAiB;MAAEqJ,GAAG,EAAE;IAAM,CAAE,CAAC;IAE/C;IACA,IAAI,CAACxN,IAAI,CAAC4N,OAAO,GAAG,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC,CAACpJ,KAAK,IAAI,iBAAiB;IAC3D,IAAI,CAACnE,IAAI,CAAC6N,QAAQ,GAAG,IAAI,CAACN,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,IAAI,MAAM;IAE/CY,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACd,IAAI,CAAC;IAC3Ca,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;MAAET,OAAO,EAAE,IAAI,CAAC5N,IAAI,CAAC4N,OAAO;MAAEC,QAAQ,EAAE,IAAI,CAAC7N,IAAI,CAAC6N;IAAQ,CAAE,CAAC;IAExF,IAAI,CAACzD,SAAS,EAAE;EAClB;EAEA;EACOgJ,UAAUA,CAACV,KAAU;IAC1BtE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqE,KAAK,CAAC;IACxCtE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACrO,IAAI,CAACyN,IAAI,CAAC;IACjDW,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEqE,KAAK,CAACjE,QAAQ,CAAC;IAC/CL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEqE,KAAK,CAACH,SAAS,CAAC;IACjDnE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEqE,KAAK,CAAC5E,IAAI,CAAC;IAEtC;IACA,IAAI,CAACA,IAAI,GAAG4E,KAAK,CAAC5E,IAAI;IACtB,IAAI,CAAC9N,IAAI,CAACyN,IAAI,GAAGiF,KAAK,CAACjB,IAAI,IAAI,IAAI,CAACzR,IAAI,CAACyN,IAAI;IAC7C,IAAI,CAACzN,IAAI,CAAC0N,UAAU,GAAG0E,IAAI,CAACiB,KAAK,CAAC,IAAI,CAACvF,IAAI,GAAG,IAAI,CAAC9N,IAAI,CAACyN,IAAI,CAAC;IAE7DW,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACP,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC9N,IAAI,CAACyN,IAAI,EAAE,cAAc,EAAE,IAAI,CAACzN,IAAI,CAAC0N,UAAU,CAAC;IAC3GU,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAG,IAAI,CAACP,IAAI,GAAG,CAAC,EAAG,GAAG,EAAEsE,IAAI,CAACE,GAAG,CAAC,IAAI,CAACxE,IAAI,GAAG,IAAI,CAAC9N,IAAI,CAACyN,IAAI,EAAE,IAAI,CAACzN,IAAI,CAACC,aAAa,CAAC,CAAC;IAEzH,IAAI,CAACmK,SAAS,EAAE;EAClB;EAEA;EACOkJ,gBAAgBA,CAACZ,KAAU;IAChCtE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqE,KAAK,CAAC;IAC7CtE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqE,KAAK,CAACjE,QAAQ,CAAC;IAE7C,IAAIiE,KAAK,CAACjE,QAAQ,IAAIiE,KAAK,CAACjE,QAAQ,KAAK,IAAI,CAACzO,IAAI,CAACyN,IAAI,EAAE;MACvDW,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACrO,IAAI,CAACyN,IAAI,EAAE,IAAI,EAAEiF,KAAK,CAACjE,QAAQ,CAAC;MAC5E,IAAI,CAACzO,IAAI,CAACyN,IAAI,GAAGiF,KAAK,CAACjE,QAAQ;MAC/B,IAAI,CAACzO,IAAI,CAAC0N,UAAU,GAAG,CAAC,CAAC,CAAC;MAC1B,IAAI,CAACI,IAAI,GAAG,CAAC;MAEb;MACA,IAAI,IAAI,CAAC9N,IAAI,CAACC,aAAa,GAAG,CAAC,EAAE;QAC/B,IAAI,CAACD,IAAI,CAAC2N,UAAU,GAAGyE,IAAI,CAACC,IAAI,CAAC,IAAI,CAACrS,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,IAAI,CAACyN,IAAI,CAAC;QAC1EW,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACrO,IAAI,CAAC2N,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC3N,IAAI,CAACC,aAAa,EAAE,sBAAsB,EAAE,IAAI,CAACD,IAAI,CAACyN,IAAI,CAAC;MACxI;MAEA;MACA,IAAI,IAAI,CAACxC,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACwD,QAAQ,GAAG,IAAI,CAACzO,IAAI,CAACyN,IAAI;QACnC,IAAI,CAACxC,IAAI,CAAC6C,IAAI,GAAG,CAAC;QAClB,IAAI,CAAC7C,IAAI,CAACsH,SAAS,GAAG,CAAC;QACvB,IAAI,CAACtH,IAAI,CAACkH,KAAK,GAAG,IAAI,CAACnS,IAAI,CAACC,aAAa;QACzCmO,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACpD,IAAI,CAACkH,KAAK,CAAC;MACxD;MAEA/D,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACrO,IAAI,CAACyN,IAAI,EAAE,OAAO,EAAE,IAAI,CAACK,IAAI,CAAC;MACrE,IAAI,CAAC1D,SAAS,EAAE;IAClB;EACF;EAEA;EACOmJ,iBAAiBA,CAACb,KAAU;IACjCtE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEqE,KAAK,CAAC;IAE9C;IACA,IAAIA,KAAK,CAACjB,IAAI,IAAIiB,KAAK,CAACjB,IAAI,KAAK,IAAI,CAACzR,IAAI,CAACyN,IAAI,EAAE;MAC/CW,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACrO,IAAI,CAACyN,IAAI,EAAE,IAAI,EAAEiF,KAAK,CAACjB,IAAI,CAAC;MACvF,IAAI,CAACzR,IAAI,CAACyN,IAAI,GAAGiF,KAAK,CAACjB,IAAI;MAC3B,IAAI,CAACzR,IAAI,CAAC0N,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MAEb;MACA,IAAI,IAAI,CAAC9N,IAAI,CAACC,aAAa,GAAG,CAAC,EAAE;QAC/B,IAAI,CAACD,IAAI,CAAC2N,UAAU,GAAGyE,IAAI,CAACC,IAAI,CAAC,IAAI,CAACrS,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,IAAI,CAACyN,IAAI,CAAC;QAC1EW,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAACrO,IAAI,CAAC2N,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC3N,IAAI,CAACC,aAAa,EAAE,sBAAsB,EAAE,IAAI,CAACD,IAAI,CAACyN,IAAI,CAAC;MACvJ;MAEA;MACA,IAAI,IAAI,CAACxC,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACwD,QAAQ,GAAG,IAAI,CAACzO,IAAI,CAACyN,IAAI;QACnC,IAAI,CAACxC,IAAI,CAAC6C,IAAI,GAAG,CAAC;QAClB,IAAI,CAAC7C,IAAI,CAACsH,SAAS,GAAG,CAAC;QACvB,IAAI,CAACtH,IAAI,CAACkH,KAAK,GAAG,IAAI,CAACnS,IAAI,CAACC,aAAa;QACzCmO,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACpD,IAAI,CAACkH,KAAK,CAAC;MACvE;MAEA/D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACrO,IAAI,CAACyN,IAAI,EAAE,OAAO,EAAE,IAAI,CAACK,IAAI,CAAC;MACpF,IAAI,CAAC1D,SAAS,EAAE;IAClB;EACF;EAEA;EACOoJ,eAAeA,CAACd,KAAU;IAC/B;IACA,MAAMe,gBAAgB,GAAGf,KAAK,CAACpE,OAAO,CAACsC,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAAC1M,KAAK,CAAC;IACnE,IAAI,CAACmI,aAAa,GAAGmH,gBAAgB;EACvC;EAEOC,sBAAsBA,CAAChB,KAAU;IACtC;IACA,MAAMiB,aAAa,GAAGjB,KAAK,CAACiB,aAAa,IAAI,EAAE;IAC/C,IAAI,CAACnH,YAAY,GAAGmH,aAAa;EACnC;EAEA;EACOC,iBAAiBA,CAAClB,KAAU;IACjC,IAAI,CAAC3E,YAAY,GAAG2E,KAAK,CAAC3E,YAAY,IAAI,EAAE;IAC5C,IAAI,CAACC,aAAa,GAChB,IAAI,CAACD,YAAY,CAAC+C,MAAM,KAAK,IAAI,CAAC5F,iBAAiB,CAAC4F,MAAM;EAC9D;EAEO+C,SAASA,CAAA;IACd,IAAI,IAAI,CAAC7F,aAAa,EAAE;MACtB,IAAI,CAACD,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACD,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC7C,iBAAiB,CAAC;MAC/C,IAAI,CAAC8C,aAAa,GAAG,IAAI;IAC3B;EACF;EAEA;EACO5O,YAAYA,CAAA;IACjB;IACA,MAAM0U,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiB,CACH;IAChB,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAAC/T,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAAC8K,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAAC2D,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACAtP,aAAaA,CAACoT,KAAU;IACtB,MAAMyB,cAAc,GAAGzB,KAAK,CAAChH,KAAK,CAAC,CAAC;IAEpC,IAAI0I,QAAQ,GAAQ,EAAE;IACtB,IAAID,cAAc,KAAK,UAAU,EAAE;MACjCC,QAAQ,GAAG,IAAI,CAAClJ,iBAAiB;MAEjC;MACA;MACA,IAAI,CAACmJ,WAAW,CAACD,QAAQ,CAAC;IAC5B,CAAC,MAAM,IAAID,cAAc,KAAK,KAAK,EAAE;MACnC,MAAMG,gBAAgB,GAAG;QACvB7F,QAAQ,EAAE,IAAI,CAACzO,IAAI,CAACC,aAAa;QACjCsU,SAAS,EAAE,IAAI,CAACvU,IAAI,CAAC6N,QAAQ;QAC7B2G,SAAS,EAAE,IAAI,CAACxU,IAAI,CAAC4N,OAAO;QAC5BF,UAAU,EAAE,IAAI,CAAC1N,IAAI,CAAC0N;QACtB;OACD;MAED;MACA,IAAI,CAACjD,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC;MAC9C;MACA,IAAI,CAACnB,cAAc,CAChBiK,aAAa,CAACH,gBAAgB;MAC/B;MAAA,CACCtF,SAAS,CAAE6C,IAAI,IAAI;QAClB;QACA,IAAI,CAACpH,eAAe,CAACwE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIkG,IAAI,CAACvB,OAAO,EAAE;UAChB,IAAI,CAAClF,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACP,GAAG,CAAC2H,YAAY,EAAE;UACvB,OAAO,CAAC;QACV;QAEA,IAAI,CAACpH,cAAc,GAAG,IAAI;QAC1BgJ,QAAQ,GAAGvC,IAAI,CAACE,YAAY,CAACF,IAAI,IAAI,EAAE;QAEvC,IAAI,CAAChH,GAAG,CAAC6D,aAAa,EAAE,CAAC,CAAC;QAC1B,IAAI,CAAC2F,WAAW,CAACD,QAAQ,CAAC;MAC5B,CAAC,CAAC;IACN;EACF;EAEAC,WAAWA,CAACK,WAAgB;IAC1B;IACA,IAAIN,QAAQ,GAAQM,WAAW;IAC/B,IAAIC,WAAW,GAAS,IAAI,CAAC7J,UAAU,CAAC8J,eAAe,CAAC,IAAIC,IAAI,EAAE,CAAC;IAEnEzG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+F,QAAQ,CAAC;IAEjC;IACA,IAAIA,QAAQ,KAAKU,SAAS,IAAIV,QAAQ,CAACtD,MAAM,GAAG,CAAC,EAAE;MACjD;MACA,MAAMiE,UAAU,GAAG,QAAQ;MAE3B;MACA;MACA;MACA;MAEA;MAEA,MAAMC,WAAW,GAAG,CAClB,eAAe,EACf,aAAa,EACb,UAAU,EACV,QAAQ,EACR,UAAU,EACV,cAAc,EACd,cAAc,EAEd,iBAAiB,EACjB,kBAAkB,CACnB;MACD;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA,MAAMC,iBAAiB,GAAQ,EAAE;MAEjC;MACA,MAAMC,UAAU,GAAQ,EAAE;MAE1B;MACAzX,IAAI,CAAC2W,QAAQ,EAAGe,OAAY,IAAI;QAC9B;QACA,MAAMC,QAAQ,GAAGlC,KAAK,CAAC8B,WAAW,CAAClE,MAAM,CAAC,CAACuE,IAAI,CAAC,IAAI,CAAC;QACrDD,QAAQ,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACG,gBAAgB;QACtCF,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAACtK,UAAU,CAAC8J,eAAe,CAACO,OAAO,CAACI,UAAU,CAAC;QACjE;QACAP,WAAW,CAACzG,OAAO,CAAC,CAACsC,GAAQ,EAAE2E,CAAS,KAAI;UAC1C,MAAMC,aAAa,GAAGD,CAAC,CAAC,CAAC;UACzB,QAAQ3E,GAAG;YACT,KAAK,eAAe;cAClBuE,QAAQ,CAACK,aAAa,CAAC,GAAGN,OAAO,CAACnT,YAAY;cAC9C;YACF,KAAK,aAAa;cAChBoT,QAAQ,CAACK,aAAa,CAAC,GAAGN,OAAO,CAACjS,UAAU;cAC5C;YACF,KAAK,UAAU;cACbkS,QAAQ,CAACK,aAAa,CAAC,GAAGN,OAAO,CAAC1R,cAAc;cAChD;YACF,KAAK,QAAQ;cACX2R,QAAQ,CAACK,aAAa,CAAC,GAAGN,OAAO,CAAC5P,YAAY;cAC9C;YACF,KAAK,UAAU;cACb6P,QAAQ,CAACK,aAAa,CAAC,GAAGN,OAAO,CAACjP,QAAQ;cAC1C;YACF,KAAK,cAAc;cACjBkP,QAAQ,CAACK,aAAa,CAAC,GAAGN,OAAO,CAACvS,WAAW;cAC7C;YACF,KAAK,cAAc;cACjBwS,QAAQ,CAACK,aAAa,CAAC,GAAG,IAAI,CAAC3K,UAAU,CAACvE,UAAU,CAAC4O,OAAO,CAAC1O,iBAAiB,CAAC;cAC/E;YACF,KAAK,iBAAiB;cACpB2O,QAAQ,CAACK,aAAa,CAAC,GAAG,IAAI,CAAC3K,UAAU,CAACvE,UAAU,CAAC4O,OAAO,CAACnO,oBAAoB,CAAC;cAClF;YACF,KAAK,kBAAkB;cACrBoO,QAAQ,CAACK,aAAa,CAAC,GAAGN,OAAO,CAAC9M,eAAe;cACjD;YACF;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACF;QACF,CAAC,CAAC;QAEF6M,UAAU,CAACnC,IAAI,CAACqC,QAAQ,CAAC;MAC3B,CAAC,CAAC;MAEF;MACA,MAAMM,OAAO,GAAGV,WAAW,CAACpE,GAAG,CAAC,CAAC+E,MAAM,EAAEC,KAAK,MAAM;QAClDC,EAAE,EAAED,KAAK,GAAG,CAAC;QACb3I,KAAK,EAAE;OACR,CAAC,CAAC;MAEH;MACA,IAAI,CAAClC,cAAc,CAAC+K,aAAa,CAC/Bf,UAAU,EACVC,WAAW,EACXE,UAAU,EACVQ;MACA;MACA;OACD;IACH,CAAC,MAAM;MACL,MAAMK,OAAO,GAAG,2CAA2C;MAC3D;IACF;EACF;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACOC,QAAQA,CAAA;IACb,MAAMC,QAAQ,GAAG;MACf7J,SAAS,EAAE,IAAI,CAACI,YAAY;MAC5BF,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,iBAAiB,EAAE,IAAI,CAACA;KACzB;IAED;IACA,IAAI,CAAC5B,kBAAkB,CAACuL,kBAAkB,CAAC;MACzChG,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,IAAI,CAAC5E,SAAS,CAACuE,MAAM;MAC7BzD,UAAU,EAAE4J,QAAQ,CAAC7J,SAAS;MAC9BE,aAAa,EAAE2J,QAAQ,CAAC3J,aAAa;MACrC6J,QAAQ,EAAE,IAAI,CAAC5K,SAAS,CAACuE;KAC1B,CAAC;IAEF1B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4H,QAAQ,CAAC;IACnC,IAAI,CAACvL,wBAAwB,CAAC0L,WAAW,CAAC,+BAA+B,EAAE,EAAE,CAAC;IAElG;EACF;EAEQC,0BAA0BA,CAACJ,QAAa;IAC9C,MAAMhG,MAAM,GAAG;MACbC,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,IAAI,CAAC5E,SAAS,CAACuE,MAAM;MAC7BzD,UAAU,EAAE4J,QAAQ,CAAC7J,SAAS;MAC9BE,aAAa,EAAE2J,QAAQ,CAAC3J,aAAa;MACrC6J,QAAQ,EAAE,IAAI,CAAC5K,SAAS,CAACuE;KAC1B;IAED,IAAI,CAACnF,kBAAkB,CAAC2L,gBAAgB,CAACrG,MAAM,CAAC,CAACjB,SAAS,CAAC;MACzDrD,IAAI,EAAG0E,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,KAAK,KAAK,EAAE;UAC9BlC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEgC,QAAQ,CAAC;UAC9C,IAAI,CAAC3F,wBAAwB,CAAC0L,WAAW,CAAC,oCAAoC,EAAE,EAAE,CAAC;UAEjG;QACF,CAAC,MAAM;UACLhI,OAAO,CAACuC,KAAK,CAAC,iCAAiC,EAAEN,QAAQ,CAAC0F,OAAO,CAAC;UACpD,IAAI,CAACrL,wBAAwB,CAAC6L,SAAS,CAAClG,QAAQ,CAAC0F,OAAO,EAAE,EAAE,CAAC;UAE3E;QACF;MACF,CAAC;MACDpF,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrC,IAAI,CAACjG,wBAAwB,CAAC6L,SAAS,CAAC,6BAA6B,EAAE,EAAE,CAAC;QAE1F;MACF;KACD,CAAC;EACJ;EAEQC,iBAAiBA,CAAA;IACvB;IACA,MAAMC,YAAY,GAAG;MACnBvG,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,IAAI,CAAC5E,SAAS,CAACuE;KACxB;IAED,IAAI,CAACnF,kBAAkB,CAAC+L,gBAAgB,CAACD,YAAY,CAAC,CAACzH,SAAS,CAAC;MAC/DrD,IAAI,EAAG0E,QAAQ,IAAI;QACjBjC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgC,QAAQ,CAAC;QACnD;QACA,IAAI,CAACgG,0BAA0B,CAAC;UAC9BjK,SAAS,EAAE,EAAE;UACbE,aAAa,EAAE,IAAI,CAACI,cAAc;UAClCH,iBAAiB,EAAE,IAAI,CAACG;SACzB,CAAC;MACJ,CAAC;MACDiE,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD;QACA,IAAI,CAAC0F,0BAA0B,CAAC;UAC9BjK,SAAS,EAAE,EAAE;UACbE,aAAa,EAAE,IAAI,CAACI,cAAc;UAClCH,iBAAiB,EAAE,IAAI,CAACG;SACzB,CAAC;MACJ;KACD,CAAC;EACJ;EAEOlN,UAAUA,CAAA;IACf4O,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IAEnD;IACA,IAAI,CAACjC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAE3B;IACA,IAAI,CAAC5B,kBAAkB,CAACgM,qBAAqB,CAAC,SAAS,CAAC;IAExD;IACA,IAAI,CAACxI,sBAAsB,EAAE;IAE7B;IACA,IAAI,CAACtD,GAAG,CAAC6D,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACzD,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC2D,OAAO,EAAE;IACrB;IAEA;IACAR,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C;EACF;EAEA;EACOnP,GAAGA,CAAA;IACR;IACA,IAAI,CAAC0C,IAAI,CAAC,CAAC,CAAC;EACd;EAEOgV,IAAIA,CAAC/U,QAAgB;IAC1B,IAAI,CAACyI,MAAM,CAACuM,QAAQ,CAAC,CAAC,eAAe,EAAEhV,QAAQ,CAAC,EAAE;MAChDiV,WAAW,EAAE;QAAEC,IAAI,EAAE;MAAa;KACnC,CAAC;EACJ;EAEOnV,IAAIA,CAACC,QAAgB;IAC1B,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjB,MAAMmV,MAAM,GAAG,IAAI,CAAC9L,iBAAiB,CAAC+L,IAAI,CACvCC,CAAC,IAAKA,CAAC,CAACrV,QAAQ,KAAKA,QAAQ,CAC/B;MACD,MAAMsV,eAAe,GAKjB;QACF1J,IAAI,EAAE,IAAI;QAAE;QACZ2J,QAAQ,EAAE,QAAQ;QAAE;QACpBC,QAAQ,EAAE,KAAK;QAAE;QACjBC,UAAU,EAAE,IAAI,CAAE;OACnB;MAED;MACA,MAAMC,QAAQ,GAAG,IAAI,CAAC3M,YAAY,CAAC4M,IAAI,CACrC3Z,oBAAoB,EACpBsZ,eAAe,CAChB;MACD;MACAI,QAAQ,CAACE,iBAAiB,CAAC5B,EAAE,GAAGhU,QAAQ;MACxC0V,QAAQ,CAACE,iBAAiB,CAACT,MAAM,GAAGA,MAAM;MAC1C;MACAO,QAAQ,CAACE,iBAAiB,CAACC,SAAS,CAAC1I,SAAS,CAC3C2I,aAAsB,IAAI;QACzB,IAAIA,aAAa,KAAK,IAAI,EAAE;UAC1B;UACA,IAAI,CAACvN,SAAS,EAAE;QAClB;MACF,CAAC,CACF;IACH,CAAC,MAAM;MACL,IAAI,CAACE,MAAM,CAACuM,QAAQ,CAAC,CAAC,eAAe,EAAEhV,QAAQ,CAAC,EAAE;QAChDiV,WAAW,EAAE;UAAEC,IAAI,EAAE;QAAa;OACnC,CAAC;IACJ;EACF;EACAa,SAASA,CAACC,OAAY;IACpB,IAAI,CAACjN,YAAY,CAAC4M,IAAI,CAACK,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACrD;EAEAC,aAAaA,CAAA;IACX3J,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF;EAEO2J,MAAMA,CAACnW,QAAgB;IAC5B,IAAIoW,OAAO,CAAC,8CAA8C,CAAC,EAAE;MAC3D,IAAI,CAACzN,cAAc,CAAC0N,YAAY,CAAC;QAAErW;MAAQ,CAAE,CAAC,CAACmN,SAAS,CAAC;QACvDrD,IAAI,EAAG0E,QAAa,IAAI;UACtB,IAAIA,QAAQ,CAAC0F,OAAO,EAAE;YACpB;YACY,IAAI,CAACrL,wBAAwB,CAAC0L,WAAW,CAAC,6BAA6B,EAAE,EAAE,CAAC;YAExF,IAAI,CAAChM,SAAS,EAAE;UAClB;QACF,CAAC;QACDuG,KAAK,EAAGA,KAAU,IAAI;UACpBvC,OAAO,CAACuC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACvB,IAAI,CAACjG,wBAAwB,CAAC6L,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;UAElF;QACF;OACD,CAAC;IACJ;EACF;EAEA;EACOhQ,UAAUA,CAAC4R,UAAkB;IAClC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAIvD,IAAI,CAACsD,UAAU,CAAC;IACjC,MAAME,KAAK,GAAG,CAACD,IAAI,CAACE,QAAQ,EAAE,GAAG,CAAC,EAAEC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/D,MAAMC,GAAG,GAAGL,IAAI,CAACM,OAAO,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,MAAMG,IAAI,GAAGP,IAAI,CAACQ,WAAW,EAAE;IAC/B,OAAO,GAAGP,KAAK,IAAII,GAAG,IAAIE,IAAI,EAAE;EAClC;EAEOlU,cAAcA,CAACjE,MAAc;IAClC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,qBAAqB;MAC9B,KAAK,mBAAmB;QACtB,OAAO,qBAAqB;MAC9B,KAAK,SAAS;QACZ,OAAO,oBAAoB;MAC7B,KAAK,SAAS;QACZ,OAAO,kBAAkB;MAC3B;QACE,OAAO,uBAAuB;IAClC;EACF;EAEO+C,gBAAgBA,CAAC7C,QAAgB;IACtC,OAAOA,QAAQ,KAAK,SAAS,GACzB,qBAAqB,GACrB,uBAAuB;EAC7B;EAEAmY,WAAWA,CAACrD,CAAM;IAChB,IAAI,CAAClK,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC4C,YAAY,GAAGsH,CAAC,IAAI,KAAK;IAC9B,IAAI,CAAChL,cAAc,CAACqO,WAAW,CAAC;MAAEC,cAAc,EAAE,CAAC;MAAE5K,YAAY,EAAE,IAAI,CAACA,YAAY;MAAE6K,SAAS,EAAE;IAAI,CAAE,CAAC,CAAC/J,SAAS,CAAC;MACjHrD,IAAI,EAAGqN,GAAQ,IAAI;QACjB,IAAI,CAAC1N,SAAS,GAAG,KAAK;QACtB8C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE2K,GAAG,CAAC;QAElC;QACA,MAAMjH,YAAY,GAAGiH,GAAG,EAAEjH,YAAY,IAAIiH,GAAG;QAE7C,IAAIjH,YAAY,EAAEzB,OAAO,EAAE;UACzB;UACc,IAAI,CAAC5F,wBAAwB,CAAC6L,SAAS,CAACxE,YAAY,CAACkH,YAAY,IAAG,uBAAuB,EAAE,EAAE,CAAC;QAEhH,CAAC,MAAM,IAAIlH,YAAY,EAAEmH,OAAO,KAAK,KAAK,EAAE;UAC1C;UACA,IAAInH,YAAY,CAACgE,OAAO,KAAK,oCAAoC,EAAE;YACjE;YACY,IAAI,CAACrL,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAEjJ,CAAC,MAAM,IAAIxE,YAAY,CAACgE,OAAO,KAAK,mCAAmC,EAAE;YACvE;YAEY,IAAI,CAACrL,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAChJ,CAAC,MAAM;YACkB,IAAI,CAAC7L,wBAAwB,CAAC6L,SAAS,CAACxE,YAAY,CAACgE,OAAO,IAAG,uBAAuB,EAAE,EAAE,CAAC;YAEnH;UACF;QACF,CAAC,MAAM;UACS,IAAI,CAACrL,wBAAwB,CAAC0L,WAAW,CAAC,8BAA8B,EAAE,EAAE,CAAC;UAE3F;QACF;QACA,IAAI,CAACvL,GAAG,CAAC2H,YAAY,EAAE;MACzB,CAAC;MACD7B,KAAK,EAAGwI,GAAQ,IAAI;QAClB,IAAI,CAAC7N,SAAS,GAAG,KAAK;QACtB;QACA8C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8K,GAAG,CAAC;QACnC/K,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,OAAO8K,GAAG,CAAC;QACtC/K,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE+K,MAAM,CAACC,IAAI,CAACF,GAAG,IAAI,EAAE,CAAC,CAAC;QAClD/K,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8K,GAAG,EAAE3Y,MAAM,CAAC;QACzC4N,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8K,GAAG,EAAEpD,OAAO,CAAC;QAC3C3H,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE8K,GAAG,EAAExI,KAAK,CAAC;QAEvC;QACA;QACA,IAAIwI,GAAG,EAAED,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAIC,GAAG,CAACpD,OAAO,KAAK,oCAAoC,EAAE;YAChC,IAAI,CAACrL,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAI4C,GAAG,CAACpD,OAAO,KAAK,mCAAmC,EAAE;YACtC,IAAI,CAACrL,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YAAyB,IAAI,CAAC7L,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAGjK;UACF;QACF,CAAC,MAAM,IAAI4C,GAAG,EAAExI,KAAK,EAAEoF,OAAO,EAAE;UAC9B,IAAIoD,GAAG,CAACxI,KAAK,CAACoF,OAAO,KAAK,oCAAoC,EAAE;YACtC,IAAI,CAACrL,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAI4C,GAAG,CAACxI,KAAK,CAACoF,OAAO,KAAK,mCAAmC,EAAE;YAC5C,IAAI,CAACrL,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAAC7L,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;YACA;UACF;QACF,CAAC,MAAM,IAAI4C,GAAG,EAAE3Y,MAAM,KAAK,GAAG,EAAE;UACN,IAAI,CAACkK,wBAAwB,CAAC6L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAE3J;UACA;QACF,CAAC,MAAM;UACmB,IAAI,CAAC7L,wBAAwB,CAAC6L,SAAS,CAAC,wBAAwB,EAAE,EAAE,CAAC;UAE7F;QACF;QACAnI,OAAO,CAACuC,KAAK,CAACwI,GAAG,CAAC;QAClB,IAAI,CAACtO,GAAG,CAAC2H,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEA8G,cAAcA,CAAA;IACZ;IACA;IACAlL,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;;qCA78CWhE,mBAAmB,EAAAvM,EAAA,CAAAyb,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA3b,EAAA,CAAAyb,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA5b,EAAA,CAAAyb,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA9b,EAAA,CAAAyb,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAAhc,EAAA,CAAAyb,iBAAA,CAAAQ,EAAA,CAAAC,wBAAA,GAAAlc,EAAA,CAAAyb,iBAAA,CAAAU,EAAA,CAAAC,kBAAA,GAAApc,EAAA,CAAAyb,iBAAA,CAAAY,EAAA,CAAAC,QAAA,GAAAtc,EAAA,CAAAyb,iBAAA,CAAAzb,EAAA,CAAAuc,iBAAA,GAAAvc,EAAA,CAAAyb,iBAAA,CAAAe,EAAA,CAAAC,UAAA,GAAAzc,EAAA,CAAAyb,iBAAA,CAAAiB,EAAA,CAAAzP,cAAA,GAAAjN,EAAA,CAAAyb,iBAAA,CAAAkB,EAAA,CAAAC,eAAA;EAAA;;UAAnBrQ,mBAAmB;IAAAsQ,SAAA;IAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QCvChChd,EAAA,CAAAqD,UAAA,IAAA6Z,kCAAA,iBAAqE;QAUnEld,EADF,CAAAC,cAAA,aAA4B,uBAiCzB;QAFCD,EAdA,CAAAc,UAAA,2BAAAqc,iEAAA7c,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6c,GAAA;UAAA,OAAApd,EAAA,CAAAa,WAAA,CAAiBoc,GAAA,CAAAvH,eAAA,CAAApV,MAAA,CAAuB;QAAA,EAAC,6BAAA+c,mEAAA/c,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6c,GAAA;UAAA,OAAApd,EAAA,CAAAa,WAAA,CACtBoc,GAAA,CAAAnH,iBAAA,CAAAxV,MAAA,CAAyB;QAAA,EAAC,0BAAAgd,gEAAAhd,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6c,GAAA;UAAA,OAAApd,EAAA,CAAAa,WAAA,CAQ7Boc,GAAA,CAAAlI,YAAA,CAAAzU,MAAA,CAAoB;QAAA,EAAC,wBAAAid,8DAAAjd,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6c,GAAA;UAAA,OAAApd,EAAA,CAAAa,WAAA,CACvBoc,GAAA,CAAA3H,UAAA,CAAAhV,MAAA,CAAkB;QAAA,EAAC,4BAAAkd,kEAAAld,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6c,GAAA;UAAA,OAAApd,EAAA,CAAAa,WAAA,CACfoc,GAAA,CAAAzH,gBAAA,CAAAlV,MAAA,CAAwB;QAAA,EAAC,6BAAAmd,mEAAAnd,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6c,GAAA;UAAA,OAAApd,EAAA,CAAAa,WAAA,CACxBoc,GAAA,CAAAxH,iBAAA,CAAAnV,MAAA,CAAyB;QAAA,EAAC,wBAAAod,8DAAApd,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6c,GAAA;UAAA,OAAApd,EAAA,CAAAa,WAAA,CAC/Boc,GAAA,CAAA/H,YAAA,CAAA5U,MAAA,CAAoB;QAAA,EAAC,oCAAAqd,0EAAArd,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6c,GAAA;UAAA,OAAApd,EAAA,CAAAa,WAAA,CACToc,GAAA,CAAArH,sBAAA,CAAAtV,MAAA,CAA8B;QAAA,EAAC;QAiKzDN,EA9JA,CAAAqD,UAAA,IAAAua,0CAAA,2BAAsC,IAAAC,0CAAA,yBAwFA,IAAAC,0CAAA,yBA0DA,IAAAC,2CAAA,4BAYW;QAuarD/d,EADE,CAAAG,YAAA,EAAa,EACT;QAGNH,EAAA,CAAAqD,UAAA,IAAA2a,kCAAA,iBAAsD;;;QAnnBhDhe,EAAA,CAAAgC,UAAA,SAAAib,GAAA,CAAA1P,OAAA,IAAA0P,GAAA,CAAAzP,SAAA,CAA0B;QAY5BxN,EAAA,CAAA6B,SAAA,GAAiB;QA6BjB7B,EA7BA,CAAAgC,UAAA,SAAAib,GAAA,CAAA5P,QAAA,CAAiB,aAAA4P,GAAA,CAAA/a,IAAA,CAAAyN,IAAA,CACK,SAAAsN,GAAA,CAAAxN,IAAA,CACT,aAAAzP,EAAA,CAAAie,eAAA,KAAAC,GAAA,EAAAle,EAAA,CAAAuG,eAAA,KAAA4X,GAAA,GAOX,UAAAlB,GAAA,CAAA/a,IAAA,CAAAC,aAAA,CAC0B,aAAAnC,EAAA,CAAAuG,eAAA,KAAA6X,GAAA,EACsB,oBAC/B,eAAApe,EAAA,CAAAuG,eAAA,KAAA8X,GAAA,EACoC,qBAGnC,oBAED,eACL,SAAApB,GAAA,CAAAjN,IAAA,CACD,WAAAiN,GAAA,CAAAhP,MAAA,CACI,eAAAjO,EAAA,CAAAuG,eAAA,KAAA+X,GAAA,EACc,kBAOd;QAgKgBte,EAAA,CAAA6B,SAAA,GAAc;QAAd7B,EAAA,CAAAgC,UAAA,YAAAib,GAAA,CAAAtO,WAAA,CAAc;QA0a7C3O,EAAA,CAAA6B,SAAA,EAAqB;QAArB7B,EAAA,CAAAgC,UAAA,UAAAib,GAAA,CAAA3P,cAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}