{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./core/layout-init.service\";\nimport * as i2 from \"./core/layout.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../modules/services/http-utils.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../partials/layout/extras/scroll-top/scroll-top.component\";\nimport * as i7 from \"../partials/layout/modals/invite-users-modal/invite-users-modal.component\";\nimport * as i8 from \"../partials/layout/modals/main-modal/main-modal.component\";\nimport * as i9 from \"../partials/layout/modals/upgrade-plan-modal/upgrade-plan-modal.component\";\nimport * as i10 from \"../partials/layout/drawers/activity-drawer/activity-drawer.component\";\nimport * as i11 from \"../partials/layout/drawers/messenger-drawer/messenger-drawer.component\";\nimport * as i12 from \"./components/header/header.component\";\nimport * as i13 from \"./components/content/content.component\";\nimport * as i14 from \"./components/footer/footer.component\";\nimport * as i15 from \"./components/scripts-init/scripts-init.component\";\nimport * as i16 from \"./components/toolbar/toolbar.component\";\nimport * as i17 from \"./components/sidebar/sidebar.component\";\nconst _c0 = [\"ktSidebar\"];\nconst _c1 = [\"ktAside\"];\nconst _c2 = [\"ktHeaderMobile\"];\nconst _c3 = [\"ktHeader\"];\nfunction LayoutComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\", 12)(3, \"span\", 13);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 14);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LayoutComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-header\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appHeaderDefaultClass);\n  }\n}\nfunction LayoutComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-sidebar\", 16, 0);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appSidebarDefaultClass);\n  }\n}\nfunction LayoutComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction LayoutComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-toolbar\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appToolbarCSSClass)(\"appToolbarLayout\", ctx_r0.appToolbarLayout);\n  }\n}\nfunction LayoutComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-footer\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appFooterCSSClass)(\"appFooterContainerCSSClass\", ctx_r0.appFooterContainerCSSClass);\n  }\n}\nexport let LayoutComponent = /*#__PURE__*/(() => {\n  class LayoutComponent {\n    initService;\n    layout;\n    router;\n    activatedRoute;\n    httpUtilService;\n    unsubscribe = [];\n    // Loading state\n    isLoading = false;\n    // Public variables\n    // page\n    pageContainerCSSClasses;\n    // header\n    appHeaderDefaultClass = '';\n    appHeaderDisplay;\n    appHeaderDefaultStickyEnabled;\n    appHeaderDefaultStickyAttributes = {};\n    appHeaderDefaultMinimizeEnabled;\n    appHeaderDefaultMinimizeAttributes = {};\n    // toolbar\n    appToolbarDisplay;\n    appToolbarLayout;\n    appToolbarCSSClass = '';\n    appToolbarSwapEnabled;\n    appToolbarSwapAttributes = {};\n    appToolbarStickyEnabled;\n    appToolbarStickyAttributes = {};\n    appToolbarMinimizeEnabled;\n    appToolbarMinimizeAttributes = {};\n    // content\n    appContentContiner;\n    appContentContainerClass;\n    contentCSSClasses;\n    contentContainerCSSClass;\n    // sidebar\n    appSidebarDefaultClass;\n    appSidebarDefaultDrawerEnabled;\n    appSidebarDefaultDrawerAttributes = {};\n    appSidebarDisplay;\n    appSidebarDefaultStickyEnabled;\n    appSidebarDefaultStickyAttributes = {};\n    ktSidebar;\n    /// sidebar panel\n    appSidebarPanelDisplay;\n    // footer\n    appFooterDisplay;\n    appFooterCSSClass = '';\n    appFooterContainer = '';\n    appFooterContainerCSSClass = '';\n    appFooterFixedDesktop;\n    appFooterFixedMobile;\n    // scrolltop\n    scrolltopDisplay;\n    ktAside;\n    ktHeaderMobile;\n    ktHeader;\n    constructor(initService, layout, router, activatedRoute, httpUtilService) {\n      this.initService = initService;\n      this.layout = layout;\n      this.router = router;\n      this.activatedRoute = activatedRoute;\n      this.httpUtilService = httpUtilService;\n      // define layout type and load layout\n      this.router.events.subscribe(event => {\n        if (event instanceof NavigationEnd) {\n          const currentLayoutType = this.layout.currentLayoutTypeSubject.value;\n          const nextLayoutType = this.activatedRoute?.firstChild?.snapshot.data.layout || this.layout.getBaseLayoutTypeFromLocalStorage();\n          if (currentLayoutType !== nextLayoutType || !currentLayoutType) {\n            this.layout.currentLayoutTypeSubject.next(nextLayoutType);\n            this.initService.reInitProps(nextLayoutType);\n          }\n        }\n      });\n    }\n    ngOnInit() {\n      const subscr = this.layout.layoutConfigSubject.asObservable().subscribe(config => {\n        this.updateProps(config);\n      });\n      this.unsubscribe.push(subscr);\n      // Subscribe to loading state changes\n      const loadingSubscr = this.httpUtilService.loadingSubject.subscribe(loading => {\n        this.isLoading = loading === true;\n      });\n      this.unsubscribe.push(loadingSubscr);\n    }\n    updateProps(config) {\n      this.scrolltopDisplay = this.layout.getProp('scrolltop.display', config);\n      this.pageContainerCSSClasses = this.layout.getStringCSSClasses('pageContainer');\n      this.appHeaderDefaultClass = this.layout.getProp('app.header.default.class', config);\n      this.appHeaderDisplay = this.layout.getProp('app.header.display', config);\n      this.appFooterDisplay = this.layout.getProp('app.footer.display', config);\n      this.appSidebarDisplay = this.layout.getProp('app.sidebar.display', config);\n      this.appSidebarPanelDisplay = this.layout.getProp('app.sidebar-panel.display', config);\n      this.appToolbarDisplay = this.layout.getProp('app.toolbar.display', config);\n      this.contentCSSClasses = this.layout.getStringCSSClasses('content');\n      this.contentContainerCSSClass = this.layout.getStringCSSClasses('contentContainer');\n      this.appContentContiner = this.layout.getProp('app.content.container', config);\n      this.appContentContainerClass = this.layout.getProp('app.content.containerClass', config);\n      // footer\n      if (this.appFooterDisplay) {\n        this.updateFooter(config);\n      }\n      // sidebar\n      if (this.appSidebarDisplay) {\n        this.updateSidebar(config);\n      }\n      // header\n      if (this.appHeaderDisplay) {\n        this.updateHeader(config);\n      }\n      // toolbar\n      if (this.appToolbarDisplay) {\n        this.updateToolbar(config);\n      }\n    }\n    updateSidebar(config) {\n      this.appSidebarDefaultClass = this.layout.getProp('app.sidebar.default.class', config);\n      this.appSidebarDefaultDrawerEnabled = this.layout.getProp('app.sidebar.default.drawer.enabled', config);\n      if (this.appSidebarDefaultDrawerEnabled) {\n        this.appSidebarDefaultDrawerAttributes = this.layout.getProp('app.sidebar.default.drawer.attributes', config);\n      }\n      this.appSidebarDefaultStickyEnabled = this.layout.getProp('app.sidebar.default.sticky.enabled', config);\n      if (this.appSidebarDefaultStickyEnabled) {\n        this.appSidebarDefaultStickyAttributes = this.layout.getProp('app.sidebar.default.sticky.attributes', config);\n      }\n      setTimeout(() => {\n        const sidebarElement = document.getElementById('kt_app_sidebar');\n        // sidebar\n        if (this.appSidebarDisplay && sidebarElement) {\n          const sidebarAttributes = sidebarElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n          sidebarAttributes.forEach(attr => sidebarElement.removeAttribute(attr));\n          if (this.appSidebarDefaultDrawerEnabled) {\n            for (const key in this.appSidebarDefaultDrawerAttributes) {\n              if (this.appSidebarDefaultDrawerAttributes.hasOwnProperty(key)) {\n                sidebarElement.setAttribute(key, this.appSidebarDefaultDrawerAttributes[key]);\n              }\n            }\n          }\n          if (this.appSidebarDefaultStickyEnabled) {\n            for (const key in this.appSidebarDefaultStickyAttributes) {\n              if (this.appSidebarDefaultStickyAttributes.hasOwnProperty(key)) {\n                sidebarElement.setAttribute(key, this.appSidebarDefaultStickyAttributes[key]);\n              }\n            }\n          }\n        }\n      }, 0);\n    }\n    updateHeader(config) {\n      this.appHeaderDefaultStickyEnabled = this.layout.getProp('app.header.default.sticky.enabled', config);\n      if (this.appHeaderDefaultStickyEnabled) {\n        this.appHeaderDefaultStickyAttributes = this.layout.getProp('app.header.default.sticky.attributes', config);\n      }\n      this.appHeaderDefaultMinimizeEnabled = this.layout.getProp('app.header.default.minimize.enabled', config);\n      if (this.appHeaderDefaultMinimizeEnabled) {\n        this.appHeaderDefaultMinimizeAttributes = this.layout.getProp('app.header.default.minimize.attributes', config);\n      }\n      setTimeout(() => {\n        const headerElement = document.getElementById('kt_app_header');\n        // header\n        if (this.appHeaderDisplay && headerElement) {\n          const headerAttributes = headerElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n          headerAttributes.forEach(attr => headerElement.removeAttribute(attr));\n          if (this.appHeaderDefaultStickyEnabled) {\n            for (const key in this.appHeaderDefaultStickyAttributes) {\n              if (this.appHeaderDefaultStickyAttributes.hasOwnProperty(key)) {\n                headerElement.setAttribute(key, this.appHeaderDefaultStickyAttributes[key]);\n              }\n            }\n          }\n          if (this.appHeaderDefaultMinimizeEnabled) {\n            for (const key in this.appHeaderDefaultMinimizeAttributes) {\n              if (this.appHeaderDefaultMinimizeAttributes.hasOwnProperty(key)) {\n                headerElement.setAttribute(key, this.appHeaderDefaultMinimizeAttributes[key]);\n              }\n            }\n          }\n        }\n      }, 0);\n    }\n    updateFooter(config) {\n      this.appFooterCSSClass = this.layout.getProp('app.footer.class', config);\n      this.appFooterContainer = this.layout.getProp('app.footer.container', config);\n      this.appFooterContainerCSSClass = this.layout.getProp('app.footer.containerClass', config);\n      if (this.appFooterContainer === 'fixed') {\n        this.appFooterContainerCSSClass += ' container-xxl';\n      } else {\n        if (this.appFooterContainer === 'fluid') {\n          this.appFooterContainerCSSClass += ' container-fluid';\n        }\n      }\n      this.appFooterFixedDesktop = this.layout.getProp('app.footer.fixed.desktop', config);\n      if (this.appFooterFixedDesktop) {\n        document.body.setAttribute('data-kt-app-footer-fixed', 'true');\n      }\n      this.appFooterFixedMobile = this.layout.getProp('app.footer.fixed.mobile');\n      if (this.appFooterFixedMobile) {\n        document.body.setAttribute('data-kt-app-footer-fixed-mobile', 'true');\n      }\n    }\n    updateToolbar(config) {\n      this.appToolbarLayout = this.layout.getProp('app.toolbar.layout', config);\n      this.appToolbarSwapEnabled = this.layout.getProp('app.toolbar.swap.enabled', config);\n      if (this.appToolbarSwapEnabled) {\n        this.appToolbarSwapAttributes = this.layout.getProp('app.toolbar.swap.attributes', config);\n      }\n      this.appToolbarStickyEnabled = this.layout.getProp('app.toolbar.sticky.enabled', config);\n      if (this.appToolbarStickyEnabled) {\n        this.appToolbarStickyAttributes = this.layout.getProp('app.toolbar.sticky.attributes', config);\n      }\n      this.appToolbarCSSClass = this.layout.getProp('app.toolbar.class', config) || '';\n      this.appToolbarMinimizeEnabled = this.layout.getProp('app.toolbar.minimize.enabled', config);\n      if (this.appToolbarMinimizeEnabled) {\n        this.appToolbarMinimizeAttributes = this.layout.getProp('app.toolbar.minimize.attributes', config);\n        this.appToolbarCSSClass += ' app-toolbar-minimize';\n      }\n      setTimeout(() => {\n        const toolbarElement = document.getElementById('kt_app_toolbar');\n        // toolbar\n        if (this.appToolbarDisplay && toolbarElement) {\n          const toolbarAttributes = toolbarElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n          toolbarAttributes.forEach(attr => toolbarElement.removeAttribute(attr));\n          if (this.appToolbarSwapEnabled) {\n            for (const key in this.appToolbarSwapAttributes) {\n              if (this.appToolbarSwapAttributes.hasOwnProperty(key)) {\n                toolbarElement.setAttribute(key, this.appToolbarSwapAttributes[key]);\n              }\n            }\n          }\n          if (this.appToolbarStickyEnabled) {\n            for (const key in this.appToolbarStickyAttributes) {\n              if (this.appToolbarStickyAttributes.hasOwnProperty(key)) {\n                toolbarElement.setAttribute(key, this.appToolbarStickyAttributes[key]);\n              }\n            }\n          }\n          if (this.appToolbarMinimizeEnabled) {\n            for (const key in this.appToolbarMinimizeAttributes) {\n              if (this.appToolbarMinimizeAttributes.hasOwnProperty(key)) {\n                toolbarElement.setAttribute(key, this.appToolbarMinimizeAttributes[key]);\n              }\n            }\n          }\n        }\n      }, 0);\n    }\n    ngOnDestroy() {\n      this.unsubscribe.forEach(sb => sb.unsubscribe());\n    }\n    static ɵfac = function LayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LayoutComponent)(i0.ɵɵdirectiveInject(i1.LayoutInitService), i0.ɵɵdirectiveInject(i2.LayoutService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.HttpUtilsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LayoutComponent,\n      selectors: [[\"app-layout\"]],\n      viewQuery: function LayoutComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n          i0.ɵɵviewQuery(_c3, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktSidebar = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktAside = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktHeaderMobile = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktHeader = _t.first);\n        }\n      },\n      decls: 20,\n      vars: 10,\n      consts: [[\"ktSidebar\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [\"id\", \"kt_app_root\", 1, \"d-flex\", \"flex-column\", \"flex-root\", \"app-root\"], [\"id\", \"kt_app_page\", 1, \"app-page\", \"flex-column\", \"flex-column-fluid\"], [4, \"ngIf\"], [\"id\", \"kt_app_wrapper\", 1, \"app-wrapper\", \"flex-column\", \"flex-row-fluid\"], [\"id\", \"kt_app_main\", 1, \"app-main\", \"flex-column\", \"flex-row-fluid\"], [1, \"d-flex\", \"flex-column\", \"flex-column-fluid\"], [\"id\", \" kt_app_content\", 1, \"app-content\", 3, \"ngClass\", \"contentContainerCSSClass\", \"appContentContiner\", \"appContentContainerClass\"], [\"id\", \"kt_scrolltop\", \"data-kt-scrolltop\", \"true\", 1, \"scrolltop\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [\"id\", \"kt_app_header\", \"data-kt-sticky\", \"true\", \"data-kt-sticky-activate\", \"{default: true, lg: true}\", \"data-kt-sticky-name\", \"app-header-minimize\", \"data-kt-sticky-offset\", \"{default: '200px', lg: '0'}\", \"data-kt-sticky-animation\", \"false\", 1, \"app-header\", 3, \"ngClass\"], [\"id\", \"kt_app_sidebar\", 1, \"app-sidebar\", \"flex-column\", 3, \"ngClass\"], [\"id\", \"kt_app_toolbar\", 1, \"app-toolbar\", 3, \"ngClass\", \"appToolbarLayout\"], [\"id\", \"kt_app_footer\", 1, \"app-footer\", 3, \"ngClass\", \"appFooterContainerCSSClass\"]],\n      template: function LayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LayoutComponent_div_0_Template, 7, 0, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3);\n          i0.ɵɵtemplate(3, LayoutComponent_ng_container_3_Template, 2, 1, \"ng-container\", 4);\n          i0.ɵɵelementStart(4, \"div\", 5);\n          i0.ɵɵtemplate(5, LayoutComponent_ng_container_5_Template, 3, 1, \"ng-container\", 4)(6, LayoutComponent_ng_container_6_Template, 1, 0, \"ng-container\", 4);\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵtemplate(9, LayoutComponent_ng_container_9_Template, 2, 2, \"ng-container\", 4);\n          i0.ɵɵelement(10, \"app-content\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, LayoutComponent_ng_container_11_Template, 2, 2, \"ng-container\", 4);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(12, \"app-scripts-init\");\n          i0.ɵɵelementContainerStart(13);\n          i0.ɵɵelement(14, \"app-scroll-top\", 9);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelement(15, \"app-activity-drawer\")(16, \"app-messenger-drawer\")(17, \"app-main-modal\")(18, \"app-invite-users-modal\")(19, \"app-upgrade-plan-modal\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.appHeaderDisplay);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.appSidebarDisplay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.appSidebarPanelDisplay);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.appToolbarDisplay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.contentCSSClasses)(\"contentContainerCSSClass\", ctx.contentContainerCSSClass)(\"appContentContiner\", ctx.appContentContiner)(\"appContentContainerClass\", ctx.appContentContainerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.appFooterDisplay);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i6.LayoutScrollTopComponent, i7.InviteUsersModalComponent, i8.MainModalComponent, i9.UpgradePlanModalComponent, i10.ActivityDrawerComponent, i11.MessengerDrawerComponent, i12.HeaderComponent, i13.ContentComponent, i14.FooterComponent, i15.ScriptsInitComponent, i16.ToolbarComponent, i17.SidebarComponent],\n      styles: [\"[_nghost-%COMP%]{height:100%;margin:0}[_nghost-%COMP%]   .flex-root[_ngcontent-%COMP%]{height:100%}.page-loaded[_ngcontent-%COMP%]   app-layout[_ngcontent-%COMP%]{opacity:1;transition:opacity 1s ease-in-out}.app-sidebar[_ngcontent-%COMP%]{border-right:0}\"]\n    });\n  }\n  return LayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}