<!-- Full Screen Loading Overlay -->
<div *ngIf="loading || isLoading" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 text-primary fs-5">Loading...</div>
  </div>
</div>

<div class="grid-container">
  <kendo-grid
    #normalGrid
    [data]="gridData"
    [pageSize]="page.size"
    [sort]="sort"
    [pageable]="{
      pageSizes: [15, 20, 50, 100],
      previousNext: true,
      info: true,
      type: 'numeric',
      buttonCount: 5
    }"
    [total]="page.totalElements"
    [sortable]="false"
    [groupable]="false"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
    (columnReorder)="onColumnReorder($event)"
    (selectionChange)="onSelectionChange($event)"
    [reorderable]="true"
    style="width: auto; overflow-x: auto"
    [resizable]="false"
    [height]="720"
    [skip]="skip"
    [filter]="filter"
    [columnMenu]="{ filter: true }"
    (filterChange)="filterChange($event)"
    (pageChange)="pageChange($event)"
    (dataStateChange)="onDataStateChange($event)"
    (columnVisibilityChange)="updateColumnVisibility($event)"
    [loading]="false"
  >
    <ng-template kendoGridToolbarTemplate>
      <!-- Search Section -->
      <div class="d-flex align-items-center me-3 search-section">
        <kendo-textbox
          [style.width.px]="500"
          placeholder="Search..."
          [(ngModel)]="searchData"
          [clearButton]="true"
          (keydown)="onSearchKeyDown($event)"
          (ngModelChange)="onSearchChange()"
        ></kendo-textbox>
      </div>

      <kendo-grid-spacer></kendo-grid-spacer>

      <!-- Total Count - Repositioned to the right -->
      <div class="d-flex align-items-center me-3">
        <span class="text-muted">Total: </span>
        <span class="fw-bold ms-1">{{ page.totalElements || 0 }}</span>
      </div>

      <!-- Action Buttons -->
      <button
        type="button"
        class="btn btn-primary btn-sm me-2"
        (click)="add()"
      >
        <span
          [inlineSVG]="'./assets/media/icons/duotune/arrows/arr075.svg'"
          class="svg-icon svg-icon-3"
        ></span>
        Add
      </button>

      <button
      type="button"
      class="btn btn-secondary btn-sm me-2"
      (click)="toggleExpand()"
      title="Toggle Grid Expansion"
    >
      <i
        class="fas"
        [class.fa-expand]="!isExpanded"
        [class.fa-compress]="isExpanded"
      ></i>
    </button>

      <kendo-dropdownbutton
        text="Export Excel"
        iconClass="fas fa-file-excel"
        [data]="exportOptions"
        class="btn btn-secondary btn-sm me-2"
        (itemClick)="onExportClick($event)"
        title="Export"
      >
      </kendo-dropdownbutton>

      <!-- Save Column Settings Button -->
      <!-- <button
        type="button"
        class="btn btn-success btn-sm me-2"
        (click)="saveHead()"
        title="Save Column Settings"
      >
        <i class="fas fa-save"></i>
      </button> -->

      <!-- Reset Button -->
      <button
        type="button"
        class="btn btn-warning btn-sm me-2"
        (click)="resetTable()"
        title="Reset to Default"
      >
        <i class="fas fa-undo"></i>
      </button>

      <!-- Refresh Button -->
      <button
        type="button"
        class="btn btn-info btn-sm me-2"
        (click)="refreshGrid()"
        title="Refresh Grid Data"
      >
        <i class="fas fa-sync-alt"></i>
      </button>
    </ng-template>

    <!-- Advanced Filters Panel -->
    <ng-template kendoGridToolbarTemplate>
      <div
        *ngIf="showAdvancedFilters"
        class="advanced-filters-panel p-3 bg-light border-bottom"
      >
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">Status</label>
            
            <kendo-dropdownlist
              [data]="advancedFilterOptions.status"
              [(ngModel)]="appliedFilters.status"
              textField="text"
              valueField="value"
              placeholder="Select Status"
            >
            </kendo-dropdownlist>
          </div>
          <div class="col-md-3 d-flex align-items-end">
            <button
              kendoButton
              (click)="applyAdvancedFilters()"
              class="btn-primary me-2"
            >
              <i class="fas fa-check"></i> Apply Filters
            </button>
            <button
              kendoButton
              (click)="clearAllFilters()"
              class="btn-secondary"
            >
              <i class="fas fa-times"></i> Clear
            </button>
          </div>
        </div>
      </div>
    </ng-template>
    <ng-container *ngFor="let column of gridColumns">
      <!-- Action Column -->
      <kendo-grid-column
        *ngIf="column === 'action'"
        title="Actions"
        [width]="90"
        [sticky]="true"
        [reorderable]="!fixedColumns.includes('action')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [includeInChooser]="false"
        [columnMenu]="false"
        [style]="{ 'background-color': '#efefef !important' }"
        [hidden]="getHiddenField('action')"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          <div class="d-flex align-items-center justify-content-center gap-1" style="min-height: 32px;">
            <!-- <a
              title="View"
              class="btn btn-icon btn-sm"
              (click)="view(dataItem.patientId)"
            >
              <span
                [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'"
                class="svg-icon svg-icon-3 svg-icon-primary"
              >
              </span>
            </a> -->
            <!-- View icon - always on the left -->
            <a
              title="View"
              class="btn btn-icon btn-sm d-flex align-items-center justify-content-center"
              style="width: 32px; height: 32px;"
              (click)="edit(dataItem.projectId)"
            >
              <i class="fas fa-pencil" style="color: var(--bs-primary, #0d6efd); font-size: 1rem;"></i>
            </a>
            <!-- Delete icon - always on the right, with consistent spacing -->
            <a
              title="Delete"
              class="btn btn-icon  btn-sm d-flex align-items-center justify-content-center"
              style="width: 32px; height: 32px;"
              [class.invisible]="!dataItem.isDeletable"
              [class.disabled]="!dataItem.isDeletable"
              (click)="dataItem.isDeletable && deletePop(deleteModal, dataItem.projectId, dataItem.projectName)"
            >
              <span
                [inlineSVG]="'./assets/media/icons/duotune/general/gen027.svg'"
                class="svg-icon svg-icon-3 svg-icon-danger"
              >
              </span>
            </a>
          </div>

          <!-- <div class="d-flex gap-1">
          <button class="btn btn-sm btn-light-primary" (click)="view(dataItem.patientId)" title="View">
            <i class="fas fa-eye"></i>
          </button>
          <button class="btn btn-sm btn-light-warning" (click)="edit(dataItem.patientId)" title="Edit">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn btn-sm btn-light-danger" (click)="delete(dataItem.patientId)" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </div> -->
        </ng-template>
      </kendo-grid-column>
      <!-- Project Name Column -->
      <kendo-grid-column
        *ngIf="column === 'projectName'"
        field="projectName"
        title="Project name"
        [width]="200"
        [sticky]="true"
        [reorderable]="!fixedColumns.includes('projectName')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [includeInChooser]="false"
        [hidden]="getHiddenField('projectName')"
        [filterable]="true"
      >
        <ng-template kendoGridHeaderTemplate>
          <div (click)="onColumnSort('projectName')" style="cursor: pointer; user-select: none;">
            Project Name
            <span *ngIf="columnSortStates['projectName'] === 'asc'" style="color: red; font-weight: bold;">↑</span>
            <span *ngIf="columnSortStates['projectName'] === 'desc'" style="color: red; font-weight: bold;">↓</span>
          </div>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
           <strong>{{ dataItem.projectName }}</strong>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>
      <!-- Internal Project Number Column -->
      <kendo-grid-column
        *ngIf="column === 'internalProjectNumber'"
        field="internalProjectNumber"
        title="Internal Project #"
        [width]="150"
        [reorderable]="!fixedColumns.includes('internalProjectNumber')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('internalProjectNumber')"
        [filterable]="true"
      >
        <ng-template kendoGridHeaderTemplate>
          <div (click)="onColumnSort('internalProjectNumber')" style="cursor: pointer; user-select: none;">
            Internal Project #
            <span *ngIf="columnSortStates['internalProjectNumber'] === 'asc'" style="color: red; font-weight: bold;">↑</span>
            <span *ngIf="columnSortStates['internalProjectNumber'] === 'desc'" style="color: red; font-weight: bold;">↓</span>
          </div>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
           {{ dataItem.internalProjectNumber }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Project Start Date Column -->
      <kendo-grid-column
        *ngIf="column === 'projectStartDate'"
        field="projectStartDate"
        title="Start Date"
        [width]="110"
        [reorderable]="!fixedColumns.includes('projectStartDate')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('projectStartDate')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ formatDate(dataItem.projectStartDate) }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-gte-operator></kendo-filter-gte-operator>
            <kendo-filter-lte-operator></kendo-filter-lte-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
      <!-- Project End Date Column -->
      <kendo-grid-column
        *ngIf="column === 'projectEndDate'"
        field="projectEndDate"
        title="End Date"
        [width]="110"
        [reorderable]="!fixedColumns.includes('projectEndDate')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('projectEndDate')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
         {{ formatDate(dataItem.projectEndDate) }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-gte-operator></kendo-filter-gte-operator>
            <kendo-filter-lte-operator></kendo-filter-lte-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Project Location Column -->
      <kendo-grid-column
        *ngIf="column === 'projectLocation'"
        field="projectLocation"
        title="Location"
        [width]="180"
        [reorderable]="!fixedColumns.includes('projectLocation')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('projectLocation')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.projectLocation }}
          <!-- <div>
            <span class="fw-bolder">
              
            </span>
          </div> -->
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Internal Project Manager Column -->
      <kendo-grid-column
        *ngIf="column === 'internalProjectManagerName'"
        field="internalProjectManagerName"
        title="Internal Manager"
        [width]="180"
        [reorderable]="!fixedColumns.includes('internalProjectManagerName')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('internalProjectManagerName')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.internalProjectManagerName }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- External PM Column -->
      <kendo-grid-column
        *ngIf="column === 'externalPMNames'"
        field="externalPMNames"
        title="External PM"
        [width]="220"
        [reorderable]="!fixedColumns.includes('externalPMNames')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('externalPMNames')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
       {{ dataItem.externalPMNames }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Last Updated Date Column -->
      <kendo-grid-column
        *ngIf="column === 'lastUpdatedDate'"
        field="lastUpdatedDate"
        title="Updated Date"
        [width]="110"
        [reorderable]="!fixedColumns.includes('lastUpdatedDate')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('lastUpdatedDate')"
        [filterable]="true"
      >
        <ng-template kendoGridHeaderTemplate>
          <div (click)="onColumnSort('lastUpdatedDate')" style="cursor: pointer; user-select: none;">
            Updated date
            <span *ngIf="columnSortStates['lastUpdatedDate'] === 'asc'" style="color: red; font-weight: bold;">↑</span>
            <span *ngIf="columnSortStates['lastUpdatedDate'] === 'desc'" style="color: red; font-weight: bold;">↓</span>
          </div>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
           {{ formatDate(dataItem.lastUpdatedDate) }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-gte-operator></kendo-filter-gte-operator>
            <kendo-filter-lte-operator></kendo-filter-lte-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
    </ng-container>

    <!-- No Data Template -->
    <ng-template kendoGridNoRecordsTemplate>
      <div
        class="custom-no-records"
        *ngIf="loading === false && serverSideRowData.length === 0"
      >
        <div class="text-center">
          <i
            class="fas fa-folder-open text-muted mb-2"
            style="font-size: 2rem"
          ></i>
          <p class="text-muted">No projects found</p>
          <button kendoButton (click)="loadTable()" class="btn-primary">
            <i class="fas fa-refresh me-2"></i>Refresh
          </button>
        </div>
      </div>
    </ng-template>
  </kendo-grid>
</div>

<!-- Delete Confirmation Modal -->
<ng-template #deleteModal let-modal>
  <div class="modal-header bg-danger text-white">
    <h5 class="modal-title">Confirm Delete</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="onDeleteCancelClick(modal)"
    ></button>
  </div>

  <div class="delete-modal-body mt-4 text-center">
    <p class="fs-5">
      Are you sure you want to delete this project? - {{ this.projectName }}
    </p>
  </div>

  <div class="modal-footer delete-modal-footer ms-2">
    <button type="button" class="btn btn-danger" (click)="onDeleteCancelClick(modal)">
      Cancel
    </button>
    <button
      type="button"
      class="btn btn-primary "
      (click)="confirmDelete(); modal.close()"
    >
      Delete
    </button>
  </div>
</ng-template>
