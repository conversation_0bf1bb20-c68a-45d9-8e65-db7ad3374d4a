{"ast": null, "code": "import { AppService } from '../../services/app.service';\nimport jsPDF from 'jspdf';\nimport autoTable from 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/custom-layout.utils.service\";\nimport * as i3 from \"../../services/app.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../services/permits.service\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(correction_r1.Response);\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Resolved Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, correction_r1.ResolvedDate, \"MM/dd/yyyy\"));\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 29);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 30)(9, \"div\", 31)(10, \"label\");\n    i0.ɵɵtext(11, \"Corrective Action\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 32);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 31)(15, \"label\");\n    i0.ɵɵtext(16, \"Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 32);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_19_Template, 5, 1, \"div\", 33)(20, ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_20_Template, 6, 4, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r2 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Correction Type: \", correction_r1.CorrectionTypeName || \"General\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Category: \", correction_r1.CorrectionCategoryName || \"General Correction\", \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(correction_r1.CorrectiveAction || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r1.Comments || \"No comment provided\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r1.Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r1.ResolvedDate);\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h6\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ReviewDetailsModalComponent_ng_container_39_div_1_div_3_Template, 21, 7, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Corrections (\", ctx_r2.review.corrections.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.review.corrections);\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"h6\", 24);\n    i0.ɵɵtext(2, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.review.comments);\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReviewDetailsModalComponent_ng_container_39_div_1_Template, 4, 2, \"div\", 21)(2, ReviewDetailsModalComponent_ng_container_39_div_2_Template, 6, 1, \"div\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.review == null ? null : ctx_r2.review.corrections) && ctx_r2.review.corrections.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(ctx_r2.review == null ? null : ctx_r2.review.corrections) || ctx_r2.review.corrections.length === 0) && (ctx_r2.review == null ? null : ctx_r2.review.comments));\n  }\n}\nfunction ReviewDetailsModalComponent_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 39);\n    i0.ɵɵlistener(\"ngSubmit\", function ReviewDetailsModalComponent_ng_container_40_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit());\n    });\n    i0.ɵɵelementStart(2, \"div\", 40)(3, \"div\", 41)(4, \"label\", 42);\n    i0.ɵɵtext(5, \"EOR / AOR / Owner Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"textarea\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\", 41)(9, \"label\", 42);\n    i0.ɵɵtext(10, \"Comment Responded By\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.reviewForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading);\n  }\n}\nfunction ReviewDetailsModalComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \" No corrections or comments available for this review.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ReviewDetailsModalComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 48);\n  }\n}\nexport class ReviewDetailsModalComponent {\n  modal;\n  customLayoutUtilsService;\n  appService;\n  fb;\n  cdr;\n  permitsService;\n  review;\n  permitId = null;\n  permitDetails = {};\n  selectedTab = 'corrections';\n  reviewForm;\n  isLoading = false;\n  loginUser;\n  constructor(modal, customLayoutUtilsService, appService, fb, cdr, permitsService) {\n    this.modal = modal;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.appService = appService;\n    this.fb = fb;\n    this.cdr = cdr;\n    this.permitsService = permitsService;\n  }\n  // Method to handle cancel button click\n  onCancelClick() {\n    this.modal.dismiss();\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.reviewForm = this.fb.group({\n      // cityComments: [this.reviewData?.comments || ''],\n      EORAOROwner_Response: [this.review?.EORAOROwner_Response || ''],\n      commentResponsedBy: [this.review?.commentResponsedBy || '']\n    });\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  onSubmit() {\n    this.isLoading = true;\n    const formData = {\n      // cityComments:this.reviewForm.controls.cityComments.value,\n      EORAOROwner_Response: this.reviewForm.controls.EORAOROwner_Response.value,\n      commentResponsedBy: this.reviewForm.controls.commentResponsedBy.value,\n      permitId: this.permitId,\n      commentsId: this.review.commentsId,\n      loggedInUserId: this.loginUser.userId\n    };\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n          this.modal.close('updated');\n        } else {\n          this.customLayoutUtilsService.showError(res.responseData.message || 'Failed to update external review', '');\n          //alert(res.responseData.message || 'Failed to update external review');\n        }\n      },\n      error: err => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('Error updating external review', '');\n        //alert('Error updating external review');\n        console.error(err);\n      }\n    });\n  }\n  getPdf() {\n    const permitNumber = this.permitDetails?.permitNumber || '';\n    const projectName = this.permitDetails?.projectName || '';\n    const applicantName = this.permitDetails?.applicantName || '';\n    const reviewer = (this.review?.AssignedTo || this.review?.municipalityReviewer || '').toString();\n    const cityComments = (this.review?.Comments || (this.review?.cityComments ?? '')).toString();\n    const ownerResponse = (this.reviewForm?.value?.EORAOROwner_Response ?? this.review?.EORAOROwner_Response ?? '').toString();\n    const respondedBy = (this.reviewForm?.value?.commentResponsedBy ?? this.review?.commentResponsedBy ?? '').toString();\n    const cycle = (this.review?.cycle || this.review?.Cycle || '').toString();\n    const status = (this.review?.StatusName || this.review?.commentstatus || this.review?.status || '').toString();\n    const reviewCategory = (this.review?.TypeName || this.review?.reviewCategory || '').toString();\n    const dueDate = this.review?.DueDate ? AppService.formatDate(this.review.DueDate) : '';\n    const completedDate = this.review?.CompletedDate ? AppService.formatDate(this.review.CompletedDate) : '';\n    const createdDate = this.review?.createdDate ? AppService.formatDate(this.review.createdDate) : '';\n    const doc = new jsPDF({\n      orientation: 'portrait',\n      unit: 'pt',\n      format: 'a4'\n    });\n    // Page metrics and margins\n    const pageWidth = doc.internal.pageSize.getWidth();\n    const pageHeight = doc.internal.pageSize.getHeight();\n    const margin = {\n      left: 50,\n      right: 50,\n      top: 60,\n      bottom: 60\n    };\n    let currentY = margin.top;\n    // Professional Header with Company Branding\n    const drawHeader = () => {\n      // Company Logo/Title Area\n      doc.setFillColor(41, 128, 185); // Professional blue\n      doc.rect(0, 0, pageWidth, 50, 'F');\n      doc.setTextColor(255, 255, 255);\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(18);\n      doc.text('PERMIT TRACKER SYSTEM', margin.left, 25);\n      doc.setFontSize(12);\n      doc.text('Review Report', pageWidth - margin.right - 80, 25);\n      // Reset text color\n      doc.setTextColor(0, 0, 0);\n      // Permit Information Header\n      currentY = 70;\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(14);\n      doc.text('REVIEW DETAILS', margin.left, currentY);\n      currentY += 20;\n    };\n    drawHeader();\n    // Permit Information Section\n    const drawPermitInfo = () => {\n      const infoData = [['Permit #:', permitNumber || ''], ['Project Name:', projectName || ''], ['Applicant Name:', applicantName || ''], ['Review Category:', reviewCategory || ''], ['Reviewer:', reviewer || ''], ['Status:', status || ''], ['Due Date:', dueDate || ''], ['Completed Date:', completedDate || ''], ['Created Date:', createdDate || '']];\n      autoTable(doc, {\n        startY: currentY,\n        body: infoData,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 10,\n          cellPadding: 8,\n          overflow: 'linebreak'\n        },\n        columnStyles: {\n          0: {\n            cellWidth: 120,\n            fontStyle: 'bold',\n            fillColor: [240, 240, 240]\n          },\n          1: {\n            cellWidth: 200\n          }\n        },\n        theme: 'grid'\n      });\n      currentY = doc.lastAutoTable.finalY + 20;\n    };\n    drawPermitInfo();\n    // City Comments Section\n    if (cityComments) {\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(12);\n      doc.setTextColor(192, 0, 0); // Red color for city comments\n      doc.text('CITY COMMENTS', margin.left, currentY);\n      currentY += 15;\n      doc.setTextColor(0, 0, 0);\n      doc.setFont('helvetica', 'normal');\n      doc.setFontSize(10);\n      // Split long text into multiple lines\n      const splitText = doc.splitTextToSize(cityComments, pageWidth - margin.left - margin.right);\n      doc.text(splitText, margin.left, currentY);\n      currentY += splitText.length * 12 + 20;\n    }\n    // Owner Response Section\n    if (ownerResponse) {\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(12);\n      doc.text('EOR/AOR/OWNER RESPONSE', margin.left, currentY);\n      currentY += 15;\n      doc.setFont('helvetica', 'normal');\n      doc.setFontSize(10);\n      const splitText = doc.splitTextToSize(ownerResponse, pageWidth - margin.left - margin.right);\n      doc.text(splitText, margin.left, currentY);\n      currentY += splitText.length * 12 + 20;\n      // Response details\n      if (respondedBy) {\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(10);\n        doc.text(`Responded By: ${respondedBy}`, margin.left, currentY);\n        currentY += 15;\n      }\n    }\n    // Corrections Section\n    if (this.review?.Corrections && this.review.Corrections.length > 0) {\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(12);\n      doc.text('CORRECTIONS & DETAILS', margin.left, currentY);\n      currentY += 15;\n      this.review.Corrections.forEach((correction, index) => {\n        // Check if we need a new page\n        if (currentY > pageHeight - 200) {\n          doc.addPage();\n          drawHeader();\n          currentY = margin.top + 20;\n        }\n        // Correction header\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(10);\n        doc.text(`Correction ${index + 1}`, margin.left, currentY);\n        currentY += 15;\n        // Correction details table\n        const correctionData = [['Correction ID:', correction.CorrectionID || ''], ['Type:', correction.CorrectionTypeName || ''], ['Category:', correction.CorrectionCategoryName || ''], ['Comments:', correction.Comments || ''], ['Corrective Action:', correction.CorrectiveAction || ''], ['Response:', correction.Response || ''], ['Owner Response:', correction.EORAOROwner_Response || ''], ['Responded By:', correction.commentResponsedBy || ''], ['Resolved Date:', correction.ResolvedDate ? AppService.formatDate(correction.ResolvedDate) : ''], ['Is Resolved:', correction.IsResolvedText || '']];\n        autoTable(doc, {\n          startY: currentY,\n          body: correctionData,\n          margin: {\n            left: margin.left,\n            right: margin.right\n          },\n          styles: {\n            font: 'helvetica',\n            fontSize: 9,\n            cellPadding: 6,\n            overflow: 'linebreak'\n          },\n          columnStyles: {\n            0: {\n              cellWidth: 100,\n              fontStyle: 'bold',\n              fillColor: [245, 245, 245]\n            },\n            1: {\n              cellWidth: 200\n            }\n          },\n          theme: 'grid'\n        });\n        currentY = doc.lastAutoTable.finalY + 15;\n      });\n    }\n    // Footer\n    const drawFooter = () => {\n      const pageCount = doc.getNumberOfPages();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        // Footer line\n        doc.setDrawColor(200, 200, 200);\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n        // Footer text\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(8);\n        doc.setTextColor(100, 100, 100);\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n      }\n    };\n    drawFooter();\n    // Save the PDF\n    const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${reviewCategory ? reviewCategory.replace(/[^a-zA-Z0-9]/g, '_') + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  }\n  static ɵfac = function ReviewDetailsModalComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ReviewDetailsModalComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i3.AppService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PermitsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ReviewDetailsModalComponent,\n    selectors: [[\"app-review-details-modal\"]],\n    inputs: {\n      review: \"review\",\n      permitId: \"permitId\",\n      permitDetails: \"permitDetails\"\n    },\n    decls: 50,\n    vars: 23,\n    consts: [[1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [1, \"dates-section\"], [1, \"date-row\"], [1, \"date-item\"], [1, \"date-value\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"min-height\", \"30px !important\", \"padding\", \"0 1.25rem !important\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-6\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", \"fs-6\", 3, \"click\", \"ngClass\"], [1, \"card-body\", \"p-0\"], [4, \"ngIf\"], [\"class\", \"no-data-section\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"btn-elevate\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [\"class\", \"corrections-section\", 4, \"ngIf\"], [\"class\", \"comments-section\", 4, \"ngIf\"], [1, \"corrections-section\"], [1, \"section-title\"], [\"class\", \"correction-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"correction-item\"], [1, \"correction-header\"], [1, \"correction-number\"], [1, \"correction-action\"], [1, \"correction-content\"], [1, \"correction-field\"], [1, \"correction-comment\"], [\"class\", \"correction-field\", 4, \"ngIf\"], [1, \"correction-response\"], [1, \"correction-resolved\"], [1, \"comments-section\"], [1, \"comment-content\"], [1, \"comment-text\"], [\"novalidate\", \"\", 1, \"px-6\", \"mb-10\", 3, \"ngSubmit\", \"formGroup\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"formControlName\", \"EORAOROwner_Response\", \"rows\", \"3\", \"placeholder\", \"Enter response\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"text\", \"formControlName\", \"commentResponsedBy\", \"placeholder\", \"Who responded to comments\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"no-data-section\"], [1, \"no-data-message\"], [1, \"fas\", \"fa-info-circle\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function ReviewDetailsModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_button_click_3_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"label\");\n        i0.ɵɵtext(9, \"Reviwer\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"span\", 7);\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 6)(13, \"label\");\n        i0.ɵɵtext(14, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"span\", 7);\n        i0.ɵɵtext(16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\");\n        i0.ɵɵtext(19, \"Due Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"span\", 7);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 6)(24, \"label\");\n        i0.ɵɵtext(25, \"Completed Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"span\", 7);\n        i0.ɵɵtext(27);\n        i0.ɵɵpipe(28, \"date\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(29, \"div\", 8)(30, \"div\", 9)(31, \"ul\", 10)(32, \"li\", 11)(33, \"a\", 12);\n        i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_a_click_33_listener($event) {\n          return ctx.showTab(\"corrections\", $event);\n        });\n        i0.ɵɵtext(34, \" Corrections / Comments \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(35, \"li\", 11)(36, \"a\", 12);\n        i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_a_click_36_listener($event) {\n          return ctx.showTab(\"review\", $event);\n        });\n        i0.ɵɵtext(37, \" Reviews \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(38, \"div\", 13);\n        i0.ɵɵtemplate(39, ReviewDetailsModalComponent_ng_container_39_Template, 3, 2, \"ng-container\", 14)(40, ReviewDetailsModalComponent_ng_container_40_Template, 12, 3, \"ng-container\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(41, ReviewDetailsModalComponent_div_41_Template, 5, 0, \"div\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"div\", 16)(43, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_button_click_43_listener() {\n          return ctx.modal.close();\n        });\n        i0.ɵɵtext(44, \"Close\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_button_click_45_listener() {\n          return ctx.getPdf();\n        });\n        i0.ɵɵtext(46, \" Download PDF \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"button\", 19);\n        i0.ɵɵlistener(\"click\", function ReviewDetailsModalComponent_Template_button_click_47_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtemplate(48, ReviewDetailsModalComponent_span_48_Template, 1, 0, \"span\", 20);\n        i0.ɵɵtext(49, \" Update \");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.review == null ? null : ctx.review.name);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.review == null ? null : ctx.review.reviewer);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.review == null ? null : ctx.review.status);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 13, ctx.review == null ? null : ctx.review.dueDate, \"MM/dd/yyyy\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(28, 16, ctx.review == null ? null : ctx.review.completedDate, \"MM/dd/yyyy\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx.selectedTab === \"corrections\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx.selectedTab === \"review\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"corrections\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"review\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (!(ctx.review == null ? null : ctx.review.corrections) || ctx.review.corrections.length === 0) && !(ctx.review == null ? null : ctx.review.comments));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      }\n    },\n    styles: [\"[_nghost-%COMP%]     .modal-dialog {\\n  max-width: 800px;\\n  width: 90%;\\n}\\n\\n[_nghost-%COMP%]     .modal-body {\\n  max-height: 70vh;\\n  overflow-y: auto;\\n  padding: 0;\\n}\\n\\n.review-header-banner[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-radius: 0.375rem 0.375rem 0 0;\\n  margin-top: 2px;\\n  margin-left: 4px;\\n}\\n\\n.banner-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n  flex: 1;\\n}\\n\\n.banner-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.status-icon.status-requires-resubmit[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.status-icon.status-approved[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.status-icon.status-under-review[_ngcontent-%COMP%] {\\n  color: #17a2b8;\\n}\\n.status-icon.status-pending[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n\\n.banner-text[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n\\n.envelope-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  margin-left: 0.5rem;\\n}\\n\\n.square-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  margin-left: 0.25rem;\\n}\\n\\n.dates-section[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background-color: #f8f9fa;\\n}\\n\\n.date-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n  gap: 2rem;\\n}\\n\\n.date-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.date-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  color: #6c7293;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n}\\n.date-item[_ngcontent-%COMP%]   .date-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n}\\n\\n.comment-section[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  margin-bottom: 0.75rem;\\n  padding-bottom: 0.25rem;\\n  border-bottom: 1px solid #e5eaee;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 0.875rem;\\n  color: #3f4254;\\n  line-height: 1.4;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-title[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-id[_ngcontent-%COMP%], \\n.contact-info[_ngcontent-%COMP%]   .contact-license[_ngcontent-%COMP%], \\n.contact-info[_ngcontent-%COMP%]   .contact-certification[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-organization[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-phone[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n}\\n.contact-info[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: 500;\\n}\\n\\n.corrections-section[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n}\\n\\n.correction-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  background: #fff;\\n}\\n.correction-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.correction-header[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 0.5rem 1rem;\\n  border-bottom: 1px solid #e5eaee;\\n  border-radius: 0.375rem 0.375rem 0 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  flex-shrink: 0;\\n}\\n.correction-header[_ngcontent-%COMP%]   .correction-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n}\\n.correction-header[_ngcontent-%COMP%]   .correction-action[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #3f4254;\\n}\\n\\n.correction-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.correction-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.correction-field[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.correction-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  color: #6c7293;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n  margin-bottom: 0.25rem;\\n}\\n.correction-field[_ngcontent-%COMP%]   .correction-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n}\\n.correction-field[_ngcontent-%COMP%]   .correction-comment[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #3f4254;\\n  line-height: 1.5;\\n  background-color: #f8f9fa;\\n  padding: 0.75rem;\\n  border-radius: 0.25rem;\\n  border-left: 3px solid #007bff;\\n}\\n.correction-field[_ngcontent-%COMP%]   .correction-response[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #28a745;\\n  line-height: 1.5;\\n  background-color: #f8fff9;\\n  padding: 0.75rem;\\n  border-radius: 0.25rem;\\n  border-left: 3px solid #28a745;\\n}\\n.correction-field[_ngcontent-%COMP%]   .correction-resolved[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c7293;\\n  font-weight: 500;\\n}\\n\\n.comments-section[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n}\\n\\n.comment-content[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #3f4254;\\n  line-height: 1.6;\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border-left: 4px solid #17a2b8;\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n}\\n\\n.no-data-section[_ngcontent-%COMP%] {\\n  padding: 2rem 1.5rem;\\n  text-align: center;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.75rem;\\n  color: #6c7293;\\n}\\n.no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #dee2e6;\\n}\\n.no-data-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.correction-contact-info[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #e5eaee;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  color: #6c7293;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n  margin-bottom: 0.5rem;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 0.75rem;\\n  border-radius: 0.25rem;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 0.875rem;\\n  color: #3f4254;\\n  line-height: 1.4;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-title[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-id[_ngcontent-%COMP%], \\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-license[_ngcontent-%COMP%], \\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-certification[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-organization[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-phone[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n}\\n.correction-contact-info[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-email[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 768px) {\\n  .date-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n  .banner-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .banner-item[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["AppService", "jsPDF", "autoTable", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "correction_r1", "Response", "ɵɵpipeBind2", "ResolvedDate", "ɵɵtemplate", "ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_19_Template", "ReviewDetailsModalComponent_ng_container_39_div_1_div_3_div_20_Template", "i_r2", "ɵɵtextInterpolate1", "CorrectionTypeName", "CorrectionCategoryName", "CorrectiveAction", "Comments", "ɵɵproperty", "ReviewDetailsModalComponent_ng_container_39_div_1_div_3_Template", "ctx_r2", "review", "corrections", "length", "comments", "ɵɵelementContainerStart", "ReviewDetailsModalComponent_ng_container_39_div_1_Template", "ReviewDetailsModalComponent_ng_container_39_div_2_Template", "ɵɵlistener", "ReviewDetailsModalComponent_ng_container_40_Template_form_ngSubmit_1_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵelement", "reviewForm", "isLoading", "ReviewDetailsModalComponent", "modal", "customLayoutUtilsService", "appService", "fb", "cdr", "permitsService", "permitId", "permitDetails", "selectedTab", "loginUser", "constructor", "onCancelClick", "dismiss", "ngOnInit", "getLoggedInUser", "group", "EORAOROwner_Response", "commentResponsedBy", "showTab", "tab", "$event", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formData", "controls", "value", "commentsId", "loggedInUserId", "userId", "updateExternalReview", "subscribe", "next", "res", "<PERSON><PERSON><PERSON>", "showSuccess", "close", "showError", "responseData", "message", "error", "err", "console", "getPdf", "permitNumber", "projectName", "applicantName", "reviewer", "AssignedTo", "municipalityReviewer", "toString", "cityComments", "ownerResponse", "respondedBy", "cycle", "Cycle", "status", "StatusName", "commentstatus", "reviewCategory", "TypeName", "dueDate", "DueDate", "formatDate", "completedDate", "CompletedDate", "createdDate", "doc", "orientation", "unit", "format", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "margin", "left", "right", "top", "bottom", "currentY", "<PERSON><PERSON><PERSON><PERSON>", "setFillColor", "rect", "setTextColor", "setFont", "setFontSize", "text", "drawPermitInfo", "infoData", "startY", "body", "styles", "font", "fontSize", "cellPadding", "overflow", "columnStyles", "cellWidth", "fontStyle", "fillColor", "theme", "lastAutoTable", "finalY", "splitText", "splitTextToSize", "Corrections", "for<PERSON>ach", "correction", "index", "addPage", "correctionData", "CorrectionID", "IsResolvedText", "drawFooter", "pageCount", "getNumberOfPages", "i", "setPage", "setDrawColor", "line", "Date", "toLocaleString", "fileName", "replace", "toISOString", "split", "save", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "CustomLayoutUtilsService", "i3", "i4", "FormBuilder", "ChangeDetectorRef", "i5", "PermitsService", "selectors", "inputs", "decls", "vars", "consts", "template", "ReviewDetailsModalComponent_Template", "rf", "ctx", "ReviewDetailsModalComponent_Template_button_click_3_listener", "ReviewDetailsModalComponent_Template_a_click_33_listener", "ReviewDetailsModalComponent_Template_a_click_36_listener", "ReviewDetailsModalComponent_ng_container_39_Template", "ReviewDetailsModalComponent_ng_container_40_Template", "ReviewDetailsModalComponent_div_41_Template", "ReviewDetailsModalComponent_Template_button_click_43_listener", "ReviewDetailsModalComponent_Template_button_click_45_listener", "ReviewDetailsModalComponent_Template_button_click_47_listener", "ReviewDetailsModalComponent_span_48_Template", "name", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\review-details-modal\\review-details-modal.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\review-details-modal\\review-details-modal.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Input } from '@angular/core';\nimport { FormGroup, FormBuilder } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { PermitsService } from '../../services/permits.service';\nimport { AppService } from '../../services/app.service';\nimport jsPDF from 'jspdf';\nimport autoTable from 'jspdf-autotable';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\n@Component({\n  selector: 'app-review-details-modal',\n  templateUrl: './review-details-modal.component.html',\n  styleUrls: ['./review-details-modal.component.scss']\n})\nexport class ReviewDetailsModalComponent {\n  @Input() review: any;\n  @Input() permitId: number | null = null;\n  @Input() permitDetails: any = {};\n  selectedTab:any ='corrections';\n  reviewForm!: FormGroup;\n  isLoading: boolean = false;\n  loginUser:any\n  constructor(public modal: NgbActiveModal,private customLayoutUtilsService: CustomLayoutUtilsService,public appService: AppService, private fb: FormBuilder, private cdr:ChangeDetectorRef, private permitsService: PermitsService) {}\n\n  // Method to handle cancel button click\n  onCancelClick(): void {\n    this.modal.dismiss();\n  }\n  ngOnInit(): void {\n    this.loginUser =this.appService.getLoggedInUser()\n    this.reviewForm = this.fb.group({\n      // cityComments: [this.reviewData?.comments || ''],\n      EORAOROwner_Response: [this.review?.EORAOROwner_Response || ''],\n      commentResponsedBy: [this.review?.commentResponsedBy || '']\n    });\n  }\n  showTab(tab: any, $event: any) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n\n  onSubmit(): void {\n    this.isLoading = true;\n    const formData = {\n      // cityComments:this.reviewForm.controls.cityComments.value,\n      EORAOROwner_Response:this.reviewForm.controls.EORAOROwner_Response.value,\n      commentResponsedBy:this.reviewForm.controls.commentResponsedBy.value,\n      permitId: this.permitId,\n      commentsId:this.review.commentsId,\n      loggedInUserId: this.loginUser.userId\n    };\n      this.permitsService.updateExternalReview(formData).subscribe({\n        next: (res: any) => {\n          this.isLoading = false;\n          if (res?.isFault === false) {\n            //alert(res.responseData.message);\n                                    this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n\n            this.modal.close('updated');\n\n          } else {\n                      this.customLayoutUtilsService.showError(res.responseData.message ||'Failed to update external review', '');\n\n            //alert(res.responseData.message || 'Failed to update external review');\n          }\n        },\n        error: (err: any) => {\n          this.isLoading = false;\n                    this.customLayoutUtilsService.showError('Error updating external review', '');\n\n          //alert('Error updating external review');\n          console.error(err);\n        }\n      });\n\n\n}\ngetPdf(){\n  const permitNumber = this.permitDetails?.permitNumber || '';\n  const projectName = this.permitDetails?.projectName || '';\n  const applicantName = this.permitDetails?.applicantName || '';\n  const reviewer = (this.review?.AssignedTo || this.review?.municipalityReviewer || '').toString();\n  const cityComments = (this.review?.Comments || (this.review?.cityComments ?? '')).toString();\n  const ownerResponse = (this.reviewForm?.value?.EORAOROwner_Response ?? this.review?.EORAOROwner_Response ?? '').toString();\n  const respondedBy = (this.reviewForm?.value?.commentResponsedBy ?? this.review?.commentResponsedBy ?? '').toString();\n  const cycle = (this.review?.cycle || this.review?.Cycle || '').toString();\n  const status = (this.review?.StatusName || this.review?.commentstatus || this.review?.status || '').toString();\n  const reviewCategory = (this.review?.TypeName || this.review?.reviewCategory || '').toString();\n  const dueDate = this.review?.DueDate ? AppService.formatDate(this.review.DueDate) : '';\n  const completedDate = this.review?.CompletedDate ? AppService.formatDate(this.review.CompletedDate) : '';\n  const createdDate = this.review?.createdDate ? AppService.formatDate(this.review.createdDate) : '';\n\n  const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\n\n  // Page metrics and margins\n  const pageWidth = doc.internal.pageSize.getWidth();\n  const pageHeight = doc.internal.pageSize.getHeight();\n  const margin = { left: 50, right: 50, top: 60, bottom: 60 };\n\n  let currentY = margin.top;\n\n  // Professional Header with Company Branding\n  const drawHeader = () => {\n    // Company Logo/Title Area\n    doc.setFillColor(41, 128, 185); // Professional blue\n    doc.rect(0, 0, pageWidth, 50, 'F');\n    \n    doc.setTextColor(255, 255, 255);\n    doc.setFont('helvetica', 'bold');\n    doc.setFontSize(18);\n    doc.text('PERMIT TRACKER SYSTEM', margin.left, 25);\n    \n    doc.setFontSize(12);\n    doc.text('Review Report', pageWidth - margin.right - 80, 25);\n    \n    // Reset text color\n    doc.setTextColor(0, 0, 0);\n    \n    // Permit Information Header\n    currentY = 70;\n    doc.setFont('helvetica', 'bold');\n    doc.setFontSize(14);\n    doc.text('REVIEW DETAILS', margin.left, currentY);\n    \n    currentY += 20;\n  };\n\n  drawHeader();\n\n  // Permit Information Section\n  const drawPermitInfo = () => {\n    const infoData = [\n      ['Permit #:', permitNumber || ''],\n      ['Project Name:', projectName || ''],\n      ['Applicant Name:', applicantName || ''],\n      ['Review Category:', reviewCategory || ''],\n      ['Reviewer:', reviewer || ''],\n      ['Status:', status || ''],\n      ['Due Date:', dueDate || ''],\n      ['Completed Date:', completedDate || ''],\n      ['Created Date:', createdDate || '']\n    ];\n\n    autoTable(doc, {\n      startY: currentY,\n      body: infoData,\n      margin: { left: margin.left, right: margin.right },\n      styles: {\n        font: 'helvetica',\n        fontSize: 10,\n        cellPadding: 8,\n        overflow: 'linebreak'\n      },\n      columnStyles: {\n        0: { cellWidth: 120, fontStyle: 'bold', fillColor: [240, 240, 240] },\n        1: { cellWidth: 200 }\n      },\n      theme: 'grid'\n    });\n\n    currentY = (doc as any).lastAutoTable.finalY + 20;\n  };\n\n  drawPermitInfo();\n\n  // City Comments Section\n  if (cityComments) {\n    doc.setFont('helvetica', 'bold');\n    doc.setFontSize(12);\n    doc.setTextColor(192, 0, 0); // Red color for city comments\n    doc.text('CITY COMMENTS', margin.left, currentY);\n    currentY += 15;\n\n    doc.setTextColor(0, 0, 0);\n    doc.setFont('helvetica', 'normal');\n    doc.setFontSize(10);\n    \n    // Split long text into multiple lines\n    const splitText = doc.splitTextToSize(cityComments, pageWidth - margin.left - margin.right);\n    doc.text(splitText, margin.left, currentY);\n    currentY += splitText.length * 12 + 20;\n  }\n\n  // Owner Response Section\n  if (ownerResponse) {\n    doc.setFont('helvetica', 'bold');\n    doc.setFontSize(12);\n    doc.text('EOR/AOR/OWNER RESPONSE', margin.left, currentY);\n    currentY += 15;\n\n    doc.setFont('helvetica', 'normal');\n    doc.setFontSize(10);\n    \n    const splitText = doc.splitTextToSize(ownerResponse, pageWidth - margin.left - margin.right);\n    doc.text(splitText, margin.left, currentY);\n    currentY += splitText.length * 12 + 20;\n\n    // Response details\n    if (respondedBy) {\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Responded By: ${respondedBy}`, margin.left, currentY);\n      currentY += 15;\n    }\n  }\n\n  // Corrections Section\n  if (this.review?.Corrections && this.review.Corrections.length > 0) {\n    doc.setFont('helvetica', 'bold');\n    doc.setFontSize(12);\n    doc.text('CORRECTIONS & DETAILS', margin.left, currentY);\n    currentY += 15;\n\n    this.review.Corrections.forEach((correction: any, index: number) => {\n      // Check if we need a new page\n      if (currentY > pageHeight - 200) {\n        doc.addPage();\n        drawHeader();\n        currentY = margin.top + 20;\n      }\n\n      // Correction header\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Correction ${index + 1}`, margin.left, currentY);\n      currentY += 15;\n\n      // Correction details table\n      const correctionData = [\n        ['Correction ID:', correction.CorrectionID || ''],\n        ['Type:', correction.CorrectionTypeName || ''],\n        ['Category:', correction.CorrectionCategoryName || ''],\n        ['Comments:', correction.Comments || ''],\n        ['Corrective Action:', correction.CorrectiveAction || ''],\n        ['Response:', correction.Response || ''],\n        ['Owner Response:', correction.EORAOROwner_Response || ''],\n        ['Responded By:', correction.commentResponsedBy || ''],\n        ['Resolved Date:', correction.ResolvedDate ? AppService.formatDate(correction.ResolvedDate) : ''],\n        ['Is Resolved:', correction.IsResolvedText || '']\n      ];\n\n      autoTable(doc, {\n        startY: currentY,\n        body: correctionData,\n        margin: { left: margin.left, right: margin.right },\n        styles: {\n          font: 'helvetica',\n          fontSize: 9,\n          cellPadding: 6,\n          overflow: 'linebreak'\n        },\n        columnStyles: {\n          0: { cellWidth: 100, fontStyle: 'bold', fillColor: [245, 245, 245] },\n          1: { cellWidth: 200 }\n        },\n        theme: 'grid'\n      });\n\n      currentY = (doc as any).lastAutoTable.finalY + 15;\n    });\n  }\n\n  // Footer\n  const drawFooter = () => {\n    const pageCount = doc.getNumberOfPages();\n    for (let i = 1; i <= pageCount; i++) {\n      doc.setPage(i);\n      \n      // Footer line\n      doc.setDrawColor(200, 200, 200);\n      doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n      \n      // Footer text\n      doc.setFont('helvetica', 'normal');\n      doc.setFontSize(8);\n      doc.setTextColor(100, 100, 100);\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n      doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n    }\n  };\n\n  drawFooter();\n\n  // Save the PDF\n  const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${reviewCategory ? reviewCategory.replace(/[^a-zA-Z0-9]/g, '_') + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n  doc.save(fileName);\n}\n}\n\n\n", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\">{{review?.name}}</h5>\r\n  <button type=\"button\" class=\"btn-close\" aria-label=\"Close\" (click)=\"modal.dismiss()\"></button>\r\n</div>\r\n\r\n<div class=\"modal-body large-modal-body\">\r\n\r\n  <!-- Due Date / Completed Date Section -->\r\n  <div class=\"dates-section\">\r\n    <div class=\"date-row\">\r\n      <div class=\"date-item\">\r\n        <label>Reviwer</label>\r\n        <span class=\"date-value\">{{ review?.reviewer }}</span>\r\n      </div>\r\n      <div class=\"date-item\">\r\n        <label>Status</label>\r\n        <span class=\"date-value\">{{ review?.status }}</span>\r\n      </div>\r\n      <div class=\"date-item\">\r\n        <label>Due Date</label>\r\n        <span class=\"date-value\">{{ review?.dueDate | date:'MM/dd/yyyy' }}</span>\r\n      </div>\r\n      <div class=\"date-item\">\r\n        <label>Completed Date</label>\r\n        <span class=\"date-value\">{{ review?.completedDate | date:'MM/dd/yyyy' }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"card shadow-sm rounded-3\">\r\n    <!-- Card Header with Tabs -->\r\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\" style=\"min-height: 30px !important;\r\n    padding: 0 1.25rem !important;\">\r\n      <!-- Tabs -->\r\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-6 fw-bold flex-nowrap\">\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer fs-6\"\r\n            [ngClass]=\"{ active: selectedTab === 'corrections' }\" (click)=\"showTab('corrections', $event)\">\r\n            Corrections / Comments\r\n          </a>\r\n        </li>\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer fs-6\"\r\n            [ngClass]=\"{ active: selectedTab === 'review' }\" (click)=\"showTab('review', $event)\">\r\n            Reviews\r\n          </a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <!-- Card Body with Single Section -->\r\n    <div class=\"card-body p-0\">\r\n      <!-- Internal Tab Content -->\r\n      <ng-container *ngIf=\"selectedTab == 'corrections'\">\r\n        <!-- Corrections Section -->\r\n        <div class=\"corrections-section\" *ngIf=\"review?.corrections && review.corrections.length > 0\">\r\n          <h6 class=\"section-title\">Corrections ({{ review.corrections.length }})</h6>\r\n\r\n          <div class=\"correction-item\" *ngFor=\"let correction of review.corrections; let i = index\">\r\n            <div class=\"correction-header\">\r\n              <span class=\"correction-number\">{{ i + 1 }}</span>\r\n              <span class=\"correction-action\">Correction Type: {{ correction.CorrectionTypeName || 'General' }}</span>\r\n              <span class=\"correction-action\">Category: {{ correction.CorrectionCategoryName || 'General Correction'\r\n                }}</span>\r\n            </div>\r\n\r\n            <div class=\"correction-content\">\r\n              <div class=\"correction-field\">\r\n                <label>Corrective Action</label>\r\n                <div class=\"correction-comment\">{{ correction.CorrectiveAction || '' }}</div>\r\n              </div>\r\n              <div class=\"correction-field\">\r\n                <label>Comment</label>\r\n                <div class=\"correction-comment\">{{ correction.Comments || 'No comment provided' }}</div>\r\n              </div>\r\n\r\n              <div class=\"correction-field\" *ngIf=\"correction.Response\">\r\n                <label>Response</label>\r\n                <div class=\"correction-response\">{{ correction.Response }}</div>\r\n              </div>\r\n\r\n              <div class=\"correction-field\" *ngIf=\"correction.ResolvedDate\">\r\n                <label>Resolved Date</label>\r\n                <div class=\"correction-resolved\">{{ correction.ResolvedDate | date:'MM/dd/yyyy' }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Comments Section (fallback when no corrections) -->\r\n        <div class=\"comments-section\"\r\n          *ngIf=\"(!review?.corrections || review.corrections.length === 0) && review?.comments\">\r\n          <h6 class=\"section-title\">Comments</h6>\r\n          <div class=\"comment-content\">\r\n            <div class=\"comment-text\">{{ review.comments }}</div>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n      <ng-container *ngIf=\"selectedTab == 'review'\">\r\n        <form [formGroup]=\"reviewForm\" class=\"px-6 mb-10\" (ngSubmit)=\"onSubmit()\" novalidate>\r\n          <!-- EOR / AOR / Owner Response -->\r\n          <div class=\"row mt-4\">\r\n            <div class=\"col-xl-12\">\r\n              <label class=\"fw-bold form-label mb-2\">EOR / AOR / Owner Response</label>\r\n              <textarea formControlName=\"EORAOROwner_Response\" rows=\"3\" class=\"form-control form-control-sm\"\r\n                placeholder=\"Enter response\" [disabled]=\"isLoading\"></textarea>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Comment Responded By -->\r\n          <div class=\"row mt-4\">\r\n            <div class=\"col-xl-12\">\r\n              <label class=\"fw-bold form-label mb-2\">Comment Responded By</label>\r\n              <input type=\"text\" formControlName=\"commentResponsedBy\" class=\"form-control form-control-sm\"\r\n                placeholder=\"Who responded to comments\" [disabled]=\"isLoading\" />\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n\r\n\r\n  <!-- No Data Message -->\r\n  <div class=\"no-data-section\" *ngIf=\"(!review?.corrections || review.corrections.length === 0) && !review?.comments\">\r\n    <div class=\"no-data-message\">\r\n      <i class=\"fas fa-info-circle\"></i>\r\n      <span> No corrections or comments available for this review.</span>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"modal-footer\">\r\n  <button type=\"button\" class=\"btn btn-secondary\" (click)=\"modal.close()\">Close</button>\r\n  <button type=\"button\" class=\"btn btn-primary btn-sm btn-elevate me-2\" (click)=\"getPdf()\" [disabled]=\"isLoading\">\r\n    Download PDF\r\n  </button>\r\n  <button type=\"submit\" class=\"btn btn-success btn-sm\" [disabled]=\"isLoading\" (click)=\"onSubmit()\">\r\n    <span *ngIf=\"isLoading\" class=\"spinner-border spinner-border-sm me-2\"></span>\r\n    Update\r\n  </button>\r\n</div>\r\n"], "mappings": "AAIA,SAASA,UAAU,QAAQ,4BAA4B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,iBAAiB;;;;;;;;;;;;ICsEvBC,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAC5DF,EAD4D,CAAAG,YAAA,EAAM,EAC5D;;;;IAD6BH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAC,aAAA,CAAAC,QAAA,CAAyB;;;;;IAI1DP,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAiD;;IACpFF,EADoF,CAAAG,YAAA,EAAM,EACpF;;;;IAD6BH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAQ,WAAA,OAAAF,aAAA,CAAAG,YAAA,gBAAiD;;;;;IAvBpFT,EAFJ,CAAAC,cAAA,cAA0F,cACzD,eACG;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,GAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxGH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,GAC5B;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAIFH,EAFJ,CAAAC,cAAA,cAAgC,cACA,aACrB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChCH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IACzEF,EADyE,CAAAG,YAAA,EAAM,EACzE;IAEJH,EADF,CAAAC,cAAA,eAA8B,aACrB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtBH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,IAAkD;IACpFF,EADoF,CAAAG,YAAA,EAAM,EACpF;IAONH,EALA,CAAAU,UAAA,KAAAC,uEAAA,kBAA0D,KAAAC,uEAAA,kBAKI;IAKlEZ,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IA1B8BH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAQ,IAAA,KAAW;IACXb,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAAc,kBAAA,sBAAAR,aAAA,CAAAS,kBAAA,kBAAiE;IACjEf,EAAA,CAAAI,SAAA,GAC5B;IAD4BJ,EAAA,CAAAc,kBAAA,eAAAR,aAAA,CAAAU,sBAAA,6BAC5B;IAM8BhB,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAC,aAAA,CAAAW,gBAAA,OAAuC;IAIvCjB,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,iBAAA,CAAAC,aAAA,CAAAY,QAAA,0BAAkD;IAGrDlB,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAmB,UAAA,SAAAb,aAAA,CAAAC,QAAA,CAAyB;IAKzBP,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAmB,UAAA,SAAAb,aAAA,CAAAG,YAAA,CAA6B;;;;;IAzBhET,EADF,CAAAC,cAAA,cAA8F,aAClE;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5EH,EAAA,CAAAU,UAAA,IAAAU,gEAAA,mBAA0F;IA6B5FpB,EAAA,CAAAG,YAAA,EAAM;;;;IA/BsBH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAc,kBAAA,kBAAAO,MAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,MAAA,MAA6C;IAEnBxB,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAmB,UAAA,YAAAE,MAAA,CAAAC,MAAA,CAAAC,WAAA,CAAuB;;;;;IAkC3EvB,EAFF,CAAAC,cAAA,cACwF,aAC5D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAErCH,EADF,CAAAC,cAAA,cAA6B,cACD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAEnDF,EAFmD,CAAAG,YAAA,EAAM,EACjD,EACF;;;;IAFwBH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAC,MAAA,CAAAG,QAAA,CAAqB;;;;;IAzCrDzB,EAAA,CAAA0B,uBAAA,GAAmD;IAqCjD1B,EAnCA,CAAAU,UAAA,IAAAiB,0DAAA,kBAA8F,IAAAC,0DAAA,kBAoCN;;;;;IApCtD5B,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAmB,UAAA,UAAAE,MAAA,CAAAC,MAAA,kBAAAD,MAAA,CAAAC,MAAA,CAAAC,WAAA,KAAAF,MAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,MAAA,KAA0D;IAoCzFxB,EAAA,CAAAI,SAAA,EAAmF;IAAnFJ,EAAA,CAAAmB,UAAA,YAAAE,MAAA,CAAAC,MAAA,kBAAAD,MAAA,CAAAC,MAAA,CAAAC,WAAA,KAAAF,MAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,MAAA,YAAAH,MAAA,CAAAC,MAAA,kBAAAD,MAAA,CAAAC,MAAA,CAAAG,QAAA,EAAmF;;;;;;IAOxFzB,EAAA,CAAA0B,uBAAA,GAA8C;IAC5C1B,EAAA,CAAAC,cAAA,eAAqF;IAAnCD,EAAA,CAAA6B,UAAA,sBAAAC,8EAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAArB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAYb,MAAA,CAAAc,QAAA,EAAU;IAAA,EAAC;IAInEnC,EAFJ,CAAAC,cAAA,cAAsB,cACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzEH,EAAA,CAAAoC,SAAA,mBACiE;IAErEpC,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAoC,SAAA,iBACmE;IAGzEpC,EAFI,CAAAG,YAAA,EAAM,EACF,EACD;;;;;IAlBDH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAmB,UAAA,cAAAE,MAAA,CAAAgB,UAAA,CAAwB;IAMOrC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAmB,UAAA,aAAAE,MAAA,CAAAiB,SAAA,CAAsB;IASXtC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAmB,UAAA,aAAAE,MAAA,CAAAiB,SAAA,CAAsB;;;;;IAW1EtC,EADF,CAAAC,cAAA,cAAoH,cACrF;IAC3BD,EAAA,CAAAoC,SAAA,YAAkC;IAClCpC,EAAA,CAAAC,cAAA,WAAM;IAACD,EAAA,CAAAE,MAAA,6DAAqD;IAEhEF,EAFgE,CAAAG,YAAA,EAAO,EAC/D,EACF;;;;;IASJH,EAAA,CAAAoC,SAAA,eAA6E;;;AD5HjF,OAAM,MAAOG,2BAA2B;EAQnBC,KAAA;EAA8BC,wBAAA;EAA0DC,UAAA;EAAgCC,EAAA;EAAyBC,GAAA;EAA+BC,cAAA;EAP1LvB,MAAM;EACNwB,QAAQ,GAAkB,IAAI;EAC9BC,aAAa,GAAQ,EAAE;EAChCC,WAAW,GAAM,aAAa;EAC9BX,UAAU;EACVC,SAAS,GAAY,KAAK;EAC1BW,SAAS;EACTC,YAAmBV,KAAqB,EAASC,wBAAkD,EAAQC,UAAsB,EAAUC,EAAe,EAAUC,GAAqB,EAAUC,cAA8B;IAA9M,KAAAL,KAAK,GAALA,KAAK;IAAyB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAAkC,KAAAC,UAAU,GAAVA,UAAU;IAAsB,KAAAC,EAAE,GAAFA,EAAE;IAAuB,KAAAC,GAAG,GAAHA,GAAG;IAA4B,KAAAC,cAAc,GAAdA,cAAc;EAAmB;EAEpO;EACAM,aAAaA,CAAA;IACX,IAAI,CAACX,KAAK,CAACY,OAAO,EAAE;EACtB;EACAC,QAAQA,CAAA;IACN,IAAI,CAACJ,SAAS,GAAE,IAAI,CAACP,UAAU,CAACY,eAAe,EAAE;IACjD,IAAI,CAACjB,UAAU,GAAG,IAAI,CAACM,EAAE,CAACY,KAAK,CAAC;MAC9B;MACAC,oBAAoB,EAAE,CAAC,IAAI,CAAClC,MAAM,EAAEkC,oBAAoB,IAAI,EAAE,CAAC;MAC/DC,kBAAkB,EAAE,CAAC,IAAI,CAACnC,MAAM,EAAEmC,kBAAkB,IAAI,EAAE;KAC3D,CAAC;EACJ;EACAC,OAAOA,CAACC,GAAQ,EAAEC,MAAW;IAC3B,IAAI,CAACZ,WAAW,GAAGW,GAAG;IACtB,IAAI,CAACf,GAAG,CAACiB,YAAY,EAAE;EACzB;EAEA1B,QAAQA,CAAA;IACN,IAAI,CAACG,SAAS,GAAG,IAAI;IACrB,MAAMwB,QAAQ,GAAG;MACf;MACAN,oBAAoB,EAAC,IAAI,CAACnB,UAAU,CAAC0B,QAAQ,CAACP,oBAAoB,CAACQ,KAAK;MACxEP,kBAAkB,EAAC,IAAI,CAACpB,UAAU,CAAC0B,QAAQ,CAACN,kBAAkB,CAACO,KAAK;MACpElB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBmB,UAAU,EAAC,IAAI,CAAC3C,MAAM,CAAC2C,UAAU;MACjCC,cAAc,EAAE,IAAI,CAACjB,SAAS,CAACkB;KAChC;IACC,IAAI,CAACtB,cAAc,CAACuB,oBAAoB,CAACN,QAAQ,CAAC,CAACO,SAAS,CAAC;MAC3DC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACjC,SAAS,GAAG,KAAK;QACtB,IAAIiC,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UAC1B;UACwB,IAAI,CAAC/B,wBAAwB,CAACgC,WAAW,CAAC,+BAA+B,EAAE,EAAE,CAAC;UAEtG,IAAI,CAACjC,KAAK,CAACkC,KAAK,CAAC,SAAS,CAAC;QAE7B,CAAC,MAAM;UACK,IAAI,CAACjC,wBAAwB,CAACkC,SAAS,CAACJ,GAAG,CAACK,YAAY,CAACC,OAAO,IAAG,kCAAkC,EAAE,EAAE,CAAC;UAEpH;QACF;MACF,CAAC;MACDC,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAACzC,SAAS,GAAG,KAAK;QACZ,IAAI,CAACG,wBAAwB,CAACkC,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;QAEvF;QACAK,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EAGR;EACAE,MAAMA,CAAA;IACJ,MAAMC,YAAY,GAAG,IAAI,CAACnC,aAAa,EAAEmC,YAAY,IAAI,EAAE;IAC3D,MAAMC,WAAW,GAAG,IAAI,CAACpC,aAAa,EAAEoC,WAAW,IAAI,EAAE;IACzD,MAAMC,aAAa,GAAG,IAAI,CAACrC,aAAa,EAAEqC,aAAa,IAAI,EAAE;IAC7D,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAAC/D,MAAM,EAAEgE,UAAU,IAAI,IAAI,CAAChE,MAAM,EAAEiE,oBAAoB,IAAI,EAAE,EAAEC,QAAQ,EAAE;IAChG,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACnE,MAAM,EAAEJ,QAAQ,KAAK,IAAI,CAACI,MAAM,EAAEmE,YAAY,IAAI,EAAE,CAAC,EAAED,QAAQ,EAAE;IAC5F,MAAME,aAAa,GAAG,CAAC,IAAI,CAACrD,UAAU,EAAE2B,KAAK,EAAER,oBAAoB,IAAI,IAAI,CAAClC,MAAM,EAAEkC,oBAAoB,IAAI,EAAE,EAAEgC,QAAQ,EAAE;IAC1H,MAAMG,WAAW,GAAG,CAAC,IAAI,CAACtD,UAAU,EAAE2B,KAAK,EAAEP,kBAAkB,IAAI,IAAI,CAACnC,MAAM,EAAEmC,kBAAkB,IAAI,EAAE,EAAE+B,QAAQ,EAAE;IACpH,MAAMI,KAAK,GAAG,CAAC,IAAI,CAACtE,MAAM,EAAEsE,KAAK,IAAI,IAAI,CAACtE,MAAM,EAAEuE,KAAK,IAAI,EAAE,EAAEL,QAAQ,EAAE;IACzE,MAAMM,MAAM,GAAG,CAAC,IAAI,CAACxE,MAAM,EAAEyE,UAAU,IAAI,IAAI,CAACzE,MAAM,EAAE0E,aAAa,IAAI,IAAI,CAAC1E,MAAM,EAAEwE,MAAM,IAAI,EAAE,EAAEN,QAAQ,EAAE;IAC9G,MAAMS,cAAc,GAAG,CAAC,IAAI,CAAC3E,MAAM,EAAE4E,QAAQ,IAAI,IAAI,CAAC5E,MAAM,EAAE2E,cAAc,IAAI,EAAE,EAAET,QAAQ,EAAE;IAC9F,MAAMW,OAAO,GAAG,IAAI,CAAC7E,MAAM,EAAE8E,OAAO,GAAGvG,UAAU,CAACwG,UAAU,CAAC,IAAI,CAAC/E,MAAM,CAAC8E,OAAO,CAAC,GAAG,EAAE;IACtF,MAAME,aAAa,GAAG,IAAI,CAAChF,MAAM,EAAEiF,aAAa,GAAG1G,UAAU,CAACwG,UAAU,CAAC,IAAI,CAAC/E,MAAM,CAACiF,aAAa,CAAC,GAAG,EAAE;IACxG,MAAMC,WAAW,GAAG,IAAI,CAAClF,MAAM,EAAEkF,WAAW,GAAG3G,UAAU,CAACwG,UAAU,CAAC,IAAI,CAAC/E,MAAM,CAACkF,WAAW,CAAC,GAAG,EAAE;IAElG,MAAMC,GAAG,GAAG,IAAI3G,KAAK,CAAC;MAAE4G,WAAW,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAE,CAAC;IAE5E;IACA,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;IAClD,MAAMC,UAAU,GAAGR,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACG,SAAS,EAAE;IACpD,MAAMC,MAAM,GAAG;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,GAAG,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAE,CAAE;IAE3D,IAAIC,QAAQ,GAAGL,MAAM,CAACG,GAAG;IAEzB;IACA,MAAMG,UAAU,GAAGA,CAAA,KAAK;MACtB;MACAhB,GAAG,CAACiB,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;MAChCjB,GAAG,CAACkB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAEd,SAAS,EAAE,EAAE,EAAE,GAAG,CAAC;MAElCJ,GAAG,CAACmB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BnB,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,uBAAuB,EAAEZ,MAAM,CAACC,IAAI,EAAE,EAAE,CAAC;MAElDX,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,eAAe,EAAElB,SAAS,GAAGM,MAAM,CAACE,KAAK,GAAG,EAAE,EAAE,EAAE,CAAC;MAE5D;MACAZ,GAAG,CAACmB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAEzB;MACAJ,QAAQ,GAAG,EAAE;MACbf,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,gBAAgB,EAAEZ,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MAEjDA,QAAQ,IAAI,EAAE;IAChB,CAAC;IAEDC,UAAU,EAAE;IAEZ;IACA,MAAMO,cAAc,GAAGA,CAAA,KAAK;MAC1B,MAAMC,QAAQ,GAAG,CACf,CAAC,WAAW,EAAE/C,YAAY,IAAI,EAAE,CAAC,EACjC,CAAC,eAAe,EAAEC,WAAW,IAAI,EAAE,CAAC,EACpC,CAAC,iBAAiB,EAAEC,aAAa,IAAI,EAAE,CAAC,EACxC,CAAC,kBAAkB,EAAEa,cAAc,IAAI,EAAE,CAAC,EAC1C,CAAC,WAAW,EAAEZ,QAAQ,IAAI,EAAE,CAAC,EAC7B,CAAC,SAAS,EAAES,MAAM,IAAI,EAAE,CAAC,EACzB,CAAC,WAAW,EAAEK,OAAO,IAAI,EAAE,CAAC,EAC5B,CAAC,iBAAiB,EAAEG,aAAa,IAAI,EAAE,CAAC,EACxC,CAAC,eAAe,EAAEE,WAAW,IAAI,EAAE,CAAC,CACrC;MAEDzG,SAAS,CAAC0G,GAAG,EAAE;QACbyB,MAAM,EAAEV,QAAQ;QAChBW,IAAI,EAAEF,QAAQ;QACdd,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDe,MAAM,EAAE;UACNC,IAAI,EAAE,WAAW;UACjBC,QAAQ,EAAE,EAAE;UACZC,WAAW,EAAE,CAAC;UACdC,QAAQ,EAAE;SACX;QACDC,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEC,SAAS,EAAE,GAAG;YAAEC,SAAS,EAAE,MAAM;YAAEC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UAAC,CAAE;UACpE,CAAC,EAAE;YAAEF,SAAS,EAAE;UAAG;SACpB;QACDG,KAAK,EAAE;OACR,CAAC;MAEFrB,QAAQ,GAAIf,GAAW,CAACqC,aAAa,CAACC,MAAM,GAAG,EAAE;IACnD,CAAC;IAEDf,cAAc,EAAE;IAEhB;IACA,IAAIvC,YAAY,EAAE;MAChBgB,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACmB,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7BnB,GAAG,CAACsB,IAAI,CAAC,eAAe,EAAEZ,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MAChDA,QAAQ,IAAI,EAAE;MAEdf,GAAG,CAACmB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzBnB,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MAEnB;MACA,MAAMkB,SAAS,GAAGvC,GAAG,CAACwC,eAAe,CAACxD,YAAY,EAAEoB,SAAS,GAAGM,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,CAAC;MAC3FZ,GAAG,CAACsB,IAAI,CAACiB,SAAS,EAAE7B,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MAC1CA,QAAQ,IAAIwB,SAAS,CAACxH,MAAM,GAAG,EAAE,GAAG,EAAE;IACxC;IAEA;IACA,IAAIkE,aAAa,EAAE;MACjBe,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,wBAAwB,EAAEZ,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MACzDA,QAAQ,IAAI,EAAE;MAEdf,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MAEnB,MAAMkB,SAAS,GAAGvC,GAAG,CAACwC,eAAe,CAACvD,aAAa,EAAEmB,SAAS,GAAGM,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,CAAC;MAC5FZ,GAAG,CAACsB,IAAI,CAACiB,SAAS,EAAE7B,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MAC1CA,QAAQ,IAAIwB,SAAS,CAACxH,MAAM,GAAG,EAAE,GAAG,EAAE;MAEtC;MACA,IAAImE,WAAW,EAAE;QACfc,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;QACnBrB,GAAG,CAACsB,IAAI,CAAC,iBAAiBpC,WAAW,EAAE,EAAEwB,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;QAC/DA,QAAQ,IAAI,EAAE;MAChB;IACF;IAEA;IACA,IAAI,IAAI,CAAClG,MAAM,EAAE4H,WAAW,IAAI,IAAI,CAAC5H,MAAM,CAAC4H,WAAW,CAAC1H,MAAM,GAAG,CAAC,EAAE;MAClEiF,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;MACnBrB,GAAG,CAACsB,IAAI,CAAC,uBAAuB,EAAEZ,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;MACxDA,QAAQ,IAAI,EAAE;MAEd,IAAI,CAAClG,MAAM,CAAC4H,WAAW,CAACC,OAAO,CAAC,CAACC,UAAe,EAAEC,KAAa,KAAI;QACjE;QACA,IAAI7B,QAAQ,GAAGP,UAAU,GAAG,GAAG,EAAE;UAC/BR,GAAG,CAAC6C,OAAO,EAAE;UACb7B,UAAU,EAAE;UACZD,QAAQ,GAAGL,MAAM,CAACG,GAAG,GAAG,EAAE;QAC5B;QAEA;QACAb,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCpB,GAAG,CAACqB,WAAW,CAAC,EAAE,CAAC;QACnBrB,GAAG,CAACsB,IAAI,CAAC,cAAcsB,KAAK,GAAG,CAAC,EAAE,EAAElC,MAAM,CAACC,IAAI,EAAEI,QAAQ,CAAC;QAC1DA,QAAQ,IAAI,EAAE;QAEd;QACA,MAAM+B,cAAc,GAAG,CACrB,CAAC,gBAAgB,EAAEH,UAAU,CAACI,YAAY,IAAI,EAAE,CAAC,EACjD,CAAC,OAAO,EAAEJ,UAAU,CAACrI,kBAAkB,IAAI,EAAE,CAAC,EAC9C,CAAC,WAAW,EAAEqI,UAAU,CAACpI,sBAAsB,IAAI,EAAE,CAAC,EACtD,CAAC,WAAW,EAAEoI,UAAU,CAAClI,QAAQ,IAAI,EAAE,CAAC,EACxC,CAAC,oBAAoB,EAAEkI,UAAU,CAACnI,gBAAgB,IAAI,EAAE,CAAC,EACzD,CAAC,WAAW,EAAEmI,UAAU,CAAC7I,QAAQ,IAAI,EAAE,CAAC,EACxC,CAAC,iBAAiB,EAAE6I,UAAU,CAAC5F,oBAAoB,IAAI,EAAE,CAAC,EAC1D,CAAC,eAAe,EAAE4F,UAAU,CAAC3F,kBAAkB,IAAI,EAAE,CAAC,EACtD,CAAC,gBAAgB,EAAE2F,UAAU,CAAC3I,YAAY,GAAGZ,UAAU,CAACwG,UAAU,CAAC+C,UAAU,CAAC3I,YAAY,CAAC,GAAG,EAAE,CAAC,EACjG,CAAC,cAAc,EAAE2I,UAAU,CAACK,cAAc,IAAI,EAAE,CAAC,CAClD;QAED1J,SAAS,CAAC0G,GAAG,EAAE;UACbyB,MAAM,EAAEV,QAAQ;UAChBW,IAAI,EAAEoB,cAAc;UACpBpC,MAAM,EAAE;YAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;YAAEC,KAAK,EAAEF,MAAM,CAACE;UAAK,CAAE;UAClDe,MAAM,EAAE;YACNC,IAAI,EAAE,WAAW;YACjBC,QAAQ,EAAE,CAAC;YACXC,WAAW,EAAE,CAAC;YACdC,QAAQ,EAAE;WACX;UACDC,YAAY,EAAE;YACZ,CAAC,EAAE;cAAEC,SAAS,EAAE,GAAG;cAAEC,SAAS,EAAE,MAAM;cAAEC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;YAAC,CAAE;YACpE,CAAC,EAAE;cAAEF,SAAS,EAAE;YAAG;WACpB;UACDG,KAAK,EAAE;SACR,CAAC;QAEFrB,QAAQ,GAAIf,GAAW,CAACqC,aAAa,CAACC,MAAM,GAAG,EAAE;MACnD,CAAC,CAAC;IACJ;IAEA;IACA,MAAMW,UAAU,GAAGA,CAAA,KAAK;MACtB,MAAMC,SAAS,GAAGlD,GAAG,CAACmD,gBAAgB,EAAE;MACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,SAAS,EAAEE,CAAC,EAAE,EAAE;QACnCpD,GAAG,CAACqD,OAAO,CAACD,CAAC,CAAC;QAEd;QACApD,GAAG,CAACsD,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/BtD,GAAG,CAACuD,IAAI,CAAC7C,MAAM,CAACC,IAAI,EAAEH,UAAU,GAAG,EAAE,EAAEJ,SAAS,GAAGM,MAAM,CAACE,KAAK,EAAEJ,UAAU,GAAG,EAAE,CAAC;QAEjF;QACAR,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCpB,GAAG,CAACqB,WAAW,CAAC,CAAC,CAAC;QAClBrB,GAAG,CAACmB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/BnB,GAAG,CAACsB,IAAI,CAAC,iBAAiB,IAAIkC,IAAI,EAAE,CAACC,cAAc,EAAE,EAAE,EAAE/C,MAAM,CAACC,IAAI,EAAEH,UAAU,GAAG,EAAE,CAAC;QACtFR,GAAG,CAACsB,IAAI,CAAC,QAAQ8B,CAAC,OAAOF,SAAS,EAAE,EAAE9C,SAAS,GAAGM,MAAM,CAACE,KAAK,GAAG,EAAE,EAAEJ,UAAU,GAAG,EAAE,CAAC;MACvF;IACF,CAAC;IAEDyC,UAAU,EAAE;IAEZ;IACA,MAAMS,QAAQ,GAAG,UAAUjF,YAAY,GAAGA,YAAY,GAAG,GAAG,GAAG,EAAE,GAAGe,cAAc,GAAGA,cAAc,CAACmE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,IAAIH,IAAI,EAAE,CAACI,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC3L7D,GAAG,CAAC8D,IAAI,CAACJ,QAAQ,CAAC;EACpB;;qCAhRa5H,2BAA2B,EAAAvC,EAAA,CAAAwK,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1K,EAAA,CAAAwK,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAA5K,EAAA,CAAAwK,iBAAA,CAAAK,EAAA,CAAAhL,UAAA,GAAAG,EAAA,CAAAwK,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA/K,EAAA,CAAAwK,iBAAA,CAAAxK,EAAA,CAAAgL,iBAAA,GAAAhL,EAAA,CAAAwK,iBAAA,CAAAS,EAAA,CAAAC,cAAA;EAAA;;UAA3B3I,2BAA2B;IAAA4I,SAAA;IAAAC,MAAA;MAAA9J,MAAA;MAAAwB,QAAA;MAAAC,aAAA;IAAA;IAAAsI,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZtC1L,EADF,CAAAC,cAAA,aAA0B,YACA;QAAAD,EAAA,CAAAE,MAAA,GAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC7CH,EAAA,CAAAC,cAAA,gBAAqF;QAA1BD,EAAA,CAAA6B,UAAA,mBAAA+J,6DAAA;UAAA,OAASD,GAAA,CAAAnJ,KAAA,CAAAY,OAAA,EAAe;QAAA,EAAC;QACtFpD,EADuF,CAAAG,YAAA,EAAS,EAC1F;QAQEH,EANR,CAAAC,cAAA,aAAyC,aAGZ,aACH,aACG,YACd;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtBH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAsB;QACjDF,EADiD,CAAAG,YAAA,EAAO,EAClD;QAEJH,EADF,CAAAC,cAAA,cAAuB,aACd;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrBH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAoB;QAC/CF,EAD+C,CAAAG,YAAA,EAAO,EAChD;QAEJH,EADF,CAAAC,cAAA,cAAuB,aACd;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACvBH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAyC;;QACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;QAEJH,EADF,CAAAC,cAAA,cAAuB,aACd;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7BH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAA+C;;QAG9EF,EAH8E,CAAAG,YAAA,EAAO,EAC3E,EACF,EACF;QASEH,EAPR,CAAAC,cAAA,cAAsC,cAGJ,cAEyE,cAChF,aAE8E;QAAzCD,EAAA,CAAA6B,UAAA,mBAAAgK,yDAAAjI,MAAA;UAAA,OAAS+H,GAAA,CAAAjI,OAAA,CAAQ,aAAa,EAAAE,MAAA,CAAS;QAAA,EAAC;QAC9F5D,EAAA,CAAAE,MAAA,gCACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAEoE;QAApCD,EAAA,CAAA6B,UAAA,mBAAAiK,yDAAAlI,MAAA;UAAA,OAAS+H,GAAA,CAAAjI,OAAA,CAAQ,QAAQ,EAAAE,MAAA,CAAS;QAAA,EAAC;QACpF5D,EAAA,CAAAE,MAAA,iBACF;QAGNF,EAHM,CAAAG,YAAA,EAAI,EACD,EACF,EACD;QAENH,EAAA,CAAAC,cAAA,eAA2B;QA+CzBD,EA7CA,CAAAU,UAAA,KAAAqL,oDAAA,2BAAmD,KAAAC,oDAAA,4BA6CL;QAsBlDhM,EADE,CAAAG,YAAA,EAAM,EACF;QAINH,EAAA,CAAAU,UAAA,KAAAuL,2CAAA,kBAAoH;QAMtHjM,EAAA,CAAAG,YAAA,EAAM;QAGJH,EADF,CAAAC,cAAA,eAA0B,kBACgD;QAAxBD,EAAA,CAAA6B,UAAA,mBAAAqK,8DAAA;UAAA,OAASP,GAAA,CAAAnJ,KAAA,CAAAkC,KAAA,EAAa;QAAA,EAAC;QAAC1E,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACtFH,EAAA,CAAAC,cAAA,kBAAgH;QAA1CD,EAAA,CAAA6B,UAAA,mBAAAsK,8DAAA;UAAA,OAASR,GAAA,CAAA1G,MAAA,EAAQ;QAAA,EAAC;QACtFjF,EAAA,CAAAE,MAAA,sBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAiG;QAArBD,EAAA,CAAA6B,UAAA,mBAAAuK,8DAAA;UAAA,OAAST,GAAA,CAAAxJ,QAAA,EAAU;QAAA,EAAC;QAC9FnC,EAAA,CAAAU,UAAA,KAAA2L,4CAAA,mBAAsE;QACtErM,EAAA,CAAAE,MAAA,gBACF;QACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;QA3IoBH,EAAA,CAAAI,SAAA,GAAgB;QAAhBJ,EAAA,CAAAK,iBAAA,CAAAsL,GAAA,CAAArK,MAAA,kBAAAqK,GAAA,CAAArK,MAAA,CAAAgL,IAAA,CAAgB;QAWTtM,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAK,iBAAA,CAAAsL,GAAA,CAAArK,MAAA,kBAAAqK,GAAA,CAAArK,MAAA,CAAA+D,QAAA,CAAsB;QAItBrF,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAK,iBAAA,CAAAsL,GAAA,CAAArK,MAAA,kBAAAqK,GAAA,CAAArK,MAAA,CAAAwE,MAAA,CAAoB;QAIpB9F,EAAA,CAAAI,SAAA,GAAyC;QAAzCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAQ,WAAA,SAAAmL,GAAA,CAAArK,MAAA,kBAAAqK,GAAA,CAAArK,MAAA,CAAA6E,OAAA,gBAAyC;QAIzCnG,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAQ,WAAA,SAAAmL,GAAA,CAAArK,MAAA,kBAAAqK,GAAA,CAAArK,MAAA,CAAAgF,aAAA,gBAA+C;QAapEtG,EAAA,CAAAI,SAAA,GAAqD;QAArDJ,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAuM,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAA3I,WAAA,oBAAqD;QAMrDhD,EAAA,CAAAI,SAAA,GAAgD;QAAhDJ,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAuM,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAA3I,WAAA,eAAgD;QASvChD,EAAA,CAAAI,SAAA,GAAkC;QAAlCJ,EAAA,CAAAmB,UAAA,SAAAwK,GAAA,CAAA3I,WAAA,kBAAkC;QA6ClChD,EAAA,CAAAI,SAAA,EAA6B;QAA7BJ,EAAA,CAAAmB,UAAA,SAAAwK,GAAA,CAAA3I,WAAA,aAA6B;QA0BlBhD,EAAA,CAAAI,SAAA,EAAoF;QAApFJ,EAAA,CAAAmB,UAAA,YAAAwK,GAAA,CAAArK,MAAA,kBAAAqK,GAAA,CAAArK,MAAA,CAAAC,WAAA,KAAAoK,GAAA,CAAArK,MAAA,CAAAC,WAAA,CAAAC,MAAA,aAAAmK,GAAA,CAAArK,MAAA,kBAAAqK,GAAA,CAAArK,MAAA,CAAAG,QAAA,EAAoF;QAUzBzB,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAmB,UAAA,aAAAwK,GAAA,CAAArJ,SAAA,CAAsB;QAG1DtC,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAmB,UAAA,aAAAwK,GAAA,CAAArJ,SAAA,CAAsB;QAClEtC,EAAA,CAAAI,SAAA,EAAe;QAAfJ,EAAA,CAAAmB,UAAA,SAAAwK,GAAA,CAAArJ,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}