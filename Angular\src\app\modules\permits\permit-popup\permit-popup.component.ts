import { Component, Input, Output, EventEmitter, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { ProjectsService } from '../../services/projects.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';

@Component({
  selector: 'app-permit-popup',
  templateUrl: './permit-popup.component.html',
  styleUrls: ['./permit-popup.component.scss']
})
export class PermitPopupComponent implements OnInit {
  @Input() id: number = 0; // 0 for new permit, existing ID for edit
  @Input() permit: any = null; // Permit data for editing
  @Output() passEntry = new EventEmitter<boolean>();

  permitForm!: FormGroup;
  selectedTab: string = 'basic';
  isLoading: boolean = false;
  permitNumber: string = '';
  permitNumberError: string = '';
  isCheckingPermitNumber: boolean = false;

  // Data arrays
  projects: any[] = [];
  municipalities: any[] = [];
  permitTypes: any[] = [];
  permitReviewTypes: any[] = [];
  statusList: any[] = [];

  // Subject for permit number validation debouncing
  private permitNumberSubject = new Subject<string>();

  constructor(
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private permitsService: PermitsService,
    private projectsService: ProjectsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadDropdownData();
    this.setupPermitNumberValidation();

    if (this.id !== 0 && this.permit) {
      this.populateFormForEdit();
      this.permitNumber = this.permit.permitNumber || '';
    }
  }

  private initializeForm(): void {
    this.permitForm = this.fb.group({
      projectId: ['', Validators.required],
      permitName: ['', Validators.required],
      permitNumber: ['', Validators.required],
      municipalityId: [''],
      permitType: [''],
      permitReviewType: [''],
      status: [''],
      permitEntityID: [''],
      cityReviewLink: [''],
      description: [''],
      attentionReason: [''],
      internalNotes: [''],
      actionTaken: ['']
    });
  }

  private setupPermitNumberValidation(): void {
    // Set up debounced permit number validation
    this.permitNumberSubject.pipe(
      debounceTime(500),
      distinctUntilChanged()
    ).subscribe(permitNumber => {
      if (permitNumber && permitNumber.trim()) {
        this.checkPermitNumberAvailability(permitNumber.trim());
      } else {
        this.permitNumberError = '';
        this.isCheckingPermitNumber = false;
      }
    });

    // Listen to permit number changes
    this.permitForm.get('permitNumber')?.valueChanges.subscribe(value => {
      if (value !== this.permitNumber) { // Don't validate if it's the same as original
        this.permitNumberSubject.next(value);
      }
    });
  }

  private checkPermitNumberAvailability(permitNumber: string): void {
    this.isCheckingPermitNumber = true;
    this.permitNumberError = '';

    this.permitsService.checkPermitNumberExists(permitNumber, this.id !== 0 ? this.id : undefined).subscribe({
      next: (res: any) => {
        this.isCheckingPermitNumber = false;
        if (res?.responseData?.exists) {
          this.permitNumberError = 'This permit number already exists';
        } else {
          this.permitNumberError = '';
        }
        this.cdr.markForCheck();
      },
      error: () => {
        this.isCheckingPermitNumber = false;
        this.permitNumberError = '';
        this.cdr.markForCheck();
      }
    });
  }

  private loadDropdownData(): void {
    this.loadProjects();
    this.loadMunicipalities();
    this.loadPermitTypes();
    this.loadPermitReviewTypes();
    this.loadStatusList();
  }

  private loadProjects(): void {
    this.projectsService.getAllProjects({ take: 1000, skip: 0 }).subscribe({
      next: (res: any) => {
        if (!res?.isFault) {
          this.projects = res.responseData?.data || [];
        }
      },
      error: (err) => console.error('Error loading projects:', err)
    });
  }

  private loadMunicipalities(): void {
    // Load municipalities - adjust service call as needed
    this.municipalities = [
      { municipalityId: 1, municipalityName: 'Default Municipality' }
    ];
  }

  private loadPermitTypes(): void {
    this.permitTypes = [
      { value: 'Building', text: 'Building' },
      { value: 'Electrical', text: 'Electrical' },
      { value: 'Mechanical', text: 'Mechanical' },
      { value: 'Plumbing', text: 'Plumbing' },
      { value: 'Fire', text: 'Fire' },
      { value: 'Other', text: 'Other' }
    ];
  }

  private loadPermitReviewTypes(): void {
    this.permitReviewTypes = [
      { value: 'Internal', text: 'Internal' },
      { value: 'External', text: 'External' },
      { value: 'Both', text: 'Both' }
    ];
  }

  private loadStatusList(): void {
    this.statusList = [
      { text: 'Pending', value: 'Pending' },
      { text: 'In Progress', value: 'In Progress' },
      { text: 'Completed', value: 'Completed' },
      { text: 'On Hold', value: 'On Hold' }
    ];
  }

  private populateFormForEdit(): void {
    if (this.permit) {
      this.permitForm.patchValue({
        projectId: this.permit.projectId,
        permitName: this.permit.permitName,
        permitNumber: this.permit.permitNumber,
        municipalityId: this.permit.municipalityId,
        permitType: this.permit.permitType,
        permitReviewType: this.permit.permitReviewType,
        status: this.permit.status,
        permitEntityID: this.permit.permitEntityID,
        cityReviewLink: this.permit.cityReviewLink,
        description: this.permit.description,
        attentionReason: this.permit.attentionReason,
        internalNotes: this.permit.internalNotes,
        actionTaken: this.permit.actionTaken
      });
    }
  }

  showTab(tab: string, event?: any): void {
    if (event) {
      event.preventDefault();
    }
    this.selectedTab = tab;
  }

  goToNextTab(): void {
    if (this.selectedTab === 'basic') {
      this.selectedTab = 'details';
    }
  }

  goToPreviousTab(): void {
    if (this.selectedTab === 'details') {
      this.selectedTab = 'basic';
    }
  }

  onProjectChange(projectId: any): void {
    // Handle project change if needed
    console.log('Project changed:', projectId);
  }

  save(): void {
    if (this.permitForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    if (this.permitNumberError) {
      this.customLayoutUtilsService.showError('Please fix the permit number error before saving', '');
      return;
    }

    this.isLoading = true;
    const formData = this.permitForm.value;

    const saveObservable = this.id === 0
      ? this.permitsService.createPermit(formData)
      : this.permitsService.updatePermit({ ...formData, permitId: this.id });

    saveObservable.subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (!res?.isFault) {
          const message = this.id === 0 ? 'Permit created successfully' : 'Permit updated successfully';
          this.customLayoutUtilsService.showSuccess(message, '');
          this.passEntry.emit(true);
          this.modal.close('saved');
        } else {
          this.customLayoutUtilsService.showError(res.faultMessage || 'Error saving permit', '');
        }
        this.cdr.markForCheck();
      },
      error: (err) => {
        this.isLoading = false;
        this.customLayoutUtilsService.showError('Error saving permit', '');
        console.error('Save error:', err);
        this.cdr.markForCheck();
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.permitForm.controls).forEach(key => {
      const control = this.permitForm.get(key);
      control?.markAsTouched();
    });
  }
}