{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { RouterModule } from '@angular/router';\nimport { NgbDropdownModule, NgbProgressbarModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TranslationModule } from '../../modules/i18n';\nimport { LayoutComponent } from './layout.component';\nimport { ExtrasModule } from '../partials/layout/extras/extras.module';\nimport { Routing } from '../../pages/routing';\nimport { HeaderComponent } from './components/header/header.component';\nimport { ContentComponent } from './components/content/content.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport { ScriptsInitComponent } from './components/scripts-init/scripts-init.component';\nimport { ToolbarComponent } from './components/toolbar/toolbar.component';\nimport { TopbarComponent } from './components/topbar/topbar.component';\nimport { PageTitleComponent } from './components/header/page-title/page-title.component';\nimport { HeaderMenuComponent } from './components/header/header-menu/header-menu.component';\nimport { DrawersModule, DropdownMenusModule, ModalsModule, EngagesModule } from '../partials';\nimport { EngagesComponent } from '../partials/layout/engages/engages.component';\nimport { ThemeModeModule } from '../partials/layout/theme-mode-switcher/theme-mode.module';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport { SidebarLogoComponent } from './components/sidebar/sidebar-logo/sidebar-logo.component';\nimport { SidebarMenuComponent } from './components/sidebar/sidebar-menu/sidebar-menu.component';\nimport { SidebarFooterComponent } from './components/sidebar/sidebar-footer/sidebar-footer.component';\nimport { NavbarComponent } from './components/header/navbar/navbar.component';\nimport { AccountingComponent } from './components/toolbar/accounting/accounting.component';\nimport { ClassicComponent } from './components/toolbar/classic/classic.component';\nimport { ExtendedComponent } from './components/toolbar/extended/extended.component';\nimport { ReportsComponent } from './components/toolbar/reports/reports.component';\nimport { SaasComponent } from './components/toolbar/saas/saas.component';\nimport { SharedModule } from \"../shared/shared.module\";\nconst routes = [{\n  path: '',\n  component: LayoutComponent,\n  children: Routing\n}];\nlet LayoutModule = class LayoutModule {};\nLayoutModule = __decorate([NgModule({\n  declarations: [LayoutComponent, HeaderComponent, ContentComponent, FooterComponent, ScriptsInitComponent, ToolbarComponent, TopbarComponent, PageTitleComponent, HeaderMenuComponent, EngagesComponent, SidebarComponent, SidebarLogoComponent, SidebarMenuComponent, SidebarFooterComponent, NavbarComponent, AccountingComponent, ClassicComponent, ExtendedComponent, ReportsComponent, SaasComponent],\n  imports: [CommonModule, RouterModule.forChild(routes), TranslationModule, InlineSVGModule, NgbDropdownModule, NgbProgressbarModule, ExtrasModule, ModalsModule, DrawersModule, EngagesModule, DropdownMenusModule, NgbTooltipModule, TranslateModule, ThemeModeModule, SharedModule],\n  exports: [RouterModule]\n})], LayoutModule);\nexport { LayoutModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "InlineSVGModule", "RouterModule", "NgbDropdownModule", "NgbProgressbarModule", "NgbTooltipModule", "TranslateModule", "TranslationModule", "LayoutComponent", "ExtrasModule", "Routing", "HeaderComponent", "ContentComponent", "FooterComponent", "ScriptsInitComponent", "ToolbarComponent", "TopbarComponent", "PageTitleComponent", "HeaderMenuComponent", "DrawersModule", "DropdownMenusModule", "ModalsModule", "EngagesModule", "EngagesComponent", "ThemeModeModule", "SidebarComponent", "SidebarLogoComponent", "SidebarMenuComponent", "SidebarFooterComponent", "NavbarComponent", "AccountingComponent", "ClassicComponent", "ExtendedComponent", "ReportsComponent", "SaasComponent", "SharedModule", "routes", "path", "component", "children", "LayoutModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["D:\\permittracker\\Angular\\src\\app\\_metronic\\layout\\layout.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { RouterModule, Routes } from '@angular/router';\nimport {\n  NgbDropdownModule,\n  NgbProgressbarModule,\n  NgbTooltipModule,\n} from '@ng-bootstrap/ng-bootstrap';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { TranslationModule } from '../../modules/i18n';\nimport { LayoutComponent } from './layout.component';\nimport { ExtrasModule } from '../partials/layout/extras/extras.module';\nimport { Routing } from '../../pages/routing';\nimport { HeaderComponent } from './components/header/header.component';\nimport { ContentComponent } from './components/content/content.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport { ScriptsInitComponent } from './components/scripts-init/scripts-init.component';\nimport { ToolbarComponent } from './components/toolbar/toolbar.component';\nimport { TopbarComponent } from './components/topbar/topbar.component';\nimport { PageTitleComponent } from './components/header/page-title/page-title.component';\nimport { HeaderMenuComponent } from './components/header/header-menu/header-menu.component';\nimport {\n  DrawersModule,\n  DropdownMenusModule,\n  ModalsModule,\n  EngagesModule,\n} from '../partials';\nimport { EngagesComponent } from '../partials/layout/engages/engages.component';\nimport { ThemeModeModule } from '../partials/layout/theme-mode-switcher/theme-mode.module';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport { SidebarLogoComponent } from './components/sidebar/sidebar-logo/sidebar-logo.component';\nimport { SidebarMenuComponent } from './components/sidebar/sidebar-menu/sidebar-menu.component';\nimport { SidebarFooterComponent } from './components/sidebar/sidebar-footer/sidebar-footer.component';\nimport { NavbarComponent } from './components/header/navbar/navbar.component';\nimport { AccountingComponent } from './components/toolbar/accounting/accounting.component';\nimport { ClassicComponent } from './components/toolbar/classic/classic.component';\nimport { ExtendedComponent } from './components/toolbar/extended/extended.component';\nimport { ReportsComponent } from './components/toolbar/reports/reports.component';\nimport { SaasComponent } from './components/toolbar/saas/saas.component';\nimport {SharedModule} from \"../shared/shared.module\";\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: LayoutComponent,\n    children: Routing,\n  },\n];\n\n@NgModule({\n  declarations: [\n    LayoutComponent,\n    HeaderComponent,\n    ContentComponent,\n    FooterComponent,\n    ScriptsInitComponent,\n    ToolbarComponent,\n    TopbarComponent,\n    PageTitleComponent,\n    HeaderMenuComponent,\n    EngagesComponent,\n    SidebarComponent,\n    SidebarLogoComponent,\n    SidebarMenuComponent,\n    SidebarFooterComponent,\n    NavbarComponent,\n    AccountingComponent,\n    ClassicComponent,\n    ExtendedComponent,\n    ReportsComponent,\n    SaasComponent,\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    TranslationModule,\n    InlineSVGModule,\n    NgbDropdownModule,\n    NgbProgressbarModule,\n    ExtrasModule,\n    ModalsModule,\n    DrawersModule,\n    EngagesModule,\n    DropdownMenusModule,\n    NgbTooltipModule,\n    TranslateModule,\n    ThemeModeModule,\n    SharedModule\n  ],\n  exports: [RouterModule],\n})\nexport class LayoutModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SACEC,iBAAiB,EACjBC,oBAAoB,EACpBC,gBAAgB,QACX,4BAA4B;AACnC,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,YAAY,QAAQ,yCAAyC;AACtE,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SACEC,aAAa,EACbC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,QACR,aAAa;AACpB,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,eAAe,QAAQ,0DAA0D;AAC1F,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,oBAAoB,QAAQ,0DAA0D;AAC/F,SAASC,oBAAoB,QAAQ,0DAA0D;AAC/F,SAASC,sBAAsB,QAAQ,8DAA8D;AACrG,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,mBAAmB,QAAQ,sDAAsD;AAC1F,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,aAAa,QAAQ,0CAA0C;AACxE,SAAQC,YAAY,QAAO,yBAAyB;AAEpD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAE9B,eAAe;EAC1B+B,QAAQ,EAAE7B;CACX,CACF;AA4CM,IAAM8B,YAAY,GAAlB,MAAMA,YAAY,GAAG;AAAfA,YAAY,GAAAC,UAAA,EA1CxB1C,QAAQ,CAAC;EACR2C,YAAY,EAAE,CACZlC,eAAe,EACfG,eAAe,EACfC,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,gBAAgB,EAChBC,eAAe,EACfC,kBAAkB,EAClBC,mBAAmB,EACnBK,gBAAgB,EAChBE,gBAAgB,EAChBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,eAAe,EACfC,mBAAmB,EACnBC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,CACd;EACDS,OAAO,EAAE,CACP3C,YAAY,EACZE,YAAY,CAAC0C,QAAQ,CAACR,MAAM,CAAC,EAC7B7B,iBAAiB,EACjBN,eAAe,EACfE,iBAAiB,EACjBC,oBAAoB,EACpBK,YAAY,EACZY,YAAY,EACZF,aAAa,EACbG,aAAa,EACbF,mBAAmB,EACnBf,gBAAgB,EAChBC,eAAe,EACfkB,eAAe,EACfW,YAAY,CACb;EACDU,OAAO,EAAE,CAAC3C,YAAY;CACvB,CAAC,C,EACWsC,YAAY,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}