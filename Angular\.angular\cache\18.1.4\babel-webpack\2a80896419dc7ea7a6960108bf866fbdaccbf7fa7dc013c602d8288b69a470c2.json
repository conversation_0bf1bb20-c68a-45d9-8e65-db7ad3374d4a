{"ast": null, "code": "// language list\nimport { locale as enLang } from './modules/i18n/vocabs/en';\nimport { locale as chLang } from './modules/i18n/vocabs/ch';\nimport { locale as esLang } from './modules/i18n/vocabs/es';\nimport { locale as jpLang } from './modules/i18n/vocabs/jp';\nimport { locale as deLang } from './modules/i18n/vocabs/de';\nimport { locale as frLang } from './modules/i18n/vocabs/fr';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./modules/i18n\";\nimport * as i2 from \"./_metronic/partials/layout/theme-mode-switcher/theme-mode.service\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = [\"root\", \"\"];\nexport class AppComponent {\n  translationService;\n  modeService;\n  constructor(translationService, modeService) {\n    this.translationService = translationService;\n    this.modeService = modeService;\n    // register translations\n    this.translationService.loadTranslations(enLang, chLang, esLang, jpLang, deLang, frLang);\n  }\n  ngOnInit() {\n    this.modeService.init();\n    // Hide splash screen when app is fully loaded\n    this.hideSplashScreenWhenReady();\n    // Test Kendo UI setup\n    console.log('Kendo UI setup check:', {\n      kendoAvailable: typeof window !== 'undefined' && window.kendo,\n      angularVersion: '18.1.4'\n    });\n  }\n  hideSplashScreenWhenReady() {\n    // Wait for the application to be fully loaded before hiding splash screen\n    // This ensures all initial data loading and component initialization is complete\n    let minTimeElapsed = false;\n    let appReady = false;\n    // Ensure minimum splash screen time of 2 seconds\n    setTimeout(() => {\n      minTimeElapsed = true;\n      if (appReady) {\n        this.hideSplashScreen();\n      }\n    }, 2000);\n    const checkAppReady = () => {\n      // Check if the main content is loaded and ready\n      const mainContent = document.querySelector('router-outlet') || document.querySelector('app-layout');\n      const isAppReady = mainContent && document.readyState === 'complete';\n      if (isAppReady) {\n        appReady = true;\n        if (minTimeElapsed) {\n          // Additional delay to ensure smooth transition\n          setTimeout(() => {\n            this.hideSplashScreen();\n          }, 500);\n        }\n      } else {\n        // Check again in 100ms\n        setTimeout(checkAppReady, 100);\n      }\n    };\n    // Start checking after initial delay\n    setTimeout(checkAppReady, 500);\n  }\n  hideSplashScreen() {\n    // Hide the static splash screen from index.html\n    const splashScreen = document.getElementById('splash-screen');\n    if (splashScreen) {\n      splashScreen.style.opacity = '0';\n      splashScreen.style.transition = 'opacity 0.8s ease-out';\n      setTimeout(() => {\n        splashScreen.style.display = 'none';\n        // Add app-loaded class to show content\n        document.body.classList.add('app-loaded');\n        // Dispatch event to restore console logging\n        window.dispatchEvent(new CustomEvent('splash-screen-hidden'));\n      }, 800);\n    }\n  }\n  static ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.TranslationService), i0.ɵɵdirectiveInject(i2.ThemeModeService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"body\", \"root\", \"\"]],\n    attrs: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    dependencies: [i3.RouterOutlet],\n    styles: [\"[_nghost-%COMP%] {\\n  height: 100%;\\n  margin: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsWUFBQTtFQUNBLFNBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcbiAgaGVpZ2h0OiAxMDAlO1xuICBtYXJnaW46IDA7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["locale", "enLang", "chLang", "esLang", "jpLang", "deLang", "frLang", "AppComponent", "translationService", "modeService", "constructor", "loadTranslations", "ngOnInit", "init", "hideSplashScreenWhenReady", "console", "log", "kendoAvailable", "window", "kendo", "angularVersion", "minTimeElapsed", "appReady", "setTimeout", "hideSplashScreen", "checkAppReady", "mainContent", "document", "querySelector", "isAppReady", "readyState", "splashScreen", "getElementById", "style", "opacity", "transition", "display", "body", "classList", "add", "dispatchEvent", "CustomEvent", "i0", "ɵɵdirectiveInject", "i1", "TranslationService", "i2", "ThemeModeService", "selectors", "attrs", "_c0", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\permittracker\\Angular\\src\\app\\app.component.ts", "D:\\permittracker\\Angular\\src\\app\\app.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';\nimport { TranslationService } from './modules/i18n';\n// language list\nimport { locale as enLang } from './modules/i18n/vocabs/en';\nimport { locale as chLang } from './modules/i18n/vocabs/ch';\nimport { locale as esLang } from './modules/i18n/vocabs/es';\nimport { locale as jpLang } from './modules/i18n/vocabs/jp';\nimport { locale as deLang } from './modules/i18n/vocabs/de';\nimport { locale as frLang } from './modules/i18n/vocabs/fr';\nimport { ThemeModeService } from './_metronic/partials/layout/theme-mode-switcher/theme-mode.service';\n\n@Component({\n  // tslint:disable-next-line:component-selector\n  // eslint-disable-next-line @angular-eslint/component-selector\n  selector: 'body[root]',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class AppComponent implements OnInit {\n  constructor(\n    private translationService: TranslationService,\n    private modeService: ThemeModeService\n  ) {\n    // register translations\n    this.translationService.loadTranslations(\n      enLang,\n      chLang,\n      esLang,\n      jpLang,\n      deLang,\n      frLang\n    );\n  }\n\n  ngOnInit() {\n    this.modeService.init();\n    \n    // Hide splash screen when app is fully loaded\n    this.hideSplashScreenWhenReady();\n    \n    // Test Kendo UI setup\n    console.log('Kendo UI setup check:', {\n      kendoAvailable: typeof window !== 'undefined' && (window as any).kendo,\n      angularVersion: '18.1.4'\n    });\n  }\n\n  private hideSplashScreenWhenReady() {\n    // Wait for the application to be fully loaded before hiding splash screen\n    // This ensures all initial data loading and component initialization is complete\n    let minTimeElapsed = false;\n    let appReady = false;\n    \n    // Ensure minimum splash screen time of 2 seconds\n    setTimeout(() => {\n      minTimeElapsed = true;\n      if (appReady) {\n        this.hideSplashScreen();\n      }\n    }, 2000);\n    \n    const checkAppReady = () => {\n      // Check if the main content is loaded and ready\n      const mainContent = document.querySelector('router-outlet') || document.querySelector('app-layout');\n      const isAppReady = mainContent && document.readyState === 'complete';\n      \n      if (isAppReady) {\n        appReady = true;\n        if (minTimeElapsed) {\n          // Additional delay to ensure smooth transition\n          setTimeout(() => {\n            this.hideSplashScreen();\n          }, 500);\n        }\n      } else {\n        // Check again in 100ms\n        setTimeout(checkAppReady, 100);\n      }\n    };\n    \n    // Start checking after initial delay\n    setTimeout(checkAppReady, 500);\n  }\n\n  private hideSplashScreen() {\n    // Hide the static splash screen from index.html\n    const splashScreen = document.getElementById('splash-screen');\n    if (splashScreen) {\n      splashScreen.style.opacity = '0';\n      splashScreen.style.transition = 'opacity 0.8s ease-out';\n      setTimeout(() => {\n        splashScreen.style.display = 'none';\n        // Add app-loaded class to show content\n        document.body.classList.add('app-loaded');\n        // Dispatch event to restore console logging\n        window.dispatchEvent(new CustomEvent('splash-screen-hidden'));\n      }, 800);\n    }\n  }\n}\n", "<router-outlet></router-outlet>\n"], "mappings": "AAEA;AACA,SAASA,MAAM,IAAIC,MAAM,QAAQ,0BAA0B;AAC3D,SAASD,MAAM,IAAIE,MAAM,QAAQ,0BAA0B;AAC3D,SAASF,MAAM,IAAIG,MAAM,QAAQ,0BAA0B;AAC3D,SAASH,MAAM,IAAII,MAAM,QAAQ,0BAA0B;AAC3D,SAASJ,MAAM,IAAIK,MAAM,QAAQ,0BAA0B;AAC3D,SAASL,MAAM,IAAIM,MAAM,QAAQ,0BAA0B;;;;;;AAW3D,OAAM,MAAOC,YAAY;EAEbC,kBAAA;EACAC,WAAA;EAFVC,YACUF,kBAAsC,EACtCC,WAA6B;IAD7B,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IAEnB;IACA,IAAI,CAACD,kBAAkB,CAACG,gBAAgB,CACtCV,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,CACP;EACH;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACI,IAAI,EAAE;IAEvB;IACA,IAAI,CAACC,yBAAyB,EAAE;IAEhC;IACAC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnCC,cAAc,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAKA,MAAc,CAACC,KAAK;MACtEC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEQN,yBAAyBA,CAAA;IAC/B;IACA;IACA,IAAIO,cAAc,GAAG,KAAK;IAC1B,IAAIC,QAAQ,GAAG,KAAK;IAEpB;IACAC,UAAU,CAAC,MAAK;MACdF,cAAc,GAAG,IAAI;MACrB,IAAIC,QAAQ,EAAE;QACZ,IAAI,CAACE,gBAAgB,EAAE;MACzB;IACF,CAAC,EAAE,IAAI,CAAC;IAER,MAAMC,aAAa,GAAGA,CAAA,KAAK;MACzB;MACA,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAC,IAAID,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnG,MAAMC,UAAU,GAAGH,WAAW,IAAIC,QAAQ,CAACG,UAAU,KAAK,UAAU;MAEpE,IAAID,UAAU,EAAE;QACdP,QAAQ,GAAG,IAAI;QACf,IAAID,cAAc,EAAE;UAClB;UACAE,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,gBAAgB,EAAE;UACzB,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC,MAAM;QACL;QACAD,UAAU,CAACE,aAAa,EAAE,GAAG,CAAC;MAChC;IACF,CAAC;IAED;IACAF,UAAU,CAACE,aAAa,EAAE,GAAG,CAAC;EAChC;EAEQD,gBAAgBA,CAAA;IACtB;IACA,MAAMO,YAAY,GAAGJ,QAAQ,CAACK,cAAc,CAAC,eAAe,CAAC;IAC7D,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;MAChCH,YAAY,CAACE,KAAK,CAACE,UAAU,GAAG,uBAAuB;MACvDZ,UAAU,CAAC,MAAK;QACdQ,YAAY,CAACE,KAAK,CAACG,OAAO,GAAG,MAAM;QACnC;QACAT,QAAQ,CAACU,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QACzC;QACArB,MAAM,CAACsB,aAAa,CAAC,IAAIC,WAAW,CAAC,sBAAsB,CAAC,CAAC;MAC/D,CAAC,EAAE,GAAG,CAAC;IACT;EACF;;qCAhFWlC,YAAY,EAAAmC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;;UAAZxC,YAAY;IAAAyC,SAAA;IAAAC,KAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnBzBb,EAAA,CAAAe,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}