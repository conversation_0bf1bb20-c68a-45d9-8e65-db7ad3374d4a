{"ast": null, "code": "import { AppSettings } from 'src/app/app.settings';\nimport { CurrencyPipe } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./app-encrypt-decrypt\";\nimport * as i2 from \"@angular/router\";\n// import { DatePipe } from '@angular/common';\n// import validator from 'validator';\nexport let AppService = /*#__PURE__*/(() => {\n  class AppService {\n    appEncryptDecryptService;\n    router;\n    constructor(appEncryptDecryptService, router) {\n      this.appEncryptDecryptService = appEncryptDecryptService;\n      this.router = router;\n    }\n    // get the local storage item with decrypted value\n    getLocalStorageItem(key, parsingNeeded) {\n      const itemVal = localStorage.getItem(key);\n      if (itemVal == null) {\n        return null;\n      }\n      const decryptedValue = this.appEncryptDecryptService.get(AppSettings.SECRET_KEY, itemVal);\n      if (!parsingNeeded) {\n        return decryptedValue;\n      } else {\n        return JSON.parse(decryptedValue);\n      }\n    }\n    // set the local storage item with encrypted value\n    setLocalStorageItem(key, value, parsingNeeded) {\n      if (!parsingNeeded) {\n        return localStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, value));\n      } else {\n        return localStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, JSON.stringify(value)));\n      }\n    }\n    // get the session storage item with decrypted value\n    getSessionStorageItem(key, parsingNeeded) {\n      const itemVal = sessionStorage.getItem(key);\n      if (itemVal == null) {\n        return null;\n      }\n      const decryptedValue = this.appEncryptDecryptService.get(AppSettings.SECRET_KEY, itemVal);\n      if (!parsingNeeded) {\n        return decryptedValue;\n      } else {\n        return JSON.parse(decryptedValue);\n      }\n    }\n    // set the local storage item with encrypted value\n    setSessionStorageItem(key, value, parsingNeeded) {\n      if (!parsingNeeded) {\n        return sessionStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, value));\n      } else {\n        return sessionStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, JSON.stringify(value)));\n      }\n    }\n    fromJsonDate(jDate) {\n      const bDate = new Date(jDate);\n      return bDate.toISOString().substring(0, 10); //Ignore time\n    }\n    logout() {\n      localStorage.removeItem('permitUser');\n      localStorage.removeItem('permitToken');\n      localStorage.removeItem('permit_access');\n      localStorage.removeItem('permit_exp');\n      const remember = this.getLocalStorageItem('permitRemember', false);\n      if (remember !== 'true') {\n        localStorage.removeItem('permitUserAuth');\n      }\n      this.router.navigate(['/auth/login']);\n    }\n    removeToken() {\n      localStorage.removeItem('permit_access');\n      localStorage.removeItem('permit_exp');\n    }\n    dollarAmount(data) {\n      const currencyPipe = new CurrencyPipe('en-US');\n      // Value to be formatted\n      const value = data;\n      // Format the currency\n      const formattedValue = currencyPipe.transform(value, 'USD', 'symbol', '1.2-2');\n      return formattedValue;\n    }\n    // get the time in sentence format\n    timeConvert(num) {\n      const hours = Math.floor(num / 60);\n      const minutes = num % 60;\n      switch (hours) {\n        case 0:\n          return minutes + 'min ago';\n        case 1:\n          return hours + 'hr' + minutes + 'min ago';\n        default:\n          return hours + 'hr' + minutes + 'min ago';\n      }\n    }\n    // get the decrypted password\n    getPasswordData(data) {\n      const decryptedValue = this.appEncryptDecryptService.get(AppSettings.API_PASSWORD_SECRET_KEY, data);\n      return JSON.parse(decryptedValue);\n    }\n    // get the date from Unix time\n    unixDate(unixtime) {\n      if (unixtime) {\n        const u = new Date(unixtime);\n        const month = String(u.getMonth() + 1).padStart(2, '0');\n        const day = String(u.getDate()).padStart(2, '0');\n        const dates = `${month}/${day}/${u.getFullYear()}`;\n        return dates;\n      } else {\n        const dates = '';\n        return dates;\n      }\n    }\n    // get the date time from unix time\n    unixTime(unixtime) {\n      if (unixtime) {\n        const u = new Date(unixtime);\n        const amOrPm = u.getHours() < 12 ? 'AM' : 'PM';\n        const hour = u.getHours() < 12 ? u.getHours() : u.getHours() - 12;\n        const month = u.getMonth() + 1;\n        const minutes = u.getMinutes();\n        let min;\n        if (minutes < 10) {\n          min = '0' + minutes.toString();\n        } else {\n          min = minutes.toString();\n        }\n        // const dates = month + '/' + u.getDate() + '/' + u.getFullYear() + ' ' + hour + ':' + min + ' ' + amOrPm;\n        const dates = hour + ':' + min + ' ' + amOrPm;\n        return dates;\n      } else {\n        const dates = '';\n        return dates;\n      }\n    }\n    dateDiffInDays(a, b) {\n      const _MS_PER_DAY = 1000 * 60 * 60 * 24;\n      // Discard the time and time-zone information.\n      const utc1 = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());\n      const utc2 = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());\n      return Math.floor((utc2 - utc1) / _MS_PER_DAY);\n    }\n    // get the format for phone number with /without extension\n    getPhoneFormat(phoneNum) {\n      // Remove all non-digit characters\n      const digitsOnly = phoneNum.replace(/\\D/g, '');\n      const baseLength = 10;\n      if (digitsOnly.length < baseLength) {\n        return phoneNum.trim(); // Not enough digits to format\n      }\n      const areaCode = digitsOnly.substring(0, 3);\n      const mid = digitsOnly.substring(3, 6);\n      const lastFour = digitsOnly.substring(6, 10);\n      const extension = digitsOnly.length > baseLength ? digitsOnly.substring(10) : '';\n      let formatted = `(${areaCode}) ${mid}-${lastFour}`;\n      if (extension) {\n        formatted += ` Ext. ${extension}`;\n      }\n      return formatted.trim();\n    }\n    // get the format for email in href\n    mailto(emailAddress) {\n      return \"mailto:\" + emailAddress;\n    }\n    onImgError(event) {\n      event.target.src = './assets/media/avatars/blank.png';\n    }\n    ImageUrl(name) {\n      return AppSettings.IMAGEPATH + name;\n    }\n    formatDate(date) {\n      if (date === '' || date === null || date === undefined) {\n        return '';\n      }\n      const d = new Date(date);\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const year = d.getFullYear();\n      return `${month}/${day}/${year}`;\n    }\n    // Static method for consistent date formatting across components\n    static formatDate(date) {\n      if (date === '' || date === null || date === undefined) {\n        return '';\n      }\n      const d = new Date(date);\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const year = d.getFullYear();\n      return `${month}/${day}/${year}`;\n    }\n    getTodayDate() {\n      const today = new Date();\n      let date = today.getDate() > 9 ? today.getDate() : `0${today.getDate()}`;\n      let month = today.getMonth() > 9 ? today.getMonth() + 1 : `0${today.getMonth() + 1}`;\n      let year = today.getFullYear();\n      let todayDate = year + \"-\" + month + \"-\" + date;\n      return todayDate;\n    }\n    customSearchFn(term, item) {\n      term = term.toLocaleLowerCase();\n      return item.email.toLocaleLowerCase().indexOf(term) > -1 || item.UserFullName.toLocaleLowerCase().indexOf(term) > -1 || item.firstname.toLocaleLowerCase().indexOf(term) > -1;\n      // (item.code + \" - \" + item.name).toLocaleLowerCase().indexOf(term) > -1;\n    }\n    formatCurrency(amount) {\n      // Convert the input to a number, handling null, undefined, or empty string\n      const amountFormatted = parseFloat(amount) || 0;\n      // Use Intl.NumberFormat to format as USD currency\n      const formatter = new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: \"USD\"\n      });\n      return formatter.format(amountFormatted);\n    }\n    formatMonthDate(dateString) {\n      if (dateString) {\n        const u = new Date(dateString);\n        const month = String(u.getUTCMonth() + 1).padStart(2, '0'); // Ensure two digits for the month\n        const day = String(u.getUTCDate()).padStart(2, '0'); // Ensure two digits for the day\n        const year = u.getUTCFullYear();\n        return `${month}/${day}/${year}`;\n      } else {\n        return '';\n      }\n    }\n    formatCommissionPercent(compAgentPercent) {\n      if (!compAgentPercent || compAgentPercent === 0 || compAgentPercent === 0.00) {\n        return '0%';\n      }\n      return `${compAgentPercent}%`;\n    }\n    decodeHtmlEntities(input) {\n      const entities = {\n        '&amp;': '&',\n        '&lt;': '<',\n        '&gt;': '>',\n        '&quot;': '\"',\n        '&#39;': \"'\"\n      };\n      return input.replace(/&amp;|&lt;|&gt;|&quot;|&#39;/g, match => entities[match] || match);\n    }\n    // formatMonthDate(dateString: string): string | null {\n    //   return this.datePipe.transform(dateString, 'MM/dd/yyyy');\n    // }\n    getDateWithoutTimezone(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0'); // Add leading zero if needed\n      const day = String(d.getDate()).padStart(2, '0'); // Add leading zero if needed\n      return `${year}-${month}-${day}`;\n    }\n    // Utility to check if the field is a date field\n    isDateField(columns, field) {\n      const dateColumnNames = columns; // List of date fields\n      return dateColumnNames.includes(field);\n    }\n    convertDateFilters(filters, dateFields) {\n      return filters.map(group => {\n        const updatedFilters = group.filters.map(f => {\n          if (dateFields.includes(f.field) && typeof f.value === 'string') {\n            return {\n              ...f,\n              value: f.value ? new Date(f.value) : null\n            };\n          }\n          return f;\n        });\n        return {\n          ...group,\n          filters: updatedFilters\n        };\n      });\n    }\n    /**\n     * Converts file size from bytes to kilobytes (KB).\n     *\n     * - Takes the file size in bytes as input.\n     * - Divides by 1024 to convert it to KB.\n     * - Rounds the result to the nearest whole number.\n     *\n     * @param size - The file size in bytes.\n     * @returns The file size in kilobytes (rounded).\n     */\n    getFileSize(size) {\n      return Math.round(size / 1024);\n    }\n    updateColumnConfig(hiddenData, columnReorderData, columnJSONFormat) {\n      // Create a deep copy of the original array to avoid modifying the input directly\n      const updatedColumns = columnJSONFormat;\n      for (const column of updatedColumns) {\n        column.hidden = false;\n      }\n      // Step 1: Update hidden property based on hiddenData\n      for (const hiddenItem of hiddenData) {\n        const fieldToUpdate = updatedColumns.find(item => item.field === hiddenItem.field);\n        if (fieldToUpdate) {\n          fieldToUpdate.hidden = hiddenItem.hidden;\n        }\n      }\n      // Step 2: Create a map of fields to their new order\n      const orderMap = {};\n      columnReorderData.forEach(item => {\n        orderMap[item.field] = item.orderIndex;\n      });\n      // Step 3: Update order based on columnReorderData\n      for (let column of updatedColumns) {\n        if (orderMap.hasOwnProperty(column.field)) {\n          column.order = orderMap[column.field];\n        }\n      }\n      // Step 4: Sort the array by order\n      updatedColumns.sort((a, b) => a.order - b.order);\n      return updatedColumns;\n    }\n    // function to check whether the form has any error\n    controlHasError(validation, controlName, name) {\n      const control = name.controls[controlName];\n      if (!control) {\n        return false;\n      }\n      let result = control.hasError(validation) && (control.dirty || control.touched);\n      return result;\n    }\n    getLoggedInUser() {\n      let loggedInUser = this.getLocalStorageItem('permitUser', true);\n      return loggedInUser;\n    }\n    // Derive initials from various possible user shapes\n    getUserInitials(user) {\n      if (!user) {\n        return '?';\n      }\n      const tryString = val => (val || '').toString().trim();\n      // Prefer explicit first/last\n      const first = tryString(user.firstname || user.firstName || user.FirstName);\n      const last = tryString(user.lastname || user.lastName || user.LastName);\n      if (first || last) {\n        const fi = first ? first.charAt(0).toUpperCase() : '';\n        const li = last ? last.charAt(0).toUpperCase() : '';\n        return fi + li || '?';\n      }\n      // Try full name fields\n      const full = tryString(user.UserFullName || user.userFullName || user.fullName || user.FullName || user.name);\n      if (full) {\n        const parts = full.split(/\\s+/).filter(Boolean);\n        if (parts.length === 1) {\n          return parts[0].charAt(0).toUpperCase();\n        }\n        return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();\n      }\n      // Fallback to email/username\n      const email = tryString(user.email || user.Email);\n      const username = tryString(user.userName || user.username || user.UserName);\n      const source = email || username;\n      if (source) {\n        // take letters from before @ or start of username\n        const base = email ? source.split('@')[0] : source;\n        const letters = base.replace(/[^A-Za-z]/g, '');\n        if (letters.length >= 2) {\n          return (letters[0] + letters[1]).toUpperCase();\n        }\n        if (letters.length === 1) {\n          return letters[0].toUpperCase();\n        }\n      }\n      return '?';\n    }\n    static ɵfac = function AppService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppService)(i0.ɵɵinject(i1.AppEncryptDecryptService), i0.ɵɵinject(i2.Router));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AppService,\n      factory: AppService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AppService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}