{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { ProjectListComponent } from './project-list/project-list.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/app.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../_metronic/layout/core/page-info.service\";\nconst _c0 = a0 => ({\n  \"active\": a0\n});\nfunction ProjectsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"ul\", 5)(3, \"li\", 6)(4, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_1_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onNavChange(\"Projects\"));\n    });\n    i0.ɵɵtext(5, \" Projects \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r1.selectedTab === \"Projects\"));\n  }\n}\nfunction ProjectsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ProjectsComponent {\n  AppService;\n  router;\n  pageInfo;\n  selectedTab = 'Projects'; //store default selected tab\n  loginUser;\n  showNavBar = true; // Control visibility of navigation bar\n  routeSubscription = new Subscription();\n  projectListComponent;\n  constructor(AppService, router, pageInfo) {\n    this.AppService = AppService;\n    this.router = router;\n    this.pageInfo = pageInfo;\n    // set the default paging options\n  }\n  ngOnInit() {\n    this.pageInfo.updateTitle('Projects');\n    // Ensure Projects tab is selected by default\n    this.selectedTab = 'Projects';\n    // Check if we're on the view route and hide nav bar accordingly\n    this.checkRouteAndToggleNavBar();\n    // Subscribe to route changes to update nav bar visibility\n    this.routeSubscription = this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      this.checkRouteAndToggleNavBar();\n    });\n  }\n  ngAfterViewInit() {\n    // Any initialization after view is ready\n  }\n  ngOnDestroy() {\n    // Clean up subscription\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n  checkRouteAndToggleNavBar() {\n    // Hide nav bar when on projects/view route\n    this.showNavBar = !this.router.url.includes('/projects/view/');\n  }\n  onNavChange(tabName) {\n    console.log(`Switching to tab: ${tabName}`);\n    this.selectedTab = tabName;\n    if (tabName === 'Projects') {\n      // Navigate to projects list\n      this.router.navigate(['/projects/list']);\n    }\n  }\n  static ɵfac = function ProjectsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectsComponent)(i0.ɵɵdirectiveInject(i1.AppService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.PageInfoService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectsComponent,\n    selectors: [[\"app-projects\"]],\n    viewQuery: function ProjectsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(ProjectListComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.projectListComponent = _t.first);\n      }\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"card\", \"mb-5\", \"mb-xl-5\"], [\"class\", \"card-body pb-0 pt-0\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"card-body\", \"pb-0\", \"pt-0\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-3x\", \"border-transparent\", \"fs-5\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"]],\n    template: function ProjectsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, ProjectsComponent_div_1_Template, 6, 3, \"div\", 1)(2, ProjectsComponent_div_2_Template, 2, 0, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showNavBar);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Projects\");\n      }\n    },\n    styles: [\".nav-line-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.nav-line-tabs[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #009ef7 !important;\\n  border-bottom: 2px solid #009ef7;\\n  font-weight: 600;\\n}\\n.nav-line-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #009ef7 !important;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9wcm9qZWN0cy9wcm9qZWN0cy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFRTtFQUNFLGVBQUE7RUFDQSx5QkFBQTtBQURKO0FBR0k7RUFDRSx5QkFBQTtFQUNBLGdDQUFBO0VBQ0EsZ0JBQUE7QUFETjtBQUlJO0VBQ0UseUJBQUE7QUFGTjs7QUFRQTtFQUNFLDhCQUFBO0FBTEY7O0FBUUE7RUFDRTtJQUNFLFVBQUE7SUFDQSwyQkFBQTtFQUxGO0VBT0E7SUFDRSxVQUFBO0lBQ0Esd0JBQUE7RUFMRjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLy8gUHJvamVjdHMgY29tcG9uZW50IHN0eWxlc1xyXG4ubmF2LWxpbmUtdGFicyB7XHJcbiAgLm5hdi1saW5rIHtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcblxyXG4gICAgJi5hY3RpdmUge1xyXG4gICAgICBjb2xvcjogIzAwOWVmNyAhaW1wb3J0YW50O1xyXG4gICAgICBib3JkZXItYm90dG9tOiAycHggc29saWQgIzAwOWVmNztcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIH1cclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgY29sb3I6ICMwMDllZjcgIWltcG9ydGFudDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIFRhYiBjb250ZW50IHRyYW5zaXRpb25zXHJcbi50YWItY29udGVudCB7XHJcbiAgYW5pbWF0aW9uOiBmYWRlSW4gMC4zcyBlYXNlLWluO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIGZhZGVJbiB7XHJcbiAgZnJvbSB7XHJcbiAgICBvcGFjaXR5OiAwO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDEwcHgpO1xyXG4gIH1cclxuICB0byB7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xyXG4gIH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "Subscription", "filter", "ProjectListComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ProjectsComponent_div_1_Template_a_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onNavChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "selectedTab", "ɵɵelement", "ProjectsComponent", "AppService", "router", "pageInfo", "loginUser", "showNavBar", "routeSubscription", "projectListComponent", "constructor", "ngOnInit", "updateTitle", "checkRouteAndToggleNavBar", "events", "pipe", "event", "subscribe", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "url", "includes", "tabName", "console", "log", "navigate", "ɵɵdirectiveInject", "i1", "i2", "Router", "i3", "PageInfoService", "selectors", "viewQuery", "ProjectsComponent_Query", "rf", "ctx", "ɵɵtemplate", "ProjectsComponent_div_1_Template", "ProjectsComponent_div_2_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\projects.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\projects.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { AppService } from '../services/app.service';\nimport { PageInfoService } from '../../_metronic/layout/core/page-info.service';\nimport { ProjectListComponent } from './project-list/project-list.component';\n\n@Component({\n  selector: 'app-projects',\n  templateUrl: './projects.component.html',\n  styleUrls: ['./projects.component.scss']\n})\nexport class ProjectsComponent implements OnInit, AfterViewInit, OnDestroy {\n  selectedTab = 'Projects'; //store default selected tab\n  loginUser: any;\n  showNavBar = true; // Control visibility of navigation bar\n  private routeSubscription: Subscription = new Subscription();\n  \n  @ViewChild(ProjectListComponent) projectListComponent: ProjectListComponent;\n  \n  constructor(\n    public AppService: AppService,\n    private router: Router,\n    private pageInfo: PageInfoService\n  ) {\n    // set the default paging options\n  }\n  \n  ngOnInit() {\n    this.pageInfo.updateTitle('Projects');\n    // Ensure Projects tab is selected by default\n    this.selectedTab = 'Projects';\n    \n    // Check if we're on the view route and hide nav bar accordingly\n    this.checkRouteAndToggleNavBar();\n    \n    // Subscribe to route changes to update nav bar visibility\n    this.routeSubscription = this.router.events\n      .pipe(filter(event => event instanceof NavigationEnd))\n      .subscribe((event: NavigationEnd) => {\n        this.checkRouteAndToggleNavBar();\n      });\n  }\n\n  ngAfterViewInit() {\n    // Any initialization after view is ready\n  }\n  \n  ngOnDestroy() {\n    // Clean up subscription\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n  \n  checkRouteAndToggleNavBar() {\n    // Hide nav bar when on projects/view route\n    this.showNavBar = !this.router.url.includes('/projects/view/');\n  }\n  \n  onNavChange(tabName: string) {\n    console.log(`Switching to tab: ${tabName}`);\n    this.selectedTab = tabName;\n    \n    if (tabName === 'Projects') {\n      // Navigate to projects list\n      this.router.navigate(['/projects/list']);\n    }\n  }\n}\n", "<div class=\"card mb-5 mb-xl-5\">\n    <div class=\"card-body pb-0 pt-0\" *ngIf=\"showNavBar\">\n        <div class=\"d-flex\">\n            <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-3x border-transparent fs-5 fw-bold flex-nowrap\">\n                <li class=\"nav-item\">\n                    <a class=\"nav-link text-active-primary me-6 cursor-pointer\"\n                        [ngClass]=\"{'active': selectedTab === 'Projects'}\" \n                        (click)=\"onNavChange('Projects')\">\n                        Projects\n                    </a>\n                </li>\n            </ul>\n        </div>\n    </div>\n\n    <div *ngIf=\"selectedTab==='Projects'\">\n        <router-outlet></router-outlet>\n    </div>\n</div>\n"], "mappings": "AACA,SAAiBA,aAAa,QAAQ,iBAAiB;AACvD,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,MAAM,QAAQ,gBAAgB;AAGvC,SAASC,oBAAoB,QAAQ,uCAAuC;;;;;;;;;;;ICDxDC,EAJhB,CAAAC,cAAA,aAAoD,aAC5B,YACuF,YAC9E,WAGqB;IAAlCD,EAAA,CAAAE,UAAA,mBAAAC,oDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAY,UAAU,CAAC;IAAA,EAAC;IACjCT,EAAA,CAAAU,MAAA,iBACJ;IAIhBV,EAJgB,CAAAW,YAAA,EAAI,EACH,EACJ,EACH,EACJ;;;;IAPcX,EAAA,CAAAY,SAAA,GAAkD;IAAlDZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAT,MAAA,CAAAU,WAAA,iBAAkD;;;;;IAStEhB,EAAA,CAAAC,cAAA,UAAsC;IAClCD,EAAA,CAAAiB,SAAA,oBAA+B;IACnCjB,EAAA,CAAAW,YAAA,EAAM;;;ADJV,OAAM,MAAOO,iBAAiB;EASnBC,UAAA;EACCC,MAAA;EACAC,QAAA;EAVVL,WAAW,GAAG,UAAU,CAAC,CAAC;EAC1BM,SAAS;EACTC,UAAU,GAAG,IAAI,CAAC,CAAC;EACXC,iBAAiB,GAAiB,IAAI3B,YAAY,EAAE;EAE3B4B,oBAAoB;EAErDC,YACSP,UAAsB,EACrBC,MAAc,EACdC,QAAyB;IAF1B,KAAAF,UAAU,GAAVA,UAAU;IACT,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAEhB;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACN,QAAQ,CAACO,WAAW,CAAC,UAAU,CAAC;IACrC;IACA,IAAI,CAACZ,WAAW,GAAG,UAAU;IAE7B;IACA,IAAI,CAACa,yBAAyB,EAAE;IAEhC;IACA,IAAI,CAACL,iBAAiB,GAAG,IAAI,CAACJ,MAAM,CAACU,MAAM,CACxCC,IAAI,CAACjC,MAAM,CAACkC,KAAK,IAAIA,KAAK,YAAYpC,aAAa,CAAC,CAAC,CACrDqC,SAAS,CAAED,KAAoB,IAAI;MAClC,IAAI,CAACH,yBAAyB,EAAE;IAClC,CAAC,CAAC;EACN;EAEAK,eAAeA,CAAA;IACb;EAAA;EAGFC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACX,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACY,WAAW,EAAE;IACtC;EACF;EAEAP,yBAAyBA,CAAA;IACvB;IACA,IAAI,CAACN,UAAU,GAAG,CAAC,IAAI,CAACH,MAAM,CAACiB,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC;EAChE;EAEA7B,WAAWA,CAAC8B,OAAe;IACzBC,OAAO,CAACC,GAAG,CAAC,qBAAqBF,OAAO,EAAE,CAAC;IAC3C,IAAI,CAACvB,WAAW,GAAGuB,OAAO;IAE1B,IAAIA,OAAO,KAAK,UAAU,EAAE;MAC1B;MACA,IAAI,CAACnB,MAAM,CAACsB,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAC1C;EACF;;qCAxDWxB,iBAAiB,EAAAlB,EAAA,CAAA2C,iBAAA,CAAAC,EAAA,CAAAzB,UAAA,GAAAnB,EAAA,CAAA2C,iBAAA,CAAAE,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAA2C,iBAAA,CAAAI,EAAA,CAAAC,eAAA;EAAA;;UAAjB9B,iBAAiB;IAAA+B,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAMjBrD,oBAAoB;;;;;;;;;;;;QCnBjCC,EAAA,CAAAC,cAAA,aAA+B;QAe3BD,EAdA,CAAAsD,UAAA,IAAAC,gCAAA,iBAAoD,IAAAC,gCAAA,iBAcd;QAG1CxD,EAAA,CAAAW,YAAA,EAAM;;;QAjBgCX,EAAA,CAAAY,SAAA,EAAgB;QAAhBZ,EAAA,CAAAa,UAAA,SAAAwC,GAAA,CAAA9B,UAAA,CAAgB;QAc5CvB,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAAa,UAAA,SAAAwC,GAAA,CAAArC,WAAA,gBAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}