{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../services/app.service\";\nimport * as i4 from \"../../services/http-utils.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../../modules/services/projects.service\";\nimport * as i7 from \"../../services/user.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Project - \", ctx_r0.projectName, \"\");\n  }\n}\nfunction ProjectPopupComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23)(3, \"span\", 24);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵtext(6, \"Loading project data...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectPopupComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 22)(2, \"div\", 23)(3, \"span\", 24);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵtext(6, \"Initializing form...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"div\", 10)(3, \"div\", 30)(4, \"label\", 31);\n    i0.ɵɵtext(5, \"Project Name\");\n    i0.ɵɵelementStart(6, \"sup\", 32);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"input\", 33);\n    i0.ɵɵtemplate(9, ProjectPopupComponent_form_21_ng_container_1_span_9_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 35)(12, \"div\", 30)(13, \"label\", 31);\n    i0.ɵɵtext(14, \"Internal Project # \");\n    i0.ɵɵelementStart(15, \"sup\", 32);\n    i0.ɵɵtext(16, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"input\", 36);\n    i0.ɵɵtemplate(18, ProjectPopupComponent_form_21_ng_container_1_span_18_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 37)(20, \"label\", 31);\n    i0.ɵɵtext(21, \"Start Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 37)(24, \"label\", 31);\n    i0.ɵɵtext(25, \"End Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 10)(28, \"label\", 31);\n    i0.ɵɵtext(29, \"Location\");\n    i0.ɵɵelementStart(30, \"sup\", 32);\n    i0.ɵɵtext(31, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(32, \"input\", 40);\n    i0.ɵɵtemplate(33, ProjectPopupComponent_form_21_ng_container_1_span_33_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 29)(35, \"div\", 10)(36, \"label\", 31);\n    i0.ɵɵtext(37, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"textarea\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"projectName\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"internalProjectNo\"));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"location\"));\n  }\n}\nfunction ProjectPopupComponent_form_21_div_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 35)(2, \"label\", 31);\n    i0.ɵɵtext(3, \"Internal Project Manager \");\n    i0.ɵɵelementStart(4, \"sup\", 32);\n    i0.ɵɵtext(5, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"ng-select\", 43);\n    i0.ɵɵlistener(\"change\", function ProjectPopupComponent_form_21_div_2_Template_ng_select_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeInternalManager($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ProjectPopupComponent_form_21_div_2_span_7_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 44)(9, \"label\", 31);\n    i0.ɵɵtext(10, \"External Project Manager (multiple)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ng-select\", 45);\n    i0.ɵɵlistener(\"change\", function ProjectPopupComponent_form_21_div_2_Template_ng_select_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeexternalPM($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.managers)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"manager\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"items\", ctx_r0.externalPMs)(\"clearable\", true)(\"multiple\", true);\n  }\n}\nfunction ProjectPopupComponent_form_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 27);\n    i0.ɵɵtemplate(1, ProjectPopupComponent_form_21_ng_container_1_Template, 39, 3, \"ng-container\", 3)(2, ProjectPopupComponent_form_21_div_2_Template, 12, 7, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.projectForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedTab == \"basic\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedTab != \"basic\");\n  }\n}\nfunction ProjectPopupComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.projectForm == null ? null : ctx_r0.projectForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.id ? \"Update\" : \"Save\", \" \");\n  }\n}\nfunction ProjectPopupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_30_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.showTab(\"role\", $event));\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let ProjectPopupComponent = /*#__PURE__*/(() => {\n  class ProjectPopupComponent {\n    modal;\n    cdr;\n    fb;\n    appService;\n    httpUtilService;\n    customLayoutUtilsService;\n    projectsService;\n    usersService;\n    id = 0; // 0 = Add, otherwise Edit\n    project; // incoming project data (for edit)\n    passEntry = new EventEmitter();\n    projectForm = null;\n    loginUser = {};\n    managers = [];\n    externalPMs = [];\n    isLoading = false;\n    selectedTab = 'basic'; //store navigation tab\n    projectName;\n    constructor(modal, cdr, fb, appService, httpUtilService, customLayoutUtilsService, projectsService,\n    // adjust path\n    usersService) {\n      this.modal = modal;\n      this.cdr = cdr;\n      this.fb = fb;\n      this.appService = appService;\n      this.httpUtilService = httpUtilService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.projectsService = projectsService;\n      this.usersService = usersService;\n      // Subscribe to loading state\n      this.httpUtilService.loadingSubject.subscribe(loading => {\n        this.isLoading = loading;\n      });\n    }\n    ngOnInit() {\n      this.loginUser = this.appService.getLoggedInUser();\n      this.loadForm();\n      // Set loading state immediately for edit mode\n      if (this.id !== 0) {\n        this.isLoading = true;\n        this.httpUtilService.loadingSubject.next(true);\n        // Wait for both manager lists to load before patching form\n        this.loadManagersAndPatchForm();\n      } else {\n        // For add mode, load managers without loading state\n        this.loadInternalManager();\n        this.loadExternalManagers();\n      }\n    }\n    loadForm() {\n      this.projectForm = this.fb.group({\n        projectName: ['', Validators.required],\n        internalProjectNo: ['', Validators.required],\n        startDate: [''],\n        endDate: [''],\n        // Make end date optional\n        location: ['', Validators.required],\n        projectDescription: [''],\n        manager: [null, Validators.required],\n        externalPM: [[]] // Remove required validator for external PM as it's optional\n      });\n      // Trigger change detection to update the view\n      this.cdr.detectChanges();\n    }\n    // Load MedicalCenters\n    loadInternalManager() {\n      this.usersService.getUserlistForDropdown({\n        roleName: 'Internal PM'\n      }).subscribe(internalPMSResponse => {\n        const users = internalPMSResponse?.responseData?.users;\n        this.managers = Array.isArray(users) ? users : [];\n      });\n    }\n    // Load roles for advanced filters\n    loadExternalManagers() {\n      this.usersService.getUserlistForDropdown({\n        roleName: 'External PM'\n      }).subscribe(externalPMSResponse => {\n        const users = externalPMSResponse?.responseData?.users;\n        this.externalPMs = Array.isArray(users) ? users : [];\n      });\n    }\n    // Load both manager lists and then patch the form\n    loadManagersAndPatchForm() {\n      // Use Promise.all to wait for both API calls to complete\n      const internalPMsPromise = this.usersService.getUserlistForDropdown({\n        roleName: 'Internal PM'\n      }).toPromise();\n      const externalPMsPromise = this.usersService.getUserlistForDropdown({\n        roleName: 'External PM'\n      }).toPromise();\n      Promise.all([internalPMsPromise, externalPMsPromise]).then(([internalResponse, externalResponse]) => {\n        // Set the manager lists\n        this.managers = Array.isArray(internalResponse?.responseData?.users) ? internalResponse.responseData.users : [];\n        this.externalPMs = Array.isArray(externalResponse?.responseData?.users) ? externalResponse.responseData.users : [];\n        // Now patch the form with the loaded data\n        this.patchForm();\n      }).catch(error => {\n        console.error('Error loading manager lists:', error);\n        // Still try to patch form even if manager lists fail\n        this.patchForm();\n      });\n    }\n    patchForm() {\n      // Loading state is already set in ngOnInit for edit mode\n      this.projectsService.getProject({\n        projectId: this.id,\n        loggedInUserId: this.loginUser.userId\n      }).subscribe({\n        next: projectResponse => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!projectResponse.isFault) {\n            let projectData = projectResponse.responseData.Project;\n            // this.projectId = projectData.projectId\n            console.log('Project data received:', projectData);\n            console.log('Available managers:', this.managers);\n            console.log('Available external PMs:', this.externalPMs);\n            this.projectName = projectData.projectName;\n            // Format dates for HTML date inputs (YYYY-MM-DD format)\n            const formatDateForInput = dateValue => {\n              if (!dateValue) return '';\n              const date = new Date(dateValue);\n              return date.toISOString().split('T')[0];\n            };\n            // The manager is already stored as userId in the database\n            const getManagerId = managerId => {\n              if (!managerId) return null;\n              return parseInt(managerId);\n            };\n            // Parse external PM names string to array of userIds\n            const parseExternalPMs = externalPMNamesString => {\n              if (!externalPMNamesString) return [];\n              // Split comma-separated names and find matching userIds\n              const pmNames = externalPMNamesString.split(',').map(name => name.trim()).filter(name => name !== '');\n              return pmNames.map(name => {\n                const pm = this.externalPMs.find(epm => epm.userFullName === name);\n                return pm ? pm.userId : null;\n              }).filter(id => id !== null);\n            };\n            const formData = {\n              projectName: projectData.projectName,\n              internalProjectNo: projectData.internalProjectNumber,\n              startDate: formatDateForInput(projectData.projectStartDate),\n              endDate: formatDateForInput(projectData.projectEndDate),\n              location: projectData.projectLocation,\n              projectDescription: projectData.projectDescription,\n              manager: getManagerId(projectData.internalProjectManager),\n              externalPM: parseExternalPMs(projectData.externalPMNames)\n            };\n            console.log('Form data to patch:', formData);\n            if (this.projectForm) {\n              this.projectForm.patchValue(formData);\n            }\n          } else {\n            console.warn('User response has isFault = true', projectResponse.responseData);\n          }\n        },\n        error: err => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('API call failed', err);\n        }\n      });\n    }\n    save() {\n      if (!this.projectForm) {\n        this.customLayoutUtilsService.showError('Form is not initialized. Please try again.', '');\n        return;\n      }\n      let controls = this.projectForm.controls;\n      if (this.projectForm.invalid) {\n        Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n        this.customLayoutUtilsService.showError('Please fill all required fields', '');\n        return;\n      }\n      let projectData = this.prepareProjectData();\n      console.log('Line: 203', projectData);\n      if (this.id === 0) {\n        this.create(projectData);\n      } else {\n        this.edit(projectData);\n      }\n    }\n    prepareProjectData() {\n      if (!this.projectForm) {\n        throw new Error('Form is not initialized');\n      }\n      const formData = this.projectForm.value;\n      let projectRequestData = {};\n      projectRequestData.projectName = formData.projectName;\n      projectRequestData.internalProjectNumber = formData.internalProjectNo;\n      projectRequestData.projectStartDate = formData.startDate;\n      projectRequestData.projectEndDate = formData.endDate;\n      projectRequestData.projectLocation = formData.location;\n      projectRequestData.projectDescription = formData.projectDescription;\n      // Send manager userId directly to backend\n      projectRequestData.internalProjectManager = formData.manager;\n      // Convert external PM userIds to comma-separated string for backend\n      const getExternalPMIds = userIds => {\n        if (!userIds || !Array.isArray(userIds)) return '';\n        return userIds.filter(id => id !== null && id !== undefined).join(',');\n      };\n      projectRequestData.externalPM = getExternalPMIds(formData.externalPM);\n      projectRequestData.loggedInUserId = this.loginUser.userId;\n      console.log('Prepared project data for backend:', projectRequestData);\n      return projectRequestData;\n    }\n    // API to update the user details based on the userid\n    edit(projectData) {\n      projectData.projectId = this.id;\n      this.httpUtilService.loadingSubject.next(true);\n      this.projectsService.updateProject(projectData).subscribe(res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          this.passEntry.emit(true);\n          this.modal.close(true); // Pass true to indicate successful edit\n        } else {\n          this.customLayoutUtilsService.showError(res.responseData.message, '');\n          this.passEntry.emit(false);\n        }\n      });\n    }\n    // API to save new user details\n    create(projectData) {\n      this.httpUtilService.loadingSubject.next(true);\n      this.projectsService.createProject(projectData).subscribe(res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          this.passEntry.emit(true);\n          this.modal.close(true); // Pass true to indicate successful creation\n        } else {\n          this.customLayoutUtilsService.showError(res.responseData.message, '');\n          this.passEntry.emit(false);\n        }\n      });\n    }\n    showTab(tab, $event) {\n      this.selectedTab = tab;\n      this.cdr.markForCheck();\n    }\n    changeInternalManager(event) {}\n    changeexternalPM(event) {}\n    controlHasError(validation, controlName) {\n      if (!this.projectForm) {\n        return false;\n      }\n      const control = this.projectForm.controls[controlName];\n      if (!control) {\n        return false;\n      }\n      let result = control.hasError(validation) && (control.dirty || control.touched);\n      return result;\n    }\n    goToPreviousTab() {\n      // if (this.selectedTab === 'notes') {\n      //   this.selectedTab = 'details';\n      // } else if (this.selectedTab === 'details') {\n      // }\n      this.selectedTab = 'basic';\n      this.cdr.markForCheck();\n    }\n    static ɵfac = function ProjectPopupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProjectPopupComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AppService), i0.ɵɵdirectiveInject(i4.HttpUtilsService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.ProjectsService), i0.ɵɵdirectiveInject(i7.UserService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectPopupComponent,\n      selectors: [[\"app-project-popup\"]],\n      inputs: {\n        id: \"id\",\n        project: \"project\"\n      },\n      outputs: {\n        passEntry: \"passEntry\"\n      },\n      decls: 31,\n      vars: 14,\n      consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [\"class\", \"loading-overlay-inside\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center align-items-center h-100\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"class\", \"form form-label-right\", 3, \"formGroup\", 4, \"ngIf\"], [1, \"modal-footer\", \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"loading-overlay-inside\"], [1, \"text-center\"], [\"role\", \"status\", 1, \"custom-colored-spinner\", \"mb-3\"], [1, \"visually-hidden\"], [1, \"text-muted\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"h-100\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [\"class\", \"row mt-4\", 4, \"ngIf\"], [1, \"row\", \"mt-4\"], [1, \"form-group\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"formControlName\", \"projectName\", \"placeholder\", \"Project Name\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"custom-error-css\", 4, \"ngIf\"], [1, \"col-xl-6\"], [\"type\", \"text\", \"formControlName\", \"internalProjectNo\", \"placeholder\", \"Internal Project\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-3\"], [\"type\", \"date\", \"formControlName\", \"startDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"endDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", \"placeholder\", \"Location\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"projectDescription\", 1, \"form-control\", \"form-control-sm\"], [1, \"custom-error-css\"], [\"bindLabel\", \"userFullName\", \"name\", \"manager\", \"formControlName\", \"manager\", \"bindValue\", \"userId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [1, \"col-xl-12\", \"mt-4\"], [\"bindLabel\", \"userFullName\", \"name\", \"externalPM\", \"formControlName\", \"externalPM\", \"bindValue\", \"userId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n      template: function ProjectPopupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelementContainerStart(3);\n          i0.ɵɵtemplate(4, ProjectPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, ProjectPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n          i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_i_click_7_listener() {\n            return ctx.modal.dismiss();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 6);\n          i0.ɵɵtemplate(9, ProjectPopupComponent_div_9_Template, 7, 0, \"div\", 7)(10, ProjectPopupComponent_div_10_Template, 7, 0, \"div\", 8);\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"ul\", 12)(15, \"li\", 13)(16, \"a\", 14);\n          i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_a_click_16_listener($event) {\n            return ctx.showTab(\"basic\", $event);\n          });\n          i0.ɵɵtext(17, \" Project Details \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"li\", 13)(19, \"a\", 14);\n          i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_a_click_19_listener($event) {\n            return ctx.showTab(\"role\", $event);\n          });\n          i0.ɵɵtext(20, \" Project Manager \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(21, ProjectPopupComponent_form_21_Template, 3, 3, \"form\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\");\n          i0.ɵɵtemplate(24, ProjectPopupComponent_button_24_Template, 2, 0, \"button\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\")(26, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_button_click_26_listener() {\n            return ctx.modal.dismiss();\n          });\n          i0.ɵɵtext(27, \" Cancel \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \" \\u00A0 \");\n          i0.ɵɵtemplate(29, ProjectPopupComponent_button_29_Template, 2, 2, \"button\", 19)(30, ProjectPopupComponent_button_30_Template, 2, 0, \"button\", 20);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.projectForm && !ctx.isLoading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.selectedTab === \"basic\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.selectedTab === \"role\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.projectForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab != \"basic\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab != \"basic\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i9.NgSelectComponent],\n      encapsulation: 2\n    });\n  }\n  return ProjectPopupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}