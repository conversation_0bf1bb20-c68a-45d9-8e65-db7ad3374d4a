import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, from, mergeMap, of } from 'rxjs';
import { AppSettings } from 'src/app/app.settings';

@Injectable({
  providedIn: 'root'
})
export class PermitsService {

  constructor(
    private http: HttpClient
  ) { }

  private handleError<T>(operation = 'operation', result?: any) {
    return (error: any): Observable<any> => {
      // TODO: send the error to remote logging infrastructure
      console.error(error); // log to console instead

      // Let the app keep running by returning an empty result.
      return from(result);
    };
  }

  public getAllPermits(queryParams: any): Observable<any> {
    const requestBody = {
      pageSize: queryParams.pageSize,
      pageNumber: queryParams.pageNumber,
      sortField: queryParams.sortField,
      sortOrder: queryParams.sortOrder,
      paginate: true,
      search: queryParams.filter?.search || '',
      columnFilter: queryParams.filter?.columnFilter || []
    };

    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllPermits`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getAllPermits:', err);
          return of({
            error: { isFault: true },
            message: 'Error retrieving Permits'
          });
        })
      );
  }

  public getPermitsForKendoGrid(state: any): Observable<any> {
    const requestBody = {
      take: state.take || 10,
      skip: state.skip || 0,
      sort: state.sort || [],
      filter: state.filter || { logic: 'and', filters: [] },
      search: state.search || '',
      loggedInUserId: state.loggedInUserId
    };

    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsForKendoGrid`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getPermitsForKendoGrid:', err);
          return of({
            data: [],
            total: 0,
            errors: ['Error retrieving Permits']
          });
        })
      );
  }

  public createPermit(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/createPermit', data);
  }

  public updatePermit(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/editPermit', data);
  }

  public checkPermitNumberExists(permitNumber: string, permitId?: number): Observable<any> {
    const params = { permitNumber: permitNumber, permitId: permitId };
    return this.http.post(AppSettings.REST_ENDPOINT + '/checkPermitNumberExists', params);
  }

  public updatePermitInternalReviewStatus(data: { permitId: number; internalReviewStatus: string }): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/updatePermitInternalReviewStatus', data);
  }

  public getPermit(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/getPermitById', data);
  }

  public deletePermit(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/deletePermit', data);
  }

  public searchPermits(searchTerm: string): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/searchPermits`, { search: searchTerm });
  }

  public exportPermits(exportType: string, selectedIds?: number[]): Observable<any> {
    const requestBody = {
      exportType: exportType,
      selectedIds: selectedIds || []
    };
    return this.http.post(`${AppSettings.REST_ENDPOINT}/exportPermits`, requestBody);
  }

  public getAllMunicipalities(params: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllMunicipalities`, params);
  }

  public getAllReviews(params: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllReviews`, params);
  }

  public syncPermits(params: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/syncPermits`, params);
  }

  // Internal Reviews Methods
  public addInternalReview(data: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/addInternalReview`, data);
  }

  public updateInternalReview(data: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateInternalReview`, data);
  }

  public getInternalReviews(params: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getInternalReviews`, params);
  }
  public updateExternalReview(params: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateExternalReview`, params);
  }

    public getPermitDetails(params: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitDetails`, params);
  }

   public editNotesAndActions(params: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/editNotesAndActions`, params);
  }

}
