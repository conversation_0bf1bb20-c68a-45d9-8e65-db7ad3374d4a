{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/custom-layout.utils.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Permit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Permit - \", ctx_r0.permitNumber, \"\");\n  }\n}\nfunction PermitPopupComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"span\", 22);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 22);\n    i0.ɵɵtext(5, \"This may take longer than usual....\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Project is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_8_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit / Sub Project Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_16_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Number is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_24_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.permitNumberError, \" \");\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \" Checking permit number availability... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Internal Review Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_33_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_40_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_48_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Review Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_48_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Municipality is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_54_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"div\", 9)(3, \"label\", 24);\n    i0.ɵɵtext(4, \"Project \");\n    i0.ɵɵelementStart(5, \"span\", 25);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"ng-select\", 26);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_21_Template_ng_select_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onProjectChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, PermitPopupComponent_ng_container_21_div_8_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 23)(10, \"div\", 9)(11, \"label\", 24);\n    i0.ɵɵtext(12, \"Permit / Sub Project Name \");\n    i0.ɵɵelementStart(13, \"span\", 25);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(15, \"input\", 28);\n    i0.ɵɵtemplate(16, PermitPopupComponent_ng_container_21_div_16_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 23)(18, \"div\", 29)(19, \"label\", 24);\n    i0.ɵɵtext(20, \"Permit Number \");\n    i0.ɵɵelementStart(21, \"span\", 25);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(23, \"input\", 30);\n    i0.ɵɵtemplate(24, PermitPopupComponent_ng_container_21_div_24_Template, 2, 1, \"div\", 27)(25, PermitPopupComponent_ng_container_21_div_25_Template, 2, 1, \"div\", 27)(26, PermitPopupComponent_ng_container_21_div_26_Template, 3, 0, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 29)(28, \"label\", 24);\n    i0.ɵɵtext(29, \"Internal Review Status \");\n    i0.ɵɵelementStart(30, \"span\", 25);\n    i0.ɵɵtext(31, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(32, \"ng-select\", 32);\n    i0.ɵɵtemplate(33, PermitPopupComponent_ng_container_21_div_33_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 29)(35, \"label\", 24);\n    i0.ɵɵtext(36, \"Permit Category \");\n    i0.ɵɵelementStart(37, \"span\", 25);\n    i0.ɵɵtext(38, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(39, \"ng-select\", 33);\n    i0.ɵɵtemplate(40, PermitPopupComponent_ng_container_21_div_40_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 23)(42, \"div\", 34)(43, \"label\", 24);\n    i0.ɵɵtext(44, \"Permit Review Type\");\n    i0.ɵɵelementStart(45, \"span\", 25);\n    i0.ɵɵtext(46, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"ng-select\", 35);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_21_Template_ng_select_change_47_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitReviewTypeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(48, PermitPopupComponent_ng_container_21_div_48_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 34)(50, \"label\", 24);\n    i0.ɵɵtext(51, \"Permit Municipality (External Review)\");\n    i0.ɵɵtemplate(52, PermitPopupComponent_ng_container_21_span_52_Template, 2, 0, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"ng-select\", 37);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_21_Template_ng_select_change_53_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitMunicipalityChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, PermitPopupComponent_ng_container_21_div_54_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_13_0;\n    let tmp_17_0;\n    let tmp_19_0;\n    let tmp_24_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.projects)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r0.permitNumberError || ((tmp_6_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_6_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_7_0.touched) && ((tmp_7_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_7_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.permitNumberError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isCheckingPermitNumber);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.internalStatusArray)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_13_0.touched) && ((tmp_13_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_13_0.invalid));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.categories)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_17_0.touched) && ((tmp_17_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_17_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.reviewTypeArray);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_19_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_19_0.touched) && ((tmp_19_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_19_0.invalid));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPermitMunicipalRequired);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"items\", ctx_r0.muncipalities)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_24_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_24_0.touched) && ((tmp_24_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_24_0.invalid));\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_ng_container_22_div_1_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.syncPermitDetails());\n    });\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2, \" Sync \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.getSyncButtonDisableStatus());\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_1_button_1_Template, 3, 1, \"button\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.value) !== \"Internal\" && ((tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.value) !== \"\" && ((tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.value) !== null);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Review Responsible Party is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_9_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_20_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Permit Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_28_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Applied Date is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_39_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_60_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Location is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_22_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_60_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_22_div_1_Template, 2, 1, \"div\", 41);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"div\", 29)(4, \"label\", 24);\n    i0.ɵɵtext(5, \"Review Responsible Party\");\n    i0.ɵɵelementStart(6, \"span\", 25);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"input\", 42);\n    i0.ɵɵtemplate(9, PermitPopupComponent_ng_container_22_div_9_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"label\", 24);\n    i0.ɵɵtext(12, \"Primary Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"label\", 24);\n    i0.ɵɵtext(16, \"Permit Status \");\n    i0.ɵɵelementStart(17, \"span\", 25);\n    i0.ɵɵtext(18, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(19, \"ng-select\", 44);\n    i0.ɵɵtemplate(20, PermitPopupComponent_ng_container_22_div_20_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 23)(22, \"div\", 29)(23, \"label\", 24);\n    i0.ɵɵtext(24, \"Permit Type \");\n    i0.ɵɵelementStart(25, \"span\", 25);\n    i0.ɵɵtext(26, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(27, \"ng-select\", 45);\n    i0.ɵɵtemplate(28, PermitPopupComponent_ng_container_22_div_28_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 29)(30, \"label\", 24);\n    i0.ɵɵtext(31, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 29)(34, \"label\", 24);\n    i0.ɵɵtext(35, \"Applied Date \");\n    i0.ɵɵelementStart(36, \"span\", 25);\n    i0.ɵɵtext(37, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(38, \"input\", 47);\n    i0.ɵɵtemplate(39, PermitPopupComponent_ng_container_22_div_39_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 23)(41, \"div\", 29)(42, \"label\", 24);\n    i0.ɵɵtext(43, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"input\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 29)(46, \"label\", 24);\n    i0.ɵɵtext(47, \"Completed Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"input\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 29)(50, \"label\", 24);\n    i0.ɵɵtext(51, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"input\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 23)(54, \"div\", 9)(55, \"label\", 24);\n    i0.ɵɵtext(56, \"Location\");\n    i0.ɵɵelementStart(57, \"span\", 25);\n    i0.ɵɵtext(58, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(59, \"input\", 51);\n    i0.ɵɵtemplate(60, PermitPopupComponent_ng_container_22_div_60_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 23)(62, \"div\", 9)(63, \"label\", 24);\n    i0.ɵɵtext(64, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(65, \"textarea\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_6_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id === 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.invalid));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", ctx_r0.statuses)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_6_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.permitTypes)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_10_0.touched) && ((tmp_10_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_10_0.invalid));\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_11_0.touched) && ((tmp_11_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_11_0.invalid));\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_12_0.touched) && ((tmp_12_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_12_0.invalid));\n  }\n}\nfunction PermitPopupComponent_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1, \" Save \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.permitForm.invalid);\n  }\n}\nfunction PermitPopupComponent_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToNextTab());\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PermitPopupComponent {\n  modal;\n  fb;\n  projectsService;\n  permitsService;\n  appService;\n  httpUtilService;\n  customLayoutUtilsService;\n  cdr;\n  id = 0; // 0 = Add, otherwise Edit\n  permit; // incoming permit data (for edit)\n  passEntry = new EventEmitter();\n  permitForm;\n  projects = [];\n  reviewTypeArray = ['Internal', 'External', 'Both'];\n  loginUser = {};\n  isLoading = false;\n  muncipalities = [];\n  selectedTab = 'basic';\n  permitNumberError = '';\n  isCheckingPermitNumber = false;\n  subscriptions = [];\n  // dropdown options\n  permitTypes = ['Access Control System - Commercial', 'Addition - Commercial', 'Addition - Residential', 'Backflow - Commercial', 'Building Miscellaneous - Commercial', 'Building Move Permit - Residential', 'Building Revisions - Commercial Revision', 'Certificate of Completion', 'Certificate of Occupancy - Commercial', 'Commercial - LV Data Voice Cable Sub-Permit', 'Demolition - Commercial', 'Document Submittal - Commercial Building', 'Electrical Sub-Permit - Commercial', 'Engineering Construction Traffic & Parking Management Plan', 'Fence - Commercial', 'Fire Alarm - Fire', 'Fire Sprinkler/Fire Suppression - Fire', 'Foundation Only - Commercial', 'Gas Sub-Permit - Commercial', 'General Electrical - Commercial', 'General Paving - Paving', 'General Sign Permit', 'Generator - Commercial', 'Interceptor - Commercial', 'Interior (<5000 sq ft) - Commercial', 'Irrigation - Commercial', 'Landscape Non-Residential and Multi-Family', 'Low Voltage - Commercial', 'Mechanical Sub-Permit - Commercial', 'Monument - Sign', 'Mural - Sign', 'New Building - Commercial', 'Plumbing Sub-Permit - Commercial', 'Pool Plumbing Commercial (Sub-Permit)', 'Public Art Permit Application', 'Remodel - Commercial', 'Right-of-Way | ENG A - General', 'Sewer Cap for Demo - Commercial', 'Windows and Doors - Commercial'];\n  categories = ['Primary', 'Sub Permit', 'Industrial', 'Municipal', 'Environmental'];\n  statuses = ['Canceled', 'Complete', 'Expired', 'Fees Due', 'In Review', 'Issued', 'On Hold', 'Requires Resubmit', 'Requires Resubmit for Prescreen', 'Submitted - Online', 'Void'];\n  internalStatusArray = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n  permitNumber;\n  isPermitMunicipalRequired = false;\n  iscityreviewLinkMunicipalRequired = false;\n  cityReviewLink = '';\n  syncedPermitData = {};\n  constructor(modal, fb, projectsService, permitsService, appService, httpUtilService, customLayoutUtilsService, cdr) {\n    this.modal = modal;\n    this.fb = fb;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.appService = appService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.cdr = cdr;\n    // Subscribe to loading state\n    const loadingSubscription = this.httpUtilService.loadingSubject.subscribe(loading => {\n      console.log('Permit popup - loading state changed:', loading);\n      this.isLoading = loading;\n    });\n    this.subscriptions.push(loadingSubscription);\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadMunicipalities();\n    this.loadForm();\n    this.loadProjects();\n    this.setupPermitNumberValidation();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n  }\n  loadForm() {\n    this.permitForm = this.fb.group({\n      projectId: ['', Validators.required],\n      permitName: ['', Validators.required],\n      permitNumber: ['', Validators.required],\n      permitCategory: ['', Validators.required],\n      permitType: ['', Validators.required],\n      description: [''],\n      permitReviewType: ['', Validators.required],\n      location: ['', Validators.required],\n      primaryContact: [''],\n      permitAppliedDate: ['', Validators.required],\n      permitExpirationDate: [''],\n      permitIssueDate: [''],\n      permitFinalDate: [''],\n      permitCompleteDate: [''],\n      permitStatus: ['', Validators.required],\n      internalReviewStatus: ['', Validators.required],\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: [''],\n      reviewResponsibleParty: ['', Validators.required],\n      permitMunicipality: ['']\n      // cityReviewLink: [''],\n    });\n  }\n  loadProjects() {\n    this.httpUtilService.setLoadingState(true);\n    const params = {\n      paginate: false\n    };\n    const subscription = this.projectsService.getAllProjectsData(params).subscribe({\n      next: response => {\n        this.httpUtilService.setLoadingState(false);\n        if (response && response.responseData) {\n          this.projects = response.responseData.data;\n        }\n      },\n      error: error => {\n        this.httpUtilService.setLoadingState(false);\n        console.error('Error loading projects:', error);\n        this.projects = [];\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  loadMunicipalities() {\n    this.httpUtilService.setLoadingState(true);\n    const params = {\n      loggedinUser: this.loginUser.userId\n    };\n    const subscription = this.permitsService.getAllMunicipalities(params).subscribe({\n      next: response => {\n        this.httpUtilService.setLoadingState(false);\n        if (response && response.responseData) {\n          this.muncipalities = response.responseData.data;\n        }\n      },\n      error: error => {\n        this.httpUtilService.setLoadingState(false);\n        console.error('Error loading projects:', error);\n        this.muncipalities = [];\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  patchForm() {\n    this.httpUtilService.setLoadingState(true);\n    const subscription = this.permitsService.getPermit({\n      permitId: this.id,\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: permitResponse => {\n        this.httpUtilService.setLoadingState(false);\n        if (!permitResponse.isFault) {\n          let permitData = permitResponse.responseData.data;\n          this.permitNumber = permitData.permitNumber;\n          this.permitForm.patchValue({\n            projectId: permitData.projectId,\n            permitNumber: permitData.permitNumber,\n            permitReviewType: permitData.permitReviewType,\n            permitCategory: permitData.permitCategory,\n            permitType: permitData.permitType,\n            description: permitData.description,\n            permitName: permitData.permitName,\n            location: permitData.location,\n            internalReviewStatus: permitData.internalReviewStatus,\n            primaryContact: permitData.primaryContact,\n            permitAppliedDate: permitData.permitAppliedDate ? this.formatDateForInput(permitData.permitAppliedDate) : '',\n            permitExpirationDate: permitData.permitExpirationDate ? this.formatDateForInput(permitData.permitExpirationDate) : '',\n            permitIssueDate: permitData.permitIssueDate ? this.formatDateForInput(permitData.permitIssueDate) : '',\n            permitFinalDate: permitData.permitFinalDate ? this.formatDateForInput(permitData.permitFinalDate) : '',\n            permitCompleteDate: permitData.permitCompleteDate ? this.formatDateForInput(permitData.permitCompleteDate) : '',\n            permitStatus: permitData.permitStatus,\n            attentionReason: permitData.attentionReason,\n            internalNotes: permitData.internalNotes,\n            actionTaken: permitData.actionTaken,\n            reviewResponsibleParty: permitData.reviewResponsibleParty,\n            permitMunicipality: permitData.permitMunicipality\n            // cityReviewLink: permitData.cityReviewLink,\n          });\n          this.onPermitReviewTypeChange(permitData.permitReviewType);\n        } else {\n          console.warn('Permit response has isFault = true', permitResponse.responseData);\n        }\n      },\n      error: err => {\n        this.httpUtilService.setLoadingState(false);\n        console.error('API call failed', err);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  formatDateForInput(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    // Use local timezone to avoid date shifting\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  preparePermitData() {\n    const formData = this.permitForm.value;\n    let permitRequestData = {};\n    permitRequestData.projectId = formData.projectId;\n    permitRequestData.permitNumber = formData.permitNumber;\n    permitRequestData.permitReviewType = formData.permitReviewType;\n    permitRequestData.permitCategory = formData.permitCategory;\n    permitRequestData.permitType = formData.permitType;\n    permitRequestData.description = formData.description;\n    permitRequestData.location = formData.location;\n    permitRequestData.primaryContact = formData.primaryContact;\n    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;\n    permitRequestData.permitExpirationDate = formData.permitExpirationDate || null;\n    permitRequestData.permitIssueDate = formData.permitIssueDate || null;\n    permitRequestData.permitFinalDate = formData.permitFinalDate || null;\n    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;\n    permitRequestData.permitStatus = formData.permitStatus;\n    permitRequestData.internalReviewStatus = formData.internalReviewStatus;\n    permitRequestData.permitName = formData.permitName;\n    permitRequestData.attentionReason = formData.attentionReason;\n    permitRequestData.internalNotes = formData.internalNotes;\n    permitRequestData.actionTaken = formData.actionTaken;\n    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;\n    permitRequestData.permitMunicipality = formData.permitMunicipality;\n    permitRequestData.cityReviewLink = this.cityReviewLink;\n    permitRequestData.loggedInUserId = this.loginUser.userId;\n    permitRequestData.syncedPermitData = this.syncedPermitData;\n    const caseId = this.syncedPermitData.EntityId || this.syncedPermitData.permitId || this.syncedPermitData.Id || null;\n    permitRequestData.permitEntityID = caseId;\n    if (this.id !== 0) {\n      permitRequestData.permitId = this.id;\n    }\n    return permitRequestData;\n  }\n  save() {\n    let controls = this.permitForm.controls;\n    console.log('Permit Data:', this.permitForm.value);\n    // Check for duplicate permit number error\n    if (this.permitNumberError) {\n      this.customLayoutUtilsService.showError(this.permitNumberError, '');\n      return;\n    }\n    if (this.permitForm.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      this.customLayoutUtilsService.showError('Please fill all required fields', '');\n      return;\n    }\n    let permitData = this.preparePermitData();\n    console.log('Permit Data:', permitData);\n    if (this.id === 0) {\n      this.create(permitData);\n    } else {\n      this.edit(permitData);\n    }\n  }\n  create(permitData) {\n    this.httpUtilService.setLoadingState(true);\n    const subscription = this.permitsService.createPermit(permitData).subscribe(res => {\n      this.httpUtilService.setLoadingState(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  edit(permitData) {\n    this.httpUtilService.setLoadingState(true);\n    const subscription = this.permitsService.updatePermit(permitData).subscribe(res => {\n      this.httpUtilService.setLoadingState(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'notes';\n    }\n    this.cdr.markForCheck();\n  }\n  goToPreviousTab() {\n    if (this.selectedTab === 'notes') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'basic';\n    }\n    this.cdr.markForCheck();\n  }\n  onProjectChange(event) {\n    this.permitForm.patchValue({\n      location: event.projectLocation\n    });\n    // console.log(\"project loacation\",event)\n  }\n  onPermitReviewTypeChange(event) {\n    const permitControl = this.permitForm.get('permitMunicipality');\n    // const cityReviewControl = this.permitForm.get('cityReviewLink');\n    if (event !== 'Internal') {\n      this.isPermitMunicipalRequired = true;\n      // this.iscityreviewLinkMunicipalRequired = true;\n      permitControl?.setValidators([Validators.required]);\n      // cityReviewControl?.setValidators([Validators.required]);\n    } else {\n      this.isPermitMunicipalRequired = false;\n      // this.iscityreviewLinkMunicipalRequired = false;\n      permitControl?.clearValidators();\n      // cityReviewControl?.clearValidators();\n    }\n    permitControl?.updateValueAndValidity();\n    // cityReviewControl?.updateValueAndValidity();\n  }\n  onPermitMunicipalityChange(event) {\n    console.log('event 0 ', event);\n    this.cityReviewLink = event.cityWebsiteLink + '#/permit/';\n    this.getSyncButtonDisableStatus();\n  }\n  syncPermitDetails() {\n    const formData = this.permitForm.value;\n    this.httpUtilService.setLoadingState(true);\n    const subscription = this.permitsService.getPermitDetails({\n      permitNumber: formData.permitNumber,\n      municipalityId: formData.permitMunicipality\n    }).subscribe({\n      next: permitResponse => {\n        this.httpUtilService.setLoadingState(false);\n        if (!permitResponse.isFault) {\n          let permitData = permitResponse.responseData.permit;\n          this.syncedPermitData = permitData;\n          this.permitForm.patchValue({\n            permitType: permitData.permitType,\n            description: permitData.description,\n            location: permitData.address,\n            permitAppliedDate: permitData.applyDate ? this.formatDateForInput(permitData.applyDate) : '',\n            permitExpirationDate: permitData.expireDate ? this.formatDateForInput(permitData.expireDate) : '',\n            permitIssueDate: permitData.issueDate ? this.formatDateForInput(permitData.issueDate) : '',\n            permitFinalDate: permitData.finalDate ? this.formatDateForInput(permitData.finalDate) : '',\n            permitCompleteDate: permitData.completeDate ? this.formatDateForInput(permitData.completeDate) : '',\n            permitStatus: permitData.permitStatus\n          });\n        } else {\n          console.warn('Permit response has isFault = true', permitResponse.responseData.message);\n        }\n      },\n      error: err => {\n        this.httpUtilService.setLoadingState(false);\n        console.error('API call failed', err);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  getSyncButtonDisableStatus() {\n    const reviewType = this.permitForm.get('permitReviewType')?.value;\n    const permitNumber = this.permitForm.get('permitNumber')?.value;\n    const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;\n    const isReviewTypeValid = !reviewType || reviewType !== 'Internal';\n    const hasPermitNumber = !!permitNumber;\n    const hasPermitMunicipality = !!permitMunicipality;\n    console.log('isReviewTypeValid ', isReviewTypeValid);\n    console.log('hasPermitNumber ', hasPermitNumber);\n    console.log('hasPermitMunicipality ', hasPermitMunicipality);\n    // Disable if any of the conditions are not satisfied\n    return !(isReviewTypeValid && hasPermitNumber && hasPermitMunicipality);\n  }\n  setupPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    if (permitNumberControl) {\n      permitNumberControl.valueChanges.subscribe(value => {\n        if (value && value.trim()) {\n          this.checkPermitNumber(value.trim());\n        } else {\n          this.permitNumberError = '';\n        }\n      });\n    }\n  }\n  checkPermitNumber(permitNumber) {\n    if (this.isCheckingPermitNumber) return;\n    this.isCheckingPermitNumber = true;\n    this.permitNumberError = '';\n    const subscription = this.permitsService.checkPermitNumberExists(permitNumber, this.id || undefined).subscribe({\n      next: response => {\n        this.isCheckingPermitNumber = false;\n        if (response && response.responseData) {\n          if (response.responseData.exists) {\n            this.permitNumberError = 'This permit number already exists. Please use a different permit number.';\n            this.permitForm.get('permitNumber')?.setErrors({\n              'duplicate': true\n            });\n          } else {\n            this.permitNumberError = '';\n            const currentErrors = this.permitForm.get('permitNumber')?.errors;\n            if (currentErrors && currentErrors['duplicate']) {\n              delete currentErrors['duplicate'];\n              this.permitForm.get('permitNumber')?.setErrors(Object.keys(currentErrors).length ? currentErrors : null);\n            }\n          }\n        }\n      },\n      error: error => {\n        this.isCheckingPermitNumber = false;\n        console.error('Error checking permit number:', error);\n        this.permitNumberError = 'Error checking permit number availability';\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  // Method to handle modal dismiss and reset loading state\n  dismissModal() {\n    console.log('Permit popup - dismissModal called - resetting loading state');\n    this.httpUtilService.setLoadingState(false);\n    // Add a small delay to ensure loading state is reset\n    setTimeout(() => {\n      console.log('Permit popup - ensuring loading state is reset after dismiss');\n      this.httpUtilService.setLoadingState(false);\n    }, 50);\n    this.modal.dismiss();\n  }\n  ngOnDestroy() {\n    console.log('Permit popup - ngOnDestroy called, cleaning up subscriptions');\n    // Unsubscribe from all subscriptions to prevent memory leaks\n    this.subscriptions.forEach(subscription => {\n      if (subscription && !subscription.closed) {\n        subscription.unsubscribe();\n      }\n    });\n    this.subscriptions = [];\n    // Ensure loading state is reset when component is destroyed\n    console.log('Permit popup - resetting loading state in ngOnDestroy');\n    this.httpUtilService.setLoadingState(false);\n    // Add a more aggressive approach to ensure loading state is reset\n    setTimeout(() => {\n      console.log('Permit popup - final loading state reset in ngOnDestroy');\n      this.httpUtilService.setLoadingState(false);\n    }, 100);\n  }\n  static ɵfac = function PermitPopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitPopupComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.AppService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitPopupComponent,\n    selectors: [[\"app-permit-popup\"]],\n    inputs: {\n      id: \"id\",\n      permit: \"permit\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 32,\n    vars: 15,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\", \"large-modal-body\"], [\"class\", \"loading-overlay-inside\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"modal-footer\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"loading-overlay-inside\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"row\", \"mt-4\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"bindLabel\", \"projectName\", \"formControlName\", \"projectId\", \"bindValue\", \"projectId\", \"placeholder\", \"Select Project\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"class\", \"text-danger mt-1 small\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"permitName\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-4\"], [\"type\", \"text\", \"formControlName\", \"permitNumber\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"text-info mt-1 small\", 4, \"ngIf\"], [\"formControlName\", \"internalReviewStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"formControlName\", \"permitCategory\", \"placeholder\", \"Select Category\", 3, \"items\", \"clearable\", \"multiple\"], [1, \"col-xl-6\"], [\"formControlName\", \"permitReviewType\", 3, \"change\", \"items\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"bindLabel\", \"cityName\", \"formControlName\", \"permitMunicipality\", \"bindValue\", \"municipalityId\", \"placeholder\", \"Select Project\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [1, \"text-danger\", \"mt-1\", \"small\"], [1, \"text-info\", \"mt-1\", \"small\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [\"class\", \"w-100 d-flex justify-content-end\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"reviewResponsibleParty\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"primaryContact\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"permitStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"formControlName\", \"permitType\", \"placeholder\", \"Select Type\", 3, \"items\", \"clearable\", \"multiple\"], [\"type\", \"date\", \"formControlName\", \"permitIssueDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitAppliedDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitExpirationDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitCompleteDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitFinalDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"description\", 1, \"form-control\", \"form-control-sm\"], [1, \"w-100\", \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-sync-alt\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n    template: function PermitPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, PermitPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, PermitPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_i_click_7_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6);\n        i0.ɵɵtemplate(9, PermitPopupComponent_div_9_Template, 6, 0, \"div\", 7);\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"ul\", 11)(14, \"li\", 12)(15, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_15_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(16, \" Basic Info \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"li\", 12)(18, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_18_listener($event) {\n          return ctx.showTab(\"details\", $event);\n        });\n        i0.ɵɵtext(19, \" Permit Details \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(20, \"form\", 14);\n        i0.ɵɵtemplate(21, PermitPopupComponent_ng_container_21_Template, 55, 25, \"ng-container\", 3)(22, PermitPopupComponent_ng_container_22_Template, 66, 12, \"ng-container\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\");\n        i0.ɵɵtemplate(25, PermitPopupComponent_button_25_Template, 2, 0, \"button\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\")(27, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_button_click_27_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵtext(28, \" Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(29, \"\\u00A0 \");\n        i0.ɵɵtemplate(30, PermitPopupComponent_button_30_Template, 2, 1, \"button\", 18)(31, PermitPopupComponent_button_31_Template, 2, 0, \"button\", 19);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, ctx.selectedTab === \"details\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.permitForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i9.NgSelectComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "permitNumber", "ɵɵtemplate", "PermitPopupComponent_ng_container_21_div_8_div_1_Template", "ɵɵproperty", "tmp_2_0", "permitForm", "get", "errors", "PermitPopupComponent_ng_container_21_div_16_div_1_Template", "PermitPopupComponent_ng_container_21_div_24_div_1_Template", "permitNumberError", "ɵɵelement", "PermitPopupComponent_ng_container_21_div_33_div_1_Template", "PermitPopupComponent_ng_container_21_div_40_div_1_Template", "PermitPopupComponent_ng_container_21_div_48_div_1_Template", "PermitPopupComponent_ng_container_21_div_54_div_1_Template", "ɵɵelementContainerStart", "ɵɵlistener", "PermitPopupComponent_ng_container_21_Template_ng_select_change_7_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onProjectChange", "PermitPopupComponent_ng_container_21_div_8_Template", "PermitPopupComponent_ng_container_21_div_16_Template", "PermitPopupComponent_ng_container_21_div_24_Template", "PermitPopupComponent_ng_container_21_div_25_Template", "PermitPopupComponent_ng_container_21_div_26_Template", "PermitPopupComponent_ng_container_21_div_33_Template", "PermitPopupComponent_ng_container_21_div_40_Template", "PermitPopupComponent_ng_container_21_Template_ng_select_change_47_listener", "onPermitReviewTypeChange", "PermitPopupComponent_ng_container_21_div_48_Template", "PermitPopupComponent_ng_container_21_span_52_Template", "PermitPopupComponent_ng_container_21_Template_ng_select_change_53_listener", "onPermitMunicipalityChange", "PermitPopupComponent_ng_container_21_div_54_Template", "projects", "tmp_4_0", "touched", "invalid", "tmp_5_0", "ɵɵclassProp", "tmp_6_0", "tmp_7_0", "isCheckingPermitNumber", "internalStatusArray", "tmp_13_0", "categories", "tmp_17_0", "reviewTypeArray", "tmp_19_0", "isPermitMunicipalRequired", "muncipalities", "tmp_24_0", "PermitPopupComponent_ng_container_22_div_1_button_1_Template_button_click_0_listener", "_r3", "syncPermitDetails", "getSyncButtonDisableStatus", "PermitPopupComponent_ng_container_22_div_1_button_1_Template", "value", "PermitPopupComponent_ng_container_22_div_9_div_1_Template", "PermitPopupComponent_ng_container_22_div_20_div_1_Template", "PermitPopupComponent_ng_container_22_div_28_div_1_Template", "PermitPopupComponent_ng_container_22_div_39_div_1_Template", "PermitPopupComponent_ng_container_22_div_60_div_1_Template", "PermitPopupComponent_ng_container_22_div_1_Template", "PermitPopupComponent_ng_container_22_div_9_Template", "PermitPopupComponent_ng_container_22_div_20_Template", "PermitPopupComponent_ng_container_22_div_28_Template", "PermitPopupComponent_ng_container_22_div_39_Template", "PermitPopupComponent_ng_container_22_div_60_Template", "id", "statuses", "permitTypes", "tmp_10_0", "tmp_11_0", "tmp_12_0", "PermitPopupComponent_button_25_Template_button_click_0_listener", "_r4", "goToPreviousTab", "PermitPopupComponent_button_30_Template_button_click_0_listener", "_r5", "save", "PermitPopupComponent_button_31_Template_button_click_0_listener", "_r6", "goToNextTab", "PermitPopupComponent", "modal", "fb", "projectsService", "permitsService", "appService", "httpUtilService", "customLayoutUtilsService", "cdr", "permit", "passEntry", "loginUser", "isLoading", "selectedTab", "subscriptions", "iscityreviewLinkMunicipalRequired", "cityReviewLink", "syncedPermitData", "constructor", "loadingSubscription", "loadingSubject", "subscribe", "loading", "console", "log", "push", "ngOnInit", "getLoggedInUser", "loadMunicipalities", "loadForm", "loadProjects", "setupPermitNumberValidation", "patchForm", "group", "projectId", "required", "permitName", "permitCategory", "permitType", "description", "permitReviewType", "location", "primaryContact", "permitAppliedDate", "permitExpirationDate", "permitIssueDate", "permitFinalDate", "permitCompleteDate", "permitStatus", "internalReviewStatus", "attentionReason", "internalNotes", "actionTaken", "reviewResponsibleParty", "permitMunicipality", "setLoadingState", "params", "paginate", "subscription", "getAllProjectsData", "next", "response", "responseData", "data", "error", "loggedinUser", "userId", "getAllMunicipalities", "get<PERSON><PERSON><PERSON>", "permitId", "loggedInUserId", "permitResponse", "<PERSON><PERSON><PERSON>", "permitData", "patchValue", "formatDateForInput", "warn", "err", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "preparePermitData", "formData", "permitRequestData", "caseId", "EntityId", "Id", "permitEntityID", "controls", "showError", "Object", "keys", "for<PERSON>ach", "controlName", "<PERSON><PERSON><PERSON><PERSON>ched", "create", "edit", "createPermit", "res", "showSuccess", "message", "emit", "close", "updatePermit", "showTab", "tab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "projectLocation", "permitControl", "setValidators", "clearValidators", "updateValueAndValidity", "cityWebsiteLink", "getPermitDetails", "municipalityId", "address", "applyDate", "expireDate", "issueDate", "finalDate", "completeDate", "reviewType", "isReviewTypeValid", "hasPermitNumber", "hasPermitMunicipality", "permitNumberControl", "valueChanges", "trim", "checkPermitNumber", "checkPermitNumberExists", "undefined", "exists", "setErrors", "currentErrors", "length", "dismissModal", "setTimeout", "dismiss", "ngOnDestroy", "closed", "unsubscribe", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "FormBuilder", "i3", "ProjectsService", "i4", "PermitsService", "i5", "AppService", "i6", "HttpUtilsService", "i7", "CustomLayoutUtilsService", "ChangeDetectorRef", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "PermitPopupComponent_Template", "rf", "ctx", "PermitPopupComponent_div_4_Template", "PermitPopupComponent_div_5_Template", "PermitPopupComponent_Template_i_click_7_listener", "PermitPopupComponent_div_9_Template", "PermitPopupComponent_Template_a_click_15_listener", "PermitPopupComponent_Template_a_click_18_listener", "PermitPopupComponent_ng_container_21_Template", "PermitPopupComponent_ng_container_22_Template", "PermitPopupComponent_button_25_Template", "PermitPopupComponent_Template_button_click_27_listener", "PermitPopupComponent_button_30_Template", "PermitPopupComponent_button_31_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.html"], "sourcesContent": ["import {\n  Component,\n  Input,\n  Output,\n  EventEmitter,\n  ChangeDetectorRef,\n  OnDestroy,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ProjectsService } from '../../services/projects.service';\nimport { PermitsService } from '../../services/permits.service';\nimport { AppService } from '../../services/app.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { even } from '@rxweb/reactive-form-validators';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-permit-popup',\n  templateUrl: './permit-popup.component.html',\n})\nexport class PermitPopupComponent implements OnDestroy {\n  @Input() id: number = 0; // 0 = Add, otherwise Edit\n  @Input() permit: any; // incoming permit data (for edit)\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\n\n  permitForm: FormGroup;\n  projects: any[] = [];\n  reviewTypeArray: any[] = ['Internal', 'External', 'Both'];\n  loginUser: any = {};\n  isLoading: boolean = false;\n  muncipalities: any = [];\n  selectedTab: any = 'basic';\n  permitNumberError: string = '';\n  isCheckingPermitNumber: boolean = false;\n  private subscriptions: Subscription[] = [];\n  // dropdown options\n  permitTypes = [\n    'Access Control System - Commercial',\n    'Addition - Commercial',\n    'Addition - Residential',\n    'Backflow - Commercial',\n    'Building Miscellaneous - Commercial',\n    'Building Move Permit - Residential',\n    'Building Revisions - Commercial Revision',\n    'Certificate of Completion',\n    'Certificate of Occupancy - Commercial',\n    'Commercial - LV Data Voice Cable Sub-Permit',\n    'Demolition - Commercial',\n    'Document Submittal - Commercial Building',\n    'Electrical Sub-Permit - Commercial',\n    'Engineering Construction Traffic & Parking Management Plan',\n    'Fence - Commercial',\n    'Fire Alarm - Fire',\n    'Fire Sprinkler/Fire Suppression - Fire',\n    'Foundation Only - Commercial',\n    'Gas Sub-Permit - Commercial',\n    'General Electrical - Commercial',\n    'General Paving - Paving',\n    'General Sign Permit',\n    'Generator - Commercial',\n    'Interceptor - Commercial',\n    'Interior (<5000 sq ft) - Commercial',\n    'Irrigation - Commercial',\n    'Landscape Non-Residential and Multi-Family',\n    'Low Voltage - Commercial',\n    'Mechanical Sub-Permit - Commercial',\n    'Monument - Sign',\n    'Mural - Sign',\n    'New Building - Commercial',\n    'Plumbing Sub-Permit - Commercial',\n    'Pool Plumbing Commercial (Sub-Permit)',\n    'Public Art Permit Application',\n    'Remodel - Commercial',\n    'Right-of-Way | ENG A - General',\n    'Sewer Cap for Demo - Commercial',\n    'Windows and Doors - Commercial',\n  ];\n  categories = [\n    'Primary',\n    'Sub Permit',\n    'Industrial',\n    'Municipal',\n    'Environmental',\n  ];\n  statuses = [\n    'Canceled',\n    'Complete',\n    'Expired',\n    'Fees Due',\n    'In Review',\n    'Issued',\n    'On Hold',\n    'Requires Resubmit',\n    'Requires Resubmit for Prescreen',\n    'Submitted - Online',\n    'Void',\n  ];\n  internalStatusArray =['Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed']\n  permitNumber: any;\n  isPermitMunicipalRequired: boolean = false;\n  iscityreviewLinkMunicipalRequired: boolean=false;\n  cityReviewLink:any ='';\n  syncedPermitData:any ={};\n  constructor(\n    public modal: NgbActiveModal,\n    private fb: FormBuilder,\n    private projectsService: ProjectsService,\n    private permitsService: PermitsService,\n    private appService: AppService,\n    private httpUtilService: HttpUtilsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private cdr: ChangeDetectorRef\n  ) {\n    // Subscribe to loading state\n    const loadingSubscription = this.httpUtilService.loadingSubject.subscribe((loading) => {\n      console.log('Permit popup - loading state changed:', loading);\n      this.isLoading = loading;\n    });\n    this.subscriptions.push(loadingSubscription);\n  }\n\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadMunicipalities();\n    this.loadForm();\n    this.loadProjects();\n    this.setupPermitNumberValidation();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n  }\n\n  loadForm() {\n    this.permitForm = this.fb.group({\n      projectId: ['', Validators.required],\n      permitName: ['', Validators.required],\n      permitNumber: ['', Validators.required],\n      permitCategory: ['', Validators.required],\n      permitType: ['', Validators.required],\n      description: [''],\n      permitReviewType: ['', Validators.required],\n      location: ['',Validators.required],\n      primaryContact: [''],\n      permitAppliedDate: ['', Validators.required],\n      permitExpirationDate: [''],\n      permitIssueDate: [''],\n      permitFinalDate: [''],\n      permitCompleteDate: [''],\n      permitStatus: ['', Validators.required],\n      internalReviewStatus: ['', Validators.required],\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: [''],\n      reviewResponsibleParty: ['', Validators.required],\n      permitMunicipality: [''],\n      // cityReviewLink: [''],\n    });\n  }\n\n  loadProjects() {\n    this.httpUtilService.setLoadingState(true);\n    const params = { paginate: false };\n    const subscription = this.projectsService.getAllProjectsData(params).subscribe({\n      next: (response: any) => {\n        this.httpUtilService.setLoadingState(false);\n        if (response && response.responseData) {\n          this.projects = response.responseData.data;\n        }\n      },\n      error: (error: any) => {\n        this.httpUtilService.setLoadingState(false);\n        console.error('Error loading projects:', error);\n        this.projects = [];\n      },\n    });\n    this.subscriptions.push(subscription);\n  }\n\n  loadMunicipalities() {\n    this.httpUtilService.setLoadingState(true);\n    const params = { loggedinUser: this.loginUser.userId };\n    const subscription = this.permitsService.getAllMunicipalities(params).subscribe({\n      next: (response: any) => {\n        this.httpUtilService.setLoadingState(false);\n        if (response && response.responseData) {\n          this.muncipalities = response.responseData.data;\n        }\n      },\n      error: (error: any) => {\n        this.httpUtilService.setLoadingState(false);\n        console.error('Error loading projects:', error);\n        this.muncipalities = [];\n      },\n    });\n    this.subscriptions.push(subscription);\n  }\n\n  patchForm() {\n    this.httpUtilService.setLoadingState(true);\n    const subscription = this.permitsService\n      .getPermit({ permitId: this.id, loggedInUserId: this.loginUser.userId })\n      .subscribe({\n        next: (permitResponse: any) => {\n          this.httpUtilService.setLoadingState(false);\n          if (!permitResponse.isFault) {\n            let permitData = permitResponse.responseData.data;\n            this.permitNumber = permitData.permitNumber;\n            this.permitForm.patchValue({\n              projectId: permitData.projectId,\n              permitNumber: permitData.permitNumber,\n              permitReviewType: permitData.permitReviewType,\n              permitCategory: permitData.permitCategory,\n              permitType: permitData.permitType,\n              description: permitData.description,\n              permitName: permitData.permitName,\n              location: permitData.location,\n              internalReviewStatus:permitData.internalReviewStatus,\n              primaryContact: permitData.primaryContact,\n              permitAppliedDate: permitData.permitAppliedDate\n                ? this.formatDateForInput(permitData.permitAppliedDate)\n                : '',\n              permitExpirationDate: permitData.permitExpirationDate\n                ? this.formatDateForInput(permitData.permitExpirationDate)\n                : '',\n              permitIssueDate: permitData.permitIssueDate\n                ? this.formatDateForInput(permitData.permitIssueDate)\n                : '',\n                  permitFinalDate: permitData.permitFinalDate\n                ? this.formatDateForInput(permitData.permitFinalDate)\n                : '',\n              permitCompleteDate: permitData.permitCompleteDate\n                ? this.formatDateForInput(permitData.permitCompleteDate)\n                : '',\n              permitStatus: permitData.permitStatus,\n              attentionReason: permitData.attentionReason,\n              internalNotes: permitData.internalNotes,\n              actionTaken: permitData.actionTaken,\n              reviewResponsibleParty: permitData.reviewResponsibleParty,\n              permitMunicipality: permitData.permitMunicipality,\n              // cityReviewLink: permitData.cityReviewLink,\n            });\n            this.onPermitReviewTypeChange(permitData.permitReviewType)\n          } else {\n            console.warn(\n              'Permit response has isFault = true',\n              permitResponse.responseData\n            );\n          }\n        },\n        error: (err) => {\n          this.httpUtilService.setLoadingState(false);\n          console.error('API call failed', err);\n        },\n      });\n    this.subscriptions.push(subscription);\n  }\n\n  formatDateForInput(dateString: string): string {\n    if (!dateString) return '';\n\n    const date = new Date(dateString);\n    // Use local timezone to avoid date shifting\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    return `${year}-${month}-${day}`;\n  }\n\n  preparePermitData() {\n    const formData = this.permitForm.value;\n\n    let permitRequestData: any = {};\n    permitRequestData.projectId = formData.projectId;\n    permitRequestData.permitNumber = formData.permitNumber;\n    permitRequestData.permitReviewType = formData.permitReviewType;\n    permitRequestData.permitCategory = formData.permitCategory;\n    permitRequestData.permitType = formData.permitType;\n    permitRequestData.description = formData.description;\n    permitRequestData.location = formData.location;\n    permitRequestData.primaryContact = formData.primaryContact;\n    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;\n    permitRequestData.permitExpirationDate =\n      formData.permitExpirationDate || null;\n    permitRequestData.permitIssueDate = formData.permitIssueDate || null;\n    permitRequestData.permitFinalDate =\n      formData.permitFinalDate || null;\n    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;\n    permitRequestData.permitStatus = formData.permitStatus;\n    permitRequestData.internalReviewStatus = formData.internalReviewStatus;\n    permitRequestData.permitName = formData.permitName;\n    permitRequestData.attentionReason = formData.attentionReason;\n    permitRequestData.internalNotes = formData.internalNotes;\n    permitRequestData.actionTaken = formData.actionTaken;\n    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;\n    permitRequestData.permitMunicipality = formData.permitMunicipality;\n    permitRequestData.cityReviewLink = this.cityReviewLink;\n    permitRequestData.loggedInUserId = this.loginUser.userId;\n    permitRequestData.syncedPermitData = this.syncedPermitData;\n    const caseId =  this.syncedPermitData.EntityId ||  this.syncedPermitData.permitId ||  this.syncedPermitData.Id || null;\n    permitRequestData.permitEntityID = caseId\n    if (this.id !== 0) {\n      permitRequestData.permitId = this.id;\n    }\n\n    return permitRequestData;\n  }\n\n  save() {\n    let controls = this.permitForm.controls;\n    console.log('Permit Data:', this.permitForm.value);\n    \n    // Check for duplicate permit number error\n    if (this.permitNumberError) {\n      this.customLayoutUtilsService.showError(\n        this.permitNumberError,\n        ''\n      );\n      return;\n    }\n    \n    if (this.permitForm.invalid) {\n      Object.keys(controls).forEach((controlName) =>\n        controls[controlName].markAsTouched()\n      );\n      this.customLayoutUtilsService.showError(\n        'Please fill all required fields',\n        ''\n      );\n      return;\n    }\n    let permitData: any = this.preparePermitData();\n    console.log('Permit Data:', permitData);\n    if (this.id === 0) {\n      this.create(permitData);\n    } else {\n      this.edit(permitData);\n    }\n  }\n\n  create(permitData: any) {\n    this.httpUtilService.setLoadingState(true);\n    const subscription = this.permitsService.createPermit(permitData).subscribe((res: any) => {\n      this.httpUtilService.setLoadingState(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n\n  edit(permitData: any) {\n    this.httpUtilService.setLoadingState(true);\n    const subscription = this.permitsService.updatePermit(permitData).subscribe((res) => {\n      this.httpUtilService.setLoadingState(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n  showTab(tab: any, $event: any) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'notes';\n    }\n    this.cdr.markForCheck();\n  }\n\n  goToPreviousTab() {\n    if (this.selectedTab === 'notes') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'basic';\n    }\n    this.cdr.markForCheck();\n  }\n\n  onProjectChange(event:any){\n     this.permitForm.patchValue({\n              location: event.projectLocation })\n    // console.log(\"project loacation\",event)\n\n\n  }\n\nonPermitReviewTypeChange(event: any) {\n  const permitControl = this.permitForm.get('permitMunicipality');\n  // const cityReviewControl = this.permitForm.get('cityReviewLink');\n\n  if (event !== 'Internal') {\n    this.isPermitMunicipalRequired = true;\n    // this.iscityreviewLinkMunicipalRequired = true;\n\n    permitControl?.setValidators([Validators.required]);\n    // cityReviewControl?.setValidators([Validators.required]);\n  } else {\n    this.isPermitMunicipalRequired = false;\n    // this.iscityreviewLinkMunicipalRequired = false;\n\n    permitControl?.clearValidators();\n    // cityReviewControl?.clearValidators();\n  }\n\n  permitControl?.updateValueAndValidity();\n  // cityReviewControl?.updateValueAndValidity();\n}\n\nonPermitMunicipalityChange(event:any){\n  console.log('event 0 ',event);\n  this.cityReviewLink= event.cityWebsiteLink+'#/permit/';\n  this.getSyncButtonDisableStatus()\n}\n\nsyncPermitDetails(){\n   const formData = this.permitForm.value;\n    this.httpUtilService.setLoadingState(true);\n    const subscription = this.permitsService\n      .getPermitDetails({ permitNumber: formData.permitNumber, municipalityId: formData.permitMunicipality })\n      .subscribe({\n        next: (permitResponse: any) => {\n          this.httpUtilService.setLoadingState(false);\n          if (!permitResponse.isFault) {\n            let permitData = permitResponse.responseData.permit;\n            this.syncedPermitData = permitData\n            this.permitForm.patchValue({\n              permitType: permitData.permitType,\n              description: permitData.description,\n              location: permitData.address,\n              permitAppliedDate: permitData.applyDate\n                ? this.formatDateForInput(permitData.applyDate)\n                : '',\n              permitExpirationDate: permitData.expireDate\n                ? this.formatDateForInput(permitData.expireDate)\n                : '',\n              permitIssueDate: permitData.issueDate\n                ? this.formatDateForInput(permitData.issueDate)\n                : '',\n              permitFinalDate: permitData.finalDate\n                ? this.formatDateForInput(permitData.finalDate)\n                : '',\n              permitCompleteDate: permitData.completeDate\n                ? this.formatDateForInput(permitData.completeDate)\n                : '',\n              permitStatus: permitData.permitStatus,\n\n            });\n          } else {\n            console.warn(\n              'Permit response has isFault = true',\n              permitResponse.responseData.message\n            );\n          }\n        },\n        error: (err) => {\n          this.httpUtilService.setLoadingState(false);\n          console.error('API call failed', err);\n        },\n      });\n    this.subscriptions.push(subscription);\n}\n\n  getSyncButtonDisableStatus(): boolean {\n  const reviewType = this.permitForm.get('permitReviewType')?.value;\n  const permitNumber = this.permitForm.get('permitNumber')?.value;\n  const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;\n\n  const isReviewTypeValid = !reviewType || reviewType !== 'Internal';\n  const hasPermitNumber = !!permitNumber;\n  const hasPermitMunicipality = !!permitMunicipality;\n\n  console.log('isReviewTypeValid ', isReviewTypeValid)\n  console.log('hasPermitNumber ', hasPermitNumber)\n  console.log('hasPermitMunicipality ', hasPermitMunicipality)\n  // Disable if any of the conditions are not satisfied\n  return !(isReviewTypeValid && hasPermitNumber && hasPermitMunicipality);\n}\n\n  setupPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    if (permitNumberControl) {\n      permitNumberControl.valueChanges.subscribe(value => {\n        if (value && value.trim()) {\n          this.checkPermitNumber(value.trim());\n        } else {\n          this.permitNumberError = '';\n        }\n      });\n    }\n  }\n\n  checkPermitNumber(permitNumber: string) {\n    if (this.isCheckingPermitNumber) return;\n    \n    this.isCheckingPermitNumber = true;\n    this.permitNumberError = '';\n    \n    const subscription = this.permitsService.checkPermitNumberExists(permitNumber, this.id || undefined).subscribe({\n      next: (response: any) => {\n        this.isCheckingPermitNumber = false;\n        if (response && response.responseData) {\n          if (response.responseData.exists) {\n            this.permitNumberError = 'This permit number already exists. Please use a different permit number.';\n            this.permitForm.get('permitNumber')?.setErrors({ 'duplicate': true });\n          } else {\n            this.permitNumberError = '';\n            const currentErrors = this.permitForm.get('permitNumber')?.errors;\n            if (currentErrors && currentErrors['duplicate']) {\n              delete currentErrors['duplicate'];\n              this.permitForm.get('permitNumber')?.setErrors(Object.keys(currentErrors).length ? currentErrors : null);\n            }\n          }\n        }\n      },\n      error: (error: any) => {\n        this.isCheckingPermitNumber = false;\n        console.error('Error checking permit number:', error);\n        this.permitNumberError = 'Error checking permit number availability';\n      }\n    });\n    this.subscriptions.push(subscription);\n  }\n\n  // Method to handle modal dismiss and reset loading state\n  dismissModal() {\n    console.log('Permit popup - dismissModal called - resetting loading state');\n    this.httpUtilService.setLoadingState(false);\n    \n    // Add a small delay to ensure loading state is reset\n    setTimeout(() => {\n      console.log('Permit popup - ensuring loading state is reset after dismiss');\n      this.httpUtilService.setLoadingState(false);\n    }, 50);\n    \n    this.modal.dismiss();\n  }\n\n  ngOnDestroy() {\n    console.log('Permit popup - ngOnDestroy called, cleaning up subscriptions');\n    // Unsubscribe from all subscriptions to prevent memory leaks\n    this.subscriptions.forEach(subscription => {\n      if (subscription && !subscription.closed) {\n        subscription.unsubscribe();\n      }\n    });\n    this.subscriptions = [];\n    \n    // Ensure loading state is reset when component is destroyed\n    console.log('Permit popup - resetting loading state in ngOnDestroy');\n    this.httpUtilService.setLoadingState(false);\n    \n    // Add a more aggressive approach to ensure loading state is reset\n    setTimeout(() => {\n      console.log('Permit popup - final loading state reset in ngOnDestroy');\n      this.httpUtilService.setLoadingState(false);\n    }, 100);\n  }\n}\n", "<div class=\"modal-content h-auto\">\r\n  <div class=\"modal-header bg-light-primary\">\r\n    <div class=\"modal-title h5 fs-3\">\r\n      <ng-container>\r\n        <div *ngIf=\"id === 0\">Add Permit</div>\r\n        <div *ngIf=\"id !== 0\">Edit Permit - {{ permitNumber }}</div>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n      <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"dismissModal()\"></i>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"modal-body large-modal-body\">\r\n    <!-- Loading Overlay -->\r\n    <div *ngIf=\"isLoading\" class=\"loading-overlay-inside\">\r\n      <div class=\"spinner-border text-primary spinner-md\" role=\"status\">\r\n        <span class=\"visually-hidden\">Loading...</span>\r\n        <span class=\"visually-hidden\">This may take longer than usual....</span>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n      <div class=\"col-xl-12\">\r\n        <div class=\"d-flex\">\r\n          <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\">\r\n            <li class=\"nav-item\">\r\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'basic' }\" (click)=\"showTab('basic', $event)\">\r\n                Basic Info\r\n              </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'details' }\" (click)=\"showTab('details', $event)\">\r\n                Permit Details\r\n              </a>\r\n            </li>\r\n            <!-- <li class=\"nav-item\">\r\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'notes' }\" (click)=\"showTab('notes', $event)\">\r\n                Notes/Actions\r\n              </a>\r\n            </li> -->\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <form class=\"form form-label-right\" [formGroup]=\"permitForm\">\r\n      <ng-container *ngIf=\"selectedTab == 'basic'\">\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Project <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"projects\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"projectName\"\r\n              formControlName=\"projectId\" bindValue=\"projectId\" placeholder=\"Select Project\"\r\n              (change)=\"onProjectChange($event)\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('projectId')?.touched && permitForm.get('projectId')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('projectId')?.errors?.['required']\">\r\n                Project is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit / Sub Project Name <span class=\"text-danger\">*</span></label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitName\" />\r\n            <div *ngIf=\"permitForm.get('permitName')?.touched && permitForm.get('permitName')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitName')?.errors?.['required']\">\r\n                Permit / Sub Project Name is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Number <span class=\"text-danger\">*</span></label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitNumber\" \r\n                   [class.is-invalid]=\"permitNumberError || (permitForm.get('permitNumber')?.touched && permitForm.get('permitNumber')?.invalid)\" />\r\n            <!-- *ngIf=\"id === 0\"  -->\r\n\r\n            <!-- <p *ngIf=\"id !== 0\">{{this.permitNumber}}</p> -->\r\n            <div *ngIf=\"permitForm.get('permitNumber')?.touched && permitForm.get('permitNumber')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitNumber')?.errors?.['required']\">\r\n                Permit Number is required.\r\n              </div>\r\n            </div>\r\n            <div *ngIf=\"permitNumberError\" class=\"text-danger mt-1 small\">\r\n              {{ permitNumberError }}\r\n            </div>\r\n            <div *ngIf=\"isCheckingPermitNumber\" class=\"text-info mt-1 small\">\r\n              <i class=\"fas fa-spinner fa-spin\"></i> Checking permit number availability...\r\n            </div>\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Internal Review Status <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"internalStatusArray\" [clearable]=\"false\" [multiple]=\"false\"\r\n              formControlName=\"internalReviewStatus\" placeholder=\"Select Status\">\r\n            </ng-select>\r\n            <div\r\n              *ngIf=\"permitForm.get('internalReviewStatus')?.touched && permitForm.get('internalReviewStatus')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('internalReviewStatus')?.errors?.['required']\">\r\n                Internal Review Status is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Category <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"categories\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitCategory\"\r\n              placeholder=\"Select Category\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitCategory')?.touched && permitForm.get('permitCategory')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitCategory')?.errors?.['required']\">\r\n                Permit Category is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-6\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Review Type<span\r\n                class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"reviewTypeArray\" formControlName=\"permitReviewType\"\r\n              (change)=\"onPermitReviewTypeChange($event)\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitReviewType')?.touched && permitForm.get('permitReviewType')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitReviewType')?.errors?.['required']\">\r\n                Permit Review Type is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-xl-6\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Municipality (External Review)<span\r\n                *ngIf=\"isPermitMunicipalRequired\" class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"muncipalities\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"cityName\"\r\n              formControlName=\"permitMunicipality\" bindValue=\"municipalityId\" placeholder=\"Select Project\"\r\n              (change)=\"onPermitMunicipalityChange($event)\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitMunicipality')?.touched && permitForm.get('permitMunicipality')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitMunicipality')?.errors?.['required']\">\r\n                Permit Municipality is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- <i class=\"fas fa-sync-alt action-icon edit-icon\" [disabled]=\"getSyncButtonDisableStatus()\"  (click)=\"syncPermitDetails()\"\r\n                    title=\"Sync Review\"></i> -->\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n      </ng-container>\r\n      <ng-container *ngIf=\"selectedTab == 'details'\">\r\n        <div class=\"w-100 d-flex justify-content-end\" *ngIf=\"id === 0\">\r\n          <button type=\"button\" class=\"btn btn-primary btn-sm\" *ngIf=\"permitForm.get('permitReviewType')?.value !== 'Internal' && permitForm.get('permitReviewType')?.value !== '' && permitForm.get('permitReviewType')?.value !== null\" [disabled]=\"getSyncButtonDisableStatus()\"\r\n            (click)=\"syncPermitDetails()\">\r\n            <i class=\"fas fa-sync-alt\"></i> Sync\r\n          </button>\r\n        </div>\r\n\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Review Responsible Party<span class=\"text-danger\">*</span></label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"reviewResponsibleParty\" />\r\n            <div\r\n              *ngIf=\"permitForm.get('reviewResponsibleParty')?.touched && permitForm.get('reviewResponsibleParty')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('reviewResponsibleParty')?.errors?.['required']\">\r\n                Review Responsible Party is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Primary Contact</label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"primaryContact\" />\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Status <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"statuses\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitStatus\"\r\n              placeholder=\"Select Status\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitStatus')?.touched && permitForm.get('permitStatus')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitStatus')?.errors?.['required']\">\r\n                Permit Status is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Type <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"permitTypes\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitType\"\r\n              placeholder=\"Select Type\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitType')?.touched && permitForm.get('permitType')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitType')?.errors?.['required']\">\r\n                Permit Type is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Issue Date</label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitIssueDate\" />\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Applied Date <span class=\"text-danger\">*</span></label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitAppliedDate\" />\r\n            <div *ngIf=\"permitForm.get('permitAppliedDate')?.touched && permitForm.get('permitAppliedDate')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitAppliedDate')?.errors?.['required']\">\r\n                Applied Date is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n\r\n\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Expiration Date</label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitExpirationDate\" />\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Completed Date</label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitCompleteDate\" />\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Final Date</label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitFinalDate\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Location<span class=\"text-danger\">*</span></label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"location\" />\r\n            <div *ngIf=\"permitForm.get('location')?.touched && permitForm.get('location')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('location')?.errors?.['required']\">\r\n                Location is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Description</label>\r\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"description\"></textarea>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n      <!-- <ng-container *ngIf=\"selectedTab == 'role'\">\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit City Review Link<span *ngIf=\"isPermitMunicipalRequired\"\r\n                class=\"text-danger\">*</span></label>\r\n            <input type=\"url\" class=\"form-control form-control-sm\" formControlName=\"cityReviewLink\" />\r\n            <div *ngIf=\"permitForm.get('cityReviewLink')?.touched && permitForm.get('cityReviewLink')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('cityReviewLink')?.errors?.['required']\">\r\n                Permit City Review Link is required\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"row mt-4\">\r\n\r\n        </div>\r\n\r\n\r\n      </ng-container> -->\r\n      <!-- <ng-container *ngIf=\"selectedTab == 'notes'\">\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Attention Reason</label>\r\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"attentionReason\"></textarea>\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Internal Notes</label>\r\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"internalNotes\"></textarea>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Action Taken</label>\r\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"actionTaken\"></textarea>\r\n          </div>\r\n        </div>\r\n      </ng-container> -->\r\n    </form>\r\n  </div>\r\n\r\n  <div class=\"modal-footer justify-content-between\">\r\n    <div>\r\n      <button *ngIf=\"selectedTab == 'details'\" type=\"button\" class=\"btn btn-secondary btn-sm btn-elevate\"\r\n        (click)=\"goToPreviousTab()\">\r\n        Previous\r\n      </button>\r\n    </div>\r\n    <div>\r\n      <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate mr-2\" (click)=\"dismissModal()\">\r\n        Cancel</button>&nbsp;\r\n      <!-- *ngIf=\"selectedTab == 'notes'\"  -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-sm\" *ngIf=\"selectedTab == 'details'\"\r\n        [disabled]=\"permitForm.invalid\" (click)=\"save()\">\r\n        Save\r\n      </button>\r\n      <button *ngIf=\"selectedTab == 'basic'\" type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"goToNextTab()\">\r\n        Next\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAIEA,YAAY,QAGP,eAAe;AACtB,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICJ3DC,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACtCH,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,YAAA,KAAgC;;;;;IAYtDP,EAFJ,CAAAC,cAAA,cAAsD,cACc,eAClC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAErEF,EAFqE,CAAAG,YAAA,EAAO,EACpE,EACF;;;;;IAwCIH,EAAA,CAAAC,cAAA,UAA+D;IAC7DD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAC,yDAAA,iBAA+D;IAGjET,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAuD;IAAvDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAuD;;;;;IAa7Dd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAO,0DAAA,iBAAgE;IAGlEf,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAgB9Dd,EAAA,CAAAC,cAAA,UAAkE;IAChED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAQ,0DAAA,iBAAkE;IAGpEhB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;;;;;IAIlEd,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAW,iBAAA,MACF;;;;;IACAjB,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAkB,SAAA,YAAsC;IAAClB,EAAA,CAAAE,MAAA,+CACzC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUJH,EAAA,CAAAC,cAAA,UAA0E;IACxED,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAEiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAW,0DAAA,iBAA0E;IAG5EnB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAkE;IAAlEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAkE;;;;;IAYxEd,EAAA,CAAAC,cAAA,UAAoE;IAClED,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAY,0DAAA,iBAAoE;IAGtEpB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA4D;IAA5DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA4D;;;;;IAgBlEd,EAAA,CAAAC,cAAA,UAAsE;IACpED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAa,0DAAA,iBAAsE;IAGxErB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA8D;;;;;IAMMd,EAAA,CAAAC,cAAA,eAClB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAOhEH,EAAA,CAAAC,cAAA,UAAwE;IACtED,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAc,0DAAA,iBAAwE;IAG1EtB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAgE;IAAhEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAgE;;;;;;IApG9Ed,EAAA,CAAAuB,uBAAA,GAA6C;IAGvCvB,EAFJ,CAAAC,cAAA,cAAsB,aACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAC,cAAA,oBAEqC;IAAnCD,EAAA,CAAAwB,UAAA,oBAAAC,0EAAAC,MAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAAyB,eAAA,CAAAL,MAAA,CAAuB;IAAA,EAAC;IACpC1B,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,IAAAwB,mDAAA,kBACiC;IAMrChC,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3GH,EAAA,CAAAkB,SAAA,iBAAuF;IACvFlB,EAAA,CAAAQ,UAAA,KAAAyB,oDAAA,kBACiC;IAMrCjC,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC/FH,EAAA,CAAAkB,SAAA,iBACwI;IAaxIlB,EATA,CAAAQ,UAAA,KAAA0B,oDAAA,kBACiC,KAAAC,oDAAA,kBAK6B,KAAAC,oDAAA,kBAGG;IAGnEpC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,+BAAuB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACxGH,EAAA,CAAAkB,SAAA,qBAEY;IACZlB,EAAA,CAAAQ,UAAA,KAAA6B,oDAAA,kBAEiC;IAKnCrC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACjGH,EAAA,CAAAkB,SAAA,qBAEY;IACZlB,EAAA,CAAAQ,UAAA,KAAA8B,oDAAA,kBACiC;IAOrCtC,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAC,cAAA,gBACjC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACxCH,EAAA,CAAAC,cAAA,qBAC8C;IAA5CD,EAAA,CAAAwB,UAAA,oBAAAe,2EAAAb,MAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAAkC,wBAAA,CAAAd,MAAA,CAAgC;IAAA,EAAC;IAC7C1B,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,KAAAiC,oDAAA,kBACiC;IAKnCzC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAQ,UAAA,KAAAkC,qDAAA,mBAClB;IAAQ1C,EAAA,CAAAG,YAAA,EAAQ;IAC1EH,EAAA,CAAAC,cAAA,qBAEgD;IAA9CD,EAAA,CAAAwB,UAAA,oBAAAmB,2EAAAjB,MAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAAsC,0BAAA,CAAAlB,MAAA,CAAkC;IAAA,EAAC;IAC/C1B,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,KAAAqC,oDAAA,kBACiC;IAMrC7C,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;;;;;IArGSH,EAAA,CAAAI,SAAA,GAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAAwC,QAAA,CAAkB,oBAAoB,mBAAmB;IAI9D9C,EAAA,CAAAI,SAAA,EAAkF;IAAlFJ,EAAA,CAAAU,UAAA,WAAAqC,OAAA,GAAAzC,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAkC,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAzC,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAkC,OAAA,CAAAE,OAAA,EAAkF;IAalFjD,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAAwC,OAAA,GAAA5C,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAqC,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA5C,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAqC,OAAA,CAAAD,OAAA,EAAoF;IAYnFjD,EAAA,CAAAI,SAAA,GAA8H;IAA9HJ,EAAA,CAAAmD,WAAA,eAAA7C,MAAA,CAAAW,iBAAA,MAAAmC,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAH,OAAA,EAA8H;IAI/HjD,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAA2C,OAAA,GAAA/C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAwC,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAA/C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAwC,OAAA,CAAAJ,OAAA,EAAwF;IAMxFjD,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAW,iBAAA,CAAuB;IAGvBjB,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAgD,sBAAA,CAA4B;IAMvBtD,EAAA,CAAAI,SAAA,GAA6B;IAAqBJ,EAAlD,CAAAU,UAAA,UAAAJ,MAAA,CAAAiD,mBAAA,CAA6B,oBAAoB,mBAAmB;IAI5EvD,EAAA,CAAAI,SAAA,EAAwG;IAAxGJ,EAAA,CAAAU,UAAA,WAAA8C,QAAA,GAAAlD,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAA2C,QAAA,CAAAR,OAAA,OAAAQ,QAAA,GAAAlD,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAA2C,QAAA,CAAAP,OAAA,EAAwG;IAShGjD,EAAA,CAAAI,SAAA,GAAoB;IAAqBJ,EAAzC,CAAAU,UAAA,UAAAJ,MAAA,CAAAmD,UAAA,CAAoB,oBAAoB,mBAAmB;IAGhEzD,EAAA,CAAAI,SAAA,EAA4F;IAA5FJ,EAAA,CAAAU,UAAA,WAAAgD,QAAA,GAAApD,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAA6C,QAAA,CAAAV,OAAA,OAAAU,QAAA,GAAApD,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAA6C,QAAA,CAAAT,OAAA,EAA4F;IAavFjD,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAU,UAAA,UAAAJ,MAAA,CAAAqD,eAAA,CAAyB;IAG9B3D,EAAA,CAAAI,SAAA,EAAgG;IAAhGJ,EAAA,CAAAU,UAAA,WAAAkD,QAAA,GAAAtD,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA+C,QAAA,CAAAZ,OAAA,OAAAY,QAAA,GAAAtD,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA+C,QAAA,CAAAX,OAAA,EAAgG;IASjGjD,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAuD,yBAAA,CAA+B;IACzB7D,EAAA,CAAAI,SAAA,EAAuB;IAAqBJ,EAA5C,CAAAU,UAAA,UAAAJ,MAAA,CAAAwD,aAAA,CAAuB,oBAAoB,mBAAmB;IAInE9D,EAAA,CAAAI,SAAA,EAAoG;IAApGJ,EAAA,CAAAU,UAAA,WAAAqD,QAAA,GAAAzD,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAkD,QAAA,CAAAf,OAAA,OAAAe,QAAA,GAAAzD,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAkD,QAAA,CAAAd,OAAA,EAAoG;;;;;;IAoB5GjD,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAwB,UAAA,mBAAAwC,qFAAA;MAAAhE,EAAA,CAAA2B,aAAA,CAAAsC,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA4D,iBAAA,EAAmB;IAAA,EAAC;IAC7BlE,EAAA,CAAAkB,SAAA,YAA+B;IAAClB,EAAA,CAAAE,MAAA,aAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHuNH,EAAA,CAAAU,UAAA,aAAAJ,MAAA,CAAA6D,0BAAA,GAAyC;;;;;IAD3QnE,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAQ,UAAA,IAAA4D,4DAAA,qBACgC;IAGlCpE,EAAA,CAAAG,YAAA,EAAM;;;;;IAJkDH,EAAA,CAAAI,SAAA,EAAwK;IAAxKJ,EAAA,CAAAU,UAAA,WAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAA0D,KAAA,sBAAA1D,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAA0D,KAAA,cAAA1D,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAA0D,KAAA,WAAwK;;;;;IAc1NrE,EAAA,CAAAC,cAAA,UAA4E;IAC1ED,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAEiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA8D,yDAAA,iBAA4E;IAG9EtE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAoE;;;;;IAgB1Ed,EAAA,CAAAC,cAAA,UAAkE;IAChED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA+D,0DAAA,iBAAkE;IAGpEvE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;;;;;IAehEd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAgE,0DAAA,iBAAgE;IAGlExE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAc9Dd,EAAA,CAAAC,cAAA,UAAuE;IACrED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAiE,0DAAA,iBAAuE;IAGzEzE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA+D;IAA/DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA+D;;;;;IA4BrEd,EAAA,CAAAC,cAAA,UAA8D;IAC5DD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAkE,0DAAA,iBAA8D;IAGhE1E,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAsD;;;;;IAzFpEd,EAAA,CAAAuB,uBAAA,GAA+C;IAC7CvB,EAAA,CAAAQ,UAAA,IAAAmE,mDAAA,kBAA+D;IAU3D3E,EAFJ,CAAAC,cAAA,cAAsB,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzGH,EAAA,CAAAkB,SAAA,gBAAmG;IACnGlB,EAAA,CAAAQ,UAAA,IAAAoE,mDAAA,kBAEiC;IAKnC5E,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAkB,SAAA,iBAA2F;IAC7FlB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC/FH,EAAA,CAAAkB,SAAA,qBAEY;IACZlB,EAAA,CAAAQ,UAAA,KAAAqE,oDAAA,kBACiC;IAOrC7E,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC7FH,EAAA,CAAAkB,SAAA,qBAEY;IACZlB,EAAA,CAAAQ,UAAA,KAAAsE,oDAAA,kBACiC;IAKnC9E,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAkB,SAAA,iBAA4F;IAC9FlB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAAkB,SAAA,iBAA8F;IAC9FlB,EAAA,CAAAQ,UAAA,KAAAuE,oDAAA,kBACiC;IAMrC/E,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAJJ,CAAAC,cAAA,eAAsB,eAGE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAkB,SAAA,iBAAiG;IACnGlB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAkB,SAAA,iBAA+F;IACjGlB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAkB,SAAA,iBAA4F;IAEhGlB,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAkB,SAAA,iBAAqF;IACrFlB,EAAA,CAAAQ,UAAA,KAAAwE,oDAAA,kBACiC;IAMrChF,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAkB,SAAA,oBAAiG;IAErGlB,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;;IAnGyCH,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAA2E,EAAA,OAAc;IAatDjF,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAU,UAAA,WAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAqC,OAAA,OAAArC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAsC,OAAA,EAA4G;IAapGjD,EAAA,CAAAI,SAAA,IAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAA4E,QAAA,CAAkB,oBAAoB,mBAAmB;IAG9DlF,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAA0C,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAH,OAAA,EAAwF;IAYnFjD,EAAA,CAAAI,SAAA,GAAqB;IAAqBJ,EAA1C,CAAAU,UAAA,UAAAJ,MAAA,CAAA6E,WAAA,CAAqB,oBAAoB,mBAAmB;IAGjEnF,EAAA,CAAAI,SAAA,EAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAA0E,QAAA,GAAA9E,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAuE,QAAA,CAAApC,OAAA,OAAAoC,QAAA,GAAA9E,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAuE,QAAA,CAAAnC,OAAA,EAAoF;IAcpFjD,EAAA,CAAAI,SAAA,IAAkG;IAAlGJ,EAAA,CAAAU,UAAA,WAAA2E,QAAA,GAAA/E,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAwE,QAAA,CAAArC,OAAA,OAAAqC,QAAA,GAAA/E,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAwE,QAAA,CAAApC,OAAA,EAAkG;IA4BlGjD,EAAA,CAAAI,SAAA,IAAgF;IAAhFJ,EAAA,CAAAU,UAAA,WAAA4E,QAAA,GAAAhF,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAyE,QAAA,CAAAtC,OAAA,OAAAsC,QAAA,GAAAhF,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAyE,QAAA,CAAArC,OAAA,EAAgF;;;;;;IA+D5FjD,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAAwB,UAAA,mBAAA+D,gEAAA;MAAAvF,EAAA,CAAA2B,aAAA,CAAA6D,GAAA;MAAA,MAAAlF,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAmF,eAAA,EAAiB;IAAA,EAAC;IAC3BzF,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,iBACmD;IAAjBD,EAAA,CAAAwB,UAAA,mBAAAkE,gEAAA;MAAA1F,EAAA,CAAA2B,aAAA,CAAAgE,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAsF,IAAA,EAAM;IAAA,EAAC;IAChD5F,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAU,UAAA,aAAAJ,MAAA,CAAAM,UAAA,CAAAqC,OAAA,CAA+B;;;;;;IAGjCjD,EAAA,CAAAC,cAAA,iBAA4G;IAAxBD,EAAA,CAAAwB,UAAA,mBAAAqE,gEAAA;MAAA7F,EAAA,CAAA2B,aAAA,CAAAmE,GAAA;MAAA,MAAAxF,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAyF,WAAA,EAAa;IAAA,EAAC;IACzG/F,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADrTf,OAAM,MAAO6F,oBAAoB;EAoFtBC,KAAA;EACCC,EAAA;EACAC,eAAA;EACAC,cAAA;EACAC,UAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,GAAA;EA1FDvB,EAAE,GAAW,CAAC,CAAC,CAAC;EAChBwB,MAAM,CAAM,CAAC;EACZC,SAAS,GAAsB,IAAI5G,YAAY,EAAE;EAE3Dc,UAAU;EACVkC,QAAQ,GAAU,EAAE;EACpBa,eAAe,GAAU,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC;EACzDgD,SAAS,GAAQ,EAAE;EACnBC,SAAS,GAAY,KAAK;EAC1B9C,aAAa,GAAQ,EAAE;EACvB+C,WAAW,GAAQ,OAAO;EAC1B5F,iBAAiB,GAAW,EAAE;EAC9BqC,sBAAsB,GAAY,KAAK;EAC/BwD,aAAa,GAAmB,EAAE;EAC1C;EACA3B,WAAW,GAAG,CACZ,oCAAoC,EACpC,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,qCAAqC,EACrC,oCAAoC,EACpC,0CAA0C,EAC1C,2BAA2B,EAC3B,uCAAuC,EACvC,6CAA6C,EAC7C,yBAAyB,EACzB,0CAA0C,EAC1C,oCAAoC,EACpC,4DAA4D,EAC5D,oBAAoB,EACpB,mBAAmB,EACnB,wCAAwC,EACxC,8BAA8B,EAC9B,6BAA6B,EAC7B,iCAAiC,EACjC,yBAAyB,EACzB,qBAAqB,EACrB,wBAAwB,EACxB,0BAA0B,EAC1B,qCAAqC,EACrC,yBAAyB,EACzB,4CAA4C,EAC5C,0BAA0B,EAC1B,oCAAoC,EACpC,iBAAiB,EACjB,cAAc,EACd,2BAA2B,EAC3B,kCAAkC,EAClC,uCAAuC,EACvC,+BAA+B,EAC/B,sBAAsB,EACtB,gCAAgC,EAChC,iCAAiC,EACjC,gCAAgC,CACjC;EACD1B,UAAU,GAAG,CACX,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,eAAe,CAChB;EACDyB,QAAQ,GAAG,CACT,UAAU,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,EACX,QAAQ,EACR,SAAS,EACT,mBAAmB,EACnB,iCAAiC,EACjC,oBAAoB,EACpB,MAAM,CACP;EACD3B,mBAAmB,GAAE,CAAC,UAAU,EAAC,uBAAuB,EAAC,cAAc,EAAC,SAAS,EAAC,cAAc,EAAC,WAAW,EAAC,mBAAmB,CAAC;EACjIhD,YAAY;EACZsD,yBAAyB,GAAY,KAAK;EAC1CkD,iCAAiC,GAAU,KAAK;EAChDC,cAAc,GAAM,EAAE;EACtBC,gBAAgB,GAAM,EAAE;EACxBC,YACSjB,KAAqB,EACpBC,EAAe,EACfC,eAAgC,EAChCC,cAA8B,EAC9BC,UAAsB,EACtBC,eAAiC,EACjCC,wBAAkD,EAClDC,GAAsB;IAPvB,KAAAP,KAAK,GAALA,KAAK;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,GAAG,GAAHA,GAAG;IAEX;IACA,MAAMW,mBAAmB,GAAG,IAAI,CAACb,eAAe,CAACc,cAAc,CAACC,SAAS,CAAEC,OAAO,IAAI;MACpFC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,OAAO,CAAC;MAC7D,IAAI,CAACV,SAAS,GAAGU,OAAO;IAC1B,CAAC,CAAC;IACF,IAAI,CAACR,aAAa,CAACW,IAAI,CAACN,mBAAmB,CAAC;EAC9C;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACf,SAAS,GAAG,IAAI,CAACN,UAAU,CAACsB,eAAe,EAAE;IAClD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,2BAA2B,EAAE;IAClC,IAAI,IAAI,CAAC9C,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC+C,SAAS,EAAE;IAClB;EACF;EAEAH,QAAQA,CAAA;IACN,IAAI,CAACjH,UAAU,GAAG,IAAI,CAACsF,EAAE,CAAC+B,KAAK,CAAC;MAC9BC,SAAS,EAAE,CAAC,EAAE,EAAEnI,UAAU,CAACoI,QAAQ,CAAC;MACpCC,UAAU,EAAE,CAAC,EAAE,EAAErI,UAAU,CAACoI,QAAQ,CAAC;MACrC5H,YAAY,EAAE,CAAC,EAAE,EAAER,UAAU,CAACoI,QAAQ,CAAC;MACvCE,cAAc,EAAE,CAAC,EAAE,EAAEtI,UAAU,CAACoI,QAAQ,CAAC;MACzCG,UAAU,EAAE,CAAC,EAAE,EAAEvI,UAAU,CAACoI,QAAQ,CAAC;MACrCI,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,gBAAgB,EAAE,CAAC,EAAE,EAAEzI,UAAU,CAACoI,QAAQ,CAAC;MAC3CM,QAAQ,EAAE,CAAC,EAAE,EAAC1I,UAAU,CAACoI,QAAQ,CAAC;MAClCO,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,iBAAiB,EAAE,CAAC,EAAE,EAAE5I,UAAU,CAACoI,QAAQ,CAAC;MAC5CS,oBAAoB,EAAE,CAAC,EAAE,CAAC;MAC1BC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,YAAY,EAAE,CAAC,EAAE,EAAEjJ,UAAU,CAACoI,QAAQ,CAAC;MACvCc,oBAAoB,EAAE,CAAC,EAAE,EAAElJ,UAAU,CAACoI,QAAQ,CAAC;MAC/Ce,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,sBAAsB,EAAE,CAAC,EAAE,EAAEtJ,UAAU,CAACoI,QAAQ,CAAC;MACjDmB,kBAAkB,EAAE,CAAC,EAAE;MACvB;KACD,CAAC;EACJ;EAEAxB,YAAYA,CAAA;IACV,IAAI,CAACxB,eAAe,CAACiD,eAAe,CAAC,IAAI,CAAC;IAC1C,MAAMC,MAAM,GAAG;MAAEC,QAAQ,EAAE;IAAK,CAAE;IAClC,MAAMC,YAAY,GAAG,IAAI,CAACvD,eAAe,CAACwD,kBAAkB,CAACH,MAAM,CAAC,CAACnC,SAAS,CAAC;MAC7EuC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACvD,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;QAC3C,IAAIM,QAAQ,IAAIA,QAAQ,CAACC,YAAY,EAAE;UACrC,IAAI,CAAChH,QAAQ,GAAG+G,QAAQ,CAACC,YAAY,CAACC,IAAI;QAC5C;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC1D,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;QAC3ChC,OAAO,CAACyC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAClH,QAAQ,GAAG,EAAE;MACpB;KACD,CAAC;IACF,IAAI,CAACgE,aAAa,CAACW,IAAI,CAACiC,YAAY,CAAC;EACvC;EAEA9B,kBAAkBA,CAAA;IAChB,IAAI,CAACtB,eAAe,CAACiD,eAAe,CAAC,IAAI,CAAC;IAC1C,MAAMC,MAAM,GAAG;MAAES,YAAY,EAAE,IAAI,CAACtD,SAAS,CAACuD;IAAM,CAAE;IACtD,MAAMR,YAAY,GAAG,IAAI,CAACtD,cAAc,CAAC+D,oBAAoB,CAACX,MAAM,CAAC,CAACnC,SAAS,CAAC;MAC9EuC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACvD,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;QAC3C,IAAIM,QAAQ,IAAIA,QAAQ,CAACC,YAAY,EAAE;UACrC,IAAI,CAAChG,aAAa,GAAG+F,QAAQ,CAACC,YAAY,CAACC,IAAI;QACjD;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC1D,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;QAC3ChC,OAAO,CAACyC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAClG,aAAa,GAAG,EAAE;MACzB;KACD,CAAC;IACF,IAAI,CAACgD,aAAa,CAACW,IAAI,CAACiC,YAAY,CAAC;EACvC;EAEA1B,SAASA,CAAA;IACP,IAAI,CAAC1B,eAAe,CAACiD,eAAe,CAAC,IAAI,CAAC;IAC1C,MAAMG,YAAY,GAAG,IAAI,CAACtD,cAAc,CACrCgE,SAAS,CAAC;MAAEC,QAAQ,EAAE,IAAI,CAACpF,EAAE;MAAEqF,cAAc,EAAE,IAAI,CAAC3D,SAAS,CAACuD;IAAM,CAAE,CAAC,CACvE7C,SAAS,CAAC;MACTuC,IAAI,EAAGW,cAAmB,IAAI;QAC5B,IAAI,CAACjE,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;QAC3C,IAAI,CAACgB,cAAc,CAACC,OAAO,EAAE;UAC3B,IAAIC,UAAU,GAAGF,cAAc,CAACT,YAAY,CAACC,IAAI;UACjD,IAAI,CAACxJ,YAAY,GAAGkK,UAAU,CAAClK,YAAY;UAC3C,IAAI,CAACK,UAAU,CAAC8J,UAAU,CAAC;YACzBxC,SAAS,EAAEuC,UAAU,CAACvC,SAAS;YAC/B3H,YAAY,EAAEkK,UAAU,CAAClK,YAAY;YACrCiI,gBAAgB,EAAEiC,UAAU,CAACjC,gBAAgB;YAC7CH,cAAc,EAAEoC,UAAU,CAACpC,cAAc;YACzCC,UAAU,EAAEmC,UAAU,CAACnC,UAAU;YACjCC,WAAW,EAAEkC,UAAU,CAAClC,WAAW;YACnCH,UAAU,EAAEqC,UAAU,CAACrC,UAAU;YACjCK,QAAQ,EAAEgC,UAAU,CAAChC,QAAQ;YAC7BQ,oBAAoB,EAACwB,UAAU,CAACxB,oBAAoB;YACpDP,cAAc,EAAE+B,UAAU,CAAC/B,cAAc;YACzCC,iBAAiB,EAAE8B,UAAU,CAAC9B,iBAAiB,GAC3C,IAAI,CAACgC,kBAAkB,CAACF,UAAU,CAAC9B,iBAAiB,CAAC,GACrD,EAAE;YACNC,oBAAoB,EAAE6B,UAAU,CAAC7B,oBAAoB,GACjD,IAAI,CAAC+B,kBAAkB,CAACF,UAAU,CAAC7B,oBAAoB,CAAC,GACxD,EAAE;YACNC,eAAe,EAAE4B,UAAU,CAAC5B,eAAe,GACvC,IAAI,CAAC8B,kBAAkB,CAACF,UAAU,CAAC5B,eAAe,CAAC,GACnD,EAAE;YACFC,eAAe,EAAE2B,UAAU,CAAC3B,eAAe,GAC3C,IAAI,CAAC6B,kBAAkB,CAACF,UAAU,CAAC3B,eAAe,CAAC,GACnD,EAAE;YACNC,kBAAkB,EAAE0B,UAAU,CAAC1B,kBAAkB,GAC7C,IAAI,CAAC4B,kBAAkB,CAACF,UAAU,CAAC1B,kBAAkB,CAAC,GACtD,EAAE;YACNC,YAAY,EAAEyB,UAAU,CAACzB,YAAY;YACrCE,eAAe,EAAEuB,UAAU,CAACvB,eAAe;YAC3CC,aAAa,EAAEsB,UAAU,CAACtB,aAAa;YACvCC,WAAW,EAAEqB,UAAU,CAACrB,WAAW;YACnCC,sBAAsB,EAAEoB,UAAU,CAACpB,sBAAsB;YACzDC,kBAAkB,EAAEmB,UAAU,CAACnB;YAC/B;WACD,CAAC;UACF,IAAI,CAAC9G,wBAAwB,CAACiI,UAAU,CAACjC,gBAAgB,CAAC;QAC5D,CAAC,MAAM;UACLjB,OAAO,CAACqD,IAAI,CACV,oCAAoC,EACpCL,cAAc,CAACT,YAAY,CAC5B;QACH;MACF,CAAC;MACDE,KAAK,EAAGa,GAAG,IAAI;QACb,IAAI,CAACvE,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;QAC3ChC,OAAO,CAACyC,KAAK,CAAC,iBAAiB,EAAEa,GAAG,CAAC;MACvC;KACD,CAAC;IACJ,IAAI,CAAC/D,aAAa,CAACW,IAAI,CAACiC,YAAY,CAAC;EACvC;EAEAiB,kBAAkBA,CAACG,UAAkB;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC;IACA,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEAE,iBAAiBA,CAAA;IACf,MAAMC,QAAQ,GAAG,IAAI,CAAC9K,UAAU,CAACyD,KAAK;IAEtC,IAAIsH,iBAAiB,GAAQ,EAAE;IAC/BA,iBAAiB,CAACzD,SAAS,GAAGwD,QAAQ,CAACxD,SAAS;IAChDyD,iBAAiB,CAACpL,YAAY,GAAGmL,QAAQ,CAACnL,YAAY;IACtDoL,iBAAiB,CAACnD,gBAAgB,GAAGkD,QAAQ,CAAClD,gBAAgB;IAC9DmD,iBAAiB,CAACtD,cAAc,GAAGqD,QAAQ,CAACrD,cAAc;IAC1DsD,iBAAiB,CAACrD,UAAU,GAAGoD,QAAQ,CAACpD,UAAU;IAClDqD,iBAAiB,CAACpD,WAAW,GAAGmD,QAAQ,CAACnD,WAAW;IACpDoD,iBAAiB,CAAClD,QAAQ,GAAGiD,QAAQ,CAACjD,QAAQ;IAC9CkD,iBAAiB,CAACjD,cAAc,GAAGgD,QAAQ,CAAChD,cAAc;IAC1DiD,iBAAiB,CAAChD,iBAAiB,GAAG+C,QAAQ,CAAC/C,iBAAiB,IAAI,IAAI;IACxEgD,iBAAiB,CAAC/C,oBAAoB,GACpC8C,QAAQ,CAAC9C,oBAAoB,IAAI,IAAI;IACvC+C,iBAAiB,CAAC9C,eAAe,GAAG6C,QAAQ,CAAC7C,eAAe,IAAI,IAAI;IACpE8C,iBAAiB,CAAC7C,eAAe,GAC/B4C,QAAQ,CAAC5C,eAAe,IAAI,IAAI;IAClC6C,iBAAiB,CAAC5C,kBAAkB,GAAG2C,QAAQ,CAAC3C,kBAAkB,IAAI,IAAI;IAC1E4C,iBAAiB,CAAC3C,YAAY,GAAG0C,QAAQ,CAAC1C,YAAY;IACtD2C,iBAAiB,CAAC1C,oBAAoB,GAAGyC,QAAQ,CAACzC,oBAAoB;IACtE0C,iBAAiB,CAACvD,UAAU,GAAGsD,QAAQ,CAACtD,UAAU;IAClDuD,iBAAiB,CAACzC,eAAe,GAAGwC,QAAQ,CAACxC,eAAe;IAC5DyC,iBAAiB,CAACxC,aAAa,GAAGuC,QAAQ,CAACvC,aAAa;IACxDwC,iBAAiB,CAACvC,WAAW,GAAGsC,QAAQ,CAACtC,WAAW;IACpDuC,iBAAiB,CAACtC,sBAAsB,GAAGqC,QAAQ,CAACrC,sBAAsB;IAC1EsC,iBAAiB,CAACrC,kBAAkB,GAAGoC,QAAQ,CAACpC,kBAAkB;IAClEqC,iBAAiB,CAAC3E,cAAc,GAAG,IAAI,CAACA,cAAc;IACtD2E,iBAAiB,CAACrB,cAAc,GAAG,IAAI,CAAC3D,SAAS,CAACuD,MAAM;IACxDyB,iBAAiB,CAAC1E,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC1D,MAAM2E,MAAM,GAAI,IAAI,CAAC3E,gBAAgB,CAAC4E,QAAQ,IAAK,IAAI,CAAC5E,gBAAgB,CAACoD,QAAQ,IAAK,IAAI,CAACpD,gBAAgB,CAAC6E,EAAE,IAAI,IAAI;IACtHH,iBAAiB,CAACI,cAAc,GAAGH,MAAM;IACzC,IAAI,IAAI,CAAC3G,EAAE,KAAK,CAAC,EAAE;MACjB0G,iBAAiB,CAACtB,QAAQ,GAAG,IAAI,CAACpF,EAAE;IACtC;IAEA,OAAO0G,iBAAiB;EAC1B;EAEA/F,IAAIA,CAAA;IACF,IAAIoG,QAAQ,GAAG,IAAI,CAACpL,UAAU,CAACoL,QAAQ;IACvCzE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC5G,UAAU,CAACyD,KAAK,CAAC;IAElD;IACA,IAAI,IAAI,CAACpD,iBAAiB,EAAE;MAC1B,IAAI,CAACsF,wBAAwB,CAAC0F,SAAS,CACrC,IAAI,CAAChL,iBAAiB,EACtB,EAAE,CACH;MACD;IACF;IAEA,IAAI,IAAI,CAACL,UAAU,CAACqC,OAAO,EAAE;MAC3BiJ,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,OAAO,CAAEC,WAAW,IACxCL,QAAQ,CAACK,WAAW,CAAC,CAACC,aAAa,EAAE,CACtC;MACD,IAAI,CAAC/F,wBAAwB,CAAC0F,SAAS,CACrC,iCAAiC,EACjC,EAAE,CACH;MACD;IACF;IACA,IAAIxB,UAAU,GAAQ,IAAI,CAACgB,iBAAiB,EAAE;IAC9ClE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiD,UAAU,CAAC;IACvC,IAAI,IAAI,CAACxF,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAACsH,MAAM,CAAC9B,UAAU,CAAC;IACzB,CAAC,MAAM;MACL,IAAI,CAAC+B,IAAI,CAAC/B,UAAU,CAAC;IACvB;EACF;EAEA8B,MAAMA,CAAC9B,UAAe;IACpB,IAAI,CAACnE,eAAe,CAACiD,eAAe,CAAC,IAAI,CAAC;IAC1C,MAAMG,YAAY,GAAG,IAAI,CAACtD,cAAc,CAACqG,YAAY,CAAChC,UAAU,CAAC,CAACpD,SAAS,CAAEqF,GAAQ,IAAI;MACvF,IAAI,CAACpG,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;MAC3C,IAAI,CAACmD,GAAG,CAAClC,OAAO,EAAE;QAChB,IAAI,CAACjE,wBAAwB,CAACoG,WAAW,CAACD,GAAG,CAAC5C,YAAY,CAAC8C,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAClG,SAAS,CAACmG,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC5G,KAAK,CAAC6G,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACvG,wBAAwB,CAAC0F,SAAS,CAACS,GAAG,CAAC5C,YAAY,CAAC8C,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAClG,SAAS,CAACmG,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,IAAI,CAAC/F,aAAa,CAACW,IAAI,CAACiC,YAAY,CAAC;EACvC;EAEA8C,IAAIA,CAAC/B,UAAe;IAClB,IAAI,CAACnE,eAAe,CAACiD,eAAe,CAAC,IAAI,CAAC;IAC1C,MAAMG,YAAY,GAAG,IAAI,CAACtD,cAAc,CAAC2G,YAAY,CAACtC,UAAU,CAAC,CAACpD,SAAS,CAAEqF,GAAG,IAAI;MAClF,IAAI,CAACpG,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;MAC3C,IAAI,CAACmD,GAAG,CAAClC,OAAO,EAAE;QAChB,IAAI,CAACjE,wBAAwB,CAACoG,WAAW,CAACD,GAAG,CAAC5C,YAAY,CAAC8C,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAClG,SAAS,CAACmG,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC5G,KAAK,CAAC6G,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACvG,wBAAwB,CAAC0F,SAAS,CAACS,GAAG,CAAC5C,YAAY,CAAC8C,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAClG,SAAS,CAACmG,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,IAAI,CAAC/F,aAAa,CAACW,IAAI,CAACiC,YAAY,CAAC;EACvC;EACAsD,OAAOA,CAACC,GAAQ,EAAEvL,MAAW;IAC3B,IAAI,CAACmF,WAAW,GAAGoG,GAAG;IACtB,IAAI,CAACzG,GAAG,CAAC0G,YAAY,EAAE;EACzB;EAEAnH,WAAWA,CAAA;IACT,IAAI,IAAI,CAACc,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,SAAS,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;IACA,IAAI,CAACL,GAAG,CAAC0G,YAAY,EAAE;EACzB;EAEAzH,eAAeA,CAAA;IACb,IAAI,IAAI,CAACoB,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,SAAS,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;IACA,IAAI,CAACL,GAAG,CAAC0G,YAAY,EAAE;EACzB;EAEAnL,eAAeA,CAACoL,KAAS;IACtB,IAAI,CAACvM,UAAU,CAAC8J,UAAU,CAAC;MAClBjC,QAAQ,EAAE0E,KAAK,CAACC;KAAiB,CAAC;IAC5C;EAGF;EAEF5K,wBAAwBA,CAAC2K,KAAU;IACjC,MAAME,aAAa,GAAG,IAAI,CAACzM,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC/D;IAEA,IAAIsM,KAAK,KAAK,UAAU,EAAE;MACxB,IAAI,CAACtJ,yBAAyB,GAAG,IAAI;MACrC;MAEAwJ,aAAa,EAAEC,aAAa,CAAC,CAACvN,UAAU,CAACoI,QAAQ,CAAC,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAACtE,yBAAyB,GAAG,KAAK;MACtC;MAEAwJ,aAAa,EAAEE,eAAe,EAAE;MAChC;IACF;IAEAF,aAAa,EAAEG,sBAAsB,EAAE;IACvC;EACF;EAEA5K,0BAA0BA,CAACuK,KAAS;IAClC5F,OAAO,CAACC,GAAG,CAAC,UAAU,EAAC2F,KAAK,CAAC;IAC7B,IAAI,CAACnG,cAAc,GAAEmG,KAAK,CAACM,eAAe,GAAC,WAAW;IACtD,IAAI,CAACtJ,0BAA0B,EAAE;EACnC;EAEAD,iBAAiBA,CAAA;IACd,MAAMwH,QAAQ,GAAG,IAAI,CAAC9K,UAAU,CAACyD,KAAK;IACrC,IAAI,CAACiC,eAAe,CAACiD,eAAe,CAAC,IAAI,CAAC;IAC1C,MAAMG,YAAY,GAAG,IAAI,CAACtD,cAAc,CACrCsH,gBAAgB,CAAC;MAAEnN,YAAY,EAAEmL,QAAQ,CAACnL,YAAY;MAAEoN,cAAc,EAAEjC,QAAQ,CAACpC;IAAkB,CAAE,CAAC,CACtGjC,SAAS,CAAC;MACTuC,IAAI,EAAGW,cAAmB,IAAI;QAC5B,IAAI,CAACjE,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;QAC3C,IAAI,CAACgB,cAAc,CAACC,OAAO,EAAE;UAC3B,IAAIC,UAAU,GAAGF,cAAc,CAACT,YAAY,CAACrD,MAAM;UACnD,IAAI,CAACQ,gBAAgB,GAAGwD,UAAU;UAClC,IAAI,CAAC7J,UAAU,CAAC8J,UAAU,CAAC;YACzBpC,UAAU,EAAEmC,UAAU,CAACnC,UAAU;YACjCC,WAAW,EAAEkC,UAAU,CAAClC,WAAW;YACnCE,QAAQ,EAAEgC,UAAU,CAACmD,OAAO;YAC5BjF,iBAAiB,EAAE8B,UAAU,CAACoD,SAAS,GACnC,IAAI,CAAClD,kBAAkB,CAACF,UAAU,CAACoD,SAAS,CAAC,GAC7C,EAAE;YACNjF,oBAAoB,EAAE6B,UAAU,CAACqD,UAAU,GACvC,IAAI,CAACnD,kBAAkB,CAACF,UAAU,CAACqD,UAAU,CAAC,GAC9C,EAAE;YACNjF,eAAe,EAAE4B,UAAU,CAACsD,SAAS,GACjC,IAAI,CAACpD,kBAAkB,CAACF,UAAU,CAACsD,SAAS,CAAC,GAC7C,EAAE;YACNjF,eAAe,EAAE2B,UAAU,CAACuD,SAAS,GACjC,IAAI,CAACrD,kBAAkB,CAACF,UAAU,CAACuD,SAAS,CAAC,GAC7C,EAAE;YACNjF,kBAAkB,EAAE0B,UAAU,CAACwD,YAAY,GACvC,IAAI,CAACtD,kBAAkB,CAACF,UAAU,CAACwD,YAAY,CAAC,GAChD,EAAE;YACNjF,YAAY,EAAEyB,UAAU,CAACzB;WAE1B,CAAC;QACJ,CAAC,MAAM;UACLzB,OAAO,CAACqD,IAAI,CACV,oCAAoC,EACpCL,cAAc,CAACT,YAAY,CAAC8C,OAAO,CACpC;QACH;MACF,CAAC;MACD5C,KAAK,EAAGa,GAAG,IAAI;QACb,IAAI,CAACvE,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;QAC3ChC,OAAO,CAACyC,KAAK,CAAC,iBAAiB,EAAEa,GAAG,CAAC;MACvC;KACD,CAAC;IACJ,IAAI,CAAC/D,aAAa,CAACW,IAAI,CAACiC,YAAY,CAAC;EACzC;EAEEvF,0BAA0BA,CAAA;IAC1B,MAAM+J,UAAU,GAAG,IAAI,CAACtN,UAAU,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEwD,KAAK;IACjE,MAAM9D,YAAY,GAAG,IAAI,CAACK,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEwD,KAAK;IAC/D,MAAMiF,kBAAkB,GAAG,IAAI,CAAC1I,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEwD,KAAK;IAE3E,MAAM8J,iBAAiB,GAAG,CAACD,UAAU,IAAIA,UAAU,KAAK,UAAU;IAClE,MAAME,eAAe,GAAG,CAAC,CAAC7N,YAAY;IACtC,MAAM8N,qBAAqB,GAAG,CAAC,CAAC/E,kBAAkB;IAElD/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2G,iBAAiB,CAAC;IACpD5G,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE4G,eAAe,CAAC;IAChD7G,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6G,qBAAqB,CAAC;IAC5D;IACA,OAAO,EAAEF,iBAAiB,IAAIC,eAAe,IAAIC,qBAAqB,CAAC;EACzE;EAEEtG,2BAA2BA,CAAA;IACzB,MAAMuG,mBAAmB,GAAG,IAAI,CAAC1N,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC;IAC/D,IAAIyN,mBAAmB,EAAE;MACvBA,mBAAmB,CAACC,YAAY,CAAClH,SAAS,CAAChD,KAAK,IAAG;QACjD,IAAIA,KAAK,IAAIA,KAAK,CAACmK,IAAI,EAAE,EAAE;UACzB,IAAI,CAACC,iBAAiB,CAACpK,KAAK,CAACmK,IAAI,EAAE,CAAC;QACtC,CAAC,MAAM;UACL,IAAI,CAACvN,iBAAiB,GAAG,EAAE;QAC7B;MACF,CAAC,CAAC;IACJ;EACF;EAEAwN,iBAAiBA,CAAClO,YAAoB;IACpC,IAAI,IAAI,CAAC+C,sBAAsB,EAAE;IAEjC,IAAI,CAACA,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACrC,iBAAiB,GAAG,EAAE;IAE3B,MAAMyI,YAAY,GAAG,IAAI,CAACtD,cAAc,CAACsI,uBAAuB,CAACnO,YAAY,EAAE,IAAI,CAAC0E,EAAE,IAAI0J,SAAS,CAAC,CAACtH,SAAS,CAAC;MAC7GuC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACvG,sBAAsB,GAAG,KAAK;QACnC,IAAIuG,QAAQ,IAAIA,QAAQ,CAACC,YAAY,EAAE;UACrC,IAAID,QAAQ,CAACC,YAAY,CAAC8E,MAAM,EAAE;YAChC,IAAI,CAAC3N,iBAAiB,GAAG,0EAA0E;YACnG,IAAI,CAACL,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEgO,SAAS,CAAC;cAAE,WAAW,EAAE;YAAI,CAAE,CAAC;UACvE,CAAC,MAAM;YACL,IAAI,CAAC5N,iBAAiB,GAAG,EAAE;YAC3B,MAAM6N,aAAa,GAAG,IAAI,CAAClO,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,MAAM;YACjE,IAAIgO,aAAa,IAAIA,aAAa,CAAC,WAAW,CAAC,EAAE;cAC/C,OAAOA,aAAa,CAAC,WAAW,CAAC;cACjC,IAAI,CAAClO,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEgO,SAAS,CAAC3C,MAAM,CAACC,IAAI,CAAC2C,aAAa,CAAC,CAACC,MAAM,GAAGD,aAAa,GAAG,IAAI,CAAC;YAC1G;UACF;QACF;MACF,CAAC;MACD9E,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC1G,sBAAsB,GAAG,KAAK;QACnCiE,OAAO,CAACyC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAAC/I,iBAAiB,GAAG,2CAA2C;MACtE;KACD,CAAC;IACF,IAAI,CAAC6F,aAAa,CAACW,IAAI,CAACiC,YAAY,CAAC;EACvC;EAEA;EACAsF,YAAYA,CAAA;IACVzH,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;IAC3E,IAAI,CAAClB,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;IAE3C;IACA0F,UAAU,CAAC,MAAK;MACd1H,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC3E,IAAI,CAAClB,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;IAC7C,CAAC,EAAE,EAAE,CAAC;IAEN,IAAI,CAACtD,KAAK,CAACiJ,OAAO,EAAE;EACtB;EAEAC,WAAWA,CAAA;IACT5H,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;IAC3E;IACA,IAAI,CAACV,aAAa,CAACsF,OAAO,CAAC1C,YAAY,IAAG;MACxC,IAAIA,YAAY,IAAI,CAACA,YAAY,CAAC0F,MAAM,EAAE;QACxC1F,YAAY,CAAC2F,WAAW,EAAE;MAC5B;IACF,CAAC,CAAC;IACF,IAAI,CAACvI,aAAa,GAAG,EAAE;IAEvB;IACAS,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,IAAI,CAAClB,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;IAE3C;IACA0F,UAAU,CAAC,MAAK;MACd1H,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE,IAAI,CAAClB,eAAe,CAACiD,eAAe,CAAC,KAAK,CAAC;IAC7C,CAAC,EAAE,GAAG,CAAC;EACT;;qCAxiBWvD,oBAAoB,EAAAhG,EAAA,CAAAsP,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxP,EAAA,CAAAsP,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1P,EAAA,CAAAsP,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA5P,EAAA,CAAAsP,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9P,EAAA,CAAAsP,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAAhQ,EAAA,CAAAsP,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAlQ,EAAA,CAAAsP,iBAAA,CAAAa,EAAA,CAAAC,wBAAA,GAAApQ,EAAA,CAAAsP,iBAAA,CAAAtP,EAAA,CAAAqQ,iBAAA;EAAA;;UAApBrK,oBAAoB;IAAAsK,SAAA;IAAAC,MAAA;MAAAtL,EAAA;MAAAwB,MAAA;IAAA;IAAA+J,OAAA;MAAA9J,SAAA;IAAA;IAAA+J,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpB7B9Q,EAFJ,CAAAC,cAAA,aAAkC,aACW,aACR;QAC/BD,EAAA,CAAAuB,uBAAA,GAAc;QAEZvB,EADA,CAAAQ,UAAA,IAAAwQ,mCAAA,iBAAsB,IAAAC,mCAAA,iBACA;;QAE1BjR,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAC+C;QAAzBD,EAAA,CAAAwB,UAAA,mBAAA0P,iDAAA;UAAA,OAASH,GAAA,CAAA/B,YAAA,EAAc;QAAA,EAAC;QAEzEhP,EAF0E,CAAAG,YAAA,EAAI,EACtE,EACF;QAENH,EAAA,CAAAC,cAAA,aAAyC;QAEvCD,EAAA,CAAAQ,UAAA,IAAA2Q,mCAAA,iBAAsD;QAY5CnR,EALV,CAAAC,cAAA,cAAiB,cACQ,eACD,cACqF,cAChF,aAEkE;QAAnCD,EAAA,CAAAwB,UAAA,mBAAA4P,kDAAA1P,MAAA;UAAA,OAASqP,GAAA,CAAA/D,OAAA,CAAQ,OAAO,EAAAtL,MAAA,CAAS;QAAA,EAAC;QAClF1B,EAAA,CAAAE,MAAA,oBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAEsE;QAArCD,EAAA,CAAAwB,UAAA,mBAAA6P,kDAAA3P,MAAA;UAAA,OAASqP,GAAA,CAAA/D,OAAA,CAAQ,SAAS,EAAAtL,MAAA,CAAS;QAAA,EAAC;QACtF1B,EAAA,CAAAE,MAAA,wBACF;QAWVF,EAXU,CAAAG,YAAA,EAAI,EACD,EAOF,EACD,EACF,EACF;QAENH,EAAA,CAAAC,cAAA,gBAA6D;QAqH3DD,EApHA,CAAAQ,UAAA,KAAA8Q,6CAAA,4BAA6C,KAAAC,6CAAA,4BAoHE;QAkJnDvR,EADE,CAAAG,YAAA,EAAO,EACH;QAGJH,EADF,CAAAC,cAAA,eAAkD,WAC3C;QACHD,EAAA,CAAAQ,UAAA,KAAAgR,uCAAA,qBAC8B;QAGhCxR,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,WAAK,kBAC2F;QAAzBD,EAAA,CAAAwB,UAAA,mBAAAiQ,uDAAA;UAAA,OAASV,GAAA,CAAA/B,YAAA,EAAc;QAAA,EAAC;QAC3FhP,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAAAH,EAAA,CAAAE,MAAA,eACjB;QAKAF,EAJA,CAAAQ,UAAA,KAAAkR,uCAAA,qBACmD,KAAAC,uCAAA,qBAGyD;QAKlH3R,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;QA1UQH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAAqQ,GAAA,CAAA9L,EAAA,OAAc;QACdjF,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAAqQ,GAAA,CAAA9L,EAAA,OAAc;QAUlBjF,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAU,UAAA,SAAAqQ,GAAA,CAAAnK,SAAA,CAAe;QAaT5G,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA4R,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAAlK,WAAA,cAA+C;QAM/C7G,EAAA,CAAAI,SAAA,GAAiD;QAAjDJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA4R,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAAlK,WAAA,gBAAiD;QAezB7G,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAU,UAAA,cAAAqQ,GAAA,CAAAnQ,UAAA,CAAwB;QAC3CZ,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAAqQ,GAAA,CAAAlK,WAAA,YAA4B;QAoH5B7G,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAqQ,GAAA,CAAAlK,WAAA,cAA8B;QAsJpC7G,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAqQ,GAAA,CAAAlK,WAAA,cAA8B;QASe7G,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAqQ,GAAA,CAAAlK,WAAA,cAA8B;QAI3E7G,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAAqQ,GAAA,CAAAlK,WAAA,YAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}