{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, Input, ViewChild } from '@angular/core';\nlet ModalComponent = class ModalComponent {\n  modalService;\n  httpUtilService;\n  modalConfig;\n  modalContent;\n  modalRef;\n  constructor(modalService, httpUtilService) {\n    this.modalService = modalService;\n    this.httpUtilService = httpUtilService;\n  }\n  open() {\n    return new Promise(resolve => {\n      this.modalRef = this.modalService.open(this.modalContent);\n      this.modalRef.result.then(resolve, resolve);\n    });\n  }\n  close() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.modalConfig.shouldClose === undefined || (yield _this.modalConfig.shouldClose())) {\n        const result = _this.modalConfig.onClose === undefined || (yield _this.modalConfig.onClose());\n        _this.modalRef.close(result);\n      }\n    })();\n  }\n  dismiss() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.modalConfig.disableDismissButton !== undefined) {\n        return;\n      }\n      if (_this2.modalConfig.shouldDismiss === undefined || (yield _this2.modalConfig.shouldDismiss())) {\n        // Reset loading state before dismissing\n        _this2.httpUtilService.loadingSubject.next(false);\n        const result = _this2.modalConfig.onDismiss === undefined || (yield _this2.modalConfig.onDismiss());\n        _this2.modalRef.dismiss(result);\n      }\n    })();\n  }\n  ngOnDestroy() {\n    // Reset loading state when component is destroyed\n    this.httpUtilService.loadingSubject.next(false);\n  }\n};\n__decorate([Input()], ModalComponent.prototype, \"modalConfig\", void 0);\n__decorate([ViewChild('modal')], ModalComponent.prototype, \"modalContent\", void 0);\nModalComponent = __decorate([Component({\n  selector: 'app-modal',\n  templateUrl: './modal.component.html'\n})], ModalComponent);\nexport { ModalComponent };", "map": {"version": 3, "names": ["Component", "Input", "ViewChild", "ModalComponent", "modalService", "httpUtilService", "modalConfig", "modalContent", "modalRef", "constructor", "open", "Promise", "resolve", "result", "then", "close", "_this", "_asyncToGenerator", "shouldClose", "undefined", "onClose", "dismiss", "_this2", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loadingSubject", "next", "on<PERSON><PERSON><PERSON>", "ngOnDestroy", "__decorate", "selector", "templateUrl"], "sources": ["D:\\permittracker\\Angular\\src\\app\\_metronic\\partials\\layout\\modals\\modal\\modal.component.ts"], "sourcesContent": ["import { Component, Input, TemplateRef, ViewChild, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { ModalConfig } from '../modal.config';\nimport { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';\nimport { HttpUtilsService } from '../../../modules/services/http-utils.service';\n\n@Component({\n  selector: 'app-modal',\n  templateUrl: './modal.component.html',\n})\nexport class ModalComponent implements OnDestroy {\n  @Input() public modalConfig: ModalConfig;\n  @ViewChild('modal') private modalContent: TemplateRef<ModalComponent>;\n  private modalRef: NgbModalRef;\n\n  constructor(\n    private modalService: NgbModal,\n    private httpUtilService: HttpUtilsService\n  ) {}\n\n  open(): Promise<boolean> {\n    return new Promise<boolean>((resolve) => {\n      this.modalRef = this.modalService.open(this.modalContent);\n      this.modalRef.result.then(resolve, resolve);\n    });\n  }\n\n  async close(): Promise<void> {\n    if (\n      this.modalConfig.shouldClose === undefined ||\n      (await this.modalConfig.shouldClose())\n    ) {\n      const result =\n        this.modalConfig.onClose === undefined ||\n        (await this.modalConfig.onClose());\n      this.modalRef.close(result);\n    }\n  }\n\n  async dismiss(): Promise<void> {\n    if (this.modalConfig.disableDismissButton !== undefined) {\n      return;\n    }\n\n    if (\n      this.modalConfig.shouldDismiss === undefined ||\n      (await this.modalConfig.shouldDismiss())\n    ) {\n      // Reset loading state before dismissing\n      this.httpUtilService.loadingSubject.next(false);\n      const result =\n        this.modalConfig.onDismiss === undefined ||\n        (await this.modalConfig.onDismiss());\n      this.modalRef.dismiss(result);\n    }\n  }\n\n  ngOnDestroy(): void {\n    // Reset loading state when component is destroyed\n    this.httpUtilService.loadingSubject.next(false);\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,KAAK,EAAeC,SAAS,QAAmB,eAAe;AAS5E,IAAMC,cAAc,GAApB,MAAMA,cAAc;EAMfC,YAAA;EACAC,eAAA;EANMC,WAAW;EACCC,YAAY;EAChCC,QAAQ;EAEhBC,YACUL,YAAsB,EACtBC,eAAiC;IADjC,KAAAD,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;EACtB;EAEHK,IAAIA,CAAA;IACF,OAAO,IAAIC,OAAO,CAAWC,OAAO,IAAI;MACtC,IAAI,CAACJ,QAAQ,GAAG,IAAI,CAACJ,YAAY,CAACM,IAAI,CAAC,IAAI,CAACH,YAAY,CAAC;MACzD,IAAI,CAACC,QAAQ,CAACK,MAAM,CAACC,IAAI,CAACF,OAAO,EAAEA,OAAO,CAAC;IAC7C,CAAC,CAAC;EACJ;EAEMG,KAAKA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACT,IACED,KAAI,CAACV,WAAW,CAACY,WAAW,KAAKC,SAAS,WACnCH,KAAI,CAACV,WAAW,CAACY,WAAW,EAAE,CAAC,EACtC;QACA,MAAML,MAAM,GACVG,KAAI,CAACV,WAAW,CAACc,OAAO,KAAKD,SAAS,WAC/BH,KAAI,CAACV,WAAW,CAACc,OAAO,EAAE,CAAC;QACpCJ,KAAI,CAACR,QAAQ,CAACO,KAAK,CAACF,MAAM,CAAC;MAC7B;IAAC;EACH;EAEMQ,OAAOA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MACX,IAAIK,MAAI,CAAChB,WAAW,CAACiB,oBAAoB,KAAKJ,SAAS,EAAE;QACvD;MACF;MAEA,IACEG,MAAI,CAAChB,WAAW,CAACkB,aAAa,KAAKL,SAAS,WACrCG,MAAI,CAAChB,WAAW,CAACkB,aAAa,EAAE,CAAC,EACxC;QACA;QACAF,MAAI,CAACjB,eAAe,CAACoB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,MAAMb,MAAM,GACVS,MAAI,CAAChB,WAAW,CAACqB,SAAS,KAAKR,SAAS,WACjCG,MAAI,CAAChB,WAAW,CAACqB,SAAS,EAAE,CAAC;QACtCL,MAAI,CAACd,QAAQ,CAACa,OAAO,CAACR,MAAM,CAAC;MAC/B;IAAC;EACH;EAEAe,WAAWA,CAAA;IACT;IACA,IAAI,CAACvB,eAAe,CAACoB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;EACjD;CACD;AAlDiBG,UAAA,EAAf5B,KAAK,EAAE,C,kDAAiC;AACb4B,UAAA,EAA3B3B,SAAS,CAAC,OAAO,CAAC,C,mDAAmD;AAF3DC,cAAc,GAAA0B,UAAA,EAJ1B7B,SAAS,CAAC;EACT8B,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE;CACd,CAAC,C,EACW5B,cAAc,CAmD1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}