{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/modules/services/http-utils.service\";\nimport * as i2 from \"src/app/modules/services/custom-layout.utils.service\";\nimport * as i3 from \"../../services/email-template.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"src/app/modules/services/app.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction EmailTemplatesEditComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1, \" Add Email Template \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmailTemplatesEditComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1, \" Edit Email Template \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmailTemplatesEditComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmailTemplatesEditComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmailTemplatesEditComponent_div_42_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const field_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", field_r2, \" \");\n  }\n}\nfunction EmailTemplatesEditComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32, 1)(2, \"div\", 33)(3, \"div\", 34);\n    i0.ɵɵelement(4, \"textarea\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"span\", 37);\n    i0.ɵɵtext(7, \"Fields that can be used for this template:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\");\n    i0.ɵɵtemplate(9, EmailTemplatesEditComponent_div_42_li_9_Template, 2, 1, \"li\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.FieldList);\n  }\n}\nfunction EmailTemplatesEditComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40, 2);\n    i0.ɵɵelement(2, \"p\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r2.formGroup.value.emailBody, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction EmailTemplatesEditComponent_ng_container_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesEditComponent_ng_container_47_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save());\n    });\n    i0.ɵɵtext(2, \" Save \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.formGroup.invalid);\n  }\n}\nfunction EmailTemplatesEditComponent_ng_container_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesEditComponent_ng_container_48_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.update());\n    });\n    i0.ɵɵtext(2, \" Update \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.formGroup.invalid);\n  }\n}\nexport class EmailTemplatesEditComponent {\n  httpUtilService;\n  layoutUtilService;\n  emailTemplateservice;\n  fb;\n  modal;\n  cdr;\n  appService;\n  template; //get template from modal\n  id; //get template from modal\n  passEntry = new EventEmitter();\n  formGroup;\n  loginUser = {}; //store localstorage user data\n  selected = 'view'; //set default navigation tab\n  FieldList = []; //store category from API\n  constructor(\n  // private CompanyService: CompanyService,\n  httpUtilService, layoutUtilService, emailTemplateservice, fb, modal, cdr, appService) {\n    this.httpUtilService = httpUtilService;\n    this.layoutUtilService = layoutUtilService;\n    this.emailTemplateservice = emailTemplateservice;\n    this.fb = fb;\n    this.modal = modal;\n    this.cdr = cdr;\n    this.appService = appService;\n  }\n  // Method to handle cancel button click\n  onCancelClick() {\n    // Reset loading state when cancel is clicked\n    this.httpUtilService.loadingSubject.next(false);\n    this.modal.dismiss();\n  }\n  //on init\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.getAllTemplateVariables();\n    this.loadForm();\n    console.log('this.id', this.id);\n    if (this.id && this.id != 0) {\n      this.patchForm();\n    }\n  }\n  /**\n   * Form initialization\n   * Default params, validators\n   */\n  loadForm() {\n    const formGroup = {};\n    formGroup['TemplateName'] = new FormControl('', Validators.compose([Validators.required]));\n    formGroup['TemplateSubject'] = new FormControl('', Validators.compose([Validators.required]));\n    formGroup['emailBody'] = new FormControl('', Validators.compose([Validators.required]));\n    // formGroup['TemplateStatus'] = new FormControl('', Validators.compose([Validators.required]));\n    this.formGroup = this.fb.group(formGroup);\n    // this.patchForm();\n  }\n  //function for patching API email template data\n  patchForm() {\n    this.emailTemplateservice.getEmailTemplate(this.id).subscribe(res => {\n      console.log('res.responseData.data.templateName', res);\n      if (res && res.isFault == false) {\n        this.formGroup.patchValue({\n          TemplateName: res.responseData.templateName,\n          TemplateSubject: res.responseData.emailSubject,\n          emailBody: res.responseData.emailBody\n          // TemplateStatus: this.template.TemplateStatus\n        });\n      }\n    });\n  }\n  controlHasError(validation, controlName) {\n    const control = this.formGroup.controls[controlName];\n    if (!control) {\n      return false;\n    }\n    let result = control.hasError(validation) && (control.dirty || control.touched);\n    return result;\n  }\n  //Form Submit\n  save() {\n    const formData = this.formGroup.value;\n    var query = {\n      // TemplateId: this.template.template_id,\n      templateName: formData.TemplateName,\n      emailSubject: formData.TemplateSubject,\n      emailBody: formData.emailBody,\n      // TemplateStatus: formData.TemplateStatus,\n      LoggedId: this.loginUser.userId\n    };\n    // console.log('this.loginUser', this.loginUser);\n    this.httpUtilService.loadingSubject.next(true);\n    this.emailTemplateservice.createEmailTemplate(query).subscribe(res => {\n      this.passEntry.emit(true);\n      this.httpUtilService.loadingSubject.next(false);\n      this.modal.close();\n      this.layoutUtilService.showSuccess(res.responseData.message, '');\n    });\n  }\n  //function to navigate tabs in email body\n  showTab(tab, $event) {\n    this.selected = tab;\n    this.cdr.markForCheck();\n  }\n  //get template catgory from API\n  getAllTemplateVariables() {\n    var query = {};\n    this.httpUtilService.loadingSubject.next(true);\n    this.emailTemplateservice.getEmailCategories().subscribe(res => {\n      var templatecategory = [];\n      templatecategory = res.responseData;\n      this.httpUtilService.loadingSubject.next(false);\n      const indexTemplate = templatecategory.findIndex(temp => temp.CategoryName === this.template.emailType);\n      if (indexTemplate !== -1) {\n        this.FieldList = templatecategory[indexTemplate].FieldsList;\n      }\n    });\n  }\n  update() {\n    const formData = this.formGroup.value;\n    let data = {\n      templatePID: this.id,\n      templateName: formData.TemplateName,\n      emailSubject: formData.TemplateSubject,\n      emailBody: formData.emailBody,\n      LoggedId: this.loginUser.userId\n    };\n    this.emailTemplateservice.updateEmailTemplate(data).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.layoutUtilService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  static ɵfac = function EmailTemplatesEditComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EmailTemplatesEditComponent)(i0.ɵɵdirectiveInject(i1.HttpUtilsService), i0.ɵɵdirectiveInject(i2.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i3.EmailTemplateService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.NgbActiveModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i6.AppService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmailTemplatesEditComponent,\n    selectors: [[\"app-email-templates-edit\"]],\n    inputs: {\n      template: \"template\",\n      id: \"id\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 49,\n    vars: 15,\n    consts: [[\"tabs\", \"\"], [\"edit\", \"\"], [\"view\", \"\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"example-modal-sizes-title-lg\", 1, \"modal-title\"], [\"class\", \"fw-bold fs-2 text-white\", 4, \"ngIf\"], [1, \"float-right\", \"cursor-pointer\", \"ir-12\"], [1, \"btn\", \"btn-icon\", \"btn-sm\", \"pl-08\", 3, \"click\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\"], [1, \"modal-body\", \"large-modal-body\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"form-group\", \"row\", \"mb-2\"], [1, \"col-lg-12\"], [1, \"fw-bold\", \"form-label\"], [1, \"text-danger\"], [\"type\", \"text\", \"name\", \"templateName\", \"placeholder\", \"Type Here\", \"autocomplete\", \"off\", \"formControlName\", \"TemplateName\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"custom-error-css\", 4, \"ngIf\"], [\"type\", \"text\", \"name\", \"TemplateSubject\", \"placeholder\", \"Type Here\", \"autocomplete\", \"off\", \"formControlName\", \"TemplateSubject\", 1, \"form-control\", \"form-control-sm\"], [\"id\", \"tabs\"], [1, \"d-flex\", \"h-30px\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-5\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", \"fs-4\", 3, \"click\", \"ngClass\"], [\"id\", \"myTabContent\", 1, \"tab-content\", \"mt-5\"], [\"class\", \"row\", \"id\", \"edit\", \"view\", \"tabpanel\", 4, \"ngIf\"], [\"class\", \"view_box\", \"id\", \"view\", \"view\", \"tabpanel\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [4, \"ngIf\"], [1, \"fw-bold\", \"fs-2\", \"text-white\"], [1, \"custom-error-css\"], [\"id\", \"edit\", \"view\", \"tabpanel\", 1, \"row\"], [1, \"col-8\"], [1, \"\"], [\"type\", \"text\", \"rows\", \"8\", \"cols\", \"6\", \"formControlName\", \"emailBody\", 1, \"form-control\"], [1, \"col-4\"], [1, \"fw-semibold\", \"fs-6\", \"mb-2\"], [\"class\", \"fs-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"fs-6\"], [\"id\", \"view\", \"view\", \"tabpanel\", 1, \"view_box\"], [3, \"innerHtml\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-elevate\", \"btn-sm\", 3, \"click\", \"disabled\"]],\n    template: function EmailTemplatesEditComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5);\n        i0.ɵɵtemplate(3, EmailTemplatesEditComponent_div_3_Template, 2, 0, \"div\", 6)(4, EmailTemplatesEditComponent_div_4_Template, 2, 0, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 7)(6, \"a\", 8);\n        i0.ɵɵlistener(\"click\", function EmailTemplatesEditComponent_Template_a_click_6_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onCancelClick());\n        });\n        i0.ɵɵelement(7, \"i\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 10);\n        i0.ɵɵelementContainerStart(9);\n        i0.ɵɵelementStart(10, \"form\", 11)(11, \"div\", 12)(12, \"div\", 13)(13, \"label\", 14);\n        i0.ɵɵtext(14, \"Template Name \");\n        i0.ɵɵelementStart(15, \"sup\", 15);\n        i0.ɵɵtext(16, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(17, \"input\", 16);\n        i0.ɵɵtemplate(18, EmailTemplatesEditComponent_div_18_Template, 2, 0, \"div\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 12)(20, \"div\", 13)(21, \"label\", 14);\n        i0.ɵɵtext(22, \"Email Subject \");\n        i0.ɵɵelementStart(23, \"sup\", 15);\n        i0.ɵɵtext(24, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(25, \"input\", 18);\n        i0.ɵɵtemplate(26, EmailTemplatesEditComponent_div_26_Template, 2, 0, \"div\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 19, 0)(29, \"label\", 14);\n        i0.ɵɵtext(30, \"Email Body \");\n        i0.ɵɵelementStart(31, \"sup\", 15);\n        i0.ɵɵtext(32, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 20)(34, \"ul\", 21)(35, \"li\", 22)(36, \"a\", 23);\n        i0.ɵɵlistener(\"click\", function EmailTemplatesEditComponent_Template_a_click_36_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.showTab(\"view\", $event));\n        });\n        i0.ɵɵtext(37, \" View \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"li\", 22)(39, \"a\", 23);\n        i0.ɵɵlistener(\"click\", function EmailTemplatesEditComponent_Template_a_click_39_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.showTab(\"edit\", $event));\n        });\n        i0.ɵɵtext(40, \" Edit \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(41, \"div\", 24);\n        i0.ɵɵtemplate(42, EmailTemplatesEditComponent_div_42_Template, 10, 1, \"div\", 25)(43, EmailTemplatesEditComponent_div_43_Template, 3, 1, \"div\", 26);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"div\", 27)(45, \"button\", 28);\n        i0.ɵɵlistener(\"click\", function EmailTemplatesEditComponent_Template_button_click_45_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onCancelClick());\n        });\n        i0.ɵɵtext(46, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(47, EmailTemplatesEditComponent_ng_container_47_Template, 3, 1, \"ng-container\", 29)(48, EmailTemplatesEditComponent_ng_container_48_Template, 3, 1, \"ng-container\", 29);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx.id || ctx.id == 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id && ctx.id != 0);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"formGroup\", ctx.formGroup);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.controlHasError(\"required\", \"TemplateName\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.controlHasError(\"required\", \"TemplateSubject\"));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx.selected === \"view\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, ctx.selected === \"edit\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selected === \"edit\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selected === \"view\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", !ctx.id || ctx.id == 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id && ctx.id != 0);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "field_r2", "ɵɵelement", "ɵɵtemplate", "EmailTemplatesEditComponent_div_42_li_9_Template", "ɵɵproperty", "ctx_r2", "FieldList", "formGroup", "value", "emailBody", "ɵɵsanitizeHtml", "ɵɵelementContainerStart", "ɵɵlistener", "EmailTemplatesEditComponent_ng_container_47_Template_button_click_1_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "save", "invalid", "EmailTemplatesEditComponent_ng_container_48_Template_button_click_1_listener", "_r5", "update", "EmailTemplatesEditComponent", "httpUtilService", "layoutUtilService", "emailTemplateservice", "fb", "modal", "cdr", "appService", "template", "id", "passEntry", "loginUser", "selected", "constructor", "onCancelClick", "loadingSubject", "next", "dismiss", "ngOnInit", "getLoggedInUser", "getAllTemplateVariables", "loadForm", "console", "log", "patchForm", "compose", "required", "group", "getEmailTemplate", "subscribe", "res", "<PERSON><PERSON><PERSON>", "patchValue", "TemplateName", "responseData", "templateName", "TemplateSubject", "emailSubject", "controlHasError", "validation", "controlName", "control", "controls", "result", "<PERSON><PERSON><PERSON><PERSON>", "dirty", "touched", "formData", "query", "LoggedId", "userId", "createEmailTemplate", "emit", "close", "showSuccess", "message", "showTab", "tab", "$event", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getEmailCategories", "templatecategory", "indexTemplate", "findIndex", "temp", "CategoryName", "emailType", "FieldsList", "data", "templatePID", "updateEmailTemplate", "showError", "ɵɵdirectiveInject", "i1", "HttpUtilsService", "i2", "CustomLayoutUtilsService", "i3", "EmailTemplateService", "i4", "FormBuilder", "i5", "NgbActiveModal", "ChangeDetectorRef", "i6", "AppService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "EmailTemplatesEditComponent_Template", "rf", "ctx", "EmailTemplatesEditComponent_div_3_Template", "EmailTemplatesEditComponent_div_4_Template", "EmailTemplatesEditComponent_Template_a_click_6_listener", "_r1", "EmailTemplatesEditComponent_div_18_Template", "EmailTemplatesEditComponent_div_26_Template", "EmailTemplatesEditComponent_Template_a_click_36_listener", "EmailTemplatesEditComponent_Template_a_click_39_listener", "EmailTemplatesEditComponent_div_42_Template", "EmailTemplatesEditComponent_div_43_Template", "EmailTemplatesEditComponent_Template_button_click_45_listener", "EmailTemplatesEditComponent_ng_container_47_Template", "EmailTemplatesEditComponent_ng_container_48_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\email-templates-edit\\email-templates-edit.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\email-templates-edit\\email-templates-edit.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport {\r\n  ChangeDetectorRef,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  Output,\r\n} from '@angular/core';\r\nimport {\r\n  FormBuilder,\r\n  FormControl,\r\n  FormGroup,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { AppService } from 'src/app/modules/services/app.service';\r\nimport { CustomLayoutUtilsService } from 'src/app/modules/services/custom-layout.utils.service';\r\nimport { HttpUtilsService } from 'src/app/modules/services/http-utils.service';\r\nimport { EmailTemplateService } from '../../services/email-template.service';\r\n@Component({\r\n  selector: 'app-email-templates-edit',\r\n  templateUrl: './email-templates-edit.component.html',\r\n  styleUrl: './email-templates-edit.component.scss'\r\n})\r\nexport class EmailTemplatesEditComponent {\r\n @Input() template: any; //get template from modal\r\n  @Input() id: any; //get template from modal\r\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\r\n  formGroup: FormGroup;\r\n  loginUser: any = {}; //store localstorage user data\r\n\r\n  selected: any = 'view'; //set default navigation tab\r\n  FieldList: any = []; //store category from API\r\n  constructor(\r\n    // private CompanyService: CompanyService,\r\n    private httpUtilService: HttpUtilsService,\r\n    private layoutUtilService: CustomLayoutUtilsService,\r\n    private emailTemplateservice: EmailTemplateService,\r\n    private fb: FormBuilder,\r\n    public modal: NgbActiveModal,\r\n    private cdr: ChangeDetectorRef,\r\n    public appService: AppService\r\n  ) {}\r\n\r\n  // Method to handle cancel button click\r\n  onCancelClick(): void {\r\n    // Reset loading state when cancel is clicked\r\n    this.httpUtilService.loadingSubject.next(false);\r\n    this.modal.dismiss();\r\n  }\r\n  //on init\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n    this.getAllTemplateVariables();\r\n    this.loadForm();\r\n    console.log('this.id', this.id);\r\n    if (this.id && this.id != 0) {\r\n      this.patchForm();\r\n    }\r\n  }\r\n  /**\r\n   * Form initialization\r\n   * Default params, validators\r\n   */\r\n  loadForm() {\r\n    const formGroup: any = {};\r\n    formGroup['TemplateName'] = new FormControl(\r\n      '',\r\n      Validators.compose([Validators.required])\r\n    );\r\n    formGroup['TemplateSubject'] = new FormControl(\r\n      '',\r\n      Validators.compose([Validators.required])\r\n    );\r\n    formGroup['emailBody'] = new FormControl(\r\n      '',\r\n      Validators.compose([Validators.required])\r\n    );\r\n    // formGroup['TemplateStatus'] = new FormControl('', Validators.compose([Validators.required]));\r\n\r\n    this.formGroup = this.fb.group(formGroup);\r\n    // this.patchForm();\r\n  }\r\n\r\n  //function for patching API email template data\r\n  patchForm() {\r\n    this.emailTemplateservice\r\n      .getEmailTemplate(this.id)\r\n      .subscribe((res: any) => {\r\n        console.log('res.responseData.data.templateName', res);\r\n        if (res && res.isFault == false) {\r\n          this.formGroup.patchValue({\r\n            TemplateName: res.responseData.templateName,\r\n            TemplateSubject: res.responseData.emailSubject,\r\n            emailBody: res.responseData.emailBody,\r\n            // TemplateStatus: this.template.TemplateStatus\r\n          });\r\n        }\r\n      });\r\n  }\r\n\r\n  controlHasError(validation: any, controlName: string | number): boolean {\r\n    const control = this.formGroup.controls[controlName];\r\n    if (!control) {\r\n      return false;\r\n    }\r\n    let result =\r\n      control.hasError(validation) && (control.dirty || control.touched);\r\n    return result;\r\n  }\r\n\r\n  //Form Submit\r\n  save() {\r\n    const formData = this.formGroup.value;\r\n    var query = {\r\n      // TemplateId: this.template.template_id,\r\n      templateName: formData.TemplateName,\r\n      emailSubject: formData.TemplateSubject,\r\n      emailBody: formData.emailBody,\r\n      // TemplateStatus: formData.TemplateStatus,\r\n      LoggedId: this.loginUser.userId,\r\n    };\r\n\r\n    // console.log('this.loginUser', this.loginUser);\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.emailTemplateservice\r\n      .createEmailTemplate(query)\r\n      .subscribe((res: any) => {\r\n        this.passEntry.emit(true);\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.modal.close();\r\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\r\n      });\r\n  }\r\n\r\n  //function to navigate tabs in email body\r\n  showTab(tab: any, $event: any) {\r\n    this.selected = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n  //get template catgory from API\r\n  getAllTemplateVariables() {\r\n    var query = {};\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.emailTemplateservice.getEmailCategories().subscribe((res: any) => {\r\n      var templatecategory = [];\r\n      templatecategory = res.responseData;\r\n\r\n      this.httpUtilService.loadingSubject.next(false);\r\n      const indexTemplate = templatecategory.findIndex(\r\n        (temp: any) => temp.CategoryName === this.template.emailType\r\n      );\r\n      if (indexTemplate !== -1) {\r\n        this.FieldList = templatecategory[indexTemplate].FieldsList;\r\n      }\r\n    });\r\n  }\r\n\r\n  update() {\r\n    const formData = this.formGroup.value;\r\n\r\n    let data = {\r\n      templatePID: this.id,\r\n      templateName: formData.TemplateName,\r\n      emailSubject: formData.TemplateSubject,\r\n      emailBody: formData.emailBody,\r\n      LoggedId: this.loginUser.userId,\r\n    };\r\n    this.emailTemplateservice.updateEmailTemplate(data).subscribe((res) => {\r\n      this.httpUtilService.loadingSubject.next(false);\r\n      if (!res.isFault) {\r\n        this.layoutUtilService.showSuccess(res.responseData.message, '');\r\n        this.passEntry.emit(true);\r\n        this.modal.close();\r\n      } else {\r\n        this.layoutUtilService.showError(res.responseData.message, '');\r\n        this.passEntry.emit(false);\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"modal-content\">\r\n  <div class=\"modal-header\">\r\n    <div class=\"modal-title\" id=\"example-modal-sizes-title-lg\">\r\n      <div *ngIf=\"!id || id == 0\" class=\"fw-bold fs-2 text-white\">\r\n        Add Email Template\r\n      </div>\r\n      <div *ngIf=\"id && id != 0\" class=\"fw-bold fs-2 text-white\">\r\n        Edit Email Template\r\n      </div>\r\n    </div>\r\n    <div class=\"float-right cursor-pointer ir-12\">\r\n      <a class=\"btn btn-icon btn-sm pl-08\" (click)=\"onCancelClick()\">\r\n        <i class=\"fa-solid fs-2 fa-xmark text-white\"></i>\r\n      </a>\r\n    </div>\r\n  </div>\r\n  <div class=\"modal-body large-modal-body\" >\r\n    <!-- style=\"min-height: auto\" -->\r\n    <ng-container>\r\n      <form class=\"form form-label-right\" [formGroup]=\"formGroup\">\r\n        <div class=\"form-group row mb-2\">\r\n          <div class=\"col-lg-12\">\r\n            <label class=\"fw-bold form-label\"\r\n              >Template Name <sup class=\"text-danger\">*</sup></label\r\n            >\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control form-control-sm\"\r\n              name=\"templateName\"\r\n              placeholder=\"Type Here\"\r\n              autocomplete=\"off\"\r\n              formControlName=\"TemplateName\"\r\n            />\r\n            <div\r\n              class=\"custom-error-css\"\r\n              *ngIf=\"controlHasError('required', 'TemplateName')\"\r\n            >\r\n              Required Field\r\n            </div>\r\n            <!-- <p>{{ template.templateName }}</p> -->\r\n          </div>\r\n        </div>\r\n        <div class=\"form-group row mb-2\">\r\n          <div class=\"col-lg-12\">\r\n            <label class=\"fw-bold form-label\"\r\n              >Email Subject <sup class=\"text-danger\">*</sup></label\r\n            >\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control form-control-sm\"\r\n              name=\"TemplateSubject\"\r\n              placeholder=\"Type Here\"\r\n              autocomplete=\"off\"\r\n              formControlName=\"TemplateSubject\"\r\n            />\r\n            <div\r\n              class=\"custom-error-css\"\r\n              *ngIf=\"controlHasError('required', 'TemplateSubject')\"\r\n            >\r\n              Required Field\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div id=\"tabs\" #tabs>\r\n          <label class=\"fw-bold form-label\"\r\n            >Email Body <sup class=\"text-danger\">*</sup></label\r\n          >\r\n          <div class=\"d-flex h-30px\">\r\n            <ul\r\n              class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold flex-nowrap\"\r\n            >\r\n              <li class=\"nav-item\">\r\n                <a\r\n                  class=\"nav-link text-active-primary me-6 cursor-pointer fs-4\"\r\n                  data-toggle=\"tab\"\r\n                  [ngClass]=\"{ active: selected === 'view' }\"\r\n                  (click)=\"showTab('view', $event)\"\r\n                >\r\n                  View\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item\">\r\n                <a\r\n                  class=\"nav-link text-active-primary me-6 cursor-pointer fs-4\"\r\n                  data-toggle=\"tab\"\r\n                  [ngClass]=\"{ active: selected === 'edit' }\"\r\n                  (click)=\"showTab('edit', $event)\"\r\n                >\r\n                  Edit\r\n                </a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div class=\"tab-content mt-5\" id=\"myTabContent\">\r\n            <div\r\n              class=\"row\"\r\n              #edit\r\n              id=\"edit\"\r\n              view=\"tabpanel\"\r\n              *ngIf=\"selected === 'edit'\"\r\n            >\r\n              <div class=\"col-8\">\r\n                <div class=\"\">\r\n                  <textarea\r\n                    type=\"text\"\r\n                    class=\"form-control\"\r\n                    rows=\"8\"\r\n                    cols=\"6\"\r\n                    formControlName=\"emailBody\"\r\n                  ></textarea>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-4\">\r\n                <span class=\"fw-semibold fs-6 mb-2\"\r\n                  >Fields that can be used for this template:</span\r\n                >\r\n                <ul>\r\n                  <li class=\"fs-6\" *ngFor=\"let field of FieldList\">\r\n                    {{ field }}\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n            <div\r\n              class=\"view_box\"\r\n              #view\r\n              id=\"view\"\r\n              view=\"tabpanel\"\r\n              *ngIf=\"selected === 'view'\"\r\n            >\r\n              <p [innerHtml]=\"formGroup.value.emailBody\"></p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </ng-container>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button\r\n      type=\"button\"\r\n      class=\"btn btn-danger btn-sm btn-elevate mr-2\"\r\n      (click)=\"onCancelClick()\"\r\n    >\r\n      Cancel\r\n    </button>\r\n    <ng-container *ngIf=\"!id || id == 0\">\r\n      <button\r\n        type=\"submit\"\r\n        class=\"btn btn-primary btn-elevate btn-sm\"\r\n        [disabled]=\"formGroup.invalid\"\r\n        (click)=\"save()\"\r\n      >\r\n        Save\r\n      </button>\r\n    </ng-container>\r\n    <ng-container *ngIf=\"id && id != 0\">\r\n      <button\r\n        type=\"submit\"\r\n        class=\"btn btn-primary btn-elevate btn-sm\"\r\n        [disabled]=\"formGroup.invalid\"\r\n        (click)=\"update()\"\r\n      >\r\n        Update\r\n      </button>\r\n    </ng-container>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAEEA,YAAY,QAIP,eAAe;AACtB,SAEEC,WAAW,EAEXC,UAAU,QACL,gBAAgB;;;;;;;;;;;;;;ICVjBC,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBAH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAiBNH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyDAH,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,QAAA,MACF;;;;;IAjBFN,EARJ,CAAAC,cAAA,iBAMC,cACoB,cACH;IACZD,EAAA,CAAAO,SAAA,mBAMY;IAEhBP,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAAmB,eAEd;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAAAF,EAAA,CAAAG,YAAA,EAC5C;IACDH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAQ,UAAA,IAAAC,gDAAA,iBAAiD;IAKvDT,EAFI,CAAAG,YAAA,EAAK,EACD,EACF;;;;IALmCH,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAU,UAAA,YAAAC,MAAA,CAAAC,SAAA,CAAY;;;;;IAMrDZ,EAAA,CAAAC,cAAA,iBAMC;IACCD,EAAA,CAAAO,SAAA,YAA+C;IACjDP,EAAA,CAAAG,YAAA,EAAM;;;;IADDH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAU,UAAA,cAAAC,MAAA,CAAAE,SAAA,CAAAC,KAAA,CAAAC,SAAA,EAAAf,EAAA,CAAAgB,cAAA,CAAuC;;;;;;IAepDhB,EAAA,CAAAiB,uBAAA,GAAqC;IACnCjB,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAkB,UAAA,mBAAAC,6EAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAX,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASZ,MAAA,CAAAa,IAAA,EAAM;IAAA,EAAC;IAEhBxB,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAJPH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAU,UAAA,aAAAC,MAAA,CAAAE,SAAA,CAAAY,OAAA,CAA8B;;;;;;IAMlCzB,EAAA,CAAAiB,uBAAA,GAAoC;IAClCjB,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAkB,UAAA,mBAAAQ,6EAAA;MAAA1B,EAAA,CAAAoB,aAAA,CAAAO,GAAA;MAAA,MAAAhB,MAAA,GAAAX,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASZ,MAAA,CAAAiB,MAAA,EAAQ;IAAA,EAAC;IAElB5B,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAJPH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAU,UAAA,aAAAC,MAAA,CAAAE,SAAA,CAAAY,OAAA,CAA8B;;;ADvItC,OAAM,MAAOI,2BAA2B;EAW5BC,eAAA;EACAC,iBAAA;EACAC,oBAAA;EACAC,EAAA;EACDC,KAAA;EACCC,GAAA;EACDC,UAAA;EAhBDC,QAAQ,CAAM,CAAC;EACdC,EAAE,CAAM,CAAC;EACRC,SAAS,GAAsB,IAAI1C,YAAY,EAAE;EAC3DgB,SAAS;EACT2B,SAAS,GAAQ,EAAE,CAAC,CAAC;EAErBC,QAAQ,GAAQ,MAAM,CAAC,CAAC;EACxB7B,SAAS,GAAQ,EAAE,CAAC,CAAC;EACrB8B;EACE;EACQZ,eAAiC,EACjCC,iBAA2C,EAC3CC,oBAA0C,EAC1CC,EAAe,EAChBC,KAAqB,EACpBC,GAAsB,EACvBC,UAAsB;IANrB,KAAAN,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,GAAG,GAAHA,GAAG;IACJ,KAAAC,UAAU,GAAVA,UAAU;EAChB;EAEH;EACAO,aAAaA,CAAA;IACX;IACA,IAAI,CAACb,eAAe,CAACc,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;IAC/C,IAAI,CAACX,KAAK,CAACY,OAAO,EAAE;EACtB;EACA;EACAC,QAAQA,CAAA;IACN,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,UAAU,CAACY,eAAe,EAAE;IAClD,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,QAAQ,EAAE;IACfC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACd,EAAE,CAAC;IAC/B,IAAI,IAAI,CAACA,EAAE,IAAI,IAAI,CAACA,EAAE,IAAI,CAAC,EAAE;MAC3B,IAAI,CAACe,SAAS,EAAE;IAClB;EACF;EACA;;;;EAIAH,QAAQA,CAAA;IACN,MAAMrC,SAAS,GAAQ,EAAE;IACzBA,SAAS,CAAC,cAAc,CAAC,GAAG,IAAIf,WAAW,CACzC,EAAE,EACFC,UAAU,CAACuD,OAAO,CAAC,CAACvD,UAAU,CAACwD,QAAQ,CAAC,CAAC,CAC1C;IACD1C,SAAS,CAAC,iBAAiB,CAAC,GAAG,IAAIf,WAAW,CAC5C,EAAE,EACFC,UAAU,CAACuD,OAAO,CAAC,CAACvD,UAAU,CAACwD,QAAQ,CAAC,CAAC,CAC1C;IACD1C,SAAS,CAAC,WAAW,CAAC,GAAG,IAAIf,WAAW,CACtC,EAAE,EACFC,UAAU,CAACuD,OAAO,CAAC,CAACvD,UAAU,CAACwD,QAAQ,CAAC,CAAC,CAC1C;IACD;IAEA,IAAI,CAAC1C,SAAS,GAAG,IAAI,CAACoB,EAAE,CAACuB,KAAK,CAAC3C,SAAS,CAAC;IACzC;EACF;EAEA;EACAwC,SAASA,CAAA;IACP,IAAI,CAACrB,oBAAoB,CACtByB,gBAAgB,CAAC,IAAI,CAACnB,EAAE,CAAC,CACzBoB,SAAS,CAAEC,GAAQ,IAAI;MACtBR,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEO,GAAG,CAAC;MACtD,IAAIA,GAAG,IAAIA,GAAG,CAACC,OAAO,IAAI,KAAK,EAAE;QAC/B,IAAI,CAAC/C,SAAS,CAACgD,UAAU,CAAC;UACxBC,YAAY,EAAEH,GAAG,CAACI,YAAY,CAACC,YAAY;UAC3CC,eAAe,EAAEN,GAAG,CAACI,YAAY,CAACG,YAAY;UAC9CnD,SAAS,EAAE4C,GAAG,CAACI,YAAY,CAAChD;UAC5B;SACD,CAAC;MACJ;IACF,CAAC,CAAC;EACN;EAEAoD,eAAeA,CAACC,UAAe,EAAEC,WAA4B;IAC3D,MAAMC,OAAO,GAAG,IAAI,CAACzD,SAAS,CAAC0D,QAAQ,CAACF,WAAW,CAAC;IACpD,IAAI,CAACC,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;IACA,IAAIE,MAAM,GACRF,OAAO,CAACG,QAAQ,CAACL,UAAU,CAAC,KAAKE,OAAO,CAACI,KAAK,IAAIJ,OAAO,CAACK,OAAO,CAAC;IACpE,OAAOH,MAAM;EACf;EAEA;EACAhD,IAAIA,CAAA;IACF,MAAMoD,QAAQ,GAAG,IAAI,CAAC/D,SAAS,CAACC,KAAK;IACrC,IAAI+D,KAAK,GAAG;MACV;MACAb,YAAY,EAAEY,QAAQ,CAACd,YAAY;MACnCI,YAAY,EAAEU,QAAQ,CAACX,eAAe;MACtClD,SAAS,EAAE6D,QAAQ,CAAC7D,SAAS;MAC7B;MACA+D,QAAQ,EAAE,IAAI,CAACtC,SAAS,CAACuC;KAC1B;IAED;IACA,IAAI,CAACjD,eAAe,CAACc,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACb,oBAAoB,CACtBgD,mBAAmB,CAACH,KAAK,CAAC,CAC1BnB,SAAS,CAAEC,GAAQ,IAAI;MACtB,IAAI,CAACpB,SAAS,CAAC0C,IAAI,CAAC,IAAI,CAAC;MACzB,IAAI,CAACnD,eAAe,CAACc,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACX,KAAK,CAACgD,KAAK,EAAE;MAClB,IAAI,CAACnD,iBAAiB,CAACoD,WAAW,CAACxB,GAAG,CAACI,YAAY,CAACqB,OAAO,EAAE,EAAE,CAAC;IAClE,CAAC,CAAC;EACN;EAEA;EACAC,OAAOA,CAACC,GAAQ,EAAEC,MAAW;IAC3B,IAAI,CAAC9C,QAAQ,GAAG6C,GAAG;IACnB,IAAI,CAACnD,GAAG,CAACqD,YAAY,EAAE;EACzB;EACA;EACAvC,uBAAuBA,CAAA;IACrB,IAAI4B,KAAK,GAAG,EAAE;IACd,IAAI,CAAC/C,eAAe,CAACc,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACb,oBAAoB,CAACyD,kBAAkB,EAAE,CAAC/B,SAAS,CAAEC,GAAQ,IAAI;MACpE,IAAI+B,gBAAgB,GAAG,EAAE;MACzBA,gBAAgB,GAAG/B,GAAG,CAACI,YAAY;MAEnC,IAAI,CAACjC,eAAe,CAACc,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,MAAM8C,aAAa,GAAGD,gBAAgB,CAACE,SAAS,CAC7CC,IAAS,IAAKA,IAAI,CAACC,YAAY,KAAK,IAAI,CAACzD,QAAQ,CAAC0D,SAAS,CAC7D;MACD,IAAIJ,aAAa,KAAK,CAAC,CAAC,EAAE;QACxB,IAAI,CAAC/E,SAAS,GAAG8E,gBAAgB,CAACC,aAAa,CAAC,CAACK,UAAU;MAC7D;IACF,CAAC,CAAC;EACJ;EAEApE,MAAMA,CAAA;IACJ,MAAMgD,QAAQ,GAAG,IAAI,CAAC/D,SAAS,CAACC,KAAK;IAErC,IAAImF,IAAI,GAAG;MACTC,WAAW,EAAE,IAAI,CAAC5D,EAAE;MACpB0B,YAAY,EAAEY,QAAQ,CAACd,YAAY;MACnCI,YAAY,EAAEU,QAAQ,CAACX,eAAe;MACtClD,SAAS,EAAE6D,QAAQ,CAAC7D,SAAS;MAC7B+D,QAAQ,EAAE,IAAI,CAACtC,SAAS,CAACuC;KAC1B;IACD,IAAI,CAAC/C,oBAAoB,CAACmE,mBAAmB,CAACF,IAAI,CAAC,CAACvC,SAAS,CAAEC,GAAG,IAAI;MACpE,IAAI,CAAC7B,eAAe,CAACc,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACc,GAAG,CAACC,OAAO,EAAE;QAChB,IAAI,CAAC7B,iBAAiB,CAACoD,WAAW,CAACxB,GAAG,CAACI,YAAY,CAACqB,OAAO,EAAE,EAAE,CAAC;QAChE,IAAI,CAAC7C,SAAS,CAAC0C,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC/C,KAAK,CAACgD,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACnD,iBAAiB,CAACqE,SAAS,CAACzC,GAAG,CAACI,YAAY,CAACqB,OAAO,EAAE,EAAE,CAAC;QAC9D,IAAI,CAAC7C,SAAS,CAAC0C,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;;qCA3JWpD,2BAA2B,EAAA7B,EAAA,CAAAqG,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAvG,EAAA,CAAAqG,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAAzG,EAAA,CAAAqG,iBAAA,CAAAK,EAAA,CAAAC,oBAAA,GAAA3G,EAAA,CAAAqG,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7G,EAAA,CAAAqG,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA/G,EAAA,CAAAqG,iBAAA,CAAArG,EAAA,CAAAgH,iBAAA,GAAAhH,EAAA,CAAAqG,iBAAA,CAAAY,EAAA,CAAAC,UAAA;EAAA;;UAA3BrF,2BAA2B;IAAAsF,SAAA;IAAAC,MAAA;MAAA/E,QAAA;MAAAC,EAAA;IAAA;IAAA+E,OAAA;MAAA9E,SAAA;IAAA;IAAA+E,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAnF,QAAA,WAAAoF,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCtBpC1H,EAFJ,CAAAC,cAAA,aAA2B,aACC,aACmC;QAIzDD,EAHA,CAAAQ,UAAA,IAAAoH,0CAAA,iBAA4D,IAAAC,0CAAA,iBAGD;QAG7D7H,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAA8C,WACmB;QAA1BD,EAAA,CAAAkB,UAAA,mBAAA4G,wDAAA;UAAA9H,EAAA,CAAAoB,aAAA,CAAA2G,GAAA;UAAA,OAAA/H,EAAA,CAAAuB,WAAA,CAASoG,GAAA,CAAAhF,aAAA,EAAe;QAAA,EAAC;QAC5D3C,EAAA,CAAAO,SAAA,WAAiD;QAGvDP,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QACNH,EAAA,CAAAC,cAAA,cAA0C;QAExCD,EAAA,CAAAiB,uBAAA,GAAc;QAINjB,EAHN,CAAAC,cAAA,gBAA4D,eACzB,eACR,iBAElB;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAMF,EAAN,CAAAG,YAAA,EAAM,EAChD;QACDH,EAAA,CAAAO,SAAA,iBAOE;QACFP,EAAA,CAAAQ,UAAA,KAAAwH,2CAAA,kBAGC;QAKLhI,EADE,CAAAG,YAAA,EAAM,EACF;QAGFH,EAFJ,CAAAC,cAAA,eAAiC,eACR,iBAElB;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAMF,EAAN,CAAAG,YAAA,EAAM,EAChD;QACDH,EAAA,CAAAO,SAAA,iBAOE;QACFP,EAAA,CAAAQ,UAAA,KAAAyH,2CAAA,kBAGC;QAILjI,EADE,CAAAG,YAAA,EAAM,EACF;QAEJH,EADF,CAAAC,cAAA,kBAAqB,iBAEhB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAMF,EAAN,CAAAG,YAAA,EAAM,EAC7C;QAMKH,EALN,CAAAC,cAAA,eAA2B,cAGxB,cACsB,aAMlB;QADCD,EAAA,CAAAkB,UAAA,mBAAAgH,yDAAA3C,MAAA;UAAAvF,EAAA,CAAAoB,aAAA,CAAA2G,GAAA;UAAA,OAAA/H,EAAA,CAAAuB,WAAA,CAASoG,GAAA,CAAAtC,OAAA,CAAQ,MAAM,EAAAE,MAAA,CAAS;QAAA,EAAC;QAEjCvF,EAAA,CAAAE,MAAA,cACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAMlB;QADCD,EAAA,CAAAkB,UAAA,mBAAAiH,yDAAA5C,MAAA;UAAAvF,EAAA,CAAAoB,aAAA,CAAA2G,GAAA;UAAA,OAAA/H,EAAA,CAAAuB,WAAA,CAASoG,GAAA,CAAAtC,OAAA,CAAQ,MAAM,EAAAE,MAAA,CAAS;QAAA,EAAC;QAEjCvF,EAAA,CAAAE,MAAA,cACF;QAGNF,EAHM,CAAAG,YAAA,EAAI,EACD,EACF,EACD;QACNH,EAAA,CAAAC,cAAA,eAAgD;QA8B9CD,EA7BA,CAAAQ,UAAA,KAAA4H,2CAAA,mBAMC,KAAAC,2CAAA,kBA6BA;QAKPrI,EAFI,CAAAG,YAAA,EAAM,EACF,EACD;;QAEXH,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,eAA0B,kBAKvB;QADCD,EAAA,CAAAkB,UAAA,mBAAAoH,8DAAA;UAAAtI,EAAA,CAAAoB,aAAA,CAAA2G,GAAA;UAAA,OAAA/H,EAAA,CAAAuB,WAAA,CAASoG,GAAA,CAAAhF,aAAA,EAAe;QAAA,EAAC;QAEzB3C,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAWTH,EAVA,CAAAQ,UAAA,KAAA+H,oDAAA,2BAAqC,KAAAC,oDAAA,2BAUD;QAWxCxI,EADE,CAAAG,YAAA,EAAM,EACF;;;QAnKMH,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAU,UAAA,UAAAiH,GAAA,CAAArF,EAAA,IAAAqF,GAAA,CAAArF,EAAA,MAAoB;QAGpBtC,EAAA,CAAAI,SAAA,EAAmB;QAAnBJ,EAAA,CAAAU,UAAA,SAAAiH,GAAA,CAAArF,EAAA,IAAAqF,GAAA,CAAArF,EAAA,MAAmB;QAaWtC,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAU,UAAA,cAAAiH,GAAA,CAAA9G,SAAA,CAAuB;QAgBlDb,EAAA,CAAAI,SAAA,GAAiD;QAAjDJ,EAAA,CAAAU,UAAA,SAAAiH,GAAA,CAAAxD,eAAA,6BAAiD;QAsBjDnE,EAAA,CAAAI,SAAA,GAAoD;QAApDJ,EAAA,CAAAU,UAAA,SAAAiH,GAAA,CAAAxD,eAAA,gCAAoD;QAkBjDnE,EAAA,CAAAI,SAAA,IAA2C;QAA3CJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAyI,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAlF,QAAA,aAA2C;QAU3CzC,EAAA,CAAAI,SAAA,GAA2C;QAA3CJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAyI,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAlF,QAAA,aAA2C;QAc9CzC,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAU,UAAA,SAAAiH,GAAA,CAAAlF,QAAA,YAAyB;QA6BzBzC,EAAA,CAAAI,SAAA,EAAyB;QAAzBJ,EAAA,CAAAU,UAAA,SAAAiH,GAAA,CAAAlF,QAAA,YAAyB;QAiBrBzC,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAU,UAAA,UAAAiH,GAAA,CAAArF,EAAA,IAAAqF,GAAA,CAAArF,EAAA,MAAoB;QAUpBtC,EAAA,CAAAI,SAAA,EAAmB;QAAnBJ,EAAA,CAAAU,UAAA,SAAAiH,GAAA,CAAArF,EAAA,IAAAqF,GAAA,CAAArF,EAAA,MAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}