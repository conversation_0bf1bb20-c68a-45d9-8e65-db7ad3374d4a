/* You can add global styles to this file, and also import other style files */
@import "./assets/sass/style.scss";
// Replace above style with this css file to enable RTL styles
// @import "./assets/css/style.rtl";
@import './assets/sass/plugins.scss';
// @import "./assets/css/style.rtl.css";
@import "./assets/sass/style.angular.scss";
@import 'ngx-toastr/toastr-bs5-alert';
@import "@ng-select/ng-select/themes/default.theme.css";

// Kendo UI Themes - Choose one based on your design preference
@import "@progress/kendo-theme-default/dist/all.css";
// Alternative: @import "@progress/kendo-theme-bootstrap/dist/all.css";

// Keenicons - High quality and pixel perfect font icons available in 3 styles, duotone, outline and solid for Metronic elements
@import "./assets/plugins/keenicons/duotone/style.css";
@import "./assets/plugins/keenicons/outline/style.css";
@import "./assets/plugins/keenicons/solid/style.css";
.small-modal-body {
height: 200px;
max-height: 200px;
overflow:auto
}

 
.medium-modal-body{
height: 350px;
max-height: 350px;
overflow:auto
}

 
.large-modal-body {
height: 550px;
max-height: 550px;
overflow:auto
}

.fullscreen-loading-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1050 !important; // above content, below modals if needed
}

.loading-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  color: #3699ff !important;
}

// Standard spinner sizes
.spinner-md {
  width: 3rem !important;
  height: 3rem !important;
}

.spinner-sm {
  width: 1.5rem !important;
  height: 1.5rem !important;
}

// Custom colored spinner with brand colors
.custom-colored-spinner {
  width: 3rem !important;
  height: 3rem !important;
  border: 0.25rem solid transparent !important;
  border-top: 0.25rem solid #D91F2D !important;
  border-right: 0.25rem solid #058247 !important;
  border-bottom: 0.25rem solid #184786 !important;
  border-radius: 50% !important;
  animation: custom-spin 1s linear infinite !important;
}

.custom-colored-spinner-sm {
  width: 1.5rem !important;
  height: 1.5rem !important;
  border: 0.2rem solid transparent !important;
  border-top: 0.2rem solid #D91F2D !important;
  border-right: 0.2rem solid #058247 !important;
  border-bottom: 0.2rem solid #184786 !important;
  border-radius: 50% !important;
  animation: custom-spin 1s linear infinite !important;
}

@keyframes custom-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Utility class to center a loader inside a relative container (e.g., modals/forms)
.loading-overlay-inside {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: transparent !important;
  z-index: 1000 !important;
}

.modal-header {
    background-color: #549c54 !important;
    padding: 1rem 1.75rem !important;
}
.modal-title {
    color: #ffffff !important;
}

.custom-error-css{
  color:red !important;
}

// Corrections section styling for permit view
.corrections-section {
  background: #ffffff !important;
  border-radius: 0.5rem !important;
  padding: 1.5rem !important;
  border: 1px solid #e5eaee !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  margin: 0 !important;

  .section-title {
    font-size: 1.2rem !important;
    font-weight: 700 !important;
    color: #2d3748 !important;
    margin-bottom: 1.5rem !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 2px solid #e2e8f0 !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .correction-item {
    background: #fff !important;
    border: 1px solid #e5eaee !important;
    border-radius: 0.5rem !important;
    padding: 1.25rem !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    position: relative !important;
    overflow: hidden !important;
    margin-bottom: 1.5rem !important;

    &::before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 4px !important;
      height: 100% !important;
      background: #3699ff !important;
      border-radius: 0.5rem 0 0 0.5rem !important;
    }

    .correction-header {
      display: flex !important;
      align-items: center !important;
      gap: 1rem !important;
      margin-bottom: 1rem !important;
      padding-bottom: 0.75rem !important;
      border-bottom: 1px solid #f1f3f4 !important;
      position: relative !important;

      .correction-number {
        background: #3699ff !important;
        color: white !important;
        width: 28px !important;
        height: 28px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 0.85rem !important;
        font-weight: 700 !important;
        flex-shrink: 0 !important;
        position: relative !important;
      }

      .correction-meta {
        display: flex !important;
        flex-direction: row !important;
        gap: 1rem !important;
        flex: 1 !important;
        flex-wrap: wrap !important;

        .meta-row {
          display: flex !important;
          align-items: center !important;
          gap: 0.5rem !important;
          padding: 0.25rem 0 !important;
          background: transparent !important;
          border-radius: 0 !important;
          border: none !important;
          flex: 1 !important;
          min-width: 180px !important;

          .meta-label {
            font-size: 0.8rem !important;
            font-weight: 600 !important;
            color: #6c757d !important;
            min-width: 60px !important;
          }

          .meta-value {
            font-size: 0.85rem !important;
            font-weight: 500 !important;
            color: #495057 !important;
            flex: 1 !important;
          }

          .resolved-date {
            color: #495057 !important;
            background: transparent !important;
            padding: 0.2rem 0 !important;
            border-radius: 0 !important;
            font-weight: 600 !important;
            font-size: 0.8rem !important;
          }

          .respond-btn {
            color: #3699ff !important;
            text-decoration: none !important;
            font-size: 0.8rem !important;
            padding: 0.2rem 0.5rem !important;
            border: 1px solid #3699ff !important;
            border-radius: 0.25rem !important;
            background: transparent !important;
            transition: all 0.2s ease !important;
            margin-left: 0.5rem !important;

            &:hover {
              background: #3699ff !important;
              color: white !important;
            }

            i {
              margin-right: 0.25rem !important;
            }
          }
        }
      }
    }

    .correction-content {
      .correction-field {
        margin-bottom: 1rem !important;
        position: relative !important;

        &:last-child {
          margin-bottom: 0 !important;
        }

        .field-label {
          font-size: 0.8rem !important;
          font-weight: 700 !important;
          color: #495057 !important;
          text-transform: uppercase !important;
          letter-spacing: 0.05rem !important;
          margin-bottom: 0.5rem !important;
          display: flex !important;
          align-items: center !important;
          gap: 0.5rem !important;
          position: relative !important;

          i {
            color: #3699ff !important;
            font-size: 0.8rem !important;
            width: 14px !important;
            text-align: center !important;
          }
        }

        .field-content {
          font-size: 0.9rem !important;
          color: #495057 !important;
          line-height: 1.5 !important;
          padding: 0.75rem !important;
          background: #f8f9fa !important;
          border-radius: 0.25rem !important;
          border: 1px solid #e9ecef !important;
          position: relative !important;

          &::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 3px !important;
            height: 100% !important;
            background: #3699ff !important;
            border-radius: 0.25rem 0 0 0.25rem !important;
          }

          &.corrective-action {
            background: #f8f9fa !important;
            border-color: #e9ecef !important;

            &::before {
              background: #3699ff !important;
            }
          }

          &.comment {
            background: #f8f9fa !important;
            border-color: #e9ecef !important;

            &::before {
              background: #3699ff !important;
            }
          }

          &.response {
            background: #f8f9fa !important;
            border-color: #e9ecef !important;

            &::before {
              background: #3699ff !important;
            }
          }
        }
      }
    }

    .correction-separator {
      height: 1px !important;
      background: #e9ecef !important;
      margin: 1rem 0 !important;
      position: relative !important;
    }
  }
}